// Node modules
var fs = require('fs'), vm = require('vm'), mergestream = require('merge-stream'), merge = require('deeply'), chalk = require('chalk'), es = require('event-stream');

// Gulp and plugins
var gulp = require('gulp'), rjs = require('gulp-requirejs-bundler'), concat = require('gulp-concat'), clean = require('gulp-clean'), modifyCssUrls = require('gulp-modify-css-urls'),
    replace = require('gulp-replace'), uglify = require('gulp-uglify'), uglifycss = require('gulp-uglifycss'), htmlreplace = require('gulp-html-replace'),
    bower = require('gulp-bower'),jshint=require('gulp-jshint');
var debug = require('gulp-debug');

// Config
var requireJsRuntimeConfig = vm.runInNewContext(fs.readFileSync('src/app/require.config.js') + '; require;');
    requireJsOptimizerConfig = merge(requireJsRuntimeConfig, {
        out: 'scripts.js',
        baseUrl: './src',
        name: 'app/startup',
        paths: {
            requireLib: 'bower_modules/requirejs/require'
        },
        include: [
            'requireLib',
            'components/login-page/login-page',
            'components/tiles-page/tiles-page',
            'components/side-bar/side-bar',
            'components/top-bar/top-bar',
            'components/add-multiple/add-multiple',
            'components/application-add-edit/application-add-edit',
            'components/doghunt-chart-3-d/doghunt-chart-3-d',
            'components/health-view/health-view',
            'components/home-page/home-page',
            'components/custom-dashboard-home/custom-dashboard-home',
            'components/lines-chart/lines-chart',
            'components/list-grid-view/list-grid-view',
            'components/app-type-list-view/app-type-list-view',
            'components/comp-type-list-view/comp-type-list-view',
            'components/app-type-add-edit/app-type-add-edit',
            'components/comp-type-add-edit/comp-type-add-edit',
            'components/multi-chart/multi-chart',
            'components/pod-cont-view/pod-cont-view',
            'components/real-time-chart/real-time-chart',
            'components/topology-view/topology-view',
            'components/component-list-view/component-list-view',
            'components/component-add-edit/component-add-edit',            
            'components/dashboard-grid-view/dashboard-grid-view',
            'components/config-wizard/config-wizard',
            'components/application-config-wizard/application-config-wizard',
            'components/kpi-producer-mapping/kpi-producer-mapping',
            'components/kpi-producer-cluster-mapping/kpi-producer-cluster-mapping',
            'components/cluster-list-view/cluster-list-view',
            'components/cluster-add-edit/cluster-add-edit',
            'components/component-instance-list-view/component-instance-list-view',
            'components/component-instance-add-edit/component-instance-add-edit',
            'components/kpi-list-view/kpi-list-view',
            'components/kpi-add-edit/kpi-add-edit',
            'components/add-multiple-kpi/add-multiple-kpi',
            'components/add-multiple-component-instance/add-multiple-component-instance',
            'components/dashboard-multi-txn-performance/dashboard-multi-txn-performance',
            'components/producer-list-view/producer-list-view',
            'components/producer-add-edit/producer-add-edit',
            'components/agent-list-view/agent-list-view',
            'components/agent-add-edit/agent-add-edit',
            'components/component-instance-config-wizard/component-instance-config-wizard',
            'components/add-multiple-component-instance-general/add-multiple-component-instance-general',
            'components/dashboard-kpi-performance/dashboard-kpi-performance',
            'components/transaction-list-view/transaction-list-view',
            'components/transaction-add-edit/transaction-add-edit',
            'components/dashboard-aggregated-txns/dashboard-aggregated-txns',
            'components/add-multiple-transactions/add-multiple-transactions',
            'components/user-role-list-view/user-role-list-view',
            'components/user-role-add-edit/user-role-add-edit',
            'components/user-profile-list-view/user-profile-list-view',
            'components/user-profile-add-edit/user-profile-add-edit',
            'components/view-individual-profile/view-individual-profile',
            'components/change-password/change-password',
            'components/message-dialog-box/message-dialog-box',
            'components/agent-wizard-modal/agent-wizard-modal',
            'components/dashboard-view/dashboard-view',
            'components/dashboard-alerts-summary/dashboard-alerts-summary',
            'components/dashboard-txn-performance/dashboard-txn-performance',
            'components/dashboard-pod-expandedview/dashboard-pod-expandedview',
            'components/sparkline-chart/sparkline-chart',
            'components/dashboard-transaction-health/dashboard-transaction-health',
            'components/global-settings/global-settings',
            'components/notification-content-profile-list-view/notification-content-profile-list-view',
            'components/notification-content-profile-add-edit/notification-content-profile-add-edit',
            'components/time-profile-list-view/time-profile-list-view',
            'components/time-profile-add-edit/time-profile-add-edit',
            'components/alert-profile-main-list-view/alert-profile-main-list-view',
            'components/alert-profile-main-add-edit/alert-profile-main-add-edit',
            'components/placeholder-options/placeholder-options',
            'components/single-axis-area-spline-chart/single-axis-area-spline-chart',
            'components/single-axis-line-chart/single-axis-line-chart',
            'components/horizontal-bar-chart/horizontal-bar-chart',
            'text!components/about-page/about.html',
            'components/dashboard-bve-performance/dashboard-bve-performance',
            'components/severity-profile-add-edit/severity-profile-add-edit',
            'components/severity-profile-list-view/severity-profile-list-view', 
            'components/escalation-profile-list-view/escalation-profile-list-view',
            'components/escalation-profile-add-edit/escalation-profile-add-edit',
            'components/global-search/global-search',
            'components/email-sms-settings/email-sms-settings',
            'components/grpc-settings-list-view/grpc-settings-list-view',
            'components/grpc-settings-add-edit/grpc-settings-add-edit',
            'components/kpi-group-list-view/kpi-group-list-view',
            'components/kpi-group-add-edit/kpi-group-add-edit',
            'components/side-bar-collapsible/side-bar-collapsible',
            'text!components/about-page/about.html'
        ],
        insertRequire: ['app/startup'],
        bundles: {
            // If you want parts of the site to load on demand, remove them from the 'include' list
            // above, and group them into bundles here.
            // 'bundle-name': [ 'some/module', 'another/module' ],
            // 'another-bundle-name': [ 'yet-another-module' ]
            'fusionCharts': [
                'bower_modules/fusioncharts/fusioncharts',
                'bower_modules/fusioncharts/fusioncharts.charts',
                'bower_modules/fusioncharts/fusioncharts.gantt',
                'bower_modules/fusioncharts/fusioncharts.maps',
                'bower_modules/fusioncharts/fusioncharts.powercharts',
                'bower_modules/fusioncharts/fusioncharts.treemap',
                'bower_modules/fusioncharts/fusioncharts.widgets',
                'bower_modules/fusioncharts/fusioncharts.zoomscatter',
                'bower_modules/fusioncharts/themes/fusioncharts.theme.fint',

            ]
        }
    });

// Discovers all AMD dependencies, concatenates together all required .js files, minifies them
gulp.task('js', ['bower'], function () {
    return rjs(requireJsOptimizerConfig)
        .pipe(uglify({ preserveComments: 'some' }))
        .pipe(gulp.dest('./dist/'));
});

gulp.task('bower', ['clean'], function() {
  return bower();
});

gulp.task('lint', function() {
    gulp.src(['./src/app/**/*.js','./src/components/**/*.js'])
        .pipe(jshint('.jshintrc').on('error',function (e){console.log(e)}))
        .pipe(jshint.reporter('jshint-stylish').on('error',function (e){console.log(e)}))
        .pipe(jshint.reporter('fail')
        .on('error',function (e){console.log(e)})).on('end', done);

});

// Concatenates CSS files, rewrites relative paths to Bootstrap fonts, copies Bootstrap fonts
gulp.task('css', ['clean','bower'], function () {
    var bowerCss = gulp.src('src/bower_modules/bootstrap/dist/css/bootstrap.min.css')
            .pipe(replace(/url\((')?\.\.\/fonts\//g, 'url($1fonts/')),
        c3Css = gulp.src('src/bower_modules/c3/c3.min.css'),
        bswitch = gulp.src('src/bower_modules/bootstrap-switch/dist/css/bootstrap3/bootstrap-switch.min.css'),
        tgdocs =  gulp.src('src/bower_modules/bootstrap-tokenfield/docs-assets/css/docs.css'),
        tgpygments = gulp.src('src/bower_modules/bootstrap-tokenfield/docs-assets/css/pygments-manni.css'),
        tgtypehead = gulp.src('src/bower_modules/bootstrap-tokenfield/dist/css/tokenfield-typeahead.min.css'),
        bttoken = gulp.src('src/bower_modules/bootstrap-tokenfield/dist/css/bootstrap-tokenfield.min.css'),
        jqchosen = gulp.src('src/bower_modules/chosen/chosen.css'),
        select2 = gulp.src('src/bower_modules/select2/dist/css/select2.min.css')
        .pipe(modifyCssUrls({
          modify: function (url, filePath) {
            return 'images/' + url;
          },

        })),
        jqui = gulp.src('src/bower_modules/jquery-ui/themes/base/jquery-ui.min.css'),
        highchecktree = gulp.src('src/bower_modules/highchecktree/css/highchecktree.css'),
        checklistbox = gulp.src('src/css/checklistbox.css'),
        tooltipster = gulp.src('src/bower_modules/tooltipster/dist/css/tooltipster.bundle.min.css'),
        fontawesome = gulp.src('src/bower_modules/components-font-awesome/css/font-awesome.min.css')
        .pipe(replace(/url\((')?\.\.\/fonts\//g, 'url($1fonts/')),
        bootstrapdatetime = gulp.src('src/bower_modules/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css'),
        gridstack = gulp.src("src/bower_modules/gridstack/dist/gridstack.min.css"),
        gridstackextra = gulp.src("src/bower_modules/gridstack/dist/gridstack-extra.min.css");

        iconCss = gulp.src('src/css/icon.css'),
        appCss = gulp.src('src/css/styles.css'),
        appNomicCSS = gulp.src('src/css/appnomic.css'),


        combinedCss = es.concat(bowerCss,c3Css,bswitch,tgdocs,tgpygments,tgtypehead,bttoken,jqchosen , jqui, highchecktree, gridstack, gridstackextra, checklistbox, tooltipster, fontawesome, bootstrapdatetime, iconCss, appCss, appNomicCSS, select2).pipe(uglifycss({ })).pipe(concat('css.css')),
        fontFiles = gulp.src('./src/bower_modules/bootstrap/fonts/*', { base: './src/bower_modules/bootstrap/' }),
        awesomeFontFiles = gulp.src('./src/bower_modules/components-font-awesome/fonts/*', { base: './src/bower_modules/components-font-awesome/' });

    return es.concat(fontFiles, awesomeFontFiles, combinedCss)
        .pipe(gulp.dest('./dist/'));
});

// Copies index.html, replacing <script> and <link> tags to reference production URLs
gulp.task('html', ['clean', 'bower'], function() {
    return gulp.src('./src/index.html')
        .pipe(htmlreplace({
            'css': {src:'css.css', tpl:'<link href="%s" rel="stylesheet" type="text/css">'},
            'js': 'scripts.js'
        }))
        .pipe(gulp.dest('./dist/'));
});


//image icon task
gulp.task('image', ['clean'], function() {
    //For custom images
    var customImg = gulp.src('src/images/*')
/*    .pipe(imagemin({
        progressive: true,
        svgoPlugins: [{removeViewBox: false}],
        use: [pngquant()]
    }))*/
    .pipe(gulp.dest('dist/images'));

    //For Dashboard Images
    var dashboardImg = gulp.src('src/images/dashboard_types/*')
/*    .pipe(imagemin({
        progressive: true,
        svgoPlugins: [{removeViewBox: false}],
        use: [pngquant()]
    }))*/
    .pipe(gulp.dest('dist/images/dashboard_types'));

    //For library related images
    var jquiImg = gulp.src('./src/bower_modules/jquery-ui/themes/base/images/*')
/*    .pipe(imagemin({
        progressive: true,
        svgoPlugins: [{removeViewBox: false}],
        use: [pngquant()]
    }))*/
    .pipe(gulp.dest('dist/images'));

    var chosenImg = gulp.src('./src/bower_modules/chosen/chosen-sprite.png')
/*    .pipe(imagemin({
        progressive: true,
        svgoPlugins: [{removeViewBox: false}],
        use: [pngquant()]
    }))*/
    .pipe(gulp.dest('dist'));

    var dashboardImg = gulp.src('./src/images/dashboard_types/*')
/*    .pipe(imagemin({
        progressive: true,
        svgoPlugins: [{removeViewBox: false}],
        use: [pngquant()]
    }))*/
    .pipe(gulp.dest('dist/images/dashboard_types'));
    
    return mergestream(customImg,jquiImg,chosenImg,dashboardImg);
});


// Removes all files from ./dist/
gulp.task('clean', function() {
    return gulp.src('./dist/**/*', { read: false })
        .pipe(clean());
});


gulp.task('default', ['clean','bower','html', 'js', 'css', 'image'], function(callback) {
    callback();
    console.log('\nPlaced optimized files in ' + chalk.magenta('dist/\n'));
});

