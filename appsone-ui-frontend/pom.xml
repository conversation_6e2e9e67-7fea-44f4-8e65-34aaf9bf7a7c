<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.appnomic</groupId>
    <artifactId>appsone</artifactId>
    <version>5.8.0</version>
  </parent>


    <groupId>com.appnomic.appsone</groupId>
    <artifactId>appsone-ui-frontend</artifactId>
    <version>5.8.1</version>
   
    <build>
        <plugins>
          <plugin>
            <groupId>com.github.eirslett</groupId>
            <artifactId>frontend-maven-plugin</artifactId>
            <version>0.0.16</version>

            <executions>

              <execution>
                <id>install node and npm</id>
                <goals>
                  <goal>install-node-and-npm</goal>
                </goals>
                <configuration>
                  <nodeVersion>v4.4.7</nodeVersion>
                  <npmVersion>1.4.21</npmVersion>
                </configuration>
              </execution>

              <execution>
                <id>npm install</id>
                <goals>
                  <goal>npm</goal>
                </goals>
                <!-- Optional configuration which provides for running any npm command -->
                <configuration>
                  <arguments>install</arguments>
                </configuration>
              </execution>

              <execution>
                <id>gulp build</id>
                <goals>
                  <goal>gulp</goal>
                </goals>

              </execution>

            </executions>
          </plugin>
          <plugin>
		<groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dist-folder</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>../appsone-ui/src/main/web</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>dist</directory>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                 </executions>
            </plugin>
        </plugins>
    </build>
</project>
