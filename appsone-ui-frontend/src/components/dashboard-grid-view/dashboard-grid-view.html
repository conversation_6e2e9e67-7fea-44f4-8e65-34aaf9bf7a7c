<div data-bind="template: {afterRender: renderHandler}">
    <div class="scrollTable" data-bind="style : { 'overflow-y':'auto' }" style="display: block; height: 600px;">
        <table id="slowPagesTable" class="table table-fixedheader table-bordered podTable" data-bind="attr: {id: 'pod_'+podTitle}">
            <thead>
                <tr data-bind="foreach: podGridHeaders">
                    <th data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data.displayName}, text: $data.displayName, css: $data.class"></th>
                </tr>
            </thead>
            <tbody data-bind="foreach: podGridData">
                <tr data-bind="foreach : $parents[0].podGridHeaders">
                   <td  data-toggle="tooltip" 
                        data-placement="bottom" 
                        data-bind=" attr:{ title : $data['columnTitle'] == 'avgResponseTime' ? 
                                                   parseFloat($parents[0][$data['columnTitle']]) : 
                                                   $parents[0][$data['columnTitle']]
                                         },
                                    text: $data['columnTitle'] == 'avgResponseTime' ? 
                                          parseFloat($parents[0][$data['columnTitle']]) : 
                                          $parents[0][$data['columnTitle']] || 'NA' ">
                       
                   </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>


