define(['jquery','knockout','d3','c3','fusionCharts','text!./dashboard-grid-view.html','hasher','ui-constants','ui-common','knockout-es5'], function($,ko,d3,c3,fusioncharts,templateMarkup,hasher,uiConstants,uicommon, koES5) {

  function DashboardGridView(params) {
    var self = this;
    this.podId = params.podId;
    this.podTitle = params.podTitle;
    this.podName = params.podName;
    this.currentPodBodyHeight = '';
    self.chartDataObj = params.chartDataObj;

    this.podGridHeaders = [
        {'columnTitle':'name', 'displayName':'KPI Name' },
        {'columnTitle':'value', 'displayName':'Value' }
      ];

      this.podGridData = [
      {'name':'CPU Util', 'value':'70%'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'},
      {'name':'Mem Util', 'value':'60%'},
      {'name':'Disk IO Read', 'value':'10 r/s'},
      {'name':'Disk IO Write', 'value':'15 w/s'},
      {'name':'Load Avg', 'value':'0.9'}
    ];

   

    
    koES5.track(this);

    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podName);

    /**
     * Runs when current Template finishes rendering
     * @return {NA} NA
     */
    this.renderHandler = function(){  
      self.currentPodBodyHeight = $('#pod_'+params.podId).height()-35;
      $('#podBody_'+params.podId).css('height',(self.currentPodBodyHeight-500)+'px');

      self.getGridData();
    }

    /**
     * Fetch grid header and body data into arrays.
     * @return {NA} [NA]
     */
    self.getGridData = function(){
      
    }
  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  DashboardGridView.prototype.dispose = function() { };
  
  return { viewModel: DashboardGridView, template: templateMarkup };

});