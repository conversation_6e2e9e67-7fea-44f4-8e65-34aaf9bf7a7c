define(['jquery','knockout','fusionCharts','text!./doghunt-chart-3-d.html','hasher','ui-constants','ui-common'], function($,ko,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function DOGHUNTCHART3D(params) {
    this.message = ko.observable('Hello from the doghunt-chart-3D component!'); 
    var self = this;
    this.podId = ko.observable(params.podId);
    this.DHCPodId = ko.observable(params.podId+'_doghunt-chart-container');
    this.ModalDHCPodId = ko.observable(params.podId+'_Modal_doghunt-chart-container');
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.isModal = ko.observable(params.isModal);

    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podType);
    if(uiConstants.common.DEBUG_MODE) console.log(self.DHCPodId()+"*******");

    this.renderHandler = function(){

        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);
        self.initChart(self.DHCPodId(),self.currentPodBodyHeight()-5, self.currentPodBodyWidth()-5); //id,height,width
    
    }

    this.modalRenderHandler = function(){
        self.currentPodBodyHeight($('body').height()*0.80);
        if(uiConstants.common.DEBUG_MODE) console.log(self.currentPodBodyHeight());        
        self.initChart(self.ModalDHCPodId(),self.currentPodBodyHeight(),'97%');
    }


    this.initChart = function(chartCont, chartHeight, chartWidth){
        //if(params.podTitle != "Transaction_Health"){
        FusionCharts.ready(function () {
            var revenueChart = new FusionCharts({
                type: 'doughnut3d',
                renderAt: chartCont,
                width: chartWidth,
                height: chartHeight,
                dataFormat: 'json',
                dataSource: {
                    "chart": {
                        "caption": "",
                        "subCaption": "",
                        "numberPrefix": "",
                        "paletteColors": "#d43f3a, #d58512, #269abc",
                        "bgColor": "#ffffff",
                        "showBorder": "0",
                        "use3DLighting": "0",
                        "showShadow": "0",
                        "enableSmartLabels": "0",
                        "startingAngle": "310",
                        "showNames":"0",
                        "showVales":"0",
                        "showLabels": "0",
                        "showPercentValues": "1",
                        "showLegend": "1",                                
                        "legendShadow": "0",
                        "legendBorderAlpha": "0",                                
                        "decimals": "0",
                        "toolTipColor": "#ffffff",
                        "toolTipBorderThickness": "0",
                        "toolTipBgColor": "#000000",
                        "toolTipBgAlpha": "80",
                        "toolTipBorderRadius": "2",
                        "toolTipPadding": "5",
                        "spacing": [0, 0, 0, 0],
                        "margin": [0, 0, 0, 0],
                        // Enable export
                        "exportEnabled": "1",
                        "exportAtClientSide":"1",
                        // Hide export menu item
                        "exportShowMenuItem": "1",
                        "showExportDialog":"1",
                        "exportDialogMessage":"Capturing Data : ",
                        "exportDialogColor":"#333",
                        "exportDialogPBColor":"#0372ab",
                        "toolbarButtonColor":"#999999",
                        "exportFileName":self.podTitle()                            
                    },
                    "data": [
                        {
                            "label": "High",
                            "value": "60"
                        },
                        {
                            "label": "Medium",
                            "value": "20"
                        }, 
                        {
                            "label": "Low",
                            "value": "20"
                        }
                    ]
                }
            }).render();
        });
        //}            
    }
  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  DOGHUNTCHART3D.prototype.dispose = function() { };
  
  return { viewModel: DOGHUNTCHART3D, template: templateMarkup};

});