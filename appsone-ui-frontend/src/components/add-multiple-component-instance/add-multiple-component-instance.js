define(['jquery','bootstrap','knockout','validator','jquery-chosen','text!./add-multiple-component-instance.html','hasher','ui-constants','ui-common'], function($,bt,ko,validator,jc,templateMarkup,hasher,uiConstants,uicommon) {

function Addmultiplecomponentinstance(params) {
	var self = this;

	this.tableHeadersSelect = ko.observableArray([
	  	{
	      "attributeId": 0,
	      "attributeName": "Sl.No.",
	      "attributeType": "label",
	      "isMandatory": 0,
	      "isCustom": 0
		},
		{ "attributeId": 0,
	      "attributeName": "Name",
	      "attributeType": "TextBox",
	      "isMandatory": 0,
	      "isCustom": 0
	  	},
	  	{ "attributeId": 0,
	      "attributeName": "",
	      "attributeType": "delete",
	      "isMandatory": 0,
	      "isCustom": 0
	  	}
	]);

	var prevVersionNameSelected="";
	var prevVersionIdSelected=0;

	this.componentsArr = ko.observableArray(params.componentsArr());
	this.currentViewIndex = ko.observable(params.currentViewIndex());
	this.pageSelected = params.pageSelected;

	this.componentNamesArr = ko.observableArray([{}]);
	this.componentVersionsArr = ko.observableArray([{}]);

	this.compAttributesArr = ko.observableArray();
	this.tableHeaders = ko.observableArray();

  	this.errorStack = ko.observableArray();
	this.reqRecordCounter = ko.observableArray();
    this.multiRowAddLimit = ko.observable(1001);
    this.errorStackLimitOnRows = ko.observable(10);

    this.requestDataArray = [];
    this.showAddRowBtn = ko.observable(1);

	this.renderHandler=function(){	 
		jQuery(".chosen").chosen({
			search_contains: true	
		});
		
		$("#compTypeList").on('change', function () {
	    	setCompNames();
		});

		$("#compNamesList").on('change', function () {
	    	setCompVersions();
		});


		$("#compVersionsList").on('focus', function () {
	        prevVersionIdSelected = this.value;
	        prevVersionNameSelected =  $("#compVersionsList :selected").text();
	    }).change(function() {});

		$("#compVersionsList").on('change', function () {
	    	setCompAttributes();
	    	self.onVersionListChange();
		});

	}

	/* Get components and version : start*/
	function setCompNames(){
		if($("#compTypeList").val() == 0){
			self.componentNamesArr({});
			self.componentVersionsArr({});
		}
		else{
			var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#compTypeList").val(); });

			if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
			self.componentNamesArr(componentsObj[0].components);
		}
		$("#compNamesList").trigger('chosen:updated');
	}

	function setCompVersions(){
		if($("#compNamesList").val() == 0){
			self.componentVersionsArr({});
		}
		else{
			var componentsObj = $.grep(self.componentNamesArr(), function(e){ return e.componentId == $("#compNamesList").val(); });

			if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
			self.componentVersionsArr(componentsObj[0].versions);
		}
		$("#compVersionsList").trigger('chosen:updated');
	}

	function setCompAttributes(){
		if($("#compVersionsList").val() == 0){
			self.compAttributesArr([]);
		}
		else{
			console.log("---------------------------------------onchangeof versionlist");
			var componentVersionId = $("#compVersionsList").val(); 
			if(uiConstants.common.DEBUG_MODE)console.log("=======================Get Attributes for multiple Component Instance ====================================");
			if(uiConstants.common.DEBUG_MODE)console.log($("#compVersionsList").val());
			requestCall(uiConstants.common.SERVER_IP + "/componentAttribute/"+$("#compVersionsList").val(), "GET", "", "getComponentAttributes", successCallback, errorCallback);
		}
	}


	this.onVersionListChange = function(){
		console.log("versionchange +++++++="+$('#compVersionsList option:selected').text());
		
		self.typeSelected=$('#compVersionsList option:selected').text();
			
			if($('#compVersionsList option:selected').text() != "Select"){
				console.log("version if not select+++++++="+$('#compVersionsList option:selected').text());
				self.getConfirmOnVersionChange(self.tableHeaders(),prevVersionIdSelected);	
			}
			else{
				console.log("ur in select");
				showMessageBox("ComponentInstance data will be cleared due to change in selection.Are you sure you want to proceed with the change?", "question", "confirm", function confirmCallback(r){
					if(r){
						self.tableHeaders(self.tableHeadersSelect());
						self.tableHeaders.removeAll();
						$('#tableCompInstAddMultiple tbody').empty();
						$("#btnSave").addClass('disabled');	
					}
					else{
						$("#compVersionsList").val(prevVersionIdSelected);
					}
				});
			}

			self.showAddRowBtn($('#compVersionsList').val() == ""?1:0);
	}

	this.getConfirmOnVersionChange=function(dynmicheaders,previous){
		var rowCounter = $('#tableCompInstAddMultiple tbody tr').length;
		if(rowCounter == 0){
				console.log("when row is zero"+dynmicheaders);
				self.tableHeaders(dynmicheaders);
				self.onAddClick();
		}else {
			console.log("when row is non zero"+dynmicheaders);
			showMessageBox("ComponentInstance data will be cleared due to change in selection.Are you sure you want to proceed with the change?", "question", "confirm", function confirmCallback(r){
				if(r){
					self.tableHeaders(dynmicheaders);
					$('#tableCompInstAddMultiple tbody').empty();
					$("#btnSave").addClass('disabled');	

					if($('#compVersionsList option:selected').text() != "Select"){
						self.onAddClick();
					}
				}
				else{
					$("#compVersionsList").val(previous);
				}
			});
		}
	}

	/* Get components and version : end*/

	this.cancelAddScreen = function(){
		self.pageSelected("Component Instance Configuration");
		$('#tableCompInstAddMultiple tbody').empty();
		params.currentViewIndex(uiConstants.common.LIST_VIEW);
	}


	//create controls first time on addclick with tabindex
	this.createControlsOnAddClick = function(controlType,controlId,rowCounter,row,indx){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlId);
		if(controlType == 'label'){
			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+controlType+rowCounter+"'>"+(rowCounter+1)+"</label></td>");			
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim != 'Tags'){
	 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(indx)+" id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'Password'){
	 		row.append("<td class='col-xs-2'><input type='password' tabindex="+(indx)+" id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'DropDown'){
	 		row.append("<td class='col-xs-2'><select tabindex="+(indx)+" id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'/></td>");		 		
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){					    			
	 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(indx)+" placeholder='Tags separated by comma(,)' id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'delete'){
	 		row.append("<td class='col-xs-1 deleteRow' tabindex="+(indx)+" id='"+controlType+rowCounter+"'><span class='glyphicon glyphicon-remove form-control gridRemove'></span></td>");
	 	}
	}

	//create controls depending on the type
	this.createControls = function(controlType,controlId,rowCounter,row,indx){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlId);
		if(controlType == 'label' && controlId == 'Sl.No.'){
			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+controlType+rowCounter+"'></label></td>");
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim != 'Tags'){
	 		row.append("<td class='col-xs-2'><input type='text' id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");
	 	}
	 	else if(controlType == 'Password'){
	 		row.append("<td class='col-xs-2'><input type='password' id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'DropDown'){
	 		row.append("<td class='col-xs-2'><select tabindex="+(indx)+" id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'/></td>");		 		
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){	
	 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");				    			 		
	 	}
	}

	this.assignValuesToControls = function(controlType,controlName,rowCounter,_listValues,isMandate,cells,indx){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlName);
		if(controlType == 'label' && controlIdAfterTrim == 'Sl.No.'){
    		$("#"+controlType+rowCounter).text(cells[indx]||"");
    	}else if(controlType == 'TextBox'){
    		var cname = cells[indx] || "";
    		if(cname == "" && isMandate == 1){
    			self.manageErrorMessage('push',rowCounter, "Field value is required");
    		}/*else if(nameValidation(cname) == 0){
    			self.manageErrorMessage('push',rowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
    		}	*/				    			
	 		$("#"+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}else if(controlType == 'Password'){
	 		var cname = cells[indx] || "";
    		if(cname == "" && isMandate == 1){
    			self.manageErrorMessage('push',rowCounter, "Field value is required");
    		}
	 		$("#"+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}
	 	else if(controlType == 'DropDown'){
	 		self.addDataListOption(controlType,controlIdAfterTrim+rowCounter, cells[xindex]||"",_listValues);
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){
	 		var ctag = cells[indx] || "";
	 		if(ctag != ""){
	 			if(tagValidation(ctag) == 0)
    			self.manageErrorMessage('push',rowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
    		}
    		if(uiConstants.common.DEBUG_MODE)console.log(ctag);	
	 		$("#"+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}
	}

	this.onAddClick = function(){
		 //add new blank row initially on click of add button
		 if(uiConstants.common.DEBUG_MODE)console.log("=========================ComponentInstance Multiple Addclick =============================");
		 if(uiConstants.common.DEBUG_MODE)console.log(self.tableHeaders());
		 
		 var rowCounter = $('#tableCompInstAddMultiple tbody tr').length;
		 var row = $('<tr class="" id="row_'+rowCounter+'"/>');

		 if(rowCounter <= self.multiRowAddLimit()-2){
	 		 for (var i = 0; i < self.tableHeaders().length; i++) {	
	 			self.createControlsOnAddClick(self.tableHeaders()[i].attributeType,self.tableHeaders()[i].attributeName,rowCounter,row,i);

			 }		 
			 $("#tableCompInstAddMultiple tbody").append(row);


			 self.deleteRowBind("delete"+rowCounter);
			 for(var i=0;i<self.tableHeaders().length;i++){
			 	if(self.tableHeaders()[i].attributeType == 'DropDown')
			 	self.addDataListOption('DropDown',self.tableHeaders()[i].attributeName+rowCounter, 'Select',attributeOptions); //when contro type is select load data with the arraydata						
			 }

			 self.bindTableRowListner("row_"+rowCounter,self.tableHeaders()[1].attributeName);
			 self.enableButton();

		 }else{
			showMessageBox(uiConstants.common.RECORDS_PASTE_LIMIT_EXCEEDED, "error");			
		 }	 
	}

	this.addDataListOption = function(type, _class, _val ,_listValues){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(_class);
		if(type == 'DropDown'){
			$('<option>', {text: 'Select', value: ''}).appendTo('#'+controlIdAfterTrim);
	 		_listValues.forEach(function(item){
	 			$('<option>', {value: item.id, text: item.name, name: item.name}).appendTo('#'+controlIdAfterTrim);
      		});
      		$('#'+controlIdAfterTrim+' option').filter(function() { 
			    if(($(this).text() === _val)) {return true;}			    
			}).prop('selected', true);
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', controlIdAfterTrim.substr(controlIdAfterTrim.length-1), "Please select valid item from the list.");}
 		}
	}

	//paste listner on every rows Application name filed and on rest of fileds treat as normal copy&paste.
	this.bindTableRowListner = function(rowid,firstColumnAttributeName){//first attribute name should always be textinput
		 if(uiConstants.common.DEBUG_MODE)console.log("====================================ComponentInstance Pasted data===============================");		
		 $('#row_0 input ,#row_0 select ').on('change', function(e){
		 	 $("#btnClearAll").removeClass('disabled');	

		 	var clearflag=true;
			for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
			 	 if($("#"+self.tableHeaders()[xindex].attributeName+"0").val() != ""){
			 	 	 clearflag=false;

			 	 }
			 }
			 if(clearflag) $("#btnClearAll").addClass('disabled');
		 });

		 //on paste listener
		 var indexID = rowid.split("_")[1];

		  if(uiConstants.common.DEBUG_MODE)console.log($('#'+rowid+'>td>#'+trimSpacesReplaceUnderscore(firstColumnAttributeName)+indexID));
		
		 $('#'+rowid+'>td>#'+trimSpacesReplaceUnderscore(firstColumnAttributeName)+indexID).on('paste', function(e){
		 	e.stopPropagation();
		    e.preventDefault(); 	    

		    $("#btnClearAll").removeClass('disabled');	

			if (e.originalEvent.clipboardData) { 
				var data = (e.originalEvent || e).clipboardData.getData('text/plain');
				var inputId = e.target.id;
			} else if (window.clipboardData) { 
				var data = window.clipboardData.getData('Text');
				var inputId = window.event.srcElement.id;
			}
			//data = data.slice(1, -1);
			
			if(uiConstants.common.DEBUG_MODE)console.log(data);

		   if(data != null){
		   		//clearing old vaule
		   		self.errorStack.removeAll();

		   		var crid = $(this).parent().parent().attr('id');
		   		$("#"+crid).addClass('empty');
		   		var curRowIndex = $(this).parent().parent().index();
				
		   		data.replace(/\n$/, "");
				var rows = data.split("\n");
				var table = $("#tableCompInstAddMultiple tbody");
				var curType = "Select";
				var curTZ = "";				
				
				var rowCounter = $('#tableCompInstAddMultiple tbody tr').length;
				
				
				var limitFlag = (rows.length-1) + rowCounter;				
				if(limitFlag <= self.multiRowAddLimit()){
					if(uiConstants.common.DEBUG_MODE)console.log("Total no of rows----------------->"+limitFlag);
					
						var col = rows[0].split("\t");
						
						if(uiConstants.common.DEBUG_MODE)console.log(col.length +"=="+ (self.tableHeaders().length-2));

						if(col.length == self.tableHeaders().length-2){

							rowCounter = $('#tableCompInstAddMultiple tbody tr').length;
							
							rows.forEach(function (y, yindex) {	

							    var cells = y.split("\t");
							    if(uiConstants.common.DEBUG_MODE)console.log(cells);					    
							    var currentRowCounter = rowCounter + yindex;
							    var row = $("<tr class='' id='row_"+currentRowCounter+"'/>");
							    

							    if(yindex < rows.length-1){					    	
								    if(cells.length != self.tableHeaders().length-2){
								    	self.manageErrorMessage('push',currentRowCounter, uiConstants.common.CELL_VALUES_SEPERATION);
								    }

								    //control creation start
							    	for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
									 	if(self.tableHeaders()[xindex].type != 'delete'){
									 		self.createControls(self.tableHeaders()[xindex].attributeType,self.tableHeaders()[xindex].attributeName,currentRowCounter,row,xindex);
									 	}
							    	};					   
								    row.append("<td class='col-xs-1 deleteRow' id='"+self.tableHeaders()[xindex].attributeType+currentRowCounter+"'><span class='glyphicon glyphicon-remove form-control gridRemove'></span></td>");
								    
								    //table.append(row);
								    $(".empty").after(row);
								    table.find('.empty').removeClass('empty').next().addClass('empty');

								    self.deleteRowBind("delete"+currentRowCounter);
									self.bindTableRowListner("row_"+currentRowCounter,self.tableHeaders()[1].attributeName);	

								    var tarr = [currentRowCounter];
								    cells = tarr.concat(cells);
								    if(uiConstants.common.DEBUG_MODE)console.log(cells);

								    //value assign to current row controls :start
								    for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {	
								    	//value assign
								    	self.assignValuesToControls(self.tableHeaders()[xindex].attributeType,self.tableHeaders()[xindex].attributeName,currentRowCounter,self.tableHeaders()[xindex].attributeOptions,self.tableHeaders()[xindex].isMandatory,cells,xindex);
								    }
								    //value assign to current row controls :end
								}
							});
						}else{
							
							showMessageBox(uiConstants.common.COLUMNS_MISMATCH, "error");	
							return false;
						}	

				}else{
					showMessageBox(uiConstants.common.ROW_LIMIT_EXCEEDED, "error");
					self.enableButton();
				}

				$("#tableCompInstAddMultiple tbody").find('.empty').removeClass('empty');
				$("#"+crid).remove();

				self.chnageRowAndCellId();
				self.validateElementsValue();
				
				if(self.errorStack().length){	
					self.manageErrorMessage('print');
					if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());								
				}
			}			
		});
		
	}

	//enabling buttons based on conditions
	this.enableButton = function(){
		if($('#tableCompInstAddMultiple tbody tr').length > 0){
			$("#btnCompInstMultipleAdd").removeClass('disabled');
			$("#btnSave").removeClass('disabled');
		}
	}

	//after adding or pasting dynamic rows in to grid, delete row listner binding.
	this.deleteRowBind = function(deleteRowId){
		$('#'+deleteRowId).on('click', function(e){
			if(uiConstants.common.DEBUG_MODE)console.log("========================================Delete row listener binding==================================");
			if(uiConstants.common.DEBUG_MODE)console.log($('#tableCompInstAddMultiple tbody tr').length);	 		
		 		if($('#tableCompInstAddMultiple tbody tr').length == 0){
		 			self.disableButton();
		 		}else if($('#tableCompInstAddMultiple tbody tr').length == 1){
		 			var deleteFlagForColumn = false;
		 			$("#row_0").children('td').each(function(j){
		 				if(self.tableHeaders()[j].attributeType == 'TextBox' || self.tableHeaders()[j].attributeType == 'Password'){
		 					if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);   
		 					if($(this).children('input').val().length){
		 						deleteFlagForColumn=true;
		 						return false;
		 					}

		 				}else if(self.tableHeaders()[j].attributeType == 'DropDown'){
		 					if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').val().length);   
		 					if($(this).children('select').val().length){
		 						deleteFlagForColumn=true;
		 						return false;
		 					} 
		 				}
		 			});	
	            
	            	if(uiConstants.common.DEBUG_MODE)console.log(deleteFlagForColumn);
		 			if(deleteFlagForColumn){
		 				showMessageBox(uiConstants.common.ERROR_LAST_ROW_DELETE, "error");
		 			}else{
		 				$("#row_0").remove();
		 				self.onAddClick();
		 			} 
		 		}
		 		else
		 		{
		 			$(this).parent().remove();
		 		}
		 		//changing each row's Id and childrens id
		 		if(uiConstants.common.DEBUG_MODE)console.log($('#tableCompInstAddMultiple tbody tr').length);
		 		self.chnageRowAndCellId();
		 });

		
	}
	
	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(){
		self.errorStack.removeAll();
		self.requestDataArray = [];
		var attributeArray = [];
		
		$('#tableCompInstAddMultiple tbody tr').each(function(i){
			var cobj = {'index': i+1};
			
	        $(this).children('td').each(function(j){
	        	var obj = {};

	        	if((self.tableHeaders()[j].attributeType == 'TextBox' || self.tableHeaders()[j].attributeType == 'Password') && trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName) != 'Tags'){
 					//if(DEBUG_MODE)console.log($(this).children('input').val());
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
		    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+"value is required");
		    		}/*else if(nameValidation(cname) == 0){
		    			self.manageErrorMessage('push',cobj.index-1, "Field value is should match some reg exp");
		    		}*/
	            	if(self.tableHeaders()[j].attributeName == "Name"){
		    			cobj['componentInstanceName'] = $(this).children('input').val();
		    		}
		    		else{
	            		obj['attributeId'] = self.tableHeaders()[j].attributeId.toString();
	            		obj['attributeValue'] = isNaN($(this).children('input').val()) ? $(this).children('input').val() : parseInt($(this).children('input').val());
	            		obj['attributeType'] = self.tableHeaders()[j].attributeType.toString();
	            		attributeArray.push(obj);  
	            	}

 				}else if(self.tableHeaders()[j].attributeType == 'DropDown'){
 					var aTypeLen = $(this).children('select').val().length;	            	 
	            	 if(aTypeLen > 0){
	            	 	obj['attributeId'] = self.tableHeaders()[j].attributeId.toString();
	            		obj['attributeValue'] = isNaN($(this).children('select').val()) ? $(this).children('select').val() : parseInt($(this).children('select').val());
	            	 	obj['attributeType'] = self.tableHeaders()[j].attributeType.toString();
	            	 	attributeArray.push(obj);
	            	 }
	            	 else{
	            	 	self.manageErrorMessage('push',cobj.index-1, uiConstants.kpiConfig.KPI_CLUSTEROPERN_REQUIRED);
	            	 }
 				}
 				else if(self.tableHeaders()[j].attributeType == 'TextBox' && self.tableHeaders()[j].attributeName == 'Tags'){// Kpi Tag
	            	//if(DEBUG_MODE)console.log($(this).children('input').val());
	            	var ctag = $(this).children('input').val();
	            	if(ctag != ""){
			 			if(tagValidation(ctag) == 0)
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		    		}
	            	var taglist = $(this).children('input').val();
	            	if(taglist != ""){
	            		taglist = taglist.split(",");
	            		var tagObj = [];
	            		for (var i = 0; i < taglist.length; i++) {
	            			tagObj.push({"tagId":null, "tagName":taglist[i].trim(), "tagOperation":"add"});
	            		};
	            	}
	            	cobj['tags'] = tagObj;
	           }		
	        }); 	 	
	        
				cobj['componentTypeId'] =  parseInt($("#compTypeList").val());
				cobj['componentId'] =  parseInt($("#compNamesList").val());
				cobj['componentVersionId'] =  parseInt($("#compVersionsList").val());
				cobj['clusterId'] = 0;
				cobj['applicationId'] = [];
				cobj['monitor'] = 0;
				cobj['kpis'] = [];
				cobj['status'] = 1;
				cobj['attributes'] = attributeArray;
				self.requestDataArray.push(cobj);
				attributeArray = [];
	    });
	}

	//managing all validation error message at pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount){		
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			//if(addRecordCount>1)
				var messageStr = addRecordCount+uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCE+" and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			//else
			//	var messageStr = addRecordCount+" application is successfully added and "+unsavecnt +" have failed. \n";

				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						messageStr += "Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";
			self.errorStack().forEach(function(item,index){
			if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				/*if(item.rowno == -1){					
					messageStr += item.message +"\n";					
					self.enableButton();					
					if(DEBUG_MODE)console.log("count not match while copy");
				}else */
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					messageStr += "Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}

	//Changing row and col elements ID's when any delete or append/prepend operation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	var rowCnt = 0;
    	$('#tableCompInstAddMultiple tbody tr').each(function(i){
	        $(this).children('td').each(function(j){

	        	if(self.tableHeaders()[j].attributeType == 'label'){
	        		$(this).children('label').attr('id',trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	            	$(this).children('label').text(rowCnt+1);
	        	}else if(self.tableHeaders()[j].attributeType == 'TextBox' || self.tableHeaders()[j].attributeType == 'Password'){
	        		$(this).children('input').attr('id',trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	        	}else if(self.tableHeaders()[j].attributeType == 'DropDown'){
	        		$(this).children('select').attr('id',trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	        	}else if(self.tableHeaders()[j].attributeType == 'delete'){
 					$(this).attr('id',trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
 				}
	        });
	        $(this).attr('id','row_'+rowCnt);
	        rowCnt++;
	    });

    }

    //clearing all rows with confirmation of user's. 
	this.onClearAll = function(){
		var rowCounter = $('#tableCompInstAddMultiple tbody tr').length;
		if(rowCounter > 1){		
		    showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableCompInstAddMultiple tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
			    }
			});
		}
		else if(rowCounter == 1 ){			
			showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS_CONTENT, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableCompInstAddMultiple tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
					
			    }
			});
		}		
	}

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
		self.requestDataArray = [];
    	self.validateElementsValue();

		if(self.errorStack().length){	
			self.manageErrorMessage('print');
		}else{
			self.reqRecordCounter(self.requestDataArray.length);
			var finalObj = {'componentInstances':self.requestDataArray};
			if(uiConstants.common.DEBUG_MODE)console.log("==================== Add Multiple ComponentInstance===================");
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
			var url = uiConstants.common.SERVER_IP+"/componentInstanceType";
			requestCall(url, 'POST', JSON.stringify(finalObj), 'addComponentInstances', successCallback, errorCallback);	
		}
    }	

    //Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
    	var succnt = 0;
    	var failcnt = 0;
    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
    	if(self.reqRecordCounter() == res.length){
    		for (var i = 0; i < res.length; i++) {
    			if(uiConstants.common.DEBUG_MODE)console.log(res[i]);
    			if(uiConstants.common.DEBUG_MODE)console.log(res[i].index);
    			var rid = res[i].index-1;
    			if(res[i].responseStatus == "Success"){
    				$("#tableCompInstAddMultiple tbody #row_"+rid).remove();
    				succnt++;	
    			}else{
    				self.manageErrorMessage('push',failcnt, res[i].message);
    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+res[i].message);
    				failcnt++;
    			}
    		};

    		//changing row index and number
    		self.manageErrorMessage('print',"","",succnt);
    		self.chnageRowAndCellId();
    		
    	}else{
    		
    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
    	}
    }


	function successCallback(data, reqType) {
		if(reqType === "getComponentAttributes"){
			var res = data.result;
			if(data.responseStatus == "failure"){
					if(res != undefined && res[0] != undefined)
						showMessageBox(res[0].message, "error");
					else
						showMessageBox(ERROR_GET_COMP_INSTANCES_ATTRIBUTES, "error");
			}
			else{
					self.compAttributesArr(data.result);
					/*self.compAttributesArr([
											    {
											      "attributeId": 1,
											      "attributeName": "IpAddress",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    },
											    {
											      "attributeId": 2,
											      "attributeName": "Description",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    },
											    {
											      "attributeId": 3,
											      "attributeName": "Username",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    },
											    {
											      "attributeId": 4,
											      "attributeName": "Password",
											      "attributeType": "Password",
											      "isMandatory": 1,
											      "isCustom": 0
											    },
											    {
											      "attributeId": 5,
											      "attributeName": "Port",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    }
											]);*/
					//add item at first of observableArray

					self.compAttributesArr.unshift( {
											      "attributeId": 0,
											      "attributeName": "Name",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    });
					self.compAttributesArr.unshift( {
											      "attributeId": 0,
											      "attributeName": "Sl.No.",
											      "attributeType": "label",
											      "isMandatory": 0,
											      "isCustom": 0
											    });

					//add item at last of observableArray
					self.compAttributesArr.push( {
											      "attributeId": self.compAttributesArr().length,
											      "attributeName": "Tags",
											      "attributeType": "TextBox",
											      "isMandatory": 0,
											      "isCustom": 0
											    });

					//add item at last of observableArray
					self.compAttributesArr.push( {
											      "attributeId": self.compAttributesArr().length+1,
											      "attributeName": "",
											      "attributeType": "delete",
											      "isMandatory": 0,
											      "isCustom": 0
											    });
					self.tableHeaders(self.compAttributesArr());
					self.onVersionListChange();

			}
		}
		else if(reqType === "addComponentInstances"){
			if(uiConstants.common.DEBUG_MODE){
				console.log("=======================Add ComponentInstance Result Handler =====================================");
				console.log(data);
			}
			var res = data.result;
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(ERROR_ADD_COMP_INSTANCES, "error");
			}
			else{
				showMessageBox(uiConstants.kpiConfig.SUCCESS_MULTIPLE_ADD_KPIS);
				$('#tableCompInstAddMultiple tbody').empty();
				/*if(self.compAttributesArr().length){
					$("#btnCompInstMultipleAdd").trigger('click');
				}*/
			}
		}
	}

	function errorCallback(reqType) {
		if(reqType === "getComponentAttributes"){
			showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_LIST, "error");
		}
	}

}

Addmultiplecomponentinstance.prototype.dispose = function() { };
return { viewModel: Addmultiplecomponentinstance, template: templateMarkup };

});
