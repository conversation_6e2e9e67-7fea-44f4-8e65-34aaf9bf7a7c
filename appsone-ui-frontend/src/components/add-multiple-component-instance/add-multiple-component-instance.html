 <div class="panel panel-default">
	<div class="configPanel panel-heading"><h4><span data-bind="text: pageSelected"></span></h4></div>
	<div class="panel-body">
		<div class="col-sm-12" id="listCompInstDetailsPage" data-bind="template: {afterRender: renderHandler}" >	

				<form class="form-horizontal" role="form" >
					<div class="form-group form-required">
						<label class="control-label col-sm-2">Component Type <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compTypeList" data-bind="foreach : componentsArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->
								<option data-bind="value: $data.componentTypeId, text: $data.componentType"></option>							
							</select>
						</div>
					</div>

					<div class="form-group form-required">
						<label class="control-label col-sm-2">Component <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compNamesList" data-bind="foreach : componentNamesArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->
								
								<option data-bind="value: $data.componentId, text: $data.componentName"></option>
							</select>
						</div>
					</div>

					<div class="form-group form-required">
						<label class="control-label col-sm-2">Version <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compVersionsList" data-bind="foreach : componentVersionsArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->
								
								<option data-bind="value: $data.versionId, text: $data.version"></option>
							</select>
						</div>
					</div>
				</form>

				<div class=text-right>
					<button type="button" class="btn btn-default" id="btnCompInstMultipleAdd" data-bind="click: onAddClick">Add Row</button> 
				</div>
				<br>
			 	<div>
			        <table class="table table-bordered table-hover table-striped" id="tableCompInstAddMultiple" >
			          <thead>
			            <tr data-bind="foreach: tableHeaders" >
			          		<th data-bind="{css: $data.attributeName == '' || $data.attributeName == 'Sl.No.' ? 'col-xs-1' : 'col-xs-2'}">
				          		<span data-bind="text : $data.attributeName"></span>
				          		<span data-bind="if : $data.isMandatory" class="mandatoryField">*</span>
				          	</th> 
			          	</tr> 
			          </thead>
			          <tbody>
			         		
			          </tbody>         
					</table>
				</div> 
			
			
			<div class=text-right> 
			     <button class="btn btn-default disabled" id="btnSave" data-bind="click: onMultipleSaveClick">Save</button> 
			     <button class="btn btn-default disabled" id="btnClearAll" data-bind="click: onClearAll">Clear All</button> 
			     <button class="btn btn-default" id="btnCancel" data-bind="click : cancelAddScreen">Cancel</button>
			</div> 
		</div>
	</div>
</div>
