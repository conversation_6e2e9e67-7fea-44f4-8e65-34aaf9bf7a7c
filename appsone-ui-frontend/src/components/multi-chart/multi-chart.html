<div data-bind="template: {afterRender: renderHandler}">
	<div data-bind="attr: {'id': MCPodId}">
    
  </div>
</div>

<!-- Expandable view as Modal -->
<div data-bind="attr :{'id':'podModal_'+podId()}" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content">
        <div class="modal-header">
          <!-- <button type="button" class="close" data-dismiss="modal">&times;</button> -->
          <ul data-bind="style:{'float':'right'}">
            <li class="glyphicon glyphicon-remove" data-dismiss="modal" data-bind="style:{'display':'inline', 'margin': '2px 4px', 'float':'right'}"></li>
            <li class="dropdown" data-bind="style:{'display':'inline', 'margin': '2px 4px'}">
                <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                    <i class="glyphicon glyphicon-download-alt"></i>
                </span>
                <ul class="dropdown-menu pull-right" role="menu">
                    <li data-bind="attr:{'id': podId()+'_Modal_exportPDF'}"><span>PDF Document</span></li>
                    <li data-bind="attr:{'id': podId()+'_Modal_exportPNG'}"><span>PNG Image</span></li>
                    <!-- <li data-bind="attr:{'id': podId()+'_Modal_exportJPG'}"><span>JPG Image</span></li> -->
                    <li data-bind="attr:{'id': podId()+'_Modal_exportCSV'}"><span>CSV Document</span></li>
                </ul>
            </li>
          </ul>
          <h4 class="modal-title" data-bind="text:podTitle"></h4>
        </div>

      <div class="modal-body">
        <p>Some text in the modal.</p>
        <!-- <div data-bind="component:{name: currentComponent, attr:{'height':'100%'}, params:{'podId':'id', 'podTitle': 'Title', 'podType': 'type'}}"></div> -->
      </div>
      <!-- <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div> -->
    </div>

  </div>
</div>