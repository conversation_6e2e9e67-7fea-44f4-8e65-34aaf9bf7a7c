define(['jquery','knockout','fusionCharts','text!./multi-chart.html','hasher','ui-constants','ui-common'], function($,ko,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function MULTICHART(params) {
    this.message = ko.observable('Hello from the multi-chart component!'); 
    var self = this;
    this.MCPodId = ko.observable(params.podId+'_multi-chart-container');
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.isModal = ko.observable(params.isModal);


    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
        //$('#podBody_'+params.podId).css('width',self.currentPodBodyWidth()+'px');


        self.initChart();
    }


    this.initChart = function(){
    

         FusionCharts.ready(function () {
                    var revenueChart = new FusionCharts({
                        type: 'mscombidy2d',
                        renderAt: self.MCPodId(),
                        width: self.currentPodBodyWidth()-5,
                        height: self.currentPodBodyHeight() -10,
                        dataFormat: 'json',
                        dataSource: {
                            "chart": {
                                "caption": "",
                                "subCaption": "",
                                "xAxisname": "Time",
                                "pYAxisName": "Count",
                                "sYAxisName": "Response Time",
                                "numberPrefix": "",
                                "sNumberSuffix" : "",
                                "sYAxisMaxValue" : "50",
                                "showValues": "0",

                                //Cosmetics
                                "plotSpacePercent":"40",
                                "plotHighlightEffect": "fadeout",
                                "paletteColors" : "#0075c2,#E62424",
                                "baseFontColor" : "#333333",
                                "baseFont" : "Helvetica Neue,Arial",
                                "captionFontSize" : "14",
                                "subcaptionFontSize" : "14",
                                "subcaptionFontBold" : "0",
                                "showBorder" : "0",
                                "bgColor" : "#ffffff",
                                "showShadow" : "0",
                                "canvasBgColor" : "#ffffff",
                                "canvasBorderAlpha" : "0",
                                "divlineAlpha" : "100",
                                "divlineColor" : "#999999",
                                "divlineThickness" : "1",
                                "divLineIsDashed" : "1",
                                "divLineDashLen" : "1",
                                "divLineGapLen" : "1",
                                "usePlotGradientColor" : "0",
                                "showplotborder" : "0",
                                "showXAxisLine" : "1",
                                "xAxisLineThickness" : "1",
                                "xAxisLineColor" : "#999999",
                                "showAlternateHGridColor" : "0",
                                "showAlternateVGridColor" : "0",
                                "legendBgAlpha" : "0",
                                "legendBorderAlpha" : "0",
                                "legendShadow" : "0",
                                "legendItemFontSize" : "10",
                                "legendItemFontColor" : "#666666",
                                // Enable export
                                "exportEnabled": "1",
                                "exportAtClientSide":"1",
                                // Hide export menu item
                                "exportShowMenuItem": "1",
                                "showExportDialog":"1",
                                "exportDialogMessage":"Capturing Data : ",
                                "exportDialogColor":"#333",
                                "exportDialogPBColor":"#0372ab",
                                "toolbarButtonColor":"#999999",
                                "exportFileName":self.podTitle(),
                                //Tooltip customization        
                                "toolTipColor": "#ffffff",
                                "toolTipBorderThickness": "0",
                                "toolTipBgColor": "#000000",
                                "toolTipBgAlpha": "80",
                                "toolTipBorderRadius": "2",
                                "toolTipPadding": "5",
                                //External image url path for logo
                                //"logoURL": "/../../images/logo.png",
                                //Changing logo alpha
                                "logoAlpha": "40",
                                //Scaling logo image
                                "logoScale": "40",
                                //Setting logo position
                                "logoPosition": "BR"
                            },
                            "categories": [{
                                "category": [
                                    { "label": "10:00" },
                                    { "label": "10:05" },
                                    { "label": "10:10" },
                                    { "label": "10:15" },
                                    { "label": "10:20" },
                                    { "label": "10:25" },
                                    { "label": "10:30" },
                                    { "label": "10:35" },
                                    { "label": "10:40" },
                                    { "label": "10:45" },
                                    { "label": "10:50" },
                                    { "label": "10:55" }, 
                                    
                                ]
                            }
                                          ],
                            "dataset": [
                                {
                                    "seriesName": "Count",
                                    "data": [
                                        { "value" : "16000" },
                                        { "value" : "20000" },
                                        { "value" : "18000" },
                                        { "value" : "19000" },
                                        { "value" : "15000" },
                                        { "value" : "21000" },
                                        { "value" : "16000" },
                                        { "value" : "20000" },
                                        { "value" : "17000" },
                                        { "value" : "22000" },
                                        { "value" : "19000" },
                                        { "value" : "23000" }
                                    ]
                                }, 
                                {
                                    "seriesName": "Response Time",
                                    "renderAs": "area",                                    
                                    "data": [
                                        { "value" : "4000" },
                                        { "value" : "5000" },
                                        { "value" : "3000" },
                                        { "value" : "4000" },
                                        { "value" : "1000" },
                                        { "value" : "7000" },
                                        { "value" : "1000" },
                                        { "value" : "4000" },
                                        { "value" : "1000" },
                                        { "value" : "8000" },
                                        { "value" : "2000" },
                                        { "value" : "7000" }
                                    ]
                                }, 
                                /*{
                                    "seriesName": "Transaction Volume",
                                    "parentYAxis": "S",
                                    "renderAs": "line",
                                    "showValues": "0",
                                    "data": [
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },
                                        { "value" : "25" },

                                    ]
                                }*/
                            ]
                        }
                    });
                    
                    revenueChart.render();
                });
    }

  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  MULTICHART.prototype.dispose = function() { };
  
  return { viewModel: MULTICHART, template: templateMarkup };

});