define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./kpi-group-list-view.html','hasher','validator','ui-constants','ui-common','jQuery-plugins','fsstepper','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,jQueryPlugins,fsstepper,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {

	function KpiGroupListGridView(params) {
		var self = this;
		var configTableHeaders = ["Name","Type","Category","Created Time","Modified Time","Modified By","Tags","Status"];
		var listData = {};
		var filterForFirstPage = false;
		var fConfigName=null;
		var fKpiType="0";
		var fConfigActiveInactive="1";
		var fCategory="2";
		var fTags=null;
		var fCreatedTime="";
		var fUpdatedTime="";
		var fUpdatedBy="";
		var colSortOrder = 0;
		var colToSort = "name";
		var filterTxtAfterDelete;

		this.gridHeader = ko.observableArray();
		this.filterGridHeader = ko.observableArray(["Select","Name","Type","Category","Created Time","Modified Time","Modified By","Tags","Status"]);
		this.gridData = ko.observableArray();
		this.configTableHeaderObjKeys = ko.observableArray(["name","groupType","isCustom","createdTime","updatedTime","updatedBy","tags","status"]);
		this.noSortColKeys = ko.observableArray(["tags"]);
		this.currentPage = ko.observable(0);
		this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
		this.totalRecordsPerPage = ko.observable(this.numOfPagesOption()[0]);
		this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
		this.enableAdd = ko.observable(true);
		this.enableEdit = ko.observable(false);
		this.enableClone = ko.observable(false);
		this.currentViewIndex = ko.observable(uiConstants.common.LIST_VIEW);
		this.selectedConfigRows = ko.observableArray();
		this.gridHeader(configTableHeaders);
		this.errorMsg = ko.observable("");
		this.isFilterOrList = ko.observable("list");
		this.recordsCountLabel = ko.observable("");
		this.pageSelected = ko.observable("KPI Group Configuration");
		this.enableFilter = ko.observable(true);
		this.showListAvailable = ko.observable("");
		this.kpiTypeArr = ko.observableArray();
		this.modifiedCols = ko.observableArray([true,true,true]);
		this.selFilterCategory = ko.observable();
		this.filtersAdded = ko.observable("");
		this.filterValueArr = ko.observableArray([]);
		
		this.renderHandler=function(){
			/*$(".wrapper").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
				this.querySelector("#filterRow").style.transform = translate;
	        });*/

			$(window).resize(function(){
			    self.refreshPageLayout();
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$('.columnsList').checklistbox({
			    data: [
			    	{"name": "Created Time", "id": 1},
			    	{"name": "Modified Time", "id": 2},
			    	{"name": "Modified By", "id": 3}
			    ]
			});
			$('.columnsList .checkList').prop("checked", true);

			$("div").on("click", "#selAllCols", function(e){
				$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
			});

			$("div").on("change", ".columnsList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
				}
	        });

			$("#fCategory_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fCategory_chosen").trigger('chosen:updated');

			//$("#fActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fActiveInactive_chosen").trigger('chosen:updated');

			//$("#btnAdd").css("display", window.addEnabled ? '' : 'none');
			//$("#btnEdit").css("display", window.updateEnabled ? '' : 'none');
			//$("#btnClone").css("display", window.addEnabled ? '' : 'none');

			localStorage.currentViewIndex = uiConstants.common.LIST_VIEW;
			configType = localStorage.configType = "kpiGroupConfig";
			
			$('#fConfigName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});

			//date definition
			

			/*$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/
			$("#fCategory").val("2").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');

			requestCall(uiConstants.common.SERVER_IP + "/kpiDataTypesWithUnits", "GET", "", "getKpiTypeList", successCallback, errorCallback);

			self.curPage(1);
		}

		self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

		this.onHeaderClick = function(columnNum, columnName){
			if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
				$(".listViewCol").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

				colSortOrder = colSortOrder ? 0 : 1;
				colToSort = columnName;
				//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
				$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

				self.getListData();
			}
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += "," + tagsData[tag].tagName;
			}

			return tagNames.substring(1);
		}

		this.msgShowListData=function(){
			if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
				self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
			}
			else{
				self.showListAvailable(uiConstants.kpiGroupConfig.KPI_GROUPS_NOT_CONFIGURED);
			}
		}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.convertToLocalTime = function(getDateTime){
			return window.gmtToLocalDateTime(getDateTime);
		}

		this.totalPages = ko.computed(function() {
			return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
		}, this);

		this.recordsCountLabel = ko.computed(function() {
			var recordsStartCount = 0;
			var recordsEndCount = 0;

			if(self.currentPage() != 0){
				recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
				recordsEndCount = recordsStartCount + (self.gridData().length - 1);
			}
			return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
		}, this);

		this.enableDisableAdd = function(length){
			self.enableAdd(length>0?false:true)
		}

		this.enableDisableUpdate = function(length){
			if(length == 1)
				self.enableEdit(true);
			else
				self.enableEdit(false);
		};

		this.enableDisableClone = function(length){
			if(length == 1)
				self.enableClone(true);
			else
				self.enableClone(false);
		};

		this.getListOrFilterData = function(){
			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.prevPage = function(){
			if(self.currentPage()>1)
				self.currentPage(self.currentPage()-1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
	
		this.nextPage = function(){
			if(self.currentPage()<self.totalPages())
				self.currentPage(self.currentPage()+1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.curPage = function(curPage){
			resetButtonStates();
			self.currentPage(curPage);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.showFilterBox = function(){
			if(!self.filterValueArr().length){
	            for(var headerTxt in self.gridHeader()){
	            	self.filterValueArr.splice(headerTxt, 0, "");
	            }
			}
			$("#filterCriteria").trigger("chosen:updated");
			$("#filterCriteria_chosen").addClass("filterFieldWidth");
			$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
			$("#filterBox").css("display", "block");

			self.onFilterCatChange();

			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.onFilterCatChange = function(){
			if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
				$('#filterCreateModTime').datetimepicker({
					format: "YYYY-MM-DD HH:00",          
					stepping: 1,
					useCurrent: true, 
					//defaultDate: null,
					showTodayButton: false,          
					collapse: true,
					sideBySide: false
				})
				.on('dp.show', function(e){
					if($('#filterCreateModTime input').val() == ""){
						$(this).data("DateTimePicker").date(moment());
					}
				})
				.on('dp.change', function(e){
					if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
						$(this).data("DateTimePicker").maxDate(moment());
						var maxHour = parseInt(moment().format("HH"));
						//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
						//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
					}
					else{
						$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
						//$(this).data("DateTimePicker").disabledHours([]);
					}

					self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();

				})
				.on('dp.hide', function(e){
					self.onFilterAdd();
				});
				$('#filterCreateModTime input').val("");
			}
		}

		this.onFilterAdd = function(){
			if(self.filtersAdded() == ""){
				self.filtersAdded($("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
				$('#filters-tokenfield-typeahead').tokenfield({
					delimiter: ['|']
				});
			}


			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];
			var filterCategoryFound = 0;
			
			self.filtersAdded("");
			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");
				
				if(filterCategoryArr[0].trim() == self.selFilterCategory()){
					filterCategoryArr[1] = self.getFiltersToAdd(self.selFilterCategory());
					filterCategoryFound = 1;
				}

				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCategoryArr[0].trim()+":"+filterCategoryArr[1]);

				if(filters == "0"){
					$('#filters-tokenfield-typeahead').tokenfield({
						delimiter: ['|']
					});
				}
			}

			if(filterCategoryFound == 0){
				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + $("#filterCriteria option:selected").text() + ":" + self.getFiltersToAdd(self.selFilterCategory()));
			}
			filterCategoryFound = 0;

			var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fConfigName=null;
						fKpiType="0";
						fConfigActiveInactive="1";
						fCategory="2";
						fTags=null;
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

		this.getFiltersToAdd = function(category){
			if(category == "Type"){
				return $("#searchType option:selected").text();
			}
			else if(category == "Category"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Standard' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Custom' : 'All');
				return $("#fCategory option:selected").text();
			}
			else if(category == "Status"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
				return $("#fActiveInactive option:selected").text();
			}
			else{
				return self.filterValueArr()[self.gridHeader.indexOf(category)];
			}
		}

		this.resetFilter = function(){
			showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
				if(confirm){
					self.resetFilterConfirmed();
				}
			});
		}

		this.resetFilterConfirmed = function(){
			self.filtersAdded("");
			for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }

			$("#searchType").val("").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');
			$("#fCategory").val("2").trigger('chosen:updated');
			$('#filterCreateModTime').val("");
			self.errorMsg("");
			self.currentPage(1);
			self.isFilterOrList("list");

			fConfigName=null;
			fKpiType="0";
			fConfigActiveInactive="1";
			fCategory="2";
			fTags=null;
			fCreatedTime="";
			fUpdatedTime="";
			fUpdatedBy="";

			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
			self.selFilterCategory("Select");

			requestCall(uiConstants.common.SERVER_IP + "/kpiGroups?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&name=" +null+"&isCustom=" +fCategory+"&kpiType=" +fKpiType+"&status="+fConfigActiveInactive+"&tags="+fTags+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorMessage) {
        	if(errorMessage != ""){
	        	scrollToPos(0, 300);
	        }
		});

		this.getFilterListData = function(filterApplied){
			self.isFilterOrList("filter");
			filterForFirstPage = filterApplied;
			setFiltersToVariables();
			var resValue = true;
			var pageOffset = self.currentPage();

			this.errorMsg("");

			/*if($("#fConfigName").val() != "" && $("#fConfigName").val().length < 2){
				self.errorMsg(uiConstants.kpiGroupConfig.KPI_GROUP_NAME_MIN_LENGTH_ERROR);
				resValue = false;
			}
			else if($("#fConfigName").val() != "" && $("#fConfigName").val().length > 45){
				self.errorMsg(uiConstants.kpiGroupConfig.KPI_GROUP_NAME_MAX_LENGTH_ERROR);
				resValue = false;
			}*/

			resetPagination();
	
			if(resValue){
				if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterApplied);
				if(filterApplied){
					pageOffset = 1;
				}

				$("#btnApplyFilter").css("display", "none");
				$("#filterOptionsBox").css("display", "none");
				$("#filterOptionsDispBtn").css("display", "");
				requestCall(uiConstants.common.SERVER_IP + "/kpiGroups?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ pageOffset +"&name=" +fConfigName+"&kpiType=" +fKpiType+"&isCustom=" +fCategory+"&status="+fConfigActiveInactive+"&tags="+fTags+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy  , "GET", "", "getListData", successCallback, errorCallback);
			}
		}

		this.onFilterOptionsDispClick = function(){
			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			
			self.refreshPageLayout();
		}

		this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

		this.getListData = function(){
			self.isFilterOrList("list");
			resetPagination();
			if(window.globalSearchTxt){
				fConfigName = window.globalSearchTxt;
			}
			$('#fConfigName').val(fConfigName);
			//window.globalSearchTxt = "";
			requestCall(uiConstants.common.SERVER_IP + "/kpiGroups?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&name=" +fConfigName+"&kpiType=" +fKpiType+"&isCustom=" +fCategory+"&status="+fConfigActiveInactive+"&tags="+fTags+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy  , "GET", "", "getListData", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/5885f07f0f0000cb35ff6653?callback=?", "GET", "", "getListData", successCallback, errorCallback);
		}

		this.switchView = function (viewIndex){
			localStorage.currentViewIndex = viewIndex;
			self.currentViewIndex(viewIndex);

			if(self.currentViewIndex() == uiConstants.common.LIST_VIEW){
				self.pageSelected("KPI Group Configuration");
			}

			else if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				self.pageSelected("Add KPI Group");
			}
		}

		this.editConfig = function(){
			getSelectedConfigRows(null);

			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
			self.pageSelected("Edit KPI Group");
			self.switchView(uiConstants.common.EDIT_VIEW);
		}

		this.cloneConfig = function(){
			getSelectedConfigRows(null);
			if(self.selectedConfigRows()[0].status == 0){
				showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "KPI Group"), "error");
			}
			else{
				self.pageSelected("Clone KPI Group");
				self.switchView(uiConstants.common.CLONE_VIEW);
			}
		}

		this.viewConfig = function(viewObj){
			getSelectedConfigRows(viewObj);
			self.pageSelected("KPI Group Configuration");
			self.switchView(uiConstants.common.READ_VIEW);
		}

		function getSelectedConfigRows(viewObj){
			self.selectedConfigRows([]);

			if(viewObj != null){
				if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
				self.selectedConfigRows.push(viewObj);
			}
			else{
				for(objData in self.gridData()){
					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(self.gridData()[objData]));

					if(self.gridData()[objData].isSelected){
						self.selectedConfigRows.push(self.gridData()[objData]);
						break; //exiting loop since multiple entry is not allowed to edit
					}
				}			
			}

			localStorage.selectedConfigRows = JSON.stringify(self.selectedConfigRows());
		}

		$('#listConfigDetailsPage table').on('click', '.chkboxCol', function(e){
			var rowIndex = $(this).parent().parent().index();
			var chkState = $(this).prop("checked");
			$(".chkboxCol").prop("checked",false);
			$(this).prop("checked",chkState);

			for(objData in self.gridData()){
				self.gridData()[objData].isSelected=false;
			}

			self.gridData()[rowIndex].isSelected = chkState;
			self.handleChkClick();
		});

	    $('#listgrid tbody').on('dblclick', 'tr', function(e){
	    	if(e.target.parentNode.rowIndex != undefined)
	    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
		});

		self.onNameClick = function(){
			self.viewConfig($(this)[0]);
		}

		self.handleChkClick = function(){
			var length = $('.chkboxCol:checked').length;
			self.enableDisableAdd(length);
			self.enableDisableUpdate(length);
			self.enableDisableClone(length);
		}

	    function resetButtonStates(){
			self.enableDisableAdd(0);
			self.enableDisableUpdate(0);
			self.enableDisableClone(0);
		}

		function setFiltersToVariables(){
			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];

			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");

				if(filterCategoryArr[0] == "Name"){
					fConfigName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Type"){
					fKpiType = $.grep(self.kpiTypeArr(), function(e){
						return e.kpiType == filterCategoryArr[1];
					})[0].kpiTypeId;
				}
				else if(filterCategoryArr[0] == "Category"){
					fCategory = filterCategoryArr[1] == 'Standard' ? 0 : (filterCategoryArr[1] == 'Custom' ? 1 : 2);
				}
				else if(filterCategoryArr[0] == "Created Time"){
					fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
				}
				else if(filterCategoryArr[0] == "Modified Time"){
					fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
				}
				else if(filterCategoryArr[0] == "Modified By"){
					fUpdatedBy = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Tags"){
					fTags = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Status"){
					fConfigActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
				}
			}
		}

		function resetPagination(){
			if(self.totalPages() != 0){
				if(typeof self.currentPage() == "string"){
					self.currentPage(parseInt(self.currentPage()));
				}

				if(self.currentPage() == "" || isNaN(self.currentPage()))
					self.currentPage(1);
				else if(self.currentPage()>self.totalPages())
					self.currentPage(self.totalPages());
				else if(self.currentPage()<1)
					self.currentPage(1);
			}
		}

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			if(reqType === "getKpiTypeList"){
				self.kpiTypeArr.removeAll();		
				self.kpiTypeArr.push.apply(self.kpiTypeArr,data.result);

				$("#searchType").trigger('chosen:updated');
			}
			else if(reqType === "getListData"){
				resetButtonStates();

				if(data.responseStatus == "success"){
					listData = data;
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					self.totalRecords(listData.totalRecords);
					
					//self.gridData(listData.result);

					$("#listgrid #gridDataBody").empty();
			 		$("#listgrid").trigger("update");
					self.gridData(listData.result);

					var initialSortColumn = 1;
					var sortOrder = 0; //0=asc; 1=desc
					if(!$("#listgrid").hasClass("tablesorter")){
						if(!self.gridData().length){
							self.enableFilter(false);
						}
						else{

							if (!$("#pageNum").hasClass("select2-hidden-accessible")){
								debugger;
								$("#pageNum").select2();

								$("#pageNum").select2("open");
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
								$("#select2-pageNum-container").parent().css({
									"border": "none",
									"outline": "none"
								});

								$("#pageNum").parent().children("span").css("width", "36px");
								//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
								$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
								$("#select2-pageNum-container").css({
										"font-weight": "bold",
										"color": "#218DC0",
										"padding-left": "4px"
									});

								$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
								$(".pageNumDropDown .select2-search").css("display", "none");
								$("#select2-pageNum-results").css("overflow-x", "hidden");
								$("#pageNum").select2("close");
							}
						}

						$("#listgrid").addClass("tablesorter")
						/*$("#listgrid").tablesorter({
							//ignoreCase : false,
							cancelSelection: false,
							headers: { 0: { sorter: false}, 7: { sorter: false} },

							widgetOptions: {
						      sortTbody_primaryRow : '.main',
						      sortTbody_sortRows   : false,
						      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
						    }
							//sortList: [[initialSortColumn, sortOrder]]
						});

						$("#listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

			            var $tab = $('#listgrid');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper');
							}
						});

						$('#unselectAll').on('click', function(e){
							window.unselectAllCheck(self.handleChkClick, '.chkboxCol');
						});
					}
					/*else{
						$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
						
					}*/

					/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
*/

					$("#listgrid").trigger("update");
					self.refreshPageLayout();

					if((self.currentPage() == 0 || filterForFirstPage) && self.gridData().length>0){
						self.currentPage(1);
						filterForFirstPage = false;
					}
					self.msgShowListData();

				}else{
					showMessageBox(data.message, "error");
					self.showListAvailable("");		
				}

				var $tab = $('#listgrid');
				$tab.floatThead('reflow');
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getKpiTypeList"){
				showMessageBox(uiConstants.kpiConfig.ERROR_GET_KPITYPE_LISTS, "error");
  			}
  			else if(reqType === "getListData"){
				showMessageBox(uiConstants.kpiGroupConfig.ERROR_GET_KPI_GROUPS, "error");
  			}
		}
	}

	KpiGroupListGridView.prototype.dispose = function() { };
	return { viewModel: KpiGroupListGridView, template: templateMarkup };
});