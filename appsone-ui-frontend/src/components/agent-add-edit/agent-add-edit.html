<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divAgentConfig">
	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divAgentDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: description" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<!-- ko if: currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && currentViewIndex() != uiConstants.common.CLONE_VIEW -->
				<div class="form-group form-required">
					<label class="control-label col-sm-2">Unique ID</label>
					<div class="col-sm-4">
						<span data-bind="text: uniqueId, style: {fontWeight:'bold'}"></span>
					</div>
				</div>
			<!-- /ko-->

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select id="agentTypeList" class="chosen form-control" data-bind="options: agentTypesArr, optionsText: 'agentType', optionsValue: 'agentTypeId', optionsCaption: uiConstants.agentConfig.SELECT_AGENT_TYPE"></select>
				</div>
			</div>
			
			<div class="form-group form-required" data-bind="visible: (selAgentType() == 'componentagent' || selAgentType() == 'psagent')">
				<label class="control-label col-sm-2">Host</label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="hostsList" data-bind="foreach : hostsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="text: uiConstants.agentConfig.ENTER_SELECT_HOST"></option>
						<!-- /ko-->
						
						<!-- ko if: $data -->
							<option data-bind="text: $data"></option>
						<!-- /ko-->
					</select>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Mode <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- <select id="agentModeList" class="form-control" data-bind="options: agentModesArr, optionsText: 'agentMode', optionsCaption: uiConstants.agentConfig.SELECT_AGENT_MODE"></select> -->

					<select class="chosen form-control" id="agentModeList" data-bind="foreach : agentModesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="text: uiConstants.agentConfig.SELECT_AGENT_MODE"></option>
						<!-- /ko-->

						<!-- ko if: $data -->
							<option data-bind="text: $data"></option>
						<!-- /ko-->
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="agent-tokenfield-typeahead" data-bind="value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			

			<!-- ko if: selAgentType() == 'psagent' -->
				<div class="form-group" style="margin-bottom: 0px;">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading"><a id="imgPsaServerDetailsShowHide" class="glyphicon glyphicon-triangle-bottom block-glyphicon" data-bind="event: {click: expandCollapsePanel.bind($data, psaServerDetailsPanel, imgPsaServerDetailsShowHide)}" title="Collapse"><span class="panel-label-expand-collapse">Server Details</span></a></div>
						<div id="psaServerDetailsPanel" class="panel-body inner-div-container" style="height: auto; padding-bottom: 0px;">
							<button id="btnInterface" type="button" class="btn" style="margin-bottom: 6px;" data-bind="event:{click: addPsaInterface}">Add Interface</button>


							<div>
								<!-- ko foreach: serverDetailsArr() -->
									<div class="panel panel-default" data-bind="attr: {'id': 'divServerDetails'+$index()}">
										<div class="panel-body">
											<div class="multi-value-row">
												<!-- ko if: $data.serverDetIndex() -->
													<label>Interface <span data-bind="text: $data.serverDetIndex()"></span><span class="mandatoryField">*</span></label>
												<!-- /ko -->
												<input type="text" data-bind="attr: {id: 'interface'+$index()}" class="form-control" style="display: inline-block; width:200px" placeholder="Enter Interface">
											</div>

											<div class="multi-value-row">
												<label>Physical Address <span class="mandatoryField">*</span></label>
												<input type="text" data-bind="attr: {id: 'physicalAddr'+$index()}" class="form-control" style="display: inline-block; width:200px" placeholder="Enter Physical Address">
											</div>

											<div class="multi-value-row">
												<label>Virtual IP</label>
												<input type="checkbox" data-bind="click:$parent.handleVirtualHostCol.bind($data,$index()), attr: {id: 'virtualIp'+$index()}">


											</div>
											<div class="multi-value-row" style="float: right;">
												<span class="glyphicon glyphicon-remove buttondelete btnDeleteInterface" data-bind="event:{click: function(){$parent.deletePsaInterface($index())}}" title="Delete"></span>
											</div>


											<div class="form-group">
												<div class="panel panel-default inner-panel" style="margin-top: 10px;">
													<div class="panel-body">
														<div class="multi-value-row">
															<button type="button" class="btn btnAddFilter" style="display: inline-block;margin-bottom: 6px;" data-bind="event:{click: function(){$parent.addPsaFilter($index(),$parent.psaFiltersArr()[$index()].length)}}">Add Filter Details</button>

															<div style="float: right;">
																<button type="button" class="btn btnCloneFilter" style="display: inline-block;margin-bottom: 6px;" data-bind="attr: {id: 'btnInterfaceClone'+$index()}, event:{click: function(){$parent.clonePsaFilter($index())}}" disabled>Clone</button>

																<button type="button" class="btn btnDeleteFilter" style="display: inline-block;margin-bottom: 6px;" data-bind="attr: {id: 'btnInterfaceDelete'+$index()}, event:{click: function(){$parent.deletePsaFilter($index())}}" disabled>Delete</button>
															</div>
														</div>

														<div class="wrapper-scroll-table" style="height: 400px;">
															<table data-bind="attr: {id: 'tblPsaFilterDetails'+$index()}" class="table table-bordered table-hover table-striped tablesorter" style="width:100%; margin-top: 0px;">
																<thead>
																	<tr class="a1-inner-table-thead">
																		<th style="display: none"></th>
																		<th style="width: 50px">Sl. No.</th>
																		<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
																			<th class="actionControl"><input type="checkbox" data-bind="attr: {id: 'psaInterfaceSelAll'+$index()}, click:  $parent.handlePsaSelAll.bind($data,$index())" title="Select All"/></th>
																		<!-- /ko-->

																		<th class="" style="display: none;" data-bind="attr: {class: 'tableHeaderOverflowOmmiter col-xs-4 virtualHostCol'+$index()}">Virtual Host <span class="mandatoryField">*</span></th>

																		<th class="tableHeaderOverflowOmmiter col-xs-4">Applications <span class="mandatoryField">*</span></th>

																		<th class="tableHeaderOverflowOmmiter col-xs-4">Port/Port Range <span class="mandatoryField">*</span></th>

																		<th class="tableHeaderOverflowOmmiter col-xs-4">Protocol <span class="mandatoryField">*</span></th>

																		<th class="tableHeaderOverflowOmmiter col-xs-4" title="Business Value Extractor Settings">Response Settings</th>
																	</tr>
																</thead>

																<tbody data-bind="foreach : $parent.psaFiltersArr()[$index()]">
																	<tr>
																	<td style="display: none">
																		<span data-bind="attr: {id: 'filterId'+$parentContext.$index()+$index()}"></span>	
																	</td>
																	<td data-bind="text: $index()+1" style="text-align: center;"></td>
																	<!-- ko if: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW -->
																		<td style="text-align:center"><input type="checkbox" data-bind="value: $index(), click: $parents[1].handlePsaSelCol.bind($data,$parentContext.$index()), attr: {class: 'psaCheckboxCol'+$parentContext.$index()}" title="Select"/></td>
																	<!-- /ko-->

																	<!-- <td data-bind="text: $data.virtualHost"></td> -->

																	<td style="display: none;" data-bind="attr: {class: 'virtualHostCol'+$parentContext.$index()}">

																	<input type="text" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaVirtualHost'+$parentContext.$index()+$index()}">

																	
																	<!-- <select class="chosen form-control psaVirtualHostClass" style="display: inline-block; width:200px" data-bind="attr: {id: 'psaVirtualHost'+$parentContext.$index()+$index()}, foreach : $parents[1].psaVirtualHostsArr" data-placeholder="" >
																		
																		<option data-bind="value: $data.virtualHostId, text: $data.virtualHostName"></option>
																	</select> -->

																	</td>

																	<td>
																		<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {id: 'selAllPsaApps'+ $parentContext.$index()+$index()}, click:$parents[1].handlePsaAppsSelAll.bind($data,$parentContext.$index(), $index())"> Select All</input></label>

																		<div class="inner-div-container" data-bind="attr: {id: 'psaApplications'+$parentContext.$index()+$index()}" style="height: 100%; max-height: 120px;"></div>
																	</td>

																	<td>
																		<div>
																			<label  class="config-option-label">
																			<input type="radio" data-bind="value:'port', attr: {'name': 'radioPortRange'+$parentContext.$index()+$index()}, click:$parents[1].handlePortRange.bind($data,$parentContext.$index(), $index())" checked>
																				Port
																			</label>

																			<label  class="config-option-label">
																			<input type="radio" data-bind="value:'portRange', attr: {'name': 'radioPortRange'+$parentContext.$index()+$index()}, click:$parents[1].handlePortRange.bind($data,$parentContext.$index(), $index())">
																				Port Range
																			</label>
																		</div>
																		<div style="display: inline-block; width: 100%">
																			<label class="form-sub-label" style="display: none" data-bind="attr: {'id': 'portFromLbl'+$parentContext.$index()+$index()}">From</label>
																			<input type="number" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaPortFrom'+$parentContext.$index()+$index()}" min="0" max="65535">
																		</div>

																		<div style="display: inline-block; width: 45%;     float: right;">
																			<label class="form-sub-label" style="display: none" data-bind="attr: {'id': 'portToLbl'+$parentContext.$index()+$index()}">To</label>
																			<input type="number" style="display: none" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaPortTo'+$parentContext.$index()+$index()}" min="0" max="65535">
																		</div>
																	</td>

																	<td style="vertical-align: top;">
																		<div style="margin-bottom: 10px;">
																			<select class="chosen form-control" data-bind="foreach: $parents[1].psaFilterProtocolArr, attr: {id: 'psaProtocol'+$parentContext.$index()+$index()}, 

																			event:{change: function(){$parents[1].handlePsaProtocol($parentContext.$index(), $index())}}">
																				<!-- ko if: $index() == 0 -->
										<option data-bind="value: '0', text: uiConstants.common.SELECT"></option>
																				<!-- /ko -->
											<option data-bind="value: $data.masterId, text: $data.name"></option>
										</select>
																		</div>

																		<div data-bind="attr: {id: 'divPsaKeyFilePath'+$parentContext.$index()+$index()}" style="display: inline-block; width: 40%;">
																			<label class="form-sub-label textOverflowOmmiter" title="Key File Path">Key File Path</label>
																			<input type="text" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaKeyFilePath'+$parentContext.$index()+$index()}"  disabled>
																		</div>

																		<div data-bind="attr: {id: 'divPsaKeyFilePwd'+$parentContext.$index()+$index()}" style="display: inline-block; width: 50%;     float: right;">
																			<label class="form-sub-label textOverflowOmmiter" title="Key File Password">Key File Password</label>
																			<input type="password" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaKeyFilePwd'+$parentContext.$index()+$index()}" disabled>
																		</div>
																	</td>

																	<td>

																		<!-- ko foreach: $parents[1].responseArr() -->
																		<div>
																		    <label class="config-option-label" style="margin-left: 20px;">
																				<input type="checkbox"  data-bind="click:$parents[2].handlePsaBodyParam.bind($data,$parentContext.$parentContext.$index(), $parentContext.$index(),$index(), $data.name), 

																				value: $data.name,

																				attr: {'disabled' : slugify($parents[2].psaFilterProtocolArr()[0]['name'].indexOf('finicore')) != -1, class: 'psaResponseChkClass'+$parentContext.$parentContext.$index()+$parentContext.$index(), id: 'psaResponseChk'+$parentContext.$parentContext.$index()+$parentContext.$index()+$index()}"> <span data-bind="attr: {id: 'spanResponse'+$parentContext.$parentContext.$index()+$parentContext.$index()+$index()}, text: $data.name"></span>
																			</label>
																		</div>

																		<!-- /ko-->

																		<!-- 

																		<div>
																			<input type="checkbox" data-bind="click:$parents[1].handlePsaBodyParam.bind($data,$parentContext.$index(), $index()), attr: {id: 'psaBodyChk'+$parentContext.$index()+$index()}">Body</input>
																		</div> -->

																		<div style="display: inline-block; width: 40%">
																			<label class="form-sub-label textOverflowOmmiter">Offset</label>
																			<input type="number" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaBodyOffset'+$parentContext.$index()+$index()}" disabled min="0">
																		</div>

																		<div style="display: inline-block; width: 50%;     float: right;">
																			<label class="form-sub-label textOverflowOmmiter">Size(in bytes)</label>
																			<input type="number" class="form-control" style="width: 100%" data-bind="attr: {id: 'psaBodySize'+$parentContext.$index()+$index()}" disabled min="0">
																		</div>
																	</td>
																	</tr>
																</tbody>
															</table>
														</div>
													</div>
												</div>
											</div>
										</div>
									</div>
								<!-- /ko-->
									</div>
						</div>
					</div>
				</div>

				<!-- /ko-->

				<div data-bind="{visible:(selAgentType() == 'psagent' && mode!='wizard')}" class="form-group" style="margin-bottom: 0px;">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading"><a id="imgPsAgentShowHide" class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="event: {click: expandCollapsePanel.bind($data, psaSettingsPanel, imgPsAgentShowHide)}" title="Expand"><span class="panel-label-expand-collapse">Advanced Settings</span></a></div>
						<div id="psaSettingsPanel" class="panel-body inner-div-container" style="height: auto; padding-bottom: 0px; display: none;">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Data Communication Protocol <span class="mandatoryField">*</span></label>
								<div class="col-sm-4" style="display: inline-flex;">
									<select id="psaDataComProtocol" class="chosen form-control" data-bind="event:{change: onPsaComProtocolChange}, options: psaDataComProtocolArr, optionsText: 'protocolName', optionsValue: 'protocolId', optionsCaption: uiConstants.agentConfig.SELECT_COM_PROTOCOL"></select>
								</div>
							</div>

							<!-- <div class="form-group form-required">
								<label class="control-label col-sm-2">Data Communication Host <span class="mandatoryField">*</span></label>
								<div class="col-sm-4">
									<input type="text" class="form-control" id="txtPsaDataComHost" data-bind="value: psaDataComHost" placeholder="Enter Host" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Data Communication Port <span class="mandatoryField">*</span></label>
								<div class="col-sm-4">
									<input type="number" class="form-control" id="txtPsaDataComPort" data-bind="value: psaDataComPort" placeholder="Enter Port" min="0" max="65535" required>
								</div>
							</div> -->

							<div class="form-group">
									<label class="control-label col-sm-2">GRPC Setting <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<select id="psaGrpcSetting" class="chosen form-control" data-bind="event:{change: onPsaComProtocolChange}, foreach : grpcSettingArr" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.agentConfig.SELECT_GRPC_SETTING"></option>
											<!-- /ko-->

											<option data-bind="value: $data.id, text: $data.name"></option>
										</select>
									</div>
								</div>

							<div class="form-group">
								<label class="control-label col-sm-2" >HTTP Proxy</label>
								<div class="col-sm-4">
									<input type="checkbox" id="chkPsaHttpProxy" data-bind="click:handleHttpProxyParams.bind($data)">
								</div>
							</div>

							<div class="form-group" id="divHttpProxy">
								<div class="multi-value-row">
									<label class="col-sm-2" style="text-align: right;">Protocol <span class="mandatoryField">*</span></label>

									<select id="psaHttpProxyProtocol" class="chosen form-control" style="display: inline-block; width: auto; margin-left: 5px;" data-bind="options: psaHttpProxyProtocolArr, optionsText: 'name', optionsValue: 'masterId', optionsCaption: uiConstants.agentConfig.SELECT_PROTOCOL" disabled></select>
								</div>

								<div class="multi-value-row">
									<label>Host <span class="mandatoryField">*</span></label>
									<input type="text" id="txtPsaHttpProxyHost" class="form-control" data-bind="value: psaHttpProxyHost" style="display: inline-block; width:200px" placeholder="Enter Host" placeholder="Enter Host" disabled>
								</div>

								<div class="multi-value-row">
									<label>Port <span class="mandatoryField">*</span></label>
									<input type="number" class="form-control" id="txtPsaHttpProxyPort" data-bind="value: psaHttpProxyPort" style="display: inline-block; width:200px" placeholder="Enter Port" min="0" disabled>
								</div>

							</div>

							<div class="form-group">
								<label class="control-label col-sm-2" >Response Time Type</label>
								<div class="col-sm-4">
									<label class="config-option-label"><input type="radio" name="responseTimeType" data-bind="value: 'dc'" checked><span> DC</span></label>
									<label class="config-option-label" style="margin-left: 15;"><input type="radio" name="responseTimeType" data-bind="value: 'eum'"><span> EUM</span></label>
									<label class="config-option-label" style="margin-left: 15;"><input type="radio" name="responseTimeType" data-bind="value: 'both'"><span> Both</span></label>
								</div>
							</div>


							<div>
								<fieldset class="config-border">
								    <legend class="config-border">Operation Mode:</legend>
								    	<div>
										    <label class="control-label control-option-label">Configuration</label>
										    <!-- ko foreach: operationModeArr() -->
											    <label class="config-option-label" style="margin-left: 20px;">
				      								<input type="radio" name="opModeConfig" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'remote' ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
				   								</label>
			   								<!-- /ko-->
		   								</div>

		   								<div style="margin-top: 10px; display: none">
										    <label class="control-label control-option-label">Data</label>

										    <!-- ko foreach: operationModeArr() -->
											    <label class="config-option-label" style="margin-left: 20px;">
				      								<input type="radio" name="opModeData" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'remote' ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
				   								</label>
			   								<!-- /ko-->
		   								</div>
								</fieldset>
							</div>
						</div>
					</div>
				</div>

			<!-- ko if: selAgentType() == 'componentagent' || selAgentType() == 'jia'-->
				<div class="form-group" style="margin-bottom: 0px;">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading"><a id="imgCompInstanceShowHide" class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="event: {click: expandCollapsePanel.bind($data, compInstancesPanel, imgCompInstanceShowHide)}" title="Expand"><span class="panel-label-expand-collapse">Component Instances</span></a></div>
						<div id="compInstancesPanel" class="panel-body inner-div-container" style="height: auto; padding-bottom: 0px; display: none;">
							<div class="form-group" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
								<div class="col-sm-6" style="display: inline-flex;">
									<label style="white-space: nowrap; margin-right: 5px;">Search by</label>
									<select id="compInstSearchByList" class="chosen form-control" data-bind="options: compInstSearchByArr, optionsCaption: uiConstants.common.SELECT, event:{change: onCompInstSearchByChange}" style="width: auto;"></select>
									<div data-bind="visible: searchCriteriaFlag() == 1" style="width: 100%; margin-left: 5px;">
										<select class="chosen form-control" id="compNamesList" data-bind="foreach : componentNamesArr" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
											<!-- /ko-->
											<option data-bind="value: $data.componentId, text: $data.componentName"></option>
										</select>
									</div>

									<input type="text" style="margin-left: 5px;" class="form-control" id="txtTag" data-bind="visible: searchCriteriaFlag() == 2, value: tagNameSearch" placeholder="Enter Tag" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>

									<input type="text" style="margin-left: 5px;" class="form-control" id="txtIpAddress" data-bind="visible: searchCriteriaFlag() == 3, value: ipAddressSearch" placeholder="Enter Name" max="39" pattern="[a-zA-Z0-9_ ]{7,39}" autofocus required>

									<button type="button" style="margin-left: 5px;" class="glyphicon glyphicon-search" style="margin: auto 5px;" data-bind="visible: searchCriteriaFlag() != 0, attr: {title: uiConstants.common.SEARCH}, event:{click: searchComponentInstances}"></button>
								</div>
							</div>

							<div class="form-group">
								<div class="col-sm-6">
									<table>
										<tr>
											<td style="width: 50%">
												<label style="margin-left: 5px;">Available:</label>
												<div data-bind="visible: selAgentType() == 'componentagent' && currentViewIndex() != uiConstants.common.READ_VIEW">
													<label style="font-weight: normal;"><input type="checkbox" id="selAllAvailCompInst" style="margin-left: 5px;" data-bind="attr: {disabled: availableCompInstArr().length == 0}">Select All</input></label>
												</div>
											</td>

											<td></td>

											<td style="width: 50%">
												<label style="margin-left: 5px;">Selected:</label>
												<div data-bind="visible: selAgentType() == 'componentagent' && currentViewIndex() != uiConstants.common.READ_VIEW">
													<label style="font-weight: normal;"><input type="checkbox" id="selAllSelCompInst" style="margin-left: 5px;" data-bind="attr: {disabled: selectedCompInstArr().length == 0}">Select All</input></label>
												</div>
											</td>

											<td></td>
										</tr>

										<tr>
											<td>
												<div class="inner-div-container" id="availableCompInstList"></div>
											</td>

											<td style="padding: 5px;">
												<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddCompInstBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddCompInstBtn()}, event:{click: addToSelectedCompInst}"></button>
												<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveCompInstBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveCompInstBtn()}, event:{click: addToAvailableCompInst}"></button>
											</td>

											<td>
												<div class="inner-div-container" id="selectedCompInstList"></div>
											</td>
										</tr>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->
		
				<div data-bind="{visible:(selAgentType() == 'componentagent' && mode!='wizard')}" class="form-group" style="margin-bottom: 0px;">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading"><a id="imgCompAgentShowHide" class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="event: {click: expandCollapsePanel.bind($data, compAgentSettingsPanel, imgCompAgentShowHide)}" title="Expand"><span class="panel-label-expand-collapse">Advanced Settings</span></a></div>
						<div id="compAgentSettingsPanel" class="panel-body inner-div-container" style="height: auto; padding-bottom: 0px; display: none;">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Collection Timeout Multiplier <span class="mandatoryField">*</span></label>
								<div class="col-sm-4" style="display: inline-flex;">
									<select id="compAgentCollTimeout" class="chosen form-control" data-bind="options: compAgentCollTimeoutArr, value: compAgentCollTimeoutVal" style="width: auto !important;"></select>
									<span style="margin: auto 8px;"><b>X </b>KPI Collection Interval</span>
								</div>
							</div>

							<!-- <div class="form-group form-required">
								<label class="control-label col-sm-2">Keep Alive</label>
								<div class="col-sm-4">
									<input type="checkbox" id="keepAlive" data-bind="checked: keepAliveFlag">
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Data Compression</label>
								<div class="col-sm-4">
									<input type="checkbox" id="dataCompression" data-bind="checked: dataCompressionFlag">
								</div>
							</div> -->

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Data Communication Protocol <span class="mandatoryField">*</span></label>
								<div class="col-sm-4">
									<select id="comProtocolList" class="chosen form-control" data-bind="event:{change: onComProtocolChange}, options: communicationProtocolArr, optionsText: 'protocolName', optionsValue: 'protocolId', optionsCaption: uiConstants.agentConfig.SELECT_COM_PROTOCOL"></select>
								</div>
							</div>

							<!-- ko if: selComProtocol() == 'grpc' -->
								<!-- div class="form-group form-required">
									<label class="control-label col-sm-2">Data Communication Host <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="txtGrpcHost" data-bind="value: grpcHost" placeholder="Enter Host" max="45" required>
									</div>
								</div>

								<div class="form-group form-required">
									<label class="control-label col-sm-2">Data Communication Port <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<input type="number" class="form-control" id="txtGrpcPort" data-bind="value: grpcPort" placeholder="Enter Port" min="0" max="65535" required>
									</div>
								</div> -->

								<div class="form-group">
									<label class="control-label col-sm-2">GRPC Setting <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<select id="grpcSetting" class="chosen form-control" data-bind="foreach : grpcSettingArr" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.agentConfig.SELECT_GRPC_SETTING"></option>
											<!-- /ko-->

											<option data-bind="value: $data.id, text: $data.name"></option>
										</select>
									</div>
								</div>
							<!-- /ko-->

							<!-- ko if: selComProtocol() == 'http' -->
								<div class="form-group form-required">
									<label class="control-label col-sm-2">Data Communication URL <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<input type="text" class="form-control" id="txtHttpUrl" data-bind="value: httpUrl" placeholder="Enter URL" max="45" required>
									</div>
								</div>
							<!-- /ko-->

							<div>
								<fieldset class="config-border">
								    <legend class="config-border">Operation Mode:</legend>
								    	<div>
										    <label class="control-label control-option-label">Configuration</label>
											    <!-- ko foreach: operationModeArr() -->
												    <label class="config-option-label" style="margin-left: 20px;">
														<input type="radio" name="compAgentOpModeConfig" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'remote' ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
													</label>
												<!-- /ko-->
											</div>

											<div style="margin-top: 10px; display: none">
											    <label class="control-label control-option-label">Data</label>

											    <!-- ko foreach: operationModeArr() -->
												    <label class="config-option-label" style="margin-left: 20px;">
														<input type="radio" name="compAgentOpModeData" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'remote' ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
													</label>
												<!-- /ko-->
											</div>
								</fieldset>
							</div>

						</div>
					</div>
				</div>
			

			<div class="form-group" data-bind="visible: mode!== 'wizard'">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>