define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./agent-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','tablesorter','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,tablesorter,floatThead) {

	function AgentAddEdit(params) {
		var self = this;

		this.hostsArr = ko.observableArray();//params.hostsArr;
		//this.hostsListViewArr = params.hostsArr;
		this.agentTypesArr = params.agentTypesArr;
		this.agentModesArr = params.agentModesArr;
		this.communicationProtocolArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.selectedConfigRows = params.selectedConfigRows;
    	this.errorMsg=ko.observable('');
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(true);
		var configTagLoaded = 0;
		this.pageSelected = params.pageSelected;
  		this.displayComponent = ko.observable(false);
		self.availableCompInstArr = ko.observableArray();
		self.selectedCompInstArr = ko.observableArray();
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var isErrExists = 0;
  		this.enableAddCompInstBtn = ko.observable(false);
  		this.enableRemoveCompInstBtn = ko.observable(false);
  		this.selCompInstName = ko.observable();
  		/*this.keepAliveFlag = ko.observable(true);
  		this.dataCompressionFlag = ko.observable(true);*/
  		this.compAgentCollTimeoutArr = ko.observableArray([1,2,3,4,5,6,7,8,9,10]);
  		this.psaDataComProtocolArr = ko.observableArray();
  		this.psaHttpProxyProtocolArr = ko.observableArray();
  		this.psaFilterProtocolArr = ko.observableArray();
  		this.selAgentType = ko.observable();
  		this.selComProtocol = ko.observable();
  		/*this.grpcHost = ko.observable();
  		this.grpcPort = ko.observable();
  		this.psaDataComHost = ko.observable();
  		this.psaDataComPort = ko.observable();*/
  		this.httpUrl = ko.observable();
		this.componentNamesArr = ko.observableArray([{}]);
  		this.compInstSearchByArr = ko.observableArray(["Component Name", "Tag", "IP Address"]);
  		this.searchCriteriaFlag = ko.observable(0);
  		this.tagNameSearch = ko.observable();
  		this.ipAddressSearch = ko.observable();
  		this.compAgentCollTimeoutVal = ko.observable(2);
  		this.uniqueId = ko.observable();
  		this.selectedConfigNames = ko.observable("");
  		var compInstancesCopyArr = [];
  		var previousAgentTypeId;
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.serverDetailsArr = ko.observableArray();
  		this.psaHttpProxyHost = ko.observable();
  		this.psaHttpProxyPort = ko.observable();
  		//this.psaVirtualHostsArr= ko.observableArray();
  		this.psaFilterApplications = ko.observableArray();
  		this.psaFilterAllApplications = ko.observableArray();
  		this.psaFiltersArr = ko.observableArray();
  		this.operationModeArr = ko.observableArray();
  		this.responseArr = ko.observableArray();
  		//this.deletedServerDetails = ko.observableArray();
  		this.serverDetailsForAgent = ko.observableArray();
  		//this.loadVirtualHosts = ko.observable(true);
  		//var serverDetVirtualHostsLoaded = 0;
  		var serverDetApplicationsLoaded = 0;
  		var serverDetResponseDetailsLoaded = 0;
  		this.mode = params && params.mode || '';
  		var unMappedServerDetailIdsArr = [];
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		var serverDetIndex = 0;
		this.description = ko.observable();
		this.addUpdateFlag = params.addUpdateFlag || ko.observable();
		this.grpcSettingArr = ko.observableArray();
		this.defaultGrpcId = ko.observable();

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#compInstSearchByList").trigger('chosen:updated');
			$("#compAgentCollTimeout").trigger('chosen:updated');
			$("#compAgentCollTimeout_chosen").addClass("chosen-dynamic-width");

			$("#agentTypeList").trigger('chosen:updated');
			$("#agentModeList").trigger('chosen:updated');

			$('#agentsTab').on('click', function(){
		 		window.agentDetailsClicked(true);
		 	});

		 	$('#agentsTab').on('change', function(){
		 		if(window.agentDetailsClicked()){
		 			window.agentDetailsChaged(true);
		 		}
		 	});

		 	$('#txtName').on('change', function(){
		 		window.agentDetailsChaged(true);
		 	});
			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$("#agentModeList option").filter(function () { return slugify($(this).html()) == 'monitor'; }).prop('selected', true).trigger('chosen:updated');

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["agentName"]));
			}

			/*$(".tokenfield .token-input").on("keydown", function(){
				$(".tokenfield .token-input").css("width", "100px !important");

				console.log($(".tokenfield .token-input").css("width"));
			});*/

			$('.panel-body #agent-tokenfield-typeahead')
				.on('tokenfield:createtoken', function (e) {
					console.log($(".tokenfield .token-input").css("width"));

				})
				.on('tokenfield:createdtoken', function (e) {

					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}

					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#agent-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#agent-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

	       /* $("#selectedCompInstList").checklistbox({
	            data: self.selectedCompInstArr()
	        });*/

			$(".panel-body #agentTypeList").on('chosen:showing_dropdown', function () {
				//alert("DSffsdsd");
			        previousAgentTypeId = this.value;
			    }).on('change', function () {
			    	if(previousAgentTypeId == 0){
				    	self.onAgentTypeChange();
				    }

				    else{
			    		showMessageBox(uiConstants.agentConfig.CONFIRM_AGENT_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClearCompInst){
							if(confirmClearCompInst){
								var selAgent = slugify($("#agentTypeList option:selected").text());
								
								if($("#agentTypeList").val() != ""){
									serverDetIndex = 0;
								}
								
								if(selAgent == "psagent"){
									self.serverDetailsArr([]);
									self.psaFiltersArr([]);
									self.addPsaInterface();
									if(self.psaDataComProtocolArr().length && self.operationModeArr().length){
							        	setPsaAdvancedSettingsDefaults();
							        }
								}
								else{
									$("#compInstSearchByList").val("").trigger('chosen:updated');
									self.onCompInstSearchByChange();
					    			self.availableCompInstArr([]);
									self.selectedCompInstArr([]);
									$('#selectedCompInstList').checklistbox({
							            data: self.selectedCompInstArr()
							        });

							        $('#availableCompInstList').checklistbox({
							            data: self.availableCompInstArr()
							        });
							        if(self.communicationProtocolArr().length && self.operationModeArr().length){
							        	setCompAgentAdvancedSettingsDefaults();
							        }
								}

								self.onAgentTypeChange();
				    		}
				    		else{
				    			$("#agentTypeList").val(previousAgentTypeId).trigger('chosen:updated');
				    		}
				    	});
			    	}
				});

			$("#hostsList").chosen({}).on('chosen:showing_dropdown', function(){
				if($("#hostsList_chosen span").text() != uiConstants.agentConfig.ENTER_SELECT_HOST){
					$("#hostsList_chosen .chosen-search").find("input").val($("#hostsList_chosen span").text());
				}
			});

			$("#hostsList_chosen .chosen-search").find("input").on("keyup", function (evt) {		
				if($(this).val() != undefined && $(this).val() != ""){
		        	$("#hostsList_chosen span").text($(this).val());
				}

				if(evt.which == 13){
					$("#hostsList_chosen").parent().trigger("click");
				}
		    });

		    $("#hostsList").chosen({}).on('chosen:hiding_dropdown', function(){
				var selHostTxt = "";

        		if(!$('#compNameList_chosen .chosen-results').find("li.active-result.result-selected")){
        			selHostTxt = $('#hostsList_chosen .chosen-search input[type="text"]').val();
        		}
           		
	           	if(selHostTxt!=""){
	           		$('#hostsList').val("0").trigger('chosen:updated');
           			$("#hostsList_chosen span").text(selHostTxt);

	           		$('#hostsList > option').each(function(){
	 					if($(this).text()==selHostTxt) {
	 						$(this).parent('select').val($(this).val());
	 						$('#hostsList').val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});

			//TODO: comment the below two lines once the tag API is ready
			//configTagLoaded = 1;
			//onMastersLoad();
		    //TODO: uncomment the below line once the tag API is ready
			requestCall(uiConstants.common.SERVER_IP + "/tag?type=Agents", "GET", "", "getAgentTag", successCallback, errorCallback);
			
		}

		$("div").on("click", "#selAllAvailCompInst", function(e){
			$("#availableCompInstList .checkList").prop("checked", $("#selAllAvailCompInst").prop("checked"));
			self.enableAddCompInstBtn($("#availableCompInstList .checkList:checked").length);
		});

		$("div").on("change", "#availableCompInstList .checkList", function(e){
			if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
				$("#selAllAvailCompInst").prop("checked", self.availableCompInstArr().length == $("#availableCompInstList .checkList:checked").length);
				self.enableAddCompInstBtn($("#availableCompInstList .checkList:checked").length);
			}
        });

		$("div").on("click", "#selAllSelCompInst", function(e){
			$("#selectedCompInstList .checkList").prop("checked", $("#selAllSelCompInst").prop("checked"));
			self.enableRemoveCompInstBtn($("#selectedCompInstList .checkList:checked").length);
		});

		$("div").on("click", "#selectedCompInstList .checkList", function(e){
			if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
				$("#selAllSelCompInst").prop("checked", self.selectedCompInstArr().length == $("#selectedCompInstList .checkList:checked").length);
				self.enableRemoveCompInstBtn($("#selectedCompInstList .checkList:checked").length);
			}
        });

        self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		this.addPsaInterface = function(){
			serverDetIndex++;
			//serverDetIndex = $.grep(self.serverDetailsArr(), function(evt){ return evt.hidden == false; }).length + 1;
			self.serverDetailsArr.push({"serverDetIndex": ko.observable(serverDetIndex)});

			/*if(self.psaVirtualHostsArr().length == 0){
				requestCall("http://www.mocky.io/v2/57ea0519130000bd0a63dc16?callback=?", "GET", "", "getPsaVirtualHosts", successCallback, errorCallback);
			}*/

			if(self.psaFilterApplications().length == 0){
				requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getPsaFilterApplications", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/57ea058c130000c90a63dc17?callback=?", "GET", "", "getPsaFilterApplications", successCallback, errorCallback);
			}

			if(self.responseArr().length == 0){
				requestCall(uiConstants.common.SERVER_IP + "/responseOptions", "GET", "", "getResponseDetails", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/57f4d94b250000280f134856?calgetResponseDetailslback=?", "GET", "", "getResponseDetails", successCallback, errorCallback);
			}

			if(!self.psaFiltersArr()[self.serverDetailsArr().length-1]){
				self.psaFiltersArr.splice(self.serverDetailsArr().length-1,0, []);				
			}


			self.addPsaFilter(self.serverDetailsArr().length-1, 0);
		}

		this.deletePsaInterface = function(rowIndex){
			//self.serverDetailsArr.splice(rowIndex, 1);
			showMessageBox(uiConstants.agentConfig.CONFIRM_DELETE_INTERFACE, "question", "confirm", function confirmCallback(confirmDeleteInterface){
				if(confirmDeleteInterface){
					$("#divServerDetails"+rowIndex).css("display", "none");
					
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						for(var filter in self.psaFiltersArr()[rowIndex]){
							if($("#filterId"+rowIndex+filter).html() != "0"){
								unMappedServerDetailIdsArr.push(parseInt($("#filterId"+rowIndex+filter).html()));
							}
						}
					}

					self.psaFiltersArr.splice(rowIndex,1, []);

					//self.serverDetailsArr.splice(rowIndex, 1, {"hidden":true, "serverDetIndex": ko.observable(0)});
					serverDetIndex = 0;
					for(var serverDet in self.serverDetailsArr()){
						if(slugify($("#divServerDetails"+serverDet).css("display")) != 'none'){
							serverDetIndex++;
							self.serverDetailsArr()[serverDet]["serverDetIndex"](serverDetIndex);
						}
					}
				}
			});
			//self.deletedServerDetails.push(rowIndex);
		}

		this.handlePsaAppsSelAll = function(parentIndx, indx){
			$("#psaApplications"+parentIndx+indx+" .checkList").prop("checked", $("#selAllPsaApps"+parentIndx+indx).prop("checked"));
			return true;
		}

		this.handlePsaAppSel = function(parentIndx, indx){
			//$("div").on("click", "#psaApplications .checkList", function(e){
				$("#selAllPsaApps"+parentIndx+indx).prop("checked", self.psaFilterApplications().length == $("#psaApplications"+parentIndx+indx+" .checkList:checked").length);
			//});
		}

		this.addPsaFilter = function(rowIndex, insertIndex){

			/*if(!self.psaFiltersArr()[rowIndex]){
				self.psaFiltersArr.splice(rowIndex,0, []);				
			}*/

			/*$(".wrapper-scroll-table").scroll(function(){
				var translate = "translate(0,"+(this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
            });*/

			if(insertIndex==0){
				var $tab = $('#divAgentConfig table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			}

			console.log(self.psaFiltersArr());


			var psaFiltersArrItem = self.psaFiltersArr()[rowIndex];
			psaFiltersArrItem.splice(insertIndex, 0, {});
			self.psaFiltersArr.splice(rowIndex,1, psaFiltersArrItem);

			
				//jQuery(".chosen").chosen({search_contains: true});
				//$("#psaVirtualHost"+ rowIndex + insertIndex).trigger('chosen:updated');

				//prepareVirtualHostHanders(rowIndex, insertIndex);
			//}

			//else{
				$("#psaApplications" + rowIndex + insertIndex).checklistbox({
			        data: self.psaFilterApplications()
			    });
			//}

			$("#psaProtocol" + rowIndex + insertIndex).trigger('chosen:updated');
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#filterId"+rowIndex+insertIndex).html("0");
			self.handleVirtualHostCol(rowIndex);
			self.handlePsaSelCol(rowIndex);
			$("div").on("click", "#psaApplications" + rowIndex+insertIndex + " .checkList", function(e){
				self.handlePsaAppSel(rowIndex, insertIndex);
	    	});
		}

		this.setFilterValues = function(rowIndex, insertIndex, filtersObj){
			var appIdsArr;
			//var updatedAppIdsArr;
			var appIdsObjArr;
			$("#filterId"+rowIndex+insertIndex).html(filtersObj.id);
			if(filtersObj.uPort){
				$("input:radio[name='radioPortRange"+rowIndex+insertIndex+"'][value='portRange']").attr("checked",true);
				self.handlePortRange(rowIndex, insertIndex);
				$("#psaPortTo"+rowIndex+insertIndex).val(filtersObj.uPort);
			}
			$("#psaPortFrom"+rowIndex+insertIndex).val(filtersObj.lPort);

			/*if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
				$("#psaVirtualHost"+rowIndex+insertIndex).val($.grep(self.psaVirtualHostsArr(), function(e){ return e.virtualHostName == filtersObj.virtualHost; })[0].virtualHostId).trigger("chosen:updated");
			}
			else if(filtersObj.virtualHost != uiConstants.common.ENTER_SELECT){
				$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen span")[0].innerHTML = filtersObj.virtualHost;
			}*/

			$("#psaVirtualHost"+rowIndex+insertIndex).val(filtersObj.virtualHost);

			appIdsArr = $("#psaApplications"+rowIndex+insertIndex).getAllValues();
			appIdsObjArr = $("#psaApplications"+rowIndex+insertIndex).getIdsNames();
				

			if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
				for(var app in filtersObj.applicationIds){
					if(appIdsArr.indexOf(filtersObj.applicationIds[app]) == -1){
						appIdsObjArr.push({
							"id": filtersObj.applicationIds[app],
							"name": $.grep(self.psaFilterAllApplications(), function(e){ return e.applicationId == filtersObj.applicationIds[app]; })[0].applicationName
						})
					}
				}
				debugger;	
				sortArrayObjByValue(appIdsObjArr, "name");
			}

			$("#psaApplications"+rowIndex+insertIndex).checklistbox({
		        data: appIdsObjArr
		    });

			for(var app in filtersObj.applicationIds){
				
				//updatedAppIdsArr = [];

				/*for(app in appIdsObjArr){
					updatedAppIdsArr.push({
						"id": appIdsObjArr[app].value,
						"name": appIdsObjArr[app].label
					});
				}*/

				

				

	     		$("#psaApplications"+rowIndex+insertIndex+" .checkList[value=" + filtersObj.applicationIds[app] + "]").prop("checked",true);
	    	}
	    	/*$("div").on("click", "#psaApplications" + rowIndex+insertIndex + " .checkList", function(e){
				self.handlePsaAppSel(rowIndex, insertIndex);
	    	});*/
			self.handlePsaAppSel(rowIndex, insertIndex);


			$("#psaProtocol"+rowIndex+insertIndex).val(filtersObj.protocolId).trigger('chosen:updated');
			self.handlePsaProtocol(rowIndex, insertIndex);

			$("#psaKeyFilePath"+rowIndex+insertIndex).val(filtersObj.keyFilePath);
			$("#psaKeyFilePwd"+rowIndex+insertIndex).val(filtersObj.keyFilePassword);

			for(var responseIndex in self.responseArr()){
				if(slugify($("#psaResponseChk"+rowIndex+insertIndex+responseIndex).val()).indexOf("header") != -1){
					$("#psaResponseChk"+rowIndex+insertIndex+responseIndex).prop("checked",filtersObj.responseHeader);
				}
				else if(slugify($("#psaResponseChk"+rowIndex+insertIndex+responseIndex).val()).indexOf("body") != -1){
					$("#psaResponseChk"+rowIndex+insertIndex+responseIndex).prop("checked",filtersObj.responseBody);
				}
				self.handlePsaBodyParam(rowIndex, insertIndex, responseIndex, $("#psaResponseChk"+rowIndex+insertIndex+responseIndex).val());
				//break;
			}


			$("#psaBodyOffset"+rowIndex+insertIndex).val(filtersObj.offset);
			$("#psaBodySize"+rowIndex+insertIndex).val(filtersObj.size);
		}

		this.clonePsaFilter = function(rowIndex){
			var filterCloneIndex = parseInt($(".psaCheckboxCol"+rowIndex).parent().find('input:checked')[0].value);
			var portType = "";
			self.addPsaFilter(rowIndex, filterCloneIndex+1);
											//if(!self.deletedServerDetails()[serverDet] || self.deletedServerDetails()[serverDet].indexOf(filterDet) == -1){
			/*var filtersObj = {
				"virtualHost": $("#virtualIp"+rowIndex).prop('checked') ? $("#psaVirtualHost"+rowIndex+filterCloneIndex).val() : "",
				"applicationIds": $("#psaApplications"+rowIndex+filterCloneIndex).getSelectedValues(),
				"lPort": $("#psaPortFrom"+rowIndex+filterCloneIndex).val(),
				"uPort": $("#psaPortTo"+rowIndex+filterCloneIndex).val() !="" ? $("#psaPortTo"+rowIndex+filterCloneIndex).val() : "",
				"protocolId": $("#psaProtocol"+rowIndex+filterCloneIndex).val(),
				"keyFilePath": slugify($("#psaProtocol"+rowIndex+filterCloneIndex+" option:selected").text()) == "https" ? $("#psaKeyFilePath"+rowIndex+filterCloneIndex).val() : "",
				"keyFilePassword": slugify($("#psaProtocol"+rowIndex+filterCloneIndex+" option:selected").text()) == "https" ? $("#psaKeyFilePwd"+rowIndex+filterCloneIndex).val() : "",
				"responseHeader" : $("#psaResponseChk"+rowIndex+filterCloneIndex+"0").prop('checked') ? 1 : 0,
				"responseBody" : $("#psaResponseChk"+rowIndex+filterCloneIndex+"1").prop('checked') ? 1 : 0,
				"offset": $("#psaResponseChk"+rowIndex+filterCloneIndex+"0").prop('checked') ? $("#psaBodyOffset"+rowIndex+filterCloneIndex).val() : "",
				"size": $("#psaResponseChk"+rowIndex+filterCloneIndex+"1").prop('checked') ? $("#psaBodySize"+rowIndex+filterCloneIndex).val() : ""
			};*/

			var responseHeaderSelected = 0;
			var responseBodySelected = 0;

			for(var responseIndex in self.responseArr()){
				if($("#psaResponseChk"+rowIndex+filterCloneIndex+responseIndex).prop("checked")){
					if(slugify($("#psaResponseChk"+rowIndex+filterCloneIndex+responseIndex).val()).indexOf("header") != -1){
						responseHeaderSelected = 1;
					}
					else if(slugify($("#psaResponseChk"+rowIndex+filterCloneIndex+responseIndex).val()).indexOf("body") != -1){
						responseBodySelected = 1;
					}
					//break;
				}
			}

			portType = $('input:radio[name=radioPortRange'+rowIndex+filterCloneIndex+']:checked').val();

			var filtersObj = {
				"id": 0,
				"virtualHost": $("#virtualIp"+rowIndex).prop('checked') ? $("#psaVirtualHost"+rowIndex+filterCloneIndex).val() : "",
				"applicationIds": $("#psaApplications"+rowIndex+filterCloneIndex).getSelectedValues().map(function (x){return parseInt(x);}),
				"lPort": parseInt($("#psaPortFrom"+rowIndex+filterCloneIndex).val()),
				"uPort": portType == "portRange" ? parseInt($("#psaPortTo"+rowIndex+filterCloneIndex).val()) : null,
				"protocolId": parseInt($("#psaProtocol"+rowIndex+filterCloneIndex).val()),
				"keyFilePath": slugify($("#psaProtocol"+rowIndex+filterCloneIndex+" option:selected").text()) == "https" ? $("#psaKeyFilePath"+rowIndex+filterCloneIndex).val() : "",
				"keyFilePassword": slugify($("#psaProtocol"+rowIndex+filterCloneIndex+" option:selected").text()) == "https" ? $("#psaKeyFilePwd"+rowIndex+filterCloneIndex).val() : "",
				"responseHeader" : responseHeaderSelected,
				"responseBody" : responseBodySelected,
				"offset": parseInt($("#psaBodyOffset"+rowIndex+filterCloneIndex).val()) || null,
				"size": parseInt($("#psaBodySize"+rowIndex+filterCloneIndex).val()) || null
			};
				
				

			self.setFilterValues(rowIndex, filterCloneIndex+1, filtersObj);

		}

		

		this.deletePsaFilter = function(rowIndex){
			showMessageBox(uiConstants.agentConfig.CONFIRM_DELETE_FILTER, "question", "confirm", function confirmCallback(confirmDeleteFilter){
				if(confirmDeleteFilter){
					$(".psaCheckboxCol"+rowIndex).parent().find('input:checked').each(function(i) {
		                console.log(self.psaFiltersArr()[rowIndex]);

		                //var selVirtualHostsArr = [];


		                var psaFiltersArrItem = self.psaFiltersArr()[rowIndex];

		                /*for(var filterDet in psaFiltersArrItem){
		                	if(filterDet == this.value){
		                		continue;
		                	}

		                	selVirtualHostsArr.push($("#psaVirtualHost"+rowIndex+filterDet+"_chosen span")[0].innerHTML);

		                }*/

		                psaFiltersArrItem.splice(this.value, 1);

						if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
							if($("#filterId"+rowIndex+this.value).html() != "0"){
								unMappedServerDetailIdsArr.push(parseInt($("#filterId"+rowIndex+this.value).html()));
							}
						}

						self.psaFiltersArr.splice(rowIndex,1, psaFiltersArrItem);

						//$("#psaVirtualHost02_chosen").remove();
						//self.loadVirtualHosts(false);
						//self.loadVirtualHosts(true);
						//jQuery(".chosen").chosen({
						//	search_contains: true	
						//});

		               /* for(var selVirtualHost in selVirtualHostsArr){
		                	$("#psaVirtualHost"+rowIndex+selVirtualHost+"_chosen span")[0].innerHTML = selVirtualHostsArr[selVirtualHost];
						}*/
		            });


				}
			});
		}

		/*function prepareVirtualHostHanders(rowIndex, insertIndex){
			//for(var serverDet in self.serverDetailsArr){
				//for(var filterDet in self.psaFiltersArr()[serverDet]){
					$("#psaVirtualHost"+rowIndex+insertIndex).chosen({}).on('chosen:showing_dropdown', function(){
						if($("#psaVirtualHost"+rowIndex+insertIndex+"_chosen span").text() != uiConstants.common.ENTER_SELECT){
							$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen .chosen-search").find("input").val($("#psaVirtualHost"+rowIndex+insertIndex+"_chosen span").text());
						}
					});

					$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen .chosen-search").find("input").on("keyup", function (evt) {		
						if($(this).val() != undefined && $(this).val() != ""){
				        	$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen span").text($(this).val());
						}

						if(evt.which == 13){
							$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen").parent().trigger("click");
						}
				    });

				    $("#psaVirtualHost"+rowIndex+insertIndex).chosen({}).on('chosen:hiding_dropdown', function(){
						var selVirtualHost = "";

		        		if(!$('#compNameList_chosen .chosen-results').find("li.active-result.result-selected")){
		        			selVirtualHost = $('#psaVirtualHost'+rowIndex+insertIndex+'_chosen .chosen-search input[type="text"]').val();
		        		}
		           		
			           	if(selVirtualHost!=""){
			           		$('#psaVirtualHost'+rowIndex+insertIndex).val("0").trigger('chosen:updated');
		           			$("#psaVirtualHost"+rowIndex+insertIndex+"_chosen span").text(selVirtualHost);

			           		$('#psaVirtualHost'+rowIndex+insertIndex+' > option').each(function(){
			 					if($(this).text()==selVirtualHost) {
			 						$(this).parent('select').val($(this).val());
			 						$('#psaVirtualHost'+rowIndex+insertIndex).val($(this).val()).trigger('chosen:updated');
			 					}
			 				});
			           	}
		        	});
				//}
			//}
			
		}*/

		this.handlePsaProtocol = function(parentContextIndex, index){
			$("#psaKeyFilePath"+parentContextIndex+index).prop("disabled", slugify($("#psaProtocol"+parentContextIndex+index+" option:selected").text()) != "https");
			$("#psaKeyFilePwd"+parentContextIndex+index).prop("disabled", slugify($("#psaProtocol"+parentContextIndex+index+" option:selected").text()) != "https");
			$("#psaResponseChk"+parentContextIndex+index+"0").prop("disabled", false);
			$("#psaResponseChk"+parentContextIndex+index+"1").prop("disabled", false);
			$("#spanResponse"+parentContextIndex+index+"1").text(self.responseArr()[1].name);
			$("#psaBodyOffset"+parentContextIndex+index).val("0");
			$("#psaBodySize"+parentContextIndex+index).val("100");
			$("#psaKeyFilePath"+parentContextIndex+index).val("");
			$("#psaKeyFilePwd"+parentContextIndex+index).val("");
			$("#psaResponseChk"+parentContextIndex+index+"0").prop("checked",false);
			$("#psaResponseChk"+parentContextIndex+index+"1").prop("checked",false);

			$("#psaResponseChk"+parentContextIndex+index+"0").parent().css("display","block");

			if(slugify($("#psaProtocol"+parentContextIndex+index+" option:selected").text()).startsWith("http")){
				$("#psaResponseChk"+parentContextIndex+index+"0").parent().css("display","block");

				if(slugify($("#psaProtocol"+parentContextIndex+index+" option:selected").text()) == "https"){
					$("#psaKeyFilePath"+parentContextIndex+index).focus();
				}
				$("#psaBodyOffset"+parentContextIndex+index).val("");
				$("#psaBodySize"+parentContextIndex+index).val("");
				$("#psaBodyOffset"+parentContextIndex+index).prop("disabled", true);
				$("#psaBodySize"+parentContextIndex+index).prop("disabled", true);
				$("#psaResponseChk"+parentContextIndex+index+"0").prop({"checked": false});
				$("#psaResponseChk"+parentContextIndex+index+"1").prop({"checked": false});
			}
			else if(slugify($("#psaProtocol"+parentContextIndex+index+" option:selected").text()).startsWith("tcp")){
				$("#psaResponseChk"+parentContextIndex+index+"0").parent().css("display","none");
				$("#psaResponseChk"+parentContextIndex+index+"0").prop("checked",false);
				$("#psaResponseChk"+parentContextIndex+index+"1").prop("checked",false);
				$("#psaBodyOffset"+parentContextIndex+index).val("");
				$("#psaBodySize"+parentContextIndex+index).val("");
				$("#spanResponse"+parentContextIndex+index+"1").text("Process Response");
				$("#psaBodyOffset"+parentContextIndex+index).prop("disabled", true);
				$("#psaBodySize"+parentContextIndex+index).prop("disabled", true);
			}
			else{
				$("#psaResponseChk"+parentContextIndex+index+"0").prop({"checked": false, "disabled": true});
				$("#psaResponseChk"+parentContextIndex+index+"1").prop({"checked": false, "disabled": true});
				//$("#psaBodyOffset"+parentContextIndex+index).val("");
				//$("#psaBodySize"+parentContextIndex+index).val("");
				$("#psaBodyOffset"+parentContextIndex+index).prop("disabled", true);
				$("#psaBodySize"+parentContextIndex+index).prop("disabled", true);
			}
		}

		this.handlePsaBodyParam = function(parentContextIndex, index, chkIndex, chkLabel){
			/*var isChecked = $("#psaResponseChk"+parentContextIndex+index+chkIndex).prop("checked");
			$(".psaResponseChkClass"+parentContextIndex+index).prop("checked",false);
			$("#psaResponseChk"+parentContextIndex+index+chkIndex).prop("checked",isChecked);*/

			if(slugify(chkLabel).indexOf("body") != -1){
				$("#psaBodyOffset"+parentContextIndex+index).prop("disabled", !$("#psaResponseChk"+parentContextIndex+index+chkIndex).prop('checked'));
				$("#psaBodySize"+parentContextIndex+index).prop("disabled", !$("#psaResponseChk"+parentContextIndex+index+chkIndex).prop('checked'));
				$("#psaBodyOffset"+parentContextIndex+index).val($("#psaResponseChk"+parentContextIndex+index+chkIndex).prop('checked') ? "0" : "");
				$("#psaBodySize"+parentContextIndex+index).val($("#psaResponseChk"+parentContextIndex+index+chkIndex).prop('checked') ? "100" : "");
			}
			/*else{
				$("#psaBodyOffset"+parentContextIndex+index).prop("disabled", true);
				$("#psaBodySize"+parentContextIndex+index).prop("disabled", true);
				$("#psaBodyOffset"+parentContextIndex+index).val("");
				$("#psaBodySize"+parentContextIndex+index).val("");
			}*/
				
			return true;
		}

		this.handleHttpProxyParams = function(){
			if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
				$("#divHttpProxy").find("input,select").prop("disabled", !$("#chkPsaHttpProxy").prop('checked'));
				$("#divHttpProxy #psaHttpProxyProtocol").prop("disabled", !$("#chkPsaHttpProxy").prop('checked')).trigger('chosen:updated');

				if(!$("#chkPsaHttpProxy").prop('checked')){
					$("#psaHttpProxyProtocol option").filter(function () { return $(this).html() == uiConstants.agentConfig.SELECT_PROTOCOL; }).prop('selected', true).trigger('chosen:updated');
					self.psaHttpProxyHost("");
					self.psaHttpProxyPort("");
				}
			}

			return true;
		}

		this.handleVirtualHostCol = function(index){
			if($("#virtualIp"+index).prop('checked')){
				$(".virtualHostCol"+index).css("display","table-cell");

			}
			else{
				$(".virtualHostCol"+index).css("display","none");
			}
			return true;
		}

		this.handleCloneDeleteState = function(index){
			if($(".psaCheckboxCol" + index + ":checked").length == 0){
				$("#btnInterfaceClone"+index).prop('disabled', true);
				$("#btnInterfaceDelete"+index).prop('disabled', true);
			}
			else{
				$("#btnInterfaceClone"+index).prop('disabled', true);
				$("#btnInterfaceDelete"+index).prop('disabled', false);

				if($(".psaCheckboxCol" + index + ":checked").length == 1){
					$("#btnInterfaceClone"+index).prop('disabled', false);
				}
			}
		}

		this.handlePsaSelAll = function(index){
			//alert($("#psaInterfaceSelAll"+index).prop('checked'));
			//$("#psaInterfaceSelAll"+index).prop('checked', true);
			$(".psaCheckboxCol"+index).prop("checked",$("#psaInterfaceSelAll"+index).prop('checked'));

			self.handleCloneDeleteState(index);

			return true;
		}

		this.handlePsaSelCol = function(index){
			var length = $(".psaCheckboxCol" + index + ":checked").length;

			if(length == self.psaFiltersArr()[index].length){
				$("#psaInterfaceSelAll"+index).prop("checked",true);
			}
			else{
				$("#psaInterfaceSelAll"+index).prop("checked",false);
			}

			self.handleCloneDeleteState(index);

			return true;
		}

        this.onAgentTypeChange = function(){
        	self.selAgentType(slugify($("#agentTypeList option:selected").text()));
        	var selTags = $('#agent-tokenfield-typeahead').val();

			$('#agent-tokenfield-typeahead').tokenfield('setTokens', selTags.split(","));
			self.grpcSettingArr([]);
			
        	if(self.selAgentType() == "componentagent"){

        		if(self.hostsArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/host?status=1", "GET", "", "getHosts", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/574d87091100005107a2611b?callback=?", "GET", "", "getHosts", successCallback, errorCallback);
				}

				if(self.communicationProtocolArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/communicationProtocol", "GET", "", "getCommunicationProtocol", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5752acaa0f0000af1cda8093?callback=?", "GET", "", "getCommunicationProtocol", successCallback, errorCallback);
				}

				if(self.operationModeArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/operationModes/CA", "GET", "", "getOperationMode", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/57f35cef0f00000f0a0604f7?callback=?", "GET", "", "getOperationMode", successCallback, errorCallback);
				}
				else{
					handleOperationModeState();
				}
			}
			else if(self.selAgentType() == "psagent"){


				if(self.hostsArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/host?status=1", "GET", "", "getHosts", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/574d87091100005107a2611b?callback=?", "GET", "", "getHosts", successCallback, errorCallback);
				}

				if(self.psaDataComProtocolArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/communicationProtocol", "GET", "", "getDataCommunicationProtocol", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/57ece3de0f00006110bca32c?callback=?", "GET", "", "getDataCommunicationProtocol", successCallback, errorCallback);
				}

				if(self.operationModeArr().length == 0){
					//requestCall("http://www.mocky.io/v2/57f35cef0f00000f0a0604f7?callback=?", "GET", "", "getOperationMode", successCallback, errorCallback);
					requestCall(uiConstants.common.SERVER_IP + "/operationModes/PSA", "GET", "", "getOperationMode", successCallback, errorCallback);
				}
				else{
					handleOperationModeState();
				}

				if(self.psaHttpProxyProtocolArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/proxyProtocols", "GET", "", "getHttpProxyProtocol", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/57ece3de0f00006110bca32c?callback=?", "GET", "", "getHttpProxyProtocol", successCallback, errorCallback);
				}

				if(self.psaFilterProtocolArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/ServerDetailProtocols", "GET", "", "getPsaFilterProtocol", successCallback, errorCallback);
				}
			}
        }

        function handleOperationModeState(){
        	/*if(self.selAgentType() == "componentagent"){
				$("input[name='compAgentOpModeConfig'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'online'; })[0].masterId +"']").parent().hide();
			}
			else if(self.selAgentType() == "psagent"){*/
				//$("input[name='opModeConfig'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'online'; })[0].masterId +"']").parent().hide();
				//$("input[name='opModeData'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'fallback'; })[0].masterId +"']").prop("disabled", true);
				//$("input[name='opModeData'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'offline'; })[0].masterId +"']").prop("disabled", true);
				$("input[name='opModeData'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'remote'; })[0].masterId +"']").prop('checked', true);
				$("input[name='compAgentOpModeData'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'remote'; })[0].masterId +"']").prop('checked', true);
			//}
        }

        this.onComProtocolChange = function(){
        	self.selComProtocol(slugify($("#comProtocolList option:selected").text()));
        	//self.availableCompInstArr([]);
			//self.selectedCompInstArr([]);


			if($("#comProtocolList").val() != undefined && $("#comProtocolList").val() != ""){
				if(self.grpcSettingArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/grpcSettingsListView?dataCommunicationType=grpc&status=2&markInactive=1", "GET", "", "getGrpcSetting", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5881adc0250000971bc9ee1c?callback=?", "GET", "", "getGrpcSetting", successCallback, errorCallback);

				}
				else{
					if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
						$("#grpcSetting").val(self.defaultGrpcId()).trigger("chosen:updated");
					}
					else{
						$("#grpcSetting").val(self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId).trigger("chosen:updated");
					}

					jQuery(".chosen").chosen({
						search_contains: true	
					});
				}
			}
        }

        this.onPsaComProtocolChange = function(){
        	if($("#psaDataComProtocol").val() != undefined && $("#psaDataComProtocol").val() != ""){
				if(self.grpcSettingArr().length == 0){
					requestCall(uiConstants.common.SERVER_IP + "/grpcSettingsListView?dataCommunicationType=grpc&status=2&markInactive=1", "GET", "", "getGrpcSetting", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5881adc0250000971bc9ee1c?callback=?", "GET", "", "getGrpcSetting", successCallback, errorCallback);

				}
				else{
					if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
						//$("#psaGrpcSetting").val(self.defaultGrpcId()).trigger("chosen:updated");
					}
					else{
						$("#psaGrpcSetting").val(self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId).trigger("chosen:updated");
					}

					jQuery(".chosen").chosen({
						search_contains: true	
					});
				}
			}

			
        	/*if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				$("#psaGrpcSetting").val(self.defaultGrpcId()).trigger("chosen:updated");
			}
			else{
				$("#psaGrpcSetting").val(self.selectedConfigRows()[0].psaAgentParam.grpcSettingId).trigger("chosen:updated");
			}*/
        }

        this.onCompInstSearchByChange = function(){
        	$("#compNamesList").val("0").trigger("chosen:updated");
        	self.tagNameSearch("");
        	self.ipAddressSearch("");
			if($("#compInstSearchByList option:selected").text() == self.compInstSearchByArr()[0]){
				self.searchCriteriaFlag(1);
				if(self.componentNamesArr().length == 1){
					requestCall(uiConstants.common.SERVER_IP + "/component?status=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/572b335d1300003b0ae2b7f9?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
				}
			}
			else if($("#compInstSearchByList option:selected").text() == self.compInstSearchByArr()[1]){
				self.searchCriteriaFlag(2);
			}
			else if($("#compInstSearchByList option:selected").text() == self.compInstSearchByArr()[2]){
				self.searchCriteriaFlag(3);
			}
			else{
				self.searchCriteriaFlag(0);
			}
        }

        this.searchComponentInstances = function(){
        	if(self.searchCriteriaFlag() == 1){
        		var componentId = $("#compNamesList").val(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?componentId=" + componentId+"&status=1", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/57308b551200000a0786387a?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        	else if(self.searchCriteriaFlag() == 2){
        		var tagName = self.tagNameSearch(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?tagName=" + tagName+"&status=1", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/575656150f0000090d2eff75?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        	else if(self.searchCriteriaFlag() == 3){
        		var ipAddress = self.ipAddressSearch(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?ipAddress=" + ipAddress+"&status=1", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/575656500f0000170d2eff76?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        }

        this.expandCollapsePanel = function(panelBody, expandCollpaseImg){
        	if(uiConstants.common.DEBUG_MODE)console.log(self.selectedCompInstArr());
        	$(panelBody).toggle();

        	if($(panelBody).is(":hidden")){
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-bottom").addClass("glyphicon-triangle-right");
        		$(expandCollpaseImg).attr("title", "Expand");
        	}
        	else{
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-right").addClass("glyphicon-triangle-bottom");
        		$(expandCollpaseImg).attr("title", "Collapse");
        	}
        }

        this.expandPanelOnError = function(panel, img){
        	if($(panel).is(":hidden")){
				self.expandCollapsePanel(panel, img);
			}
        }

        this.tableScrollTop = function(element){
        	if(isErrExists == 0){
				$(".wrapper-scroll-table").animate({
				    scrollTop: $(element).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
				});

				isErrExists = 1;
			}
        }

        //Adding/Updating single agent
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();
			self.configName(self.configName().trim());
			$("#divAgentConfig #txtDescription").val($("#divAgentConfig #txtDescription").val().trim());

			var portType = "";
			var tagsArr = [];

			if(self.configName() == undefined || self.configName() == ""){
				//self.errorMsg(uiConstants.agentConfig.AGENT_NAME_REQUIRED);
				showError("#divAgentConfig #txtName", uiConstants.agentConfig.AGENT_NAME_REQUIRED);
			    self.errorMsg("#divAgentConfig #txtName");
			}
			else if(self.configName().length < 2){
				//self.errorMsg(uiConstants.agentConfig.AGENT_NAME_MIN_LENGTH_ERROR);
				showError("#divAgentConfig #txtName", uiConstants.agentConfig.AGENT_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divAgentConfig #txtName");
			}
			else if(self.configName().length > 45){
				//self.errorMsg(uiConstants.agentConfig.AGENT_NAME_MAX_LENGTH_ERROR);
				showError("#divAgentConfig #txtName", uiConstants.agentConfig.AGENT_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divAgentConfig #txtName");
			}
			else if(!nameValidation(self.configName())){
				//self.errorMsg(uiConstants.agentConfig.AGENT_NAME_INVALID_ERROR);
				showError("#divAgentConfig #txtName", uiConstants.agentConfig.AGENT_NAME_INVALID_ERROR);
			    self.errorMsg("#divAgentConfig #txtName");
			}
			if($("#divAgentConfig #txtDescription").val() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divAgentConfig #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divAgentConfig #txtDescription");
			}
			else if($("#divAgentConfig #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divAgentConfig #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divAgentConfig #txtDescription");
			}
			else if($("#divAgentConfig #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divAgentConfig #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divAgentConfig #txtDescription");
			}
			else{

			}
			if($("#divAgentConfig #agentTypeList").val() == undefined || $("#agentTypeList").val() == ""){
				//self.errorMsg(uiConstants.agentConfig.SELECT_AGENT_TYPE_MSG);

			    showError("#divAgentConfig #agentTypeList_chosen", uiConstants.agentConfig.SELECT_AGENT_TYPE_MSG);
				showError("#divAgentConfig #agentTypeList_chosen span", uiConstants.agentConfig.SELECT_AGENT_TYPE_MSG);
			    self.errorMsg("#divAgentConfig #agentTypeList_chosen");
			}

			//else if(self.errorMsg() == ""){
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				removeError("#divAgentConfig .tokenfield");
				removeError("#divAgentConfig #agent-tokenfield-typeahead-tokenfield");
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divAgentConfig .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divAgentConfig #agent-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divAgentConfig .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divAgentConfig .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divAgentConfig #agent-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divAgentConfig .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divAgentConfig .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divAgentConfig #agent-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divAgentConfig .tokenfield");
						break;
					}
				}

			//}

			//if(self.errorMsg() == ""){
				if(self.selAgentType() == "componentagent"){
					/*if($("#hostsList_chosen span")[0].innerHTML == uiConstants.agentConfig.ENTER_SELECT_HOST){
						self.errorMsg(uiConstants.agentConfig.ENTER_SELECT_HOST_MSG);
					}
					else */
					if($("#agentModeList option:selected").text() == uiConstants.agentConfig.SELECT_AGENT_MODE){
						showError("#divAgentConfig #agentModeList_chosen", uiConstants.agentConfig.SELECT_AGENT_MODE_MSG);
						showError("#divAgentConfig #agentModeList_chosen span", uiConstants.agentConfig.SELECT_AGENT_MODE_MSG);
					    self.errorMsg("#divAgentConfig #agentModeList_chosen");
					}
					if(self.mode != 'wizard' && ($("#comProtocolList").val() == undefined || $("#comProtocolList").val() == "")){
						self.expandPanelOnError("#compAgentSettingsPanel", "#imgCompAgentShowHide");
						
						//self.errorMsg(uiConstants.agentConfig.SELECT_COM_PROTOCOL_MSG);
						showError("#divAgentConfig #comProtocolList_chosen", uiConstants.agentConfig.SELECT_COM_PROTOCOL_MSG);
						showError("#divAgentConfig #comProtocolList_chosen span", uiConstants.agentConfig.SELECT_COM_PROTOCOL_MSG);
					    self.errorMsg("#divAgentConfig #comProtocolList_chosen");
					}
					if(self.mode != 'wizard' && $("#grpcSetting").val() == "0"){
						//self.errorMsg(uiConstants.agentConfig.SELECT_GRPC_SETTING_MSG);
						self.expandPanelOnError("#compAgentSettingsPanel", "#imgCompAgentShowHide");

						showError("#divAgentConfig #grpcSetting_chosen", uiConstants.agentConfig.SELECT_GRPC_SETTING_MSG);
						showError("#divAgentConfig #grpcSetting_chosen span", uiConstants.agentConfig.SELECT_GRPC_SETTING_MSG);
					    self.errorMsg("#divAgentConfig #grpcSetting_chosen");
					}
					else{
						removeError("#divAgentConfig #grpcSetting_chosen");
						removeError("#divAgentConfig #grpcSetting_chosen span");
					}
					/*else if(self.mode != 'wizard' && self.selComProtocol() == "grpc" && (self.grpcHost() == undefined || self.grpcHost() == "")){
						self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_HOST_REQUIRED);
					}
					else if(self.mode != 'wizard' && self.selComProtocol() == "grpc" && (!hostIpValidation(self.grpcHost()))){
						self.errorMsg(uiConstants.agentConfig.INVALID_COM_PROTOCOL_HOST);
					}
					else if(self.mode != 'wizard' && self.selComProtocol() == "grpc" && (self.grpcPort() == undefined || self.grpcPort() == "")){
						self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_PORT_REQUIRED);
					}
					else if(self.mode != 'wizard' && self.selComProtocol() == "grpc" && (parseInt(self.grpcPort()) < 0 || parseInt(self.grpcPort()) > uiConstants.common.MAX_PORT_RANGE)){
						self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_PORT_INVALID);
					}*/
					if(self.mode != 'wizard' && self.selComProtocol() == "http" && (self.httpUrl() == undefined || self.httpUrl() == "")){
						//self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_URL_REQUIRED);
						self.expandPanelOnError("#compAgentSettingsPanel", "#imgCompAgentShowHide");
						
						showError("#divAgentConfig #txtHttpUrl", uiConstants.agentConfig.COM_PROTOCOL_URL_REQUIRED);
					    self.errorMsg("#divAgentConfig #txtHttpUrl");
					}
				}
				else if(self.selAgentType() == "psagent"){
					isErrExists = 0;
					/*if($("#hostsList_chosen span")[0].innerHTML == uiConstants.agentConfig.ENTER_SELECT_HOST){
						self.errorMsg(uiConstants.agentConfig.ENTER_SELECT_HOST_MSG);
					}
					else */
					/*if($("#agentModeList option:selected").text() == uiConstants.agentConfig.SELECT_AGENT_MODE){
						self.errorMsg(uiConstants.agentConfig.SELECT_AGENT_MODE_MSG);
					}
					else{*/
						var interfaceStr = "";
						var filterStr = "";
						for(var serverDet in self.serverDetailsArr()){
							interfaceStr = "Interface " + self.serverDetailsArr()[serverDet]["serverDetIndex"]();
							if(slugify($("#divServerDetails"+serverDet).css("display")) != 'none'){
								if($("#interface"+serverDet).val() == ""){
									self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");

									//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INTERFACE_REQUIRED);
									showError("#divAgentConfig #interface"+serverDet, uiConstants.agentConfig.INTERFACE_REQUIRED);
					   				self.errorMsg("#divAgentConfig #interface"+serverDet);
									//break;
								}
								if($("#physicalAddr"+serverDet).val() == ""){
									//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.PHYSICAL_ADDRESS_REQUIRED);
									self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
									
									showError("#divAgentConfig #physicalAddr"+serverDet, uiConstants.agentConfig.PHYSICAL_ADDRESS_REQUIRED);
					   				self.errorMsg("#divAgentConfig #physicalAddr"+serverDet);
									//break;
								}
								else if(!hostIpValidation($("#physicalAddr"+serverDet).val())){
									//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_PHYSICAL_ADDRESS);
									self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
									
									showError("#divAgentConfig #physicalAddr"+serverDet, uiConstants.agentConfig.INVALID_PHYSICAL_ADDRESS);
					   				self.errorMsg("#divAgentConfig #physicalAddr"+serverDet);
									//break;
								}

								//console.log(self.deletedServerDetails()[serverDet]);

								if(self.psaFiltersArr()[serverDet].length == 0){
									showMessageBox(interfaceStr+": "+uiConstants.agentConfig.SERVER_DETAILS_FILTER_REQUIRED, "error");
									//break;
								}

								for(var filterDet in self.psaFiltersArr()[serverDet]){
									filterStr = "filter #"+(parseInt(filterDet)+1);
									portType = $('input:radio[name=radioPortRange'+serverDet+filterDet+']:checked').val();
									//if(!self.deletedServerDetails()[serverDet] || self.deletedServerDetails()[serverDet].indexOf(filterDet) == -1){
										if($("#virtualIp"+serverDet).prop('checked') && $("#psaVirtualHost"+serverDet+filterDet).val() == ""){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.ENTER_VIRTUAL_HOST+" in "+filterStr);

											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaVirtualHost"+serverDet+filterDet);

											showError("#divAgentConfig #psaVirtualHost"+serverDet+filterDet, uiConstants.agentConfig.ENTER_VIRTUAL_HOST);
							   				//self.errorMsg("#divAgentConfig #virtualIp"+serverDet);


											//break;
										}
										else if($("#virtualIp"+serverDet).prop('checked') && (!hostIpValidation($("#psaVirtualHost"+serverDet+filterDet).val()))){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_VIRTUAL_HOST+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaVirtualHost"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaVirtualHost"+serverDet+filterDet, uiConstants.agentConfig.INVALID_VIRTUAL_HOST);
							   				//self.errorMsg("#divAgentConfig #virtualIp"+serverDet);

											//break;
										}
										if($("#psaApplications"+serverDet+filterDet).getSelectedItems().length == 0){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.APPLICATION_SELECTION_REQUIRED+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaApplications"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaApplications"+serverDet+filterDet, uiConstants.agentConfig.APPLICATION_SELECTION_REQUIRED);
							   				self.errorMsg("#divAgentConfig #psaApplications"+serverDet+filterDet);
											//break;
										}
										else{
											removeError("#divAgentConfig #psaApplications"+serverDet+filterDet);
										}
										if(portType == "port" && $("#psaPortFrom"+serverDet+filterDet).val()==""){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.PORT_REQUIRED+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.PORT_REQUIRED);
							   				//self.errorMsg("#divAgentConfig #psaPortFrom"+serverDet+filterDet);
							   				//break;
										}
										else if(portType == "port" && (parseInt($("#psaPortFrom"+serverDet+filterDet).val()) < 0 || parseInt($("#psaPortFrom"+serverDet+filterDet).val()) > uiConstants.common.MAX_PORT_RANGE)){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.PORT_INVALID+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.PORT_INVALID);
							   				//self.errorMsg("#divAgentConfig #psaPortFrom"+serverDet+filterDet);
											//break;
										}
										else if(portType == "port" && parseInt($("#psaPortFrom"+serverDet+filterDet).val())<0){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_PORT_VALUE+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.INVALID_PORT_VALUE);
							   				//self.errorMsg("#divAgentConfig #psaPortFrom"+serverDet+filterDet);
											//break;
										}
										if(portType == "portRange" && $("#psaPortFrom"+serverDet+filterDet).val()==""){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.FROM_PORT_RANGE_REQUIRED+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.FROM_PORT_RANGE_REQUIRED);
							   				//self.errorMsg("#divAgentConfig #psaPortFrom"+serverDet+filterDet);
							   				//break;
										}
										else if(portType == "portRange" && parseInt($("#psaPortFrom"+serverDet+filterDet).val())<0){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.FROM_PORT_RANGE_INVALID+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.FROM_PORT_RANGE_INVALID);
							   				//self.errorMsg("#divAgentConfig #psaPortFrom"+serverDet+filterDet);
							   				//break;
										}
										if(portType == "portRange" && $("#psaPortTo"+serverDet+filterDet).val()==""){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.TO_PORT_RANGE_REQUIRED+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortTo"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortTo"+serverDet+filterDet, uiConstants.agentConfig.TO_PORT_RANGE_REQUIRED);
							   				//self.errorMsg("#divAgentConfig #psaPortTo"+serverDet+filterDet);
											//break;
										}
										else if(portType == "portRange" && parseInt($("#psaPortTo"+serverDet+filterDet).val())<0){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.TO_PORT_RANGE_INVALID+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortTo"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortTo"+serverDet+filterDet, uiConstants.agentConfig.TO_PORT_RANGE_INVALID);
							   				//self.errorMsg("#divAgentConfig #psaPortTo"+serverDet+filterDet);
							   				//break;
										}
										else if(portType == "portRange"  && $("#psaPortFrom"+serverDet+filterDet).val()!="" && (parseInt($("#psaPortFrom"+serverDet+filterDet).val()) >= parseInt($("#psaPortTo"+serverDet+filterDet).val()))){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_PORT_RANGE+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortFrom"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortFrom"+serverDet+filterDet, uiConstants.agentConfig.INVALID_PORT_RANGE);
											showError("#divAgentConfig #psaPortTo"+serverDet+filterDet, uiConstants.agentConfig.INVALID_END_PORT_RANGE);
							   				//self.errorMsg("#divAgentConfig #psaPortTo"+serverDet+filterDet);
							   				//break;
										}
										else if(portType == "portRange" && parseInt($("#psaPortTo"+serverDet+filterDet).val())>uiConstants.common.MAX_PORT_RANGE){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.TO_PORT_INVALID+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaPortTo"+serverDet+filterDet);
											
											showError("#divAgentConfig #psaPortTo"+serverDet+filterDet, uiConstants.agentConfig.TO_PORT_INVALID);
							   				//self.errorMsg("#divAgentConfig #psaPortTo"+serverDet+filterDet);
							   				//break;
										}
										if($("#psaProtocol"+serverDet+filterDet + " option:selected").text() == uiConstants.common.SELECT){
											//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.PSA_PROTOCOL_REQUIRED+" in "+filterStr);
											self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
											self.tableScrollTop(".wrapper-scroll-table #psaProtocol_chosen"+serverDet+filterDet);
									
											showError("#divAgentConfig #psaProtocol"+serverDet+filterDet+"_chosen", uiConstants.agentConfig.PSA_PROTOCOL_REQUIRED);
											showError("#divAgentConfig #psaProtocol"+serverDet+filterDet+"_chosen span", uiConstants.agentConfig.PSA_PROTOCOL_REQUIRED);
							   				//self.errorMsg("#divAgentConfig #psaProtocol"+serverDet+filterDet);
											//break;
										}
										else if(slugify($("#psaProtocol"+serverDet+filterDet + " option:selected").text()) == "https"){
											if($("#psaKeyFilePath"+serverDet+filterDet).val()==""){
												//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.KEY_FILE_PATH_REQUIRED+" in "+filterStr);
												self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
												self.tableScrollTop(".wrapper-scroll-table #psaKeyFilePath"+serverDet+filterDet);
												
												showError("#divAgentConfig #psaKeyFilePath"+serverDet+filterDet, uiConstants.agentConfig.KEY_FILE_PATH_REQUIRED);
								   				//self.errorMsg("#divAgentConfig #psaKeyFilePath"+serverDet+filterDet);
								   				//break;
											}
											if($("#psaKeyFilePwd"+serverDet+filterDet).val()==""){
												//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.KEY_FILE_PASSWORD_REQUIRED+" in "+filterStr);
												self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
												self.tableScrollTop(".wrapper-scroll-table #psaKeyFilePwd"+serverDet+filterDet);
												
												showError("#divAgentConfig #psaKeyFilePwd"+serverDet+filterDet, uiConstants.agentConfig.KEY_FILE_PASSWORD_REQUIRED);
								   				//self.errorMsg("#divAgentConfig #psaKeyFilePwd"+serverDet+filterDet);
												//break;
											}
										}
										//if(self.errorMsg() == ""){
											var isResponseBodySelected = false;
											for(var responseIndex in self.responseArr()){
												if($("#psaResponseChk"+serverDet+filterDet+responseIndex).prop("checked")){
													if(slugify($("#psaResponseChk"+serverDet+filterDet+responseIndex).val()).indexOf("body") != -1){
														isResponseBodySelected = true;
														break;
													}
												}
											}

											if(isResponseBodySelected){
												if($("#psaBodyOffset"+serverDet+filterDet).val() == ""){
													//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.FILTER_OFFSET_VALUE_REQUIRED+" in "+filterStr);
													self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
													self.tableScrollTop(".wrapper-scroll-table #psaBodyOffset"+serverDet+filterDet);
													
													showError("#divAgentConfig #psaBodyOffset"+serverDet+filterDet, uiConstants.agentConfig.FILTER_OFFSET_VALUE_REQUIRED);
									   				//self.errorMsg("#divAgentConfig #psaBodyOffset"+serverDet+filterDet);
													
													//break;
												}
												else if(parseInt($("#psaBodyOffset"+serverDet+filterDet).val()) < 0){
													//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_FILTER_OFFSET_VALUE+" in "+filterStr);
													self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
													self.tableScrollTop(".wrapper-scroll-table #psaBodyOffset"+serverDet+filterDet);
													
													showError("#divAgentConfig #psaBodyOffset"+serverDet+filterDet, uiConstants.agentConfig.INVALID_FILTER_OFFSET_VALUE);
									   				//self.errorMsg("#divAgentConfig #psaBodyOffset"+serverDet+filterDet);
									   				//break;
												}
												if($("#psaBodySize"+serverDet+filterDet).val() == ""){
													//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.FILTER_SIZE_VALUE_REQUIRED+" in "+filterStr);
													self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
													self.tableScrollTop(".wrapper-scroll-table #psaBodySize"+serverDet+filterDet);
													
													showError("#divAgentConfig #psaBodySize"+serverDet+filterDet, uiConstants.agentConfig.FILTER_SIZE_VALUE_REQUIRED);
									   				//self.errorMsg("#divAgentConfig #psaBodySize"+serverDet+filterDet);
													//break;
												}
												else if(parseInt($("#psaBodySize"+serverDet+filterDet).val()) < 0){
													//self.errorMsg(interfaceStr+": "+uiConstants.agentConfig.INVALID_FILTER_SIZE_VALUE+" in "+filterStr);
													self.expandPanelOnError("#psaServerDetailsPanel", "#imgPsaServerDetailsShowHide");
													self.tableScrollTop(".wrapper-scroll-table #psaBodySize"+serverDet+filterDet);
													
													showError("#divAgentConfig #psaBodySize"+serverDet+filterDet, uiConstants.agentConfig.INVALID_FILTER_SIZE_VALUE);
									   				//self.errorMsg("#divAgentConfig #psaBodySize"+serverDet+filterDet);
													//break;
												}
											}
										//}

										/*if(self.errorMsg() != "" && $("#psaResponseChk"+serverDet+filterDet+"1").prop('checked')){
											if($("#psaBodyOffset"+serverDet+filterDet).val()==""){
												self.errorMsg(uiConstants.agentConfig.RESPONSE_BODY_OFFSET_REQUIRED);
												break;
											}
											else if($("#psaBodySize"+serverDet+filterDet).val()==""){
												self.errorMsg(uiConstants.agentConfig.RESPONSE_BODY_SIZE_REQUIRED);
												break;
											}
										}*/
									//}
								}

								/*if(self.errorMsg() != ""){
									break;
								}*/
							}
						}
					//}

					//if(self.errorMsg() == ""){
						if(self.mode != 'wizard' && ($("#psaDataComProtocol").val() == undefined || $("#psaDataComProtocol").val() == "")){
							//self.errorMsg(uiConstants.agentConfig.SELECT_COM_PROTOCOL_MSG);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #psaDataComProtocol_chosen", uiConstants.agentConfig.SELECT_COM_PROTOCOL_MSG);
							self.errorMsg("#divAgentConfig #psaDataComProtocol_chosen span");
						}
						if(self.mode != 'wizard' && $("#psaGrpcSetting").val() == "0"){
							//self.errorMsg(uiConstants.agentConfig.SELECT_GRPC_SETTING_MSG);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #psaGrpcSetting_chosen", uiConstants.agentConfig.SELECT_GRPC_SETTING_MSG);
							self.errorMsg("#divAgentConfig #psaGrpcSetting_chosen");
						}
						/*else if(self.mode != 'wizard' && (self.psaDataComHost() == undefined || self.psaDataComHost() == "")){
							self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_HOST_REQUIRED);
						}
						else if(self.mode != 'wizard' && (!hostIpValidation(self.psaDataComHost()))){
							self.errorMsg(uiConstants.agentConfig.INVALID_COM_PROTOCOL_HOST);
						}
						else if(self.mode != 'wizard' && (self.psaDataComPort() == undefined || self.psaDataComPort() == "")){
							self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_PORT_REQUIRED);
						}
						else if(self.mode != 'wizard' && (parseInt(self.psaDataComPort()) < 0 || parseInt(self.psaDataComPort()) > uiConstants.common.MAX_PORT_RANGE)){
							self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_PORT_INVALID);
						}*/
						if($("#chkPsaHttpProxy").prop('checked') && ($("#psaHttpProxyProtocol").val() == undefined || $("#psaHttpProxyProtocol").val() == "")){
							//self.errorMsg(uiConstants.agentConfig.SELECT_PROTOCOL_MSG);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #psaHttpProxyProtocol_chosen", uiConstants.agentConfig.SELECT_PROTOCOL_MSG);
							showError("#divAgentConfig #psaHttpProxyProtocol_chosen span", uiConstants.agentConfig.SELECT_PROTOCOL_MSG);
							self.errorMsg("#divAgentConfig #psaHttpProxyProtocol_chosen");
						}
						if($("#chkPsaHttpProxy").prop('checked') && (self.psaHttpProxyHost() == undefined || self.psaHttpProxyHost() == "")){
							//self.errorMsg(uiConstants.agentConfig.HTTP_PROXY_HOST_REQUIRED);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #txtPsaHttpProxyHost", uiConstants.agentConfig.HTTP_PROXY_HOST_REQUIRED);
							self.errorMsg("#divAgentConfig #txtPsaHttpProxyHost");
						}
						if($("#chkPsaHttpProxy").prop('checked') && (self.psaHttpProxyPort() == undefined || self.psaHttpProxyPort() == "")){
							//self.errorMsg(uiConstants.agentConfig.HTTP_PROXY_PORT_REQUIRED);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #txtPsaHttpProxyPort", uiConstants.agentConfig.HTTP_PROXY_PORT_REQUIRED);
							self.errorMsg("#divAgentConfig #txtPsaHttpProxyPort");
						}
						else if($("#chkPsaHttpProxy").prop('checked') && (parseInt(self.psaHttpProxyPort()) < 0 || parseInt(self.psaHttpProxyPort()) > uiConstants.common.MAX_PORT_RANGE)){
							//self.errorMsg(uiConstants.agentConfig.HTTP_PROXY_PORT_INVALID);
							self.expandPanelOnError("#psaSettingsPanel", "#imgPsAgentShowHide");

							showError("#divAgentConfig #txtPsaHttpProxyPort", uiConstants.agentConfig.HTTP_PROXY_PORT_INVALID);
							self.errorMsg("#divAgentConfig #txtPsaHttpProxyPort");
						}
					//}
				}
				else if(self.selAgentType() == "jia" || self.selAgentType() == "jimagent"){
					if(self.httpUrl() == ""){
						self.expandPanelOnError("#compAgentSettingsPanel", "#imgCompAgentShowHide");

						showError("#divAgentConfig #txtHttpUrl", uiConstants.agentConfig.COM_PROTOCOL_URL_REQUIRED);
						self.errorMsg("#divAgentConfig #txtHttpUrl");
						//self.errorMsg(uiConstants.agentConfig.COM_PROTOCOL_URL_REQUIRED);
					}
				}
			//}

			//if(self.errorMsg() == ""){
				if(containsDuplicate($("#agent-tokenfield-typeahead").val())){
					//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);

					showError("#divAgentConfig .tokenfield", uiConstants.common.DUPLICATE_TAGS);
					showError("#divAgentConfig #agent-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
				    self.errorMsg("#divAgentConfig .tokenfield");
				}
				else{
					var tagsObjArr = [];

					if(self.errorMsg() == ""){
						/*if(self.tags() && self.tags().trim().length == 1)
							tagsArr.push(self.tags());

						else if(self.tags() && self.tags().trim().length > 1)
							tagsArr = self.tags().split(",");
						
						for(var t in tagsArr){
							if(tagsArr[t].trim().length < 2){
								self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
								break;
							}
							else if(tagsArr[t].trim().length > 45){
								self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
								break;
							}
							else if(!tagValidation(tagsArr[t].trim())){
								self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
								break;
							}
						}*/

						//if(self.errorMsg() == ""){
							for(var tag in tagsArr){
								if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
									tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
								}
								else{
									tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
								}
							}

							for(tag in tagsToDeleteArr){
								tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
							}

							debugger;
							if(self.selAgentType() != "psagent"){
								var compInstIds = [];
								for(var selCompInst in self.selectedCompInstArr()){
									compInstIds.push(self.selectedCompInstArr()[selCompInst].id);
								}
							}

							var compObj;
							var selHostArr=[];
							var selHostStr="";

							if($("#hostsList_chosen span")[0].innerHTML != uiConstants.agentConfig.ENTER_SELECT_HOST){
								selHostArr = $.grep(self.hostsArr(), function(e){ return e == $("#hostsList_chosen span")[0].innerHTML; });
								if(selHostArr.length){
									selHostStr = selHostArr[0].split("(Inactive)")[0].trim().split("(").length > 1 ? selHostArr[0].split("(")[1].split(")")[0].trim() : selHostArr[0];
								}
								else{
									selHostStr = $("#hostsList_chosen span")[0].innerHTML.trim();
								}
							}

							if(self.selAgentType() == "componentagent"){
								compObj = {
									"index":1,
									"agentName": self.configName(),
									"description": self.description(),
									"host": selHostStr,
									"agentTypeId": parseInt($("#agentTypeList").val()),
									"agentType": $("#agentTypeList option:selected").text(),
									"agentMode":  $("#agentModeList option:selected").text(),
									"timeoutMultiplier": parseInt($("#compAgentCollTimeout option:selected").text()),
									/*"keepAlive": self.keepAliveFlag()?1:0,
									"dataCompression": self.dataCompressionFlag()?1:0,*/
									"communicationProtocol": $("#comProtocolList option:selected").text(),
									"grpcSettingId": $("#grpcSetting").val(),
									//"communicationProtocolHost": self.selComProtocol() == 'grpc' ? self.grpcHost() : null,
									//"communicationProtocolPort": self.selComProtocol() == 'grpc' ? self.grpcPort() : null,
									"communicationProtocolUrl": self.selComProtocol() == 'http' ? self.httpUrl() : null,
									"configOperationModeId": $("input[name='compAgentOpModeConfig']:checked").val(),
									"dataOperationModeId": $("input[name='compAgentOpModeData']:checked").val(),
									"componentInstanceIds": compInstIds,
									"tags": tagsObjArr,
									"status" : self.configStatus()?1:0
								};
							}
							else if(self.selAgentType() == "jia" || self.selAgentType() == "jimagent"){
								compObj = {
									"index":1,
									"agentName": self.configName(),
									"description": self.description(),
									"agentTypeId": parseInt($("#agentTypeList").val()),
									"agentType": $("#agentTypeList option:selected").text(),
									"agentMode":  $("#agentModeList option:selected").text(),
									"componentInstanceIds": compInstIds,
									"tags": tagsObjArr,
									"status" : self.configStatus()?1:0
								};
							}
							else if(self.selAgentType() == "psagent"){
								var serverDetArr = [];
								var filtersArr = [];
								var filterDetailsObj = {};
								var filterDetailsArr = [];
								for(var serverDet in self.serverDetailsArr()){
									if(slugify($("#divServerDetails"+serverDet).css("display")) != 'none'){
										filtersArr = [];
										appsArr = [];

										for(var filterDet in self.psaFiltersArr()[serverDet]){
											var responseHeaderSelected = 0;
											var responseBodySelected = 0;
											portType = $('input:radio[name=radioPortRange'+serverDet+filterDet+']:checked').val();
											for(var responseIndex in self.responseArr()){
												if($("#psaResponseChk"+serverDet+filterDet+responseIndex).prop("checked")){
													if(slugify($("#psaResponseChk"+serverDet+filterDet+responseIndex).val()).indexOf("header") != -1){
														responseHeaderSelected = 1;
													}
													else if(slugify($("#psaResponseChk"+serverDet+filterDet+responseIndex).val()).indexOf("body") != -1){
														responseBodySelected = 1;
													}
													//break;
												}
											}

											filterDetailsObj = {
												"id": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? parseInt($("#filterId"+serverDet+filterDet).html()) : 0,//self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW ? 0 : (self.serverDetailsForAgent()[serverDet] && self.serverDetailsForAgent()[serverDet].filters[filterDet] ? self.serverDetailsForAgent()[serverDet].filters[filterDet].id : 0),
												"virtualHost": $("#virtualIp"+serverDet).prop('checked') ? $("#psaVirtualHost"+serverDet+filterDet).val() : "",
												"applicationIds": $("#psaApplications"+serverDet+filterDet).getSelectedValues().map(function (x){return parseInt(x);}),
												"lPort": parseInt($("#psaPortFrom"+serverDet+filterDet).val()),
												"uPort": portType == "portRange" ? parseInt($("#psaPortTo"+serverDet+filterDet).val()) : null,
												"protocolId": parseInt($("#psaProtocol"+serverDet+filterDet).val()),
												"keyFilePath": slugify($("#psaProtocol"+serverDet+filterDet+" option:selected").text()) == "https" ? $("#psaKeyFilePath"+serverDet+filterDet).val() : "",
												"keyFilePassword": slugify($("#psaProtocol"+serverDet+filterDet+" option:selected").text()) == "https" ? $("#psaKeyFilePwd"+serverDet+filterDet).val() : "",
												"responseHeader": responseHeaderSelected,
												"responseBody": responseBodySelected,
												"offset": $("#psaBodyOffset"+serverDet+filterDet).val(),
												"size": $("#psaBodySize"+serverDet+filterDet).val()
											};
											filtersArr.push(JSON.parse(JSON.stringify(filterDetailsObj)));

											delete filterDetailsObj["id"];
											delete filterDetailsObj["keyFilePassword"];

											if(filterDetailsArr.indexOf(JSON.stringify(filterDetailsObj)) == -1){
												filterDetailsArr.push(JSON.stringify(filterDetailsObj));
											}
											else{
												self.errorMsg(uiConstants.agentConfig.ERROR_DUPLICATE_FILTER_DETAILS);
												break;
											}
										}

										if(self.errorMsg() != ""){
											break;
										}

										var serverDetObjArr = $.grep(serverDetArr, function(e){ 
											return e.interface_ == $("#interface"+serverDet).val() && e.physicalAddress == $("#physicalAddr"+serverDet).val() && e.virtualIp == ($("#virtualIp"+serverDet).prop('checked') ? 1 : 0); 
										});


										if(serverDetObjArr.length == 0){
											serverDetArr.push({
												"interface_": $("#interface"+serverDet).val(),
												"physicalAddress": $("#physicalAddr"+serverDet).val(),
												"virtualIp": $("#virtualIp"+serverDet).prop('checked') ? 1 : 0,
												"filters": filtersArr
											});
										}
										else{
											self.errorMsg(uiConstants.agentConfig.ERROR_DUPLICATE_INTERFACE_DETAILS);
											break;
										}
									}
									
								}

								var selHostArr=[];
								var selHostStr="";

								if($("#hostsList_chosen span")[0].innerHTML != uiConstants.agentConfig.ENTER_SELECT_HOST){
									selHostArr = $.grep(self.hostsArr(), function(e){ return e == $("#hostsList_chosen span")[0].innerHTML; });
									if(selHostArr.length){
										selHostStr = selHostArr[0].split("(Inactive)")[0].trim().split("(").length > 1 ? selHostArr[0].split("(")[1].split(")")[0].trim() : selHostArr[0];
									}
									else{
										selHostStr = $("#hostsList_chosen span")[0].innerHTML.trim();
									}
								}

								if(self.errorMsg() == ""){
									compObj = {
										"index":1,
										"agentName": self.configName(),
										"description": self.description(),
										"host": selHostStr,
										"agentTypeId": parseInt($("#agentTypeList").val()),
										"agentType": $("#agentTypeList option:selected").text(),
										"agentMode":  $("#agentModeList option:selected").text(),
										"communicationProtocolId": $("#psaDataComProtocol").val(),
										"grpcSettingId": $("#psaGrpcSetting").val(),
										//"communicationProtocolHost": self.psaDataComHost(),
										//"communicationPort": self.psaDataComPort(),
										"configOperationModeId": $("input[name='opModeConfig']:checked").val(),
										"dataOperationModeId": $("input[name='opModeData']:checked").val(),
										"httpProxy": $("#chkPsaHttpProxy").prop('checked') ? 1 : 0,
										"httpProxyProtocolId": $("#chkPsaHttpProxy").prop('checked') ? $("#psaHttpProxyProtocol").val() : 0,
										"httpProxyHost": $("#chkPsaHttpProxy").prop('checked') ? $("#txtPsaHttpProxyHost").val() : "",
										"httpProxyPort": $("#chkPsaHttpProxy").prop('checked') ? $("#txtPsaHttpProxyPort").val() : "",
										"responseTimeType": $("input[name='responseTimeType']:checked").val(),
										"serverDetails": serverDetArr,
										"unMappedServerDetailIds": unMappedServerDetailIdsArr,
										"tags": tagsObjArr,
										"status" : self.configStatus()?1:0
									};
								}
							}
						//}

						if(self.errorMsg() == ""){
							if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(compObj));
							var agentTypeString = "";
							if(self.selAgentType() == "componentagent"){
								agentTypeString = "component";
							}
							else if(self.selAgentType() == "jia" || self.selAgentType() == "jimagent"){
								agentTypeString = "JIA";
							}
							else if(self.selAgentType() == "psagent"){
								agentTypeString = "PSA";
							}

							if(self.configId() == 0)
								requestCall(uiConstants.common.SERVER_IP + "/agent/" + agentTypeString, "POST", JSON.stringify(compObj), "addSingleConfig", successCallback, errorCallback);
							else
								requestCall(uiConstants.common.SERVER_IP + "/agent/" + agentTypeString + "/" + self.configId(), "PUT", JSON.stringify(compObj), "editSingleConfig", successCallback, errorCallback);
						}
					}
				}
			//}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			$('#agentTypeList').prop('disabled', true).trigger('chosen:updated');
			debugger;
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedCompInstArr());
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			/*$("input,textarea,.chosen-container").addClass("readonly-element");
			$(".chosen-container a").css("padding-left", "5px");
			$(".tokenfield").css("display", "none");
			$("#tokenfield-readonly").css("display", "");
			$(".tokenfield").css("display", "none");
			$("#tokenfield-readonly").css("display", "");*/

			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$('#agent-tokenfield-typeahead').tokenfield('readonly');
			$('#agentTypeList').prop('disabled', true).trigger('chosen:updated');
			$('#hostsList').prop('disabled', true).trigger("chosen:updated");
			$('#agentModeList').prop('disabled', true).trigger('chosen:updated');
			$("#availableCompInstList").find("input,button,select").attr("disabled", "disabled");
			$("#selectedCompInstList").find("input,button,select").attr("disabled", "disabled");

			if(self.selAgentType() == "componentagent"){
				$("#compAgentCollTimeout").prop('disabled', true).trigger("chosen:updated");
				/*document.getElementById("keepAlive").disabled = true;
				document.getElementById("dataCompression").disabled = true;*/
				$('#comProtocolList').prop('disabled', true).trigger('chosen:updated');

			}
			else if(self.selAgentType() == "psagent"){
				$("#psaDataComProtocol").prop('disabled', true).trigger('chosen:updated');
				//$("#txtPsaDataComHost").prop('readonly', true);
				//$("#txtPsaDataComPort").prop('readonly', true);
				document.getElementById("chkPsaHttpProxy").disabled = true;
				$("#divHttpProxy").find("input,select").prop("disabled", true);
				$("#btnInterface").css("display","none");
				$("input[name='responseTimeType']").prop("disabled", true);
			}

			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
			}

			$("#divAgentConfig .chosen-container b").css("display", "none");
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
			$('#agentTypeList').prop('disabled', true).trigger('chosen:updated');
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log("-------------------------------------edit : agent-------------------------------------");
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			/*for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}*/

			//if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			//self.configId(configObj[0].agentId);
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configId(0);
				self.description("");
			}
			else{
				self.configName(configObj[0].agentName);
				self.configId(configObj[0].agentId);
				self.description(configObj[0].description);
			}
		 	window.agentDetailsChaged(false);


			self.uniqueId(self.selectedConfigRows()[0].identifier);
			$("#agentTypeList").val(configObj[0].agentTypeId).trigger('chosen:updated');
			self.onAgentTypeChange();
			$("#agentModeList option").filter(function () { return $(this).html() == configObj[0].agentMode; }).prop('selected', true).trigger('chosen:updated');

			if(self.selAgentType() == "componentagent"){
				self.compAgentCollTimeoutVal(configObj[0].collectionAgentParam.timeoutMultiplier);
				/*self.keepAliveFlag(configObj[0].collectionAgentParam.keepAlive);
				self.dataCompressionFlag(configObj[0].collectionAgentParam.dataCompression);*/

				setSelectedCompInstances();

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						for(var tagObj in self.selectedConfigRows()[0].tags){
							tagsNameArr.push(self.selectedConfigRows()[0].tags[tagObj].tagName);
						}

						$('#agent-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
					}
			}
			else if(self.selAgentType() == "JIA" || self.selAgentType() == "jimagent"){
				setSelectedCompInstances();
			}

			$("input[name='responseTimeType'][value='"+self.selectedConfigRows()[0].responseTimeType+"']").prop('checked', true);


			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			//$('#agent-tokenfield-typeahead').tokenfield('setTokens', []);
		}

		function setSelectedCompInstances(){
			debugger;
			if(self.selectedConfigRows()[0].componentInstances && self.selectedConfigRows()[0].componentInstances.length>0){
				for(var compInst in self.selectedConfigRows()[0].componentInstances){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW || self.selectedConfigRows()[0].componentInstances[compInst].status != 0){
						self.selectedCompInstArr.push({
							"id": self.selectedConfigRows()[0].componentInstances[compInst].componentInstanceId,
							"name": self.selectedConfigRows()[0].componentInstances[compInst].componentInstanceName + (self.selectedConfigRows()[0].componentInstances[compInst].status ? "" : " (Inactive)")
							//"isActive": self.selectedConfigRows()[0].componentInstances[compInst].status
						});
					}
				}

				//self.selectedCompInstArr(selCompInstArr);
				$('#selectedCompInstList').checklistbox({
		            data: self.selectedCompInstArr()
		        });
			}
		}

		function uncheckCompInst(){
			$("#selectedCompInstList .checkList").prop("checked",false);
			$("#availableCompInstList .checkList").prop("checked",false);
			$("#selAllAvailCompInst").prop("checked",false);
			$("#selAllSelCompInst").prop("checked",false);
		}

		this.handlePortRange = function(parentContextIndex, index){
			removeError("#psaPortFrom"+parentContextIndex+index);
			removeError("#psaPortTo"+parentContextIndex+index);

			$("#psaPortFrom"+parentContextIndex+index).val("");
			$("#psaPortTo"+parentContextIndex+index).val("");
			if($('input:radio[name=radioPortRange'+parentContextIndex+index+']:checked').val() == "port"){
				$("#portFromLbl"+parentContextIndex+index).css("display", "none");
				$("#portToLbl"+parentContextIndex+index).css("display", "none");
				$("#psaPortTo"+parentContextIndex+index).css("display", "none");
				$("#psaPortFrom"+parentContextIndex+index).css("margin-top", "5px");
				$("#psaPortFrom"+parentContextIndex+index).parent().css("width", "100%");
			}
			else if($('input:radio[name=radioPortRange'+parentContextIndex+index+']:checked').val() == "portRange"){
				$("#portFromLbl"+parentContextIndex+index).css("display", "block");
				$("#portToLbl"+parentContextIndex+index).css("display", "block");
				$("#psaPortTo"+parentContextIndex+index).css("display", "block");
				$("#psaPortFrom"+parentContextIndex+index).css("margin-top", "0px");
				$("#psaPortFrom"+parentContextIndex+index).parent().css("width", "45%");
			}
			return true;
		}

		this.addToSelectedCompInst = function(){
			if((self.selAgentType() == "jia" || self.selAgentType() == "jimagent") && (self.selectedCompInstArr().length + $('#availableCompInstList').getSelectedValues().length) > 1){
				showMessageBox(uiConstants.agentConfig.MULTIPLE_COMP_INSTANCES_SELECTION_ERROR + $("#agentTypeList option:selected").text(), "error");
			}
			else{
				var availArr = $('#availableCompInstList').getSelectedValues();

				var compInstObj;
				if(uiConstants.common.DEBUG_MODE)console.log(self.availableCompInstArr());

				console.log(self.selectedCompInstArr());

				for(arr in availArr){
					compInstObj = $.grep(self.availableCompInstArr(), function(e){ return e.id == availArr[arr]; });
					self.availableCompInstArr.splice(self.availableCompInstArr.indexOf(compInstObj[0]), 1);
					self.selectedCompInstArr.push(compInstObj[0]);
				}

				console.log(self.selectedCompInstArr());

		        $('#selectedCompInstList').checklistbox({
		            data: self.selectedCompInstArr()
		        });

		        $('#availableCompInstList').checklistbox({
		            data: self.availableCompInstArr()
		        });

		        uncheckCompInst();
		        self.enableAddCompInstBtn(false);
		    }
		}

		this.addToAvailableCompInst = function(){
			var selArr = $('#selectedCompInstList').getSelectedValues();
			//if(uiConstants.common.DEBUG_MODE)console.log(compInstancesCopyArr[self.searchCriteriaFlag()-1]);
			var compInstObj;
			for(arr in selArr){
				compInstObj = $.grep(self.selectedCompInstArr(), function(e){ return e.id == selArr[arr]; });
				self.selectedCompInstArr.splice(self.selectedCompInstArr.indexOf(compInstObj[0]), 1);
					
				//if(compInstancesCopyArr[self.searchCriteriaFlag()-1] && compInstancesCopyArr[self.searchCriteriaFlag()-1].indexOf(parseInt(selArr[arr])) != -1){
					self.availableCompInstArr.push(compInstObj[0]);
				//}
			}

	        $('#selectedCompInstList').checklistbox({
	            data: self.selectedCompInstArr()
	        });

	        $('#availableCompInstList').checklistbox({
	            data: self.availableCompInstArr()
	        });

	        uncheckCompInst();
	        self.enableRemoveCompInstBtn(false);
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("Agent Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onServerDetailsMastersLoad(){
			//if(serverDetVirtualHostsLoaded == 1 && serverDetApplicationsLoaded == 1 && serverDetResponseDetailsLoaded){
			if(serverDetApplicationsLoaded == 1 && serverDetResponseDetailsLoaded){
				var filtersDetArr = [];

				for(var serverDet in self.serverDetailsForAgent()){
					if(serverDet != "0"){
						self.addPsaInterface();
					}

					$("#interface"+serverDet).val(self.serverDetailsForAgent()[serverDet].interface_);
					$("#physicalAddr"+serverDet).val(self.serverDetailsForAgent()[serverDet].physicalAddress);
					$("#virtualIp"+serverDet).prop('checked', self.serverDetailsForAgent()[serverDet].virtualIp);
					self.handleVirtualHostCol(serverDet);

					filtersDetArr = self.serverDetailsForAgent()[serverDet].filters || [];

					for(var filterDet in filtersDetArr){
						if(filterDet != "0"){
							self.addPsaFilter(serverDet, filterDet);
						}

						/*$("#psaVirtualHost"+serverDet+filterDet+" option:contains(" + filtersDetArr[filterDet].virtualHost + ")").attr('selected', 'selected');
						$("#psaVirtualHost"+serverDet+filterDet).trigger('chosen:updated');
						jQuery(".chosen").chosen({
							search_contains: true	
						});*/

						//$("#psaApplications

						self.setFilterValues(serverDet, filterDet, filtersDetArr[filterDet]);

						/*if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
							//$("#interface"+serverDet).prop('readonly', true);
							//$("#physicalAddr"+serverDet).prop('readonly', true);
							//document.getElementById("virtualIp"+serverDet).disabled = true;
							$("#btnInterfaceClone"+serverDet).css("display", "none");
							$("#btnInterfaceDelete"+serverDet).css("display", "none");
						}*/
					}
				}

				if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					$(".btnDeleteInterface").css("display", "none");
					$(".btnAddFilter").css("display", "none");
					$(".btnCloneFilter").css("display", "none");
					$(".btnDeleteFilter").css("display", "none");
					//$('.psaVirtualHostClass').prop('disabled', true).trigger("chosen:updated");
					$("#psaServerDetailsPanel").find("input,select").attr("disabled", "disabled");
					$('#psaServerDetailsPanel .chosen').prop('disabled', true).trigger('chosen:updated');

				}

			}
		}

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
					if(!self.selectedConfigRows()[0].status){ //if the component is inactive
						setConfigUneditable(true);
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		/*This subscriber listens to the topic 'saveAgentDetailsClickEvent' which , if published from anywhere,  will be invoked*/
		uicommon.postbox.subscribe(function(value){
			self.addEditConfig();
		},"saveAgentDetailsClickEvent");

		function setCompAgentAdvancedSettingsDefaults(){
			$("#compAgentCollTimeout").val("2").trigger("chosen:updated");
			/*$("#keepAlive").prop('checked',true);
			$("#dataCompression").prop('checked',true);*/
			var commProtocolObj = $.grep(self.communicationProtocolArr(), function(e){ return slugify(e.protocolName) == "grpc"; });
			$("#comProtocolList option").filter(function () { return $(this).html() == commProtocolObj[0].protocolName; }).prop('selected', true).trigger("chosen:updated");
			self.onComProtocolChange();
			//self.grpcHost('localhost');
			//self.grpcPort('9150');
		}

		function setPsaAdvancedSettingsDefaults(){
			var commProtocolObj = $.grep(self.psaDataComProtocolArr(), function(e){ return slugify(e.protocolName) == "grpc"; });
			$("#psaDataComProtocol option").filter(function () { return $(this).html() == commProtocolObj[0].protocolName; }).prop('selected', true).trigger('chosen:updated');
			//self.psaDataComHost('localhost');
			//self.psaDataComPort('9150');
			$("#chkPsaHttpProxy").prop('checked',false);
			self.handleHttpProxyParams();
			self.onPsaComProtocolChange();
		}

		/*function downloadPropertiesConfig(){
			downloadAgentPropertiesConfig();
		}*/

		function successCallback(data, reqType) {
			if(reqType === "getAgentTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}

				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #agent-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#agent-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('#agent-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#agent-tokenfield-typeahead-tokenfield").blur();		
					}
				});

				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getHosts"){
				if(data.result.length == 0){
					self.hostsArr([""]);
				}
				else{
					for(var host in data.result){
						/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
							if(data.result[host].hostName && data.result[host].hostAddress){
								self.hostsArr.push(data.result[host].hostName+" ("+data.result[host].hostAddress+")" + (data.result[host].status == 0 ? "Inactive" : ""));
							}
							else if(data.result[host].hostName){
								self.hostsArr.push(data.result[host].hostName + (data.result[host].status == 0 ? "Inactive" : ""));
							}
							else if(data.result[host].hostAddress){
								self.hostsArr.push(data.result[host].hostAddress + (data.result[host].status == 0 ? "Inactive" : ""));
							}
						}
						else{
							if(data.result[host].status == 1){*/
								if(data.result[host].hostName && data.result[host].hostAddress){
									self.hostsArr.push(data.result[host].hostName+" ("+data.result[host].hostAddress+")");
								}
								else if(data.result[host].hostName){
									self.hostsArr.push(data.result[host].hostName);
								}
								else if(data.result[host].hostAddress){
									self.hostsArr.push(data.result[host].hostAddress);
								}	
							//}
						//}
					}
					/*self.hostsArr(data.result.map(function(host) {if(host.hostName != ""){return host.hostName;}}));

					if(self.hostsArr()){
						self.hostsArr(self.hostsArr().concat(data.result.map(function(host) {if(host.hostAddress != ""){return host.hostAddress;}})));
					}
					else{
						self.hostsArr(data.result.map(function(host) {if(host.hostAddress != ""){return host.hostAddress;}}));
					}*/
				}

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
						if(self.selectedConfigRows()[0].hostStatus == 1 && self.selectedConfigRows()[0].host && !$.grep(self.hostsArr(), function(e){ return e == self.selectedConfigRows()[0].host; }).length){
							self.hostsArr.push(self.selectedConfigRows()[0].host);
						}
					}
					else{
						if(self.selectedConfigRows()[0].host && !$.grep(self.hostsArr(), function(e){ return e == self.selectedConfigRows()[0].host; }).length){
							self.hostsArr.push(self.selectedConfigRows()[0].host + (self.selectedConfigRows()[0].hostStatus == 0 ? " (Inactive)" : ""));
						}
					}
					
				}
				self.hostsArr(removeDuplicates(self.hostsArr()));

				console.log(self.hostsArr());

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					var selHostArr = $.grep(self.hostsArr(), function(e){ return e == self.selectedConfigRows()[0].hostName+" ("+self.selectedConfigRows()[0].host+")"; });

					if(selHostArr.length){
						$("#hostsList option:contains(" + self.selectedConfigRows()[0].hostName +" ("+self.selectedConfigRows()[0].host +"))").attr('selected', 'selected');
					}
					else if(self.selectedConfigRows()[0].host){
						$("#hostsList option:contains(" + self.selectedConfigRows()[0].host + ")").attr('selected', 'selected');
					}
				}
				$("#hostsList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getCommunicationProtocol"){
				self.communicationProtocolArr(data.result);
				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					$("#comProtocolList option:contains(" + self.selectedConfigRows()[0].collectionAgentParam.dataProtocol + ")").filter(function() {
    					return $(this).text() === self.selectedConfigRows()[0].collectionAgentParam.dataProtocol;
					}).attr('selected', 'selected').trigger("chosen:updated");

					self.selComProtocol(slugify($("#comProtocolList option:selected").text()));

					//$("#comProtocolList").find("option[text=" + self.selectedConfigRows()[0].collectionAgentParam.dataProtocol  + "]").attr("selected", 'selected');
					if(slugify($("#comProtocolList option:selected").text()) == "grpc"){
						//self.grpcHost(self.selectedConfigRows()[0].collectionAgentParam.dataHost);
						//self.grpcPort(self.selectedConfigRows()[0].collectionAgentParam.dataPort);

						/*if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
							$('#txtGrpcHost').prop('readonly', true);
							$('#txtGrpcPort').prop('readonly', true);
						}*/
					}
					else if(slugify($("#comProtocolList option:selected").text()) == "http"){
						self.httpUrl(self.selectedConfigRows()[0].collectionAgentParam.dataUrl);

						if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
							$('#txtHttpUrl').prop('readonly', true);
						}
					}
					self.onComProtocolChange();
				}
				else{
					setCompAgentAdvancedSettingsDefaults();
				}
			}
			else if(reqType === "getGrpcSetting"){
				debugger;
				
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					data.result.grpcSettings = getMasterList(data.result.grpcSettings, "id", self.selAgentType() == "componentagent" ? [self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId] : [self.selectedConfigRows()[0].psAgentParam.grpcSettingId], true);
				}
				else{
					data.result.grpcSettings = getMasterList(data.result.grpcSettings, "id", null, false);
				}

				self.grpcSettingArr(data.result.grpcSettings);

				self.defaultGrpcId(data.result.defaultGrpcId);

				if(self.selAgentType() == "componentagent"){
					//$("#grpcSetting").trigger("chosen:updated");


					if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
						if($.grep(self.grpcSettingArr(), function(e){ return  e.id == self.defaultGrpcId(); }).length == 0){
							self.defaultGrpcId(0);
						}
						$("#grpcSetting").val(self.defaultGrpcId()).trigger("chosen:updated");
					}
					else{

						if($.grep(self.grpcSettingArr(), function(e){ return  e.id == self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId; }).length){
							$("#grpcSetting").val(self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId).trigger('chosen:updated');
						}
						else{
							$("#grpcSetting").val("0").trigger('chosen:updated');
						}

						//$("#grpcSetting").val(self.selectedConfigRows()[0].collectionAgentParam.grpcSettingId).trigger("chosen:updated");

						if(self.currentViewIndex() == uiConstants.common.READ_VIEW || self.selectedConfigRows()[0].status == 0){
							$('#grpcSetting').prop('disabled', true).trigger("chosen:updated");
						}
					}
				}
				else if(self.selAgentType() == "psagent"){
					//$("#psaGrpcSetting").trigger("chosen:updated");


					if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
						
						if($.grep(self.grpcSettingArr(), function(e){ return  e.id == self.defaultGrpcId(); }).length == 0){
							self.defaultGrpcId(0);
						}
						$("#psaGrpcSetting").val(self.defaultGrpcId()).trigger("chosen:updated");
					}
					else{

						if($.grep(self.grpcSettingArr(), function(e){ return  e.id == self.selectedConfigRows()[0].psAgentParam.grpcSettingId; }).length){
							$("#psaGrpcSetting").val(self.selectedConfigRows()[0].psAgentParam.grpcSettingId).trigger('chosen:updated');
						}
						else{
							$("#psaGrpcSetting").val("0").trigger('chosen:updated');
						}

						//$("#psaGrpcSetting").val(self.selectedConfigRows()[0].psAgentParam.grpcSettingId).trigger("chosen:updated");

						if(self.currentViewIndex() == uiConstants.common.READ_VIEW || self.selectedConfigRows()[0].status == 0){
							$('#psaGrpcSetting').prop('disabled', true).trigger("chosen:updated");
						}
					}
				}
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					$("#divAgentConfig .chosen-container b").css("display", "none");
				}
			}
			else if(reqType === "getCompTypeVersion"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.componentNamesArr([{}]);

				var compNamesArr = [];
				for(var comp in data.result){
				//	compNamesArr.push(data.result[comp].components);
					compNamesArr = compNamesArr.concat(data.result[comp].components);

				}

				self.componentNamesArr(compNamesArr);

				sortArrayObjByValue(self.componentNamesArr, "componentName");
				$("#compNamesList").val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getCompInstanceList"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selCompInstIds = [];

					if(self.selectedConfigRows()[0].componentInstances && self.selectedConfigRows()[0].componentInstances.length>0){
						for(var compInst in self.selectedConfigRows()[0].componentInstances){
							selCompInstIds.push(self.selectedConfigRows()[0].componentInstances[compInst].componentInstanceId);
						}
					}

					data.result = getMasterList(data.result, "componentInstanceId", selCompInstIds, true);
				}
				else{
					data.result = getMasterList(data.result, "componentInstanceId", null, false);
				}*/

				//compInstancesCopyArr[self.searchCriteriaFlag()-1] = [];
				self.availableCompInstArr([]);
				//self.selectedCompInstArr([]);

				var selArr = self.availableCompInstArr().length ? $('#selectedCompInstList').getAllValues() : [];
				
				for(compInst in data.result){
					if(selArr.indexOf(data.result[compInst].componentInstanceId.toString()) == -1){
						self.availableCompInstArr.push({
							"id": data.result[compInst].componentInstanceId,
							"name": data.result[compInst].componentInstanceName
						});
					}

					//compInstancesCopyArr[self.searchCriteriaFlag()-1].push(data.result[compInst].componentInstanceId);
				}

				$('#availableCompInstList').checklistbox({
		            data: self.availableCompInstArr()
		        });
			}
			/*else if(reqType === "getPsaVirtualHosts"){
				if(data.result.length == 0){
					self.psaVirtualHostsArr([{}]);
				}
				else{
					self.psaVirtualHostsArr(data.result);

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						serverDetVirtualHostsLoaded = 1;
						onServerDetailsMastersLoad();
					}
				}

			}*/
			else if(reqType === "getResponseDetails"){
				if(data.result.length == 0){
					self.responseArr([{}]);
				}
				else{
					self.responseArr(data.result);

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						serverDetResponseDetailsLoaded = 1;
						onServerDetailsMastersLoad();
					}
				}
			}
			else if(reqType === "getPsaFilterApplications"){
				if(data.result.length == 0){
					self.psaFilterApplications([{}]);
					self.psaFilterAllApplications([]);
				}
				else{
					self.psaFilterAllApplications(data.result);

					for(var app in data.result){
						if(data.result[app].status == 1){
							self.psaFilterApplications.push({
								"id": data.result[app].applicationId,
								"name": data.result[app].applicationName
							});
						}
					}

				}

				$("#psaApplications00").checklistbox({
			        data: self.psaFilterApplications()
			    });
				
				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					serverDetApplicationsLoaded = 1;
					onServerDetailsMastersLoad();
				}
			}
			else if(reqType === "getDataCommunicationProtocol"){
				if(data.result.length == 0){
					self.psaDataComProtocolArr([{}]);
					$("#psaDataComProtocol").trigger('chosen:updated');
				}
				else{
					self.psaDataComProtocolArr(data.result);
					$("#psaDataComProtocol").trigger('chosen:updated');

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						/*$("#psaDataComProtocol option:contains(" + self.selectedConfigRows()[0].psAgentParam.dataProtocol + ")").filter(function() {
    						return $(this).text() === self.selectedConfigRows()[0].psAgentParam.dataProtocol;
						}).attr('selected', 'selected');*/

						$("#psaDataComProtocol").val(self.selectedConfigRows()[0].psAgentParam.dataProtocolId).trigger('chosen:updated');
						//self.psaDataComHost(self.selectedConfigRows()[0].psAgentParam.dataHost);
						//self.psaDataComPort(self.selectedConfigRows()[0].psAgentParam.dataPort);
						//$("input[name='opModeConfig'][value='"+self.selectedConfigRows()[0].psAgentParam.configOperationModeId+"']").prop('checked', true);
						//$("input[name='opModeData'][value='"+self.selectedConfigRows()[0].psAgentParam.dataOperationModeId+"']").prop('checked', true);

						/*if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
							
						}*/
						self.onPsaComProtocolChange();
					}
					else{
						setPsaAdvancedSettingsDefaults();

					}
				}
			}
			else if(reqType === "getOperationMode"){
				if(data.result.length == 0){
					self.operationModeArr([{}]);
				}
				else{
					self.operationModeArr(data.result);

					handleOperationModeState();

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						if(self.selAgentType() == "componentagent"){
							$("input[name='compAgentOpModeConfig'][value='"+self.selectedConfigRows()[0].collectionAgentParam.configOperationModeId+"']").prop('checked', true);
							$("input[name='compAgentOpModeData'][value='"+self.selectedConfigRows()[0].collectionAgentParam.dataOperationModeId+"']").prop('checked', true);

							if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
								$("input[name='compAgentOpModeConfig']").prop("disabled", true);
								$("input[name='compAgentOpModeData']").prop("disabled", true);
							}
						}
						else if(self.selAgentType() == "psagent"){
							$("input[name='opModeConfig'][value='"+self.selectedConfigRows()[0].psAgentParam.configOperationModeId+"']").prop('checked', true);
							$("input[name='opModeData'][value='"+self.selectedConfigRows()[0].psAgentParam.dataOperationModeId+"']").prop('checked', true);

							if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
								$("input[name='opModeConfig']").prop("disabled", true);
								$("input[name='opModeData']").prop("disabled", true);
							}
						}
					}
					else{
						if(self.selAgentType() == "componentagent"){
							$("input[name='opModeConfig'][value='"+ $.grep(self.operationModeArr(), function(evt){console.log(slugify(evt.name)); return slugify(evt.name) == 'remote'; })[0].masterId +"']").prop('checked', true);
						}
						else if(self.selAgentType() == "psagent"){
							$("input[name='compAgentOpModeConfig'][value='"+ $.grep(self.operationModeArr(), function(evt){ return slugify(evt.name) == 'remote'; })[0].masterId +"']").prop('checked', true);
						}
					}
				}
			}
			else if(reqType === "getPsaFilterProtocol"){
				if(data.result.length == 0){
					self.psaFilterProtocolArr([{}]);
				}
				else{
					self.psaFilterProtocolArr(data.result);

					$("#psaProtocol00").trigger('chosen:updated');
					jQuery(".chosen").chosen({
						search_contains: true	
					});
				}
			}
			else if(reqType === "getHttpProxyProtocol"){
				if(data.result.length == 0){
					self.psaHttpProxyProtocolArr([{}]);
					$("#psaHttpProxyProtocol").trigger('chosen:updated');
				}
				else{
					self.psaHttpProxyProtocolArr(data.result);
					$("#psaHttpProxyProtocol").trigger('chosen:updated');

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						$("#chkPsaHttpProxy").prop('checked', self.selectedConfigRows()[0].psAgentParam.httpProxy);
						self.handleHttpProxyParams();
						if(self.selectedConfigRows()[0].psAgentParam.httpProxy == 1){
							$("#psaHttpProxyProtocol").val(self.selectedConfigRows()[0].psAgentParam.httpProxyProtocolId).trigger('chosen:updated');
							self.psaHttpProxyHost(self.selectedConfigRows()[0].psAgentParam.httpProxyHost);
							self.psaHttpProxyPort(self.selectedConfigRows()[0].psAgentParam.httpProxyPort);
						}

						//requestCall("http://www.mocky.io/v2/57f3475e0f000097060604da?callback=?", "GET", "", "getPsaServerDetails", successCallback, errorCallback);
						requestCall(uiConstants.common.SERVER_IP + "/serverDetails/"+self.selectedConfigRows()[0].agentId, "GET", "", "getPsaServerDetails", successCallback, errorCallback);

					}
					else{
						if(!self.serverDetailsArr().length){
							self.addPsaInterface();
						}
					}
				}

				$("#psaHttpProxyProtocol_chosen").removeClass("chosen-min-dynamic-width").addClass("chosen-min-dynamic-width");
			}
			else if(reqType === "getPsaServerDetails"){
				if(data.result.length > 0){
					self.serverDetailsForAgent(data.result);

					if(self.serverDetailsForAgent().length > 0){
						self.addPsaInterface();
					}

					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						for(var tagObj in self.selectedConfigRows()[0].tags){
							tagsNameArr.push(self.selectedConfigRows()[0].tags[tagObj].tagName);
						}

						$('#agent-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
					}
				}
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_AGENT,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.agentConfig.ERROR_ADD_AGENT, "error");
					}
				}
				else{

					if(self.selAgentType() == "componentagent" || self.selAgentType() == "psagent"){
						//self.addedUpdatedHost($("#hostsList_chosen span")[0].innerHTML.trim());
						requestCall(uiConstants.common.SERVER_IP + "/agent?limit=1&offset=1&identifier="+data.result, "GET", "", "getAgentDownloadtData", successCallback, errorCallback);
					}
					else{
						showMessageBox(uiConstants.agentConfig.SUCCESS_ADD_AGENT_OTHERS, "info");
						self.cancelConfig();
						params.curPage(1);
						self.addUpdateFlag(true);
					}
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_AGENT,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.agentConfig.ERROR_UPDATE_AGENT, "error");
					}
				}
				else{
					if(self.selAgentType() == "componentagent" || self.selAgentType() == "psagent"){
						requestCall(uiConstants.common.SERVER_IP + "/agent?limit=1&offset=1&identifier="+self.selectedConfigRows()[0].identifier, "GET", "", "getAgentDownloadtData", successCallback, errorCallback);
					}
					else{
						showMessageBox(uiConstants.agentConfig.SUCCESS_UPDATE_AGENT_OTHERS, "info");
						self.cancelConfig();
						params.curPage(1);
						self.addUpdateFlag(true);
					}
				}
			}
			else if(reqType === "getAgentDownloadtData"){
				if(data.responseStatus == "success"){
					window.agentConfDownloadData = data.result[0];
 //+ "<br><br><span style='color: blue; cursor: pointer' onclick='downloadAgentPropertiesConfig(null)'><u>Download conf.properties</u></span>"
					showMessageBox((self.configId() == 0 ? uiConstants.agentConfig.SUCCESS_ADD_AGENT : uiConstants.agentConfig.SUCCESS_UPDATE_AGENT) + window.agentConfDownloadData.identifier + "<br><span style='color: blue; cursor: pointer' onclick='downloadAgentPropertiesConfig(null)'><u>Download basic.properties</u></span><br><span style='color: blue; cursor: pointer' onclick='downloadAgentDetailsJson(\""+window.agentConfDownloadData.identifier+"\")'><u>Download agent-details.json</u></span>", "info");

					self.cancelConfig();
					params.curPage(1);
					self.addUpdateFlag(true);
				}else{
					showMessageBox(data.message, "error");
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getAgentTag"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_CLUSTER_TAGS, "error");
			}
			else if(reqType === "getHosts"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_HOSTS, "error");
			}
  			else if(reqType === "getCommunicationProtocol"){
  				showMessageBox(uiConstants.agentConfig.ERROR_GET_COM_PROTOCOLS, "error");
  			}
  			else if(reqType === "getGrpcSetting"){
  				showMessageBox(uiConstants.agentConfig.ERROR_GET_GRPC_SETTINGS, "error");
  			}
			else if(reqType === "getCompTypeVersion"){
				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
			}
			else if(reqType === "getCompInstanceList"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_COMP_INSTANCES, "error");
			}
			/*else if(reqType === "getPsaVirtualHosts"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_VIRTUAL_HOSTS, "error");
			}*/
			else if(reqType === "getResponseDetails"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_RESPONSE_DETAILS, "error");
			}
			else if(reqType === "getPsaFilterApplications"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_APPLICATIONS, "error");
			}
			else if(reqType === "getDataCommunicationProtocol"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_COM_PROTOCOLS, "error");
			}
			else if(reqType === "getOperationMode"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_OPERATION_MODES, "error");
			}
			else if(reqType === "getPsaServerDetails"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_PSA_SERVER_DETAILS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.agentConfig.ERROR_ADD_AGENT, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.agentConfig.ERROR_UPDATE_AGENT, "error");
			}
			else if(reqType === "getAgentDownloadtData"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENT_CONFIG_PROPERTIES, "error");
			}
		}
	}

	AgentAddEdit.prototype.dispose = function() { };
	return { viewModel: AgentAddEdit, template: templateMarkup };
});