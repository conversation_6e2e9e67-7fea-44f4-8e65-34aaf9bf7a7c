define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./kpi-group-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','jQuery-plugins','fsstepper'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,jQueryPlugins,fsstepper) {

	function KpiGroupAddEdit(params) {
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = ko.observableArray();
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.regexPattern = ko.observable("");
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableConfig = ko.observable(true);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		this.kpiTypeArr = params.kpiTypeArr;
  		this.discoveryFlag = ko.observable(0);
		
		var configTagLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			//$("input[type=number]").stepper();

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#kpiTypeList").trigger('chosen:updated');

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$("#kpiGrpDiscoveryMode").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "On",
				'offText': "Off"
			});

			$('#kpiGrpDiscoveryMode').bootstrapSwitch('state', self.discoveryFlag());

			$("#kpiGrpDiscoveryMode").on('switchChange.bootstrapSwitch', function () {
				self.discoveryFlag($('#kpiGrpDiscoveryMode').bootstrapSwitch('state')?1:0);
			});

			self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW && self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			$('#kpi-group-tokenfield-typeahead')
			.on('tokenfield:createdtoken', function (e) {

				if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
					tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
				}
				var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
				//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
				if(tagIndex != -1){
					self.configTagAutoCompleteArr.splice(tagIndex, 1);
				}

				$('#kpi-group-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			})

			.on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			.on('tokenfield:removedtoken', function (e) {
				if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
					tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
				}
				tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
				var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

				if(tagIndex != -1){
					self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
					self.configTagAutoCompleteArr.sort();
				}

				$('#kpi-group-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			});

			if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				$('#kpiGrpDiscoveryMode').bootstrapSwitch('state',self.discoveryFlag());
			}

	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=KpiGroup", "GET", "", "getKpiGroupTag", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

       	//Adding/Updating single KPI group
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var configData;

			$("#divKpiGroup #txtName").val($("#divKpiGroup #txtName").val().trim());
			$("#divKpiGroup #txtDescription").val($("#divKpiGroup #txtDescription").val().trim());
			$("#divKpiGroup #txtRegexPattern").val($("#divKpiGroup #txtRegexPattern").val().trim());

			if($("#divKpiGroup #txtName").val().trim() == ""){
				showError("#divKpiGroup #txtName", uiConstants.kpiGroupConfig.KPI_GROUP_NAME_REQUIRED);
		    	self.errorMsg("#divKpiGroup #txtName");
			}
			else if($("#divKpiGroup #txtName").val().length < 2){
				showError("#divKpiGroup #txtName", uiConstants.kpiGroupConfig.KPI_GROUP_NAME_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divKpiGroup #txtName");
			}
			else if($("#divKpiGroup #txtName").val().length > 45){
				showError("#divKpiGroup #txtName", uiConstants.kpiGroupConfig.KPI_GROUP_NAME_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divKpiGroup #txtName");
			}
			else if(!nameValidation($("#divKpiGroup #txtName").val())){
				showError("#divKpiGroup #txtName", uiConstants.kpiGroupConfig.KPI_GROUP_NAME_INVALID_ERROR);
		    	self.errorMsg("#divKpiGroup #txtName");
			}
			if($("#divKpiGroup #txtDescription").val().trim() == ""){
				showError("#divKpiGroup #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
		    	self.errorMsg("#divKpiGroup #txtDescription");
			}
			else if($("#divKpiGroup #txtDescription").val().length < 25){
				showError("#divKpiGroup #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divKpiGroup #txtDescription");
			}
			else if($("#divKpiGroup #txtDescription").val().length > 256){
				showError("#divKpiGroup #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divKpiGroup #txtDescription");
			}
			if(!$("#divKpiGroup #kpiTypeList").val()){
				showError("#divKpiGroup #kpiTypeList_chosen", uiConstants.kpiGroupConfig.SELECT_KPI_GROUP_TYPE_MSG);
				showError("#divKpiGroup #kpiTypeList_chosen span", uiConstants.kpiGroupConfig.SELECT_KPI_GROUP_TYPE_MSG);
		    	self.errorMsg("#divKpiGroup #kpiTypeList_chosen");
			}
			if($("#divKpiGroup #txtRegexPattern").val()){
				try{
					new RegExp($("#divKpiGroup #txtRegexPattern").val());
				}
				catch(err){
					showError("#divKpiGroup #txtRegexPattern", uiConstants.kpiGroupConfig.INVALID_REGEX_PATTERN);
			    	self.errorMsg("#divKpiGroup #txtRegexPattern");
				}
			}

			removeError("#divKpiGroup .tokenfield");
			removeError("#divKpiGroup #kpi-group-tokenfield-typeahead-tokenfield");
			
			if(containsDuplicate($("#divKpiGroup #kpi-group-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divKpiGroup .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divKpiGroup #kpi-group-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divKpiGroup .tokenfield");
			}
			else{

				if(self.errorMsg() == ""){
					if(self.tags() && self.tags().trim().length == 1)
						tagsArr.push(self.tags());

					else if(self.tags() && self.tags().trim().length > 1)
						tagsArr = self.tags().split(",");
					
					for(var t in tagsArr){
						if(tagsArr[t].trim().length < 2){
							showError("#divKpiGroup .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("#divKpiGroup #kpi-group-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("#divKpiGroup .tokenfield");
							break;
						}
						else if(tagsArr[t].trim().length > 45){
							showError("#divKpiGroup .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("#divKpiGroup #kpi-group-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("#divKpiGroup .tokenfield");
							break;
						}
						else if(!tagValidation(tagsArr[t].trim())){
							showError("#divKpiGroup .tokenfield", uiConstants.common.INVALID_TAG_NAME);
							showError("#divKpiGroup #kpi-group-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("#divKpiGroup .tokenfield");
							break;
						}
					}

					if(self.errorMsg() == ""){
						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}
					}

					if(self.errorMsg() == ""){
						var configData = {"index":1,
							"name": self.configName(),
							"description":  self.configDescription().trim(),
							"tags": tagsObjArr,
							"kpiTypeId": $("#kpiTypeList").val(),
							"pattern": self.regexPattern(),
							"discoveryFlag" : self.discoveryFlag()?1:0,
							"status" : self.configStatus()?1:0};

						if(self.configId() == 0)
							requestCall(uiConstants.common.SERVER_IP + "/kpiGroup", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
						else
							requestCall(uiConstants.common.SERVER_IP + "/kpiGroup/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
					}
				}
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();

			if(self.selectedConfigRows()[0].isCustom == 0 && self.selectedConfigRows()[0].status == 1){
				$('#kpi-group-tokenfield-typeahead').tokenfield('writeable');
			}
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			setConfigUneditable();
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(){
			if(self.selectedConfigRows()[0].isCustom == 0 || self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#kpiTypeList').prop('disabled', true).trigger('chosen:updated');
				$('#txtRegexPattern').prop('readonly', true);
				$('#kpi-group-tokenfield-typeahead').tokenfield('readonly');
				$("[name='kpiGrpDiscoveryMode']").bootstrapSwitch('disabled',true);
			}
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW || self.selectedConfigRows()[0].isCustom == 0){
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divKpiGroup .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].name);
				self.configId(configObj[0].id);
				self.configDescription(configObj[0].description);
			}

			$("#kpiTypeList").val(configObj[0].kpiTypeId).prop('disabled', true);
			$("#kpiTypeList").val(configObj[0].kpiTypeId).trigger('chosen:updated');;
			self.regexPattern(configObj[0].pattern);

			self.discoveryFlag(configObj[0].discoveryFlag);
			$('#kpiGrpDiscoveryMode').bootstrapSwitch('state',self.discoveryFlag());
			$("[name='kpiGrpDiscoveryMode']").bootstrapSwitch('disabled',true);

			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#kpi-group-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			self.errorMsg("");
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("KPI Group Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}

				$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getKpiGroupTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#kpi-group-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#kpi-group-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#kpi-group-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#kpi-group-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_KPI_GROUP,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.kpiGroupConfig.ERROR_ADD_KPI_GROUP, "error");
					}
				}
				else{
					params.curPage(1);
					self.cancelConfig();

					showMessageBox(uiConstants.kpiGroupConfig.SUCCESS_ADD_KPI_GROUP);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_KPI_GROUP,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.kpiGroupConfig.ERROR_UPDATE_KPI_GROUP, "error");
					}
				}
				else{
					showMessageBox(uiConstants.kpiGroupConfig.SUCCESS_UPDATE_KPI_GROUP);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getKpiGroupTag"){
				showMessageBox(uiConstants.kpiGroupConfig.ERROR_GET_KPI_GROUP_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.kpiGroupConfig.ERROR_ADD_KPI_GROUP, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.kpiGroupConfig.ERROR_UPDATE_KPI_GROUP, "error");
			}
		}
	}

	KpiGroupAddEdit.prototype.dispose = function() { };
	return { viewModel: KpiGroupAddEdit, template: templateMarkup };
});