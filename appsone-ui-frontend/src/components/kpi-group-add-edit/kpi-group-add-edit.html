<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default">
	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body" id="divKpiGroup">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>

			<div id="divKpiName" class="form-group form-required" >
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
				</div>
			</div>

			<div id="divKpiDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description<span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription,attr:{'title':uiConstants.kpiConfig.KPI_DESC_INFO}" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>
			
			<div class="form-group" id="divKpiType">
				<label class="control-label col-sm-2">Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="kpiTypeList" data-bind="options: kpiTypeArr(),  optionsText: 'kpiType', optionsValue: 'kpiTypeId', optionsCaption: 'Select'" required="true" >
					</select>
				</div>
			</div>

			<div id="divRegex" class="form-group form-required" >
				<label class="control-label col-sm-2">Regex Pattern</label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtRegexPattern" data-bind="value: regexPattern" placeholder="Enter Regex Pattern" title="Provide an acceptable format for the values expected">
				</div>
			</div>

			<div id="tagsSingleApp" class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="kpi-group-tokenfield-typeahead" data-bind="value: tags" >
				</div>
			</div>

			<div id="divKpiGrpDiscoveryStatus" class="form-group">
				<label class="control-label col-sm-2" >Discovery Mode</label>
				<div class="col-sm-4">
					<input type="checkbox" id="kpiGrpDiscoveryMode" data-on-color="success" data-off-color="danger" data-size="mini" name="kpiGrpDiscoveryMode">
				</div>
			</div>

			<div id="divAppStatus" class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>