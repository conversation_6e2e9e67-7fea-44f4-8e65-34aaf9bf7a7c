define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./component-instance-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,floatThead) {
		this.hostsArr = ko.observableArray([{}]);
	function ComponentInstanceAddEdit(params) {
		var self = this;

		this.componentsArr = ko.observableArray();//params.componentsArr;
		this.componentNamesArr = ko.observableArray([{}]);
		this.componentVersionsArr = ko.observableArray([{}]);
		this.clusterNamesArr = ko.observableArray([{}]);
		//this.hostsArr = ko.observableArray([{}]);//params.hostsArr;
		this.compAttributesArr = ko.observableArray();
		this.applicationsArr = ko.observableArray();//params.applicationsArr;
		this.kpiProducerMapArr = ko.observableArray();
		this.kpiGroupProducerMapArr = ko.observableArray();
		this.multipleInstancesKpiProducerMapArr = ko.observableArray();
		this.multipleInstancesKpiGroupProducerMapArr = ko.observableArray();
		this.producerArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.selectedConfigRows = params.selectedConfigRows || ko.observableArray([]);
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
  		this.displayComponent = ko.observable(0);
		this.isKpiGroupExits = ko.observable(false);
		this.kpiGroupArr = ko.observableArray();
		this.kpiGroupValueArr = ko.observableArray();
		this.kpiRowIndex = ko.observable();
		this.kpiProducerCompInstMapArr = ko.observableArray();
  		this.isAppMandatory = ko.observable(false);
  		this.modalTitle = ko.observable();
  		this.compType = ko.observable("");
  		this.compName = ko.observable("");
  		this.selectedConfigNames = ko.observable("");
  		this.enableConfig = ko.observable(true);
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		self.availableApplicationArr = ko.observableArray();
		self.selectedApplicationArr = ko.observableArray();
  		this.enableAddApplicationBtn = ko.observable(false);
  		this.enableRemoveApplicationBtn = ko.observable(false);
  		//mk self.availableClusterArr = ko.observableArray();
		//mk self.selectedClusterArr = ko.observableArray();
  		//mk this.enableAddClusterBtn = ko.observable(false);
  		//mk this.enableRemoveClusterBtn = ko.observable(false);
  		this.isModal = ko.observable(params.isModal || false);
  		this.modalCompType = ko.observable(params.modalCompType || "");
  		this.hostAddress = ko.observable();
  		this.selectedCompType = ko.observable();
		this.hostAddressAttribId = ko.observable(0);
		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.modalParam = ko.observableArray([]);
		//this.description = ko.observable();
		var configTagLoaded = 0;
		var compTypeVersionLoaded = 0;
		var applicationLoaded = 0;
		var hostLoaded = 0;
		//var appArr = [];
		var tagsNameArr = [];
		var commonTagsArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var kpiGroupValArr=[];
		var selectedCompTypeId = 0;
		var selectedCompId = 0;
		var hostAddressAttribMappingId = 0;
  		var previousCompTypeId;
  		var previousCompId;
  		var previousCompVersionId;

  		
		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			$("#hostModalmodalCompType").css("display", "none");
			$("#hostModalbtnAddComp").css("display", "none");

			$("#"+self.modalCompType()+"txtName").focus();

			var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_COMPTYPE_LINK; });
      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
				$("#modalCompType").css("visibility","hidden");
      		}

      		optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_COMPONENT_LINK; });
      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
				$("#btnAddComp").css("visibility","hidden");
				$("#modalHost").css("visibility","hidden");
      		}
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#"+self.modalCompType()+"configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$("#"+self.modalCompType()+"configStatus").bootstrapSwitch('state', 1);

			$("#"+self.modalCompType()+"configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#'+self.modalCompType()+'configStatus').bootstrapSwitch('state')?1:0);
			});

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["componentInstanceName"]));
			}

			$("#idModal").on('hidden.bs.modal', function () {
				//self.displayComponent(0);
				//self.modalCompType("");

				if(self.modalCompType() == ""){
					self.isModal(false);

			        if(self.compType() && self.compType() != ""){
			        	self.compType("");
			        	getCompType();
			        }
			        else if(self.compName() && self.compName() != ""){
			        	self.compName("");
			        	getCompType();
			        }
			        else{
			        	$("#"+self.modalCompType()+"compTypeList").val(selectedCompTypeId).trigger('chosen:updated');
						setCompNames();
						$("#"+self.modalCompType()+"compNamesList").val(selectedCompId).trigger('chosen:updated');
			        }
					//self.compType("");
				} 
				else if(self.modalCompType() == "hostModal" && self.compName() != ""){
					self.modalCompType("");
					requestCall(uiConstants.common.SERVER_IP + "/host?status=1", "GET", "", "getHosts", successCallback, errorCallback);
				}
		    });

			$("#idModal").on('show.bs.modal', function () {
				selectedCompTypeId = $("#compTypeList").val();
		    	selectedCompId = $("#compNamesList").val();
		    });

		    $("#idModal").on('shown.bs.modal', function () {
		    	var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_COMPTYPE_LINK; });
	      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("div #modalCompType").css("visibility","hidden");
	      		}
	      		
	      		optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_COMPONENT_LINK; });
	      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("div #btnAddComp").css("visibility","hidden");
	      		}
		    });

			if(params.selectedConfigRows() && params.selectedConfigRows().length>1){
				debugger;
				$("#divCompInstanceName").hide();
				//$("#divCompInstDescription").hide();
				$("#divApplication").hide();

				$("#divCluster").hide();
				$("#advSettingsSep").hide();
			}

			$('#'+self.modalCompType()+'kpiProducerMappingList tbody').on('click', '.'+self.modalCompType()+'chkboxMappingCol', function(e){
				setHeaderCheckboxState('#'+self.modalCompType()+'chkboxMappingHeader', '.'+self.modalCompType()+'chkboxMappingCol', self.kpiProducerMapArr());
			});

			$('#'+self.modalCompType()+'grpKpiProducerMappingList tbody').on('click', '.'+self.modalCompType()+'grpChkboxMappingCol', function(e){
				setHeaderCheckboxState('#'+self.modalCompType()+'grpChkboxMappingHeader', '.'+self.modalCompType()+'grpChkboxMappingCol', self.kpiGroupProducerMapArr());
			});

			$('#'+self.modalCompType()+'kpiProducerMappingList thead').on('click', '#'+self.modalCompType()+'chkboxMappingHeader', function(e){
				if (this.checked == false) {
					$("."+self.modalCompType()+"chkboxMappingCol").prop("checked",false);
				}
				else {
					$("."+self.modalCompType()+"chkboxMappingCol").prop("checked",true);
				}
			});

			$('#'+self.modalCompType()+'grpKpiProducerMappingList thead').on('click', '#'+self.modalCompType()+'grpChkboxMappingHeader', function(e){
				if (this.checked == false) {
					$("."+self.modalCompType()+"grpChkboxMappingCol").prop("checked",false);
				}
				else {
					$("."+self.modalCompType()+"grpChkboxMappingCol").prop("checked",true);
				}
			});

			$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if((commonTagsArr.indexOf(e.attrs.label.trim()) !=-1 || tagsNameArr.indexOf(e.attrs.label.trim()) !=-1) && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			$("#"+self.modalCompType()+"compTypeList").on('chosen:showing_dropdown', function () {
				previousCompTypeId = $("#"+self.modalCompType()+"compTypeList").val();
			})
			.on('change', function () {
		    	if(previousCompTypeId == 0){
			    	setCompNames();
			    	setCompVersions();
			    	setClusters();
			    	setCompAttributes();
			    	setCompKpiProducerMap();
			    }

			    else{
		    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClearCompInst){
						if(confirmClearCompInst){
					    	setCompNames();
					    	setCompVersions();
					    	setClusters();
					    	setCompAttributes();
					    	setCompKpiProducerMap();
		    			}

			    		else{
			    			$("#"+self.modalCompType()+"compTypeList").val(previousCompTypeId).trigger("chosen:updated");
			    		}
	    			});
			   	}
			});

			/*$("#"+self.modalCompType()+"compNamesList").on('change', function () {
		    	setCompVersions();
		    	setClusters();
		    	setCompAttributes();
		    	setCompKpiProducerMap();
			});*/


			$("#"+self.modalCompType()+"compNamesList").on('chosen:showing_dropdown', function () {
				previousCompId = $("#"+self.modalCompType()+"compNamesList").val();
			})
			.on('change', function () {
		    	if(previousCompId == 0){
			    	setCompVersions();
			    	setClusters();
			    	setCompAttributes();
			    	setCompKpiProducerMap();
			    }

			    else{
		    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_CHANGE, "question", "confirm", function confirmCallback(confirmClearCompInst){
						if(confirmClearCompInst){
					    	setCompVersions();
					    	setClusters();
					    	setCompAttributes();
					    	setCompKpiProducerMap();
		    			}

			    		else{
			    			$("#"+self.modalCompType()+"compNamesList").val(previousCompId).trigger("chosen:updated");
			    		}
	    			});
			   	}
			});




			/*$("#"+self.modalCompType()+"compVersionsList").on('change', function () {
		    	setCompAttributes();
		    	setCompKpiProducerMap();

		    	if($("#"+self.modalCompType()+"attribsPanel").is(":hidden")){
		    		self.expandCollapsePanel("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
		    	}
			});*/

			$("#"+self.modalCompType()+"compVersionsList").on('chosen:showing_dropdown', function () {
				previousCompVersionId = $("#"+self.modalCompType()+"compVersionsList").val();
			})
			.on('change', function () {
		    	if(previousCompVersionId == 0){
			    	setCompAttributes();
			    	setCompKpiProducerMap();

			    	if($("#"+self.modalCompType()+"attribsPanel").is(":hidden")){
			    		self.expandCollapsePanel("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
			    	}
			    }

			    else{
		    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_VERSION_CHANGE, "question", "confirm", function confirmCallback(confirmClearCompInst){
						if(confirmClearCompInst){
					    	setCompAttributes();
					    	setCompKpiProducerMap();

					    	if($("#"+self.modalCompType()+"attribsPanel").is(":hidden")){
					    		self.expandCollapsePanel("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
					    	}
		    			}

			    		else{
			    			$("#"+self.modalCompType()+"compVersionsList").val(previousCompVersionId).trigger("chosen:updated");
			    		}
	    			});
			   	}
			});

			$("div").on("click", "#"+self.modalCompType()+"selAllAvailApplication", function(e){
				$("#"+self.modalCompType()+"availableApplicationList .checkList").prop("checked", $("#"+self.modalCompType()+"selAllAvailApplication").prop("checked"));
				self.enableAddApplicationBtn($("#"+self.modalCompType()+"availableApplicationList .checkList:checked").length);
			});

			$("div").on("change", "#"+self.modalCompType()+"availableApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#"+self.modalCompType()+"selAllAvailApplication").prop("checked", self.availableApplicationArr().length == $("#"+self.modalCompType()+"availableApplicationList .checkList:checked").length);
					self.enableAddApplicationBtn($("#"+self.modalCompType()+"availableApplicationList .checkList:checked").length);
				}
	        });


	        $("div").on("click", "#"+self.modalCompType()+"selAllSelApplication", function(e){
				$("#"+self.modalCompType()+"selectedApplicationList .checkList").prop("checked", $("#"+self.modalCompType()+"selAllSelApplication").prop("checked"));
				self.enableRemoveApplicationBtn($("#"+self.modalCompType()+"selectedApplicationList .checkList:checked").length);
			});

			$("div").on("click", "#"+self.modalCompType()+"selectedApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#"+self.modalCompType()+"selAllSelApplication").prop("checked", self.selectedApplicationArr().length == $("#"+self.modalCompType()+"selectedApplicationList .checkList:checked").length);
					self.enableRemoveApplicationBtn($("#"+self.modalCompType()+"selectedApplicationList .checkList:checked").length);
				}
			});

			//TODO: Uncomment this once the API is ready
	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=ComponentInstance", "GET", "", "getCompInstTag", successCallback, errorCallback);

	        getCompType();

	        $('#'+self.modalCompType()+'compNamesList').prop('disabled', true).trigger("chosen:updated");
			$('#'+self.modalCompType()+'compVersionsList').prop('disabled', true).trigger("chosen:updated");
			//mk $('#'+self.modalCompType()+'clusterNamesList').prop('disabled', true).trigger("chosen:updated");
	        requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getApplications", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575bf9f70f00008a164fc99f?callback=?", "GET", "", "getApplications", successCallback, errorCallback);
			
			if(self.modalCompType() != "hostModal")
			requestCall(uiConstants.common.SERVER_IP + "/host?status=1", "GET", "", "getHosts", successCallback, errorCallback);
		}

		this.switchModal = function(modalScreen){
			self.displayComponent(0);
			//self.modalCompType("");
			if(modalScreen == "componentType"){
				self.displayComponent(1);
				self.modalTitle("Add Component Type");
			}
			else if(modalScreen == "component"){
				self.displayComponent(2);
				self.modalTitle("Add Component");
			}
			else if(modalScreen == "host"){
				self.displayComponent(3);
				self.modalTitle("Add Host");
				//self.modalCompType("hostModal");
			}

			/*
			else if(modalScreen == "cluster"){
				self.displayComponent(3);
				self.modalTitle("Add Cluster");
			}*/
		}

		function getCompType(){
			requestCall(uiConstants.common.SERVER_IP + "/component?status=2&markInactive=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575b0eb6110000b421539fbb?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
		}

		this.onChosenDropdownOpen = function(elementId, parentDiv){
			onChosenDropdownOpen(elementId, parentDiv);
	    };

	    this.onChosenDropdownClose = function(elementId, parentDiv){
			onChosenDropdownClose(elementId, parentDiv);
	    };

	    this.addToSelectedApplication = function(){
			var availArr = $('#'+self.modalCompType()+'availableApplicationList').getSelectedValues();

			var applicationObj;
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableApplicationArr());

			for(arr in availArr){
				applicationObj = $.grep(self.availableApplicationArr(), function(e){ return e.id == availArr[arr]; });
				self.availableApplicationArr.splice(self.availableApplicationArr.indexOf(applicationObj[0]), 1);
				self.selectedApplicationArr.push(applicationObj[0]);
			}

			$('#'+self.modalCompType()+'selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#'+self.modalCompType()+'availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableAddApplicationBtn(false);
			setClusters();
		}

		this.addToAvailableApplication = function(){
			var selArr = $('#'+self.modalCompType()+'selectedApplicationList').getSelectedValues();
			var applicationObj;
			for(arr in selArr){
				applicationObj = $.grep(self.selectedApplicationArr(), function(e){ return e.id == selArr[arr]; });
				self.selectedApplicationArr.splice(self.selectedApplicationArr.indexOf(applicationObj[0]), 1);
				self.availableApplicationArr.push(applicationObj[0]);
			}

			$('#'+self.modalCompType()+'selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#'+self.modalCompType()+'availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableRemoveApplicationBtn(false);
			setClusters();
		}

		function setKpiGroup(kpiIndex){
			self.isKpiGroupExits(false);
			self.kpiGroupArr([]);
	    	self.kpiRowIndex(kpiIndex);

	    	if(self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupId){
	    		self.isKpiGroupExits(true);
	    		self.kpiGroupArr.push(self.kpiGroupProducerMapArr()[kpiIndex]);
	    	}

			self.kpiGroupValueArr([]);

			if(kpiGroupValArr[self.kpiRowIndex()]){
		    	self.kpiGroupValueArr.splice(0,1,kpiGroupValArr[self.kpiRowIndex()]);
		    }

	    	if(uiConstants.common.DEBUG_MODE)console.log(kpiGroupValArr);
		}

		function uncheckApplication(){
			$("#"+self.modalCompType()+"selectedApplicationList .checkList").prop("checked",false);
			$("#"+self.modalCompType()+"availableApplicationList .checkList").prop("checked",false);
			$("#"+self.modalCompType()+"selAllAvailApplication").prop("checked",false);
			$("#"+self.modalCompType()+"selAllSelApplication").prop("checked",false);
		}

		function setHeaderCheckboxState(headerCheckbox, checkboxColumn, mapArr){
			if ($(checkboxColumn + ':checked').length == mapArr.length) {
				$(headerCheckbox).prop("checked",true);
			}
			else {
				$(headerCheckbox).prop("checked",false);
			}
		}

		this.removeKpiGroupValue = function(rowIndex, itemIndex){
		    showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_KPI_GROUP_VALUE_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if (confirmDelete) {
					kpiGroupValArr[rowIndex].splice(itemIndex,1);
					self.kpiGroupValueArr.splice(rowIndex, 1, kpiGroupValArr[rowIndex]);
				}
			});
		}


//var kpiGroup = [];
		
		this.addKpiGroupValue = function(rowIndex){
			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupValueArr());
			if(kpiGroupValArr[rowIndex] == undefined){
				kpiGroupValArr[rowIndex] = [{'kpiGroupValue':''}];
			}
			else{
				kpiGroupValArr[rowIndex].push({'kpiGroupValue':''});
			}

			self.kpiGroupValueArr.splice(rowIndex, 1, kpiGroupValArr[rowIndex]);
			//self.kpiGroupValueArr()[rowIndex]=kpiGroupValArr[rowIndex];

			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupValueArr());
		}

        this.expandCollapsePanel = function(panelBody, expandCollpaseImg){
        	$(panelBody).toggle();

        	if($(panelBody).is(":hidden")){
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-bottom").addClass("glyphicon-triangle-right");
        		$(expandCollpaseImg).attr("title", "Expand");
        	}
        	else{
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-right").addClass("glyphicon-triangle-bottom");
        		$(expandCollpaseImg).attr("title", "Collapse");
        	}
        }

        self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

        this.updateMultipleConfigs = function(){
        	var compObjArr =[];
        	var configData;
    		var indexCounter = 1;
        	var statusFlag = $('#configStatus').bootstrapSwitch('state')?1:0;
        	var appsArr = [];
        	var clustersArr = [];
			var attributesList = [];
			var nonGroupkpiProdMapArr = [];
			var groupkpiProdMapArr = [];
			var tagsObjArr = [];
			var tagsArr = [];
       		var hostDet = "";
       		var isAttribErrorExists = 0;
       		var isAdvSettingsErrorExists = 0;
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

        	for(selConfig in self.selectedConfigRows()){
        		appsArr = [];
        		clustersArr = [];
        		attributesList = [];
				nonGroupkpiProdMapArr = [];
				groupkpiProdMapArr = [];
        		tagsObjArr = [];
        		hostDet = "";

        		if($('#configStatus').bootstrapSwitch('indeterminate')){
        			statusFlag = self.selectedConfigRows()[selConfig].status;
        		}

        		for(app in self.selectedConfigRows()[selConfig].applications){
        			appsArr.push(self.selectedConfigRows()[selConfig].applications[app].applicationId);
        		}

        		for(cluster in self.selectedConfigRows()[selConfig].clusters){
        			clustersArr.push(self.selectedConfigRows()[selConfig].clusters[cluster].clusterId);
        		}

        		removeError("#attribsPanel input");
				removeError("#attribsPanel select");
				removeError("#hostsList_chosen");
				removeError("#hostsList_chosen span");

				if(slugify(self.selectedCompType()) == "host"){
					if(rgbTohex($("#txtHostAddress").css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#txtHostAddress").val() == ""){
						//self.errorMsg("Please enter value for attribute: Host Address");
						self.expandPanelOnError("#attribsPanel", "#imgAttribShowHide");
							
						showError("#txtHostAddress", "Please enter value");
					    self.errorMsg("#attribsPanel");

					    $("#attribsPanel").animate({
						    scrollTop: $("#txtHostAddress").offset().top - $("#attribsPanel").offset().top + $("#attribsPanel").scrollTop() - 10
						});

						isAttribErrorExists = 1;
					}
					else{

//						slugify(self.selectedCompType()) != "host" ? $("#hostsList_chosen span")[0].innerHTML : self.hostAddress()
						hostDet = self.hostAddress() || self.selectedConfigRows()[selConfig].hostAddress;
					}
				}
				else{
					if(rgbTohex($("#hostsList").next(".chosen-container").css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#hostsList_chosen span")[0].innerHTML == ""){
						showError("#hostsList_chosen", uiConstants.common.SELECT_HOST_MSG);
						showError("#hostsList_chosen span", uiConstants.common.SELECT_HOST_MSG);
					    self.errorMsg("#hostsList_chosen");

					    $("#attribsPanel").animate({
						    scrollTop: $("#hostsList_chosen").offset().top - $("#attribsPanel").offset().top + $("#attribsPanel").scrollTop() - 10
						});

						isAttribErrorExists = 1;
					}
					else{
						hostDet = $("#hostsList_chosen span")[0].innerHTML == "" ? self.selectedConfigRows()[selConfig].hostAddress : $("#hostsList_chosen span")[0].innerHTML;
					}
				}


				if(self.hostAddressAttribId() != 0){
					attributesList.push({
						"attributeId": self.hostAddressAttribId(),
						"attributeValue": hostDet.indexOf("(") != -1 ? hostDet.split("(")[1].split(")")[0] : hostDet,
						"componentAttributeMappingId": hostAddressAttribMappingId
					});
				}

        		for(compAttr in self.compAttributesArr()){
        			var selComponentsAttrib = $.grep(self.selectedConfigRows()[selConfig].attributes, function(e){ return e.attributeId == self.compAttributesArr()[compAttr].attributeId; }); //self.selectedConfigRows()[selConfig].attributes[compAttr].attributeId; });

        			if(selComponentsAttrib[0]){
        				if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX'){
							if(rgbTohex($("#txtbox"+compAttr).css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && self.compAttributesArr()[compAttr].isMandatory && $("#txtbox"+compAttr).val() == ""){
								self.expandPanelOnError("#attribsPanel", "#imgAttribShowHide");
								showError("#txtbox"+compAttr, "Please enter value");
							    self.errorMsg("#attribsPanel");

						    	if(isAttribErrorExists == 0){
								    $("#attribsPanel").animate({
									    scrollTop: $("#txtbox"+compAttr).offset().top - $("#attribsPanel").offset().top + $("#attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else{
								attributesList.push({
									"attributeId": parseInt(selComponentsAttrib[0].attributeId),
									"attributeValue": rgbTohex($("#txtbox"+compAttr).css("background-color")).toUpperCase() == uiConstants.common.UNCOMMON_VALUE_BGCOLOR ?
										($("#txtbox"+compAttr).val()==""?selComponentsAttrib[0].attributeValue : $("#txtbox"+compAttr).val()) : $("#txtbox"+compAttr).val(),
									"componentAttributeMappingId": selComponentsAttrib[0].componentAttributeMappingId
								});
							}
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
							if(rgbTohex($("#password"+compAttr).css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && self.compAttributesArr()[compAttr].isMandatory && $("#password"+compAttr).val() == ""){
								//self.errorMsg("Please enter value for attribute: "+self.compAttributesArr()[compAttr].attributeName)

								self.expandPanelOnError("#attribsPanel", "#imgAttribShowHide");
								showError("#password"+compAttr, "Please enter value");
							    self.errorMsg("#attribsPanel");

						    	if(isAttribErrorExists == 0){
								    $("#attribsPanel").animate({
									    scrollTop: $("#password"+compAttr).offset().top - $("#attribsPanel").offset().top + $("#attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							attributesList.push({
								"attributeId": parseInt(selComponentsAttrib[0].attributeId),
								"attributeValue": rgbTohex($("#password"+compAttr).css("background-color")).toUpperCase() == uiConstants.common.UNCOMMON_VALUE_BGCOLOR ?
									($("#password"+compAttr).val()==""?selComponentsAttrib[0].attributeValue : $("#password"+compAttr).val()) : $("#password"+compAttr).val(),
								"componentAttributeMappingId": selComponentsAttrib[0].componentAttributeMappingId
							});
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
							/*if(self.compAttributesArr()[compAttr].isMandatory && $("#dropdown"+compAttr).val() == "" || $("#dropdown"+compAttr).val() == null || $("#dropdown"+compAttr).val().toUpperCase() == "uiConstants.common.SELECT"){
								self.errorMsg("Please select "+self.compAttributesArr()[compAttr].attributeName)
								break;
							}*/

							if(self.compAttributesArr()[compAttr].isMandatory && $("#dropdown"+compAttr +" option:selected").text() == uiConstants.common.SELECT){
								//self.errorMsg("Please select value for attribute: "+self.compAttributesArr()[compAttr].attributeName)
								
								self.expandPanelOnError("#attribsPanel", "#imgAttribShowHide");
								showError("#dropdown"+compAttr+"_chosen", "Please select value");
								showError("#dropdown"+compAttr+"_chosen span", "Please select value");
							    self.errorMsg("#attribsPanel");

								if(isAttribErrorExists == 0){
								    $("#attribsPanel").animate({
									    scrollTop: $("#dropdown").offset().top - $("#attribsPanel").offset().top + $("#attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							attributesList.push({
								"attributeId": parseInt(selComponentsAttrib[0].attributeId),
								"attributeValue": $("#dropdown"+compAttr).val()==null?selComponentsAttrib[0].attributeValue : $("#dropdown"+compAttr +" option:selected").text(),
								"componentAttributeMappingId": selComponentsAttrib[0].componentAttributeMappingId
							});
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
							attributesList.push({
								"attributeId": parseInt(selComponentsAttrib[0].attributeId),
								"attributeValue": $("#checkbox"+compAttr).prop("indeterminate") ? self.selectedConfigRows()[selConfig] : ($("#checkbox"+compAttr).prop('checked')?1:0),
								"componentAttributeMappingId": selComponentsAttrib[0].componentAttributeMappingId
							});
						}
        			}
				}
				
				removeError(".tokenfield");
				removeError("#compinstance-tokenfield-typeahead-tokenfield");
				if(containsDuplicate($("#compinstance-tokenfield-typeahead").val())){
					//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
					
					showError(".tokenfield", uiConstants.common.DUPLICATE_TAGS);
					showError("#compinstance-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
				    self.errorMsg(".tokenfield");
				}

				//mk self.kpiProducerCompInstMapArr(self.selectedConfigRows()[selConfig].kpiDetails);
				//mk self.kpiProducerCompInstMapArr(self.kpiProducerCompInstMapArr().concat(self.selectedConfigRows()[selConfig].kpiGroups));
				setKpiGroupValues();
				var selkpiProducerMap;
				var selKpiProdMappingArr;
				var selProducerId;

				for(kpi in self.kpiProducerMapArr()){
					if(rgbTohex($("#producer"+kpi).next(".chosen-container").css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#producer"+kpi+" option:selected").val() == "0"){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
						
						showError("#producer"+kpi+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						showError("#producer"+kpi+"_chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#producer"+kpi+"_chosen").offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
					    }
						isAdvSettingsErrorExists = 1;
					}
					else{
						removeError("#producer"+kpi+"_chosen");
						removeError("#producer"+kpi+"_chosen span");
					}

					if(rgbTohex($("#collInterval"+kpi).css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#collInterval"+kpi).val() == ""){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#collInterval"+kpi, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#collInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#collInterval"+kpi).val() != "" && $("#collIntervalUnit"+kpi).val() == null	){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#collIntervalUnit"+kpi+"_chosen", uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
						showError("#collIntervalUnit"+kpi+"_chosen span", uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#collIntervalUnit"+kpi+"_chosen").offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#collInterval"+kpi).val() == "" && $("#collIntervalUnit"+kpi).val() != null){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#collInterval"+kpi, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#collInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#collIntervalUnit"+kpi).val() == "seconds" &&
						parseInt($("#collInterval"+kpi).val()) != 15 && parseInt($("#collInterval"+kpi).val()) != 30){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#collInterval"+kpi, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#collInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					else if($("#collIntervalUnit"+kpi).val() == "minutes" &&
						(parseInt($("#collInterval"+kpi).val()) < 1 || parseInt($("#collInterval"+kpi).val()) >1440)){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#collInterval"+kpi, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#collInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}

					selKpiProdMappingArr = $.grep(self.multipleInstancesKpiProducerMapArr(), function(e){ return e.componentInstanceId == self.selectedConfigRows()[selConfig].componentInstanceId; })[0];
					selkpiProducerMap = $.grep(selKpiProdMappingArr.kpiDetails, function(e){ return e.kpiId == self.kpiProducerMapArr()[kpi].kpiId; });
					selProducerId = $("#producer"+kpi).val()==null?parseInt(selkpiProducerMap[0].producers.defaultId) : parseInt($("#producer"+kpi).val());

					if(selkpiProducerMap.length > 0){
						nonGroupkpiProdMapArr.push({
					        "kpiId": parseInt(self.kpiProducerMapArr()[kpi].kpiId),
				        	"componentInstanceKpiId": parseInt(self.kpiProducerMapArr()[kpi].componentInstanceKpiId),
				        	"producerId": selProducerId,
				       		"collectionInterval": $("#collInterval"+kpi).val() == "" ? selkpiProducerMap[0].collectionInterval : ($("#collIntervalUnit"+kpi).val() == "minutes" ? $("#collInterval"+kpi).val() * 60 : parseInt($("#collInterval"+kpi).val())),
				        	"kpiProducerMappingId": parseInt($.grep(selkpiProducerMap[0].producers.all, function(e){return e.producerId == selProducerId; })[0].kpiProducerMappingId),
				        	"status": $("#status"+kpi).prop("checked")?1:0
					    });
					}
				}

				var selkpiProducerMap;
				var selKpiProdMappingArr;
				var selProducerId;

				for(kpi in self.kpiGroupProducerMapArr()){
					if(rgbTohex($("#grpProducer"+kpi).next(".chosen-container").css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#grpProducer"+kpi+" option:selected").val() == "0"){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#grpProducer"+kpi+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						showError("#grpProducer"+kpi+"_chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpProducer"+kpi+"_chosen").offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
					    }
						isAdvSettingsErrorExists = 1;
					}
					else{
						removeError("#grpProducer"+kpi+"_chosen");
						removeError("#grpProducer"+kpi+"_chosen span");
					}

					if(rgbTohex($("#grpCollInterval"+kpi).css("background-color")).toUpperCase() != uiConstants.common.UNCOMMON_VALUE_BGCOLOR && $("#grpCollInterval"+kpi).val() == ""){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
						
						showError("#grpCollInterval"+kpi, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpCollInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#grpCollInterval"+kpi).val() != "" && $("#grpCollIntervalUnit"+kpi).val() == null	){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#grpCollIntervalUnit"+kpi+"_chosen", uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
						showError("#grpCollIntervalUnit"+kpi+"_chosen span", uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpCollIntervalUnit"+kpi+"_chosen").offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#grpCollInterval"+kpi).val() == "" && $("#grpCollIntervalUnit"+kpi).val() != null){
						self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
						
						showError("#grpCollInterval"+kpi, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpCollInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					if($("#grpCollIntervalUnit"+kpi).val() == "seconds" &&
						parseInt($("#grpCollInterval"+kpi).val()) != 15 && parseInt($("#grpCollInterval"+kpi).val()) != 30){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#grpCollInterval"+kpi, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpCollInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}
					else if($("#grpCollIntervalUnit"+kpi).val() == "minutes" &&
						(parseInt($("#grpCollInterval"+kpi).val()) < 1 || parseInt($("#grpCollInterval"+kpi).val()) >1440)){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
						
						self.expandPanelOnError("#advSettingsPanel", "#imgAdvSettingsShowHide");
							
						showError("#grpCollInterval"+kpi, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
					    self.errorMsg("#advSettingsPanel");

					    if(isAdvSettingsErrorExists == 0){
						    $("#advSettingsPanel").animate({
							    scrollTop: $("#grpCollInterval"+kpi).offset().top - $("#advSettingsPanel").offset().top + $("#advSettingsPanel").scrollTop() - 10
							});
						}
						isAdvSettingsErrorExists = 1;
					}

					var kpiGroupValObjArr = [];
					for(kpiVal in kpiGroupValArr[kpi]){
						/*if(kpiGroupValArr[kpi][kpiVal].kpiGroupValue.trim() == ""){
							self.errorMsg(uiConstants.componentInstanceConfig.ERROR_KPI_GROUP_VALUE_REQUIRED + ": " + self.kpiGroupProducerMapArr()[kpi].kpiGroupName);
							//setKpiGroup(kpi);
							break;
						}
						else if(!isRegexValid(self.kpiGroupProducerMapArr()[kpi].pattern, kpiGroupValArr[kpi][kpiVal].kpiGroupValue)){
							self.errorMsg("Regex "+self.kpiGroupProducerMapArr()[kpi].pattern+" for KPI Group: "+self.kpiGroupProducerMapArr()[kpi].kpiGroupName+" is invalid");
							break;
						}
						else if(self.kpiGroupProducerMapArr()[kpi].pattern && self.kpiGroupProducerMapArr()[kpi].pattern!="undefined" && kpiGroupValArr[kpi][kpiVal].kpiGroupValue != ((kpiGroupValArr[kpi][kpiVal].kpiGroupValue).match(self.kpiGroupProducerMapArr()[kpi].pattern) ? (kpiGroupValArr[kpi][kpiVal].kpiGroupValue).match(self.kpiGroupProducerMapArr()[kpi].pattern)[0] : null)){
							self.errorMsg("Value "+(parseInt(kpiVal)+1)+" in KPI Group: "+self.kpiGroupProducerMapArr()[kpi].kpiGroupName+" does not match the Regex pattern " + self.kpiGroupProducerMapArr()[kpi].pattern);
							break;
						}
						else{*/
							kpiGroupValObjArr.push(kpiGroupValArr[kpi][kpiVal].kpiGroupValue.trim());
							if(uiConstants.common.DEBUG_MODE)console.log(kpiGroupValObjArr);
						//}
					}

					selKpiProdMappingArr = $.grep(self.multipleInstancesKpiProducerMapArr(), function(e){ return e.componentInstanceId == self.selectedConfigRows()[selConfig].componentInstanceId; })[0];
					selkpiProducerMap = $.grep(selKpiProdMappingArr.kpiGroups, function(e){ return e.kpiGroupId == self.kpiGroupProducerMapArr()[kpi].kpiGroupId; });
					selProducerId = $("#grpProducer"+kpi).val()==null?parseInt(selkpiProducerMap[0].producers.defaultId) : parseInt($("#grpProducer"+kpi).val());

					if(selkpiProducerMap.length > 0){
						groupkpiProdMapArr.push({
					        "kpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpi].kpiGroupId),
					        "componentInstanceKpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpi].componentInstanceKpiGroupId),
					        "kpiGroupValues": kpiGroupValObjArr,
				        	"producerId": $("#grpProducer"+kpi).val()==null?parseInt(selkpiProducerMap[0].producers.defaultId) : parseInt($("#grpProducer"+kpi).val()),
				        	"collectionInterval": $("#grpCollInterval"+kpi).val() == "" ? selkpiProducerMap[0].collectionInterval : ($("#grpCollIntervalUnit"+kpi).val() == "minutes" ? $("#grpCollInterval"+kpi).val() * 60 : parseInt($("#grpCollInterval"+kpi).val())),
				        	"kpiProducerMappingId": parseInt($.grep(selkpiProducerMap[0].producers.all, function(e){return e.producerId == selProducerId; })[0].kpiProducerMappingId),
				        	"status": $("#grpStatus"+kpi).prop("checked")?1:0
					    });
					}
				}

				if(self.errorMsg() == ""){

					tagsArr = [];
					if(self.tags() && self.tags().trim().length == 1){
						tagsArr.push(self.tags());
					}

					else if(self.tags() && self.tags().trim().length > 1){
						tagsArr = self.tags().split(",");
					}

					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, 
								"tagName":tagsArr[tag].trim(), 
								"tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), 
								"tagName":tagsArr[tag].trim(),
								"tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), 
							"tagName":tagsToDeleteArr[tag].trim(), 
							"tagOperation":"delete"});
					}



					for(var tag in self.selectedConfigRows()[selConfig].tags){
						if(tagsToDeleteArr.indexOf(self.selectedConfigRows()[selConfig].tags[tag].tagName.trim()) == -1 && tagsArr.indexOf(self.selectedConfigRows()[selConfig].tags[tag].tagName.trim()) == -1){
							tagsObjArr.push({
								"tagId":self.selectedConfigRows()[selConfig].tags[tag].tagId,
								"tagName":self.selectedConfigRows()[selConfig].tags[tag].tagName.trim(), 
								"tagOperation":"none"});
						}
					}

					compObjArr.push({
					"index":indexCounter++,
					"componentInstanceId": self.selectedConfigRows()[selConfig].componentInstanceId,
					"componentInstanceName": self.selectedConfigRows()[selConfig].componentInstanceName,
					//"description": self.selectedConfigRows()[selConfig].description,
					"componentTypeId": self.selectedConfigRows()[selConfig].componentTypeId,
					"componentId": self.selectedConfigRows()[selConfig].componentId,
					"componentVersionId":  self.selectedConfigRows()[selConfig].componentVersionId,
					//"isHost": slugify(self.selectedCompType()) == "host",
					//"hostDetails": hostDet,
					"attributes": attributesList, 
					"applicationIds": appsArr,
					"clusterIds": clustersArr,
					"tags": tagsObjArr,
					"kpiDetails": nonGroupkpiProdMapArr,
					"kpiGroups": groupkpiProdMapArr,
					"status" : statusFlag});
				}
        	}

        	if(self.errorMsg() == ""){
				configData = {"componentInstances":compObjArr, "isAddMultiple":1};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configData));
				requestCall(uiConstants.common.SERVER_IP + "/componentInstance", "PUT", JSON.stringify(configData), "editConfig", successCallback, errorCallback);
			}
        }

        this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

        this.passwordChangeHandler = function(index){
        	if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
	        	if($('#'+self.modalCompType()+'password'+index).val() != ''){
				    showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_PASSWORD_RESET, "question", "confirm", function confirmCallback(confirmReset){
						if (confirmReset) {
							$('#'+self.modalCompType()+'password'+index).val("");
						}
						else{
							$('#'+self.modalCompType()+'password'+index).blur();
						}
					});
	        	}
        	}
        }

        this.expandPanelOnError = function(panel, img){
        	if($(panel).is(":hidden")){
				self.expandCollapsePanel(panel, img);
			}
        }

		this.addEditConfig = function(){
			if(self.selectedConfigRows().length > 1){
				self.updateMultipleConfigs();
			}
			else{
				self.errorMsg("");
				self.firstFieldToShowErr("");
				removeError();

				var tagsArr = [];
				var tagsObjArr = [];
				var configData;
				var hostAddress = "";
				var isAdvSettingsErrorExists = 0;
				var isAttribErrorExists = 0;

				//$("#divCompInstDescription #"+self.modalCompType()+"txtDescription").val($("#divCompInstDescription #"+self.modalCompType()+"txtDescription").val().trim());
				$("#"+self.modalCompType()+"txtName").val($("#"+self.modalCompType()+"txtName").val().trim());
				var attributesList = [];
				var nonGroupkpiProdMapArr = [];
				var groupkpiProdMapArr = [];

				if($("#"+self.modalCompType()+"txtName").val().length < 2){
					//self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MIN_LENGTH_ERROR);
					showError("#"+self.modalCompType()+"txtName", uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MIN_LENGTH_ERROR);
			    	self.errorMsg("#"+self.modalCompType()+"txtName");
				}
				else if($("#"+self.modalCompType()+"txtName").val().length > 45){
					//self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MAX_LENGTH_ERROR);
					showError("#"+self.modalCompType()+"txtName", uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MAX_LENGTH_ERROR);
			    	self.errorMsg("#"+self.modalCompType()+"txtName");
				}
				else if(!nameValidation($("#"+self.modalCompType()+"txtName").val())){
					//self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_INVALID_ERROR);
					showError("#"+self.modalCompType()+"txtName", uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_INVALID_ERROR);
			    	self.errorMsg("#"+self.modalCompType()+"txtName");
				}
				/*else if($("#divCompInstDescription #"+self.modalCompType()+"txtDescription").val() == ""){
					self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				}
				else if($("#divCompInstDescription #"+self.modalCompType()+"txtDescription").val().length < 25){
					self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				}
				else if($("#divCompInstDescription #"+self.modalCompType()+"txtDescription").val().length > 256){
					self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				}*/
				if($("#"+self.modalCompType()+"compTypeList").val() == 0){
					//self.errorMsg(uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
					showError("#"+self.modalCompType()+"compTypeList_chosen", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
					showError("#"+self.modalCompType()+"compTypeList_chosen span", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				    self.errorMsg("#"+self.modalCompType()+"compTypeList_chosen");
				}
				else{
					removeError("#"+self.modalCompType()+"compTypeList_chosen");
					removeError("#"+self.modalCompType()+"compTypeList_chosen span");
				}
				
				if($("#"+self.modalCompType()+"compNamesList").val() == 0){
					//self.errorMsg(uiConstants.common.SELECT_COMPONENT_MSG);
					showError("#"+self.modalCompType()+"compNamesList_chosen", uiConstants.common.SELECT_COMPONENT_MSG);
					showError("#"+self.modalCompType()+"compNamesList_chosen span", uiConstants.common.SELECT_COMPONENT_MSG);
				    self.errorMsg("#"+self.modalCompType()+"compNamesList_chosen");
				}
				else{
					removeError("#"+self.modalCompType()+"compNamesList_chosen");
					removeError("#"+self.modalCompType()+"compNamesList_chosen span");
				}

				if($("#"+self.modalCompType()+"compVersionsList").val() == 0){
					//self.errorMsg(uiConstants.common.SELECT_COMPONENT_VERSION_MSG);
					showError("#"+self.modalCompType()+"compVersionsList_chosen", uiConstants.common.SELECT_COMPONENT_VERSION_MSG);
					showError("#"+self.modalCompType()+"compVersionsList_chosen span", uiConstants.common.SELECT_COMPONENT_VERSION_MSG);
				    self.errorMsg("#"+self.modalCompType()+"compVersionsList_chosen");
				}
				else{
					removeError("#"+self.modalCompType()+"compVersionsList_chosen");
					removeError("#"+self.modalCompType()+"compVersionsList_chosen span");
				}
				
				if(self.isAppMandatory() && $('#'+self.modalCompType()+'selectedApplicationList').getAllValues().length == 0){
					//self.errorMsg(uiConstants.common.SELECT_APPLICATION_MULT_OPTION_MSG);
					showError("#"+self.modalCompType()+"selectedApplicationList", uiConstants.common.SELECT_APPLICATION_MULT_OPTION_MSG);
				    self.errorMsg("#"+self.modalCompType()+"selectedApplicationList");
				}
				
				removeError("."+self.modalCompType()+"tokenfield");
				removeError("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield");
				if(containsDuplicate($("#"+self.modalCompType()+"compinstance-tokenfield-typeahead").val())){
					//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
					showError("."+self.modalCompType()+"tokenfield", uiConstants.common.DUPLICATE_TAGS);
					showError("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
				    self.errorMsg("."+self.modalCompType()+"tokenfield");
				}
				else{
					if(self.tags() && self.tags().trim().length == 1)
						tagsArr.push(self.tags());

					else if(self.tags() && self.tags().trim().length > 1)
						tagsArr = self.tags().split(",");
					
					for(var t in tagsArr){
						if(tagsArr[t].trim().length < 2){
							//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("."+self.modalCompType()+"tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("."+self.modalCompType()+"tokenfield");
							break;
						}
						else if(tagsArr[t].trim().length > 45){
							//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("."+self.modalCompType()+"tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						    self.errorMsg("."+self.modalCompType()+"tokenfield");
							break;
						}
						else if(!tagValidation(tagsArr[t].trim())){
							//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
							showError("."+self.modalCompType()+"tokenfield", uiConstants.common.INVALID_TAG_NAME);
							showError("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
						    self.errorMsg("."+self.modalCompType()+"tokenfield");
							break;
						}
					}

					removeError("#"+self.modalCompType()+"advSettingsPanel input");
					for(kpiIndex in self.kpiProducerMapArr()){
						if($("#"+self.modalCompType()+"producer"+kpiIndex+" option:selected").val() == "0"){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"producer"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
							showError("#"+self.modalCompType()+"producer"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"producer"+kpiIndex+"_chosen").offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
						    }
							isAdvSettingsErrorExists = 1;
						}
						else{
							removeError("#"+self.modalCompType()+"producer"+kpiIndex+"_chosen");
							removeError("#"+self.modalCompType()+"producer"+kpiIndex+"_chosen span");
						}

						if($("#"+self.modalCompType()+"collInterval"+kpiIndex).val() == ""){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"collInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}
						else if($("#"+self.modalCompType()+"collIntervalUnit"+kpiIndex).val() == "seconds" &&
							parseInt($("#"+self.modalCompType()+"collInterval"+kpiIndex).val()) != 15 && parseInt($("#"+self.modalCompType()+"collInterval"+kpiIndex).val()) != 30){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
							
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"collInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}
						else if($("#"+self.modalCompType()+"collIntervalUnit"+kpiIndex).val() == "minutes" &&
							(parseInt($("#"+self.modalCompType()+"collInterval"+kpiIndex).val()) < 1 || parseInt($("#"+self.modalCompType()+"collInterval"+kpiIndex).val()) >1440)){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
							
							self.expandPanelOnError("#"+self.modalCompType()+"	advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"collInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}

						if(self.kpiProducerMapArr()[kpiIndex].kpiId){
							if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProducerMapArr()[kpiIndex].producers.all);
							if(uiConstants.common.DEBUG_MODE)console.log($.grep(self.kpiProducerMapArr()[kpiIndex].producers.all, function(e){return e.producerId == $("#"+self.modalCompType()+"producer"+kpiIndex+" option:selected").val(); })[0]);

							nonGroupkpiProdMapArr.push({
						        "kpiId": parseInt(self.kpiProducerMapArr()[kpiIndex].kpiId),
						        "componentInstanceKpiId": parseInt(self.kpiProducerMapArr()[kpiIndex].componentInstanceKpiId),
						        "producerId": parseInt($("#"+self.modalCompType()+"producer"+kpiIndex+" option:selected").val()),
						        "collectionInterval": $("#"+self.modalCompType()+"collIntervalUnit"+kpiIndex).val() == "minutes" ? $("#"+self.modalCompType()+"collInterval"+kpiIndex).val() * 60 : parseInt($("#"+self.modalCompType()+"collInterval"+kpiIndex).val()),
						        "kpiProducerMappingId": $.grep(self.kpiProducerMapArr()[kpiIndex].producers.all, function(e){return e.producerId == $("#"+self.modalCompType()+"producer"+kpiIndex+" option:selected").val(); })[0].kpiProducerMappingId,
				        		"status": $("#"+self.modalCompType()+"status"+kpiIndex).prop("checked")?1:0
						    });
						}
					}

					for(kpiIndex in self.kpiGroupProducerMapArr()){
						if($("#"+self.modalCompType()+"grpProducer"+kpiIndex+" option:selected").val() == "0"){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"grpProducer"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
							showError("#"+self.modalCompType()+"grpProducer"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"grpProducer"+kpiIndex+"_chosen").offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
						    }
							isAdvSettingsErrorExists = 1;
						}
						else{
							removeError("#"+self.modalCompType()+"grpProducer"+kpiIndex+"_chosen");
							removeError("#"+self.modalCompType()+"grpProducer"+kpiIndex+"_chosen span");
						}

						if($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val() == ""){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"grpCollInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}
						else if($("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiIndex).val() == "seconds" &&
							parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val()) != 15 && parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val()) != 30){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"grpCollInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}
						else if($("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiIndex).val() == "minutes" &&
							(parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val()) < 1 || parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val()) >1440)){
							//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
							self.expandPanelOnError("#"+self.modalCompType()+"advSettingsPanel", "#"+self.modalCompType()+"imgAdvSettingsShowHide");
							
							showError("#"+self.modalCompType()+"grpCollInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
						    self.errorMsg("#"+self.modalCompType()+"advSettingsPanel");

						    if(isAdvSettingsErrorExists == 0){
							    $("#"+self.modalCompType()+"advSettingsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
								});
							}
							isAdvSettingsErrorExists = 1;
						}

						if(self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupId){
							var kpiGroupValObjArr = [];
							for(kpiVal in kpiGroupValArr[kpiIndex]){

								//kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue = kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue.replace("\\","\\\\");
							//	alert((kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue).match(new RegExp(self.kpiGroupProducerMapArr()[kpiIndex].pattern, "g")).join(""));
								if(kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue.trim() == ""){
									//self.errorMsg(uiConstants.componentInstanceConfig.ERROR_KPI_GROUP_VALUE_REQUIRED + ": " + self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupName);
									//setKpiGroup(kpi);
									//break;

									showError("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal, uiConstants.componentInstanceConfig.ERROR_KPI_GROUP_VALUE_REQUIRED);
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal);

			    					if(isAdvSettingsErrorExists == 0){
									    $("#"+self.modalCompType()+"advSettingsPanel").animate({
										    scrollTop: $("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
										});
									}
									isAdvSettingsErrorExists = 1;
								}
								else if(!isRegexValid(self.kpiGroupProducerMapArr()[kpiIndex].pattern, kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue)){
									//self.errorMsg("Regex "+self.kpiGroupProducerMapArr()[kpiIndex].pattern+" for KPI Group: "+self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupName+" is invalid");
									showError("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal, "Regex "+self.kpiGroupProducerMapArr()[kpiIndex].pattern+" is invalid");
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal);

			    					if(isAdvSettingsErrorExists == 0){
									    $("#"+self.modalCompType()+"advSettingsPanel").animate({
										    scrollTop: $("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
										});
									}
									isAdvSettingsErrorExists = 1;
								}
								else if(self.kpiGroupProducerMapArr()[kpiIndex].pattern && self.kpiGroupProducerMapArr()[kpiIndex].pattern!="undefined" && kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue != ((kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue).match(new RegExp(self.kpiGroupProducerMapArr()[kpiIndex].pattern, "g")).join(""))){
									//self.errorMsg("Value "+(parseInt(kpiVal)+1)+" in KPI Group: "+self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupName+" does not match the Regex pattern " + self.kpiGroupProducerMapArr()[kpiIndex].pattern);
									showError("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal, "Value does not match the Regex pattern " + self.kpiGroupProducerMapArr()[kpiIndex].pattern);
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal);

			    					if(isAdvSettingsErrorExists == 0){
									    $("#"+self.modalCompType()+"advSettingsPanel").animate({
										    scrollTop: $("#"+self.modalCompType()+"kpiGroupVal"+kpiIndex+kpiVal).offset().top - $("#"+self.modalCompType()+"advSettingsPanel").offset().top + $("#"+self.modalCompType()+"advSettingsPanel").scrollTop() - 10
										});
									}
									isAdvSettingsErrorExists = 1;
								}
								else{
									kpiGroupValObjArr.push(kpiGroupValArr[kpiIndex][kpiVal].kpiGroupValue.trim());
									if(uiConstants.common.DEBUG_MODE)console.log(kpiGroupValObjArr);
								}
							}

							groupkpiProdMapArr.push({
						        "kpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpiIndex].kpiGroupId),
						        "componentInstanceKpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpiIndex].componentInstanceKpiGroupId),
						        "kpiGroupValues": kpiGroupValObjArr,
						        "producerId": parseInt($("#"+self.modalCompType()+"grpProducer"+kpiIndex+" option:selected").val()),
						        "collectionInterval": $("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiIndex).val() == "minutes" ? $("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val() * 60 : parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpiIndex).val()),
				        		"kpiProducerMappingId": $.grep(self.kpiGroupProducerMapArr()[kpiIndex].producers.all, function(e){ return e.producerId == $("#"+self.modalCompType()+"grpProducer"+kpiIndex+" option:selected").val(); })[0].kpiProducerMappingId,
				        		"status": $("#"+self.modalCompType()+"grpStatus"+kpiIndex).prop("checked")?1:0
						    });

							
						}
					}

					removeError("#"+self.modalCompType()+"attribsPanel input");
					removeError("#"+self.modalCompType()+"attribsPanel select");
					removeError("#hostsList_chosen");
					removeError("#hostsList_chosen span");
					if(slugify(self.selectedCompType()) != "host"){
						if(self.hostAddressAttribId() != 0){
							if($("#hostsList_chosen span")[0].innerHTML == uiConstants.common.SELECT_HOST){
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								
								//self.errorMsg(uiConstants.common.SELECT_HOST_MSG);
								showError("#hostsList_chosen", uiConstants.common.SELECT_HOST_MSG);
								showError("#hostsList_chosen span", uiConstants.common.SELECT_HOST_MSG);
							    self.errorMsg("#hostsList_chosen");

							    $("#"+self.modalCompType()+"attribsPanel").animate({
								    scrollTop: $("#"+self.modalCompType()+"hostsList_chosen").offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
								});

								isAttribErrorExists = 1;
							}
							else{
								hostAddress = $("#hostsList_chosen span")[0].innerHTML.split("(")[1].split(")")[0];
							}
						}
					}
					else{
						hostAddress = self.hostAddress();
						if(!self.hostAddress()){
							//self.errorMsg("Please enter value for attribute: Host Address");

							self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
							
							showError("#txtHostAddress", "Please enter value");
						    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

						    $("#"+self.modalCompType()+"attribsPanel").animate({
							    scrollTop: $("#txtHostAddress").offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
							});

							isAttribErrorExists = 1;
						}
					}


					if(self.hostAddressAttribId() != 0){
						attributesList.push({
							"attributeId": self.hostAddressAttribId(),
							"attributeValue": hostAddress,
							"componentAttributeMappingId": hostAddressAttribMappingId
						});
					}

					var txtPwd="";
					for(compAttr in self.compAttributesArr()){
						if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' || self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
							txtPwd = self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#"+self.modalCompType()+"txtbox"+compAttr : "#"+self.modalCompType()+"password"+compAttr;

							$(txtPwd).val($(txtPwd).val().trim());
							var cellValue=$(txtPwd).val();
							

							if(self.compAttributesArr()[compAttr].isMandatory && cellValue == ""){
								//self.errorMsg("Please enter value for attribute: "+self.compAttributesArr()[compAttr].attributeName)

								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr, "Please enter value");
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

						    	if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr).offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else if(cellValue && self.compAttributesArr()[compAttr].attributeMinLength && self.compAttributesArr()[compAttr].attributeMinLength!="undefined" && cellValue.length < parseInt(self.compAttributesArr()[compAttr].attributeMinLength)){
								//self.errorMsg("Value for "+self.compAttributesArr()[compAttr].attributeName+" should be of minimum " + self.compAttributesArr()[compAttr].attributeMinLength + " characters length");
								
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr, "Value should be of minimum " + self.compAttributesArr()[compAttr].attributeMinLength + " characters length");
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

							    if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr).offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else if(cellValue && self.compAttributesArr()[compAttr].attributeMaxLength && self.compAttributesArr()[compAttr].attributeMaxLength != "undefined" && cellValue.length > parseInt(self.compAttributesArr()[compAttr].attributeMaxLength)){
								//self.errorMsg("Value for "+self.compAttributesArr()[compAttr].attributeName+" should be of maximum " + self.compAttributesArr()[compAttr].attributeMaxLength + " characters length");
								
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr, "Value should be of maximum " + self.compAttributesArr()[compAttr].attributeMaxLength + " characters length");
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

							    if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr).offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else if(!isRegexValid(self.compAttributesArr()[compAttr].attributeRegEx, cellValue)){
								//self.errorMsg("Regex "+self.compAttributesArr()[compAttr].attributeRegEx+" for "+self.compAttributesArr()[compAttr].attributeName+" is invalid");
								
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr, "Regex "+self.compAttributesArr()[compAttr].attributeRegEx+" is invalid");
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

							    if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr).offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else if(cellValue && self.compAttributesArr()[compAttr].attributeRegEx && self.compAttributesArr()[compAttr].attributeRegEx!="undefined" && cellValue != (cellValue.match(new RegExp(self.compAttributesArr()[compAttr].attributeRegEx), "g") ? cellValue.match(self.compAttributesArr()[compAttr].attributeRegEx)[0] : null)){
								//self.errorMsg("Value for "+self.compAttributesArr()[compAttr].attributeName+" does not match the Regex pattern " + self.compAttributesArr()[compAttr].attributeRegEx);
								
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr, "Value does not match the Regex pattern " + self.compAttributesArr()[compAttr].attributeRegEx);
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

								if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX' ? "#txtbox"+compAttr : "#password"+compAttr).offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}

							if(self.errorMsg() == ""){
								attributesList.push({
									"attributeId": self.compAttributesArr()[compAttr].attributeId,
									"attributeValue": $(txtPwd).val(),
									"componentAttributeMappingId": self.compAttributesArr()[compAttr].componentAttributeMappingId
								});
							}
						}
						/*else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
							if(self.compAttributesArr()[compAttr].isMandatory && $("#"+self.modalCompType()+"password"+compAttr).val() == ""){
								self.errorMsg("Please enter value for attribute: "+self.compAttributesArr()[compAttr].attributeName)
								break;
							}
							attributesList.push({
								"attributeId": self.compAttributesArr()[compAttr].attributeId,
								"attributeValue": $("#"+self.modalCompType()+"password"+compAttr).val(),
								"componentAttributeMappingId": self.compAttributesArr()[compAttr].componentAttributeMappingId
							});
						}*/
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
							if(self.compAttributesArr()[compAttr].isMandatory && ($("#"+self.modalCompType()+"dropdown"+compAttr).val() == "" || $("#"+self.modalCompType()+"dropdown"+compAttr).val() == undefined || $("#"+self.modalCompType()+"dropdown"+compAttr).val().toUpperCase() == "uiConstants.common.SELECT")){
								//self.errorMsg("Please select value for attribute: "+self.compAttributesArr()[compAttr].attributeName)
								
								self.expandPanelOnError("#"+self.modalCompType()+"attribsPanel", "#"+self.modalCompType()+"imgAttribShowHide");
								showError("#dropdown"+compAttr+"_chosen", "Please select value");
								showError("#dropdown"+compAttr+"_chosen span", "Please select value");
							    self.errorMsg("#"+self.modalCompType()+"attribsPanel");

								if(isAttribErrorExists == 0){
								    $("#"+self.modalCompType()+"attribsPanel").animate({
									    scrollTop: $("#dropdown").offset().top - $("#"+self.modalCompType()+"attribsPanel").offset().top + $("#"+self.modalCompType()+"attribsPanel").scrollTop() - 10
									});
								}
								isAttribErrorExists = 1;
							}
							else{
								attributesList.push({
									"attributeId": self.compAttributesArr()[compAttr].attributeId,
									"attributeValue": $("#"+self.modalCompType()+"dropdown"+compAttr+" option:selected").text(),
									"componentAttributeMappingId": self.compAttributesArr()[compAttr].componentAttributeMappingId
								});
							}
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
							attributesList.push({
								"attributeId": self.compAttributesArr()[compAttr].attributeId,
								"attributeValue": $("#"+self.modalCompType()+"checkbox"+compAttr).prop('checked')?1:0,
								"componentAttributeMappingId": self.compAttributesArr()[compAttr].componentAttributeMappingId
							});
						}
					}

					/*for(kpi in self.kpiProducerMapArr()){
						

						
					}
*/
					/*for(kpi in self.kpiGroupProducerMapArr()){
						if(self.kpiGroupProducerMapArr()[kpi].kpiGroupId){
							var kpiGroupValObjArr = [];
							for(kpiVal in kpiGroupValArr[kpi]){
								if(kpiGroupValArr[kpi][kpiVal].kpiGroupValue.trim() == ""){
									self.errorMsg(uiConstants.componentInstanceConfig.ERROR_KPI_GROUP_VALUE_REQUIRED + ": " + self.kpiGroupProducerMapArr()[kpi].kpiGroupName);
									//setKpiGroup(kpi);
									//break;

									showError("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal, uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MIN_LENGTH_ERROR);
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal);
								}
								else if(!isRegexValid(self.kpiGroupProducerMapArr()[kpi].pattern, kpiGroupValArr[kpi][kpiVal].kpiGroupValue)){
									//self.errorMsg("Regex "+self.kpiGroupProducerMapArr()[kpi].pattern+" for KPI Group: "+self.kpiGroupProducerMapArr()[kpi].kpiGroupName+" is invalid");
									showError("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal, "Regex "+self.kpiGroupProducerMapArr()[kpi].pattern+" is invalid");
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal);
								}
								else if(self.kpiGroupProducerMapArr()[kpi].pattern && self.kpiGroupProducerMapArr()[kpi].pattern!="undefined" && kpiGroupValArr[kpi][kpiVal].kpiGroupValue != ((kpiGroupValArr[kpi][kpiVal].kpiGroupValue).match(self.kpiGroupProducerMapArr()[kpi].pattern) ? (kpiGroupValArr[kpi][kpiVal].kpiGroupValue).match(self.kpiGroupProducerMapArr()[kpi].pattern)[0] : null)){
									//self.errorMsg("Value "+(parseInt(kpiVal)+1)+" in KPI Group: "+self.kpiGroupProducerMapArr()[kpi].kpiGroupName+" does not match the Regex pattern " + self.kpiGroupProducerMapArr()[kpi].pattern);
									showError("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal, "Value does not match the Regex pattern " + self.kpiGroupProducerMapArr()[kpi].pattern);
			    					self.errorMsg("#"+self.modalCompType()+"kpiGroupVal"+kpi+kpiVal);
								}
								else{
									kpiGroupValObjArr.push(kpiGroupValArr[kpi][kpiVal].kpiGroupValue.trim());
									if(uiConstants.common.DEBUG_MODE)console.log(kpiGroupValObjArr);
								}
							}

							groupkpiProdMapArr.push({
						        "kpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpi].kpiGroupId),
						        "componentInstanceKpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpi].componentInstanceKpiGroupId),
						        "kpiGroupValues": kpiGroupValObjArr,
						        "producerId": parseInt($("#"+self.modalCompType()+"grpProducer"+kpi+" option:selected").val()),
						        "collectionInterval": $("#"+self.modalCompType()+"grpCollIntervalUnit"+kpi).val() == "minutes" ? $("#"+self.modalCompType()+"grpCollInterval"+kpi).val() * 60 : parseInt($("#"+self.modalCompType()+"grpCollInterval"+kpi).val()),
				        		"kpiProducerMappingId": $.grep(self.kpiGroupProducerMapArr()[kpi].producers.all, function(e){ return e.producerId == $("#"+self.modalCompType()+"grpProducer"+kpi+" option:selected").val(); })[0].kpiProducerMappingId,
				        		"status": $("#"+self.modalCompType()+"grpStatus"+kpi).prop("checked")?1:0
						    });

							
						}

					}*/

					if(self.errorMsg() == ""){
						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":0, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}

						var configObjArr = [{
							"index":1,
							"componentInstanceId": self.configId(),
							"componentInstanceName": self.configName(),
							//"description": self.description(),
							"componentTypeId": parseInt($("#"+self.modalCompType()+"compTypeList").val()),
							"componentId":  parseInt($("#"+self.modalCompType()+"compNamesList").val()),
							"componentVersionId":  parseInt($("#"+self.modalCompType()+"compVersionsList").val()),
							//"isHost": slugify(self.selectedCompType()) == "host",
							//"hostDetails": slugify(self.selectedCompType()) != "host" ? $("#hostsList_chosen span")[0].innerHTML : self.hostAddress(),
							"attributes": attributesList, 
							"applicationIds": $("#"+self.modalCompType()+"selectedApplicationList").getAllValues().map(function (x){return parseInt(x);}),
							"clusterIds": $("#"+self.modalCompType()+"clusterNamesList").val() != "0" ? [parseInt($("#"+self.modalCompType()+"clusterNamesList").val())] : [],
							"tags": tagsObjArr,
							"kpiDetails": nonGroupkpiProdMapArr,
							"kpiGroups": groupkpiProdMapArr,
							"status" : self.configStatus()?1:0}];

						configData = {"componentInstances":configObjArr};

						if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configData));

						if(self.configId() == 0)
							requestCall(uiConstants.common.SERVER_IP + "/componentInstance", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
						else
							requestCall(uiConstants.common.SERVER_IP + "/componentInstance", "PUT", JSON.stringify(configData), "editConfig", successCallback, errorCallback);
					}
				}
			}
		}

		function editSingleConfig(configObj){
			if(configObj[0].status == 0){
				self.enableConfig(false);
			}
			setConfigValues(configObj);

			if($("#attribsPanel").is(":hidden")){
	    		self.expandCollapsePanel("#attribsPanel", "#imgAttribShowHide");
	    	}
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			self.enableConfig(false);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			//$('#txtDescription').prop('readonly', true);
			$('#compinstance-tokenfield-typeahead').tokenfield('readonly');
			$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
			$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
			$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");
			$("#"+self.modalCompType()+"clusterNamesList").prop('disabled', true).trigger("chosen:updated");
			$("#appDiv").find("input,button,select").attr("disabled", "disabled");
			$("#availableApplicationList").addClass("checklist-disabled");
			$("#selectedApplicationList").addClass("checklist-disabled");


			$("#kpiProducerMappingList").find("input,button,select").attr("disabled", "disabled");
			$("#grpKpiProducerMappingList").find("input,button,select").attr("disabled", "disabled");
			//$("#saveBtn").hide();

			if(self.selectedCompType() == "Host"){
				$("#txtHostAddress").prop('readonly', true);
			}
			else{
				$('#hostsList').prop('disabled', true).trigger("chosen:updated");
			}

			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='"+self.modalCompType()+"configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divCompInstAddEdit .chosen-container b").css("display", "none");
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				//self.description("");
			}
			else{
				self.configId(configObj[0].componentInstanceId);
				self.configName(configObj[0].componentInstanceName);
				//self.description(configObj[0].description);
			}

			if($.grep(self.componentsArr(), function(e){ return  e.componentTypeId == configObj[0].componentTypeId; }).length){
				$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
			}
			else{
				$("#compTypeList").val("0").trigger('chosen:updated');
			}

			setCompNames();
			if($.grep(self.componentNamesArr(), function(e){ return  e.componentId == configObj[0].componentId; }).length){
				$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
			}
			else{
				$("#compNamesList").val("0").trigger('chosen:updated');
			}
			/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(self.componentNamesArr());
				if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === configObj[0].componentId;} )){
					self.componentNamesArr.push({
						"componentId": configObj[0].componentId,
						"componentName": configObj[0].componentName,
						"isActive": false
					});
					$("#compNamesList_chosen span").first().addClass("inactiveOptionClass");
				}
				
				$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
			}
			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === configObj[0].componentId;} )){
					$("#compNamesList").val("0").trigger('chosen:updated');
				}
				else{
					$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
				}
			}*/

			setCompVersions();

			if($.grep(self.componentVersionsArr(), function(e){ return  e.versionId == configObj[0].componentVersionId; }).length){
				$("#compVersionsList").val(configObj[0].componentVersionId).trigger('chosen:updated');
			}
			else{
				$("#compVersionsList").val("0").trigger('chosen:updated');
			}
			/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(!self.componentVersionsArr().find( function( ele ) {return ele.versionId && ele.versionId === configObj[0].componentVersionId;} )){
					self.componentVersionsArr.push({
						"versionId": configObj[0].componentVersionId,
						"version": configObj[0].componentVersion,
						"isActive": false
					});
					$("#compVersionsList_chosen span").first().addClass("inactiveOptionClass");
				}
				$("#compVersionsList").val(configObj[0].componentVersionId).trigger('chosen:updated');
			}
			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(self.componentVersionsArr());
				if(jQuery.isEmptyObject(self.componentVersionsArr()) || !self.componentVersionsArr().find( function( ele ) {return ele.versionId && ele.versionId ===configObj[0].componentVersionId;} )){
					$("#compVersionsList").val("0").trigger('chosen:updated');
				}
				else{
					$("#compVersionsList").val(configObj[0].componentVersionId).trigger('chosen:updated');
				}
			}*/

			if(self.selectedCompType() == "Host" && self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
				self.hostAddress(configObj[0].hostAddress);
			}
			else{
				$("#hostsList option:contains("+configObj[0].hostName + ' (' + configObj[0].hostAddress + ')'+")").attr('selected', 'selected');
	        	$('#hostsList').trigger("chosen:updated");
			}

			setCompAttributes();
			setClusters();


			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				for(selApp in configObj[0].applications){
					if(!self.applicationsArr().find( function( ele ) {return ele.applicationId && ele.applicationId === configObj[0].applications[selApp].applicationId;} )){
						
						self.availableApplicationArr.push({
							"id": configObj[0].applications[selApp].applicationId,
							"name": configObj[0].applications[selApp].applicationName,
							"isActive": false
						});

						self.applicationsArr.push(self.availableApplicationArr()[self.availableApplicationArr().length]);
					}
				}

				sortArrayObjByValue(self.availableApplicationArr(), "name");
			}

			$("#availableApplicationList").checklistbox({
	            data: self.availableApplicationArr()
	        });

	       /* $('#availableApplicationList').checklistbox({
				    data: self.availableApplicationArr()
				});

				$('#selectedApplicationList').checklistbox({
				    data: self.selectedApplicationArr()
				});*/




	        /*for(appId in configObj[0].applications){
				$("#applicationsList .checkList[value=" + configObj[0].applications[appId].applicationId + "]").prop("checked",true);
			}
			$("#selAllApps").prop("checked", self.applicationsArr().length == $("#applicationsList .checkList:checked").length);
*/
			self.configStatus(configObj[0].status);
			$('#'+self.modalCompType()+'configStatus').bootstrapSwitch('state',self.configStatus());
			$('#compinstance-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
		}

		this.cancelConfig = function(){
			if(self.modalCompType() && self.modalCompType() == "hostModal"){
				$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Component Instance Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function setCompNames(){
			self.selectedCompType($("#"+self.modalCompType()+"compTypeList option:selected").text());

			if($("#"+self.modalCompType()+"compTypeList").val() == 0){
				self.componentNamesArr({});
				$('#'+self.modalCompType()+'compNamesList').prop('disabled', true).trigger("chosen:updated");
			}
			else{
				if($("#"+self.modalCompType()+"compTypeList option:selected").text().toUpperCase() == 'HOST'){
					self.isAppMandatory(false);
				}
				else{
					self.isAppMandatory(true);
				}
				var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#"+self.modalCompType()+"compTypeList").val(); });

				if(componentsObj[0] && componentsObj[0].components && componentsObj[0].components.length){
					/*if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
					self.componentNamesArr(componentsObj[0].components);*/

					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						self.componentNamesArr(getMasterList(componentsObj[0].components, "componentId", [self.selectedConfigRows()[0].componentId], true));
					}
					else{
						self.componentNamesArr(getMasterList(componentsObj[0].components, "componentId", null, false));
					}
				}
				else{
					self.componentNamesArr({});
				}

				if(self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
					$('#'+self.modalCompType()+'compNamesList').prop('disabled', false).trigger("chosen:updated");
				}
			}
		}

		function setCompVersions(){
			if($("#"+self.modalCompType()+"compNamesList").val() == 0){
				self.componentVersionsArr({});
				$('#'+self.modalCompType()+'compVersionsList').prop('disabled', true).trigger("chosen:updated");
			}
			else{
				var componentsObj = $.grep(self.componentNamesArr(), function(e){ return e.componentId == $("#"+self.modalCompType()+"compNamesList").val(); });

				if(componentsObj[0] && componentsObj[0].versions && componentsObj[0].versions.length){
					/*if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
					self.componentVersionsArr(componentsObj[0].versions);*/

					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						self.componentVersionsArr(getMasterList(componentsObj[0].versions, "versionId", [self.selectedConfigRows()[0].componentVersionId], true));
					}
					else{
						self.componentVersionsArr(getMasterList(componentsObj[0].versions, "versionId", null, false));
					}
				}
				else{
					self.componentVersionsArr([{}]);
				}
				if(self.currentViewIndex() != uiConstants.common.EDIT_VIEW || self.modalCompType() == "hostModal"){
					$('#'+self.modalCompType()+'compVersionsList').prop('disabled', false).trigger("chosen:updated");
				}
			}
		}

		function setClusters(){
			if($("#"+self.modalCompType()+"compNamesList").val() == 0 || $("#"+self.modalCompType()+"selectedApplicationList").getAllValues().length == 0){
				self.clusterNamesArr([{}]);
				$("#"+self.modalCompType()+"clusterNamesList").trigger('chosen:updated');
				$("#"+self.modalCompType()+"clusterNamesList").prop('disabled', true).trigger("chosen:updated");
				
				//mk
				/*$("#"+self.modalCompType()+"clusterDiv").find("input,button,select").attr("disabled", "disabled");
				$("#"+self.modalCompType()+"availableClusterList").addClass("checklist-disabled");
				$("#"+self.modalCompType()+"selectedClusterList").addClass("checklist-disabled");*/
			}
			else{
				var componentId = $("#"+self.modalCompType()+"compNamesList").val(); //Send this to REST API for getting clusters
				var appIdsArr = $("#"+self.modalCompType()+"selectedApplicationList").getAllValues();//.map(function (x){return parseInt(x);})
				var appIdStr = "";

				for(appId in appIdsArr){
					appIdStr += "&applicationId=" + appIdsArr[appId];
				}
				//requestCall(uiConstants.common.SERVER_IP + "/clusterName/"+componentId, "GET", "", "getClusters", successCallback, errorCallback);
				requestCall(uiConstants.common.SERVER_IP + "/clusters/application?status=2&markInactive=1&componentId=" + componentId + appIdStr, "GET", "", "getClusters", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5781b800100000e81db11d38?callback=?", "GET", "", "getClusters", successCallback, errorCallback);
			}
		}

		function setCompAttributes(){
			self.hostAddressAttribId(0);
			hostAddressAttribMappingId = 0;

			if($("#"+self.modalCompType()+"compVersionsList").val() == 0){
				self.compAttributesArr([]);
			}
			else{
				var componentVersionId = $("#"+self.modalCompType()+"compVersionsList").val(); //Send this to REST API for getting component attributes
				requestCall(uiConstants.common.SERVER_IP + "/componentAttribute/"+componentVersionId, "GET", "", "getComponentAttributes", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5740bb30250000702f548bae", "GET", "", "getComponentAttributes", successCallback, errorCallback);
			}
		}

		function setCompKpiProducerMap(){
			if($("#"+self.modalCompType()+"compVersionsList").val() == 0){
				self.kpiProducerMapArr([]);
				self.kpiGroupProducerMapArr([]);
			}
			else{

				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					var componentVersionId = $("#"+self.modalCompType()+"compVersionsList").val(); //Send this to REST API for getting KPI/Producer mapping
					
					//This API call should give only active KPI's mapping
					requestCall(uiConstants.common.SERVER_IP + "/instance/add/producerKpiMapping/"+componentVersionId, "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
				}
				else{
					var selectedInstanceIds = "";
					for(selConfig in self.selectedConfigRows()){
						selectedInstanceIds = selectedInstanceIds + "&id=" + self.selectedConfigRows()[selConfig].componentInstanceId;
					}

					if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
						requestCall(uiConstants.common.SERVER_IP + "/kpiProducerMappingForInstances?" + selectedInstanceIds.substring(1)+"&status=1", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);					
					}
					else{
						requestCall(uiConstants.common.SERVER_IP + "/kpiProducerMappingForInstances?" + selectedInstanceIds.substring(1)+"&status=2&markInactive=1", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);					
					}
				}
				//requestCall("http://www.mocky.io/v2/575c546a0f00000d244fc9df?callback=?", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
			}
		}

		var producersChosenElementArr = [];
		function addInactiveToList(allData, dataToAdd, allDataIdKey, allDataNameKey, dataToAddId, dataToAddName, configElement){
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(allData);

				if(!allData.find(function( ele ) {return ele[allDataIdKey] && ele[allDataIdKey] === dataToAddId;} )){
					var obj = {};
					obj[allDataIdKey] = dataToAddId;
					obj[allDataNameKey] = dataToAddName;
					obj["isActive"] = false;
					allData.push(obj);

					producersChosenElementArr.push(configElement);

					sortArrayObjByValue(allData, allDataNameKey);
				}
				if(uiConstants.common.DEBUG_MODE)console.log(allData);
				$(configElement).trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
		}

		function setKpiProducerMappingValue(){
			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProducerMapArr());

			//if(self.selectedConfigRows().length == 1)
			for(kpiProducer in self.kpiProducerMapArr()){
				//self.configureKpiProducerMap();
				//self.getProducersList(kpiProducer);

				
				if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProducerMapArr()[kpiProducer].producers);
				
				if($.grep(self.kpiProducerMapArr()[kpiProducer].producers.all, function(e){ return  e.producerId == self.kpiProducerMapArr()[kpiProducer].producers.defaultId; }).length){
					$("#"+self.modalCompType()+"producer"+kpiProducer).val(self.kpiProducerMapArr()[kpiProducer].producers.defaultId).trigger('chosen:updated');
				}
				else{
					$("#"+self.modalCompType()+"producer"+kpiProducer).val("0").trigger('chosen:updated');
				}

				var collInterval = self.kpiProducerMapArr()[kpiProducer].collectionInterval;
				if(collInterval > 30){
					var collInterval = collInterval/60;
					$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer).val("minutes").trigger('chosen:updated');
				} 

				debugger;

				$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer).trigger('chosen:updated');

				jQuery(".chosen").chosen({
					search_contains: true	
				});

				$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer+"_chosen").addClass("chosen-dynamic-width");
				$("#"+self.modalCompType()+"collInterval"+kpiProducer).val(collInterval);
				$("#"+self.modalCompType()+"status"+kpiProducer).prop("checked", self.kpiProducerMapArr()[kpiProducer].status == 1);

				//self.kpiGroupValueArr.splice(kpiProducer, 1, []);
			}

			for(kpiProducer in self.kpiGroupProducerMapArr()){
				if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupProducerMapArr()[kpiProducer].producers);

				if($.grep(self.kpiGroupProducerMapArr()[kpiProducer].producers.all, function(e){ return  e.producerId == self.kpiGroupProducerMapArr()[kpiProducer].producers.defaultId; }).length){
					$("#"+self.modalCompType()+"grpProducer"+kpiProducer).val(self.kpiGroupProducerMapArr()[kpiProducer].producers.defaultId).trigger('chosen:updated');
				}
				else{
					$("#"+self.modalCompType()+"grpProducer"+kpiProducer).val("0").trigger('chosen:updated');
				}

				//$("#"+self.modalCompType()+"grpProducer"+kpiProducer).val(self.kpiGroupProducerMapArr()[kpiProducer].producers.defaultId);
				//$("#"+self.modalCompType()+"grpProducer"+kpiProducer).trigger('chosen:updated');

				var collInterval = self.kpiGroupProducerMapArr()[kpiProducer].collectionInterval;
				if(collInterval > 30){
					var collInterval = collInterval/60;
					$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer).val("minutes");
				} 

				$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer).trigger('chosen:updated');
				
				jQuery(".chosen").chosen({
					search_contains: true	
				});
				$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer+"_chosen").addClass("chosen-dynamic-width");

				$("#"+self.modalCompType()+"grpCollInterval"+kpiProducer).val(collInterval);
				$("#"+self.modalCompType()+"grpStatus"+kpiProducer).prop("checked", self.kpiGroupProducerMapArr()[kpiProducer].status == 1);
				self.kpiGroupValueArr.splice(kpiProducer, 1, []);
			}

			


		}

		function setKpiProducerCompInstValue(){
			//var kpiProducerObj;
			for(kpiProducer in self.kpiProducerMapArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProducerMapArr());

					$("#"+self.modalCompType()+"producer"+kpiProducer).val(self.kpiProducerMapArr()[kpiProducer].producers.defaultId).trigger('chosen:updated');

					var collInterval = self.kpiProducerMapArr()[kpiProducer].collectionInterval;
					if(collInterval > 30){
						var collInterval = collInterval/60;
						$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer).val("minutes").trigger('chosen:trigger');
					} 
					debugger;
					$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer).trigger('chosen:updated');
					
					jQuery(".chosen").chosen({
						search_contains: true	
					});

					$("#"+self.modalCompType()+"collIntervalUnit"+kpiProducer+"_chosen").addClass("chosen-dynamic-width");
					$("#"+self.modalCompType()+"collInterval"+kpiProducer).val(collInterval);
					$("#"+self.modalCompType()+"status"+kpiProducer).prop("checked", self.kpiProducerMapArr()[kpiProducer].status == 1);
				//}
			}

			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupProducerMapArr());
			for(kpiProducer in self.kpiGroupProducerMapArr()){
					var obj = self.kpiGroupProducerMapArr()[kpiProducer];
					self.kpiGroupProducerMapArr.splice(kpiProducer, 1);
					self.kpiGroupProducerMapArr.splice(kpiProducer, 0, obj);

					$("#"+self.modalCompType()+"grpProducer"+kpiProducer).val(self.kpiGroupProducerMapArr()[kpiProducer].producers.defaultId).trigger('chosen:updated');

					var collInterval = self.kpiGroupProducerMapArr()[kpiProducer].collectionInterval;
					if(collInterval > 30){
						var collInterval = collInterval/60;
						$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer).val("minutes");
					} 

					$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer).trigger('chosen:updated');
					
					jQuery(".chosen").chosen({
						search_contains: true	
					});
					$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer+"_chosen").addClass("chosen-dynamic-width");

					$("#"+self.modalCompType()+"grpCollInterval"+kpiProducer).val(collInterval);
					$("#"+self.modalCompType()+"grpStatus"+kpiProducer).prop("checked", self.kpiGroupProducerMapArr()[kpiProducer].status == 1);
				//}
			}

			jQuery(".chosen").chosen({
				search_contains: true	
			});

			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("#"+self.modalCompType()+"kpiProducerMappingList .chosen-container b").css("display", "none");
				$("#"+self.modalCompType()+"grpKpiProducerMappingList .chosen-container b").css("display", "none");
			}
		}

		function setKpiProducerMultipleCompInstValue(kpiProducerValueMapArr, isKpiGroup){
			var kpiValObj;
			var kpiValArr = [];
			var kpiProducerArr = [];
			var statusArr = [];
			var selKpiProdMappingArr = [];

			for(selConfig in self.selectedConfigRows()){
				selKpiProdMappingArr = $.grep(self.multipleInstancesKpiProducerMapArr(), function(e){ return e.componentInstanceId == self.selectedConfigRows()[selConfig].componentInstanceId; })[0];
				
				for(kpiVal in kpiProducerValueMapArr){
					if(!isKpiGroup){
						kpiValObj = $.grep(selKpiProdMappingArr.kpiDetails, function(e){ return e.kpiId == kpiProducerValueMapArr[kpiVal].kpiId; });
					}
					else{
						kpiValObj = $.grep(selKpiProdMappingArr.kpiGroups, function(e){ return e.kpiGroupId == kpiProducerValueMapArr[kpiVal].kpiGroupId; });
					}

					console.log(kpiValObj);

					if(kpiValObj.length != 0){
						if(kpiValArr[kpiVal] == undefined){
							kpiValArr[kpiVal] = [];
						}
						if(kpiValArr[kpiVal].length <= 1){
							if(kpiValArr[kpiVal].indexOf(kpiValObj[0].collectionInterval) == -1){
								kpiValArr[kpiVal].push(kpiValObj[0].collectionInterval);	
							}
						}

						if(kpiProducerArr[kpiVal] == undefined){
							kpiProducerArr[kpiVal] = [];
						}
						if(kpiProducerArr[kpiVal].length <= 1){
							if(kpiProducerArr[kpiVal].indexOf(kpiValObj[0].producers.defaultId) == -1){
								kpiProducerArr[kpiVal].push(kpiValObj[0].producers.defaultId);	
							}
						}

						if(statusArr[kpiVal] == undefined){
							statusArr[kpiVal] = [];
						}
						if(statusArr[kpiVal].length <= 1){
							if(statusArr[kpiVal].indexOf(kpiValObj[0].status) == -1){
								statusArr[kpiVal].push(kpiValObj[0].status);	
							}
						}

						if(kpiValArr[kpiVal].length > 1 && kpiProducerArr[kpiVal].length > 1 && statusArr[kpiVal].length > 1){
							break;
						}
					}
				}

				if(!isKpiGroup){
					for(kpiVal in kpiProducerValueMapArr){
						kpiValObj = $.grep(selKpiProdMappingArr.kpiDetails, function(e){ return e.kpiId == kpiProducerValueMapArr[kpiVal].kpiId; });

						if(kpiProducerArr[kpiVal].length == 1){
							addInactiveToList(kpiProducerValueMapArr[kpiVal].producers.all, kpiValObj[0].producers, 'producerId', 'producerName', kpiValObj[0].producers.defaultId, kpiValObj[0].producers.defaultProducerName, '#'+self.modalCompType()+'producer'+kpiVal);
							if(uiConstants.common.DEBUG_MODE)console.log(kpiValObj);
							var obj = kpiProducerValueMapArr[kpiVal];
							kpiProducerValueMapArr.splice(kpiVal, 1);
							kpiProducerValueMapArr.splice(kpiVal, 0, obj);

							$("#"+self.modalCompType()+"producer"+kpiVal).val(kpiValObj[0].producers.defaultId).trigger('chosen:updated');

						}
						else{
							$("#"+self.modalCompType()+"producer"+kpiVal).val("").trigger('chosen:updated');
							$("#"+self.modalCompType()+"producer"+kpiVal).next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
						}

						if(kpiValArr[kpiVal].length == 1){
							var collInterval = kpiValObj[0].collectionInterval;
							if(collInterval > 30){
								var collInterval = collInterval/60;
								$("#"+self.modalCompType()+"collIntervalUnit"+kpiVal).val("minutes").trigger('chosen:updated');
							}

							$("#"+self.modalCompType()+"collIntervalUnit"+kpiVal).trigger('chosen:updated');

							jQuery(".chosen").chosen({
								search_contains: true	
							});
							$("#"+self.modalCompType()+"collIntervalUnit"+kpiVal+"_chosen").addClass("chosen-dynamic-width");
							$("#"+self.modalCompType()+"collInterval"+kpiVal).val(collInterval);
						}
						else{
							$("#"+self.modalCompType()+"collIntervalUnit"+kpiVal).val("").trigger('chosen:updated');
							jQuery(".chosen").chosen({
								search_contains: true	
							});
							$("#"+self.modalCompType()+"collIntervalUnit"+kpiVal).next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
							$("#"+self.modalCompType()+"collInterval"+kpiVal).val("");
							$("#"+self.modalCompType()+"collInterval"+kpiVal).css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
						}

						if(statusArr[kpiVal].length == 1){
							$("#"+self.modalCompType()+"status"+kpiVal).prop("checked",kpiValObj[0].status == 1);
						}
						else{
							$("#"+self.modalCompType()+"status"+kpiVal).prop("indeterminate", true);
						}

						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}
				}

				else{
					for(kpiVal in kpiProducerValueMapArr){
						kpiValObj = $.grep(selKpiProdMappingArr.kpiGroups, function(e){ return e.kpiGroupId == kpiProducerValueMapArr[kpiVal].kpiGroupId; });
						console.log(kpiProducerArr[kpiVal].length);
						if(kpiProducerArr[kpiVal].length == 1){
							if(uiConstants.common.DEBUG_MODE)console.log(kpiProducerValueMapArr[kpiVal].producers.all);
							addInactiveToList(kpiProducerValueMapArr[kpiVal].producers.all, kpiValObj[0].producers, 'producerId', 'producerName', kpiValObj[0].producers.defaultId, kpiValObj[0].producers.defaultProducerName, '#'+self.modalCompType()+'grpProducer'+kpiVal);
							//addInactiveToList(self.kpiGroupProducerMapArr()[kpiProducer].producers.all, kpiProducerObj[0].producers, 'producerId', 'producerName', kpiProducerObj[0].producers.defaultId, kpiProducerObj[0].producers.defaultProducerName, '#grpProducer'+kpiProducer);
							

							if(uiConstants.common.DEBUG_MODE)console.log(kpiProducerValueMapArr[kpiVal].producers.all);
							var obj = kpiProducerValueMapArr[kpiVal];
							self.kpiGroupProducerMapArr.splice(kpiVal, 1);
							self.kpiGroupProducerMapArr.splice(kpiVal, 0, obj);
							console.log(kpiValObj[0].producers.defaultId);

							$("#"+self.modalCompType()+"grpProducer"+kpiVal).val(kpiValObj[0].producers.defaultId).trigger('chosen:updated');

						}
						else{
							$("#"+self.modalCompType()+"grpProducer"+kpiVal).val("").trigger('chosen:updated');
							$("#"+self.modalCompType()+"grpProducer"+kpiVal).next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
						}

						if(kpiValArr[kpiVal].length == 1){
							var collInterval = kpiValObj[0].collectionInterval;
							if(collInterval > 30){
								var collInterval = collInterval/60;
								$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal).val("minutes");

							} 

							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal).trigger('chosen:updated');
							
							jQuery(".chosen").chosen({
								search_contains: true	
							});
							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal+"_chosen").addClass("chosen-dynamic-width");
							$("#"+self.modalCompType()+"grpCollInterval"+kpiVal).val(collInterval);
						}
						else{
							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal).val("");
							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal).trigger('chosen:updated');

							jQuery(".chosen").chosen({
								search_contains: true	
							});
							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiProducer+"_chosen").addClass("chosen-dynamic-width");


							$("#"+self.modalCompType()+"grpCollIntervalUnit"+kpiVal).next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
							$("#"+self.modalCompType()+"grpCollInterval"+kpiVal).val("");
							$("#"+self.modalCompType()+"grpCollInterval"+kpiVal).css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
						}

						if(statusArr[kpiVal].length == 1){
							$("#"+self.modalCompType()+"grpStatus"+kpiVal).prop("checked",kpiValObj[0].status == 1);
						}
						else{
							$("#"+self.modalCompType()+"grpStatus"+kpiVal).prop("indeterminate", true);
						}

						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}
				}
				
			}
			$(".addKpiGrpValBtn").css("display","none");
		}

		function setKpiGroupValues(){
			kpiGroupValArr = [];
			/*for(kpi in self.kpiProducerCompInstMapArr()){
				if(self.kpiProducerCompInstMapArr()[kpi].kpiGroupId){
					for(kpiVal in self.kpiProducerCompInstMapArr()[kpi].kpiGroupValues){
						if(kpiGroupValArr[kpi] == undefined){
							kpiGroupValArr[kpi] = [{'kpiGroupValue':self.kpiProducerCompInstMapArr()[kpi].kpiGroupValues[kpiVal]}];
						}
						else{
							kpiGroupValArr[kpi].push({'kpiGroupValue':self.kpiProducerCompInstMapArr()[kpi].kpiGroupValues[kpiVal]});
						}

						
					}
				}
			}*/

			for(kpi in self.kpiGroupProducerMapArr()){
				if(self.kpiGroupProducerMapArr()[kpi].kpiGroupValues && self.kpiGroupProducerMapArr()[kpi].kpiGroupValues.length!=0){
					for(kpiVal in self.kpiGroupProducerMapArr()[kpi].kpiGroupValues){
						if(kpiGroupValArr[kpi] == undefined){
							kpiGroupValArr[kpi] = [{'kpiGroupValue':self.kpiGroupProducerMapArr()[kpi].kpiGroupValues[kpiVal]}];
						}
						else{
							kpiGroupValArr[kpi].push({'kpiGroupValue':self.kpiGroupProducerMapArr()[kpi].kpiGroupValues[kpiVal]});
						}
					}
				}
				else{
					kpiGroupValArr[kpi] = [];
				}
			}
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function editMultipleConfigs(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj);

			if($("#attribsPanel").is(":hidden")){
	    		self.expandCollapsePanel("#attribsPanel", "#imgAttribShowHide");
	    	}

			$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
			
			setCompNames();
			/*if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === configObj[0].componentId;} )){
				self.componentNamesArr.push({
					"componentId": configObj[0].componentId,
					"componentName": configObj[0].componentName,
					"isActive": false
				});
				$("#compNamesList_chosen span").first().addClass("inactiveOptionClass");
			}*/
			
			$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');

			setCompVersions();
			/*if(!self.componentVersionsArr().find( function( ele ) {return ele.versionId && ele.versionId === configObj[0].componentVersionId;} )){
				self.componentVersionsArr.push({
					"versionId": configObj[0].componentVersionId,
					"version": configObj[0].componentVersion,
					"isActive": false
				});
				$("#compVersionsList_chosen span").first().addClass("inactiveOptionClass");
			}*/
			$("#compVersionsList").val(configObj[0].componentVersionId).trigger('chosen:updated');
			
			$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
			$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
			$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");
			
			setCompAttributes();
			//mk setClusters();

			//mk var configCommonTypeArr = [];
			//var configCommonProfileArr = [];
			var configCommonStatusArr = [];
			commonTagsArr = [];
			uncommonTagsArr = [];
			//mk configCommonTypeArr.push(configObj[0].componentTypeId);
			
				/*if(configObj[0].maintenanceWindowProfile != undefined && configObj[0].maintenanceWindowProfile.profileName)
				configCommonProfileArr.push(configObj[0].maintenanceWindowProfile.profileName);
			else
				configCommonProfileArr.push("");*/
			
			configCommonStatusArr.push(configObj[0].status);

			//tokenfield for multiple edit
			/*var commonEngine = new Bloodhound({	
				local: self.configTagAutoCompleteArr(),
				datumTokenizer: function(d) {
					return Bloodhound.tokenizers.whitespace(d.value);
				},
				queryTokenizer: Bloodhound.tokenizers.whitespace
			});

			commonEngine.initialize();

			$('#compinstance-tokenfield-typeahead').tokenfield({
				typeahead: [null, { source: commonEngine.ttAdapter() }],
				createTokensOnBlur: true
			});*/

			$('#compinstance-tokenfield-typeahead').tokenfield({
			  autocomplete: {
			    source: self.configTagAutoCompleteArr(),
			    delay: 100
			  },
			  createTokensOnBlur: true
			});

			$('#compinstance-tokenfield-typeahead-tokenfield').on("keyup", function(e){
				if(e.which == 13 && $(this).val().trim() != ""){
					$("#compinstance-tokenfield-typeahead-tokenfield").blur();		
				}
			});

			var tagsArr = [];
			var allTagsArr = [];
			var tagsObjArr = [];

			for(obj in configObj){
				tagsObjArr = [];
				for(tag in configObj[obj].tags){

					if(uiConstants.common.DEBUG_MODE)console.log(configObj[obj].tags[tag].tagName);
					tagsObjArr.push(configObj[obj].tags[tag].tagName);
					//if (allTagsArr.indexOf(configObj[obj].tags[tab].tagName)==-1) allTagsArr.push(configObj[obj].tags[tab].tagName);
				}
				tagsArr.push(tagsObjArr);
			}

			commonTagsArr = tagsArr.shift().filter(function(v) {
			    return tagsArr.every(function(a) {
			        return a.indexOf(v) !== -1;
			    });
			});

			$('#compinstance-tokenfield-typeahead').tokenfield('setTokens', commonTagsArr);

			for(obj in configObj){
				
				/*mk if(configCommonTypeArr.length<=1){
					if(configCommonTypeArr.indexOf(configObj[obj].componentTypeId) == -1){
						configCommonTypeArr.push(configObj[obj].componentTypeId);	
					}
				}*/

				/*if(configCommonProfileArr.length<=1){
					if(configObj[obj].maintenanceWindowProfile != undefined && configObj[obj].maintenanceWindowProfile.profileName != undefined){
						if(configCommonProfileArr.indexOf(configObj[obj].maintenanceWindowProfile.profileName) == -1){
							configCommonProfileArr.push(configObj[obj].maintenanceWindowProfile.profileName);
						}
					}
					else if(configCommonProfileArr.indexOf("") == -1 ){
						configCommonProfileArr.push("");
					}
				}

				if(DEBUG_MODE)console.log(configCommonProfileArr);*/
				
				if(configCommonStatusArr.length==1){
					if(configCommonStatusArr.indexOf(configObj[obj].status) == -1){
						configCommonStatusArr.push(configObj[obj].status);	
					}
				}
			}

			if(configCommonStatusArr.length == 1){
				//$('#configStatus').prop('checked', configCommonStatusArr[0] == 1);
				$('#'+self.modalCompType()+'configStatus').bootstrapSwitch('state', configCommonStatusArr[0] == 1);

				if(!configObj[0].status){ //is status is inactive
					self.enableConfig(false);
					setConfigUneditable(true);
				}
			}
			else{
				//$("#configStatus").prop("indeterminate", true);
				$('#'+self.modalCompType()+'configStatus').bootstrapSwitch('indeterminate', true);

				self.enableConfig(false);
				setConfigUneditable(true);
			}		
		}
		
		function onMastersLoad(){
			//if(configTagLoaded == 1){
			if(compTypeVersionLoaded == 1 && applicationLoaded == 1 && hostLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){

					if(uiConstants.common.DEBUG_MODE)console.log(params.selectedConfigRows().length);
					if(params.selectedConfigRows().length == 1){ // for single application edit
						editSingleConfig(self.selectedConfigRows());
						if(!self.selectedConfigRows()[0].status){ //if the component is inactive
							setConfigUneditable(true);
						}
					}
					else{
						editMultipleConfigs(params.selectedConfigRows());
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		$('#messageDialogBox').on('hidden.bs.modal', function () {
			if(self.isModal()){
				if(!$('#messageDialogBox #msgSign').hasClass("glyphicon-info-sign")){
					$("body").addClass("modal-open");
				}
				$("body").css("padding-right", "0px");
			}
		});

		function successCallback(data, reqType) {
			if(reqType === "getCompInstTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }]
				});*/

				$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('#'+self.modalCompType()+'compinstance-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#"+self.modalCompType()+"compinstance-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getCompTypeVersion"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.componentsArr(getMasterList(data.result, "componentTypeId", [self.selectedConfigRows()[0].componentTypeId], true));
				}
				else{
					self.componentsArr(getMasterList(data.result, "componentTypeId", null, false));
				}
				if(uiConstants.common.DEBUG_MODE)console.log(self.componentsArr());

				$("#"+self.modalCompType()+"compTypeList").trigger('chosen:updated');

				if(self.displayComponent()!=0){
					self.displayComponent(0);

					
					$("#"+self.modalCompType()+"compTypeList").val(selectedCompTypeId).trigger('chosen:updated');
					setCompNames();
					$("#"+self.modalCompType()+"compNamesList").val(selectedCompId).trigger('chosen:updated');
				}

				if(self.modalCompType() == "hostModal"){
					$("#hostModalcompTypeList option:contains(Host)").attr('selected', 'selected');
	        		$('#hostModalcompTypeList').prop('disabled', true).trigger("chosen:updated");
					setCompNames();
					$('#hostModalcompNamesList').prop('disabled', false).trigger("chosen:updated");

				}

				compTypeVersionLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getKpiProducerMapping"){
				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					if(data.result.kpiDetails){
						self.kpiProducerMapArr($.grep(data.result.kpiDetails, function(e){ return  e.producers.defaultId!=undefined; }));
					}

					if(data.result.kpiGroups){
						self.kpiGroupProducerMapArr($.grep(data.result.kpiGroups, function(e){ return  e.producers.defaultId!=undefined; }));
					}

					if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProducerMapArr());

					setKpiProducerMappingValue();
				}
				
				else{
					if(data.result[0].kpiDetails){
						self.kpiProducerMapArr($.grep(data.result[0].kpiDetails, function(e){ return  e.producers.defaultId!=undefined; }));
					}

					if(data.result[0].kpiGroups){
						self.kpiGroupProducerMapArr($.grep(data.result[0].kpiGroups, function(e){ return  e.producers.defaultId!=undefined; }));
					}
				}

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.selectedConfigRows().length == 1){
					//mk self.kpiProducerCompInstMapArr(self.selectedConfigRows()[0].kpiDetails);
					//console.log(self.kpiProducerCompInstMapArr());
					//mk self.kpiProducerCompInstMapArr(self.kpiProducerCompInstMapArr().concat(self.selectedConfigRows()[0].kpiGroups));
					//mk self.kpiProducerCompInstMapArr(self.selectedConfigRows()[0].kpiGroups);
					//mk console.log(self.kpiProducerCompInstMapArr());
					

					setKpiProducerCompInstValue();

					for(producer in producersChosenElementArr){
						$(producersChosenElementArr[producer] + "_chosen span").first().addClass("inactiveOptionClass").trigger('chosen:updated');
					}

					setKpiGroupValues();

					for(kpiGrpProdMap in self.kpiGroupProducerMapArr()){
						if(kpiGroupValArr[kpiGrpProdMap] != undefined && kpiGroupValArr[kpiGrpProdMap].length != 0){
							self.kpiGroupValueArr.splice(kpiGrpProdMap, 1, kpiGroupValArr[kpiGrpProdMap]);
						}
						else{
							self.kpiGroupValueArr.splice(kpiGrpProdMap, 1, []);
						}
					}
				}
				/*else if(self.currentViewIndex() == ADD_SINGLE_VIEW){
					setKpiProducerMappingValue();
				}*/
				else if(params.selectedConfigRows().length>1){
					self.multipleInstancesKpiProducerMapArr(data.result);

					setKpiProducerMultipleCompInstValue(self.kpiProducerMapArr(), false);
					setKpiProducerMultipleCompInstValue(self.kpiGroupProducerMapArr(), true);

					console.log(producersChosenElementArr);

					for(producer in producersChosenElementArr){
						$(producersChosenElementArr[producer] + "_chosen span").first().addClass("inactiveOptionClass").trigger('chosen:updated');
					}
				}

				setHeaderCheckboxState('#'+self.modalCompType()+'chkboxMappingHeader', '.'+self.modalCompType()+'chkboxMappingCol', self.kpiProducerMapArr());
				setHeaderCheckboxState('#'+self.modalCompType()+'grpChkboxMappingHeader', '.'+self.modalCompType()+'grpChkboxMappingCol', self.kpiGroupProducerMapArr());

				$("#"+self.modalCompType()+"advSettingsPanel").css("display", "");
				
				var $tab = $('#'+self.modalCompType()+'formAddEdit table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});

				$("#"+self.modalCompType()+"advSettingsPanel").css("display", "none");
			}
			else if(reqType === "getComponentAttributes"){
				//self.compAttributesArr([{}]);// for handling host address/name
				//self.compAttributesArr(self.compAttributesArr().concat(data.result));
				if(data.result.length){
					var compAttribHostAddrObj = $.grep(data.result, function(e){ return (slugify(e.attributeName) == "hostaddress" || slugify(e.attributeName) == "host address"); })[0];
					if(compAttribHostAddrObj){
						self.hostAddressAttribId(compAttribHostAddrObj.attributeId);
	       				$('#hostsList').trigger("chosen:updated");
	       				jQuery(".chosen").chosen({
							search_contains: true	
						});

						if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
							if(slugify(self.selectedCompType()) == 'host'){
								$("#"+self.modalCompType()+"txtHostAddress").val(compAttribHostAddrObj.attributeDefaultValue);
							}
							else{
								$("#hostsList option").filter(function () { return $(this).html() == compAttribHostAddrObj.attributeDefaultValue || $(this).html().indexOf("("+compAttribHostAddrObj.attributeDefaultValue+")") != -1 || $(this).html().indexOf(compAttribHostAddrObj.attributeDefaultValue+" ") != -1; }).prop('selected', true);
								$('#hostsList').trigger("chosen:updated");
							}
						}

						hostAddressAttribMappingId = compAttribHostAddrObj.componentAttributeMappingId;
						data.result.splice(data.result.indexOf(compAttribHostAddrObj),1);
					}
				}

				self.compAttributesArr(data.result);

				var compAttributesObj;

				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){


					for(compAttr in self.compAttributesArr()){
						if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX'){
							$("#"+self.modalCompType()+"txtbox"+compAttr).val(self.compAttributesArr()[compAttr].attributeDefaultValue);
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
							$("#"+self.modalCompType()+"password"+compAttr).val(self.compAttributesArr()[compAttr].attributeDefaultValue);
						}
						else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
							
							if(self.compAttributesArr()[compAttr].attributeDefaultValue != ""){
								$("#"+self.modalCompType()+"dropdown"+ compAttr +" option").filter(function () { return $(this).html() == self.compAttributesArr()[compAttr].attributeDefaultValue; }).prop('selected', true).trigger('chosen:updated');
							}
						}
					}
				}

				else if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.selectedConfigRows().length == 1){
					
					for(compAttr in self.compAttributesArr()){
						compAttributesObj = $.grep(self.selectedConfigRows()[0].attributes, function(e){ return e.attributeId == self.compAttributesArr()[compAttr].attributeId; });
						
						if(compAttributesObj.length != 0){
							if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX'){
								$("#"+self.modalCompType()+"txtbox"+compAttr).val(compAttributesObj[0].attributeValue);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
								$("#"+self.modalCompType()+"password"+compAttr).val(compAttributesObj[0].attributeValue);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
								//$("#dropdown"+compAttr +" option:contains(" + compAttributesObj[0].attributeValue + ")").attr('selected', 'selected');
								$("#"+self.modalCompType()+"dropdown"+ compAttr +" option").filter(function () { return $(this).html() == compAttributesObj[0].attributeValue; }).prop('selected', true).trigger('chosen:updated');
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
								$("#"+self.modalCompType()+"checkbox"+compAttr).prop('checked', compAttributesObj[0].attributeValue == 1);
							}
						}
					}
					setCompKpiProducerMap();
				}

				else if(self.selectedConfigRows().length > 1){
					var hostAddressesArr = [];
					var attribsArr = [];
					var hostVal = "";

					for(selConfig in self.selectedConfigRows()){
						if(self.selectedCompType() == "Host"){
							if(hostAddressesArr.length == 0){
								hostAddressesArr.push(self.selectedConfigRows()[selConfig].hostAddress);
							}
							else if(hostAddressesArr.length == 1){
								if(hostAddressesArr.indexOf(self.selectedConfigRows()[selConfig].hostAddress) == -1){
									hostAddressesArr.push(self.selectedConfigRows()[selConfig].hostAddress);
								}
							}
						}
						else{
							hostVal = self.selectedConfigRows()[selConfig].hostName + " (" + self.selectedConfigRows()[selConfig].hostAddress + ")";

							if(hostAddressesArr.length == 0){
								hostAddressesArr.push(hostVal);
							}
							else if(hostAddressesArr.length == 1){
								if(hostAddressesArr.indexOf(hostVal) == -1){
									hostAddressesArr.push(hostVal);
								}
							}
						}

						for(compAttr in self.compAttributesArr()){
							//console.log(self.selectedConfigRows()[selConfig].attributes);
							compAttributesObj = $.grep(self.selectedConfigRows()[selConfig].attributes, function(e){ return e.attributeId == self.compAttributesArr()[compAttr].attributeId; });
							
							if(attribsArr[compAttr] == undefined){
								attribsArr[compAttr] = [];
							}

							var attribVal = "";

							if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
								attribVal = 0;
							}

							if(compAttributesObj.length > 0){
								attribVal = compAttributesObj[0].attributeValue;
							}
							
							if(attribsArr[compAttr].length == 0){

								attribsArr[compAttr].push(attribVal);
							}
							else if(attribsArr[compAttr].length == 1){
								if(attribsArr[compAttr].indexOf(attribVal) == -1){
									attribsArr[compAttr].push(attribVal);
								}
							}
						}
					}

					if(uiConstants.common.DEBUG_MODE)console.log(attribsArr);

					if(self.selectedCompType() == "Host"){
						if(hostAddressesArr.length == 1){
							$("#txtHostAddress").val(hostAddressesArr[0]);
						}
						else if(hostAddressesArr.length > 1){
							$("#txtHostAddress").val("");
							$("#txtHostAddress").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
						}
					}
					else{
						if(hostAddressesArr.length == 1){
							$("#hostsList option:contains("+hostAddressesArr[0]+")").attr('selected', 'selected');
	        				$('#hostsList').trigger("chosen:updated");
						}
						else if(hostAddressesArr.length > 1){
							$("#hostsList").val("").trigger('chosen:updated');
							$("#hostsList").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
	       					//$('#hostsList').trigger("chosen:updated");

						}
					}

					for(compAttr in self.compAttributesArr()){
						compAttributesObj = $.grep(self.selectedConfigRows()[0].attributes, function(e){ return e.attributeId == self.compAttributesArr()[compAttr].attributeId; });

						if(attribsArr[compAttr].length == 1){
							if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX'){
								$("#"+self.modalCompType()+"txtbox"+compAttr).val(attribsArr[compAttr]);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
								$("#"+self.modalCompType()+"password"+compAttr).val(attribsArr[compAttr]);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
								if(attribsArr[compAttr] != ""){
									$("#"+self.modalCompType()+"dropdown"+ compAttr +" option").filter(function () { return $(this).html() == attribsArr[compAttr]; }).prop('selected', true).trigger('chosen:updated');
								}
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
								if(attribsArr[compAttr] != ""){
									$("#"+self.modalCompType()+"checkbox"+compAttr).prop('checked', attribsArr[compAttr] == 1);
								}
							}
						}
						else if(attribsArr[compAttr].length > 1){
							if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'TEXTBOX'){
								$("#"+self.modalCompType()+"txtbox"+compAttr).val("");
								$("#"+self.modalCompType()+"txtbox"+compAttr).css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'PASSWORD'){
								$("#"+self.modalCompType()+"password"+compAttr).val("");
								$("#"+self.modalCompType()+"password"+compAttr).css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'DROPDOWN'){
								$("#"+self.modalCompType()+"dropdown"+compAttr).val("").trigger('chosen:updated');
								$("#"+self.modalCompType()+"dropdown"+compAttr).css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
							}
							else if(self.compAttributesArr()[compAttr].attributeType.toUpperCase() == 'CHECKBOX'){
								$("#"+self.modalCompType()+"checkbox"+compAttr).prop("indeterminate", true);
							}
						}
					}

					setCompKpiProducerMap();

				}
			}
			else if(reqType === "getClusters"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				if(data.result.length){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						self.clusterNamesArr(getMasterList(data.result, "clusterId", [self.selectedConfigRows()[0].clusters[0].clusterId], true));
					}
					else{
						self.clusterNamesArr(getMasterList(data.result, "clusterId", null, false));
					}
				}
				else{
					self.clusterNamesArr([{}]);
				}

				$("#"+self.modalCompType()+"clusterNamesList").prop('disabled', false).trigger("chosen:updated");
				$("#"+self.modalCompType()+"clusterNamesList").trigger('chosen:updated');

				if(self.currentViewIndex() != uiConstants.common.LIST_VIEW && self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					if(self.selectedConfigRows()[0].clusters.length){

						if($.grep(self.clusterNamesArr(), function(e){ return  e.clusterId == self.selectedConfigRows()[0].clusters[0].clusterId; }).length){
							$("#"+self.modalCompType()+"clusterNamesList").val(self.selectedConfigRows()[0].clusters[0].clusterId).trigger('chosen:updated');
						}
						else{
							$("#"+self.modalCompType()+"clusterNamesList").val("0").trigger('chosen:updated');
						}

						//$("#"+self.modalCompType()+"clusterNamesList").val(self.selectedConfigRows()[0].clusters[0].clusterId).trigger('chosen:updated');
					}
				}

				if(self.currentViewIndex() == uiConstants.common.READ_VIEW || !self.enableConfig()){
	        		$('#'+self.modalCompType()+'clusterNamesList').prop('disabled', true).trigger("chosen:updated");
				}
			}
			else if(reqType === "addSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPINST,res[0].errorCode,res[0].message), "error");
					}else{
						if(self.modalCompType() == "hostModal"){
							showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_HOST_INSTANCE, "error");
						}
						else{
							showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCE, "error");
						}
					}
				}
				else{
					if(self.modalCompType() == "hostModal"){
						self.compName($("#hostModaltxtName").val() + " (" + $("#txtHostAddress").val() + ")");
						$(".modal-header button").click();
						showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_ADD_HOST_INSTANCE);
					}
					else if(self.modalCompType() == ""){
						self.cancelConfig();

						params.curPage(1);
						showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCE);

					}
					

				}
			}
			else if(reqType === "editConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPINST,res[0].errorCode,res[0].message), "error");
					}else{
						showMessageBox(uiConstants.componentInstanceConfig.ERROR_UPDATE_COMP_INSTANCE, "error");

					}
				}
				else{
					showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_UPDATE_COMP_INSTANCE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "getApplications"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.applicationsArr(data.result);

				self.availableApplicationArr([]);
				self.selectedApplicationArr([]);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					/*if(self.selectedConfigRows().length > 1){
						$(modalCompType()+'divApplication').css("visibility", "hidden");
					}*/
					var selAppIdsArr = [];

					for(var app in self.selectedConfigRows()[0].applications){
						selAppIdsArr.push(self.selectedConfigRows()[0].applications[app].applicationId);
					}

					self.applicationsArr(getMasterList(data.result, "applicationId", selAppIdsArr, true));
				}
				else{
					self.applicationsArr(getMasterList(data.result, "applicationId", null, false));
				}

				for(var app in self.applicationsArr()){
					self.availableApplicationArr.push({
						"id": self.applicationsArr()[app].applicationId,
						"name": self.applicationsArr()[app].applicationName
					});
				}

				/*if(self.currentViewIndex() == EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					for(selApp in self.selectedConfigRows()[0].applications){
						if(!self.availableApplicationArr().find( function( ele ) {return ele.id && ele.id === self.selectedConfigRows()[0].applications[selApp].applicationId;} )){
							self.availableApplicationArr.push({
								"id": self.selectedConfigRows()[0].applications[selApp].applicationId,
								"name": self.selectedConfigRows()[0].applications[selApp].applicationName,
								"isActive": false
							});
						}
					}

					sortArrayObjByValue(self.availableApplicationArr(), "name");
				}
*/
				$('#'+self.modalCompType()+'availableApplicationList').checklistbox({
				    data: self.availableApplicationArr()
				});

				$('#'+self.modalCompType()+'selectedApplicationList').checklistbox({
				    data: self.selectedApplicationArr()
				});

				if(self.currentViewIndex() != uiConstants.common.LIST_VIEW && self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.modalCompType() != "hostModal"){
			        for(application in self.selectedConfigRows()[0].applications){
						$("#"+self.modalCompType()+"availableApplicationList .checkList[value=" + self.selectedConfigRows()[0].applications[application].applicationId + "]").prop("checked",true);
					}
					self.addToSelectedApplication();
				}

				applicationLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getHosts"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(self.selectedConfigRows().length == 1 && self.selectedConfigRows()[0].hostStatus == 0){
						data.result.push({
							"hostName": self.selectedConfigRows()[0].hostName,
							"hostAddress": self.selectedConfigRows()[0].hostAddress,
							"status": 0
						});
					}
				}

				window.hostsArr(data.result.length ? data.result : [{}]);
	       		$('#hostsList').trigger("chosen:updated");

				if(self.compName() != ""){
					$("#hostsList option:contains("+self.compName()+")").attr('selected', 'selected');
	       			$('#hostsList').trigger("chosen:updated");
	       			self.compName("");
				}

				hostLoaded = 1;
				onMastersLoad();
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getCompInstTag"){
				showMessageBox(uiConstants.componentInstanceConfig.ERROR_GET_COMP_INSTANCE_TAGS, "error");
			}
  			else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}
			else if(reqType === "getKpiProducerMapping"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_KPI_PRODUCER_MAP, "error");
			}
			else if(reqType === "getComponentAttributes"){
				showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_LIST, "error");
			}
			else if(reqType === "getClusters"){
				showMessageBox(uiConstants.common.ERROR_GET_CLUSTERS, "error");
			}
			else if(reqType === "addSingleConfig"){

				if(self.modalCompType() == "hostModal"){
					showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_HOST_INSTANCE, "error");
				}
				else{
					showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCE, "error");
				}
			}
			else if(reqType === "editConfig"){
				showMessageBox(ERROR_UPDATE_COMP_INSTANCE, "error");
			}
  			else if(reqType === "getApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
			else if(reqType === "getHosts"){
  				showMessageBox(uiConstants.common.ERROR_GET_HOSTS, "error");
			}
		}
	}

	ComponentInstanceAddEdit.prototype.dispose = function() { };
	return { viewModel: ComponentInstanceAddEdit, template: templateMarkup };
});