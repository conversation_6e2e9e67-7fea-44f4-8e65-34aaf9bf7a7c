<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divCompInstAddEdit">
	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body">
		<form class="form-horizontal" role="form" data-bind="attr: {'id': modalCompType()+'formAddEdit'}, template: {afterRender: renderHandler }">
			<div id="divCompInstanceName" class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" data-bind="attr: {'id': modalCompType()+'txtName'}, value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<!-- <div id="divCompInstDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" rows="3" data-bind="attr: {'id': modalCompType()+'txtDescription'}, value: description" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div> -->

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Component Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" data-bind="attr:{'id': modalCompType()+'compTypeList', disabled: currentViewIndex() == uiConstants.common.EDIT_VIEW}, foreach : componentsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
						<!-- /ko-->

						<option data-bind="value: $data.componentTypeId, text: $data.componentType"></option>
					</select>
				</div>

				<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW && (currentViewIndex() == uiConstants.common.EDIT_VIEW && selectedConfigRows()[0] && selectedConfigRows()[0].status == 1) || currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW  || currentViewIndex() == uiConstants.common.CLONE_VIEW -->
					<button type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="attr: {'id': modalCompType()+'modalCompType'}, event:{click: switchModal.bind($data, 'componentType')}" data-toggle="modal" data-target="#idModal" title="Add Component Type"></button>
				<!-- /ko-->
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Component <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" data-bind="attr:{'id': modalCompType()+'compNamesList', disabled: currentViewIndex() == uiConstants.common.EDIT_VIEW}, event:{change: function(){checkInactive($data.componentNamesArr(), '#compNamesList', 'componentId')}}, foreach : componentNamesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
						<!-- /ko-->
						
						<option data-bind="value: $data.componentId, text: $data.componentName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>

				<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW && (currentViewIndex() == uiConstants.common.EDIT_VIEW && selectedConfigRows()[0] && selectedConfigRows()[0].status == 1) || currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW  || currentViewIndex() == uiConstants.common.CLONE_VIEW -->
					<button type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="attr: {'id': modalCompType()+'btnAddComp'}, event:{click: switchModal.bind($data, 'component')}" data-toggle="modal" data-target="#idModal" title="Add Component"></button>
				<!-- /ko-->
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Version <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" data-bind="attr:{'id': modalCompType()+'compVersionsList', disabled: currentViewIndex() == uiConstants.common.EDIT_VIEW}, event:{change: function(){checkInactive($data.componentVersionsArr(), '#compVersionsList', 'versionId')}}, foreach : componentVersionsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
						<!-- /ko-->
						
						<option data-bind="value: $data.versionId, text: $data.version"></option>
					</select>
				</div>
			</div>

			<hr class="form-group-seperator">
			
			<div class="form-group">
				<div class="panel panel-default inner-panel" style="margin-bottom: 0px;">
					<div class="configPanel panel-heading"><a class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="attr:{'id': modalCompType()+'imgAttribShowHide'}, event: {click: expandCollapsePanel.bind($data, '#'+modalCompType()+'attribsPanel', '#'+modalCompType()+'imgAttribShowHide')}" title="Collapse"><span class="panel-label-expand-collapse">Attributes</span></a></div>


					<div class="panel-body inner-div-container" style="display: none;" data-bind="attr: {'id': modalCompType()+'attribsPanel'}">
						<div>
							<div class="form-group form-required inner-panel-form">
								<!-- ko if: selectedCompType() != 'Host' -->

									<label data-bind="visible: hostAddressAttribId() != 0" class="control-label col-sm-2">Host <span class="mandatoryField">*</span></label>

									<div data-bind="visible: hostAddressAttribId() != 0" class="col-sm-4">
									
										<select class="chosen form-control" data-bind="attr: {'id': 'hostsList'}, foreach : window.hostsArr" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="text: uiConstants.common.SELECT_HOST"></option>
											<!-- /ko-->
											
											<!-- ko if: $data.hostName || $data.hostAddress -->
												<option data-bind="text: ($data.hostName + ' (' + $data.hostAddress + ')' + ($data.status == 0 ? ' (Inactive)' : ''))"></option>
											<!-- /ko-->
										</select>
									</div>

										<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW && (currentViewIndex() == uiConstants.common.EDIT_VIEW && selectedConfigRows()[0] && selectedConfigRows()[0].status == 1) || currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW  || currentViewIndex() == uiConstants.common.CLONE_VIEW -->

										<button id="modalHost" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="visible: hostAddressAttribId() != 0, event:{click: switchModal.bind($data, 'host')}" data-toggle="modal" data-target="#idModal" title="Add Host"></button>
									<!-- /ko-->
								<!-- /ko-->

								<!-- ko if: slugify(selectedCompType()) == 'host' -->
									<label class="control-label col-sm-2">Host Address <span class="mandatoryField">*</span></label>

									<div class="col-sm-4">
										<input type="text" id="txtHostAddress" class="form-control" data-bind="value: hostAddress" placeholder="Enter Host Address">
									</div>
								<!-- /ko-->

									
							</div>

							<!-- ko foreach: compAttributesArr -->
									<div class="form-group form-required inner-panel-form">
										<label class="control-label col-sm-2"><span data-bind="text: $data.attributeName"></span> <span data-bind="visible : $data.isMandatory" class="mandatoryField">*</span></label>

										 <div class="col-sm-4">
										<!-- ko if: $data.attributeType.toUpperCase() == 'TEXTBOX' -->
											<input type="text" class="form-control" data-bind="enable: $parent.enableConfig(), attr:{'id': $parent.modalCompType()+'txtbox'+$index(), 'placeholder': 'Enter ' + $data.attributeName}" max="45" pattern="[a-zA-Z0-9_ ]{2,45}">
										<!-- /ko-->

										<!-- ko if: $data.attributeType.toUpperCase() == 'PASSWORD' -->
											<input type="password" class="form-control" data-bind="enable: $parent.enableConfig(), attr:{'id': $parent.modalCompType()+'password'+$index(), 'placeholder': 'Enter ' + $data.attributeName}, event: {click: function(){$parent.passwordChangeHandler($index())}}" max="45">
										<!-- /ko-->

										<!-- ko if: $data.attributeType.toUpperCase() == 'DROPDOWN' -->
											<select class="chosen form-control" data-bind="foreach : $data.attributeOptions, enable: $parent.enableConfig(), attr:{'id': $parent.modalCompType()+'dropdown'+$index(), 'placeholder': 'Select ' + $data.attributeName}" max="45">

											<!-- ko if: $index() == 0 -->
												<option data-bind="value: '', text: uiConstants.common.SELECT"></option>
											<!-- /ko-->

											<option data-bind="value: $data.attributeOptionId, text: $data.attributeOptionName"></option>>
											</select>
										<!-- /ko-->

										<!-- ko if: $data.attributeType.toUpperCase() == 'CHECKBOX' -->
											<input type="checkbox" data-bind="enable: $parent.enableConfig(), attr:{'id': $parent.modalCompType()+'checkbox'+$index()}">
										<!-- /ko-->

										</div>
									</div>
							<!-- /ko-->
						</div>
					</div>
				</div>
			</div>

			<hr class="form-group-seperator">

			<div class="form-group form-required" data-bind="attr: {'id': modalCompType()+'divApplication'}">
				<label class="control-label col-sm-2">Application <span class="mandatoryField" data-bind="visible: isAppMandatory()">*</span></label>
				<div data-bind="attr: {'id': modalCompType()+'appDiv'}" class="col-sm-6">
					<table class="checklist-div-table">
						<tr>
							<td style="width: 270px">
								<label style="margin-left: 5px;">Available:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': modalCompType()+'selAllAvailApplication', disabled: availableApplicationArr().length == 0}">Select All</input></label>
								</div>
							</td>

							<td style="width: 40px"></td>

							<td style="width: 270px">
								<label style="margin-left: 5px;">Selected:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': modalCompType()+'selAllSelApplication', disabled: selectedApplicationArr().length == 0}">Select All</input></label>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<div class="inner-div-container" data-bind="attr: {'id': modalCompType()+'availableApplicationList'}"></div>
							</td>

							<td style="padding: 5px;">
								<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddApplicationBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddApplicationBtn()}, event:{click: addToSelectedApplication}"></button>
								<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveApplicationBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveApplicationBtn()}, event:{click: addToAvailableApplication}"></button>
							</td>

							<td>
								<div class="inner-div-container" data-bind="attr: {'id': modalCompType()+'selectedApplicationList'}"></div>
							</td>
						</tr>
					</table>
				</div>
			</div>

			<div data-bind="attr: {'id': modalCompType()+'divCluster'}" class="form-group form-required">
				<label class="control-label col-sm-2">Cluster</label>

				<div class="col-sm-4">
					<select class="chosen form-control" data-bind="attr:{'id': modalCompType()+'clusterNamesList'}, foreach : clusterNamesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
						<!-- /ko-->
						
						<!-- ko if: $data.clusterId -->
							<option data-bind="value: $data.clusterId, text: $data.clusterName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
						<!-- /ko-->
					</select>
				</div>
				<!-- <div data-bind="attr: {'id': modalCompType()+'clusterDiv'}" class="col-sm-6">
					<table>
						<tr>
							<td style="width: 270px">
								<label style="margin-left: 5px;">Available:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': modalCompType()+'selAllAvailCluster', disabled: availableClusterArr().length == 0}">Select All</input></label>
								</div>
							</td>

							<td style="width: 40px"></td>

							<td style="width: 270px">	
								<label style="margin-left: 5px;">Selected:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': modalCompType()+'selAllSelCluster', disabled: selectedClusterArr().length == 0}">Select All</input></label>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<div style="width: 270px" class="inner-div-container" data-bind="attr: {'id': modalCompType()+'availableClusterList'}"></div>
							</td>

							<td style="padding: 5px;">
								<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddClusterBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddClusterBtn()}, event:{click: addToSelectedCluster}"></button>
								<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveClusterBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveClusterBtn()}, event:{click: addToAvailableCluster}"></button>
							</td>

							<td>
								<div style="width: 270px" class="inner-div-container" data-bind="attr : {'id': modalCompType()+'selectedClusterList'}"></div>
							</td>
						</tr>
					</table>
				</div> -->
			</div>

			<hr id="advSettingsSep" class="form-group-seperator">

			<div class="form-group">
				<div class="panel panel-default inner-panel" style="height: 100%;">
					<div class="configPanel panel-heading"><a class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="attr: {'id': modalCompType()+'imgAdvSettingsShowHide'}, event: {click: expandCollapsePanel.bind($data, '#'+modalCompType()+'advSettingsPanel', '#'+modalCompType()+'imgAdvSettingsShowHide')}" title="Collapse"><span class="panel-label-expand-collapse">KPI Settings</span></a></div>
					<div data-bind="attr: {'id': modalCompType()+'advSettingsPanel'}" class="panel-body" style="display: none; padding: 0px; margin-top: 5px;">
						<div class="col-sm-12 wrapper-scroll-table" style="height: 250px; margin-bottom: 20px;">
							<table data-bind="attr: {'id': modalCompType()+'kpiProducerMappingList'}" class="table table-bordered table-hover table-striped" style="width:100%">
								<thead>
									<tr class="a1-inner-table-thead">
										<th style="visibility: hidden; width: 0px;"></th>
										<th style="visibility: hidden;"></th>
										<th class="tableHeaderOverflowOmmiter col-xs-4">KPI Name</th>
										<th class="tableHeaderOverflowOmmiter col-xs-4">Producer <span class="mandatoryField">*</span></th>
										<th class="tableHeaderOverflowOmmiter col-xs-3">Collection Interval <span class="mandatoryField">*</span></th>
										<th class="col-xs-1"><input type="checkbox" data-bind ="attr: {'id': modalCompType()+'chkboxMappingHeader'}" title="Select All"/> Status</th>
									</tr>
								</thead>

								<tbody data-bind="foreach : kpiProducerMapArr" >
									<tr>
										<td class="tdKpiId" style="visibility: hidden;" data-bind="text: $data.kpiId ? $data.kpiId : $data.kpiGroupId"></td>

										<td style="visibility: hidden;" data-bind="text: $data.componentInstanceKpiId"></td>
										
										<td class="textOverflowOmmiter" data-placement="bottom" data-bind="text: $data.kpiName"></td>
										
										<td>
											<select class="chosen" data-bind="event:{change: function(){$parent.checkInactive($data.producers.all, '#'
											+'producer'+$index(), 'producerId')}}, enable: $parent.enableConfig(), foreach : $data.producers.all, attr: {id: $parent.modalCompType()+'producer'+$index()},
											event: {'chosen:showing_dropdown': function(){$parent.onChosenDropdownOpen($parent.modalCompType()+'producer'+$index(), 'advSettingsPanel')}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose($parent.modalCompType()+'producer'+$index(), 'advSettingsPanel')}}" data-placeholder=" ">

												<!-- ko if: $index() == 0 -->
													<option data-bind="value: 0, text: 'Select Producer'"></option>
												<!-- /ko-->

												<!-- ko if: $data.producerId -->
													<option data-bind="value: $data.producerId, text: $data.producerName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
												<!-- /ko-->
											</select>
										</td>

										<td style="padding-right: 12px;">
											<input class="form-control" type="number" data-bind="enable: $parent.enableConfig(), attr: {'id': $parent.modalCompType()+'collInterval'+$index()}" min="1" style="display: inline; width: 60%"></input>

											<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), attr: {'id': $parent.modalCompType()+'collIntervalUnit'+$index()},
											event: {'chosen:showing_dropdown': function(){$parent.onChosenDropdownOpen($parent.modalCompType()+'collIntervalUnit'+$index(), 'advSettingsPanel')}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose($parent.modalCompType()+'collIntervalUnit'+$index(), 'advSettingsPanel')}}">
												<option value="seconds">Seconds</option>
												<option value="minutes">Minutes</option>
											</select>
										</td>

										<td style="text-align:center">
											<input type="checkbox" data-bind="enable: $parent.enableConfig(), attr: {'class': $parent.modalCompType()+'chkboxMappingCol', 'id': $parent.modalCompType()+'status'+$index()}"></input>
										</td>
									</tr>
								</tbody>
							</table>

							<!-- <hr class="form-group-seperator" style="margin-left: 5px; margin-right: 5px;"> -->

						</div>

						<div class="col-sm-12 wrapper-scroll-table" style="height: 250px;">
							<table data-bind="attr: {'id': modalCompType()+'grpKpiProducerMappingList'}" class="table table-bordered table-hover table-striped" style="width:100%;">
								<thead>
									<tr class="a1-inner-table-thead">
										<th style="visibility: hidden;"></th>
										<th style="visibility: hidden;"></th>
										<th class="tableHeaderOverflowOmmiter col-xs-4">KPI Group Name</th>
										<th class="tableHeaderOverflowOmmiter col-xs-4">Producer <span class="mandatoryField">*</span></th>
										<th class="tableHeaderOverflowOmmiter col-xs-3">Collection Interval <span class="mandatoryField">*</span></th>
										<th class="col-xs-1"><input type="checkbox" data-bind ="attr: {'id': modalCompType()+'grpChkboxMappingHeader'}" title="Select All"/> Status</th>
									</tr>
								</thead>

								<tbody data-bind="foreach : kpiGroupProducerMapArr" >
									<tr>
										<td class="tdKpiId" style="visibility: hidden;" data-bind="text: $data.kpiId ? $data.kpiId : $data.kpiGroupId"></td>

										<td style="visibility: hidden;" data-bind="text: $data.componentInstanceKpiGroupId"></td>


										<td class="textOverflowOmmiter" data-placement="bottom">
											<span data-bind="text: $data.kpiGroupName"></span>
											<!-- <a class="kpiGroupConfig" title="Configure KPI Group"> 
												<img src="images/settings-16.png" alt="Configure KPI Group" />
											</a> -->
											
											<!-- ko if: $data.discoveryFlag == 0 -->
												<button class="glyphicon glyphicon-plus addKpiGrpValBtn" type="button" data-bind="visible : $parent.currentViewIndex() != uiConstants.common.READ_VIEW && $parent.enableConfig(), event:{click: function(){$parent.addKpiGroupValue($index())}}" title="Add Group Value"></button>

												<!-- ko if: $parent.kpiGroupValueArr()[$index()] -->
													<div data-bind="foreach : $parent.kpiGroupValueArr()[$index()]" style="max-height: 110px; overflow-y: scroll;">
														<div style="margin-left: 20px; margin-bottom: 5px;">
															
															<div class="input-group col-sm-12" style="padding-right: 3px; margin-left: -5px;">
																<!-- <span class="input-group-addon">KPI Group Value</span> -->

																<span class="input-group-addon input-grp-label" style="width: 20%;" data-bind="text: 'Value '+($index()+1)+':'"></span>

																<input type="text" data-bind="enable: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW && $parents[1].enableConfig(), value: $data.kpiGroupValue, attr: {'id': $parents[1].modalCompType()+'kpiGroupVal'+$parentContext.$index()+$index()}" class="form-control small-form-control" max="45" pattern="[a-zA-Z0-9_ ]{2,45}">

																<!-- ko if: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW && $parents[1].enableConfig() -->
																	<span class="input-group-addon glyphicon glyphicon-remove buttondelete input-group-btndelete" data-bind="event:{click: function(){$parents[1].removeKpiGroupValue($parentContext.$index(), $index())}}" title="Delete"></span>
																<!-- /ko-->
															</div>
														</div>
													</div>
												<!-- /ko-->
											<!-- /ko-->
										</td>

										<td>
											<select class="chosen" data-bind="event:{change: function(){$parent.checkInactive($data.producers.all, '#'
											+'producer'+$index(), 'producerId')}}, enable: $parent.enableConfig(), foreach : $data.producers.all, attr: {id: $parent.modalCompType()+'grpProducer'+$index()},
											event: {'chosen:showing_dropdown': function(){$parent.onChosenDropdownOpen($parent.modalCompType()+'grpProducer'+$index(), 'advSettingsPanel')}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose($parent.modalCompType()+'grpProducer'+$index(), 'advSettingsPanel')}}" data-placeholder=" ">

												<!-- ko if: $index() == 0 -->
													<option data-bind="value: 0, text: 'Select Producer'"></option>
												<!-- /ko-->

												<!-- ko if: $data.producerId -->
													<option data-bind="value: $data.producerId, text: $data.producerName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
												<!-- /ko-->
											</select>
										</td>

										<td style="padding-right: 12px;">
											<input class="form-control" type="number" data-bind="enable: $parent.enableConfig(), attr: {'id': $parent.modalCompType()+'grpCollInterval'+$index()}" min="1" style="display: inline; width: 60%"></input>

											<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), attr: {'id': $parent.modalCompType()+'grpCollIntervalUnit'+$index()},
											event: {'chosen:showing_dropdown': function(){$parent.onChosenDropdownOpen($parent.modalCompType()+'grpCollIntervalUnit'+$index(), 'advSettingsPanel')}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose($parent.modalCompType()+'grpCollIntervalUnit'+$index(), 'advSettingsPanel')}}"">
												<option value="seconds">Seconds</option>
												<option value="minutes">Minutes</option>
											</select>
										</td>

										<td style="text-align:center">
											<input type="checkbox" data-bind="enable: $parent.enableConfig(), attr: {'class': $parent.modalCompType()+'grpChkboxMappingCol', 'id': $parent.modalCompType()+'grpStatus'+$index()}" selected></input>
										</td>
									</tr>
								</tbody>
							</table>

						</div>

					</div>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" data-bind="attr: {'id': modalCompType()+'compinstance-tokenfield-typeahead', 'class': 'form-control ' + modalCompType()+'tokenfield'}, value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && currentViewIndex() != uiConstants.common.CLONE_VIEW && modalCompType() == ''">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" data-bind="attr: {'id': modalCompType()+'configStatus'}, checked: configStatus"> -->

					<input type="checkbox" data-bind="attr: {'id': modalCompType()+'configStatus', 'name': modalCompType()+'configStatus'}"" data-on-color="success" data-off-color="danger" data-size="mini">

				</div>
			</div>


			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button id="saveBtn" type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>

			<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" data-backdrop="static" data-keyboard="false">
		   		<div class="modal-dialog modal-lg">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4><span data-bind="text: modalTitle"></span></h4>
			            </div>	
			            <!-- <div data-bind="component: comp"> -->
			            <div data-bind="if : displayComponent() == 1">
			        		<comp-type-add-edit params="{isModal:true, compType: compType, currentViewIndex: currentViewIndex}"></comp-type-add-edit>
			        	</div>

			        	<div data-bind="if : displayComponent() == 2">
			        		<component-add-edit params="{isModal:true, currentViewIndex: currentViewIndex, compName: compName, componentsArr: componentsArr}"></component-add-edit>
			        	</div>

			        	<div data-bind="if : displayComponent() == 3">
			        		<!-- <component-instance-add-edit params="{isModal:true, currentViewIndex: currentViewIndex, compName: compName, componentsArr: componentsArr, modalCompType: 'Host'}"></component-instance-add-edit> -->

			        		<component-instance-add-edit params="{isModal:true, compName: compName, modalCompType: 'hostModal', componentsArr: componentsArr, applicationsArr: applicationsArr, selectedConfigRows: modalParam, curPage: 1, pageSelected: pageSelected}"></component-instance-add-edit>

			        	</div>

			        	<!-- <div data-bind="if : displayComponent() == 3">
			        		<cluster-add-edit params="{isModal:true, currentViewIndex: currentViewIndex, compType: compType, componentsArr: componentsArr}"></cluster-add-edit>
			        	</div> -->
			        </div>
		        </div> <!-- /.modal-content -->
		    </div>

		

		</form>

		

	</div>
</div>