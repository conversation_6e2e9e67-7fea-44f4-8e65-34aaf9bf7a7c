define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./grpc-settings-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','jQuery-plugins','fsstepper'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,jQueryPlugins,fsstepper) {

	function GrpcSettingsAddEdit(params) {
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = ko.observableArray();
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
		this.dataCommHostsArr = params.dataCommHostsArr;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableConfig = ko.observable(true);
  		this.selectedPlaceholdersArr = ko.observableArray();
  		this.modalTitle = ko.observable();
  		this.isModal = ko.observable(false);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		this.dataCommPort = ko.observable();

		var configTagLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$("#hostsList").chosen({}).on('chosen:showing_dropdown', function(){
				if($("#hostsList_chosen span").text() != uiConstants.grpcSettings.ENTER_SELECT_GRPC_HOST){
					$("#hostsList_chosen .chosen-search").find("input").val($("#hostsList_chosen span").text());
				}
			});

			$("#hostsList_chosen .chosen-search").find("input").on("keyup", function (evt) {		
				if($(this).val() != undefined && $(this).val() != ""){
		        	$("#hostsList_chosen span").text($(this).val());
				}

				if(evt.which == 13){
					$("#hostsList_chosen").parent().trigger("click");
				}
		    });

		    $("#hostsList").chosen({}).on('chosen:hiding_dropdown', function(){
				var selHostTxt = "";

        		if(!$('#compNameList_chosen .chosen-results').find("li.active-result.result-selected")){
        			selHostTxt = $('#hostsList_chosen .chosen-search input[type="text"]').val();
        		}
           		
	           	if(selHostTxt!=""){
	           		$('#hostsList').val("0").trigger('chosen:updated');
           			$("#hostsList_chosen span").text(selHostTxt);

	           		$('#hostsList > option').each(function(){
	 					if($(this).text()==selHostTxt) {
	 						$(this).parent('select').val($(this).val());
	 						$('#hostsList').val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});

			$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });

			self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			$('#grpc-tokenfield-typeahead')
			.on('tokenfield:createdtoken', function (e) {

				if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
					tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
				}
				var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
				//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
				if(tagIndex != -1){
					self.configTagAutoCompleteArr.splice(tagIndex, 1);
				}

				$('#grpc-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			})

			.on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			.on('tokenfield:removedtoken', function (e) {
				if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
					tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
				}
				tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
				var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

				if(tagIndex != -1){
					self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
					self.configTagAutoCompleteArr.sort();
				}

				$('#grpc-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			});

	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=grpcSettings", "GET", "", "getGrpcSettingsTag", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
        		if(self.isModal()){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

       	//Adding/Updating single GRPC settings
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();
			var tagsArr = [];
			var tagsObjArr = [];
			var configData;

			$("#divGrpcSettings #txtName").val($("#divGrpcSettings #txtName").val().trim());
			$("#divGrpcSettings #txtDescription").val($("#divGrpcSettings #txtDescription").val().trim());
			
			if($("#divGrpcSettings #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.grpcSettings.GRPC_SETTINGS_NAME_REQUIRED);
				showError("#divGrpcSettings #txtName", uiConstants.grpcSettings.GRPC_SETTINGS_NAME_REQUIRED);
			    self.errorMsg("#divGrpcSettings #txtName");
			}
			else if($("#divGrpcSettings #txtName").val().length < 2){
				//self.errorMsg(uiConstants.grpcSettings.GRPC_SETTINGS_NAME_MIN_LENGTH_ERROR);
				showError("#divGrpcSettings #txtName", uiConstants.grpcSettings.GRPC_SETTINGS_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divGrpcSettings #txtName");
			}
			else if($("#divGrpcSettings #txtName").val().length > 45){
				//self.errorMsg(uiConstants.grpcSettings.GRPC_SETTINGS_NAME_MAX_LENGTH_ERROR);
				showError("#divGrpcSettings #txtName", uiConstants.grpcSettings.GRPC_SETTINGS_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divGrpcSettings #txtName");
			}
			else if(!nameValidation($("#divGrpcSettings #txtName").val())){
				//self.errorMsg(uiConstants.grpcSettings.GRPC_SETTINGS_NAME_INVALID_ERROR);
				showError("#divGrpcSettings #txtName", uiConstants.grpcSettings.GRPC_SETTINGS_NAME_INVALID_ERROR);
			    self.errorMsg("#divGrpcSettings #txtName");
			}
			
			if($("#divGrpcSettings #txtDescription").val().trim() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divGrpcSettings #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divGrpcSettings #txtDescription");
			}
			else if($("#divGrpcSettings #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divGrpcSettings #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divGrpcSettings #txtDescription");
			}
			else if($("#divGrpcSettings #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divGrpcSettings #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divGrpcSettings #txtDescription");
			}
			
			if($("#divGrpcSettings #hostsList_chosen span")[0].innerHTML == uiConstants.agentConfig.ENTER_SELECT_HOST){
				showError("#divGrpcSettings #hostsList_chosen", uiConstants.grpcSettings.ENTER_SELECT_DATA_COMM_HOST);
				showError("#divGrpcSettings #hostsList_chosen span", uiConstants.grpcSettings.ENTER_SELECT_DATA_COMM_HOST);
			    self.errorMsg("#hostsList_chosen");
			}
			
			if(!$("#divGrpcSettings #txtDataCommPort").val()){
				//self.errorMsg(uiConstants.grpcSettings.ENTER_DATA_COMM_PORT);
				showError("#divGrpcSettings #txtDataCommPort", uiConstants.grpcSettings.ENTER_DATA_COMM_PORT);
			    self.errorMsg("#divGrpcSettings #txtDataCommPort");
			}
			else if((parseInt(self.dataCommPort()) < 0 || parseInt(self.dataCommPort()) > uiConstants.common.MAX_PORT_RANGE)){
				//self.errorMsg(uiConstants.grpcSettings.COM_PROTOCOL_PORT_INVALID);
				showError("#divGrpcSettings #txtDataCommPort", uiConstants.grpcSettings.COM_PROTOCOL_PORT_INVALID);
			    self.errorMsg("#divGrpcSettings #txtDataCommPort");
			}
			
			removeError("#divGrpcSettings .tokenfield");
			removeError("#divGrpcSettings #grpc-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divGrpcSettings #grpc-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divGrpcSettings .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divGrpcSettings #grpc-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divGrpcSettings .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divGrpcSettings .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divGrpcSettings #grpc-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divGrpcSettings .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divGrpcSettings .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divGrpcSettings #grpc-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divGrpcSettings .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divGrpcSettings .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divGrpcSettings #grpc-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divGrpcSettings .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}
				}

				if(self.errorMsg() == ""){
					var selHostArr=[];
					var selHostStr="";

					if($("#hostsList_chosen span")[0].innerHTML != uiConstants.agentConfig.ENTER_SELECT_HOST){
						selHostArr = $.grep(self.dataCommHostsArr(), function(e){ return e == $("#hostsList_chosen span")[0].innerHTML; });
						if(selHostArr.length){
							selHostStr = selHostArr[0].split("(Inactive)")[0].trim().split("(").length > 1 ? selHostArr[0].split("(")[1].split(")")[0].trim() : selHostArr[0];
						}
						else{
							selHostStr = $("#hostsList_chosen span")[0].innerHTML.trim();
						}
					}

					var configData = {"index":1,
						"name": self.configName(),
						"description":  self.configDescription().trim(),
						"host": selHostStr,
						"port": self.dataCommPort(),
						"tags": tagsObjArr,
						"status" : self.configStatus()?1:0};

					if(self.configId() == 0)
						requestCall(uiConstants.common.SERVER_IP + "/grpcSettings", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
					else
						requestCall(uiConstants.common.SERVER_IP + "/grpcSettings/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
				}
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();			
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			setConfigUneditable();			
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(){
			if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$("#grpcAddEdit .chosen-container b").css("display", "none");
				
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#hostsList').prop('disabled', true).trigger("chosen:updated");
				$('#txtDataCommPort').prop('readonly', true);
				$('#grpc-tokenfield-typeahead').tokenfield('readonly');
			}
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW || !self.selectedConfigRows() && self.selectedConfigRows()[0].status){
				//$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#grpcAddEdit .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].name);
				self.configId(configObj[0].id);
				self.configDescription(configObj[0].description);
			}

			$("#hostsList").val(configObj[0].communicationProtocolHost).trigger('chosen:updated');
			
			var selHostArr = $.grep(self.dataCommHostsArr(), function(e){ return e == self.selectedConfigRows()[0].hostName+" ("+self.selectedConfigRows()[0].host+")"; });

			if(selHostArr.length){
				$("#hostsList option:contains(" + self.selectedConfigRows()[0].hostName +" ("+self.selectedConfigRows()[0].host +"))").attr('selected', 'selected');
			}
			else if(self.selectedConfigRows()[0].host){
				$("#hostsList option:contains(" + self.selectedConfigRows()[0].host + ")").attr('selected', 'selected');
			}
			else if(self.selectedConfigRows()[0].hostName){
				$("#hostsList option:contains(" + self.selectedConfigRows()[0].hostName + ")").attr('selected', 'selected');
			}

			$("#hostsList").trigger('chosen:updated');
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			self.dataCommPort(configObj[0].port);
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#grpc-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			self.errorMsg("");
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("GRPC Settings");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getGrpcSettingsTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);
				}

				$('#grpc-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#grpc-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#grpc-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_GRPC_SETTINGS,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.grpcSettings.ERROR_ADD_GRPC_SETTINGS, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.configName());
					}
					//  
					else{
						params.addUpdateFlag(true);
						params.curPage(1);
					}
					self.cancelConfig();

					showMessageBox(uiConstants.grpcSettings.SUCCESS_ADD_GRPC_SETTINGS);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_GRPC_SETTINGS,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.grpcSettings.ERROR_UPDATE_GRPC_SETTINGS, "error");
					}
				}
				else{
					showMessageBox(uiConstants.grpcSettings.SUCCESS_UPDATE_GRPC_SETTINGS);
					self.cancelConfig();
					if(params){
						params.addUpdateFlag(true);
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getGrpcSettingsTag"){
				showMessageBox(uiConstants.grpcSettings.ERROR_GET_GRPC_SETTINGS_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.grpcSettings.ERROR_ADD_GRPC_SETTINGS, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.grpcSettings.ERROR_UPDATE_GRPC_SETTINGS, "error");
			}
		}
	}

	GrpcSettingsAddEdit.prototype.dispose = function() { };
	return { viewModel: GrpcSettingsAddEdit, template: templateMarkup };
});