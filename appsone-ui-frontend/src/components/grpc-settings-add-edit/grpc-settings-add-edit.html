<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="grpcAddEdit">
 	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body" id="divGrpcSettings">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Data Communication Host <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="hostsList" data-bind="foreach : dataCommHostsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="text: uiConstants.agentConfig.ENTER_SELECT_HOST"></option>
						<!-- /ko-->
						
						<!-- ko if: $data -->
							<option data-bind="text: $data"></option>
						<!-- /ko-->
					</select>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Data Communication Port <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="number" class="form-control positive-integer" id="txtDataCommPort" data-bind="value: dataCommPort" placeholder="Enter Port" min="0" max="65535" required>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="grpc-tokenfield-typeahead" data-bind="enable: enableConfig(), value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 5">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>