<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default">
	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body" id="divSeverityProfile">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>

			<div class="form-group form-required" >
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: profileName,attr:{'title':uiConstants.severityProfile.SEVERITY_MULTIPLE_NAME_LENGTH_ERROR}" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Description<span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: profileDescription,attr:{'title':uiConstants.severityProfile.SEVERITY_DESC_INFO}" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

 			<div class="form-group" id="divKpiDataType">
			   <label class="control-label col-sm-2" >Alert Profile Type <span class="mandatoryField">*</span></label>
			   <div class="col-sm-4">
					<select class="chosen form-control" id="alertProfileType" 
							data-bind="foreach : alertProfileTypesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE"></option>
						<!-- /ko-->
						<option data-bind="value: $data.masterId, text: $data.name"></option>
					</select>
			   </div>
			</div>
			
			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="severity-tokenfield-typeahead" data-bind="value: tags" >
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<input type="checkbox" id="severityStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="severityStatus">
				</div>
			</div>

			<div id="divTxnPattern" class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Severity Settings</div>
					<div class="panel-body" style="padding:15px" >

						<table class="checklist-div-table">
							<tr>
								<td style="width:20%" >
									<div class="panel panel-default inner-panel">
										<div class="configPanel panel-heading">High</div>
										<div class="panel-body" style="padding:15px" >
											<div class="form-group">
												<label class="control-label col-sm-4" >Status</label>
												<div class="col-sm-4">
													<input type="checkbox" id="highStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="highStatus">
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-sm-4" >Persistence<span data-bind="visible:highStatus" class="mandatoryField">*</span></label>
												<div class="col-sm-4">
													<input type="number" min="1" max="1440" class="form-control" id="highPersistence"  data-bind="enable : highStatus()">
												</div>
											</div>
										</div>
									</div>
								</td>

								<!-- <td style="width: 40px"></td> -->

								<td style="width: 20%" data-bind="style:{'visibility':(getAlertProfileTypeSelected() ==  'CORE' || getAlertProfileTypeSelected() ==  'TRANSACTION') ? 'visible' : 'hidden'}">
									
									<div class="panel panel-default inner-panel">
										<div class="configPanel panel-heading">Medium</div>
										<div class="panel-body" style="padding:15px" >
											<div class="form-group">
												<label class="control-label col-sm-4" >Status</label>
												<div class="col-sm-4">
													<input type="checkbox" id="mediumStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="mediumStatus">
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-sm-4" >Persistence<span data-bind="visible:mediumStatus" class="mandatoryField">*</span></label>
												<div class="col-sm-4">
													<input type="number" min="1" max="1440" class="form-control" id="mediumPersistence"  data-bind="enable : mediumStatus()">
												</div>
											</div>
										</div>
									</div>
								</td>

								<!-- <td style="width: 40px"></td> -->

								<td style="width: 20%" data-bind="style:{'visibility':(getAlertProfileTypeSelected() ==  'CORE' || getAlertProfileTypeSelected() ==  'TRANSACTION') ? 'visible' : 'hidden'}">
									<div class="panel panel-default inner-panel">
										<div class="configPanel panel-heading">Low</div>
										<div class="panel-body" style="padding:15px" >
											<div class="form-group">
												<label class="control-label col-sm-4" >Status</label>
												<div class="col-sm-4">
													<input type="checkbox" id="lowStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="lowStatus">
												</div>
											</div>
											<div class="form-group">
												<label class="control-label col-sm-4" >Persistence<span data-bind="visible:lowStatus" class="mandatoryField">*</span></label>
												<div class="col-sm-4">
													<input type="number" min="1" max="1440" class="form-control" id="lowPersistence"  data-bind="enable : lowStatus()">
												</div>
											</div>
										</div>
									</div>
								</td>
								
							</tr>
						</table>	
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5, event:{click: addEditSeverity}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelAddScreen}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>


			
		</form>

		<!-- data-bind="attr:{'id': modalCompType()+'imgAttribShowHide'}, event: {click: expandCollapsePanel.bind($data, '#'+modalCompType()+'attribsPanel', '#'+modalCompType()+'imgAttribShowHide')}" -->
	</div>
</div>
