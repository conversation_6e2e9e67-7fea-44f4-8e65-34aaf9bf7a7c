define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead', 'text!./severity-profile-add-edit.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon) {

	function severityAddEdit(params){
		var self = this;

		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var configTagLoaded = 0;
  		var previousAlertProfTypeId;

		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");

		//params passed from severity list view
		this.selectedConfigRows = params.selectedConfigRows;
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.pageSelected = params.pageSelected;
		this.alertProfileTypesArr = params.alertProfileTypesArr;

		//other common variables
		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		this.selectedConfigNames = ko.observable("");
		this.tags = ko.observable();
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();

		this.profileId = ko.observable(0);
		this.profileName = ko.observable();
		this.profileDescription = ko.observable();
		this.profileStatus = ko.observable(true);
		this.highStatus = ko.observable();
		this.mediumStatus = ko.observable();
		this.lowStatus = ko.observable();
		this.getAlertProfileTypeSelected = ko.observable();
  		this.isModal = ko.observable(false);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();


		this.renderHandler = function(){
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			if(params.isModal){
				self.isModal(true);
				previousAlertProfTypeId = $("#alertProfileType").val();
				$("#divSeverityProfile #alertProfileType").val(params.profileTypeId()).trigger("chosen:updated");
				resetSeveritySettings();
			}

			self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			$("#severityStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});
			$('#severityStatus').bootstrapSwitch('state', self.profileStatus());
			$("#severityStatus").on('switchChange.bootstrapSwitch', function () {
				self.profileStatus($('#severityStatus').bootstrapSwitch('state')?1:0);
			});

			//High
			$("#lowStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});
			$('#lowStatus').bootstrapSwitch('state', self.lowStatus());
			$("#lowStatus").on('switchChange.bootstrapSwitch', function () {
				self.lowStatus($('#lowStatus').bootstrapSwitch('state')?(self.currentViewIndex() == uiConstants.common.READ_VIEW ? 0 : 1):0);
			});

			//medium
			$("#mediumStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});
			$('#mediumStatus').bootstrapSwitch('state', self.mediumStatus());
			$("#mediumStatus").on('switchChange.bootstrapSwitch', function () {
				self.mediumStatus($('#mediumStatus').bootstrapSwitch('state')?(self.currentViewIndex() == uiConstants.common.READ_VIEW ? 0 : 1):0);
			});

			//low
			$("#highStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});
			$('#highStatus').bootstrapSwitch('state', self.highStatus());
			$("#highStatus").on('switchChange.bootstrapSwitch', function () {
				self.highStatus($('#highStatus').bootstrapSwitch('state')?(self.currentViewIndex() == uiConstants.common.READ_VIEW ? 0 : 1):0);
			});

			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');

			//tag changes
			$('.panel-body #severity-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#severity-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#severity-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			requestCall(uiConstants.common.SERVER_IP + "/tag?type=SeverityProfile", "GET", "", "getSeverityTag", successCallback, errorCallback);
		
			//onMastersLoad();//need to remove this later when tag is implemented

			$("#alertProfileType").on('chosen:showing_dropdown', function () {
				previousAlertProfTypeId = $("#alertProfileType").val();
			})
			.on('change', function () {
		    	if(previousAlertProfTypeId == 0){
					resetSeveritySettings();
			    }

			    else{
		    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClearCompInst){
						if(confirmClearCompInst){
							resetSeveritySettings();
		    			}
			    		else{
			    			$("#alertProfileType").val(previousAlertProfTypeId).trigger("chosen:updated");
			    		}
	    			});
			   	}
			});


			/*$("#alertProfileType").on('change',function(e){
				self.getAlertProfileTypeSelected($("#alertProfileType option:selected").text().toUpperCase());	
			});*/	
		}

		function resetSeveritySettings(){
			self.getAlertProfileTypeSelected($("#alertProfileType option:selected").text().toUpperCase());	
			$('#highStatus').bootstrapSwitch('state',false);
			$('#mediumStatus').bootstrapSwitch('state',false);
			$('#lowStatus').bootstrapSwitch('state',false);

			$("#highPersistence").val("");
			$("#mediumPersistence").val("");
			$("#lowPersistence").val("");
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		/////////////////copy pasted

		/*Get tagid from tagname : START*/
		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(objIndx in self.configTagArr()){
				if(self.configTagArr()[objIndx].tagName.trim() == tagName.trim()){
					return self.configTagArr()[objIndx].tagId;
				}
			}
			return 0;
		}
		/*END*/

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
					if(!self.selectedConfigRows()[0].status){ //if the component is inactive
						setConfigUneditable(true);
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		/************Adding/Updating single severity profile************/
		this.addEditSeverity = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var severityObj= {};

			$("#divSeverityProfile #txtName").val($("#divSeverityProfile #txtName").val().trim());
			$("#divSeverityProfile #txtDescription").val($("#divSeverityProfile #txtDescription").val().trim());

			if($("#divSeverityProfile #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_REQUIRED);
				showError("#divSeverityProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_REQUIRED);
			    self.errorMsg("#divSeverityProfile #txtName");
			}	
			else if($("#divSeverityProfile #txtName").val().length < 2){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MIN_LENGTH_ERROR);
				showError("#divSeverityProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divSeverityProfile #txtName");
			}
			else if($("#divSeverityProfile #txtName").val().length > 45){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MAX_LENGTH_ERROR);
				showError("#divSeverityProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divSeverityProfile #txtName");
			}
			else if(!nameValidation($("#divSeverityProfile #txtName").val())){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_INVALID_ERROR);
				showError("#divSeverityProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_INVALID_ERROR);
			    self.errorMsg("#divSeverityProfile #txtName");
			}
			if($("#divSeverityProfile #txtDescription").val().trim()  == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divSeverityProfile #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divSeverityProfile #txtDescription");
			}
			else if($("#divSeverityProfile #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divSeverityProfile #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divSeverityProfile #txtDescription");
			}
			else if($("#divSeverityProfile #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divSeverityProfile #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divSeverityProfile #txtDescription");
			}
			if($("#divSeverityProfile #alertProfileType").chosen().val() == "0"){
				//self.errorMsg(uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
				showError("#divSeverityProfile #alertProfileType_chosen", uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
				showError("#divSeverityProfile #alertProfileType_chosen span", uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
			    self.errorMsg("#divSeverityProfile #alertProfileType_chosen");
			}
			else{
				removeError("#divSeverityProfile #alertProfileType_chosen");
				removeError("#divSeverityProfile #alertProfileType_chosen span");
			}

			removeError("#divSeverityProfile .tokenfield");
			removeError("#divSeverityProfile #severity-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divSeverityProfile #severity-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divSeverityProfile .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divSeverityProfile #severity-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divSeverityProfile .tokenfield");
			}
			if($('#highStatus').bootstrapSwitch('state') == true && $('#highPersistence').val() == ""){
				//self.errorMsg(uiConstants.severityProfile.HIGH_PERSISTENT_VALUE);
				showError("#divSeverityProfile #highPersistence", uiConstants.severityProfile.HIGH_PERSISTENT_VALUE);
			    self.errorMsg("#divSeverityProfile #highPersistence");
			}
			else if($('#highPersistence').val() != "" && $('#highPersistence').val() <= 0){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_HIGH_VALUE);
				showError("#divSeverityProfile #highPersistence", uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_HIGH_VALUE);
			    self.errorMsg("#divSeverityProfile #highPersistence");
			}
			else if($('#highPersistence').val() != "" && $('#highPersistence').val() > 1440){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_HIGH_VALUE);
				showError("#divSeverityProfile #highPersistence", uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_HIGH_VALUE);
			    self.errorMsg("#divSeverityProfile #highPersistence");
			}
			if(($('#mediumStatus').bootstrapSwitch('state') == true && $('#mediumPersistence').val() == "") && (self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION')){
				//self.errorMsg(uiConstants.severityProfile.MEDIUM_PERSISTENT_VALUE);
				showError("#divSeverityProfile #mediumPersistence", uiConstants.severityProfile.MEDIUM_PERSISTENT_VALUE);
			    self.errorMsg("#divSeverityProfile #mediumPersistence");
			}
			else if($('#mediumPersistence').val() != "" && $('#mediumPersistence').val() <= 0){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_MEDIUM_VALUE);
				showError("#divSeverityProfile #mediumPersistence", uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_MEDIUM_VALUE);
			    self.errorMsg("#divSeverityProfile #mediumPersistence");
			}
			else if($('#mediumPersistence').val() > 1440){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_MEDIUM_VALUE);
				showError("#divSeverityProfile #mediumPersistence", uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_MEDIUM_VALUE);
			    self.errorMsg("#divSeverityProfile #mediumPersistence");
			}
			if(($('#lowStatus').bootstrapSwitch('state') == true && $('#lowPersistence').val() == "") && (self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') ){
				//self.errorMsg(uiConstants.severityProfile.LOW_PERSISTENT_VALUE);
				showError("#divSeverityProfile #lowPersistence", uiConstants.severityProfile.LOW_PERSISTENT_VALUE);
			    self.errorMsg("#divSeverityProfile #lowPersistence");
			}
			else if($('#lowPersistence').val() != "" && $('#lowPersistence').val() <= 0){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_LOW_VALUE);
				showError("#divSeverityProfile #lowPersistence", uiConstants.severityProfile.ERROR_MIN_PERSISTENCE_LOW_VALUE);
			    self.errorMsg("#divSeverityProfile #lowPersistence");
			}
			else if($('#lowPersistence').val() > 1440){
				//self.errorMsg(uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_LOW_VALUE);
				showError("#divSeverityProfile #lowPersistence", uiConstants.severityProfile.ERROR_MAX_PERSISTENCE_LOW_VALUE);
			    self.errorMsg("#divSeverityProfile #lowPersistence");
			}
			//else{
				

				//if(self.errorMsg() == ""){
					if(self.tags() && self.tags().trim().length == 1)
						tagsArr.push(self.tags());

					else if(self.tags() && self.tags().trim().length > 1)
						tagsArr = self.tags().split(",");
					
					for(var t in tagsArr){
						if(tagsArr[t].trim().length < 2){
							//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("#divSeverityProfile .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("#divSeverityProfile #severity-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("#divSeverityProfile .tokenfield");
							break;
						}
						else if(tagsArr[t].trim().length > 45){
							//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("#divSeverityProfile .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("#divSeverityProfile #severity-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						    self.errorMsg("#divSeverityProfile .tokenfield");
							break;
						}
						else if(!tagValidation(tagsArr[t].trim())){
							//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
							showError("#divSeverityProfile .tokenfield", uiConstants.common.INVALID_TAG_NAME);
							showError("#divSeverityProfile #severity-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
						    self.errorMsg("#divSeverityProfile .tokenfield");
							break;
						}
					}

					if(self.errorMsg() == ""){
						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}

						severityObj["high"]={"status":$('#highStatus').bootstrapSwitch('state')?1:0, "persistence":$("#highPersistence").val() == ""?0:parseInt($("#highPersistence").val())};
						severityObj["medium"]={"status":$('#mediumStatus').bootstrapSwitch('state')?1:0, "persistence":$("#mediumPersistence").val() == ""?0:parseInt($("#mediumPersistence").val())};
						severityObj["low"]={"status":$('#lowStatus').bootstrapSwitch('state')?1:0, "persistence":$("#lowPersistence").val() == ""?0:parseInt($("#lowPersistence").val())};
						
						var severityObj = {
						"index":1,
						"id":self.profileId(),
						"name": self.profileName().trim(),
						"description": self.profileDescription(),
						"alertProfileTypeId":($("#divSeverityProfile #alertProfileType").chosen().val() == "Select" || $("#alertProfileType").chosen().val() == null) ? 0 : parseInt($("#alertProfileType").chosen().val()),
						"settings":severityObj,
						"tags": tagsObjArr,
						"status" : self.profileStatus()?1:0};//self.configStatus()?1:0

						if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(severityObj));

						if(self.profileId() == 0)
							requestCall(uiConstants.common.SERVER_IP + "/severityProfile", "POST", JSON.stringify(severityObj), "addSingleConfig", successCallback, errorCallback);
						else
							requestCall(uiConstants.common.SERVER_IP + "/severityProfile/" + self.profileId(), "PUT", JSON.stringify(severityObj), "editSingleConfig", successCallback, errorCallback);

						
					}
				//}

			//}
			
		}

		/************Editing single severity profile************/
		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();	
		}

		function setConfigValues(severityObj){
			if(uiConstants.common.DEBUG_MODE)console.log(severityObj[0]);
			for(var tagObj in severityObj[0].tags){
				tagsNameArr.push(severityObj[0].tags[tagObj].tagName);
			}

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.profileName("");
				self.profileDescription("");
			}
			else{
				self.profileName(severityObj[0].name);
				self.profileId(severityObj[0].id);
				self.profileDescription(severityObj[0].description);
			}
			
			
			self.profileStatus(severityObj[0].status);
			$('#severityStatus').bootstrapSwitch('state',self.profileStatus());
			$("#alertProfileType").val(severityObj[0].alertProfileTypeId).prop('disabled', true).trigger('chosen:updated');
			self.getAlertProfileTypeSelected($("#alertProfileType option:selected").text().toUpperCase());	
			$('#severity-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			var data=severityObj[0].settings;
			$('#highStatus').bootstrapSwitch('state',data['high'].status);
			$('#mediumStatus').bootstrapSwitch('state',data['medium'].status);
			$('#lowStatus').bootstrapSwitch('state',data['low'].status);

			if(data['high'].persistence == 0){
				$("#highPersistence").val("");
			}
			else{
				$("#highPersistence").val(data['high'].persistence);
			}

			if(data['medium'].persistence == 0){
				$("#mediumPersistence").val("");
			}
			else{
				$("#mediumPersistence").val(data['medium'].persistence);
			}

			if(data['low'].persistence == 0){
				$("#lowPersistence").val("");
			}
			else{
				$("#lowPersistence").val(data['low'].persistence);
			}
		}

		/************View single severity profile************/
		function viewConfig(configObj){
			//self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){

			if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$("#alertProfileType").prop('disabled', true).trigger('chosen:updated');
				$('#severity-tokenfield-typeahead').tokenfield('readonly');

				$("[name='highStatus']").bootstrapSwitch('disabled',true);
				$("[name='mediumStatus']").bootstrapSwitch('disabled',true);
				$("[name='lowStatus']").bootstrapSwitch('disabled',true);

				$('#highPersistence').prop('readonly', true);
				$('#mediumPersistence').prop('readonly', true);
				$('#lowPersistence').prop('readonly', true);
			}

			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("[name='severityStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divSeverityProfile .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		this.cancelAddScreen = function(){
			if(params.isModal){
				//$(".modal-header button").click();
				$("#idModalAlertProfile").modal("hide");
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Severity Profile");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
				self.errorMsg("");
			}
		}

		/**************Result and Error handlers ****************/
		function successCallback(data, reqType) {
			 if(reqType === "getSeverityTag"){
			 	self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #severity-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#severity-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('.panel-body #severity-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#severity-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			 }
			 else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_SEVERITY_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.severityProfile.ERROR_ADD_SEVERITY, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.profileName());
					}
					//  
					else{
						params.curPage(1);
					}
					self.cancelAddScreen();

					showMessageBox(uiConstants.severityProfile.SUCCESS_ADD_SEVERITY);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_SEVERITY_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.severityProfile.ERROR_UPDATE_SEVERITY, "error");
					}
				}
				else{
					showMessageBox(uiConstants.severityProfile.SUCCESS_UPDATE_SEVERITY);
					self.cancelAddScreen();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.severityProfile.ERROR_ADD_SEVERITY, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.severityProfile.ERROR_UPDATE_SEVERITY, "error");
			}
		}


	}
	severityAddEdit.prototype.dispose = function() { };
	return { viewModel: severityAddEdit, template: templateMarkup };
});