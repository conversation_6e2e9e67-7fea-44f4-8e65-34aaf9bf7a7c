define(['jquery','knockout', 'text!./sparkline-chart.html', 'knockout-es5','d3','c3'], function($, ko, templateMarkup, koES5, d3, c3){

	function SparkLineChart(params) {
    	var self = this;

    	self.podId = params.podId;
    	self.chartDataObj = params.chartDataObj;
    	self.chartContId = self.chartDataObj.chartContId;

    	self.renderHandler = function(){
    		self.sparkInitChart(self.chartDataObj.chartContId, self.chartDataObj.chartHeight, self.chartDataObj.chartWidth, self.chartDataObj.alertTrendArray);
    	}

    	/**
	     * sparkinitChart function will generate Spark line for given values
	     * @param  {[id selector]} chartCont       ID selector to which Spark lines to be binded to
	     * @param  {[number]} chartHeight     - Height to be assigned to the Chart Container
	     * @param  {[number]} chartWidth      - Width to be assigned to the Chart Container
	     * @param  {[array]} chartDataPoints - Data Points which will be shown in the Chart
	     * @return {[type]}                 [description]
	     */
	    self.sparkInitChart = function(chartCont, chartHeight, chartWidth, chartDataPoints){        
    		var alertTrendArray = [];
	        alertTrendArray.push('data1'); // Datum Name to be the first Elem in the DataPoint Array
	        alertTrendArray.push.apply(alertTrendArray, chartDataPoints); // Merge the 2 argumnet-arrays

	        var chart1 = c3.generate({
	            bindto: document.getElementById(chartCont),
	            size: {
	                width: parseInt($("#"+chartCont).width()),
	                height : 25,
                },
	            grid: {
	                focus: {
	            	    show: false
	                }
	            },
	            area: {
	                zerobased: true
	            },
	            data: {
	                columns: [alertTrendArray],                
	                types: {
	                    data1: 'area-spline'
	                },
	                
	            },
	            point: {
	                show: false
	            },
	            legend: {
	                show: false,
	            },
	            axis:{
	                x:{
	                    show:false,
	                },
	                y:{
	                    show:false,                    
	                }
	            },
	            tooltip: {
	                show: true,
	                grouped : false,
	                contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                      	var $$ = this, config = $$.config,
	                    titleFormat = config.tooltip_format_title || defaultTitleFormat,
	                    nameFormat = config.tooltip_format_name || function (name) { return name; },
	                    valueFormat = config.tooltip_format_value || defaultValueFormat,
	                    text, i, title, value, name, bgcolor;
	                    for (i = 0; i < d.length; i++) {
	                        if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }
	                        if (! text) {
	                            title = titleFormat ? titleFormat(d[i].x) : d[i].x;
	                            text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='2'>Total Count</th></tr>" : "");
	                        }
	                        name = nameFormat(d[i].name);
	                        value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
	                        bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);
	                        text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
	                        text += "<td class='value'>" + value + "</td>";
	                        text += "</tr>";
	                    }
	                    return text + "</table>";
	                }
	            }
	        });
        
	        //To draw X axis line and Y axis line using bottom and left border of svg container
	        //$("#"+chartCont+" svg").css("border-bottom","1px solid").css("border-left","1px solid");
	        $("#"+chartCont).prepend("<span style='font-size:7px'>Min = "+Math.min.apply(Math, chartDataPoints)+" & Max = "+Math.max.apply(Math, chartDataPoints)+"</span><br />");
                  
    	};
    }
    
    SparkLineChart.prototype.dispose = function() {}

	return { viewModel: SparkLineChart, template: templateMarkup };
});

