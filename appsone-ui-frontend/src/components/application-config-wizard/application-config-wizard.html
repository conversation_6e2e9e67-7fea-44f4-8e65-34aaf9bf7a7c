<!-- <span class="errorMessageField" data-bind="text: errorMsg()"></span>  -->
<application-add-edit id="appConfigWizard" params="{'panelTitle':panelTitle,'mode':mode}"></application-add-edit>
<!--<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
	<div id="componentsForSelectedAppType" >	
		<table id="compVersion" class="table" style="width:100%">
	         <thead>
	            <tr >
	               <th>Server Type</th>   
	               <th>Component<span class="mandatoryField">*</span></th>   
	               <th>Version<span class="mandatoryField">*</span></th>  
	               <th>No. of Instances<span class="mandatoryField">*</span></th>  
	               <th>Host</th>  
	               <th>Host Version</th>   
	            </tr>
	         </thead> 

            <tbody data-bind="foreach : componentsArr">
            //  ko if: $data.componentType.toUpperCase() != 'HOST' 
              <tr>
                <td  data-bind="text: $data.componentType+' Server Details',attr:{'id': $data.componentType+'|'+$data.componentTypeId}">
                </td>
                <td>
                  <select  class="form-control" data-bind="options: $data.components,
                             optionsText: 'componentName',
                             optionsValue: 'componentId',
                             optionsCaption: 'Select', attr:{ id:'compNameClass'+$index()},
                             event: {change: function(){$parent.onComponentChange($index())}}">
                  </select> 
                </td>
                <td>
                  // ko if: $parent.compVersionsArr()[$index()]
                     <select class="form-control" data-bind="options: $parent.compVersionsArr()[$index()].versions,
                                optionsText: 'version',
                                optionsValue: 'versionId',
                                optionsCaption: 'Select'">
                     </select>

                  // ko

                  // ko ifnot: $parent.compVersionsArr()[$index()]
                      <select class="form-control" data-bind="optionsText: 'Select'">

                       </select>
                  // ko

                </td>
                <td>
                  <input type="text" class="form-control" >
              </td>
                <td>
                     <select class="form-control" data-bind="options: $parent.hostsArr,
                                optionsText: 'componentName',
                                optionsValue: 'componentId',
                                optionsCaption: 'Select', attr:{ id:'hostNameClass'+$index()},
                                event: {change: function(){$parent.onHostChange($index())}}">
                     </select> 
                </td>
                <td>
                  //   ko if: $parent.hostVersionsArr()[$index()] 
                     <select class="form-control" data-bind="options: $parent.hostVersionsArr()[$index()].versions,
                                optionsText: 'version',
                                optionsValue: 'versionId',
                                optionsCaption: 'Select'">
                     </select>
                     //ko

                     //ko ifnot: $parent.hostVersionsArr()[$index()]
                      <select class="form-control" data-bind="optionsText: 'Select'">

                      </select>
                     //ko
                </td>
              </tr>
              //ko
            </tbody>
      </table>
	</div>

</form>  -->



