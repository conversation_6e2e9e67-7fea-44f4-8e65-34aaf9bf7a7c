define(['jquery','knockout','jquery-chosen','bootstrap','typeahead', 'text!./application-config-wizard.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bt,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon){

function Applicationconfigwizard(params) {
  		var self = this;

  		this.panelTitle = params.panelTitle;
  		this.mode = params.mode;

  		this.renderHandler=function(){
  			
  			
  		}

  		
  		/*this.typeArr = ko.observableArray();
  		this.timezoneArr = ko.observableArray();
  		this.displayComponent = ko.observable(false);
  		this.modalTitle = ko.observable();
  		this.appType = ko.observable("");
  		this.componentsArr = ko.observableArray();
  		this.compTypeVersionArr = ko.observableArray();
  	
  		//dynamic componnettype loading vars
  		this.compVersionsArr = ko.observableArray([]);
        this.hostVersionsArr = ko.observableArray([]);
        this.hostsArr = ko.observableArray([]);
        var hostObjIndex = 0;
        this.componentDetailsVisiblity = ko.observable(0);
        this.componentHostsArr = ko.observableArray([]);

        //application config changes 
        this.appId = ko.observable(0);
		this.appName = ko.observable();
		this.appDescription = ko.observable();
		this.errorMsg = ko.observable("");
		this.resultAppId = ko.observable(0);

  		this.renderHandler=function(){
			
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			//self.comp({name: 'app-type-add-edit',  params: {isModal:true, appType: self.appType}});
			self.modalTitle("Add Application Type");
			getAppType();
			requestCall(uiConstants.common.SERVER_IP + "/timeZones", "GET", "", "getTimezone", successCallback, errorCallback);

			$("#idModal").on('show.bs.modal', function () {
				self.displayComponent(true);
			});

			$("#idModal").on('hidden.bs.modal', function () {
				self.displayComponent(false);
				console.log(self.appType());
		        if(self.appType() && self.appType() != ""){
		        	getAppType();
		        	//$("#appTypeList").text(self.appType()).trigger('chosen:updated');
		        	//$("#appTypeList option:selected").text(self.appType()).trigger('chosen:updated');
		        }
				self.appType("");
		    });

		    //On application type change
		    $("#appTypeList").on('change', function () {
		   		onAppTypeChange();
		    });  	   

		    //$("#compVersion tbody").on('click', '#compNameClass', function(e){
	         //   var compObjIndex= $(this).closest('tr').get(0).rowIndex;
	        //    compObjIndex=(compObjIndex-1);
	         //   setVersion(compObjIndex,this,self.compVersionsArr,self.componentsArr());
	        //});


		    //for(var i=0;i<self.componentsArr().length;i++){
		    //    $("#compVersion tbody").on('click', '#hostNameClass'+i, function(e){
		    //          setVersion(hostObjIndex,this,self.hostVersionsArr,self.componentHostsArr());

		    //    });
		   // }

		    

	        //$("#savedApplicationId").css('visibility','hidden');


	       // self.$parent.enableNext="done";
		}

		this.onComponentChange = function(rowIndex){
			setVersionc(rowIndex,rowIndex,self.compVersionsArr,self.componentsArr());
		}

		this.onHostChange = function(rowIndex){
	    	setVersion(hostObjIndex,rowIndex,self.hostVersionsArr,self.componentHostsArr());
	    }

		function getAppType(){
			requestCall(uiConstants.common.SERVER_IP + "/masterTypes/application", "GET", "", "getApplicationType", successCallback, errorCallback);
		}

		function onAppTypeChange(){
			var selectedApplicationType=0;
			//request to get hostdetails
			requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getCompTypeVersionDefinition", successCallback, errorCallback);

			if($("#appTypeList").chosen().val() != "Select"){
				selectedApplicationType=$("#appTypeList").chosen().val();
			}
			//request to get associated component details
			requestCall(uiConstants.common.SERVER_IP + "/component/"+selectedApplicationType, "GET", "", "getCompTypeVersion", successCallback, errorCallback);
		}

		function setVersion(compObjIndex, rowIndex, versionArr, initialArr){
            var verObj = initialArr[compObjIndex].components;
            for(compVer in verObj){
                if(verObj[compVer].componentName == $("#hostNameClass" + rowIndex + " option:selected").text()){
                  versionArr.splice(rowIndex,1,verObj[compVer]);
                  break;
                }
            }
        }

        function setVersionc(compObjIndex, rowIndex, versionArr, initialArr){
            var verObj = initialArr[compObjIndex].components;
            for(compVer in verObj){
                if(verObj[compVer].componentName == $("#compNameClass" + rowIndex + " option:selected").text()){
                  versionArr.splice(rowIndex,1,verObj[compVer]);
                  break;
                }
            }
        }

        this.seperateHostDataFromComponentSet = function(){
        	console.log(self.componentHostsArr());
        	for(compType in self.componentHostsArr()){
	        	if(self.componentHostsArr()[compType].componentType.toUpperCase() == "HOST"){
		              hostObjIndex = compType;
		              console.log("compType"+compType);
		              console.log("isHost"+self.componentHostsArr()[compType].componentType.toUpperCase());
		              self.hostsArr(self.componentHostsArr()[compType].components);
		              console.log(self.componentHostsArr()[compType].components);
		        }
		    }
        }
		//application page save functionality :end


	    this.saveApplicationConfiguration=function(){
	      var appObjArr = [];
	      var appData;

	      self.errorMsg("");
	      $("#txtName").val($("#txtName").val().trim());
	      var compInstValidationMsg=self.jsonFormatForCompInstValidation();

	      if($("#txtName").val() == ""){
	          self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
	        }
	        else if($("#txtName").val().length < 2){
	          self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MIN_LENGTH_ERROR);
	        }
	        else if($("#txtName").val().length > 45){
	          self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MAX_LENGTH_ERROR);
	        }
	        else if(!nameValidation($("#txtName").val())){
	          self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_INVALID_ERROR);
	        }
	        else if($("#txtDescription").val().length > 256){
	          self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
	        }
	        else if($('#appTypeList').val() == "Select Application Type" ){
	          self.errorMsg(uiConstants.common.APPLICATION_TYPE_REQUIRED);
	        }
	        else if($("#timezoneList").chosen().val() == ""){
	          self.errorMsg(uiConstants.common.INVALID_TIMEZONE);
	        }
	        else if(compInstValidationMsg != ""){//check for comp instance selection 
	          self.errorMsg(compInstValidationMsg);
	        }
	        else{
	            self.errorMsg("");
	            var appObj = {
	                  "index":1,
	                  "applicationId": 0,
	                  "applicationName": $("#txtName").val().trim(),
	                  "description": $("#txtDescription").val(),
	                  "timezoneId": $("#timezoneList").chosen().val() == "Select Time Zone" ? null : $("#timezoneList").chosen().val(),
	                  "applicationType": $("#appTypeList option:selected").text()
	                  };

	            appObjArr.push(appObj);
	            appData = {"applications":appObjArr};

	            if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(appData));
	            requestCall(uiConstants.common.SERVER_IP + "/applications", "POST", JSON.stringify(appData), "addSingleApp", successCallback, errorCallback);
	        }
	    }

	    this.jsonFormatForCompInstValidation=function(){
	        console.log("------------------Json componentinstance validation---------------------------");
	        var validatedMessage="";
	        $('#compVersion tbody tr').each(function(i){    
	              $(this).children('td').each(function(j){
	              	//console.log((j==4 && $(this).children('select').val() != ""));
	                  if(j==1 && $(this).children('select').val() == ""){
	                        validatedMessage="Please select component name at line "+(i+1);
	                        return false;
	                  }    
	                  else if(j==2 && $(this).children('select').val() == ""){
	                        validatedMessage="Please select version for selected component name at line "+(i+1);
	                        return false;
	                  }
	                  else if(j==3 && $(this).children('input').val() == ""){
	                        validatedMessage="Please provide number of instances to create at line "+(i+1);
	                        return false;
	                  }
	                  else if($('#hostNameClass'+i).val() != "" &&  (j==5 && $(this).children('select').val() == "") ){						
	                        validatedMessage="Please select version for selected host name type at line "+(i+1);
	                        return false;
	                  }
	              });
	         }); 
	        return validatedMessage;
	         console.log(validatedMessage);
	    }
	    //end

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			
			if(reqType === "getApplicationType"){
				self.typeArr.removeAll();					
				self.typeArr.push.apply(self.typeArr,data.result);
				$("#appTypeList").trigger('chosen:updated');
				if(uiConstants.common.DEBUG_MODE)console.log(self.typeArr());
			}
			else if(reqType === "getTimezone"){
				self.timezoneArr.removeAll();
				self.timezoneArr.push.apply(self.timezoneArr,data.result);
				$("#timezoneList").trigger('chosen:updated');
				if(uiConstants.common.DEBUG_MODE)console.log(self.timezoneArr());
			}
			else if(reqType === "getCompTypeVersion"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.componentsArr(data.result);
				if($('#appTypeList').val() != "Select" && self.componentsArr().length > 0) {
					self.componentDetailsVisiblity(1);
				}
				else{
					self.componentDetailsVisiblity(0);
				}
				for(compType in self.componentsArr()){
		        	self.compVersionsArr.push({versions: []});
	        	}
			}
			else if(reqType === "addSingleApp"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined)
						showMessageBox(res[0].message, "error");
					else
						showMessageBox(uiConstants.applicationConfig.ERROR_ADD_APPLICATION, "error");
					$('#btnAppWizardSave').prop('disabled', false);
					$('#btnNext').prop('disabled', false);
				}
				else{	
					self.resultAppId(res[0]);//always there will be array of on element id as result
					showMessageBox(uiConstants.applicationConfig.SUCCESS_ADD_APPLICATION);
					$('#btnNext').prop('disabled', false);
					$('#btnAppWizardSave').prop('disabled', true);
				}
			}
			else if(reqType === "getCompTypeVersionDefinition"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.componentHostsArr(data.result);

				self.seperateHostDataFromComponentSet();
				
				for(compType in self.componentHostsArr()){
		       		self.hostVersionsArr.push({versions: []});
		        }
			}
		}

		function errorCallback(reqType) {
			if(reqType === "addSingleApp"){
				showMessageBox(uiConstants.applicationConfig.ERROR_ADD_APPLICATION, "error");
			}
		}	*/
    
}

Applicationconfigwizard.prototype.dispose = function() { };
return { viewModel: Applicationconfigwizard, template: templateMarkup };

});

