define(['jquery','knockout','text!./top-bar.html','hasher','ui-constants','ui-common'], function($,ko,templateMarkup,hasher,uiConstants,uicommon) {
  function TopBar(params) {
    var self = this;
    var counter = 0;
    var ele = document.createElement("div");
    var openPage = "";
    var menuList = "";
    //this.sideMenuObj = ko.observable({});

    this.renderHandler=function(){
     // if(localStorage.apptoken){
        commonMenuFunc();
        console.log(hasher.getHash());

        $('#srchTxt').keypress(function(event) {
            if (event.which == 13) {
            self.onSearchClick();
          }
        });

        if(hasher.getHash() == ""){
          $("#slide-nav").css("visibility","hidden");
        }

        /*if(hasher.getHash() == "compTypeListView" ||  hasher.getHash() == "appTypeListView"){
          var sideMenu = "";
          console.log( window.sideMenuObj());
          var curSideMenuObj = window.sideMenuObj()["list"+$(this).data("parentid")];
          for(var menu in curSideMenuObj){
            sideMenu=sideMenu+"<li><a href='" + curSideMenuObj[menu].accessLink + "'>"
              + "<span>" + curSideMenuObj[menu].name + "</span>"
              + "<span class='glyphicon glyphicon-star pull-right'></span>"
              + "</a></li>"
          }

          $("#side-nav").css("display", "");
          $(sideMenu).appendTo('#sideBarMenus');
        }*/
      //}

      $('html').on('show.bs.dropdown', function () {
         $("#slide-nav").removeClass("navbar-zindex").addClass("navbar-zindex");
      });

      $('html').on('hidden.bs.dropdown', function () {
         $("#slide-nav").removeClass("navbar-zindex");
      });

    }

    this.onSearchClick = function(){
      $("#srchTxt").val($("#srchTxt").val().trim());
      if($("#srchTxt").val() == ""){
        showMessageBox(uiConstants.globalSearch.ENTER_SEARCH_TXT);
      }
      else{
        if(window.currentPageMode != "AddUpdate"){
          window.globalSearchTxt = $("#srchTxt").val();
          hasher.setHash("");
          hasher.setHash("#globalSearch");
        }
        else{
          showMessageBox(uiConstants.common.CONFIRM_PAGE_NAVIGATION, "question", "confirm", function confirmCallback(confirmClear){
            if (confirmClear){
              window.currentPageMode = "";
              window.globalSearchTxt = $("#srchTxt").val();
              hasher.setHash("");
              hasher.setHash("#globalSearch");
            }
            else{
              $("#srchTxt").val("");
            }
          });
        }
      }
    }

    

    /*this.dashboardMenuClick = function(type){
      
      if(type == "custom"){
          hasher.setHash("cDashboard");
      }else{
          hasher.setHash("home");
      }

    }*/   

    //TODO: remove this later
    // if(localStorage.apptoken){
    //     requestCall(uiConstants.common.SERVER_IP + "/masterTypes/application", "GET", "", null, successCallback, errorCallback);
    // }


     
    function successCallback(data, reqType) {
    }

    function errorCallback(data, reqType) {
      showMessageBox("Error in top bar", "error");
    }

   /* self.openDropDown = function() {    
         $("#menuUserProfie").addClass('open');
    }*/
        
    self.Logout = function() {
        showMessageBox(uiConstants.componentConfig.CONFIRM_LOGOUT, "question", "confirm", function confirmCallback(confirmLogout){
          if(confirmLogout){
            window.currentPageMode = "";
            logout();
          }
        });
    }

    self.loadProfileDetails = function() {
        curHasher = hasher.getHash()
        hasher.setHash("viewIndividualProfile");
    }

    this.onMenuClick = function(data,event) {

        /*if(data.linkAddress == "#listGridView"){
          hasher.setHash("");
          hasher.setHash(data.linkAddress);
        }*/

        /*self.menuItemName(data.name);

        self.sideMenuList(data.sideMenu);
        if(data.parentId!=0){
            self.slideMenuLength(data.sideMenu.length);
        }
        else
        {
             self.slideMenuLength(0);
        }

        $("div #configMenu").removeClass("open");

        if(data.subMenu != undefined && data.subMenu.length === 0){
          event.stopPropagation();
          //$(this).toggleClass('aria-expanded',!selected);
          return true;
        }*/
       

    }

    /*this.subMenuPermission = function(subMenu) {
        if(subMenu.length > 0){
            for(menuOpt in subMenu){
                if(self.menuItemsPermission()[subMenu[menuOpt].name].access != "none"){
                    return 1;
                }
            }
            return 0;
        }
    };*/

    commonMenuFunc = function(){
        var userName = "";
        if(localStorage.apptoken){
          userName = JSON.parse(atob(localStorage.apptoken.split(".")[1])).userName;
        }
        window.userName(userName);
        requestCall(uiConstants.common.SERVER_IP + "/user/menuDetails", "GET", "", "getMenuPermission", successCallback, errorCallback);
    }


    function onMenuLoad(){
        if(self.menuList().length>0 && self.menuItemsPermission()){
            self.loadMenu(true);
        }
    }

    function setOptionPermissions(urlHash){
      window.commonAddEnabled(false);
      window.commonUpdateEnabled(false);
      window.commonDeleteEnabled(false);/*

      window.koMenuPermissions([
      {
        "id": 3,
        "screenId": 3,
        "readEnabled": 1,
        "createEnabled": 0,
        "updateEnabled": 0,
        "deleteEnabled": 0,
        "accessLink": "#appTypeListView"
      },
      {
        "id": 3,
        "screenId": 3,
        "readEnabled": 1,
        "createEnabled": 0,
        "updateEnabled": 0,
        "deleteEnabled": 0,
        "accessLink": "#appTypeListView"
      },
      {
        "id": 4,
        "screenId": 4,
        "readEnabled": 1,
        "createEnabled": 1,
        "updateEnabled": 1,
        "deleteEnabled": 0,
        "accessLink": "#compTypeListView"
      },
      {
        "id": 7,
        "screenId": 7,
        "readEnabled": 1,
        "createEnabled": 1,
        "updateEnabled": 1,
        "deleteEnabled": 0,
        "accessLink": "#transactionListView"
      },
      {
        "id": 4,
        "screenId": 4,
        "readEnabled": 1,
        "createEnabled": 1,
        "updateEnabled": 1,
        "deleteEnabled": 0,
        "accessLink": "#compTypeListView"
      },
      {
        "id": 7,
        "screenId": 7,
        "readEnabled": 1,
        "createEnabled": 1,
        "updateEnabled": 1,
        "deleteEnabled": 0,
        "accessLink": "#transactionListView"
      }
    ]);

      debugger;*/

      var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == urlHash; });
      if(optionPermissionsObj.length){
        window.commonAddEnabled(optionPermissionsObj[0].createEnabled);
        window.commonUpdateEnabled(optionPermissionsObj[0].updateEnabled);
        window.commonDeleteEnabled(optionPermissionsObj[0].deleteEnabled);
      }
    }

    window.koMenuPermissions.subscribe(function(obj) {
      removeLoadingContainer();
      if(localStorage.apptoken && window.lastAction != "addUpdate"){
        var urlHash = "#"+hasher.getHash();
        hasher.setHash("#");
        hasher.setHash(urlHash);
      }
      else if(obj){
        window.lastAction = "";
      }
    });

    function successCallback(data, reqType) {     
        if(reqType === "getMenuPermission"){
            window.sideMenuObj([]);

            /*menuList =  {
                          "menuDetails": [
                            {
                              "name": "Configuration",
                              "id": 1,
                              "level": "0",
                              "submenu": [
                                {
                                  "name": "Producers",
                                  "level": "1_1",
                                  "accessLink": "#producerListView"
                                }
                              ]
                            },
                            {
                              "name": "Dashboard",
                              "level": "0",
                              "id": 20,
                              "submenu": [
                                {
                                  "name": "Masters",
                                  "id": "21",
                                  "level": 1,
                                  "submenu": [
                                    {
                                      "name": "Component Type",
                                      "level": "2_21"
                                    },
                                    {
                                      "name": "Component Version",
                                      "level": "2_21"
                                    }
                                  ]
                                },
                                {
                                  "name": "Application",
                                  "id": "7",
                                  "level": 1,
                                  "accessLink": "#listGridView"
                                }
                              ]
                            },
                            {
                              "name": "Settings",
                              "id": "0"
                            }
                          ],
                          "permissions": [
                            {
                              "menuId": 1,
                              "accessLink": "#producerListView",
                              "create": 1,
                              "update": 1,
                              "delete": 1
                            },
                            {
                              "menuId": 1,
                              "accessLink": "#componentListView",
                              "create": 1,
                              "update": 1,
                              "delete": 1
                            },
                            {
                              "menuId": 2,
                              "accessLink": "#componentInstanceListView",
                              "create": 1,
                              "update": 0,
                              "delete": 1
                            }
                          ]
                        };*/

            menuList = data.result;

            window.koMenuPermissions(menuList.permissions);

            for(topMenu in menuList.menuDetails){
                counter = 0;
                var arr = [];
                arr.push(menuList.menuDetails[topMenu]);
                getSubMenuObj(arr, 0);
            }

            setOptionPermissions("#"+hasher.getHash());

            console.log(ele.firstChild);
            $('#topmenubar').empty();
            $(ele.firstChild).appendTo('#topmenubar');

            $('#topmenubar a[href]').click(function(e){
              var menuObj = $(this);
              if(window.koPreviousHash){
                  window.koPreviousHash(hasher.getHash());
              }
              
              if(menuObj.attr('href') == "#tiles" || window.currentPageMode != "AddUpdate"){
                return handleMenuOptions(menuObj);
              }
              else{
                showMessageBox(uiConstants.common.CONFIRM_PAGE_NAVIGATION, "question", "confirm", function confirmCallback(confirmClear){
                  if (confirmClear){
                    window.currentPageMode = "";
                    return handleMenuOptions(menuObj);
                  }

                });
                return false;
              }
            });
        }
    }

    function handleMenuOptions(menuObj){

      $("#side-nav").css("display", "none");

      //if($(this).attr('href') == "#listGridView"){
       if(!menuObj.data("sidemenu")){
        hasher.setHash("");
        hasher.setHash(menuObj.attr('href'));

      }
      //}
        
      if(menuObj.attr('href') != "" && menuObj.attr('href') != "#tiles"){
        setOptionPermissions(menuObj.attr('href'));
      }

       $('#sideBarMenus').empty();
       if(menuObj.data("sidemenu")){
          $(".sideBar_wrapper").removeClass("active");

          var sideMenu = "";
          var curSideMenuObj = window.sideMenuObj()["list"+menuObj.data("parentid")];
          for(var menu in curSideMenuObj){
            sideMenu=sideMenu+"<li><a href='" + curSideMenuObj[menu].accessLink + "'>"
              + "<span>" + curSideMenuObj[menu].name + "</span>"
              + "<span class='glyphicon glyphicon-star pull-right' title='"+curSideMenuObj[menu].name+"'></span>"
              + "</a></li>"
          }

          $("#side-nav").css("display", "");
          $(sideMenu).appendTo('#sideBarMenus');
          $("div .dropdown-toggle").parent().removeClass("open");

          $('.side-bar .menu li a').on('click',function() {         
              $('.side-bar .menu li a').removeClass('active');
              //$(this).removeClass('active');
              $(this).addClass('active');  

              setOptionPermissions($(this).attr('href'));

              var sideMenuObject = $(this);

              if(window.koPreviousHash){
                  window.koPreviousHash(hasher.getHash());
              }


              if(window.currentPageMode != "AddUpdate"){
                hasher.setHash("");
                hasher.setHash(sideMenuObject.attr('href'));
              }
              else{
                showMessageBox(uiConstants.common.CONFIRM_PAGE_NAVIGATION, "question", "confirm", function confirmCallback(confirmClear){
                  if (confirmClear){
                    window.currentPageMode = "";
                    hasher.setHash("");
                    hasher.setHash(sideMenuObject.attr('href'));
                  }

                });
                return false;
              }


          });    

          $('.side-bar .menu li a').on('click',function() {         
            if(!$(".sideBar_wrapper").hasClass('active')){
              $(".sideBar_wrapper").addClass("active");
              $(".container").css('opacity','1.0').find("*").prop("disabled", false);
            }
            //$("#side-nav").css("display", "none");
          });

          return false;
       }
    }

    function getSubMenuObj(menuObj, parentId){
        for(menu in menuObj){
            if(menuObj[menu].accessLink != undefined)
                openPage = menuObj[menu].accessLink;
            else
                openPage = "";

            if(counter == 0){
                counter = 1;

                if($( ele ).find(".list0").length == 0){
                    if(menuObj[menu].hasOwnProperty("submenu")){
                        $(ele).append("<ul class='nav navbar-nav list0'><li class='dropdown'> <a class='dropdown-toggle' data-toggle='dropdown'>" + menuObj[menu].name + " <b class='caret'></b></a></li></ul>");
                    }
                    else{
                        $(ele).append("<ul class='nav navbar-nav list0'><li class='dropdown'> <a href='"+openPage+"' data-parentid='" + menuObj[menu].level + "' data-sidemenu='" + menuObj[menu].hasOwnProperty("sidemenu") +"'>" + menuObj[menu].name + "</a></li></ul>");
                    }
                }
                else{
                    console.log($(ele).find(".list0").length);
                    if(menuObj[menu].hasOwnProperty("submenu")){
                        $(ele).find(".list0").append("<li class='dropdown'> <a class='dropdown-toggle' data-toggle='dropdown'>" + menuObj[menu].name + " <b class='caret'></b></a></li>");
                    }
                    else{
                        $(ele).find(".list0").append("<li class='dropdown'> <a href='"+openPage+"' data-parentid='" + menuObj[menu].level + "' data-sidemenu='" + menuObj[menu].hasOwnProperty("sidemenu") +"'>" + menuObj[menu].name + "</a></li>");
                    }
                }
            }
            else{
                if($( ele ).find( ".list" + menuObj[menu].level).length == 0){

                    console.log($( ele ).find( ".list" + menuObj[menu].level + " ul" ).length);
                    
                    console.log(menuObj[menu]);

                   if(menuObj[menu].hasOwnProperty("submenu")){
                        $( ele ).find( ".list" + parentId + " li").last().append("<ul class='dropdown-menu list" + menuObj[menu].level + "' role='menu' aria-labelledby='dropdownMenu'><li class='dropdown-submenu'><a>"+ menuObj[menu].name +"</a></li></ul>");
                    }
                   else{
                        $( ele ).find( ".list" + parentId + " li").last().append("<ul class='dropdown-menu list" + menuObj[menu].level + "' role='menu' aria-labelledby='dropdownMenu'><li><a href='"+openPage+"' data-parentid='" + menuObj[menu].level + "' data-sidemenu='" + menuObj[menu].hasOwnProperty("sidemenu") +"'>"+ menuObj[menu].name +"</a></li></ul>");
                   }

                    console.log(menuObj[menu].name+"  "+menuObj[menu].level+" "+parentId+ " "+$( ele ).find( ".list" + menuObj[menu].level + " ul").length);
                    
                }
                else{
                    console.log($( ele ).find( ".list" + menuObj[menu].level ).length);
                    console.log(menuObj[menu].name);
                    if(menuObj[menu].hasOwnProperty("submenu")){
                        $( ele ).find( ".list" + menuObj[menu].level ).last().append("<li class='dropdown-submenu'><a>"+ menuObj[menu].name +"</a></li>");
                    }
                    else{
                        $( ele ).find( ".list" + menuObj[menu].level ).last().append("<li><a href='"+openPage+"' data-parentid='" + menuObj[menu].level + "' data-sidemenu='" + menuObj[menu].hasOwnProperty("sidemenu") +"'>"+ menuObj[menu].name +"</a></li>");
                    }
                }

                console.log(menuObj[menu].level+"  "+$( ele ).find( ".list" + menuObj[menu].level + " ul" ).length);
            }

            if(menuObj[menu].hasOwnProperty("sidemenu")){
                generateSideMenuObj(menuObj[menu].sidemenu, menuObj[menu].level);
            }

            if(menuObj[menu].hasOwnProperty("submenu")){
                getSubMenuObj(menuObj[menu].submenu, menuObj[menu].level);
            }
        }
    }

    function errorCallback(reqType) {
        if(reqType === "getMenuDetails"){
            showMessageBox(uiConstants.menuDetails.ERROR_GET_MENU_DETAILS, "error");
        }
        else if(reqType === "getMenuPermission"){
            showMessageBox(uiConstants.menuDetails.ERROR_GET_MENU_PERMISSION, "error");
        }
    }

    

    function generateSideMenuObj(menuObj, parentId){
      console.log("***************");
        console.log(window.sideMenuObj());
        console.log(parentId);
        if(!window.sideMenuObj().hasOwnProperty("list" + parentId)){
            window.sideMenuObj()["list" + parentId] = menuObj;
        }



        if(hasher.getHash() == "compTypeListView" ||  hasher.getHash() == "appTypeListView"){
          var sideMenu = "";
          console.log( window.sideMenuObj());
          var curSideMenuObj = window.sideMenuObj()["list1_1"]; //constant for masters menu
          for(var menu in curSideMenuObj){
            debugger;
            sideMenu=sideMenu+"<li><a id='sideMenu" + menu + "' href='" + curSideMenuObj[menu].accessLink + "' onclick='window.onSideMenuClick("+menu+")'" +(curSideMenuObj[menu].accessLink == "#"+hasher.getHash() ? " class='sideMenuClass active'" : "class='sideMenuClass'") + ">"
              + "<span>" + curSideMenuObj[menu].name + "</span>"
              + "<span class='glyphicon glyphicon-star pull-right' title='"+curSideMenuObj[menu].name+"'></span>"
              + "</a></li>"
          }

          $("#side-nav").css("display", "");
          $(sideMenu).appendTo('#sideBarMenus');
        }



       // window.sideMenuObj()["list" + parentId].push(menuObj);

        console.log(window.sideMenuObj());
    }

    /*if(nonTokenBasedPages.indexOf(hasher.getHash()) == -1){
        commonMenuFunc();
    }*/

    /*Side bar slider open/close immplementation in small device*/

    //stick in the fixed 100% height behind the navbar but don't wrap it
    $('#slide-nav .navbar-inverse').after($('<div class="inverse" id="navbar-height-col"></div>'));  
    $('#slide-nav .navbar-default').after($('<div id="navbar-height-col"></div>'));  

    // Enter your ids or classes
    var toggler = '.navbar-toggle';
    var pagewrapper = '#page-content';
    var navigationwrapper = '.navbar-header';
    var menuwidth = '100%'; // the menu inside the slide menu itself
    var slidewidth = '50%';
    var menuneg = '-100%';
    var slideneg = '-50%';

    $("#slide-nav").on("click", toggler, function (e) {
        var selected = $(this).hasClass('slide-active');
        $('#slidemenu').stop().animate({
            left: selected ? menuneg : '0px'
        });
        $('#navbar-height-col').stop().animate({
            left: selected ? slideneg : '0px'
        });
        $(pagewrapper).stop().animate({
            left: selected ? '0px' : slidewidth
        });
        $(navigationwrapper).stop().animate({
            left: selected ? '0px' : slidewidth
        });
        $(this).toggleClass('slide-active', !selected);
        $('#slidemenu').toggleClass('slide-active');
        $('#page-content, .navbar, body, .navbar-header').toggleClass('slide-active');
    });
    var selected = '#slidemenu, #page-content, body, .navbar, .navbar-header';
    $(window).on("resize", function () {
        if ($(window).width() > 767 && $('.navbar-toggle').is(':hidden')) {
            $(selected).removeClass('slide-active');
        }
    });

  }

  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  TopBar.prototype.dispose = function() { };
  return { viewModel: TopBar, template: templateMarkup };
});