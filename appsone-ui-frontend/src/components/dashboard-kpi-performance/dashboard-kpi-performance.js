define(['jquery','bootstrap','bootstrap-switch','d3','c3','knockout','fusionCharts','text!./dashboard-kpi-performance.html','hasher','ui-constants','ui-common'], function($,bootstrap,bs,d3,c3,ko,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function KPIPerformance(params) {
    var self = this;
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.isModal = ko.observable(params.isModal);
    this.isGrpahView = ko.observable(true);

    /*POD Grpah related observables*/
    this.KPIPodId = ko.observable('kpi-performance'+params.podId);
    this.ModalKPIPodId = ko.observable('Modal_kpi-performance'+params.podId);
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.timeWindow = ko.observableArray(["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"]);
    this.kpiValues = ko.observableArray([
            ["KPI1",15,11,36,87,97,4,7,28,43,22,11,41,31],
            ["KPI2",5,4,7,28,41,4,7,28,13,22,11,41,13],
            ["KPI3",52,1,6,87,42,7,28,73,43,22,11,41,23],
            ["KPI4",11,12,69,81,97,4,7,28,4,7,2,43,43],
            ["KPI5",45,19,39,7,97,4,7,28,49,4,7,8,23],
           	["SAlerts1",null,11,36,87,null,4,7,28,43,null,11,41,null],
            ["SAlerts2",5,null,7,28,null,4,7,28,13,22,null,41,13],
            ["SAlerts3",null,1,6,87,null,7,28,73,43,22,11,41,null],
            ["SAlerts4",11,12,null,81,null,4,7,28,4,7,2,43,43],
            ["SAlerts5",null,19,39,7,97,4,null,28,49,4,7,8,null],            
            ["AAlerts1",15,11,null,87,null,4,7,28,null,22,11,null,31],
            ["AAlerts2",null,4,7,28,41,4,null,28,13,22,11,41,null],
            ["AAlerts3",52,null,6,null,42,null,28,null,43,null,11,null,23],
            ["AAlerts4",null,12,null,81,null,4,null,28,null,7,null,43,null],
            ["AAlerts5",45,null,null,7,97,null,null,28,49,null,7,null,23],

            ["KPI6",5,1,6,7,7,4,7,8,3,2,1,4,1],
            ["KPI7",2,4,7,2,1,4,7,8,3,2,1,4,3],
            

     ]);
    this.kpiSAlertsValues = ko.observableArray([
    		["SAlerts1",0,1,6,7,0,4,7,2,4,0,1,4,0],
            ["SAlerts2",1,0,7,2,0,4,7,2,3,2,0,1,3],
            ["SAlerts3",0,1,6,7,2,7,2,3,3,2,1,1,0],
            ["SAlerts4",0,2,0,1,0,4,7,8,4,7,2,4,3],
            ["SAlerts5",0,1,3,7,9,4,0,1,4,4,7,8,0]
    	]);

    this.kpiAAlertsValues = ko.observableArray([
    		["SAlerts1",2,1,0,7,0,4,7,2,0,1,1,0,1],
            ["SAlerts2",0,1,7,2,1,4,0,2,3,2,1,1,0],
            ["SAlerts3",4,0,6,0,2,0,2,0,3,0,1,0,1],
            ["SAlerts4",0,2,0,1,0,4,0,8,0,7,0,4,0],
            ["SAlerts5",7,0,0,7,9,0,0,1,4,0,7,0,1]
    	]);

    this.kpiPerfGridHeaders = ko.observableArray([
        {'columnTitle':'time', 'displayName':'Time' ,'class': 'col-xs-3 textOverflowOmmiter'},
        {'columnTitle':'name', 'displayName':'Name' ,'class': 'col-xs-3 textOverflowOmmiter'},
        {'columnTitle':'value', 'displayName':'Value(%)', 'class': 'col-xs-2 textOverflowOmmiter'},
        {'columnTitle':'alerts', 'displayName':'Alerts' ,'class': 'col-xs-2 textOverflowOmmiter'},
      
    ]);

    this.kpiPerfGridData = ko.observableArray([
    	{'time':'10:00', 'name':'KPI-1', 'value':'23', 'alerts':'2'},
    	{'time':'10:20', 'name':'KPI-3', 'value':'23', 'alerts':'1'},
    	{'time':'10:30', 'name':'KPI-4', 'value':'23', 'alerts':'12'},
    	{'time':'10:40', 'name':'KPI-5', 'value':'23', 'alerts':'4'},
    	{'time':'10:50', 'name':'KPI-1', 'value':'23', 'alerts':'7'},
    	{'time':'11:00', 'name':'KPI-2', 'value':'23', 'alerts':'9'},
    ]);

    

    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);

        self.initChart(self.KPIPodId(),self.currentPodBodyHeight()-5,self.currentPodBodyWidth()-5,"Time","Value(%)","Value",self.timeWindow(),self.kpiValues(),self.kpiSAlertsValues());
        self.bindExportCSVListner("#"+self.podId()+"_exportCSV" );
    }


    this.bindExportCSVListner = function(iconId){
        /*Export as csv button listner*/
        $(iconId).on( "click", function() {
          if(uiConstants.common.DEBUG_MODE) console.log(self.podId()+"_exportCSV");
          JSONToCSVConvertor(self.graphDataSet(),self.podTitle(), "Appsone_", true); //(dataset, reporrtTitle, FileName+ReportTilte, Labelflag)
        });
    }

    this.modalRenderHandler = function(){
        self.currentPodBodyHeight($('body').height()*0.80);
        self.currentPodBodyWidth($('body').width()*0.85);
        if(uiConstants.common.DEBUG_MODE) console.log(self.currentPodBodyHeight());        
        self.initChart(self.ModalKPIPodId(),self.currentPodBodyHeight()-30,self.currentPodBodyWidth(),"Time","Value(%)","Value",self.timeWindow(),self.kpiValues(),self.kpiSAlertsValues());
        self.bindExportCSVListner("#"+self.podId()+"_Modal_exportCSV" );
        
        self.bindGridGraphSwicthListner();   
    }

    this.bindGridGraphSwicthListner = function(){
        //intilize switch.
        $("#expandGridGraphFilter_pod_"+self.podId()).bootstrapSwitch('state', true);
        
        //Switch listner for change the view b/w Graph and Grid
        $("#expandGridGraphFilter_pod_"+self.podId()).on('switchChange.bootstrapSwitch', function (event, state) {
            if(uiConstants.common.DEBUG_MODE) console.log(event);
            if(uiConstants.common.DEBUG_MODE) console.log(this.id+"----->"+state);
            if(state){ console.log("Graph View");}
            else{ console.log("Grid View");}
            self.isGrpahView(state);
        });
    }

    this.initChart = function(chartCont, chartHeight, chartWidth,xAxisLabel,y1AxisLabel,y2AxisLabel,timeWindowSet,y1axisDataSet,y2axisDataSet){
        /*Parameters:-
            chartCont :- container Id where Chart will render
            chartHeight :- chart canvas height
            chartWidth :- chart canvas width
            xAxisLabel  :- chart X Axis Label
            y1AxisLabel :- chart Y1 AXis Label
            y2AxisLabel :- chart Y2 AXis Label
            timeWindowSet :- chart x AXis Data set Array(for Ex - Time window)
            y1axisDataSet :- chart actual entity data set(Array of object)
            y2axisDataSet :- chart actual entity data set(Array of object)
        */
    
        var chart = c3.generate({
            bindto: document.getElementById(chartCont),
            size: {
              width: parseInt(chartWidth),
              height: parseInt(chartHeight),
            },
            grid: {
              focus: {
                show: false
              }
            },
            area: {
              zerobased: true
            },
            data: {
                  columns: y1axisDataSet,
                  //groups: [['KPI1','KPI2','KPI3','KPI4','KPI5']],
                  type:'spline',
                  types:{
                  	//'KPI1':'spline','KPI2':'spline','KPI3':'spline','KPI4':'spline','KPI5':'spline','KPI6':'spline','KPI7':'spline',
                  	'SAlerts1':'scatter','SAlerts2':'scatter','SAlerts3':'scatter','SAlerts4':'scatter','SAlerts5':'scatter',
                  	'AAlerts1':'scatter','AAlerts2':'scatter','AAlerts3':'scatter','AAlerts4':'scatter','AAlerts5':'scatter'
                  },  // 'line', 'spline', 'step', 'area', 'area-step' are also available to stack
                  axes: {
	              		KPI1:'y',KPI2:'y',KPI3:'y',KPI4:'y',KPI5:'y',
	              		KPI6:'y2',KPI7:'y2',
			            //Alerts1: 'y',
			            
			       },
                  colors: {
                      'KPI1': 'rgb(31, 119, 180)',
                      'KPI2': 'rgb(255, 127, 14)',
                      'KPI3': 'rgb(44, 160, 44)',
                      'KPI4': 'rgb(148, 103, 189)',
                      'KPI5':'rgb(140, 86, 75)',
                      'SAlerts1':'rgb(230, 45, 0)',
                      'SAlerts2':'rgb(255, 45, 0)',
                      'SAlerts3':'rgb(255, 45, 0)',
                      'SAlerts4':'rgb(255, 45, 0)',
                      'SAlerts5':'rgb(255, 45, 0)',
                      'AAlerts1':'rgb(255, 255, 0)',
                      'AAlerts2':'rgb(255, 255, 0)',
                      'AAlerts3':'rgb(255, 255, 0)',
                      'AAlerts4':'rgb(255, 255, 0)',
                      'AAlerts5':'rgb(255, 255, 0)',
                  },                  
                  names:{
                  	'SAlerts1':'Static Alerts',
                  	'AAlerts1':'Analytical Alerts',
                  },

              },
              point: {
      				  show: false,
      				  r: 5,
      				  
      				},   
              zoom:{
                enabled: false,
              },
              padding: {
                //bottom: 40,
                //top: 5,
                //right: 0,
                //left : 0,

              },
            legend: {
                  show: true,
                  hide: ['SAlerts2','SAlerts3','SAlerts4','SAlerts5','AAlerts2','AAlerts3','AAlerts4','AAlerts5'],
                  item: {
				    onmouseover: function (id) { if(id != "SAlerts1" && id != "AAlerts1") chart.focus(id); },
				    onclick: function (id) { if(id != "SAlerts1" && id != "AAlerts1") chart.toggle(id); }
				  },
				  height:20,
				  width:50,              

              },
              axis: {
                rotated: false,
                y: {
                  show:true,
                  type: 'category',
                  label: {
                    text: y1AxisLabel,
                    position: 'inner-top',   // inner-top,inner-middle,inner-bottom,outer-top,outer-middle,outer-bottom

                  },                 
                  //min: 0,
                  //max: Math.max.apply(Math,tickYValues),
                  tick: {
                      //min: 0,
                      //max: 5,
                      //format: d3.format('d'),
                      //values: [tickYValues],
                      /*format: function(d){
                           if(d%2==0) return d;
                      },*/
                    },

                },
                y2: {
                  show:true,
                  type: 'category',
                  label: {
                    text: y2AxisLabel,
                    position: 'inner-top',   // inner-top,inner-middle,inner-bottom,outer-top,outer-middle,outer-bottom

                  },                  
                  //min: 0,
                  //max: Math.max.apply(Math,tickYValues),
                  tick: {
                      //min: 0,
                      //max: 5,
                      //format: d3.format('d'),
                      //values: [tickYValues],
                      /*format: function(d){
                           if(d%2==0) return d;
                      },*/
                    },

                },
                x: {
                  show:true,
                  type: 'category',
                  categories: timeWindowSet,
                  tick: {
                  rotate: -90,
                  multiline: false,
                        culling: {
                                //max: 7,// the number of tick texts will be adjusted to less than this value
                                //count: 7,

                            }
                          // for normal axis, default on
                          // for category axis, default off
                      },
                  label: {
                    text: xAxisLabel,
                    position: 'inner-center',// inner-right,inner-center,inner-left,outer-right,outer-center,outer-left
                  },
                  height:50,
                },
              },
              tooltip: {
                  grouped:true,

                  contents: function (d, defaultTitleFormat, defaultValueFormat, color) {

                      var $$ = this, config = $$.config,
                          titleFormat = config.tooltip_format_title || defaultTitleFormat,
                          nameFormat = config.tooltip_format_name || function (name) { return name; },
                          valueFormat = config.tooltip_format_value || defaultValueFormat,
                          text, i, title, value, name, bgcolor;
                      for (i = 0; i < y2axisDataSet.length; i++) {
                          if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }
                   
                          if (! text) {
                              title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                              text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='4'>Time - " + title + "</th></tr>" : "");
                          }

                          
                          name = nameFormat(d[i].name);
                          value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                          bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);

                        
                    	if(i == 0){
                          text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                          text += "<td class='name'><b>Name</b> &nbsp&nbsp</td>";
                          text += "<td class='value'><b>Value</b></td>";
                          text += "<td class='value'><b>Static Alerts</b></td>";
                          text += "<td class='value'><b>Analytical Alerts</b></td>";
                          text += "</tr>";
                         }
                          text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                          text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "&nbsp&nbsp</td>";                          
                          text += "<td class='value'>" + y1axisDataSet[i][d[i].index+1] + "%</td>";
                          text += "<td class='value'>" + self.kpiSAlertsValues()[i][d[i].index+1] + "</td>";
                          text += "<td class='value'>" + self.kpiAAlertsValues()[i][d[i].index+1] + "</td>";
                          text += "</tr>";
                       
                        
                      }
                      return text + "</table>";
                  }


              }
        });


    }

  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  KPIPerformance.prototype.dispose = function() { };
  
  return { viewModel: KPIPerformance, template: templateMarkup };

});