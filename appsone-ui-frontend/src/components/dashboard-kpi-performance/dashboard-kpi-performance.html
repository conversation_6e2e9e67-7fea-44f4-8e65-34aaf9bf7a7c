<div data-bind="template: {afterRender: renderHandler}">
	<!--  KPI Line chart example -->
 	<div data-bind="attr: {'id': KPIPodId}">

 	</div>
 	<!-- KPI chart end-->

 </div>
 

<!-- Expandable view as Modal -->
<div class="podModalCont" data-bind="template: {afterRender: modalRenderHandler}">
  <div data-bind="attr :{'id':'podModal_'+podId()}" class="modal fade modal-fullscreen force-fullscreen" role="dialog">
    <div class="modal-dialog" data-bind="style : {height : '100%', width: '100%'}">

      <!-- Modal content-->
      <div class="modal-content" data-bind="style : {height : '90%', width: '90%', margin:'0 5%'}">
			<div class="modal-header">
  	          <!-- <button type="button" class="close" data-dismiss="modal">&times;</button> -->
  	          <ul data-bind="style:{'float':'right'}">
  	            <li class="glyphicon glyphicon-remove" data-dismiss="modal" data-bind="style:{'display':'inline', 'margin': '2px 4px', 'float':'right'}"></li>
  	            <li class="dropdown" data-bind="style:{'display':'inline', 'margin': '2px 4px'}">
  	                <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
  	                    <i class="glyphicon glyphicon-download-alt"></i>
  	                </span>
  	                <ul class="dropdown-menu pull-right" role="menu">
  	                    <li data-bind="attr:{'id': podId()+'_Modal_exportPDF'}"><span>PDF Document</span></li>
  	                    <li data-bind="attr:{'id': podId()+'_Modal_exportPNG'}"><span>PNG Image</span></li>
  	                    <!-- <li data-bind="attr:{'id': podId()+'_Modal_exportJPG'}"><span>JPG Image</span></li> -->
  	                    <li data-bind="attr:{'id': podId()+'_Modal_exportCSV'}"><span>CSV Document</span></li>
  	                </ul>
  	            </li>
  	          </ul>
  	          <h4 class="modal-title" data-bind="text:podTitle"></h4>
  	          <div class="breadcrumb" data-bind="style:{'height':'auto','margin-bottom':'0','padding':'0'}">
                      <div class="form-group" data-bind="style: {'margin-bottom':'0'}">
                          <div class="input-group">
                              <span class="input-group-addon"><span class="glyphicon glyphicon-time"></span></span>
                              <select class="form-control" data-bind="style:{'width':'auto', 'margin-right':'10px'}, attr:{'id' : 'expandTimeFilter_pod_'+podId()}">
                                  <option value="60">1 hr</option>
                                  <option value="1440">24 hr</option>
                                  <option value="4320">1 month</option>
                                  <option value="525600">1 year</option>
                              </select>
                          
                              <input type="checkbox" data-size="small" data-handle-width="24px" data-label-width="22px" data-on-color="info" data-off-color="info" data-off-text="<span class='glyphicon glyphicon-th'></span>" data-on-text="<span class='glyphicon glyphicon-picture'></span>" checked="true" class="BSswitch" data-bind="attr :{'id' : 'expandGridGraphFilter_pod_'+podId()}">
                          </div>

                      </div>                  
             </div>
          </div>
            
            <div class="modal-body" data-bind="attr :{'id':'podModalBody_'+podId()}">
  
                  <!--  KPI Line chart example -->
                  <div data-bind="style : { 'display' : isGrpahView() == true && kpiPerfGridData().length ? 'block' : 'none'}, attr: {'id': ModalKPIPodId}">
                  </div>
                  <!-- KPI chart end-->


                  <!-- KPI Grid View start -->
                    <div data-bind="style : { 'display' : isGrpahView() == false && kpiPerfGridData().length ? 'block' : 'none', 'height' : parseInt(currentPodBodyHeight()*0.9)+'px', 'overflow-y':'auto'}" class="scrollTable">
                        <table id="slowPagesTable" class="table table-fixedheader table-bordered podTable" data-bind="attr: {id: 'pod_'+podTitle()}">
                            <thead>
                                <tr data-bind="foreach: kpiPerfGridHeaders">
                                    <th data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data.displayName}, text: $data.displayName, css: $data.class"></th>
                                </tr>
                            </thead>
                            <tbody data-bind="foreach: kpiPerfGridData">
                                <tr data-bind="foreach : $parents[0].kpiPerfGridHeaders">
                                   <td data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data['columnTitle'] == 'avgResponseTime' ? parseFloat($parents[0][$data['columnTitle']]) : $parents[0][$data['columnTitle']]},text: $data['columnTitle'] == 'avgResponseTime' ? parseFloat($parents[0][$data['columnTitle']]) : $parents[0][$data['columnTitle']] || 'NA', css: $data.class"></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                  <!-- KPI Grid View end -->

            </div>
            <!-- <div class="modal-footer">
              <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div> -->
          </div>

        </div>
    </div>
</div>