 <div class="panel panel-default">
	<div class="configPanel panel-heading"><h4>Add Multiple Applications</h4></div>
	<div class="panel-body">
		<div class="col-sm-12" id="listAppDetailsPage" data-bind="template: {afterRender: renderHandler}" >	

			<div class=text-right>
				<button type="button" class="btn btn-default disabled" id="btnAdd" data-bind="click: onAddClick">Add</button>
			</div>
			<br>
		 	<div class="wrapper">
		        <table class="table table-hover table-striped table-sm" id="tableMultipleAdd" >
		          <thead class="a1-list-grid-header">
		          	<tr data-bind="foreach: appTableHeaders" >
		          		<!-- <th data-bind="{text: $data.name == 'Timezone' ? 'Time Zone' : $data.name, css: $data.name == '' || $data.name == '#' ? 'col-xs-1' : 'col-xs-2'}"></th> -->		          	
		          		<!-- <th data-bind="{html: $data.name == 'Timezone' ? 'Time Zone' : $data.name == 'Name' ? 'Name<span class=mandatoryField>*</span>' : $data.name == 'Type' ? 'Type<span class=mandatoryField>*</span>' : $data.name, css: $data.name == '' || $data.name == '#' ? 'col-xs-1' : 'col-xs-2'}"></th> -->
		          		<th data-bind="{css: $data.attributeName == '' || $data.attributeName == 'Sl.No.' ? 'col-xs-1' : 'col-xs-2'}">
			          		<span data-bind="text : $data.attributeName"></span>
			          		<span data-bind="if : $data.mandate" class="mandatoryField">*</span>
			          	</th> 
		          	</tr>
		          </thead>
		          <tbody>
		         		
		          </tbody>         
				</table>
			</div>
			<!-- <div><span class="col-xs-10" data-bind="visible: errorMessage"></span></div> -->
			
			<div id="actionBtns" class="text-right divActionPanel"> 
			     <button class="btn btn-primary disabled" id="btnSave" data-bind="click: onMultipleSaveClick">Save</button>
			     <button class="btn disabled" id="btnClearAll" data-bind="click: onClearAll">Reset</button>
			     <button class="btn" id="btnCancel" data-bind="click :  onMultipleCancelClick">Cancel</button>

			</div>
		</div>
	</div>
</div>
