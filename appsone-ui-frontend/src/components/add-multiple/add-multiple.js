define(['jquery','bootstrap','knockout','validator','jquery-chosen','text!./add-multiple.html','hasher','ui-constants','ui-common','floatThead'], function($,bt,ko,validator,jc,templateMarkup,hasher,uiConstants,uicommon,floatThead) {

function AddMultiple(params) {
  var self = this;
  
  this.appType = ko.observableArray();
  this.appTimeZone = ko.observableArray();
  this.appTableHeaders = ko.observableArray([
  	{'attributeName':'Sl.No.','name': '#','type': 'label','mandate':false},
  	{'attributeName':'Name','name':'Name','type':'input','mandate':true},
  	{'attributeName':'Description','name':'Description','type':'input','mandate':true},
  	{'attributeName':'Type','name':'Type','type':'type','mandate':true},
  	{'attributeName':'Time Zone','name':'Timezone','type':'timezone','mandate':false},
  	{'attributeName':'Tag','name':'Tag','type':'input','mandate':false},  	
  	{'attributeName':'','name':'','type':'delete','mandate':false}
  ]);
  this.errorStack = ko.observableArray();
  this.reqRecordCounter = ko.observableArray();
  this.multiRowAddLimit = ko.observable(1001);
  this.errorStackLimitOnRows = ko.observable(10);
  this.requestDataArray = [];


	//render handler will be call after loading all elements of current SPA component.
	this.renderHandler=function(){
		var $tab = $('#tableMultipleAdd');
		$tab.floatThead({
			scrollContainer: function($table){
				return $table.closest('.wrapper');
			}
		});

		$(window).resize(function(){
		    self.refreshPageLayout();
		});

	 	var url = uiConstants.common.SERVER_IP+"/masterTypes/application";
	 	requestCall(url, "GET", {}, "ReqAppType", successCallback, errorCallback);

		var url = uiConstants.common.SERVER_IP+"/timeZones";
	 	requestCall(url, "GET", {}, "ReqTimezone", successCallback, errorCallback);

	    self.refreshPageLayout();
	}

	this.refreshPageLayout = function(){
		$(".wrapper").height(($(window).outerHeight() - $(".wrapper").offset().top - $("#actionBtns").outerHeight() - 50) + "px");
		$('#tableMultipleAdd').floatThead('reflow');
	}

	//Loading all values into Type dropdown and Timezone custom dropdown and selecting current option otherwise default value will be set.
	this.addDataListOption = function(type, _class, _val ){
		if(type == 'type'){
			$('<option>', {text: 'Select', value: ''}).appendTo('#'+_class);
	 		self.appType().forEach(function(item){
	 			$('<option>', {value: item.masterId, text: item.name, name: item.masterId}).appendTo('#'+_class);
      		});
      		$('#'+_class+' option').filter(function() { 
			    //return ($(this).text() === _val); //To select Blue
			    if(($(this).text() === _val)) {return true;}			    
			}).prop('selected', true);
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid application type from the list.");}
 		}else if(type == 'timezone'){
 			if(uiConstants.common.DEBUG_MODE)console.log(type+"-------"+_class+"-----"+_val);
 			var flag = false;
 			$('<option>', {value: 'Select', text: 'Select', name: ''}).appendTo('#'+_class);
 			
 			self.appTimeZone().forEach(function(item){
      			if(_val == item.timeZoneName){
      				$('<option>', {value: item.timeZoneId, text: item.timeZoneName, name: item.timeZoneName, selected:true}).appendTo('#'+_class);
      				flag = true;
      			}else{
      				$('<option>', {value: item.timeZoneId, text: item.timeZoneName, name: item.timeZoneName}).appendTo('#'+_class);	
      			}
      			
      		});

      		/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			/*Jquery chosen start*/
			$("#"+_class+"_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
      		
      					
			if($("#"+_class).chosen().val() != "" && flag == false){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid application timezone from the list.");}
 		}
	}
	
	//paste listner on every rows Application name filed and on rest of fileds treat as normal copy&paste.
	this.bindTableRowListner = function(rowid){

		 $('#row_0 input ,#row_0 select ').on('change', function(e){
		 	 $("#btnClearAll").removeClass('disabled');	

		 	 if($("#Name0").val() == "" &&  $("#Description0").val() == "" && $("#Tag0").val() == "" && $("#Type0 option:selected").text() == "Select" && $("#Timezone0").chosen().val() == "Select"){
		 	 	 $("#btnClearAll").addClass('disabled');
		 	 }
		 });

		//on paste listener
		var indexID = rowid.split("_")[1];
		 //$('#'+rowid+'>td>input').on('paste', function(e){
		 $('#'+rowid+'>td>#Name'+indexID).on('paste', function(e){
		 	console.log("On paste listener call");
		 	console.log('#'+rowid+'>td>#Name'+indexID);
		 	var curObj = this;
		 	showLoadingContainer();

		 	e.stopPropagation();
		    e.preventDefault(); 	    

		    $("#btnClearAll").removeClass('disabled');	

			if (e.originalEvent.clipboardData) { 
				var data = (e.originalEvent || e).clipboardData.getData('text/plain');
				var inputId = e.target.id;
			} else if (window.clipboardData) { 
				var data = window.clipboardData.getData('Text');
				var inputId = window.event.srcElement.id;
			}
			//data = data.slice(1, -1);

			setTimeout(function () {
				if(uiConstants.common.DEBUG_MODE)console.log(data);

			   	if(data != null){
			   		//clearing old vaule
			   		self.errorStack.removeAll();

			   		var crid = $(curObj).parent().parent().attr('id');
			   		$("#"+crid).addClass('empty');
			   		var curRowIndex = $(curObj).parent().parent().index();
					
			   		/*loadin animation start*/
				    //$(".loadingContainer").css('display','block');
				    //$("#page").addClass('disabled');
				    /*loadin animation end*/
			    
			   		//data.replace(/\n$/, "");
			   		//data = data.replace(/\t/g,"");
					var rows = data.split("\n");

					rows.forEach(function(element, indx) {
						if(!element.replace(/\s/g, '').length){
							rows.splice(indx,1);
						}
					});

					//data = data.replace(/\t/g,"");
					//data = data.replace(/\r?\n|\r/g, "")

					console.log(rows);

					var table = $("#tableMultipleAdd tbody");
					var curType = "Select";
					var curTZ = "";				
					
					var rowCounter = $('#tableMultipleAdd tbody tr').length;
					
					
					var limitFlag = (rows.length-1) + rowCounter;				
					if(limitFlag <= self.multiRowAddLimit()){
						if(uiConstants.common.DEBUG_MODE)console.log("Total no of rows----------------->"+limitFlag);
						
							var col = rows[0].split("\t");
							
							if(uiConstants.common.DEBUG_MODE)console.log(col.length +"=="+ (self.appTableHeaders().length-2));

							if(col.length <= self.appTableHeaders().length-2){

								rowCounter = $('#tableMultipleAdd tbody tr').length;
								
								rows.forEach(function (y, yindex) {	

								    var cells = y.split("\t");
								    if(uiConstants.common.DEBUG_MODE)console.log(cells);					    
								    var currentRowCounter = rowCounter + yindex;
								    var row = $("<tr class='' id='row_"+currentRowCounter+"'/>");
								    

								    if(yindex < rows.length){					    	
									    if(cells.length != self.appTableHeaders().length-2){
									    	self.manageErrorMessage('push',currentRowCounter, "Cell values not be sperated by newline('\\n')");
									    }

									    //control creation start
								    	for (var xindex = 0; xindex < self.appTableHeaders().length-1; xindex++) {
								    		if(self.appTableHeaders()[xindex].type == 'label' && self.appTableHeaders()[xindex].name == '#'){
								    			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.appTableHeaders()[xindex].type+currentRowCounter+"'></label></td>");
								    		}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Name'){
										 		row.append("<td class='col-xs-2'><input type='text' id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}else if(self.appTableHeaders()[xindex].type == 'timezone'){
										 		row.append("<td class='col-xs-2'><select class='chosen form-control col-xs-10' id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"' data-placeholder=''></select></td>");
										 		//row.append("<td class='col-xs-2'><input class='col-xs-10 form-control' placeholder='Select' list='"+self.appTableHeaders()[xindex].name+currentRowCounter+"'/><datalist id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}else if(self.appTableHeaders()[xindex].type == 'type'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Tag'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Description'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Description' id='"+self.appTableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}
								    	};					   
									    row.append("<td class='col-xs-1 deleteRow' id='"+self.appTableHeaders()[xindex].type+currentRowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
									    
									    //table.append(row);
									    $(".empty").after(row);
									    table.find('.empty').removeClass('empty').next().addClass('empty');

									    self.deleteRowBind("delete"+currentRowCounter);
									    self.bindTableRowListner("row_"+currentRowCounter);
									    //control creation end

									    var tarr = [currentRowCounter+1];
									    cells = tarr.concat(cells);
									    if(uiConstants.common.DEBUG_MODE)console.log(cells);

									    //value assign to current row controls start
									    for (var xindex = 0; xindex < self.appTableHeaders().length-1; xindex++) {					    	
									    	
									    	//value assign
									    	if(self.appTableHeaders()[xindex].type == 'label' && self.appTableHeaders()[xindex].name == '#'){
									    		$("#"+self.appTableHeaders()[xindex].type+currentRowCounter).text(cells[xindex]||"");
									    	}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Name'){
									    		var cname = cells[xindex] || "";
									    		if(cname == ""){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
									    		}else if(nameValidation(cname) == 0){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
									    		}					    			
										 		$("#"+self.appTableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}else if(self.appTableHeaders()[xindex].type == 'timezone'){
									    		self.addDataListOption('timezone','Timezone'+currentRowCounter, cells[xindex]||"")
										 	}else if(self.appTableHeaders()[xindex].type == 'type'){							 		
										 		self.addDataListOption('type','Type'+currentRowCounter, cells[xindex]||"");
										 	}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Tag'){
										 		var ctag = cells[xindex] || "";
										 		if(ctag != ""){
										 			if(ctag.trim().endsWith(","))
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
										 			else if(tagValidationWithComma(ctag) == 0)
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
									    		}
									    		if(uiConstants.common.DEBUG_MODE)console.log(ctag);	
										 		$("#"+self.appTableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}else if(self.appTableHeaders()[xindex].type == 'input' && self.appTableHeaders()[xindex].name == 'Description'){
										 		
										 		$("#"+self.appTableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}
									    }
									    //value assign to current row controls end
									}
								});
							}else{
								
								showMessageBox(uiConstants.common.COLUMNS_MISMATCH, "error");	
								return false;
							}	

					}else{
						showMessageBox(uiConstants.common.ROW_LIMIT_EXCEEDED, "error");
						//self.onAddClick();
						self.enableButton();
					}

					if(limitFlag <= self.multiRowAddLimit()){
						$("#tableMultipleAdd tbody").find('.empty').removeClass('empty');
						$("#"+crid).remove();

						if($('#tableMultipleAdd tbody tr').length == 1){
							$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
						}

						self.chnageRowAndCellId();
						self.validateElementsValue();
						if(self.errorStack().length){	
							self.manageErrorMessage('print');
							if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());								
						}
					}
				}

				removeLoadingContainer();

				//}
		 	}, 1);
			
			/*loadin animation start*/
            //$(".loadingContainer").css('display','none');
    		//$("#page").removeClass('disabled');
    		/*loadin animation end*/			
		});
		
	}

	//managing all validation error message at pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount){		
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			//if(addRecordCount>1)
				var messageStr = addRecordCount+" application(s) added successfully and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			//else
			//	var messageStr = addRecordCount+" application is successfully added and "+unsavecnt +" have failed. \n";

				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						messageStr += "Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";
			self.errorStack().forEach(function(item,index){
			if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				/*if(item.rowno == -1){					
					messageStr += item.message +"\n";					
					self.enableButton();					
					if(uiConstants.common.DEBUG_MODE)console.log("count not match while copy");
				}else */
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					messageStr += "Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}


	//clearing all rows with confirmation of user's. 
	this.onClearAll = function(){
		var rowCounter = $('#tableMultipleAdd tbody tr').length;
		if(rowCounter > 1){		
		    showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableMultipleAdd tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
			    }
			});
		}
		else if(rowCounter == 1 ){

			//if($("#Name0").val() != "" ||  $("#Description0").val() != "" || $("#Tag0").val() != ""){
				showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS_CONTENT, "question", "confirm", function confirmCallback(r){
					if (r == true) {
				        $('#tableMultipleAdd tbody').empty();
						self.onAddClick();
						 $("#btnClearAll").addClass('disabled');	
						
				    }
				});
			/*}else{
				 $('#tableMultipleAdd tbody').empty();
				 self.onAddClick();
				 $("#btnClearAll").addClass('disabled');	
			}*/
		}		
	}

	//disabling buttons based on conditions
	this.disableButton = function(){
		if($('#tableMultipleAdd tbody tr').length == 0){
			$("#btnSave").addClass('disabled');
			$("#btnClearAll").addClass('disabled');			
		}
	}

	//enabling buttons based on conditions
	this.enableButton = function(){
		if($('#tableMultipleAdd tbody tr').length > 0){
			$("#btnAdd").removeClass('disabled');
			$("#btnSave").removeClass('disabled');
			//$("#btnClearAll").removeClass('disabled');
			//creating first blank row for paste functionality
	 		
		}
	}

	//Adding blank row into the grid and calling binding listner and assigning ID's of row and col elements.
	this.onAddClick = function(){
		 //add new blank row initially on click of add button
		 if(uiConstants.common.DEBUG_MODE)console.log('%%%%%%%%%');
		 if(uiConstants.common.DEBUG_MODE)console.log(self.appTableHeaders());
		 
		 var rowCounter = $('#tableMultipleAdd tbody tr').length;
		 var row = $('<tr class="" id="row_'+rowCounter+'"/>');
		 var tabIndexCounter = rowCounter * self.appTableHeaders().length;

		 if(rowCounter <= self.multiRowAddLimit()-2){

	 		for (var i = 0; i < self.appTableHeaders().length; i++) {		 	
			 	if(self.appTableHeaders()[i].type == 'label'){
			 		row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.appTableHeaders()[i].type+rowCounter+"'>"+(rowCounter+1)+"</label></span></td>");
			 	}else if(self.appTableHeaders()[i].type == 'delete'){
			 		row.append("<td class='col-xs-1 deleteRow' id='"+self.appTableHeaders()[i].type+rowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
			 	}else if(self.appTableHeaders()[i].type == 'input' && self.appTableHeaders()[i].name == 'Name'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Name' id='"+self.appTableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}else if(self.appTableHeaders()[i].type == 'input' && self.appTableHeaders()[i].name == 'Tag'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Tags separated by comma(,)' id='"+self.appTableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}else if(self.appTableHeaders()[i].type == 'input' && self.appTableHeaders()[i].name == 'Description'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Description' id='"+self.appTableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}else if(self.appTableHeaders()[i].type == 'timezone'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen form-control col-xs-10' id='"+self.appTableHeaders()[i].name+rowCounter+"' data-placeholder=''></select></td>");
			 		//row.append("<td class='col-xs-2'><input class='col-xs-10 form-control' placeholder='Select' list='"+self.appTableHeaders()[i].name+rowCounter+"'/><datalist id='"+self.appTableHeaders()[i].name+rowCounter+"'/></td>");
			 	}else if(self.appTableHeaders()[i].type == 'type'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen col-xs-10 form-control' id='"+self.appTableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}

			 	tabIndexCounter++;
			 }		 
			 $("#tableMultipleAdd tbody").append(row);
			 self.addDataListOption('type','Type'+rowCounter, 'Select');
			 self.addDataListOption('timezone','Timezone'+rowCounter, '');
			 
			 //delete row 
			 self.deleteRowBind("delete"+rowCounter);
			 
			 //calling table row listner
			 self.bindTableRowListner("row_"+rowCounter);
			 self.enableButton();

		 }else{
			showMessageBox(uiConstants.common.RECORDS_PASTE_LIMIT_EXCEEDED, "error");			
		}
		
		if($('#tableMultipleAdd tbody tr').length == 1){
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
		}
		else{
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled");
		}

	    self.refreshPageLayout();
	}

	//after adding or pasting dynamic rows in to grid, delete row listner binding.
	this.deleteRowBind = function(deleteRowId){
		$('#'+deleteRowId).on('click', function(e){

			if(uiConstants.common.DEBUG_MODE)console.log($('#tableMultipleAdd tbody tr').length);	 		
		 		if($('#tableMultipleAdd tbody tr').length == 0){
		 			self.disableButton();
		 		}else if($('#tableMultipleAdd tbody tr').length == 1){
		 			return;
		 			/*var deleteFlag = true;
		 			$("#row_0").children('td').each(function(j){
		 				if(j==1){// Application Name 	
		 					if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }else if(j==2){// Application Description
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);   	
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }else if(j==3){// Application Type
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').val().length);
			            	if($(this).children('select').val().length) deleteFlag=false;
			            }else if(j==4){// Application Timezone
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').chosen().val());
			            	if($(this).children('select').chosen().val() != "Select") deleteFlag=false;	
			            }else if(j==5){// Application Tag
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }
		 			});	
		            
		            	if(uiConstants.common.DEBUG_MODE)console.log(deleteFlag);
			 			if(deleteFlag){
			 				showMessageBox(uiConstants.common.ERROR_LAST_ROW_DELETE, "error");
			 			}else{
			 				$("#row_0").remove();
			 				self.onAddClick();
			 			} */
			 		
		 		}else{
		 			var curObj = this;
	 				showMessageBox(uiConstants.applicationConfig.CONFIRM_DELETE_APPLICATION, "question", "confirm", function confirmCallback(confirmDelete){
						if(confirmDelete){
				 			$(curObj).parent().remove();

		 					self.chnageRowAndCellId();
					 		if($('#tableMultipleAdd tbody tr').length == 1){
								$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
							}

							self.refreshPageLayout();
						}
					});
		 		}

		 		//changing each row's Id and childrens id
		 		
		 });
	}	

	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(startOffSet, endOffSet){
		self.errorStack.removeAll();
		self.requestDataArray = [];
		$('#tableMultipleAdd tbody tr').each(function(i){
			var cobj = {'index': i+1};
	        $(this).children('td').each(function(j){
	            if(j==1){// Application Name 
	            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
		    		}else if(nameValidation(cname) == 0){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
		    		}
	            	cobj['applicationName'] = trimSpacesReplaceSingleBlankspace($(this).children('input').val());
	            }else if(j==2){// Application Description
	            	if($(this).children('input').val().length > 0){
	            		cobj['description'] = $(this).children('input').val();
	            	}
	            	else{
	            	 	self.manageErrorMessage('push',cobj.index-1, "Description is required");
	            	}
	            }else if(j==3){// Application Type
	            	 var aTypeLen = $(this).children('select').val().length;	            	 
	            	 if(aTypeLen > 0)
	            	 	cobj['applicationTypeId'] = $(this).children('select').val();
	            	 else
	            	 	self.manageErrorMessage('push',cobj.index-1, uiConstants.common.APPLICATION_TYPE_REQUIRED);
	            }else if(j==4){// Application Timezone	            	
	            	var tzval = $(this).children('select').chosen().val();
	            	var tid ="";
	            	$.grep(self.appTimeZone(), function(element, index){	            		
		              if(element.timeZoneId == tzval){ // retain appropriate elements
		              	if(uiConstants.common.DEBUG_MODE)console.log(element.timeZoneId +"=="+ tzval);
		              	tid = element.timeZoneId;
		              }
		            }); 
		            if(uiConstants.common.DEBUG_MODE)console.log(tid);
		            cobj['timezoneId'] = tid || 0;
	            }else if(j==5){// Application Tag
	            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var ctag = $(this).children('input').val();
	            	if(ctag != ""){
	            		if(ctag.trim().endsWith(","))
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
			 			else if(tagValidationWithComma(ctag) == 0)
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		    		}
	            	var taglist = $(this).children('input').val();
	            	if(taglist != ""){
	            		taglist = taglist.split(",");
	            		if(uiConstants.common.DEBUG_MODE)console.log("*********");
	            		if(uiConstants.common.DEBUG_MODE)console.log(taglist);
	            		var tagObj = [];
	            		for (var i = 0; i < taglist.length; i++) {
	            			if($.grep(tagObj, function(evt){ return evt.tagName == taglist[i].trim(); }).length>0){
		    					self.manageErrorMessage('push',cobj.index-1, uiConstants.common.DUPLICATE_TAGS);
		    					break;
	            			}
	            			tagObj.push({'tagId':0 , 'tagName': taglist[i], 'tagOperation': 'add'});
	            		};
	            		 
	            	}
	            	cobj['tags'] = tagObj;
	            }
	        });
				cobj['maintenanceWindowProfileId'] = 0;
				cobj['maintenanceFlag'] = 0;
				cobj['status'] = true;
				self.requestDataArray.push(cobj);
	    });
	}

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
		self.requestDataArray = [];
    	self.validateElementsValue();

		if(self.errorStack().length){	
			self.manageErrorMessage('print');
		}else{
			self.reqRecordCounter(self.requestDataArray.length);
			var finalObj = {'applications':self.requestDataArray};
			if(uiConstants.common.DEBUG_MODE)console.log(finalObj);
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
			var url = uiConstants.common.SERVER_IP+"/applications";
			requestCall(url, 'POST', JSON.stringify(finalObj), 'AddMultipleApp', successCallback, errorCallback);	
		}
		

    }

    this.cancelAddScreen = function(){
		$('#tableMultipleAdd tbody').empty();
		hasher.setHash("#listGridView");
	}

    //cancel listner for current page and navigate to parent page of application configuration.
    this.onMultipleCancelClick = function(){
    	  hasher.setHash('#listGridView'); 
    }

    //Chnaging row and col elements ID's when any delete or append/prepend operaation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	var rowCnt = 0;
    	$('#tableMultipleAdd tbody tr').each(function(i){    		    					
	        $(this).children('td').each(function(j){
	        	if(j==0){// Application Index 
	            	$(this).children('label').attr('id','label'+rowCnt);
	            	$(this).children('label').text(rowCnt+1);
	            }else if(j==1){// Application Name 
	            	$(this).children('input').attr('id','Name'+rowCnt);
	            }else if(j==2){//Application Descripition
	            	$(this).children('input').attr('id','Description'+rowCnt);
	            }else if(j==3){//Application Type
	            	$(this).children('select').attr('id','Type'+rowCnt);
	            }else if(j==4){//Application Timezone	            	
	            	$(this).children('select').attr('id','Timezone'+rowCnt);	            	
	            }else if(j==5){//Application Tag
	            	$(this).children('input').attr('id','Tag'+rowCnt);
	            }else if(j==6){
 					$(this).attr('id','delete'+rowCnt);
 				}
	        });
	        $(this).attr('id','row_'+rowCnt);
	        rowCnt++;
	    });

    }

    //Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
    	var succnt = 0;
    	var failcnt = 0;
    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
    	if(self.reqRecordCounter() == res.length){
    		for (var i = 0; i < res.length; i++) {
    			//if(uiConstants.common.DEBUG_MODE)console.log(res[i]);
    			//if(uiConstants.common.DEBUG_MODE)console.log(res[i].index);
    			//var rid = res[i].index-1;
    			var rid = parseInt(res[i].objectId)-1;
    			if(res[i].responseStatus == uiConstants.common.CONST_SUCCESS){
    				$("#tableMultipleAdd tbody #row_"+rid).remove();
    				succnt++;	
    			}else{
    				self.manageErrorMessage('push',failcnt, handleServiceErrorMsgs(uiConstants.common.CONST_APPLICATION,res[i].errorCode,res[i].message));
    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+res[i].message);
    				failcnt++;
    			}
    		};

    		//changing row index and number
    		self.manageErrorMessage('print',"","",succnt);
    		self.chnageRowAndCellId();
    		
    	}else{
    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
    	}

    }

	function successCallback(data, reqType) {
		if(reqType === "ReqAppType"){
			self.appType.removeAll();
			self.appType.push.apply(self.appType, data.result);
			self.appType.valueHasMutated();
			if(self.appType().length && self.appTimeZone().length){
				//self.onAddClick();
					$("#btnAdd").trigger('click');
			}
		}else if(reqType === "ReqTimezone"){
			self.appTimeZone.removeAll();
			self.appTimeZone.push.apply(self.appTimeZone, data.result);
			self.appTimeZone.valueHasMutated();
			if(self.appType().length && self.appTimeZone().length){
				//self.onAddClick();
					$("#btnAdd").trigger('click');
			}

		}else if(reqType === "AddMultipleApp"){
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			if(data.responseStatus == uiConstants.common.CONST_SUCCESS){
				showMessageBox(uiConstants.applicationConfig.SUCCESS_MULTIPLE_ADD_APPLICATION);
				$('#tableMultipleAdd tbody').empty();
				if(self.appType().length && self.appTimeZone().length){
						$("#btnAdd").trigger('click');
				}
				self.cancelAddScreen();
			}else{
				self.processFailureResponse(data.result);
			}
		}
	}

	function errorCallback(reqType) {
		showMessageBox(uiConstants.applicationMultipleAddEdit.ERROR_ADD_MULTIPLE_APPLICATIONS, "error");
	}
}
AddMultiple.prototype.dispose = function() { };
return { viewModel: AddMultiple, template: templateMarkup };

});