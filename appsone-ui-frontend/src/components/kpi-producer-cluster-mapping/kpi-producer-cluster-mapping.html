<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default">
	<div class="configPanel panel-heading"><h4><span data-bind="text: pageSelected"></span></h4></div>
	<div class="panel-body" id="divKpiProducerMap">

		<form class="form-horizontal" role="form" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group">
				<div class="col-sm-12 wrapper-scroll-table" style="margin-bottom: 20px; max-height: 400px;">
					<!-- <div style="padding-bottom: 2px">
						<span class="glyphicon glyphicon-plus" data-bind="event:{click: configureKpiProducerMap}" title="Add"></span>
					</div> -->

					<table id="kpiProducerMapList" class="table table-bordered table-hover table-striped" style="width:100%;">
						<thead>
							<tr class="a1-inner-table-thead">
								<th style="display: none">KPI ID</th>
								<th class="tableHeaderOverflowOmmiter col-xs-3">KPI Name <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-1">KPI Unit</th>
								<th class="tableHeaderOverflowOmmiter col-xs-1">KPI Type</th>
								<!-- <th class="tableHeaderOverflowOmmiter col-xs-1"><input type="checkbox" id ="chkboxMappingAnalyticsHeader" title="Select All"/> Analytics</th> -->
								<th class="tableHeaderOverflowOmmiter col-xs-2" style="width: 200px">Default Producer <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-3" style="width: 200px" colspan="2">Collection Interval <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-1"><input type="checkbox" id ="chkboxMappingStatusHeader" title="Select All"/> Data Collection</th>
								<!-- <th class="tableHeaderOverflowOmmiter" style="width: 30px"></th> -->
							</tr>
						</thead>
						<tbody data-bind="foreach : kpiMapRowsArr">
							<tr data-bind="visible: !$data.hidden">
								<td style="display: none">
									<span data-bind="attr: {'id': 'kpiId'+$index()}">
									</span>
								</td>
								<td>
									<div class="input-group" style="margin-bottom: 5px">
										<span data-bind="attr: {id: 'kpiTxt'+$index()}"></span>
										<!-- <select class="chosen form-control" data-bind="optionsValue: function(item) {return item.kpiId ? 'kpi'+item.kpiId : 'group'+item.kpiGroupId}, attr: {id: 'kpiList'+$index()}, options: $parent.kpisList(),
                       					optionsText:  function(item) {return item.kpiId ? item.kpiName : item.kpiGroupName},
                       					value: function(item) {return item.kpiId ? 'kpi'+item.kpiId : 'group'+item.kpiGroupId}, event:{change: $parent.getProducersList.bind($data, $index()), 'chosen:showing_dropdown': $parent.onKpiLstOpen.bind($data, $index())}" data-placeholder=" "></select>
										


									 	<span class="input-group-addon glyphicon glyphicon-plus input-group-btnadd" type="button" data-bind="attr: {'id':'addGroupVal'+$index()}, visible : $parent.currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: function(){$parent.addKpiGroupValue($index())}}" title="Add Group Value"></span> -->
									</div>
								</td>

								<td class="textOverflowOmmiter" style="text-align: center">
									<span data-bind="attr: {'id': 'kpiUnit'+$index()}">
									</span>
								</td>

								<td class="textOverflowOmmiter" style="text-align: center">
									<span data-bind="attr: {'id': 'kpiType'+$index()}">
									</span>
								</td>

								<!-- <td style="text-align:center" class="textOverflowOmmiter">
									<input class="chkboxMappingAnalyticsCol" type="checkbox" data-bind="attr: {'id': 'kpiAvailAnalytics'+$index()}"></input>
								</td> -->

								<td class="producerClass">
									<!-- <div class="input-group"> -->
										<select class="chosen form-control" data-bind="optionsValue: 'producerId', attr: {id: 'producer'+$index()}, options: $parent.producerArr()[$index()],
                       					optionsText: 'producerName',
                       					value: 'producerId'" data-placeholder=" "></select>
										<!-- <span class="input-group-addon glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parent.setProducerModalParamData.bind($data, $index())}" data-toggle="modal" title="Add Producer"></span> -->
									<!-- </div> -->
								</td>

								<td style="padding-left: 5px; border-right: none;">
									<input class="form-control" type="number" data-bind="attr: {'id': 'collInterval'+$index()}" min="1" value="1" style="width: 100%"></input>
								</td>

								<td style="border-left: none; padding-left: 0px;">
									<select class="chosen form-control" data-bind="attr: {'id': 'collIntervalUnit'+$index()}">
										<option value="seconds">Seconds</option>
										<option value="minutes" selected>Minute(s)</option>
									</select>
								</td>

								<td style="text-align:center">
									<input class="chkboxMappingStatusCol" type="checkbox" data-bind="attr: {'id': 'status'+$index()}" selected></input>
								</td>

								<!-- <td style="text-align:center">
									<span type="button" data-bind="attr: {disabled: $data.existing && $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}, css: {confButtonDisabled: $data.existing &&  $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}" class="glyphicon glyphicon-remove buttondelete" title="Delete" style="width: 15px;"></span>
								</td> -->
							</tr>
						</tbody>
					</table>
				</div>
				
				<div class="col-sm-12 wrapper-scroll-table" style="max-height: 400px;">
					<table id="kpiGroupProducerMapList" class="table table-bordered table-hover table-striped" style="width:100%;">
						<thead>
							<tr class="a1-inner-table-thead">
								<th style="display: none">KPI Group ID</th>
								<th style="display: none">KPI Group Pattern</th>
								<th class="tableHeaderOverflowOmmiter col-xs-3">KPI Group Name <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-1">KPI Unit</th>
								<th class="tableHeaderOverflowOmmiter col-xs-1">KPI Type</th>
								<!-- <th class="tableHeaderOverflowOmmiter col-xs-1"><input type="checkbox" id ="chkboxMappingAnalyticsHeader" title="Select All"/> Analytics</th> -->
								<th class="tableHeaderOverflowOmmiter col-xs-2" style="width: 200px">Default Producer <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-3" style="width: 200px" colspan="2">Collection Interval <span class="mandatoryField">*</span></th>
								<th class="tableHeaderOverflowOmmiter col-xs-1"><input type="checkbox" id ="chkboxMappingGroupStatusHeader" title="Select All"/> Data Collection</th>
								
								<!-- <th class="tableHeaderOverflowOmmiter" style="width: 30px"></th> -->
							</tr>
						</thead>
						<tbody data-bind="foreach : kpiGroupMapRowsArr">
							<tr data-bind="visible: !$data.hidden">
								<td style="display: none">
									<span data-bind="attr: {'id': 'kpiGroupId'+$index()}">
									</span>
								</td>
								<td style="display: none">
									<span data-bind="attr: {'id': 'kpiGroupPattern'+$index()}">
									</span>
								</td>
								<td>
									<div class="input-group" style="margin-bottom: 5px">
										<span data-bind="attr: {id: 'kpiGroupTxt'+$index()}"></span>
										<!-- <select class="chosen form-control" data-bind="optionsValue: function(item) {return item.kpiId ? 'kpi'+item.kpiId : 'group'+item.kpiGroupId}, attr: {id: 'kpiList'+$index()}, options: $parent.kpisList(),
                       					optionsText:  function(item) {return item.kpiId ? item.kpiName : item.kpiGroupName},
                       					value: function(item) {return item.kpiId ? 'kpi'+item.kpiId : 'group'+item.kpiGroupId}, event:{change: $parent.getProducersList.bind($data, $index()), 'chosen:showing_dropdown': $parent.onKpiLstOpen.bind($data, $index())}" data-placeholder=" "></select> -->
										

										<!-- ko if: $data.discoveryFlag == 0 -->
									 		<span class="input-group-addon glyphicon glyphicon-plus input-group-btnadd" type="button" data-bind="attr: {'id':'addGroupVal'+$index()}, visible : $parent.currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: function(){$parent.addKpiGroupValue($index())}}" title="Add Group Value"></span>
										<!-- /ko-->
									</div>

									<!-- ko if: $data.discoveryFlag == 0 && $parent.kpiGroupValueArr()[$index()] -->
										<div data-bind="foreach : $parent.kpiGroupValueArr()[$index()]" style="max-height: 110px; overflow-y: scroll;">
											<div style="margin-left: 20px; margin-bottom: 5px;">
												
												<div class="input-group col-sm-12" style="padding-right: 3px; margin-left: -5px;">
													<!-- <span class="input-group-addon">KPI Group Value</span> -->

													<span class="input-group-addon input-grp-label" style="width: 20%;" data-bind="text: 'Value '+($index()+1)+':'"></span>

													<input type="text" data-bind="enable: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW, value: $data.kpiGroupValue, attr: {'id': 'kpiGroupVal'+$parentContext.$index()+$index()}" class="form-control small-form-control" max="45" pattern="[a-zA-Z0-9_ ]{2,45}">

													<!-- ko if: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW -->
														<span class="input-group-addon glyphicon glyphicon-remove buttondeleteGroup input-group-btndelete" data-bind="event:{click: function(){$parents[1].removeKpiGroupValue($parentContext.$index(), $index())}}" title="Delete"></span>
													<!-- /ko-->
												</div>
											</div>
										</div>
									<!-- /ko-->
								</td>

								<td class="textOverflowOmmiter" style="text-align: center">
									<span data-bind="attr: {'id': 'kpiGroupUnit'+$index()}">
									</span>
								</td>

								<td class="textOverflowOmmiter" style="text-align: center">
									<span data-bind="attr: {'id': 'kpiGroupType'+$index()}">
									</span>
								</td>

								<!-- <td style="text-align:center" class="textOverflowOmmiter">
									<input class="chkboxMappingAnalyticsCol" type="checkbox" data-bind="attr: {'id': 'kpiAvailAnalytics'+$index()}"></input>
								</td> -->

								<td class="producerClass">
									<!-- <div class="input-group"> -->
										<select class="chosen form-control" data-bind="optionsValue: 'producerId', attr: {id: 'kpiGroupProducer'+$index()}, options: $parent.kpiGroupProducerArr()[$index()],
                       					optionsText: 'producerName',
                       					value: 'producerId'" data-placeholder=" "></select>
										<!-- <span class="input-group-addon glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parent.setProducerModalParamData.bind($data, $index())}" data-toggle="modal" title="Add Producer"></span> -->
									<!-- </div> -->
								</td>

								<td style="padding-left: 5px; border-right: none;">
									<input class="form-control" type="number" data-bind="attr: {'id': 'kpiGroupCollInterval'+$index()}" min="1" value="1" style="width: 100%"></input>
								</td>

								<td style="border-left: none; padding-left: 0px;">	
									<select class="chosen form-control" data-bind="attr: {'id': 'kpiGroupCollIntervalUnit'+$index()}">
										<option value="seconds">Seconds</option>
										<option value="minutes" selected>Minute(s)</option>
									</select>
								</td>

								<td style="text-align:center">
									<input class="chkboxMappingGroupStatusCol" type="checkbox" data-bind="attr: {'id': 'kpiGroupStatus'+$index()}" selected></input>
								</td>

								<!-- <td style="text-align:center">
									<span type="button" data-bind="attr: {disabled: $data.existing && $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}, css: {confButtonDisabled: $data.existing &&  $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}" class="glyphicon glyphicon-remove buttondelete" title="Delete" style="width: 15px;"></span>
								</td> -->
							</tr>
						</tbody>
					</table>
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-2">
					<button type ="button" id="btnSave" class="btn btn-primary" data-bind="event:{click: updateKpiProducerMapping}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}">Cancel</button>
				</div>
			</div>

			<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" style="overflow: scroll;" data-backdrop="static" data-keyboard="false">
		   		<div class="modal-dialog" style="width: 80%">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4>Add Producer</h4>
			            </div>	
			            <div data-bind="if : displayConfig()">
			        		<producer-add-edit params="{isModal: true, selectedComponentTypeObj: selectedComponentTypeObj, selectedComponentObj: selectedComponentObj, selectedKpiObj: selectedKpiObj, isKpiGroup: isKpiGroup, selectedVersionObj: selectedVersionObj, componentsArr: componentsArr, currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows,
			        		modalConfigName: modalConfigName}"></producer-add-edit>
			        	</div>
			        </div>
		        </div>
		    </div>
		</form>
	</div>
</div>