define(['jquery','knockout','jquery-chosen','text!./kpi-producer-cluster-mapping.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

	function KpiProducerClusterMapping(params) {
		var self = this;
		this.kpiMapRowsArr = ko.observableArray();
		this.kpiGroupMapRowsArr = ko.observableArray();
		this.kpiProdMapArr = ko.observableArray();
		this.producerMasterArr = ko.observableArray([{"producerName" : "Select Producer", "producerId" : 0}]);
		this.producerArr = ko.observableArray();
		this.kpiGroupProducerArr = ko.observableArray();
		this.kpiProduceMapArr = ko.observableArray();
		this.kpiProdMapFormattedArr = ko.observableArray();
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.pageSelected = params.pageSelected;
		this.currentViewIndex = params.currentViewIndex;
		this.kpisList = ko.observableArray([]);
		this.selectedComponentTypeObj = ko.observable()
		this.selectedComponentObj = ko.observable();
		this.selectedKpiObj = ko.observable();
		this.isKpiGroup = ko.observable(false);
		this.selectedVersionObj = ko.observable();
		this.displayConfig = ko.observable(false);
		this.componentsArr = params.componentsArr;
		this.modalConfigName = ko.observable("");
		//this.kpisExistingList = ko.observableArray();
		this.mappingType = ko.observable(params.mappingType);
		this.kpiGroupValueArr = ko.observableArray();
		var selectedProducerIdArr = [];
		var kpisLoaded = 0;
		var producersLoaded = 0;
		var producersToListRow;
		var kpiGrpStartIndex = 0;
  		var kpiGroupValArr=[];
		var kpiGrpImgEle = "<img style='margin-right: 2px;' src='/images/group16.gif' title='KPI Group'>";

		this.renderHandler = function(){
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			/*$("#idModal").on('hidden.bs.modal', function(){
				self.displayConfig(false);
				if(self.modalConfigName() != ""){
					for(kpiProducer in self.kpiMapRowsArr()){
						selectedProducerIdArr.push($("#producer"+kpiProducer).val());
					}

					requestCall(uiConstants.common.SERVER_IP + "/producerName", "GET", "", "getProducersMaster", successCallback, errorCallback);
				}
			});*/

			/*$("#kpiProducerMapList tbody").on('click', '.buttondelete', function(e){

				var rowToDelete = $(this).closest('tr').get(0).rowIndex-1;
				
				//if(DEBUG_MODE)console.log(kpiProducerMapDeleteObj);
				if(!self.kpiMapRowsArr()[rowToDelete].existing || self.kpiProdMapFormattedArr()[rowToDelete].isCustom == 1){
					if(uiConstants.common.DEBUG_MODE)console.log(self.kpiMapRowsArr());//[rowToDelete].

				    showMessageBox(uiConstants.kpiProducerMap.CONFIRM_KPI_PROD_MAPPING_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
						if (confirmDelete) {
					 		if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
							//self.kpiMapRowsArr.splice(rowToDelete, 1);
							//self.kpiProdMapFormattedArr.splice(rowToDelete, 1);

							self.kpiMapRowsArr.splice(rowToDelete, 1, {"hidden":true});

							//kpiGroupValArr[rowIndex].splice(itemIndex,1);
							self.kpiGroupValueArr.splice(rowToDelete, 1, {});

					 	}
					 });
				}
			});*/

			self.pageSelected(self.pageSelected() + " for " + self.selectedConfigRows()[0].clusterName);
			
			$('#kpiProducerMapList tbody').on('click', '.chkboxMappingStatusCol', function(e){
				setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol', self.kpiMapRowsArr());
			});

			$('#kpiProducerMapList thead').on('click', '#chkboxMappingStatusHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingStatusCol").prop("checked",false);
				}
				else {
					$(".chkboxMappingStatusCol").prop("checked",true);
				}
			});

			/*$('#kpiProducerMapList tbody').on('click', '.chkboxMappingAnalyticsCol', function(e){
				setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol', self.kpiMapRowsArr());
			});*/

			/*$('#kpiProducerMapList thead').on('click', '#chkboxMappingAnalyticsHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingAnalyticsCol").prop("checked",false);
				}
				else {
					$(".chkboxMappingAnalyticsCol").prop("checked",true);
				}
			});*/

			$('#kpiGroupProducerMapList tbody').on('click', '.chkboxMappingGroupStatusCol', function(e){
				setHeaderCheckboxState('#chkboxMappingGroupStatusHeader', '.chkboxMappingGroupStatusCol', self.kpiGroupMapRowsArr());
			});

			$('#kpiGroupProducerMapList thead').on('click', '#chkboxMappingGroupStatusHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingGroupStatusCol").prop("checked",false);
				}
				else {
					$(".chkboxMappingGroupStatusCol").prop("checked",true);
				}
			});

			/*$('#kpiGroupProducerMapList tbody').on('click', '.chkboxMappingAnalyticsCol', function(e){
				setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol', self.kpiGroupMapRowsArr());
			});

			$('#kpiGroupProducerMapList thead').on('click', '#chkboxMappingAnalyticsHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingAnalyticsCol").prop("checked",false);
				}
				else {
					$(".chkboxMappingAnalyticsCol").prop("checked",true);
				}
			});*/

			//var compVersionId = self.selectedConfigRows()[0].componentVersionId;
			//if(self.mappingType() == "component"){
				requestCall(uiConstants.common.SERVER_IP + "/kpis_KpiGroups", "GET", "", "getKpisList", successCallback, errorCallback);
			/*}
			else if(self.mappingType() == "cluster"){
				requestCall(uiConstants.common.SERVER_IP + "/clusterKpiGroups", "GET", "", "getKpisList", successCallback, errorCallback);
			}*/

			//requestCall(uiConstants.common.SERVER_IP + "/producerName", "GET", "", "getProducersMaster", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/5762b204100000541c8b14c3?callback=?", "GET", "", "getKpisList", successCallback, errorCallback);
		}

		function setHeaderCheckboxState(headerCheckbox, checkboxColumn, kpiMapArr){
			var hiddenMappingsLen = $.grep(kpiMapArr, function(e){console.log(e.hasOwnProperty("hidden")); return e.hasOwnProperty("hidden") == false; }).length;

			if ($(checkboxColumn + ':checked').length  == (hiddenMappingsLen)) {
				$(headerCheckbox).prop("checked",true);
			}
			else {
				$(headerCheckbox).prop("checked",false);
			}
		}

		/*function getProducersForKpi(){
			var isKpiGroup;

			if(self.isKpiGroup()){
				isKpiGroup = 1;
			}
			else{
				isKpiGroup = 0;
			}

			if(self.mappingType() == "component"){
				requestCall(uiConstants.common.SERVER_IP + "/producerMapping?componentVersionId=" + self.selectedConfigRows()[0].componentVersionId + "&isGroup=" + isKpiGroup + "&kpiId=" + (isKpiGroup ? self.selectedKpiObj().kpiGroupId : self.selectedKpiObj().kpiId), "GET", "", "getProducerMappingForKpi", successCallback, errorCallback);
			}
			else if(self.mappingType() == "cluster"){
				requestCall(uiConstants.common.SERVER_IP + "/producerMapping?clusterId=" + self.selectedConfigRows()[0].clusterId + "&isGroup=" + isKpiGroup + "&kpiId=" + (isKpiGroup ? self.selectedKpiObj().kpiGroupId : self.selectedKpiObj().kpiId), "GET", "", "getProducerMappingForKpi", successCallback, errorCallback);
			}
			//requestCall("http://www.mocky.io/v2/57500f34100000ac1875e9f1?callback=?", "GET", "", "getProducerMappingForKpi", successCallback, errorCallback);
		}*/

		this.removeKpiGroupValue = function(rowIndex, itemIndex){
		    showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_KPI_GROUP_VALUE_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if (confirmDelete) {
					kpiGroupValArr[rowIndex].splice(itemIndex,1);
					self.kpiGroupValueArr.splice(rowIndex, 1, kpiGroupValArr[rowIndex]);
				}

				console.log(self.kpiMapRowsArr());
			});
		}

		/*this.setProducerModalParamData = function(rowIndex){
			if($("#kpiList"+rowIndex).val() == "0"){
				showMessageBox("Please select KPI to add a producer", "error");
			}
			else{
				$('#idModal').modal('show');
				var selKpiObj;
				self.modalConfigName("");
				producersToListRow = rowIndex;
				self.displayConfig(true);

				self.selectedComponentTypeObj({
					"componentTypeId":self.selectedConfigRows()[0].componentTypeId,
					"componentType":self.selectedConfigRows()[0].componentType
				});

				self.selectedComponentObj({
					"componentId":self.selectedConfigRows()[0].componentId,
					"componentName":self.selectedConfigRows()[0].componentName
				});

				self.selectedVersionObj({
					"componentVersionId":self.selectedConfigRows()[0].componentVersionId,
					"componentVersion":self.selectedConfigRows()[0].componentVersion
				});

				if($("#kpiList"+rowIndex).val().startsWith("kpi")){
					self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiId == $("#kpiList"+rowIndex).val().substring(3); })[0]);
					self.isKpiGroup(false);
				}
				else if($("#kpiList"+rowIndex).val().startsWith("group")){
					console.log(self.kpisList());
					self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiGroupId == $("#kpiList"+rowIndex).val().substring(5); })[0]);
					self.isKpiGroup(true);
				}
			}
		}*/

		/*function setKpiGroupImg(rowIndex){
			$("#kpiList"+rowIndex+"_chosen")
		    .find("li.active-result").each(function() {

			    if (kpiGrpStartIndex==0 || $(this).attr("data-option-array-index") < (kpiGrpStartIndex+1)) //1 is added since the first item in the list will be the text indicating the user to select the option
			        return;

			   if($(this).find("img").length == 0){
				    $(this).prepend(kpiGrpImgEle);
				}
			});
		}*/

		this.onKpiLstOpen = function(rowIndex){
			setKpiGroupImg(rowIndex);
		}

		this.addKpiGroupValue = function(rowIndex){
			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupValueArr());
			if(kpiGroupValArr[rowIndex] == undefined){
				kpiGroupValArr[rowIndex] = [{'kpiGroupValue':''}];
			}
			else{
				kpiGroupValArr[rowIndex].push({'kpiGroupValue':''});
			}

			self.kpiGroupValueArr.splice(rowIndex, 1, kpiGroupValArr[rowIndex]);
			//self.kpiGroupValueArr()[rowIndex]=kpiGroupValArr[rowIndex];

			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGroupValueArr());
		}

		/*this.configureKpiProducerMap = function(){
			self.kpiMapRowsArr.push({'existing':false});
			var mapRowIndex = self.kpiMapRowsArr().length-1;

			kpiGroupValArr[mapRowIndex] = [];
			self.kpiGroupValueArr.splice(mapRowIndex, 1, kpiGroupValArr[mapRowIndex]);
			$("#addGroupVal"+mapRowIndex).css("display","table-column-group");

			self.producerArr.splice(self.kpiMapRowsArr().length, 1, [{}]);
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			$("#kpiList" + mapRowIndex + "_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#kpiUnit" + mapRowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiUnitName == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiUnitName);
			$("#kpiType" + mapRowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiType == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiType);
		
			$("#kpiList" + mapRowIndex + "_chosen .chosen-search").find("input").on("keyup", function (evt) {
				setKpiGroupImg(mapRowIndex);
			});

			$("#status"+mapRowIndex).prop('checked', true);
			scrollToPos($('#producer' + (mapRowIndex) + '_chosen').offset().top, 500);
		}*/

		this.getProducersList = function(rowIndex, kpiId, isGroup){
			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProdMapArr());
			var producersObj;
			self.selectedKpiObj(null);
			//var selKpiObj;

			if(!isGroup){
				//$("#addGroupVal"+rowIndex).css("display","table-column-group");

				self.isKpiGroup(false);
				self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiId == kpiId; })[0]);
				producersObj = $.grep(self.kpiProdMapArr().kpiDetails, function(e){ return e.kpiId == kpiId; })[0];
				$("#kpiUnit"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiUnitName == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiUnitName);
				$("#kpiType"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiType == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiType);
				
				self.producerArr.splice(rowIndex, 1, producersObj.producers.defaultId == undefined ? [{}] : producersObj.producers.all);
				$("#producer"+rowIndex).trigger('chosen:updated');
				$("#collIntervalUnit"+rowIndex).trigger('chosen:updated');

				//$("#producer1").trigger('chosen:updated');
			}
			else{
				//$("#addGroupVal"+rowIndex).css("display","");


				self.isKpiGroup(true);
				self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiGroupId == kpiId; })[0]);
				producersObj = $.grep(self.kpiProdMapArr().kpiGroups, function(e){ return e.kpiGroupId == kpiId; })[0];
				$("#kpiGroupUnit"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiUnitName == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiUnitName);
				$("#kpiGroupType"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiType == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiType);

				self.kpiGroupProducerArr.splice(rowIndex, 1, producersObj.producers.defaultId == undefined ? [{}] : producersObj.producers.all);
				$("#kpiGroupProducer"+rowIndex).trigger('chosen:updated');
				$("#kpiGroupCollIntervalUnit"+rowIndex).trigger('chosen:updated');

				//$("#producer1").trigger('chosen:updated');

				kpiGroupValArr[rowIndex] = [];
				self.kpiGroupValueArr.splice(rowIndex, 1, kpiGroupValArr[rowIndex]);
			}
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			
			//$("#kpiList" + rowIndex + "_chosen span").prop("title",$("#kpiList" + rowIndex + "_chosen span").text());

			if(uiConstants.common.DEBUG_MODE)console.log(producersObj);

			/*if(producersObj){ 
				self.producerArr.splice(rowIndex, 1, producersObj.producers.defaultId == undefined ? [{}] : producersObj.producers.all);
				$("#producer"+rowIndex).trigger('chosen:updated');

				//$("#producer1").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}*/
			/*else{
				producersToListRow = rowIndex;
				if(self.selectedKpiObj() != null){
					getProducersForKpi();	
				}
			}*/

			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedKpiObj());

		}

		function onMastersLoad(){
			if(kpisLoaded == 1){
				if(self.mappingType() == "component"){
					var compVersionId = self.selectedConfigRows()[0].componentVersionId; //Send this to API call for getting KPI/Producer mapping
					requestCall(uiConstants.common.SERVER_IP + "/componentKpiProducerMapping/" + compVersionId, "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
				}
				else if(self.mappingType() == "cluster"){
					requestCall(uiConstants.common.SERVER_IP + "/clusterKpiMapping/" + self.selectedConfigRows()[0].clusterId+"?status=2&markInactive=1", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
				}
				//requestCall("http://www.mocky.io/v2/575d2d710f00007b1222ade4?callback=?", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
			}
		}

		function setKpiProducerMapping(){
			var kpiGroupValsArr = [];
			var kpiCounter = 0;
			var kpiGroupCounter = 0;

			console.log(self.kpiProdMapFormattedArr());

			for(kpiProducer in self.kpiProdMapFormattedArr()){
				if(self.kpiProdMapFormattedArr()[kpiProducer].kpiId){
					self.kpiMapRowsArr.push({'existing':true});
					$("#kpiId"+kpiCounter).text(self.kpiProdMapFormattedArr()[kpiProducer].kpiId);
					$("#kpiTxt"+kpiCounter).text(self.kpiProdMapFormattedArr()[kpiProducer].kpiName);
					self.getProducersList(kpiCounter, self.kpiProdMapFormattedArr()[kpiProducer].kpiId, false);

					$("#producer"+kpiCounter).val(self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId);
					//$("#producer"+kpiCounter+"_chosen").css({"width": "100px"});
					$("#producer"+kpiCounter+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#producer"+kpiCounter+"_chosen").css("width") + ' !important;' });
					$("#producer"+kpiCounter).trigger('chosen:updated');

					$("#producer"+kpiCounter+"_chosen span").prop("title",$("#producer"+kpiCounter+"_chosen span").text());


					var collInterval = self.kpiProdMapFormattedArr()[kpiProducer].collectionInterval;
					if(collInterval > 30){
						var collInterval = collInterval/60;
						$("#collIntervalUnit"+kpiCounter).val("minutes");
					}
					else{
						$("#collIntervalUnit"+kpiCounter).val("seconds");
					}
					$("#collInterval"+kpiCounter).val(collInterval).trigger('chosen:updated');
					$("#status"+kpiCounter).prop("checked", self.kpiProdMapFormattedArr()[kpiProducer].status == 1);
					kpiCounter++;
				}
				else{
					debugger;
					self.kpiGroupMapRowsArr.push({'existing':true, 'discoveryFlag': self.kpiProdMapFormattedArr()[kpiProducer].discoveryFlag});
					$("#kpiGroupId"+kpiGroupCounter).text(self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupId);
					$("#kpiGroupPattern"+kpiGroupCounter).text(self.kpiProdMapFormattedArr()[kpiProducer].pattern);
					$("#kpiGroupTxt"+kpiGroupCounter).text(self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupName);
					self.getProducersList(kpiGroupCounter, self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupId, true);

					$("#kpiGroupProducer"+kpiGroupCounter).val(self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId);
					//$("#producer"+kpiGroupCounter+"_chosen").css({"width": "100px"});
					$("#kpiGroupProducer"+kpiGroupCounter+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#kpiGroupProducer"+kpiGroupCounter+"_chosen").css("width") + ' !important;' });
					$("#kpiGroupProducer"+kpiGroupCounter).trigger('chosen:updated');

					$("#kpiGroupProducer"+kpiGroupCounter+"_chosen span").prop("title",$("#kpiGroupProducer"+kpiGroupCounter+"_chosen span").text());

					var collInterval = self.kpiProdMapFormattedArr()[kpiProducer].collectionInterval;
					if(collInterval > 30){
						var collInterval = collInterval/60;
						$("#kpiGroupCollIntervalUnit"+kpiGroupCounter).val("minutes");
					}
					else{
						$("#kpiGroupCollIntervalUnit"+kpiGroupCounter).val("seconds");
					}
					$("#kpiGroupCollInterval"+kpiGroupCounter).val(collInterval).trigger('chosen:updated');
					$("#kpiGroupStatus"+kpiGroupCounter).prop("checked", self.kpiProdMapFormattedArr()[kpiProducer].status == 1);

					kpiGroupValsArr = self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupValues;

					for(var kpiGroup in kpiGroupValsArr){
						self.addKpiGroupValue(kpiGroupCounter);

						//kpiGroupValArr[i][kpiGroup].kpiGroupValue = kpiGroupValsArr[kpiGroup];

						$("#kpiGroupVal"+kpiGroupCounter+kpiGroup).val(kpiGroupValsArr[kpiGroup]);
					}

					kpiGroupCounter++;
				}

				//i++;
				//console.log(self.kpiProdMapFormattedArr()[kpiProducer]);

				//$("#kpiAvailAnalytics"+kpiProducer).prop('checked', self.kpiProdMapFormattedArr()[kpiProducer].availableForAnalytics == 1);

				//addInactiveToList(self.producerArr()[kpiProducer], self.kpiProdMapFormattedArr()[kpiProducer].producers, 'producerId', 'producerName', self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId, self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultProducerName, '#producer'+kpiProducer);
				/*var obj = self.producerArr()[kpiProducer];
				self.producerArr.splice(kpiProducer, 1);
				self.producerArr.splice(kpiProducer, 0, obj);

				if(uiConstants.common.DEBUG_MODE)console.log(self.producerArr());*/


				/*$("#producer"+kpiProducer).val(self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId);
				//$("#producer"+kpiProducer+"_chosen").css({"width": "100px"});
				$("#producer"+kpiProducer+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#producer"+kpiProducer+"_chosen").css("width") + ' !important;' });
				$("#producer"+kpiProducer).trigger('chosen:updated');

				$("#producer"+kpiProducer+"_chosen span").prop("title",$("#producer"+kpiProducer+"_chosen span").text());


				var collInterval = self.kpiProdMapFormattedArr()[kpiProducer].collectionInterval;
				if(collInterval > 30){
					var collInterval = collInterval/60;
					$("#collIntervalUnit"+kpiProducer).val("minutes");
				}
				else{
					$("#collIntervalUnit"+kpiProducer).val("seconds");
				}
				$("#collInterval"+kpiProducer).val(collInterval);
				$("#status"+kpiProducer).prop("checked", self.kpiProdMapFormattedArr()[kpiProducer].status == 1);*/
				//console.log($(".chosen-container").offsetWidth);
				//	$(".chosen-container").attr('style', 'width: '+(195-42)+'px !important');
			}

			if(kpiCounter>0){
				setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol', self.kpiMapRowsArr());
			}
			if(kpiGroupCounter>0){
				setHeaderCheckboxState('#chkboxMappingGroupStatusHeader', '.chkboxMappingGroupStatusCol', self.kpiGroupMapRowsArr());
			}

			//for(i in self.kpiProdMapFormattedArr()){
				//kpiGroupValsArr = [];
				/*$("#kpiList" + i + "_chosen span").prop("title",$("#kpiList"+i + "_chosen span").text());
				$("#kpiList" + i + "_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});

				if(self.kpiProdMapFormattedArr()[i].kpiGroupId){
					$("#kpiList"+i+"_chosen span").prepend(kpiGrpImgEle);
				}*/
				
			//}

			jQuery(".chosen").chosen({
				search_contains: true	
			});

			var $tab = $('table');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			//setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol');
		}

		/*var producersChosenElementArr = [];
		function addInactiveToList(allData, dataToAdd, allDataIdKey, allDataNameKey, dataToAddId, dataToAddName, configElement){
				if(!allData.find(function( ele ) {return ele[allDataIdKey] && ele[allDataIdKey] === dataToAddId;} )){
					var obj = {};
					obj[allDataIdKey] = dataToAddId;
					obj[allDataNameKey] = dataToAddName;
					obj["isActive"] = false;
					allData.push(obj);

					producersChosenElementArr.push(configElement);

					sortArrayObjByValue(allData, allDataNameKey);
				}
				if(uiConstants.common.DEBUG_MODE)console.log(allData);
				$(configElement).trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
		}*/

		self.errorMsg.subscribe(function(errorField) {
	        if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		this.updateKpiProducerMapping = function(){
			var kpiProdMapDataNonGroupArr = [];
			var kpiProdMapDataGroupArr = [];
			var validationCounter;
			var selKpiId = 0;
			var kpiProducerObj = {};
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			for(var kpiIndex in self.kpiMapRowsArr()){
				if($("#collInterval"+kpiIndex).val() == ""){
					showError("#collInterval", uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
		    		self.errorMsg("#collInterval");
				}
				else if($("#collIntervalUnit"+kpiIndex).val() == "seconds" &&
					parseInt($("#collInterval"+kpiIndex).val()) != 15 && parseInt($("#collInterval"+kpiIndex).val()) != 30){
					
					showError("#collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
		    		self.errorMsg("#collInterval"+kpiIndex);
				}
				else if($("#collIntervalUnit"+kpiIndex).val() == "minutes" &&
					(parseInt($("#collInterval"+kpiIndex).val()) < 1 || parseInt($("#collInterval"+kpiIndex).val()) >1440)){

					showError("#collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
		    		self.errorMsg("#collInterval"+kpiIndex);
				}

				if(self.errorMsg() == ""){
					kpiProducerObj = $.grep(self.producerArr()[kpiIndex], function(e){ return e.producerId == $("#producer"+kpiIndex+" option:selected").val(); });

					kpiProdMapDataNonGroupArr.push({
				        "kpiId": $("#kpiId"+kpiIndex).text(),
				        "producerId": parseInt($("#producer"+kpiIndex+" option:selected").val()),
				        "availableForAnalytics": $("#kpiAvailAnalytics"+kpiIndex).prop('checked')?1:0,
				        "collectionInterval": $("#collIntervalUnit"+kpiIndex).val() == "minutes" ? parseInt($("#collInterval"+kpiIndex).val()) * 60 : parseInt($("#collInterval"+kpiIndex).val()),
				        "kpiProducerMappingId": kpiProducerObj[0].kpiProducerMappingId,
				        "status": $("#status"+kpiIndex).prop('checked')?1:0
				    });
				}
			}


			for(var kpiGroupIndex in self.kpiGroupMapRowsArr()){
				if($("#kpiGroupCollInterval"+kpiGroupIndex).val() == ""){

					showError("#kpiGroupCollInterval"+kpiGroupIndex, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
		    		self.errorMsg("#kpiGroupCollInterval"+kpiGroupIndex);
				}
				else if($("#kpiGroupCollIntervalUnit"+kpiGroupIndex).val() == "seconds" &&
					parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()) != 15 && parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()) != 30){

					showError("#kpiGroupCollInterval"+kpiGroupIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
		    		self.errorMsg("#kpiGroupCollInterval"+kpiGroupIndex);
				}
				else if($("#kpiGroupCollIntervalUnit"+kpiGroupIndex).val() == "minutes" &&
					(parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()) < 1 || parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()) >1440)){

					showError("#kpiGroupCollInterval"+kpiGroupIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
		    		self.errorMsg("#kpiGroupCollInterval"+kpiGroupIndex);
				}

				if(self.errorMsg() == ""){
					var kpiGroupValObjArr = [];
					for(kpiVal in kpiGroupValArr[kpiGroupIndex]){
						if($("#kpiGroupVal"+kpiGroupIndex+kpiVal).val()){
							if($("#kpiGroupVal"+kpiGroupIndex+kpiVal).val().trim() == ""){
								
								showError("#kpiGroupVal"+kpiGroupIndex+kpiVal, uiConstants.componentInstanceConfig.ERROR_KPI_GROUP_VALUE_REQUIRED);
		    					self.errorMsg("#kpiGroupVal"+kpiGroupIndex+kpiVal);
								//break;
							}
							else if(!isRegexValid($("#kpiGroupPattern"+kpiGroupIndex).text(), $("#kpiGroupVal"+kpiGroupIndex+kpiVal).val())){
								showError("#kpiGroupVal"+kpiGroupIndex+kpiVal, "Regex "+$("#kpiGroupPattern"+kpiGroupIndex).text()+" is invalid");
		    					self.errorMsg("#kpiGroupVal"+kpiGroupIndex+kpiVal);
							}
							else if($("#kpiGroupPattern"+kpiGroupIndex).text() && $("#kpiGroupPattern"+kpiGroupIndex).text()!="undefined" && $("#kpiGroupVal"+kpiGroupIndex+kpiVal).val() != (($("#kpiGroupVal"+kpiGroupIndex+kpiVal).val()).match(new RegExp($("#kpiGroupPattern"+kpiGroupIndex).text(), "g")).join(""))){
								showError("#kpiGroupVal"+kpiGroupIndex+kpiVal, "Value does not match the Regex pattern " + $("#kpiGroupPattern"+kpiGroupIndex).text());
		    					self.errorMsg("#kpiGroupVal"+kpiGroupIndex+kpiVal);
							}
							if(self.errorMsg() == ""){
								kpiGroupValObjArr.push($("#kpiGroupVal"+kpiGroupIndex+kpiVal).val().trim());
								if(uiConstants.common.DEBUG_MODE)console.log(kpiGroupValObjArr);
							}
						}
					}

					kpiProducerObj = $.grep(self.kpiGroupProducerArr()[kpiGroupIndex], function(e){ return  e.producerId == $("#kpiGroupProducer"+kpiGroupIndex+" option:selected").val(); });

					kpiProdMapDataGroupArr.push({
			       		"kpiGroupId": $("#kpiGroupId"+kpiGroupIndex).text(),
				        "producerId": parseInt($("#kpiGroupProducer"+kpiGroupIndex+" option:selected").val()),
				        "collectionInterval": $("#kpiGroupCollIntervalUnit"+kpiGroupIndex).val() == "minutes" ? parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()) * 60 : parseInt($("#kpiGroupCollInterval"+kpiGroupIndex).val()),
				        "kpiProducerMappingId": kpiProducerObj[0].kpiProducerMappingId,
				        "status": $("#kpiGroupStatus"+kpiGroupIndex).prop('checked')?1:0,
				        "kpiGroupValues": kpiGroupValObjArr
				    });
				}
			}

			if(self.errorMsg() == ""){
				//if(uiConstants.common.DEBUG_MODE)console.log(kpiProdMapDataArr);
				var kpiProdMapData = {};

				if(self.mappingType() == "component"){
					kpiProdMapData = {
				    	"index": 1,
				    	"componentId": parseInt(self.selectedConfigRows()[0].componentId),
				    	"componentVersionId": parseInt(self.selectedConfigRows()[0].componentVersionId),
				    	"kpiDetails": kpiProdMapDataNonGroupArr,
				    	"kpiGroups": kpiProdMapDataGroupArr
					};
				}
				else if(self.mappingType() == "cluster"){
					kpiProdMapData = {
				    	"index": 1,
				    	"clusterId": parseInt(self.selectedConfigRows()[0].clusterId),
				    	"kpiDetails": kpiProdMapDataNonGroupArr,
				    	"kpiGroups": kpiProdMapDataGroupArr
					};
				}

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(kpiProdMapData));
				if(self.mappingType() == "component"){
					requestCall(uiConstants.common.SERVER_IP + "/componentKpiMapping", "PUT", JSON.stringify(kpiProdMapData), "updateKpiProducerMapping", successCallback, errorCallback);
				}
				else if(self.mappingType() == "cluster"){
					requestCall(uiConstants.common.SERVER_IP + "/clusterKpiMapping/" + self.selectedConfigRows()[0].clusterId, "PUT", JSON.stringify(kpiProdMapData), "updateKpiProducerMapping", successCallback, errorCallback);
				}
				//requestCall("http://www.mocky.io/v2/572afece1300009802e2b7a2", "PUT", JSON.stringify(kpiProdMapData), "updateKpiProducerMapping", successCallback, errorCallback);
			}
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("Cluster Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		this.checkInactive = function(dataObj, configElement, configId){
			$(configElement+"_chosen span").prop("title",$(configElement+"_chosen span").text());
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function successCallback(data, reqType) {
			if(reqType === "getKpisList"){
				self.kpisList([{"kpiName" : "Select KPI", "kpiId" : "0"}]);
				self.kpisList(self.kpisList().concat(data.result.kpiDetails));

				//self.kpisList(data.result.kpiDetails);

				if(data.result.kpiGroups.length > 0){
					kpiGrpStartIndex = self.kpisList().length;
					self.kpisList(self.kpisList().concat(data.result.kpiGroups));
				}

				if(uiConstants.common.DEBUG_MODE)console.log(kpiGrpStartIndex);
				kpisLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getKpiProducerMapping"){
				self.kpiProdMapArr(data.result);
				//self.producerMasterArr(data.result.allProducers);
				//var kpiProducerMapping = data.result;

				if(data.result.kpiDetails){
					self.kpiProdMapFormattedArr($.grep(data.result.kpiDetails, function(e){ return  e.producers.defaultId!=undefined; }));
				}

				if(data.result.kpiGroups){
					self.kpiProdMapFormattedArr(self.kpiProdMapFormattedArr().concat($.grep(data.result.kpiGroups, function(e){ return  e.producers.defaultId!=undefined; })));
				}

				if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProdMapFormattedArr());
				setKpiProducerMapping();

				/*for(producer in producersChosenElementArr){
					$(producersChosenElementArr[producer] + "_chosen span").first().addClass("inactiveOptionClass").trigger('chosen:updated');
				}*/
			}
			else if(reqType === "getProducersMaster"){
				self.producerMasterArr([{"producerName" : "Select Producer", "producerId" : 0}]);
				self.producerMasterArr(self.producerMasterArr().concat(data.result));

				if(selectedProducerIdArr.length > 0){
					for(var producerId in selectedProducerIdArr){
						$("#producer"+producerId).trigger('chosen:updated');
						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}


					for(var kpiProducer in selectedProducerIdArr){
						$("#producer"+kpiProducer).val(selectedProducerIdArr[kpiProducer]);
						$("#producer"+kpiProducer).trigger('chosen:updated');
					}
				}

				selectedProducerIdArr = [];
				producersLoaded = 1;

				if(self.modalConfigName() != ""){
					var producerToSelectObj = $.grep(self.producerMasterArr(), function(e){return e.producerName == self.modalConfigName(); })[0];
					$("#producer"+producersToListRow).val(producerToSelectObj.producerId).trigger('chosen:updated');
					self.modalConfigName(""); 
				}
				else{
					onMastersLoad();
				}
			}
			/*else if(reqType === "getProducerMappingForKpi"){
				var selDefaultProducerId = $("#producer"+producersToListRow).val();
				
				if(uiConstants.common.DEBUG_MODE)console.log(selDefaultProducerId);

				if(data.result.producers.all.length == 0){
					self.producerArr.splice(producersToListRow, 1, [{"value":"0"}]);
				}
				else{

					self.producerArr.splice(producersToListRow, 1, data.result.producers.all);
				}


				if(uiConstants.common.DEBUG_MODE)console.log(self.producerArr());

				var producerToSelectObj = $.grep(data.result.producers.all, function(e){return e.producerName == self.modalConfigName(); })[0];
				console.log(producerToSelectObj);

				if(selDefaultProducerId == "0" && self.modalConfigName()!=""){
					$("#producer"+producersToListRow).val(producerToSelectObj.producerId);
				}
				else{
					$("#producer"+producersToListRow).val(selDefaultProducerId);
				//}
				$("#producer"+producersToListRow+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#producer"+producersToListRow+"_chosen").css("width") + ' !important;' });
				//$("#producer"+kpiProducer).trigger('chosen:updated');

				$("#producer"+producersToListRow).trigger('chosen:updated');
				$("#producer" + producersToListRow + "_chosen span").prop("title",$("#producer" + producersToListRow + "_chosen span").text());
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				self.modalConfigName("");
			}*/
			else if(reqType === "updateKpiProducerMapping"){
				var res = data.result;

				if(data.responseStatus.toUpperCase() == "FAILURE"){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined)
						showMessageBox(res[0].message, "error");
					else
						showMessageBox(data.message, "error");
				}
				else{
					showMessageBox(uiConstants.kpiProducerMap.SUCCESS_UPDATE_KPI_PRODUCER_MAP);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getKpiProducerMapping"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_KPI_PRODUCER_MAP, "error");
			}
			else if(reqType === "getKpisList"){
				showMessageBox(uiConstants.common.ERROR_GET_KPIS, "error");
			}
			/*else if(reqType === "getProducerMappingForKpi"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_GET_PRODUCERS_FOR_KPI, "error");
			}*/
			else if(reqType === "updateKpiProducerMapping"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_UPDATE_KPI_PRODUCER_MAP, "error");
			}
		}
	}

	KpiProducerClusterMapping.prototype.dispose = function() { };
	return { viewModel: KpiProducerClusterMapping, template: templateMarkup };
});