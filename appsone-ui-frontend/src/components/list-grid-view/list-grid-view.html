<div class="col-sm-12" id="listAppDetailsPage" data-bind="visible : currentViewIndex() == 0" >	 
	<div class="form-inline" role="form">
		<div class="form-group" style="padding-bottom: 5px;">
			<!-- <select class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}"></select>
			<label data-bind="text:uiConstants.common.PER_PAGE"></label> -->

			<h4 data-bind="if: currentViewIndex() == 0"><span class="a1-page-title" data-bind="text: pageSelected"></span></h4>
			<div data-bind="template: {afterRender: renderHandler}"></div>
		</div>
		
		<div class="form-group" style="float: right;">
			<span class="mandatoryField" data-bind="text: errorMsg()"></span>
			<button type="button" id="btnFilter" class="btn listViewBtn" data-bind="event: {click: function(){showFilterBox()}}"><span class="fa fa-filter listViewBtnTxt"></span>FILTER</button>

			<!-- ko if: wizardAddAvailable() -->
				<!-- <button id="addWizard" type="button" class="btn btn-primary" title="Add application through wizard" data-bind="attr: {disabled: !enableAdd()}, event: {click: addWizardConfig}">Add Using Wizard <span class="glyphicon a1-glyphicon-action-btn"></span></button> -->
			<!-- /ko -->

			<!-- ko if: window.commonAddEnabled() -->
				<div id="divAddOptionsList" class="input-group">
					<button type="button" class="btn listViewBtn btn-primary" id="btnAdd" style="border-right: none;" data-bind="attr: {disabled: !enableAdd()}, event: {click: switchView.bind($data,uiConstants.common.ADD_SINGLE_VIEW)}"><span class="fa fa-plus listViewBtnTxt" style="color: #ffffff;"></span>ADD</button>

					<button type="button" id="addOptionsList" class="btn listViewBtn input-group-addon optionListButton" data-bind="attr: {disabled: !enableAdd()}, event: {click: function(){toggleAddOptions()}}"><span class="glyphicon glyphicon-menu-down"></span></button>
					<div class="addOptionsContainer" style="right: 0px !important; width: 160px;">
						<ul style="list-style-type: none; margin: 0; padding: 0;">
  							<li class="addOptionsChoice" data-bind="event: {click: function(){addWizardConfig()}}" style="border-bottom: 2px solid #E1E2E3;">Add Using Wizard</li>
  							<li class="addOptionsChoice" data-bind="event: {click: function(){onAddMultipleClick()}}">Add Multiple</li>
  						</ul>
					</div>
				</div>
			<!-- /ko -->

			<!-- ko if: window.commonAddEnabled() -->
				<!-- <button type="button" class="btn btn-primary" id="btnAddMultiple" data-bind="attr: {disabled: !enableAdd()}, click: onAddMultipleClick">Add Multiple <span class="glyphicon glyphicon-plus a1-glyphicon-action-btn"></span><span class="glyphicon glyphicon-plus a1-glyphicon-action-btn"></span></button> -->
			<!-- /ko -->

			<!-- ko if: window.commonUpdateEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnEdit" data-bind="attr: {disabled: !enableEdit()}, event: {click: editConfig}"><span class="fa fa-pencil-square-o listViewBtnTxt"></span>EDIT</button>
			<!-- /ko -->

			<!-- ko if: window.commonAddEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnClone" data-bind="attr: {disabled: !enableClone()}, event: {click: cloneConfig}"><span class="fa fa-clone listViewBtnTxt"></span>CLONE</button>
			<!-- /ko -->

			<span class="glyphicon glyphicon-cog" id="columnsOption" title="Show/Hide Columns" data-bind="event: {click: function(){toggleColumnsList()}}"></span>
			<div class="columnsListContainer">
				<span class="columnsListSpan">Columns:</span>

				<label title="Select All" class="columnsSelectAll"><input type="checkbox" id="selAllCols" style="margin-right: 5px;" checked>Select All</label>
				<hr class="horizRuleColumnsList">
				
				<div class="columnsList"></div>
				<button class="btn-small" type="button" style="margin-left: 5px; margin-bottom: 5px;" data-bind="event: {click : function(){modifyColumnsListBox()}}">Done</button>
				<button class="btn-small" type="button" style="margin-bottom: 5px;" data-bind="event: {click : function(){closeColumnsListBox()}}">Cancel</button>
			</div>

			<!-- <button type="button" class="btn btn-primary" id="btnSave" data-bind="event: {click: updateSatus}, attr: {disabled: !enableDelete()}">Save</button> -->
		</div>
	</div>
	
	<div id="filterBox" class="a1-filter-box">
		<div id="filterOptionsBox">
			<select class="chosen form-control" id="filterCriteria" data-bind="value: selFilterCategory, event: {change: function(){onFilterCatChange()}}, foreach: filterGridHeader" data-placeholder=" " >
				<!-- ko if: $data!=' ' -->
					<option  data-bind="text: $data"></option>
				<!-- /ko -->
			</select>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<input type="text" class="form-control filterFieldWidth" style="display: inline;" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"> 
			<!-- /ko -->

			<!-- ko if: selFilterCategory() == 'Created Time' || selFilterCategory() == 'Modified Time' -->
				<div class='input-group date filterFieldWidth' id='filterCreateModTime' style="display: inline-flex;">
		            <input type='text' class="form-control" placeholder="Pick Date/Hour" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"/>
		            <span id="calendarIcon" class="input-group-addon" title="Pick Date/Hour" style="width: 40px;">
		                <span class="glyphicon glyphicon-calendar"></span>
		            </span>
		        </div>
			<!-- /ko -->

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Type' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="searchType" data-bind="foreach : typeArrFilter, value: filterValueArr()[gridHeader.indexOf('Type')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" required="true">
					<option  data-bind="attr:{'name': masterId},text: $data.name"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Time Zone' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="ftimezoneList" data-bind="foreach : timezoneArr, value: filterValueArr()[gridHeader.indexOf('Time Zone')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="value: '0', text: 'Select'"></option>
					<!-- /ko-->
					<option data-bind="value: timeZoneId, text: timeZoneName"></option>				
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Maintenance' ? 'inline-block' : 'none'}">
			<select id="searchMaintainence" class="chosen form-control" data-bind="foreach : maintenanceArr, value: filterValueArr()[gridHeader.indexOf('Maintenance')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}">
					<option data-bind="value: id, text: name"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Status' ? 'inline-block' : 'none'}">
				<select id="fActiveInactive" class="chosen form-control" data-bind="value: filterValueArr()[gridHeader.indexOf('Status')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}">
					<option value="2" >All</option>
					<option value="1">Active</option>
					<option value="0">Inactive</option>
				</select>
			</div>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<button type="button" class="btn a1-action-btn" data-bind="event: {click: function(){onFilterAdd()}}">ADD</button>
			<!-- /ko-->
		</div>

		<!-- ko if: filtersAdded() -->
			<table style="margin-top: 5px;">
				<tbody>
					<tr>
						<td class="filter-table-td" style="width: 100%;">
							<input type="text" class="form-control tokenfield" id="filters-tokenfield-typeahead" style="margin-top: 5px;" data-bind="value: filtersAdded">
						</td>

						<td class="filter-table-td">
							<button type="button" class="btn a1-action-link" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL <span class="fa fa-times-circle"></span></button>
						</td>

						<td>
							<button type="button" style="width: 70px; margin-left: 6px;" class="btn btn-primary" id="btnApplyFilter" data-bind="event: {click: function(){getFilterListData(true)}}">APPLY</button>

							<span id="filterOptionsDispBtn" class="fa fa-angle-double-down a1-options-display" data-bind="event: {click: function(){onFilterOptionsDispClick()}}" style="display: none;" title="Show Filter Options"></span>
						</td>
					</tr>
				</tbody>
			</table>
			<!-- <div style="width: 80%; display: inline-block;">
				
			</div>

			<div style="width: 15%; display: inline-block;">
				<button type="button" class="btn listViewBtn" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL</button>

				


			</div> -->
		<!-- /ko -->

	</div>

	<div>
		<div class="wrapper"><!-- style="width:100%;float:left; padding-top: 5px" -->
			<table id="listgrid" class="table table-sm table-hover a1-list-grid" style="width:100%">
				<thead class="a1-list-grid-header">
                    <tr data-bind="foreach: gridHeader">
						<!-- ko if: $index() == 0 -->
						<th class="actionControl"><input type="checkbox" id ="chkboxHeader" title="Select All"/></th>
						<!-- /ko -->

						<!-- ko if: $index() < ($parent.gridHeader().length-1) || window.wizardEditAvailable() -->
							<!-- <th data-toggle="tooltip" data-placement="bottom" data-bind="text: $data,attr:{'class' : $index() < ($parent.gridHeader().length-1) ? 'textOverflowOmmiter' : 'actionControl textOverflowOmmiter', 'title': $data.applicationType}, event: {click: function(){$parent.onHeaderClick($index()+1, $parent.configTableHeaderObjKeys()[$index()])}}" ></th> -->

							<th class="listViewCol textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="css: $parent.noSortColKeys.indexOf($parent.configTableHeaderObjKeys()[$index()]) == -1 ? 'header' : 'noSort', text: $data, attr:{'width': $data=='Status'?'100px':'auto'}, event: {click: function(){$parent.onHeaderClick($index(), $parent.configTableHeaderObjKeys()[$index()])}}" ></th>
						<!-- /ko -->
					</tr>
				</thead>

				<tbody id="gridDataBody" data-bind="foreach: gridData" class="main" style="background-color: white;">
					<tr>
						<td style="text-align:center"><input type="checkbox" class="chkboxCol" data-bind="checked: $data.isSelected, attr:{rowData: $data}" title="Select"/></td>
						<!-- <td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.applicationName, attr:{'title': $data.applicationName}"></td> -->
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<a data-bind="text: $data.applicationName, attr:{'title': $data.applicationName}, click: $parent.onNameClick"></a>
						</td>
						<td class="textOverflowOmmiter"  data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.applicationType, attr:{'title': $data.applicationType}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.timezone != undefined ? $data.timezone.timeZoneName : '',attr:{'title': $data.timezone != undefined ? $data.timezone.timeZoneName : ''}"></td>
						<td class="textOverflowOmmiter"  data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.maintenanceWindowProfile != undefined ? $data.maintenanceWindowProfile.currentProfileStatus : '',attr:{'title': $data.maintenanceWindowProfile != undefined ? $data.maintenanceWindowProfile.currentProfileStatus : ''}"></td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.createdTime), attr:{'title': $parent.convertToLocalTime($data.createdTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.updatedTime), attr:{'title': $parent.convertToLocalTime($data.updatedTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.updatedBy, attr:{'title': $data.updatedBy}"></td>

						<td class="col-xs-2">
							<div class="text-two-line-ellipsis" data-bind="text: $parent.getTagNames($data.tags), attr:{title: $parent.getTagNames($data.tags)}" data-toggle="tooltip"
							>
							</div>
						</td>

						<!-- <td style="text-align:center"><input type="checkbox" class="chkboxStatus" data-bind="checked: $data.status, attr:{appId: $data.applicationId, title: $data.status?'Active':'Inactive'}"/></td> -->

					 	<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<!-- ko if: $data.status == 1 -->
								<span>
									<img src="/images/green-circle-12.png">
									Active
								</span>
							<!-- /ko -->

							<!-- ko if: $data.status == 0 -->
								<span>
									<img src="/images/red-circle-12.png">
									Inactive
								</span>
							<!-- /ko -->
						</td>

						<!-- ko if: window.wizardEditAvailable() -->
						 	<td style="text-align: center;">
						 		<span type="button" class="glyphicon glyphicon-edit buttonedit glyphicon-style" id="btnWizardEdit" data-bind="attr: {title: 'Edit Using Wizard'}"></span>

						 		<!-- data-bind="event: {click: $parent.openWizard}" -->
						 	</td>
						<!-- /ko-->
					</tr>
				</tbody>
			</table>

			<!-- ko if: gridData().length == 0 -->
				<div colspan="11" style="text-align: center;"><h4><span data-bind="text: showListAvailable" ></span></h4></div>
			<!-- /ko -->
		</div>
	</div>

	<!-- ko if : currentViewIndex() == 0 && gridData().length>0 -->
		<div class="config-pagination-footer">
			<label class="a1LabelFooter"></label>

			<div style="float:right;">
				<label class="a1LabelFooter" data-bind="text:uiConstants.common.PER_PAGE"></label>
				<select id="pageNum" class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}" style="display: inline; width: auto;"></select>

				<label class="a1LabelFooter" data-bind="text: recordsCountLabel()"></label>

				<span id="prevButton" class="fa fa-angle-left paginationPrev" data-bind="attr: {title: 'Previous Page', disabled: currentPage()<=1}, click: currentPage()<=1 ? '' : prevPage, css: {paginationArrowDisabled: currentPage()<=1}"></span>

				<!-- <span class="paginationLabel">Page <input type="number" min="1" class="pageNumber" data-bind="value: currentPage, event: {change: getListOrFilterData}"> of&nbsp; </span><strong class="paginationLabel" data-bind="text: totalPages()"></strong> -->
				<span class="fa fa-angle-right paginationNext" data-bind="attr: {title: 'Next Page', disabled: currentPage()==totalPages()}, click: currentPage()==totalPages() ? '' : nextPage, css: {paginationArrowDisabled: currentPage()==totalPages()}"></span>
			</div>
		</div>
	<!-- /ko -->
</div>

<div style="width:100%;float:left;" data-bind="if : currentViewIndex() == 1 || currentViewIndex() == 3 || currentViewIndex() == 4 || currentViewIndex() == 5">
	<application-add-edit params="{'typeArr':typeArr, 'timezoneArr': timezoneArr,currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, curPage: curPage, pageSelected: pageSelected}"></application-add-edit>
</div>

<div style="width:100%;float:left;" data-bind="if : currentViewIndex() == uiConstants.common.START_WIZARD ">
	<config-wizard params="{'panelTitle':'Add','mode':'wizard','typeArr':typeArr, 'timezoneArr': timezoneArr,currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, pageSelected: pageSelected, applicationsGridData: gridData}"></config-wizard>
</div> 

<div style="width:100%;float:left;" data-bind="if : currentViewIndex() == uiConstants.common.EDIT_WIZARD_WITH_APPLICATION ">
	<config-wizard params="{'panelTitle':'Edit','mode':'wizard','typeArr':typeArr, 'timezoneArr': timezoneArr,currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, pageSelected: pageSelected, applicationsGridData: gridData}"></config-wizard>
</div> 
