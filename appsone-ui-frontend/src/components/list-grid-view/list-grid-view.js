define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./list-grid-view.html','hasher','validator','ui-constants','ui-common','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {
	this.wizardEditAvailable = ko.observable(false);
	function ListGridView(params) {
		var self = this;
		var appTableHeaders = ["Name","Type","Time Zone","Maintenance","Created Time","Modified Time","Modified By","Tags","Status"," "];
		var listData = {};
		//const SERVER_IP = "http://**********:4567/v1.0";//**********

		var pageSelectedId = "applicationIds";
		var statusChangeList = {};
		var filterForFirstPage = false;
		var fAppName=null;
		var fAppType="All";
		var fAppTimezone="All";
		var fAppTags=null;
		var fAppMaintenance="0";
		var fAppActiveInactive="1";
		var fCreatedTime="";
		var fUpdatedTime="";
		var fUpdatedBy="";
		var colSortOrder = 0;
		var colToSort = "applicationName";
		var filterTxtAfterDelete;
		var c = 0;
		
		//this.gridHeader = ko.observableArray();
		this.gridData = ko.observableArray();
		this.filterGridHeader = ko.observableArray(["Select","Name","Type","Time Zone","Maintenance","Created Time","Modified Time","Modified By","Tags","Status"," "]);
		this.configTableHeaderObjKeys = ko.observableArray(["applicationName","applicationType","timezone","maintenanceWindowProfile","createdTime","updatedTime","updatedBy","tags","status"]);
		this.noSortColKeys = ko.observableArray(["tags"]);
		this.currentPage = ko.observable(0);
		this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
		this.totalRecordsPerPage = ko.observable(this.numOfPagesOption()[0]);
		this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
		this.enableAdd = ko.observable(true);
		this.enableEdit = ko.observable(false);
		this.enableClone = ko.observable(false);
		this.enableDelete = ko.observable(false);
		this.currentViewIndex = ko.observable(0);
		this.selectedConfigRows = ko.observableArray();
		this.gridHeader = ko.observableArray(appTableHeaders);
		this.typeArr = ko.observableArray();
		this.typeArrFilter = ko.observableArray();
		this.timezoneArr = ko.observableArray();
		this.errorMsg = ko.observable("");
		this.listViewErrorMsg = ko.observable("");//this will be used later to show msg when applictaions not found
		this.isFilterOrList = ko.observable("list");
		//this.recordStartCount = ko.observable(0);
		//this.recordEndCount = ko.observable(0);
		this.recordsCountLabel = ko.observable("");
		this.pageSelected = ko.observable("Application Configuration");
		this.enableFilter = ko.observable(true);
		this.showListAvailable = ko.observable("");
		this.wizardAddAvailable = ko.observable(false);
		this.modifiedCols = ko.observableArray([true,true,true]);
		this.selFilterCategory = ko.observable();
		this.filtersAdded = ko.observable("");
		this.filterValueArr = ko.observableArray([]);
		this.maintenanceArr = ko.observableArray([
			{"id": 0, "name": "All"},
			{"id": 1, "name": "Immediate Maintenance"},
			{"id": 2, "name": "Under Maintenance"},
			{"id": 3, "name": "Not Under Maintenance"}
		]);
		//this.wizardEditAvailable = ko.observable(false);
		//this.panelTitle

		this.renderHandler=function(){
			/*$(".wrapper").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
				this.querySelector("#filterRow").style.transform = translate;
           	});*/

			$(window).resize(function(){
			    self.refreshPageLayout();
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$(".addOptionsContainer").offset({ top: $("#divAddOptionsList").position().top + $("#divAddOptionsList").outerHeight() });

			$('.columnsList').checklistbox({
			    data: [
			    	{"name": "Created Time", "id": 1},
			    	{"name": "Modified Time", "id": 2},
			    	{"name": "Modified By", "id": 3}
			    ]
			});
			$('.columnsList .checkList').prop("checked", true);

			$("div").on("click", "#selAllCols", function(e){
				$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
			});

			$("div").on("change", ".columnsList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
				}
	        });

			$("#searchType_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchType_chosen").trigger('chosen:updated');

			$("#searchMaintainence_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchMaintainence_chosen").trigger('chosen:updated');

			$("#searchActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchActiveInactive_chosen").trigger('chosen:updated');

			/*$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/


	 		requestCall(uiConstants.common.SERVER_IP + "/masterTypes/application?status=2&markInactive=1", "GET", "", "getApplicationType", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/timeZones", "GET", "", "getTimezone", successCallback, errorCallback);
			$('#searchAppName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});

			$("#listgrid tbody").on('click', '#btnWizardEdit', function(e){
			 	var nameStr = $(this).closest("tr").find("td:eq(1)").text().trim();
			 	getSelectedConfigRows($.grep(self.gridData(), function(evt){ return evt.applicationName == nameStr; })[0]);
				self.switchView(8);
			});

			/*var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_APPLICATION_LINK || evt.accessLink == uiConstants.common.CONST_COMPINST_LINK || evt.accessLink == uiConstants.common.CONST_TRANSACTION_LINK || evt.accessLink == uiConstants.common.CONST_AGENT_LINK; });
      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled || !optionPermissionsObj[0].editEnabled || !!optionPermissionsObj[0].deleteEnabled){
				$("#modalAppType").css("visibility","hidden");
      		}*/

      		var pagePermission = {};
      		var applicationAddEnabled = false;
      		var applicationEditEnabled = false;
      		var compInstanceAddEnabled = false;
      		var compInstanceEditEnabled = false;
      		var transactionAddEnabled = false;
      		var transactionEditEnabled = false;
      		var agentAddEnabled = false;
      		var agentEditEnabled = false;
      		for(var permission in window.koMenuPermissions()){
      			pagePermission = window.koMenuPermissions()[permission];
      			if(pagePermission.accessLink == uiConstants.common.CONST_APPLICATION_LINK){
      				if(pagePermission.createEnabled){
      					applicationAddEnabled = true;
      				}
      				if(pagePermission.updateEnabled){
      					applicationEditEnabled = true;
      				}
      			}
      			else if(pagePermission.accessLink == uiConstants.common.CONST_COMPINST_LINK){
      				if(pagePermission.createEnabled){
	      				compInstanceAddEnabled = true;
	      			}
	      			if(pagePermission.updateEnabled){
	      				compInstanceEditEnabled = true;
	      			}
      			}
      			else if(pagePermission.accessLink == uiConstants.common.CONST_TRANSACTION_LINK){
      				if(pagePermission.createEnabled){
      					transactionAddEnabled = true;
      				}
      				if(pagePermission.updateEnabled){
      					transactionEditEnabled = true;
      				}
      			}
      			else if(pagePermission.accessLink == uiConstants.common.CONST_AGENT_LINK){
      				if(pagePermission.createEnabled){
      					agentAddEnabled = true;
      				}
      				if(pagePermission.updateEnabled){
      					agentEditEnabled = true;
      				}
      			}
      		}

      		if(applicationAddEnabled && compInstanceAddEnabled && transactionAddEnabled && agentAddEnabled){
  				self.wizardAddAvailable(true);
  			}
  			else{
  				self.wizardAddAvailable(false);
  			}

  			if(applicationEditEnabled && compInstanceEditEnabled && transactionEditEnabled && agentEditEnabled){
  				window.wizardEditAvailable(true);
  			}
  			else{
  				window.wizardEditAvailable(false);
  			}

			$("#searchMaintainence").val("0").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');

			self.curPage(1);
		}

		self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

		this.onHeaderClick = function(columnNum, columnName){
			if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
				$(".listViewCol").not(".noSort").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

				colSortOrder = colSortOrder ? 0 : 1;
				colToSort = columnName;
				//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
				$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

				self.getListData();
			}
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += "," + tagsData[tag].tagName;
			}

			return tagNames.substring(1);
		}

		this.msgShowListData=function(){
			if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
				self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
			}
			else{
				self.showListAvailable(uiConstants.applicationConfig.APPLICATION_LISTS_NOT_CONFIGURED);
			}
		}

		this.toggleAddOptions = function(){
			$(".addOptionsContainer").toggle();
			document.addEventListener('click', window.outsideClickListener);
		}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.convertToLocalTime = function(getDateTime){
			return window.gmtToLocalDateTime(getDateTime);
		}

		this.totalPages = ko.computed(function() {
			return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
		}, this);

		this.recordsCountLabel = ko.computed(function() {
			var recordsStartCount = 0;
			var recordsEndCount = 0;

			if(self.currentPage() != 0){
				recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
				recordsEndCount = recordsStartCount + (self.gridData().length - 1);
			}
			return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
		}, this);

		this.enableDisableAdd = function(length){
			self.enableAdd(length>0?false:true)
		}

		/*this.disableFilter = ko.computed(function(){
			if(self.gridData().length > 0)
				return false;
			else
				return true;
		});*/

		this.enableDisableUpdate = function(length){
			if(length>0){
				self.enableEdit(true);
			}
			else{
				self.enableEdit(false);
			}
		};

		this.enableDisableDelete = function(length){
			if(length>0)
				self.enableDelete(true);
			else
				self.enableDelete(false);
		};

		this.enableDisableClone = function(length){
			if(length === 1)
				self.enableClone(true);
			else
				self.enableClone(false);
		};

		this.getListOrFilterData = function(){
			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.prevPage = function(){
			if(self.currentPage()>1)
				self.currentPage(self.currentPage()-1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
	
		this.nextPage = function(){
			if(self.currentPage()<self.totalPages())
				self.currentPage(self.currentPage()+1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.curPage = function(curPage){
			resetButtonStates();
			self.currentPage(curPage);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.onAddMultipleClick = function(){
			  hasher.setHash('#addMultiple');    
		}

		this.openWizard = function(){
			hasher.setHash('#configWizard');    
		}

		this.showFilterBox = function(){
			if(!self.filterValueArr().length){
	            for(var headerTxt in self.gridHeader()){
	            	self.filterValueArr.splice(headerTxt, 0, "");
	            }
			}
			$("#filterCriteria").trigger("chosen:updated");
			$("#filterCriteria_chosen").addClass("filterFieldWidth");
			$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
			$("#filterBox").css("display", "block");

			self.onFilterCatChange();

			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.onFilterCatChange = function(){
			if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
				$('#filterCreateModTime').datetimepicker({
					format: "YYYY-MM-DD HH:00",          
					stepping: 1,
					useCurrent: true, 
					//defaultDate: null,
					showTodayButton: false,          
					collapse: true,
					sideBySide: false
				})
				.on('dp.show', function(e){
					if($('#filterCreateModTime input').val() == ""){
						$(this).data("DateTimePicker").date(moment());
					}
				})
				.on('dp.change', function(e){
					if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
						$(this).data("DateTimePicker").maxDate(moment());
						var maxHour = parseInt(moment().format("HH"));
						//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
						//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
					}
					else{
						$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
						//$(this).data("DateTimePicker").disabledHours([]);
					}

					self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();

				})
				.on('dp.hide', function(e){
					self.onFilterAdd();
				});
				$('#filterCreateModTime input').val("");
			}
		}

		this.onFilterAdd = function(){
			if(self.filtersAdded() == ""){
				self.filtersAdded($("#filterCriteria option:selected").text() + "::" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
				$('#filters-tokenfield-typeahead').tokenfield({
					delimiter: ['|']
				});
			}


			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];
			var filterCategoryFound = 0;
			
			self.filtersAdded("");
			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split("::");
				
				if(filterCategoryArr[0].trim() == self.selFilterCategory()){
					filterCategoryArr[1] = self.getFiltersToAdd(self.selFilterCategory());
					filterCategoryFound = 1;
				}

				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCategoryArr[0].trim()+"::"+filterCategoryArr[1]);

				if(filters == "0"){
					$('#filters-tokenfield-typeahead').tokenfield({
						delimiter: ['|']
					});
				}
			}

			if(filterCategoryFound == 0){
				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + $("#filterCriteria option:selected").text() + "::" + self.getFiltersToAdd(self.selFilterCategory()));
			}
			filterCategoryFound = 0;

			var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fAppName=null;
						fAppType="All";
						fAppTimezone="All";
						fAppTags=null;
						fAppMaintenance="0";
						fAppActiveInactive="1";
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

		this.getFiltersToAdd = function(category){
			if(category == "Type"){
				return $("#searchType option:selected").text();
			}
			else if(category == "Time Zone"){
				return $("#ftimezoneList option:selected").text();
			}
			else if(category == "Maintenance"){
				return $("#searchMaintainence option:selected").text();
			}
			else if(category == "Status"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
				return $("#fActiveInactive option:selected").text();
			}
			else{
				return self.filterValueArr()[self.gridHeader.indexOf(category)];
			}
		}

		this.resetFilter = function(){
			showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
				if(confirm){
					self.resetFilterConfirmed();
				}
			});
		}

		this.resetFilterConfirmed = function(){
			self.filtersAdded("");
			for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }

			$('#searchType').val("").trigger("chosen:updated");
			$("#ftimezoneList").val("0").trigger('chosen:updated');
			$("#searchMaintainence").val("0").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');
			$('#filterCreateModTime').val("");
			self.errorMsg("");
			self.currentPage(1);
			self.isFilterOrList("list");

			fAppName=null;
			fAppType="All";
			fAppTimezone="All";
			fAppTags=null;
			fAppMaintenance="0";
			fAppActiveInactive="1";
			fCreatedTime="";
			fUpdatedTime="";
			fUpdatedBy="";

			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
			self.selFilterCategory("Select");
	
			requestCall(uiConstants.common.SERVER_IP + "/applications?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&applicationName=" +null+"&type="+fAppType+"&timeZone="+fAppTimezone+"&maintenenaceWindow="+fAppMaintenance+"&tag="+fAppTags+"&status="+fAppActiveInactive+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);
		}

		this.getFilterListData = function(filterApplied){
			self.isFilterOrList("filter");
			filterForFirstPage = filterApplied;
			setFiltersToVariables();
			var resValue = true;

			if(uiConstants.common.DEBUG_MODE)console.log("****************** Filter Params *********************");
			if(uiConstants.common.DEBUG_MODE)console.log("Appname : "+fAppName);
			if(uiConstants.common.DEBUG_MODE)console.log("Type : "+fAppType);
			if(uiConstants.common.DEBUG_MODE)console.log("Timezone : "+fAppTimezone);
			if(uiConstants.common.DEBUG_MODE)console.log("Tags : "+fAppTags);
			if(uiConstants.common.DEBUG_MODE)console.log("Maintenance : "+fAppMaintenance);
			if(uiConstants.common.DEBUG_MODE)console.log("Active : "+fAppActiveInactive);

			this.errorMsg("");

			/*if($("#searchAppName").val() != "" && $("#searchAppName").val().length < 2){
				self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MIN_LENGTH_ERROR);
				resValue = false;
			}
			else if($("#searchAppName").val() != "" && $("#searchAppName").val().length > 45){
				self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MAX_LENGTH_ERROR);
				resValue = false;
			}*/
			/*else if(fAppName != "" && fAppName != null){
				resValue=nameValidation(fAppName);
				if(resValue == 0){
					self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_INVALID_ERROR);
					
					resValue = false;
				}
			}*/
			/*else if($('#searchTimezone').val() != "" && fAppTimezone == 0){
				self.errorMsg("Please select a valid Time Zone from the list");
				resValue = false;
			}*/	
			/*else if(fAppTags != "" && fAppTags != null){
				resValue=tagValidation(fAppTags);
				if(resValue == 0){
					self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
					resValue = false;
				}
			}*/

			resetPagination();
	
			if(resValue){
				if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterApplied);
				if(filterApplied){
					$("#btnApplyFilter").css("display", "none");
					$("#filterOptionsBox").css("display", "none");
					$("#filterOptionsDispBtn").css("display", "");

					requestCall(uiConstants.common.SERVER_IP + "/applications?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&applicationName=" +fAppName+"&type="+fAppType+"&timeZone="+fAppTimezone+"&maintenenaceWindow="+fAppMaintenance+"&tag="+fAppTags+"&status="+fAppActiveInactive+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);
				}
				else
					requestCall(uiConstants.common.SERVER_IP + "/applications?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&applicationName=" +fAppName+"&type="+fAppType+"&timeZone="+fAppTimezone+"&maintenenaceWindow="+fAppMaintenance+"&tag="+fAppTags+"&status="+fAppActiveInactive+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);			
			}
		}

		this.onFilterOptionsDispClick = function(){
			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			
			self.refreshPageLayout();
		}

		this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

		this.getListData = function(){
			self.isFilterOrList("list");
			resetPagination();
			if(window.globalSearchTxt){
				fAppName = window.globalSearchTxt;
			}
			$('#searchAppName').val(fAppName);
			//window.globalSearchTxt = "";
			requestCall(uiConstants.common.SERVER_IP + "/applications?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&applicationName=" +fAppName+"&type="+fAppType+"&timeZone="+fAppTimezone+"&maintenenaceWindow="+fAppMaintenance+"&tag="+fAppTags+"&status="+fAppActiveInactive+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);			
			//requestCall("http://www.mocky.io/v2/5888787a260000dd1a9663d3?callback=?", "GET", "", "getListData", successCallback, errorCallback);
		}

		this.updateSatus = function(){
			if(statusChangeList[pageSelectedId].length>0)
				requestCall(uiConstants.common.SERVER_IP + "/applications", "DELETE", JSON.stringify(statusChangeList), "changeStatus", successCallback, errorCallback);
		}

		this.switchView = function (viewIndex){
			self.currentViewIndex(viewIndex);

			if(self.currentViewIndex() == 0){
				self.pageSelected("Application Configuration");
			}

			else if(self.currentViewIndex() == 1){
				self.pageSelected("Add Application");
				uicommon.postbox.publish("Add Application","ViewIsChangedToAddEdit");
			}

			else if(self.currentViewIndex() == 3){
				if(self.selectedConfigRows().length>1){
					self.pageSelected("Edit Applications");
				}
				else{
					self.pageSelected("Edit Application");
					uicommon.postbox.publish('displaySkip',"ViewIsChangedToAddEdit");
				}
			}

			else if(self.currentViewIndex() == 4){
				self.pageSelected("Clone Application");
			}

			else if(self.currentViewIndex() == 5){
				self.pageSelected("Application");
			}

			else if(self.currentViewIndex() == 7){
				self.pageSelected("Add Application");
				uicommon.postbox.publish("Add Application","ViewIsChangedToAddEdit");
			}

			else if(self.currentViewIndex() == 8){
				self.pageSelected("Edit Application");
				uicommon.postbox.publish('displaySkip',"ViewIsChangedToAddEdit");
			}
		}

		this.editConfig = function(){
			getSelectedConfigRows(null);
			self.switchView(3);
		}

		this.cloneConfig = function(){
			getSelectedConfigRows(null);
			if(self.selectedConfigRows()[0].status == 0){
				showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "Application"), "error");
			}
			else{
				self.switchView(4);
			}
		}

		this.viewConfig = function(viewObj){
			getSelectedConfigRows(viewObj);
			self.switchView(5);
		}

		this.addWizardConfig= function(){
			getSelectedConfigRows(null);
			self.switchView(7);
		}

		/*this.editWizardConfig= function(rowIndex){
			getSelectedConfigRows(self.gridData()[rowIndex]);
			self.switchView(8);
		}*/

		function getSelectedConfigRows(viewObj){
			self.selectedConfigRows([]);

			if(viewObj != null){
				if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
				self.selectedConfigRows.push(viewObj);
			}
			else{
				for(objData in self.gridData()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.gridData()[objData]);

					if(self.gridData()[objData].isSelected){
						self.selectedConfigRows.push(self.gridData()[objData]);
					}
				}			
			}
		}

		$('#listAppDetailsPage table').on('click', '.chkboxCol', function(e){
			self.handleChkClick();
		});

		$('#listAppDetailsPage table').on('click', '.chkboxStatus', function(e){
	    	var appId = e.target.getAttribute('appId');

	    	if(statusChangeList[pageSelectedId].indexOf(appId) == -1)
	    		statusChangeList[pageSelectedId].push(appId);

	    	self.enableDisableDelete(statusChangeList[pageSelectedId].length);
	    	if(uiConstants.common.DEBUG_MODE)console.log("statusChangeList[pageSelectedId].length" + statusChangeList[pageSelectedId].length);
	    	if(uiConstants.common.DEBUG_MODE)console.log("statusChangeList in JSON format: " + JSON.stringify(statusChangeList));
	    });

	    $('#listgrid tbody').on('dblclick', 'tr', function(e){
	    	if(e.target.parentNode.rowIndex != undefined)
	    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
		});

		self.onNameClick = function(){
			self.viewConfig($(this)[0]);
		}

		self.handleChkClick = function(){
			var length = $('.chkboxCol:checked').length;

			if(length == 0){
				for(objData in self.gridData()){
					self.gridData()[objData].isSelected=false;
				}
			}
			self.enableDisableAdd(length);
			self.enableDisableUpdate(length);
			self.enableDisableClone(length);
			if (length == self.gridData().length) {
				$("#chkboxHeader").prop("checked",true);
			}
			else {
				$("#chkboxHeader").prop("checked",false);
			}
		}

	    /*function getTimezoneIdByName(timezone){
			if(timezone == "" || timezone == null)
				return 0;
			for(tzone in self.timezoneArr()){
				if((self.timezoneArr()[tzone].timeZoneName.trim()) == timezone.trim()){
					return self.timezoneArr()[tzone].timeZoneId;
				}
			}
			return 0;
		}*/

		function getTypeIdByName(type){
			if(type == "All" || type == "Select" )
				return 0;
			for(appType in self.typeArr()){
				if( self.typeArr()[appType].name == type.trim()){
					return self.typeArr()[appType].masterId;
				}
			}
			return 0;
		}

		function resetButtonStates(){
			self.enableDisableAdd(0);
			self.enableDisableUpdate(0);
			self.enableDisableDelete(0);
			self.enableDisableClone(0);
		}

		function setFiltersToVariables(){
			/*fAppName=($('#searchAppName').val() == "")?null:$('#searchAppName').val();
			fAppType=getTypeIdByName($("#searchType option:selected").val());
			//fAppTimezone=getTimezoneIdByName($('#searchTimezone').val());
			fAppTimezone = $("#ftimezoneList").val();
			fAppTags=($('#searchTag').val() == "")?null:$('#searchTag').val();
			fAppMaintenance=$('#searchMaintainence option:selected').val();
			fAppActiveInactive=$('#searchActiveInactive').val();

			//debugger;
			fCreatedTime=localToGmtDateTime($('#createdTime input').val().trim());
			fUpdatedTime=localToGmtDateTime($('#updatedTime input').val().trim()); 
			fUpdatedBy=($('#searchModifiedBy').val() == "")?null:$('#searchModifiedBy').val();*/

			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];

			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split("::");

				if(filterCategoryArr[0] == "Name"){
					fAppName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Type"){
					fAppType = $.grep(self.typeArrFilter(), function(e){
						return e.name == filterCategoryArr[1];
					})[0].masterId;
				}
				else if(filterCategoryArr[0] == "Time Zone"){
					fAppTimezone = $.grep(self.timezoneArr(), function(e){
						return e.timeZoneName == filterCategoryArr[1];
					})[0].timeZoneId;
				}
				else if(filterCategoryArr[0] == "Maintenance"){
					fAppMaintenance = $.grep(self.maintenanceArr(), function(e){
						return e.name == filterCategoryArr[1];
					})[0].id;
				}
				else if(filterCategoryArr[0] == "Created Time"){
					fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
				}
				else if(filterCategoryArr[0] == "Modified Time"){
					fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
				}
				else if(filterCategoryArr[0] == "Modified By"){
					fUpdatedBy = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Tags"){
					fAppTags = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Status"){
					fAppActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
				}
			}
		}

		function resetPagination(){
			if(self.totalPages() != 0){
				if(typeof self.currentPage() == "string"){
					self.currentPage(parseInt(self.currentPage()));
				}

				if(self.currentPage() == "" || isNaN(self.currentPage()))
					self.currentPage(1);
				else if(self.currentPage()>self.totalPages())
					self.currentPage(self.totalPages());
				else if(self.currentPage()<1)
					self.currentPage(1);
			}
		}

		this.showFilter = function(){
			if($('#searchAppName').val() == "" && 
				$("#searchType").val() == "" && 
				$("#ftimezoneList").val() == "" && 
				$("#searchMaintainence").val() == "" && 
				$('#searchTag').val() == null && 
				$("#searchActiveInactive").val() == "" && 
				gridData().length == 0)
				self.enableFilter(false);
			else
				self.enableFilter(true);
		}

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			statusChangeList[pageSelectedId]=[];

			if(reqType === "getListData"){
				$("#chkboxHeader").prop("checked",false);
				resetButtonStates();

				if(data.responseStatus == "success"){
					listData = data;
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					self.totalRecords(listData.totalRecords);

					//self.gridData(listData.result);

					$("#listgrid #gridDataBody").empty();
			 		$("#listgrid").trigger("update");
					self.gridData(listData.result);

					var initialSortColumn = 1;
					var sortOrder = 0; //0=asc; 1=desc
					if(!$("#listgrid").hasClass("tablesorter")){
						if(!self.gridData().length){
							self.enableFilter(false);
						}
						else{

							if (!$("#pageNum").hasClass("select2-hidden-accessible")){
								debugger;
								$("#pageNum").select2();

								$("#pageNum").select2("open");
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
								$("#select2-pageNum-container").parent().css({
									"border": "none",
									"outline": "none"
								});

								$("#pageNum").parent().children("span").css("width", "36px");
								//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
								$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
								$("#select2-pageNum-container").css({
										"font-weight": "bold",
										"color": "#218DC0",
										"padding-left": "4px"
									});

								$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
								$(".pageNumDropDown .select2-search").css("display", "none");
								$("#select2-pageNum-results").css("overflow-x", "hidden");
								$("#pageNum").select2("close");
							}
						}

						$("#listgrid").addClass("tablesorter")
						/*$("#listgrid").tablesorter({
							//ignoreCase : false,
							cancelSelection: false,
							headers: { 0: { sorter: false}, 8: { sorter: false}, 10: { sorter: false} },

							widgetOptions: {
						      sortTbody_primaryRow : '.main',
						      sortTbody_sortRows   : false,
						      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
						    }
							//sortList: [[initialSortColumn, sortOrder]]
						});

						$("#listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

			            var $tab = $('#listgrid');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper');
							}
						});

						$('#listAppDetailsPage table').on('click', '#chkboxHeader', function(e){
							if (this.checked == false) {
								$(".chkboxCol").prop("checked",false);
								for(objData in self.gridData()){
									self.gridData()[objData].isSelected=false;
								}
							}
							else {
								$(".chkboxCol").prop("checked",true);
								for(objData in self.gridData()){
									self.gridData()[objData].isSelected=false;
								}
								for(objData in self.gridData()){
									self.gridData()[objData].isSelected=true;
								}
							}
							var length = $('.chkboxCol:checked').length;
							self.enableDisableAdd(length);
							self.enableDisableUpdate(length);
							self.enableDisableClone(length);
						});
					}
					/*else{
						$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
						
					}*/

					/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
*/

					$("#listgrid").trigger("update");
					self.refreshPageLayout();

					if((self.currentPage() == 0 || filterForFirstPage) && self.gridData().length>0){
						self.currentPage(1);
						filterForFirstPage = false;
					}
					self.showFilter();
					self.msgShowListData();
				}else{
					showMessageBox(data.message, "error");							
					self.showListAvailable("");	
				}

				var $tab = $('#listgrid');
				$tab.floatThead('reflow');
			}
			else if(reqType === "changeStatus"){
				showMessageBox(uiConstants.applicationConfig.SUCCESS_APPLICATION_STATUS_CHANGE);
				self.curPage(1);
			}
			else if(reqType === "getApplicationType"){
				self.typeArr.removeAll();
				//self.typeArr.push({'masterId': '','name': "Select Type"});						
				self.typeArr.push.apply(self.typeArr,data.result);

				self.typeArrFilter.removeAll();
				self.typeArrFilter.push({'masterId': '','name': "All"});	
				self.typeArrFilter.push.apply(self.typeArrFilter,data.result);

				$("#searchType").trigger('chosen:updated');

				if(uiConstants.common.DEBUG_MODE)console.log(self.typeArr());
			}
			else if(reqType === "getTimezone"){
				self.timezoneArr.removeAll();
				self.timezoneArr.push.apply(self.timezoneArr,data.result);
				$("#ftimezoneList").trigger('chosen:updated');
				$("#ftimezoneList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
				if(uiConstants.common.DEBUG_MODE)console.log(self.timezoneArr());
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getListData"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
  			else if(reqType === "changeStatus"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_APPLICATION_STATUS_CHANGE, "error");
  			}
		}
	}

	ListGridView.prototype.dispose = function() { };
	return { viewModel: ListGridView, template: templateMarkup };
});
