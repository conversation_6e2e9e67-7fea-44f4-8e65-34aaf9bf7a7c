define(['jquery','knockout','fusionCharts','text!./real-time-chart.html','hasher','ui-constants','ui-common'], function($,ko,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function REALTIMECHART(params) {
    this.message = ko.observable('Hello from the real-time-chart component!'); 
    var self = this;
    this.RCPodId = ko.observable(params.podId+'_real-chart-container');
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.isModal = ko.observable(params.isModal);


    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');


        self.initChart();
    }


    this.initChart = function(){

        FusionCharts.ready(function () {
                    var stockPriceChart = new FusionCharts({
                        id: "stackRealTimeChart",
                        type: 'realtimestackedarea',
                        renderAt: self.RCPodId(),
                        width: self.currentPodBodyWidth()-5,
                        height: self.currentPodBodyHeight()-5,
                        dataFormat: 'json',
                        dataSource: {
                            "chart": {
                                "caption": "Live Visitors on Site",
                                "subCaption": "Updated every 5 seconds",
                                "xAxisName": "Time",
                                "yAxisName": "No. of visitors",
                                "numberSuffix":"s",
                                "refreshinterval": "5",
                                "yaxisminvalue": "0",
                                "yaxismaxvalue": "60",
                                "numdisplaysets": "10",
                                "labeldisplay": "rotate",
                                "showValues": "0",
                                "showRealTimeValue": "0",
                                
                                //Cosmetics
                                "paletteColors" : "#0075c2,#1aaf5d",
                                "baseFontColor" : "#333333",
                                "baseFont" : "Helvetica Neue,Arial",
                                "captionFontSize" : "14",
                                "subcaptionFontSize" : "14",
                                "subcaptionFontBold" : "0",
                                "showBorder" : "0",
                                "bgColor" : "#ffffff",
                                "showShadow" : "0",
                                "usePlotGradientColor" :"0",
                                "showPlotBorder": "0",
                                "canvasBgColor" : "#ffffff",
                                "canvasBorderAlpha" : "0",
                                "divlineAlpha" : "100",
                                "divlineColor" : "#999999",
                                "divlineThickness" : "1",
                                "divLineIsDashed" : "1",
                                "divLineDashLen" : "1",
                                "divLineGapLen" : "1",
                                "showXAxisLine" : "1",
                                "xAxisLineThickness" : "1",
                                "xAxisLineColor" : "#999999",
                                "showAlternateHGridColor" : "0",
                                "legendBgAlpha" : "0",
                                "legendBorderAlpha" : "0",
                                "legendShadow" : "0",
                                "legendItemFontSize" : "10",
                                "legendItemFontColor" : "#666666",
                                // Enable export
                                "exportEnabled": "1",
                                "exportAtClientSide":"1",
                                // Hide export menu item
                                "exportShowMenuItem": "1",
                                "showExportDialog":"1",
                                "exportDialogMessage":"Capturing Data : ",
                                "exportDialogColor":"#333",
                                "exportDialogPBColor":"#0372ab",
                                "toolbarButtonColor":"#999999",
                                "exportFileName":self.podTitle(),
                                //Tooltip customization        
                                "toolTipColor": "#ffffff",
                                "toolTipBorderThickness": "0",
                                "toolTipBgColor": "#000000",
                                "toolTipBgAlpha": "80",
                                "toolTipBorderRadius": "2",
                                "toolTipPadding": "5",
                                //External image url path for logo
                                //"logoURL": "/../../images/logo.png",
                                //Changing logo alpha
                                "logoAlpha": "40",
                                //Scaling logo image
                                "logoScale": "40",
                                //Setting logo position
                                "logoPosition": "BR"

                            },
                            "categories": [
                                {
                                    "category": [
                                        { "label": "Day Start" }
                                    ]
                                }
                            ],
                            "dataset": [
                                {
                                    "seriesName" : "clothing.hsm.com",
                                    "data": [
                                        { "value": "12" }
                                    ]
                                },
                                {
                                    "seriesName" : "food.hsm.com",
                                    "data": [
                                        { "value": "20" }
                                    ]
                                }
                            ]
                        },
                        "events": {
                            "initialized": function (e) {
                                function addLeadingZero(num){
                                    return (num <= 9)? ("0"+num) : num;
                                }
                                function updateData() {
                                    // Get reference to the chart using its ID
                                    var chartRef = FusionCharts("stackRealTimeChart"),
                                        // We need to create a querystring format incremental update, containing
                                        // label in hh:mm:ss format
                                        // and a value (random).
                                        currDate = new Date(),
                                        label = addLeadingZero(currDate.getHours()) + ":" +
                                        addLeadingZero(currDate.getMinutes()) + ":" +
                                        addLeadingZero(currDate.getSeconds()),
                                        // Get random number between 20 & 38 - rounded to 2 decimal places
                                        randomValue = parseInt(Math.random()     
                                                                 * 15 )  + 10,
                                        randomValue2 = parseInt(Math.random()     
                                                                 * 20 )  + 15,
                                        // Build Data String in format &label=...&value=...
                                        strData = "&label=" + label 
                                    + "&value=" 
                                    + randomValue+ "|" + randomValue2;
                                    // Feed it to chart.
                                    chartRef.feedData(strData);
                                }
                                
                                var myVar = setInterval(function () {
                                    updateData();
                                }, 2000);
                            }
                        }
                    })
                    .render();
                });
    }
  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  REALTIMECHART.prototype.dispose = function() { };
  
  return { viewModel: REALTIMECHART, template: templateMarkup };

});