<div data-bind="template: {afterRender: renderHandler}">
	<div data-bind="attr: {'id': RCPodId}">FusionCharts will render here</div>
</div>


<!-- Expandable view as Modal -->
<div data-bind="attr :{'id':'podModal_'+podId()}" class="modal fade" role="dialog">
  <div class="modal-dialog">

    <!-- Modal content-->
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal">&times;</button>
        <h4 class="modal-title" data-bind="text:podTitle"></h4>
      </div>
      <div class="modal-body">
        <p>Some text in the modal.</p>
        <!-- <div data-bind="component:{name: currentComponent, attr:{'height':'100%'}, params:{'podId':'id', 'podTitle': 'Title', 'podType': 'type'}}"></div> -->
      </div>
      <!-- <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
      </div> -->
    </div>

  </div>
</div>