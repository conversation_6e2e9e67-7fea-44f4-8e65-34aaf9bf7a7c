define(['jquery','knockout', 'bootstrap','bootstrap-switch', 'd3','c3','fusionCharts','text!./dashboard-pod-expandedview.html', 'knockout-es5', 'hasher', 'jquery-ui','ui-constants','ui-common', 'moment'], function($, ko, bootstrap, bs, d3, c3, fusioncharts, templateMarkup, koES5, hasher,jqui, uiConstants, uiCommon, mmnt){

	var pad_with_zeroes = function(number, length) {

		var my_string = '' + number;
		while (my_string.length < length) {
			my_string = '0' + my_string;
		}

		return my_string;

	}

	var getDateWithUTCOffset = function (inputTzOffset){
    	var now = new Date(); // get the current time
    	var currentTzOffset = -now.getTimezoneOffset() / 60 // in hours, i.e. -4 in NY
    	var deltaTzOffset = inputTzOffset - currentTzOffset; // timezone diff
    	var nowTimestamp = now.getTime(); // get the number of milliseconds since unix epoch 
    	var deltaTzOffsetMilli = deltaTzOffset * 1000 * 60 * 60; // convert hours to milliseconds (tzOffsetMilli*1000*60*60)
    	var outputDate = new Date(nowTimestamp + deltaTzOffsetMilli) // your new Date object with the timezone offset applied.
    	return outputDate;
	}

	function DashboardPODExpandedView(params) {
		var self = this; 
		self.podId = params.podId ;
		self.dualView = params.dualView || false;
		self.podTitle = params.podTitle;
		self.isGraphView = params.isGraphView || false;
		self.ModalLCPodId ="";
		self.slowTxnGridData = [];
		self.currentPodBodyHeight = "";
		self.slowTxnGridHeaders = [];
		self.activatepng = true;
		self.activatecsv = false;
		self.printId = "Print_"+self.podId();

	
		self.chartComponentName = params.chartComponentName;// 'single-axis-line-chart';
		
		/*self.showGraph =  function(data, event){
			console.log("EVENT ACTIVE");
			console.log(event);
		
		}
		*/

		self.chartDataObj = params.chartDataObj;

		
		//Testing with sample data
		self.graphDataSet = [

		{'name':'CPU Util', 'value':'70%'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'},
		{'name':'Mem Util', 'value':'60%'},
		{'name':'Disk IO Read', 'value':'10 r/s'},
		{'name':'Disk IO Write', 'value':'15 w/s'},
		{'name':'Load Avg', 'value':'0.9'}
		];


		koES5.track(this);		
		 /*koES5 track*/
		 
		//Get this from server
		var today = getDateWithUTCOffset(params.offSet()/3600000) || new Date();


		
		self.datevalue = pad_with_zeroes(today.getFullYear(), 4)+"-"+
						 pad_with_zeroes(today.getMonth(), 2)+"-"+
						 pad_with_zeroes(today.getDate(), 2);

		self.rightnowtime = pad_with_zeroes(today.getHours(), 2)+":"+
							pad_with_zeroes(today.getMinutes(), 2);


		var selectedTimeFromMainWindow = params.timeUnit();
		
		if(selectedTimeFromMainWindow.indexOf("hour")>=0){
			self.timeunit = "hours";
		}
		else if(selectedTimeFromMainWindow.indexOf("day")>=0){
			self.timeunit = "days";
		}
		else if(selectedTimeFromMainWindow.indexOf("month")>=0){
			self.timeunit = "months";
		}
		else if(selectedTimeFromMainWindow.indexOf("year")>=0){
			self.timeunit = "years";
		}					
		
	    self.timeunitcount = "1";

	      koES5.track(this);		
		 /*koES5 track*/

		self.changetimelimits = function(data, event){
			var chosenTimeUnit = event.target.value;
		
			console.log(data);
			console.log(event)
		}

		//This is configurable 
		self.maximumacceptablevalue = 100;

		 //this.chartContId = ko.observable("#chartContId_" + self.podId);
		 this.chartContId = ko.observable("chartContId_Modal_" + self.podId);

	    this.modalRenderHandler = function(){
	    
	        self.currentPodBodyHeight =$('body').height()*0.70;
	        self.currentPodBodyWidth =$('body').width()*0.85;




			// if(self.podName === "Slow_Transactions"){
	        //     self.initChart = self.ModalLCPodId,self.currentPodBodyHeight-30,self.currentPodBodyWidth-5,"Time","Response Time(ms)",self.chartPaletteColors,self.timeWindow,self.responseTime;
	        // }
	        // else{
	        //     self.initChart =self.ModalLCPodId,self.currentPodBodyHeight-30,self.currentPodBodyWidth-5,"Time","Volume",self.chartPaletteColors,self.timeWindow,self.transactionVolume;
	        // }

	        self.bindExportCSVListner = "#"+self.podId+"_Modal_exportCSV" ;	  
	        self.bindGridGraphSwitchListener();
	    }	  

	    this.bindGridGraphSwitchListener = function(){    	
	        //intilize switch.
	        $("#expandGridGraphFilter_pod_"+self.podId).bootstrapSwitch('state', true);	        

	        //Switch listner for change the view b/w Graph and Grid
	        $("#expandGridGraphFilter_pod_"+self.podId).on('switchChange.bootstrapSwitch', function (event, state) {
	            if(state){ 
	            	console.log("Graph View");

					self.chartComponentName = params.chartComponentName;//'single-axis-line-chart';
					self.activatecsv = false;
	            	self.activatepng = true;	            
	            }
	            else{ 
	            	console.log("Grid View");
	            	self.activatecsv = true;
	            	self.activatepng = false;
	            	self.chartComponentName = 'dashboard-grid-view';	  
	            }

	            self.isGraphView =state;
	        });	       
	    }

	    self.startExport = function(data, event){
			
			if(event.target.innerHTML === "PDF Document"){
				var prtContent=null, WinPrint=null;
				prtContent = document.getElementById(self.printId);
				WinPrint = window.open('', '', 'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0');
				WinPrint.document.write('<link href="bower_modules/c3/c3.min.css" rel="stylesheet" type="text/css">'+prtContent.innerHTML);
				
				WinPrint.document.close();
				WinPrint.focus();
				WinPrint.print();
				WinPrint.close();
			}
			else if(event.target.innerHTML === "CSV Document"){
				JSONToCSVConvertor(self.graphDataSet,self.podTitle, "Appsone_", true);
			}		
		}
	  
    }
    
    DashboardPODExpandedView.prototype.dispose = function() {}
	
	return { viewModel: DashboardPODExpandedView, template: templateMarkup };
})