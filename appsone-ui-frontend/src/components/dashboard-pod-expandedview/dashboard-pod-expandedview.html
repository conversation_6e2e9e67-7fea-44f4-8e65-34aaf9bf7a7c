

<!-- Expandable view as Modal -->
 <div class="podModalCont" data-bind="attr: {id: 'expandableView_'+podId}, template: {afterRender: modalRenderHandler}">
  <div data-bind="attr :{'id':'podModal_'+podId}" class="modal fade modal-fullscreen force-fullscreen" role="dialog">
    <div class="modal-dialog" data-bind="style : {height : '100%', width: '100%'}">

      <!-- Modal content-->
      <div class="modal-content" data-bind="style : {height : '90%', width: '90%', margin:'0 5%'}">
        <div class="modal-header" >
          <!-- <button type="button" class="close" data-dismiss="modal">&times;</button> -->
          <ul data-bind="style:{'float':'right'}">
            <li class="glyphicon glyphicon-remove" data-dismiss="modal" data-bind="style:{'display':'inline', 'margin': '2px 4px', 'float':'right'}"></li>
            <li class="dropdown" data-bind="style:{'display':'inline', 'margin': '2px 4px'}">
              <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                <i class="glyphicon glyphicon-download-alt"></i>
              </span>
              <ul class="dropdown-menu pull-right" role="menu" data-bind="click: startExport">
              <!--  <li data-bind="attr:{'id': podId+'_Modal_exportPDF'}"><span>PDF Document</span></li> -->
              <!-- <li data-bind="attr:{'id': podId()+'_Modal_exportJPG'}"><span>JPG Image</span></li> -->

                <li data-bind="attr:{'id': podId+'_Modal_exportPNG'}, visible: activatepng" id="pngImage"><span>PDF Document</span></li>
                <li data-bind="attr:{'id': podId+'_Modal_exportCSV'}, visible: activatecsv" id="csvDocument"><span>CSV Document</span></li>
              </ul>
            </li>
          </ul>
          <h4 class="modal-title" data-bind="text:podTitle"></h4>

          <div class="breadcrumb" data-bind="style:{'height':'auto','margin-bottom':'0','padding':'3'}">
            <div class="form-group row" data-bind="style: {'margin-bottom':'0'}">
              <div class="col-md-1" style="padding-top: 2px; margin-right: -10px;">
              <label for="slider"></label>
                <input type="checkbox" data-size="small" data-handle-width="24px" data-handle-height="50px"             data-label-width="22px" data-on-color="info" data-off-color="info" 
                             data-off-text="<span class='glyphicon glyphicon-th'></span>" 
                             data-on-text="<span class='glyphicon glyphicon-picture'></span>" 
                             checked="true" class="BSswitch" 
                             data-bind="attr :{'id' : 'expandGridGraphFilter_pod_'+podId}">


              </div>

              <div class="col-md-2" style="margin-left: 10px; margin-right: -20px;">

               <label for="date">From</label>
               <input type="date" class="form-control" data-bind="value: datevalue" style="width: 170px; border-radius: 3px;">             
             </div>
     
             <div class="col-md-1">   
             <label for="rightnowtime"></label>     
              <input type="time" class="form-control" value="10:00" data-bind="value: rightnowtime" style="width: 100px; border-radius: 3px;">
            </div>
            <div class="col-md-1" style="margin-left: 10px;">
              <label for="timeunitcount">For Last</label>
              <input type="number" min="1" step="1" class="form-control" data-bind="value: timeunitcount, attr:{max: maximumacceptablevalue}" style="width: 50px;">
            </div>
            <div class="col-md-1" style="margin-left: -40px;" >
              <label for="timeunit"></label>
              <select class="form-control" style="width: 100px;" data-bind="value: timeunit, event:{change: changetimelimits}">          
                <option value="hours">hours</option>                     
                <option value="days">days</option>
                <option value="months">months</option>
                <option value="years">years</option>
              </select>
            </div>
        
           <!--  <div class="col-xs-2" style="margin-left: 10px; width: 150px; color: white;" >
              <label for="button"></label>
              <button type="button" class="form-control btn btn-success">Get Info</button>
            </div> -->
          </div>                  
        </div> 
      </div>








      <div class="modal-body podModalBody" data-bind="attr :{'id':'podModalBody_'+podId}">
     
        <div data-bind="attr: {'id': printId}, component:{ name: chartComponentName, 
                                     params: { 'podId': podId, 
                                               'chartDataObj' : chartDataObj,
                                               'chartContId': chartContId
                                             }
                                   }" >
        </div>
      
        <!--  Multi Txn Line chart example -->
       <div data-bind="style : { 'display' : isGraphView == true && slowTxnGridData.length ? 'block' : 'none'},attr: {'id': ModalLCPodId}">
       </div>
       <!-- Line chart end-->


       <!-- Multi Txn Grid View start -->
       
       <div data-bind="style : { 'display' : isGraphView == false && slowTxnGridData.length ? 'block' : 'none', 'height' : parseInt(currentPodBodyHeight*0.90)+'px', 'overflow-y':'auto'}" class="scrollTable">                  

        <table id="slowPagesTable" class="table table-fixedheader table-bordered podTable" data-bind="attr: {id: 'pod_'+podTitle}">
          <thead>
            <tr data-bind="foreach: slowTxnGridHeaders">
              <th data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data.displayName}, text: $data.displayName, css: $data.class"></th>
            </tr>
          </thead>
          <tbody data-bind="foreach: slowTxnGridData">
            <tr data-bind="foreach : $parents[0].slowTxnGridHeaders">
             <td data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data['columnTitle'] == 'avgResponseTime' ? parseFloat($parents[0][$data['columnTitle']]) : $parents[0][$data['columnTitle']]},text: $data['columnTitle'] == 'avgResponseTime' ? parseFloat($parents[0][$data['columnTitle']]) : $parents[0][$data['columnTitle']] || 'NA', css: $data.class"></td>
           </tr>
         </tbody>
       </table>
     
     </div>
   </div>      
      </div>

    </div>
  </div>
</div>


<style>
/*@media print {
div#testid {
    background-color: red; 
    -webkit-print-color-adjust: exact; 
}
}*/

</style>