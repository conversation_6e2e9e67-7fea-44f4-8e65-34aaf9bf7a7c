define(['jquery','knockout','text!./custom-dashboard-home.html','hasher','ui-constants','ui-common'], function($,ko,templateMarkup,hasher,uiConstants,uicommon) {

  function CustomDashboardHome(params) {
    var self = this;
    this.message = ko.observable('Hello from the custom-dashboard-home component!'); 
    this.currentDashboard = ko.observable("custom");

   
  this.jsonArray = ko.observableArray([
        /*{podid: '1',podtype: 'topology', podname: 'Topology View', 'displayTitle' :'','height':'4','width':'8', 'xPos':'0', 'yPos':'0', 'classList':''},
        {podid: '2',podtype: 'health', podname: 'Application Health', 'displayTitle' : '','height':'2','width':'2', 'xPos':'8', 'yPos':'0', 'classList':'' },
        {podid: '2',podtype: 'doghunt', podname: 'Transaction Health', 'displayTitle' : '','height':'2','width':'2', 'xPos':'10', 'yPos':'0', 'classList':'' },
        {podid: '3',podtype: 'grid', podname: 'Component Details', 'displayTitle' : '','height':'4','width':'4', 'xPos':'0', 'yPos':'4', 'classList':''},
        {podid: '4',podtype: 'doghunt', podname: 'Alerts & Events', 'displayTitle' : '','height':'4','width':'4', 'xPos':'4', 'yPos':'4', 'classList':''},
        {podid: '5',podtype: 'lines', podname: 'KPI Performance', 'displayTitle' : '','height':'4','width':'4', 'xPos':'8', 'yPos':'4', 'classList':''},
        {podid: '6',podtype: 'multi', podname: 'Transaction Summary', 'displayTitle' : '','height':'4','width':'8', 'xPos':'0', 'yPos':'8', 'classList':''},
        {podid: '7',podtype: 'hlineargauge', podname: 'Transaction Performance', 'displayTitle' : '','height':'4','width':'4', 'xPos':'8', 'yPos':'8', 'classList':''},*/
    ]);

  
    

    this.renderHandler = function(){  
      if(uiConstants.common.DEBUG_MODE) console.log("custom dashboard home page");

      requestCall(uiConstants.common.SERVER_IP + "/dashboard/account/0/podInstances", "GET", "", "PodList", self.successCallback, self.errorCallback);
      
     
    };

    this.successCallback = function(data, reqType) {
        if(reqType === "PodList"){
          if(uiConstants.common.DEBUG_MODE) console.log("Custom Dashboard Response---------->");
          if(uiConstants.common.DEBUG_MODE) console.log(data);
          
            console.log(data.result);
            self.jsonArray.removeAll();
            /*data.result.forEach(function(podInstance){
             self.jsonArray.push(podInstance);
            });*/
            self.jsonArray.push.apply(self.jsonArray, data.result);
            console.log(self.jsonArray());
              
             
        }
        
    }

    this.errorCallback = function(reqType) {
        if(reqType === "PodList"){
            if(uiConstants.common.DEBUG_MODE) console.log("error block");
        }
      
    }

  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  CustomDashboardHome.prototype.dispose = function() { };
  
  return { viewModel: CustomDashboardHome, template: templateMarkup };

});