<!--appsone Loading Animation start -->
<div class="podLoadingCont" style="display:none;">
      <div id = "cupcake" class = "box">  
        <span class = "letter box apps">a</span>
        <span class = "letter box apps">p</span>
        <span class = "letter box apps">p</span>
        <span class = "letter box apps">s</span>
        <div class = "cupcakeCircle box one">
              <div class = "cupcakeInner box">
                  <div class = "cupcakeCore box"></div>          
              </div>
        </div>
        <span class = "letter box one">n</span>
        <span class = "letter box one">e</span>
      </div>
</div>
<!--appsone Loading Animation end -->

<div data-bind="template: {afterRender: renderHandler}" class="topologyParent" style="display:none;" >
	<div class="legendCont">
		<span class="label label-danger">Idle</span>
		<span class="label label-warning">Slow</span>
		<span class="label label-success">Good</span>
	</div>	
	

	<div class="topologyCont col-sm-12"> <!-- style="position:relative; height: 100%;z-index:100;"> -->

		
		<div class="entryCont" style="width:30%;" data-toggle="tooltip" data-placement="bottom" data-bind="attr:{'title': 'Violation Count - '+entryReqSlowCount()}, style :{'visibility' : entryDetails().length == 0 ? 'hidden' : 'visible'}">
			<div class="dataLabel">Request Call p/m <span data-bind="text:entryReqCount"></span></div>
			
			<div class="dataLabel">Avg Response Time <span data-bind="text : entryResTime"></span>(ms)</div>
			<div class="dataLabel" data-bind="css:{'violationColor' : entryReqSlowCount() == '0'}">Violations <span data-bind="text:entryReqSlowCount()"></span></div>
		</div>

		<div id="entryChild1" class="topologyChildEntity" data-bind="style :{'visibility' : entryDetails().length == 0 ? 'hidden' : 'visible'}"><span class="glyphicon glyphicon-arrow-right"></span></span></div>
	

		<div class="donutChartCont" style="width:20%;">
			<div id="donutChart" class="topologyChildEntity"></div>
		</div>

		
		<div class="exitCont" style="width:10%;" data-bind="style : {'visibility' : exitDetails().length == 0 ? 'hidden' : 'visible'}">
			<div><span class="glyphicon glyphicon-arrow-right"></span></span></div>
		</div>

		
		<div class="exitChildCont" style="width:40%;" data-bind="foreach : exitDetails, style : {'visibility' : exitDetails().length == 0 ? 'hidden' : 'visible'}">
			<div class="topologyChildEntity" style="width:100%;" data-bind="attr:{'id':'exitCont'+$index()}">
				<span style="width:100%;"><img src="images/DB.png" style="max-height:100%;max-width:100%";/></span>
				<label data-toggle="tooltip" data-placement="bottom" class="violationColor textoverloading" style="width:30%;vertical-align:middle;" data-bind="text:$data.transformType, attr:{'title':$data.transformType} ,css:{'violationColor' : parseInt($data.responseBadCount) > 0}"></label>
				<div class="topologyChildEntityData" style="width:50%;">
					<div class="dataLabel">Request Call p/m <span data-bind="text:$data.responseCallCount"></span></div>
					<div class="dataLabel">Avg Response Time <span data-bind="text:$data.avgResponseTime"></span>(ms)</div>
					<div class="dataLabel" data-bind="css:{'violationColor' : parseInt($data.responseBadCount) > 0}">Violations <span data-bind="text:$data.responseBadCount"></span></div>
				</div>
			</div>
		</div>

	</div>
</div>

<!-- popover for tooltip -->
<div id="firstPopoverTemplate">	
   
   <table id="" class="table table-fixedheader table-bordered table-striped topologyPopoverTable" data-bind="attr: {id: 'pod_'+podTitle()}">
            <thead>
                <tr data-bind="foreach: topologyPopovHeaders" style="font-size:0.9em;">
                    <th data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $data.displayName}, text: $data.displayName, css: $data.class"></th>
                </tr>
            </thead>
            <tbody data-bind="foreach: topologyPopovData" style="font-size:0.9em;">
                <tr data-bind="foreach : $parents[0].topologyPopovHeaders">
                   <td data-toggle="tooltip" data-placement="bottom" data-bind="attr:{ title : $parents[0][$data['columnTitle']]},text: $parents[0][$data['columnTitle']], css: $data.class"></td>
                </tr>
            </tbody>
    </table>
</div>


<!-- Expandable view as Modal -->
<div class="podModalCont" data-bind="template: {afterRender: modalRenderHandler}">
  <div data-bind="attr :{'id':'podModal_'+podId()}" class="modal fade modal-fullscreen force-fullscreen" role="dialog">
    <div class="modal-dialog" data-bind="style : {height : '100%', width: '100%'}">

      <!-- Modal content-->
      <div class="modal-content" data-bind="style : {height : '90%', width: '90%', margin:'0 5%'}">
        	<div class="modal-header">
            <div>
            <!-- <button type="button" class="close" data-dismiss="modal">&times;</button> -->
            <ul data-bind="style:{'float':'right'}">
              <li class="glyphicon glyphicon-remove" data-dismiss="modal" data-bind="style:{'display':'inline', 'margin': '2px 4px', 'float':'right'}"></li>
              <li class="dropdown" data-bind="style:{'display':'inline', 'margin': '2px 4px'}">
                  <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                      <i class="glyphicon glyphicon-download-alt"></i>
                  </span>
                  <ul class="dropdown-menu pull-right" role="menu">
                      <li data-bind="attr:{'id': podId()+'_Modal_exportPDF'}"><span>PDF Document</span></li>
                      <li data-bind="attr:{'id': podId()+'_Modal_exportPNG'}"><span>PNG Image</span></li>
                      <!-- <li data-bind="attr:{'id': podId()+'_Modal_exportJPG'}"><span>JPG Image</span></li> -->
                      <li data-bind="attr:{'id': podId()+'_Modal_exportCSV'}"><span>CSV Document</span></li>
                  </ul>
              </li>
            </ul>
            <h4 class="modal-title" data-bind="text:podTitle"></h4>
            <div class="breadcrumb" data-bind="style:{'height':'auto','margin-bottom':'0','padding':'0'}">
                    <div class="form-group" data-bind="style: {'margin-bottom':'0'}">
                        <div class="input-group">
                            <span class="input-group-addon"><span class="glyphicon glyphicon-time"></span></span>
                            <select class="form-control" data-bind="style:{'width':'auto', 'margin-right':'10px'}, attr:{'id' : 'expandTimeFilter_pod_'+podId()}">
                                <option value="60">1 hr</option>
                                <option value="1440">24 hr</option>
                                <option value="4320">1 month</option>
                                <option value="525600">1 year</option>
                            </select>
                        
                        </div>

                    </div>                  
           </div>
          </div>
          
          
        </div>

        <div class="modal-body">
          
          <p>Some text in the modal.</p>
          <!-- <div data-bind="component:{name: currentComponent, attr:{'height':'100%'}, params:{'podId':'id', 'podTitle': 'Title', 'podType': 'type'}}"></div> -->
        </div>
        <!-- <div class="modal-footer">
          <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        </div> -->
      </div>

    </div>
  </div>
</div>