define(['jquery','knockout','bootstrap','bootstrap-switch','btpopover','jqSimpleConnect','d3', 'c3','fusionCharts','text!./topology-view.html','hasher','ui-constants','ui-common'], function($,ko,bootstrap,bs,btpopover,jqSimpleConnect,d3,c3,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function TOPOLOGYVIEW(params) {
    var self = this;
    this.message = ko.observable('Hello from the '+params.podTitle+' component!'); 
    this.ptitle = ko.observable(params.podTitle);

    this.isModal = ko.observable(params.isModal);
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);

    /*JIM Topology related Observables*/
    this.topologyResponse = ko.observable();
    this.entryDetails = ko.observableArray();
    this.entryReqCount = ko.observable();
    this.entryResTime = ko.observable();
    this.entryReqSlowCount = ko.observable();
    this.totalJVM = ko.observable();
    this.goodCount = ko.observable();
    this.idleCount = ko.observable();
    this.slowCount = ko.observable();
    this.exitDetails = ko.observableArray();
    this.currentTime = ko.observable();

    /*Topology popover/tooltip Observables*/
    this.topologyPopovTitle = ko.observable();
    this.topologyPopovHeaders = ko.observableArray();
    this.topologyPopovData = ko.observableArray();

    

    this.currentPodBodyHeight = ko.observable();

    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podType);

    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        $('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');

        /*popover define and hiding container design*/
        $("#firstPopoverTemplate").hide(); 
        $('#donutChart').popover({
            placement : 'left',
            html : true,
            title: function(){
              return self.topologyPopovTitle()  
            },
            content: function() {
                return $('#firstPopoverTemplate').html();
            }                 

        });

        //on mouse out of topology drawing area closing popover/tooltip
        $( ".topologyCont" ).mouseleave(function() {
            if($("#donutChart").popover()){
              $("#donutChart").popover('hide');
            }
        });

          //self.initRequest();
        
    }

    this.modalRenderHandler = function(){

    }
   
  
    //div scroll listner to handle jqSimpleConnect
    $( "#podCont" ).scroll(function() {
      if(uiConstants.common.DEBUG_MODE) console.log("scroll listner called");
      jqSimpleConnect.repaintAll();

    });

    this.initRequest = function(){
      self.requestCall("http://www.mocky.io/v2/5704ab240f0000df14e5bbec?callback=?", "GET", {}, "Topology");
      self.entryDetails.removeAll();
      self.exitDetails.removeAll();
      jqSimpleConnect.removeAll();

      /*if($("#btnoverview").hasClass('active')){
        self.entryDetails.removeAll();
        self.exitDetails.removeAll();

        //reseting everything before sending request to server
        $("#pod1_podBody .jimLoadingCont").css("display","flex");
        $(".topologyParent").css('display','none');
        JimTopologyView.prototype.dispose();

        //calling REST API
        var appId = $('#applications :selected').val();
        var jvmId = $('#jvms :selected').val() || "0";
        if(uiConstants.common.DEBUG_MODE) console.log(appId +"------"+jvmId);
        if(jvmId == "0"){ //for All
           self.requestCall(serverIP+"/opdashboard/rest/jim/applications/"+appId+"/topologyDetails?callback=?", "GET", {}, "Topology");     
        }else{ //for particular jvms 
            self.requestCall(serverIP+"/opdashboard/rest/jim/jvms/"+jvmId+"/topologyDetails?callback=?", "GET", {}, "Topology");
        } 
      }*/
      
    }

    this.processTopologyData = function(data){
       if(data.result.idleCount == 0 && data.result.slowCount == 0 && data.result.goodCount == 0 && data.result.entry.length == 0 && data.result.exit.length == 0){
        $(".topologyParent").css('display','none');       
        //$("#pod1_podBody .podErrorCont").css("display","block").children('span').text("Topology data not available");
        JimTopologyView.prototype.dispose();
        
        }else{
          
           $(".topologyParent").css('display','block');
           //topology master data
           self.topologyMasterData = data.result;
           if(uiConstants.common.DEBUG_MODE) console.log("$$$$$$");
           if(uiConstants.common.DEBUG_MODE) console.log(self.entryDetails());
           if(uiConstants.common.DEBUG_MODE) console.log(self.exitDetails());

           if(data.result.entry.length){
            self.entryDetails.removeAll();
            self.entryDetails.push.apply(self.entryDetails, data.result.entry);
            if(uiConstants.common.DEBUG_MODE) console.log("%%%%%%%%%%%");
            if(uiConstants.common.DEBUG_MODE) console.log(self.entryDetails());
            self.entryReqCount(data.result.entry[0].requestCallCount);
            self.entryResTime(data.result.entry[0].avgRequestTime);
            self.entryReqSlowCount(data.result.entry[0].requestBadCount);
      
           }else{
            self.entryDetails.removeAll();
            jqSimpleConnect.removeAll();
           }
           
           if(data.result.exit.length){
             self.exitDetails.removeAll();
             self.exitDetails.push.apply(self.exitDetails, data.result.exit);
           }else{
            self.exitDetails.removeAll();
            jqSimpleConnect.removeAll();
           }

           //Dount chart related observables
           self.idleCount(data.result.idleCount);
           self.slowCount(data.result.slowCount);
           self.goodCount(data.result.goodCount);
           if($('#jvms :selected').val() == 0){
            self.totalJVM($("#jvms option").length - 1);
           }else{
            self.totalJVM('');
           }
           
           /*Donut chart drawing*/
          self.loadDonutChart("donutChart");
     }
      
     
    }

    this.loadDonutChart = function(ContId){
      var chart = c3.generate({
          bindto: document.getElementById(ContId),
          size: {
                //width: 150,
                height: 200
                //height : 180,
                //height: self.appPanelHeight(),
              },
          donut: {
              title: self.totalJVM(),
              label: {
              show: false
            }
          },
          grid: {
                focus: {
                  show: false
                }
              },
              area: {
                zerobased: true
              },
              legend: {
                  show: false
                },
              zoom:{
                  enabled: true,
              },
              padding: {
                  bottom: -20,
                  top: -20,
                  right: -20,
                  left : -20,

              },
          data: {
              //x: 'ga:browser',
              type: 'donut',              
              //xFormat: '%Y%m%d', // 'xFormat' can be used as custom format of 'x'
              columns: [                  
                  ['Slow', self.topologyResponse().slowCount ],
                  ['Idle', self.topologyResponse().idleCount ],
                  ['Good', self.topologyResponse().goodCount ],
              ],
              colors: {
                Idle: '#d9534f', 
                Slow: '#ec971f',
                Good: '#449d44'
            },            
            onmouseover: function(d, i){
              ////if(DEBUG_MODE) console.log("mouse over");
              self.loadPopoverWindow(this, d.id, d.value);
             }

          },          
          axis: {
              x: {
                  //type: 'timeseries',
                  tick: {
                      format: '%Y-%m-%d'
                  }
              }
          },
          tooltip: {
            show: false,                      
        }
      });

    if(self.entryDetails().length && self.exitDetails().length){
      jqSimpleConnect.removeAll();
      
      //Drawing line from entry to donut & donnut to exit
        jqSimpleConnect.connect("#entryChild1", "#donutChart svg", {radius: 1, color: '#333',anchorA: 'horizontal', anchorB: 'horizontal'});

        self.exitDetails().forEach(function(exitItem,index){
          jqSimpleConnect.connect("#donutChart svg", "#exitCont"+index, {radius: 1, color: '#333'});
          
        });
        
    }
    
    }

    this.loadPopoverWindow = function(context, jtype, jvalue){
      self.topologyPopovTitle(jtype+" JVM - "+jvalue);
      ////if(DEBUG_MODE) console.log(jtype+" JVM - "+jvalue);
      if(jtype == "Slow"){
        self.topologyPopovHeaders([
          {'columnTitle':'name', 'displayName': 'Name' ,'class': 'col-xs-4 textoverloading'},
          {'columnTitle':'violations','displayName': 'Violations', 'class': 'col-xs-4 textoverloading'},
          {'columnTitle':'count','displayName': 'Total Count', 'class': 'col-xs-4 textoverloading'}
      ]);
      }else{
        self.topologyPopovHeaders([
          {'columnTitle':'name', 'displayName': 'Name' ,'class': 'col-xs-8 textoverloading'},         
          {'columnTitle':'count','displayName': 'Total Count', 'class': 'col-xs-4 textoverloading'}
     ]);
      }
      

      if(jtype == "Slow"){
        self.topologyPopovData.removeAll();
        self.topologyResponse().slowJVM.forEach(function(violationItem,index){
          self.topologyPopovData.push({'name':violationItem.name, 'violations':(parseFloat(violationItem.violationPercentile).toFixed(2))+("%"), 'count':violationItem.totalCount});
        });
        self.topologyPopovTitle(jtype+" JVM - "+self.topologyPopovData().length);
      }else if(jtype == "Idle"){
        self.topologyPopovData.removeAll();
        self.topologyResponse().idleJVM.forEach(function(violationItem,index){
          self.topologyPopovData.push({'name':violationItem.name, 'count':violationItem.totalCount});
        });
        self.topologyPopovTitle(jtype+" JVM - "+self.topologyPopovData().length);
      }else if(jtype == "Good"){
        self.topologyPopovData.removeAll();
        self.topologyResponse().goodJVM.forEach(function(violationItem,index){
          self.topologyPopovData.push({'name':violationItem.name, 'count':violationItem.totalCount});
        });
        self.topologyPopovTitle(jtype+" JVM - "+self.topologyPopovData().length);
      }
      
      
    $("#donutChart").popover('show');
    }

    //calling all types(GET,POST,PUT,DELETE) of server/REST call
  this.requestCall = function(reqUrl, reqMethod, reqData, reqType){
      //Ajax call to get data of user selected menu(left)
      if(uiConstants.common.DEBUG_MODE) console.log(reqUrl);
      //if(DEBUG_MODE) console.log(reqData);
      //$("#pod1_podBody .podErrorCont").css("display","none");

      $.ajax({
            url: reqUrl,
            type: reqMethod,
            dataType: "json",
            data: reqData,
            success: function(data) {
                  if(uiConstants.common.DEBUG_MODE) console.log(reqType+" Response---->");
                  if(uiConstants.common.DEBUG_MODE) console.log(data);
                  if(reqType == "Topology"){
                      //$("#pod1_podBody .jimLoadingCont").css("display","none");
                      if(data.responseStatus.toLowerCase() == "success"){

                          //$("#pod1_podBody .podErrorCont").css("display","none");

                          self.topologyResponse(data.result);
                          self.currentTime(data.time);//.slice(0,-2));
                          $("#timeCont span").text("Last Collation Time - "+data.time);

                          //calling to process response data
                          self.processTopologyData(data);
                          $('#podBody_0').css("overflow-y","hidden !important");
                        
                      }else{
                        //$("#pod1_podBody .podErrorCont").css("display","block").children('span').text("Topology data not available");
                      }                          
                  }
                  
            },
            error: function(x, t, m) {
              if(uiConstants.common.DEBUG_MODE) console.log(x.status);
              if(uiConstants.common.DEBUG_MODE) console.log(m);
              if(uiConstants.common.DEBUG_MODE) console.log(t);
              //$("#pod1_podBody .jimLoadingCont").css("display","none");
              //$("#pod1_podBody .podErrorCont").css("display","block").children('span').text(x.status+"-"+m);           
            }
      });
  }

  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  TOPOLOGYVIEW.prototype.dispose = function() { };
  
  return { viewModel: TOPOLOGYVIEW, template: templateMarkup };

});