define(['jquery','bootstrap','knockout','validator','jquery-chosen','text!./add-multiple-transactions.html','hasher','ui-constants','ui-common','floatThead'], function($,bt,ko,validator,jc,templateMarkup,hasher,uiConstants,uicommon,floatThead) {

function AddmultipleTransactions(params) {
	var self = this;

	this.tableHeaders = ko.observableArray([]);

	this.tableHeadersHttp = ko.observableArray([
	  	{'attributeName': 'Sl.No.','name': '#','type': 'label','mandate':false},
	  	{'attributeName': 'Name','name':'Name','type':'input','mandate':true},
	  	{'attributeName': 'Description','name':'Description','type':'input','mandate':true},
	  	{'attributeName': 'Method','name':'Method','type':'method','mandate':true},
	  	{'attributeName': 'URL','name':'URL','type':'input','mandate':true},
	  	{'attributeName': 'Header Pattern','name':'HeaderPattern','type':'input','mandate':false},
	  	{'attributeName': 'Body Pattern','name':'BodyPattern','type':'input','mandate':false},
	  	{'attributeName': 'Query Param Name','name':'QueryParamName','type':'input','mandate':true},
	  	{'attributeName': 'Query Param Value','name':'QueryParamValue','type':'input','mandate':true},
	  	{'attributeName': 'DC Threshold(ms)','name':'DCThreshold','type':'input','mandate':false},
	  	{'attributeName': 'EUE Threshold(ms)','name':'EUEThreshold','type':'input','mandate':false},
	  	{'attributeName': 'Tag','name':'Tag','type':'input','mandate':false},
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.tableHeadersTcp = ko.observableArray([
	  	{'attributeName': 'Sl.No.','name': '#','type': 'label','mandate':false},
	  	{'attributeName': 'Name','name':'Name','type':'input','mandate':true},
	  	{'attributeName': 'Description','name':'Description','type':'input','mandate':true},
	  	{'attributeName': 'Start Pattern','name':'StartPattern','type':'input','mandate':true},
	  	{'attributeName': 'Length','name':'Length','type':'input','mandate':false},
	  	{'attributeName': 'End Pattern','name':'EndPattern','type':'input','mandate':false},
	  	{'attributeName': 'DC Threshold(ms)','name':'DCThreshold','type':'input','mandate':false},
	  	{'attributeName': 'EUE Threshold(ms)','name':'EUEThreshold','type':'input','mandate':false},
	  	{'attributeName': 'Tag','name':'Tag','type':'input','mandate':false},  
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.tableHeadersSelect = ko.observableArray([
	  	{'attributeName': '','name': '','type': 'label','mandate':false},
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.txnTypeArr = ko.observableArray(params.transactionTypeArr().slice(1));
	this.txnMethodArr = ko.observableArray(params.transactionMethodArr());
	this.transactionResTypeArr = ko.observableArray(params.transactionResTypeArr());
	this.pageSelected = params.pageSelected;
	this.currentViewIndex = ko.observable(params.currentViewIndex());

    this.errorStack = ko.observableArray();
    this.typeSelected = ko.observable();
    this.reqRecordCounter = ko.observableArray();
    this.multiRowAddLimit = ko.observable(1001);
    this.errorStackLimitOnRows = ko.observable(10);
    this.requestDataArray = [];
    this.showAddRowBtn = ko.observable(1);
    var previousTxnTypeSelected = "";
    var previousTxnTypeIdSelected = 0;

	//render handler will be call after loading all elements of current SPA component.
	this.renderHandler=function(){
		var $tab = $('#tableMultipleAdd');
		$tab.floatThead({
			scrollContainer: function($table){
				return $table.closest('.wrapper');
			}
		});

		$(window).resize(function(){
		    self.refreshPageLayout();
		});

		/*Jquery chosen start*/
		jQuery(".chosen").chosen({
			search_contains: true	
		});
		$("#txnTypeList").trigger('chosen:updated');

	    $("#txnTypeList").on('chosen:showing_dropdown', function () {
	        // Store the current value on focus and on change
	        previousTxnTypeIdSelected = this.value;
	        previousTxnTypeSelected =  $("#txnTypeList option:selected").text();
	    }).change(function() {
	        // Do something with the previous value after the change
	    });

	    self.refreshPageLayout();
	}

	this.refreshPageLayout = function(){
		$(".wrapper").height(($(window).outerHeight() - $(".wrapper").offset().top - $("#actionBtns").outerHeight() - 50) + "px");
		$('#tableMultipleAdd').floatThead('reflow');
	}

	this.onTxnTypeChange = function(){
		var rowCounter = $('#tableMultipleAdd tbody tr').length;
		self.typeSelected($('#txnTypeList option:selected').text());
		
		if(slugify($('#txnTypeList option:selected').text()) == "http"){
			self.getConfirmOnTypeChange(rowCounter,self.tableHeadersHttp(),previousTxnTypeIdSelected);	
		}
		else if(slugify($('#txnTypeList option:selected').text()) == "tcp"){
			self.getConfirmOnTypeChange(rowCounter,self.tableHeadersTcp(),previousTxnTypeIdSelected);
		}
		else{
			showMessageBox(uiConstants.transactionConfig.CONFIRM_TXN_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
				if(r){
					debugger;
					self.tableHeaders(self.tableHeadersSelect());
					self.tableHeaders.removeAll();
					$('#tableMultipleAdd tbody').empty();
					$("#btnSave").addClass('disabled');	

					console.log("id selected : "+previousTxnTypeIdSelected);
				}
				else{
					console.log("id selected : "+previousTxnTypeIdSelected);
					$("#txnTypeList").val(previousTxnTypeIdSelected).trigger('chosen:updated');
					self.typeSelected($('#txnTypeList option:selected').text());
				}
			});
		}

		self.showAddRowBtn($('#txnTypeList').val() == ""?1:0);
	}

	this.getConfirmOnTypeChange=function(rowCounter,dynmicheaders,previous){
		if(rowCounter == 0){
				self.tableHeaders(dynmicheaders);
				self.onAddClick();
		}else {
			showMessageBox(uiConstants.transactionConfig.CONFIRM_TXN_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
				if(r){
					self.tableHeaders(dynmicheaders);
					$('#tableMultipleAdd tbody').empty();
					$("#btnSave").addClass('disabled');	

					if($('#txnTypeList option:selected').text() != "Select"){
						self.onAddClick();
					}
				}
				else{
					$("#txnTypeList").val(previous).trigger('chosen:updated');
					self.typeSelected($('#txnTypeList option:selected').text());
				}
			});
		}
	}

	this.cancelAddScreen = function(){
		self.pageSelected("Transaction Configuration");
		$('#tableMultipleAdd tbody').empty();
		params.currentViewIndex(uiConstants.common.LIST_VIEW);
	}

	//Adding blank row into the grid and calling binding listner and assigning ID's of row and col elements.
	this.onAddClick = function(){
		 //add new blank row initially on click of add button
		 if(uiConstants.common.DEBUG_MODE)console.log('%%%%%%%%%');
		 if(uiConstants.common.DEBUG_MODE)console.log(self.tableHeaders());
		 
		 var rowCounter = $('#tableMultipleAdd tbody tr').length;
		 var row = $('<tr class="" id="row_'+rowCounter+'"/>');
		 var tabIndexCounter = rowCounter * self.tableHeaders().length;

		 if(rowCounter <= self.multiRowAddLimit()-2){
	 		for (var i = 0; i < self.tableHeaders().length; i++) {		 	
			 	if(self.tableHeaders()[i].type == 'label'){
			 		row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.tableHeaders()[i].type+rowCounter+"'>"+(rowCounter+1)+"</label></span></td>");
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Name'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Name' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Description'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Description' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'method' && self.tableHeaders()[i].name == 'Method'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'URL'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Pattern' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'HeaderPattern'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Pattern' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'BodyPattern'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Pattern' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'QueryParamName'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Name' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'QueryParamValue'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Value' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'StartPattern'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Pattern' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Length'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Length' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'EndPattern'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Pattern' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'DCThreshold'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter DC Threshold' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'EUEThreshold'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter EUE Threshold' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Tag'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Tags separated by comma(,)' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'delete'){
			 		row.append("<td class='col-xs-1 deleteRow' id='"+self.tableHeaders()[i].type+rowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
			 	}

			 	tabIndexCounter++;
			 }		 
			 $("#tableMultipleAdd tbody").append(row);
			 self.addDataListOption('method','Method'+rowCounter, uiConstants.common.SELECT);

			 //delete row 
			 self.deleteRowBind("delete"+rowCounter);
			 
			 //calling table row listner
			 self.bindTableRowListner("row_"+rowCounter);
			 self.enableButton();

		 }else{
			showMessageBox(uiConstants.common.RECORDS_PASTE_LIMIT_EXCEEDED, "error");			
		}
		
		if($('#tableMultipleAdd tbody tr').length == 1){
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
		}
		else{
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled");
		}

		self.refreshPageLayout();
	}

	//Loading all values into Type dropdown and Timezone custom dropdown and selecting current option otherwise default value will be set.
	this.addDataListOption = function(type, _class, _val ){
		_val = (typeof _val == "string" ? _val.trim() : _val);
		/*console.log(_class.slice(-1));
		var rowCnt=_class.slice(-1);*/
		if(type == 'method'){
			$('<option>', {text: uiConstants.common.SELECT, value: ''}).appendTo('#'+_class);
	 		self.txnMethodArr().forEach(function(item){
	 			$('<option>', {value: item.transactionMethodId, text: item.transactionMethodName, name: item.transactionMethodName}).appendTo('#'+_class);
      		});

			//$('#'+_class+' option').filter(function () { return $(this).html() == compAttribHostAddrObj.attributeDefaultValue || $(this).html().indexOf("("+compAttribHostAddrObj.attributeDefaultValue+")") != -1 || $(this).html().indexOf(compAttribHostAddrObj.attributeDefaultValue+" ") != -1; }).prop('selected', true);

      		$('#'+_class+' option').filter(function() { 
				if(($(this).text() === _val)) {
				  return true;
				}                       
			}).prop('selected', true);

			jQuery(".chosen").chosen({
				search_contains: true	
			});
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid Method from the list.");}
 			
 		}
	}

	//paste listner on every rows Application name filed and on rest of fileds treat as normal copy&paste.
	this.bindTableRowListner = function(rowid){
		
		 $('#row_0 input ,#row_0 select ').on('change', function(e){
		 	 $("#btnClearAll").removeClass('disabled');	

		 	 if($("#Name0").val() == "" &&  $("#Description0").val() == "" && $("#Tag0").val() == "" && $("#Method0 option:selected").text() == uiConstants.common.SELECT){
		 	 	 $("#btnClearAll").addClass('disabled');
		 	 }
		 });


		 //on paste listener
		 var indexID = rowid.split("_")[1];
		 //$('#'+rowid+'>td>input').on('paste', function(e){
		 $('#'+rowid+'>td>#Name'+indexID).on('paste', function(e){
		 	var curObj = this;
		 	showLoadingContainer();

		 	e.stopPropagation();
		    e.preventDefault(); 	

		    if (e.originalEvent.clipboardData) { 
				var data = (e.originalEvent || e).clipboardData.getData('text/plain');
				var inputId = e.target.id;
			} else if (window.clipboardData) { 
				var data = window.clipboardData.getData('Text');
				var inputId = window.event.srcElement.id;
			}

		 	setTimeout(function () {
			    $("#btnClearAll").removeClass('disabled');	

				//data = data.slice(1, -1);
				if(uiConstants.common.DEBUG_MODE)console.log(data);

			   if(data != null){
			   		//clearing old vaule
			   		self.errorStack.removeAll();

			   		var crid = $(curObj).parent().parent().attr('id');
			   		$("#"+crid).addClass('empty');
			   		var curRowIndex = $(curObj).parent().parent().index();
					
			   		data.replace(/\n$/, "");
					var rows = data.split("\n");
					var table = $("#tableMultipleAdd tbody");
					var curType = uiConstants.common.SELECT;
					var curTZ = "";				
					
					var rowCounter = $('#tableMultipleAdd tbody tr').length;
					
					
					var limitFlag = (rows.length-1) + rowCounter;				
					if(limitFlag <= self.multiRowAddLimit()){
						if(uiConstants.common.DEBUG_MODE)console.log("Total no of rows----------------->"+limitFlag);
						
							var col = rows[0].split("\t");
							
							if(uiConstants.common.DEBUG_MODE)console.log(col.length +"=="+ (self.tableHeaders().length-2));

							if(col.length <= self.tableHeaders().length-2){

								rowCounter = $('#tableMultipleAdd tbody tr').length;
								

								

								rows.forEach(function (y, yindex) {	

								    var cells = y.split("\t");
								    if(uiConstants.common.DEBUG_MODE)console.log(cells);					    
								    var currentRowCounter = rowCounter + yindex;
								    var row = $("<tr class='' id='row_"+currentRowCounter+"'/>");
								    

								    if(yindex < rows.length-1){					    	
									    if(cells.length != self.tableHeaders().length-2){
									    	self.manageErrorMessage('push',currentRowCounter, "Cell values not be separated by newline('\\n')");
									    }

									    //control creation start
								    	for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
								    		if(self.tableHeaders()[xindex].type == 'label' && self.tableHeaders()[xindex].name == '#'){
								    			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.tableHeaders()[xindex].type+currentRowCounter+"'></label></td>");
								    		}
								    		else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Name'){
										 		row.append("<td class='col-xs-2'><input type='text' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Description'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Description' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'method'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'URL'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Pattern' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'HeaderPattern'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Pattern' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'BodyPattern'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Pattern' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'QueryParamName'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Name' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'QueryParamValue'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Value' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'StartPattern'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Pattern' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Length'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Length' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'EndPattern'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Pattern' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'DCThreshold'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter DC Threshold' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
											else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'EUEThreshold'){
												row.append("<td class='col-xs-2'><input type='text' placeholder='Enter EUE Threshold' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");	
											}
										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Tag'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}
								    	};					   
									    row.append("<td class='col-xs-1 deleteRow' id='"+self.tableHeaders()[xindex].type+currentRowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
									    
									    //table.append(row);
									    $(".empty").after(row);
									    table.find('.empty').removeClass('empty').next().addClass('empty');

									    self.deleteRowBind("delete"+currentRowCounter);
									    self.bindTableRowListner("row_"+currentRowCounter);
									    //control creation end

									    var tarr = [currentRowCounter+1];
									    cells = tarr.concat(cells);
									    if(uiConstants.common.DEBUG_MODE)console.log(cells);

									    //value assign to current row controls start
									    for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {					    	
									    	
									    	//value assign
									    	if(self.tableHeaders()[xindex].type == 'label' && self.tableHeaders()[xindex].name == '#'){
									    		$("#"+self.tableHeaders()[xindex].type+currentRowCounter).text(cells[xindex]||"");
									    	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Name'){
									    		var cname = cells[xindex] || "";
									    		if(cname == ""){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
									    		}else if(nameValidation(cname) == 0){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
									    		}					    			
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Description'){
										 		
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'method'){
										 		self.addDataListOption('method','Method'+currentRowCounter, cells[xindex]||"");
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'URL'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'HeaderPattern'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'BodyPattern'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'QueryParamName'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'QueryParamValue'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'StartPattern'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Length'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'EndPattern'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'DCThreshold'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'EUEThreshold'){
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]);
										 	}

										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Tag'){
										 		var ctag = cells[xindex] || "";
										 		if(ctag != ""){
										 			if(ctag.trim().endsWith(","))
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
										 			else if(tagValidationWithComma(ctag) == 0)
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
									    		}
									    		if(uiConstants.common.DEBUG_MODE)console.log(ctag);	
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}
									    }
									    //value assign to current row controls end
									}
								});
							}else{
								
								showMessageBox(uiConstants.common.COLUMNS_MISMATCH, "error");	
								return false;
							}	

					}else{
						showMessageBox(uiConstants.common.ROW_LIMIT_EXCEEDED, "error");
						self.enableButton();
						//self.onAddClick();
					}

					if(limitFlag <= self.multiRowAddLimit()){
						$("#tableMultipleAdd tbody").find('.empty').removeClass('empty');
						$("#"+crid).remove();

						if($('#tableMultipleAdd tbody tr').length == 1){
							$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
						}

						self.chnageRowAndCellId();
						self.validateElementsValue();
						if(self.errorStack().length){	
							self.manageErrorMessage('print');
							if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());								
						}
					}
				}


				//if(rowid > 200){
		 			removeLoadingContainer();

				//}
		 	}, 1);

		 	
		});

		
	}


	 //Changing row and col elements ID's when any delete or append/prepend operation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	var rowCnt = 0;
    	$('#tableMultipleAdd tbody tr').each(function(i){    		    					
	        $(this).children('td').each(function(j){
	        	if(slugify($("#txnTypeList option:selected").text()) == "http"){
	            	if(j==0){// Application Index 
		            	$(this).children('label').attr('id','label'+rowCnt);
		            	$(this).children('label').text(rowCnt+1);
		            }else if(j==1){// Transaction Name 
		            	$(this).children('input').attr('id','Name'+rowCnt);
		            }else if(j==2){// Descripition
		            	$(this).children('input').attr('id','Description'+rowCnt);
		            }else if(j==3){
		            	$(this).children('input').attr('id','Method'+rowCnt);
		            }else if(j==4){
		            	$(this).children('input').attr('id','URL'+rowCnt);
		            }else if(j==5){
		            	$(this).children('input').attr('id','HeaderPattern'+rowCnt);
		            }else if(j==6){
		            	$(this).children('input').attr('id','BodyPattern'+rowCnt);
		            }else if(j==7){
		            	$(this).children('input').attr('id','QueryParamName'+rowCnt);
		            }else if(j==8){
		            	$(this).children('input').attr('id','QueryParamValue'+rowCnt);
		            }else if(j==9){
		            	$(this).children('input').attr('id','DCThreshold'+rowCnt);
		            }else if(j==10){
		            	$(this).children('input').attr('id','EUEThreshold'+rowCnt);
		            }else if(j==11){//Transaction Tag
		            	$(this).children('input').attr('id','Tag'+rowCnt);
		            }else if(j==12){
	 					$(this).attr('id','delete'+rowCnt);
	 				}
	            }
	            else if(slugify($("#txnTypeList option:selected").text()) == "tcp"){
	            	if(j==0){// Application Index 
		            	$(this).children('label').attr('id','label'+rowCnt);
		            	$(this).children('label').text(rowCnt+1);
		            }else if(j==1){// Transaction Name 
		            	$(this).children('input').attr('id','Name'+rowCnt);
		            }else if(j==2){// Descripition
		            	$(this).children('input').attr('id','Description'+rowCnt);
		            }else if(j==3){
		            	$(this).children('input').attr('id','StartPattern'+rowCnt);
		            }else if(j==4){
		            	$(this).children('input').attr('id','Length'+rowCnt);
		            }else if(j==5){
		            	$(this).children('input').attr('id','EndPattern'+rowCnt);
		            }else if(j==6){
		            	$(this).children('input').attr('id','DCThreshold'+rowCnt);
		            }else if(j==7){
		            	$(this).children('input').attr('id','EUEThreshold'+rowCnt);
		            }else if(j==8){//Transaction Tag
		            	$(this).children('input').attr('id','Tag'+rowCnt);
		            }else if(j==9){
	 					$(this).attr('id','delete'+rowCnt);
	 				}
	            }
	        	
	        });
	        $(this).attr('id','row_'+rowCnt);
	        rowCnt++;
	    });

    }

	//after adding or pasting dynamic rows in to grid, delete row listner binding.
	this.deleteRowBind = function(deleteRowId){
		$('#'+deleteRowId).on('click', function(e){

			if(uiConstants.common.DEBUG_MODE)console.log($('#tableMultipleAdd tbody tr').length);	 		
		 		if($('#tableMultipleAdd tbody tr').length == 0){
		 			self.disableButton();
		 		}else if($('#tableMultipleAdd tbody tr').length == 1){
		 			return;
		 		}else{
		 			var curObj = this;
	 				showMessageBox(uiConstants.transactionConfig.CONFIRM_DELETE_TXN, "question", "confirm", function confirmCallback(confirmDelete){
						if(confirmDelete){
		 					$(curObj).parent().remove();

		 					self.chnageRowAndCellId();
					 		if($('#tableMultipleAdd tbody tr').length == 1){
								$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
							}
			 			}
					});
		 		}

		 		//changing each row's Id and childrens id
		 		
		 });
	}	
    
	//enabling buttons based on conditions
	this.enableButton = function(){
		if($('#tableMultipleAdd tbody tr').length > 0){
			$("#btnAddMultiple").removeClass('disabled');
			$("#btnSave").removeClass('disabled');
		}
	}

	//clearing all rows with confirmation of user's. 
	this.onClearAll = function(){
		var rowCounter = $('#tableMultipleAdd tbody tr').length;
		if(rowCounter > 1){		
		    showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableMultipleAdd tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
			    }
			});
		}
		else if(rowCounter == 1 ){			
			showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS_CONTENT, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableMultipleAdd tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
					
			    }
			});
		}		
	}

	//managing all validation error message at pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount){		
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			//if(addRecordCount>1)
				var messageStr = addRecordCount+" "+uiConstants.transactionConfig.SUCCESS_MULTIPLE_ADD_TXNS+" and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			//else
			//	var messageStr = addRecordCount+" application is successfully added and "+unsavecnt +" have failed. \n";

				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						messageStr += "Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";
			self.errorStack().forEach(function(item,index){
			if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				/*if(item.rowno == -1){					
					messageStr += item.message +"\n";					
					self.enableButton();					
					if(DEBUG_MODE)console.log("count not match while copy");
				}else */
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					messageStr += "Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}

	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(startOffSet, endOffSet){
		self.errorStack.removeAll();
		self.requestDataArray = [];
		var selThresholdArr = [];
		var dcReponseObj = $.grep(self.transactionResTypeArr(), function(evt){ return evt.responseTypeName == 'DC'; })[0];
		var eueReponseObj = $.grep(self.transactionResTypeArr(), function(evt){ return evt.responseTypeName == 'EUE' || evt.responseTypeName == 'EUM'; })[0];

		debugger;

		$('#tableMultipleAdd tbody tr').each(function(i){
			var cobj = {'index': i+1};
			var txnPatternObj = {};
			selThresholdArr = [];
	        $(this).children('td').each(function(j){
	            if(j==1){// Transaction Name 
	            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.common.NAME_REQUIRED);
		    		}else if(nameValidation(cname) == 0){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.transactionConfig.TXN_MULTIPLE_NAME_LENGTH_ERROR);
		    		}
	            	cobj['txnName'] = $(this).children('input').val();
	            }
	            else if(j==2){// Description
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
	            		self.manageErrorMessage('push',cobj.index-1, uiConstants.login.DESC_REQUIRED);
	            	}
	            	cobj['description'] = $(this).children('input').val();
	            }
	            else{
	            	if(slugify($("#txnTypeList option:selected").text()) == "http"){
		            	if(j==3){
			            	var cname = $(this).children('select').val();
			            	if(cname == "" || cname == "0"){
			            		self.manageErrorMessage('push',cobj.index-1, "Method type is required");
			            	}
			            	txnPatternObj['typeId'] = parseInt($(this).children('select').val());
			            	txnPatternObj['type'] = $(this).children('select').children("option").filter(":selected").html();
		            	}
		            	else if(j==4){
			            	var cname = $(this).children('input').val();
			            	if(cname == ""){
			            		self.manageErrorMessage('push',cobj.index-1, "URL is required");
			            	}
			            	txnPatternObj['urlPattern'] = $(this).children('input').val();
		            	}
		            	else if(j==5){
			            	var cname = $(this).children('input').val();
			            	txnPatternObj['headerPattern'] = $(this).children('input').val();
		            	}
		            	else if(j==6){
			            	var cname = $(this).children('input').val();
			            	txnPatternObj['bodyPattern'] = $(this).children('input').val();
		            	}
		            	else if(j==7){
			            	var cname = $(this).children('input').val();
			            	if(cname == ""){
			            		self.manageErrorMessage('push',cobj.index-1, "Query Parameter Name is required");
			            	}
			            	txnPatternObj['queryName'] =   $(this).children('input').val();
		            	}
		            	else if(j==8){
			            	var cname = $(this).children('input').val();
			            	if(cname == ""){
			            		self.manageErrorMessage('push',cobj.index-1, "Query Parameter Value is required");
			            	}
			            	txnPatternObj['queryValue'] =   $(this).children('input').val();
		            	}
		            	txnPatternObj['queryParam'] = [{"key":txnPatternObj['queryName'],"value":txnPatternObj['queryValue']}];
		            }




		            else if(slugify($("#txnTypeList option:selected").text()) == "tcp"){
		            	if(j==3){
			            	var cname = $(this).children('input').val();
			            	if(cname == ""){
			            		self.manageErrorMessage('push',cobj.index-1, uiConstants.transactionConfig.START_PATTERN_REQUIRED);
			            	}
			            	txnPatternObj['tcpStartPattern'] = $(this).children('input').val();
			            }
		            	else if(j==4){
		            		var cname = $(this).children('input').val();
			            	if(cname != "" && isNaN(cname)){
			            		self.manageErrorMessage('push',cobj.index-1, "Pattern length should be a number");
			            	}
			            	txnPatternObj['length'] = $(this).children('input').val();
		            	}
		            	else if(j==5){
		            		var cname = $(this).children('input').val();
			            	if(txnPatternObj['length'] != "" && cname == ""){
			            		self.manageErrorMessage('push',cobj.index-1, uiConstants.transactionConfig.END_PATTERN_REQUIRED);
			            	}
			            	txnPatternObj['tcpEndPattern'] = $(this).children('input').val();
		            	}

		            }
		        }


	            if((slugify($("#txnTypeList option:selected").text()) == "http" && j==9) || (slugify($("#txnTypeList option:selected").text()) == "tcp" && j==6)){
	            	if(!isNaN($(this).children('input').val().trim()) && $(this).children('input').val().trim() != ""){
		            	selThresholdArr.push({
							"responseTypeId": parseInt(dcReponseObj.responseTypeId),//      parseInt($("#responseTypeList_"+thresholdIdx).val()),//self.thresholdArr()[thresholdIdx].responseTypeId,
							"responseType":  dcReponseObj.responseTypeName,
							"threshold": parseInt($(this).children('input').val())
						});
	            	}
            	}
            	else if((slugify($("#txnTypeList option:selected").text()) == "http" && j==10) || (slugify($("#txnTypeList option:selected").text()) == "tcp" && j==7)){
	            	if(!isNaN($(this).children('input').val().trim()) && $(this).children('input').val().trim() != ""){
            			selThresholdArr.push({
							"responseTypeId": parseInt(eueReponseObj.responseTypeId),
							"responseType":  eueReponseObj.responseTypeName,
							"threshold": parseInt($(this).children('input').val())
						});
            		}
            	}
            	else if((slugify($("#txnTypeList option:selected").text()) == "http" && j==11) || (slugify($("#txnTypeList option:selected").text()) == "tcp" && j==8)){
            		if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var ctag = $(this).children('input').val();
	            	if(ctag != ""){
			 			if(ctag.trim().endsWith(","))
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
			 			else if(tagValidationWithComma(ctag) == 0)
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		    		}
	            	var taglist = $(this).children('input').val();
	            	if(taglist != ""){
	            		taglist = taglist.split(",");
	            		if(uiConstants.common.DEBUG_MODE)console.log("*********");
	            		if(uiConstants.common.DEBUG_MODE)console.log(taglist);
	            		var tagObj = [];
	            		for (var i = 0; i < taglist.length; i++) {
	            			if($.grep(tagObj, function(evt){ return evt.tagName == taglist[i].trim(); }).length>0){
		    					self.manageErrorMessage('push',cobj.index-1, uiConstants.common.DUPLICATE_TAGS);
		    					break;
	            			}
	            			//tagObj.push({'tagId':null , 'tagName': taglist[i]});
	            			tagObj.push({"tagId":null, "tagName":taglist[i].trim(), "tagOperation":"add"});
	            		};
	            	}
	            	cobj['tags'] = tagObj;
            	}
	        });

			if(selThresholdArr.length == 0){
		    	self.manageErrorMessage('push',cobj.index-1, "Threshold for atleast one response type (DC or EUE) is required");
			}


        	cobj['txnTypeId'] = parseInt($("#txnTypeList option:selected").val());
        	cobj['txnType'] = $("#txnTypeList option:selected").text();
        	cobj['bizValueExtractorList'] = [];
        	cobj['txnAuditDetails'] = [];
        	cobj['applicationId'] = parseInt(params.applicationId);
        	cobj['isAutoConfigured'] = false;
        	cobj['isAuditEnabled'] = false;
        	cobj['txnThresholds'] = selThresholdArr;
			cobj['status'] = 1;

			if(slugify($("#txnTypeList option:selected").text()) == "http"){
				cobj['subTransactions'] =[{
					"httpTxnConfig":txnPatternObj,
					"tcpTxnConfig": null
				}];
            }
            else if(slugify($("#txnTypeList option:selected").text()) == "tcp"){
				cobj['subTransactions'] =[{
					"httpTxnConfig":null,
					"tcpTxnConfig": txnPatternObj
				}];
            }
            
			self.requestDataArray.push(cobj);
	    });
	}

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
		self.requestDataArray = [];
    	self.validateElementsValue();

		if(self.errorStack().length){	
			self.manageErrorMessage('print');
		}else{
			self.reqRecordCounter(self.requestDataArray.length);
			var finalObj = {'transactions':self.requestDataArray};
			if(uiConstants.common.DEBUG_MODE)console.log("==================== Add Multiple Transactions ===================");
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
			var url = uiConstants.common.SERVER_IP+"/transaction";
			requestCall(url, 'POST', JSON.stringify(finalObj), 'AddTransactions', successCallback, errorCallback);	
		}
    }

    //Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
    	var succnt = 0;
    	var failcnt = 0;
    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
    	if(self.reqRecordCounter() == res.length){
    		for (var i = 0; i < res.length; i++) {
    			//if(DEBUG_MODE)console.log(res[i]);
    			//if(DEBUG_MODE)console.log(res[i].index);
    			//var rid = res[i].index-1;
    			var rid = parseInt(res[i].objectId)-1;
    			if(res[i].responseStatus == uiConstants.common.CONST_SUCCESS){
    				$("#tableMultipleAdd tbody #row_"+rid).remove();
    				succnt++;	
    			}else{
    				self.manageErrorMessage('push',failcnt,handleServiceErrorMsgs(uiConstants.common.CONST_TRANSACTION,res[i].errorCode,res[i].message));
    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+res[i].message);
    				failcnt++;
    			}
    		};

    		//changing row index and number
    		self.manageErrorMessage('print',"","",succnt);
    		self.chnageRowAndCellId();
    	}else{
    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
    	}
    }

    function successCallback(data, reqType) {
		if(reqType === "AddTransactions"){
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			var res = data.result;
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(uiConstants.transactionConfig.ERROR_ADD_MULTIPLE_TXNS, "error");
			}
			else{
				showMessageBox(uiConstants.transactionConfig.SUCCESS_MULTIPLE_ADD_TXNS);
				$('#tableMultipleAdd tbody').empty();
				if(self.txnTypeArr().length){
					$("#btnAddMultiple").trigger('click');
				}
				self.cancelAddScreen();
			}
		}
	}

	function errorCallback(reqType) {
		showMessageBox(uiConstants.transactionConfig.ERROR_ADD_MULTIPLE_TXNS, "error");
	}

}

AddmultipleTransactions.prototype.dispose = function() { };
return { viewModel: AddmultipleTransactions, template: templateMarkup };

});