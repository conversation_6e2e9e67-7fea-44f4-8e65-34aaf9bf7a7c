 <div class="panel panel-default">
	<div class="configPanel panel-heading"><h4><span data-bind="text: pageSelected"></span></h4></div>
	<div class="panel-body">
		<div class="col-sm-12" id="listkpiDetailsPage" data-bind="template: {afterRender: renderHandler}" >
				<div class="row" style="padding-right:14px"> 
					<label class="control-label col-sm-1">Type <span class="mandatoryField">*</span></label>
					<div class="col-sm-2">
						<select class="chosen form-control" id="txnTypeList" data-bind="options: txnTypeArr(),  optionsText: 'transactionTypeName', optionsValue: 'transactionTypeId', optionsCaption: 'Select', event:{change: onTxnTypeChange}" required="true">
						</select>
					</div>
					<!-- <span style="width:100%"></span> -->
					<div class="text-right">
						<button  type="button" class="btn btn-default" id="btnAddMultiple" data-bind="disable: showAddRowBtn ,click: onAddClick">Add</button> 
					</div>
				 </div> 
				<br>
			 	<div class="wrapper">
			        <table class="table table-hover table-striped table-sm" id="tableMultipleAdd" >
			          <thead class="a1-list-grid-header">
			            <tr data-bind="foreach: tableHeaders" >
			          		<th style="text-align: center;" data-bind="{css: $data.attributeName == '' || $data.attributeName == 'Sl.No.' ? 'col-xs-1' : 'col-xs-2'}">
				          		<span data-bind="text : $data.attributeName"></span>
				          		<span data-bind="if : $data.mandate" class="mandatoryField">*</span>
				          	</th> 
			          	</tr>
			          </thead>
			          <tbody>
			         		
			          </tbody>         
					</table>
				</div>
			<!-- <div><span class="col-xs-10" data-bind="visible: errorMessage"></span></div> -->
			
			<div id="actionBtns" class="text-right divActionPanel">
			     <button class="btn btn-primary disabled" id="btnSave" data-bind="click: onMultipleSaveClick">Save</button> 
			     <button class="btn disabled" id="btnClearAll" data-bind="click: onClearAll">Reset</button> 
			     <button class="btn" id="btnCancel"  data-bind="click : cancelAddScreen" >Cancel</button>
			</div> 
		</div>
	</div>
</div>
