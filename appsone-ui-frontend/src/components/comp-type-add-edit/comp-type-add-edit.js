define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead','text!./comp-type-add-edit.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon) {

	function ConfigAddEdit(params) {	
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.currentViewIndex = params.isModal ? ko.observable(uiConstants.common.ADD_SINGLE_VIEW) : params.currentViewIndex;
		this.configStatus = ko.observable(1);
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.pageSelected = ko.observable("");
		configType = localStorage.configType;
		this.isModal = ko.observable(false);
		this.compType = params.compType;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.selectedConfigRows = params.selectedConfigRows;


		/*if(localStorage.selectedConfigRows)
			selectedConfigRows = JSON.parse(localStorage.selectedConfigRows);
		else
			selectedConfigRows = [];*/

		this.renderHandler=function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$("#txtName").focus();

			if(params.isModal){
				self.isModal(true);
			}
			else{
				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows().length>0){
					self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
				}
			}

			setPageTitle();
			if(self.currentViewIndex() == 3){
				self.pageSelected("Edit "+self.pageSelected());
				editSingleConfig(self.selectedConfigRows());
				if(!self.selectedConfigRows()[0].status){ //if the config is inactive
					setConfigUneditable(true);
				}
			}

			else if(self.currentViewIndex() == 5){
				viewConfig(self.selectedConfigRows());
			}
			else{
				self.pageSelected("Add "+self.pageSelected());
			}
		}

		function setPageTitle(){
			self.pageSelected("");
			self.pageSelected("Component Type");
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
        		if(self.isModal()){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});


		//Adding/Updating single config
		this.addEditConfig = function(){
			this.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			self.configName(self.configName().trim());
			$("#divCompTypeAddEdit #txtDescription").val($("#divCompTypeAddEdit #txtDescription").val().trim());

			if(self.configName() == undefined || self.configName() == ""){
				showError("#divCompTypeAddEdit #txtName", uiConstants.componentTypeConfig.COMPONENT_TYPE_NAME_REQUIRED);
		    	self.errorMsg("#divCompTypeAddEdit #txtName");
			}
			else if(self.configName().length < 2){
				showError("#divCompTypeAddEdit #txtName", uiConstants.componentTypeConfig.COMPONENT_TYPE_NAME_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divCompTypeAddEdit #txtName");
			}
			else if(self.configName().length > 45){
				showError("#divCompTypeAddEdit #txtName", uiConstants.componentTypeConfig.COMPONENT_TYPE_NAME_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divCompTypeAddEdit #txtName");
			}
			else if(!nameValidation(self.configName())){
				showError("#divCompTypeAddEdit #txtName", uiConstants.componentTypeConfig.COMPONENT_TYPE_NAME_INVALID_ERROR);
		    	self.errorMsg("#divCompTypeAddEdit #txtName");
			}
			if(self.configDescription() == undefined || self.configDescription() == ""){
				showError("#divCompTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
		    	self.errorMsg("#divCompTypeAddEdit #txtDescription");
			}
			else if(self.configDescription().length < 25){
				showError("#divCompTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divCompTypeAddEdit #txtDescription");
			}
			else if(self.configDescription().length > 256){
				showError("#divCompTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divCompTypeAddEdit #txtDescription");
			}

			if(self.errorMsg() == ""){
				var configObj = {
					"index":1,
					"masterId": self.configId(),
					"name": self.configName(),
					"description": $("#divCompTypeAddEdit #txtDescription").val() != ""? self.configDescription().trim():null,
					"status" : self.configStatus()?1:0};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configObj));

				if(self.configId() == 0 || self.isModal()){
					requestCall(uiConstants.common.SERVER_IP + "/componentType", "POST", JSON.stringify(configObj), "addSingleConfig", successCallback, errorCallback);
				}
				else{
					requestCall(uiConstants.common.SERVER_IP + "/componentType", "PUT", JSON.stringify(configObj), "editSingleConfig", successCallback, errorCallback);
				}
			}
		}

		function editSingleConfig(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);

			self.configId(configObj[0].masterId);
			self.configName(configObj[0].name);
			self.configDescription(configObj[0].description);
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());

			if(configObj[0].hasOwnProperty("isCustom") && configObj[0].isCustom == 0)
				$('#txtName').prop('readonly', true);
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);

			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divCompTypeAddEdit .chosen-container b").css("display", "none");
		}

		this.cancelConfig = function(){
			if(self.isModal()){
				$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Component Type Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPTYPE,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.componentTypeConfig.ERROR_ADD_COMPONENT_TYPE, "error");
					}
				}
				else{
					if(self.isModal()){
						self.compType(self.configName());
					}
					else{
						params.curPage(1);
					}
					self.cancelConfig();
					showMessageBox(uiConstants.componentTypeConfig.SUCCESS_ADD_COMPONENT_TYPE);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPTYPE,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.componentTypeConfig.ERROR_UPDATE_COMPONENT_TYPE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.componentTypeConfig.SUCCESS_UPDATE_COMPONENT_TYPE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.componentTypeConfig.ERROR_ADD_COMPONENT_TYPE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.componentTypeConfig.ERROR_UPDATE_COMPONENT_TYPE, "error");
			}
		}		
	}

	ConfigAddEdit.prototype.dispose = function() { };
	return { viewModel: ConfigAddEdit, template: templateMarkup };
});