<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divCompTypeAddEdit">
	<!-- <div class="panel-heading" data-bind="visible: !isModal()"><h4><span data-bind="text: pageSelected"></span></h4></div> -->

	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
	
	<div class="panel-body">
	<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
		<div id="divConfigName" class="form-group form-required">
			<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
			<div class="col-sm-4">
				<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
			</div>
		</div>

		<div id="divConfigDescription" class="form-group form-required">
			<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
			<div class="col-sm-4">
				<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
			</div>
		</div>

		<div id="divConfigStatus" class="form-group" data-bind="visible : currentViewIndex() != 1 && !isModal()">
			<label class="control-label col-sm-2" >Status</label>
			<div class="col-sm-4">
				<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

				<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">

			</div>
		</div>

		<div class="form-group">
			<div class="col-sm-offset-2 col-sm-4 divActionPanel">
				<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5, event:{click: addEditConfig}">Save</button>
				<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
			</div>
		</div>
	</form>
	</div>
</div>