 <div class="panel panel-default">
	<div class="configPanel panel-heading"><h5><span data-bind="attr: {id: mode == 'componentWizard' ? 'panel'+ instTypeIndex() : ''}, text: mode == 'componentWizard' ? panelTitle : pageSelected"></span></h5></div>
	<div class="panel-body" data-bind="template: {afterRender: renderHandler}"> 
		<div class="col-sm-12" data-bind="attr: {'id': widgetName}" >	<!-- -->

				<form class="form-horizontal" role="form" >
					<div class="form-group form-required">
						<label class="control-label col-sm-2">Component Type <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compTypeList" data-bind="foreach : componentsArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->

								
								<option data-bind="value: $data.componentTypeId, text: $data.componentType"></option>
								
							</select>
						</div>
					</div>

					<div class="form-group form-required">
						<label class="control-label col-sm-2">Component <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compNamesList" data-bind="foreach : componentNamesArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->
								
								<option data-bind="value: $data.componentId, text: $data.componentName"></option>
							</select>
						</div>
					</div>

					<div class="form-group form-required">
						<label class="control-label col-sm-2">Version <span class="mandatoryField">*</span></label>
						<div class="col-sm-4">
							<select class="chosen form-control" id="compVersionsList" data-bind="event:{'chosen:showing_dropdown': setPreviousVersion}, foreach : componentVersionsArr" data-placeholder=" ">
								<!-- ko if: $index() == 0 -->
									<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
								<!-- /ko-->
								
								<option data-bind="value: $data.versionId, text: $data.version"></option>
							</select>
						</div>
					</div>
				</form>

				<div class=text-right>
					<button type="button" class="btn btn-default disabled" id="btnCompInstMultipleAdd" tabindex="-1">Add</button>  <!--  data-bind="click: onAddClick" -->
				</div>
				<br>
			 	<div class="wrapper">
			        <table class="table table-hover table-striped table-sm" id="tableCompInstAddMultiple"  data-bind="visible:showTable">
			          <thead class="a1-list-grid-header">
			            <tr data-bind="foreach: tableHeaders" >
			          		<th style="word-break: break-all;" data-bind="{css: $data.attributeName == '' || $data.attributeName == 'Sl.No.' ? 'col-xs-1' : 'col-xs-2'}">
			          			
				          		<span data-bind="text : $data.attributeName"></span>
				          		<span data-bind="attr : {'id' : $data.attributeId+'|~'+$data.attributeType+'|~'+$data.isMandatory+'|~'+$data.attributeMinLength+'|~'+$data.attributeMaxLength+'|~'+$data.attributeRegEx+'|~'+$data.componentAttributeMappingId}"></span>
				          		<span data-bind="if : $data.isMandatory" class="mandatoryField">*</span>
				          	</th>

				          	
			          	</tr> 
			          </thead>
			          <tbody>
			         		
			          </tbody>         
					</table>
				</div> 
			
			
			<div id="actionBtns" class="text-right divActionPanel">
			     <button class="btn btn-primary disabled" id="btnSave" data-bind="click: onMultipleSaveClick">Save</button>
			     <button class="btn disabled" id="btnClearAll" data-bind="click: onClearAll">Reset</button> 
			     <button class="btn" id="btnCancel" data-bind="click : cancelAddScreen">Cancel</button>
			</div> 

		</div>
		<!--Duplicate component-->
		

	</div>
</div>
