define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./add-multiple-component-instance-general.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

function Addmultiplecomponentinstancegeneral(params) {
    var self = this;
	var prevVersionNameSelected="";
	var prevVersionIdSelected=0;

	this.tableHeadersSelect = ko.observableArray([
	  	{
	      "attributeId": 0,
	      "attributeName": "Sl.No.",
	      "attributeType": "label",
	      "isMandatory": 0,
	      "isCustom": 0
		},
		{ "attributeId": 0,
	      "attributeName": "Name",
	      "attributeType": "TextBox",
	      "isMandatory": 0,
	      "isCustom": 0
	  	},
		/*{ "attributeId": 0,
	      "attributeName": "Description",
	      "attributeType": "TextBox",
	      "isMandatory": 0,
	      "isCustom": 0
	  	},*/
	  	{ "attributeId": 0,
	      "attributeName": "",
	      "attributeType": "delete",
	      "isMandatory": 0,
	      "isCustom": 0
	  	}
	]);

	this.mode=params.mode;
	this.panelTitle=params.panelTitle;


    if(self.mode == "componentWizard"){
		self.widgetName = trimSpacesReplaceUnderscore(params.pageSelected);
		this.componentsArr=params.componentsArr;
	}
	else{
		self.pageSelected=params.pageSelected();
		self.widgetName=trimSpacesReplaceUnderscore(params.pageSelected());
		this.componentsArr=params.tempComponentsArr();
	}	

    this.selcomponentTypeId=params.selcomponentTypeId;
    this.selComponentId=params.selComponentId;
    this.selVersionId=params.selVersionId;
    this.numInst=params.numInst;
    this.selHostId=params.selHostId;
    this.selHostVersion=params.selHostVersion;
    this.mode=params.mode;
    this.applicationId = params.applicationId;
    this.applicationsArr = params.applicationsArr;

	this.currentViewIndex = params.currentViewIndex;
	this.pageSelected = params.pageSelected;

	this.componentNamesArr = ko.observableArray([{}]);
	this.componentVersionsArr = ko.observableArray([{}]);

	this.compAttributesArr = ko.observableArray();
	this.tableHeaders = ko.observableArray();

  	this.errorStack = ko.observableArray();
	this.reqRecordCounter = ko.observableArray();
    this.multiRowAddLimit = ko.observable(1001);
    this.errorStackLimitOnRows = ko.observable(10);
    this.kpiProducerMapArr = ko.observableArray();
    this.kpiGroupProducerMapArr = ko.observableArray();

    this.requestDataArray = [];
    this.nonGroupkpiProdMapArr = [];
    this.groupkpiProdMapArr = [];
    this.showAddRowBtn = ko.observable(1);
    this.showTable = ko.observable(true);

    this.noInstancesArr = params.noInstancesArr || ko.observableArray();
    this.instanceDetailArr = params.instanceDetailArr || ko.observableArray();
    this.instanceDeleted = params.instanceDeleted || ko.observable("");
    this.rowIndex = params.rowIndex;
    this.allElementsLoaded = params.allElementsLoaded;
    this.newCompTypeInstPanelsCount = params.newCompTypeInstPanelsCount || ko.observable(-1);
    this.totalCompTypeInstPanelsCount = ko.observable(params.totalCompTypeInstPanelsCount);
    this.deletedCompInstArr = params.deletedCompInstArr || ko.observableArray();
    var compTypeInstArr = (params.compTypeInstArr && params.compTypeInstArr() && params.compTypeInstArr()['componentTypeInstances'] && params.compTypeInstArr()['componentTypeInstances'].length && params.compTypeInstArr()['componentTypeInstances'][params.instTypeIndex()]) ? params.compTypeInstArr()['componentTypeInstances'][params.instTypeIndex()].componentInstances : ko.observableArray();
    this.compInstanceMode = params.compInstanceMode || ko.observableArray();
    this.instTypeIndex = params.instTypeIndex || ko.observable();

	this.renderHandlerCompWizard=function(){
		self.newCompTypeInstPanelsCount(self.newCompTypeInstPanelsCount()-1);

		if(self.newCompTypeInstPanelsCount() == 0){
			self.allElementsLoaded(true);
		}
	}

	this.renderHandler=function(){
		var $tab = $('#tableCompInstAddMultiple');
		/*$tab.floatThead({
			scrollContainer: function($table){
				return $table.closest('.wrapper');
			}
		});*/

		$(window).resize(function(){
		   // self.refreshPageLayout();
		});

		jQuery(".chosen").chosen({
			search_contains: true	
		});
		
		enableAddbutton();
		$("#"+self.widgetName+" #compTypeList").trigger('chosen:updated');
		
		$("#"+self.widgetName+" #compTypeList").on('change', function () {	
	    	setCompNames();
		});
		if(self.mode == "componentWizard"){
			$("#"+self.widgetName+" #compTypeList").val(self.selcomponentTypeId).trigger('chosen:updated');
			setCompNames();
		}


		$("#"+self.widgetName+" #compNamesList").on('change', function () {
	    	setCompVersions();
		});
		if(self.mode == "componentWizard"){
			$("#"+self.widgetName+" #compNamesList").val(self.selComponentId).trigger('chosen:updated');
			setCompVersions();
		}

		/*$("#Add_Component_Instances #compVersionsList_chosen").on('focus', function () {
			alert("initiall 12Selected : "+prevVersionIdSelected);
	        prevVersionIdSelected =  this.value;
	        prevVersionNameSelected =  $("#"+self.widgetName+" #compVersionsList :selected").text();
	    }).change(function() {});*/

		$("#"+self.widgetName+" #compVersionsList").on('change', function () {
	    	setCompAttributes(); 	
	    	//self.onVersionListChange();
	    	setCompKpiProducerMap();
		});

		if(self.mode == "componentWizard"){
			$("#"+self.widgetName+" #compVersionsList").val(self.selVersionId).trigger('chosen:updated');
			setCompAttributes(); 	
	    	//self.onVersionListChange();
		}

		$("#"+self.widgetName+" #btnCompInstMultipleAdd").on('click', function () {
	    	self.onAddClick();
		});

		//show save and cancel for individual component instance wizard
		if(self.mode == "componentWizard"){
			//$("#"+self.widgetName+" #btnSave").css('display','none');
			//$("#"+self.widgetName+" #btnCancel").css('display','none');
			$("#"+self.widgetName+" #btnSave").css('visibility','hidden');
			$("#"+self.widgetName+" #btnClearAll").css('visibility','hidden');
			$("#"+self.widgetName+" #btnCancel").css('visibility','hidden');
		}
		else{
			//$("#"+self.widgetName+" #btnSave").css('display','block');
			//$("#"+self.widgetName+" #btnCancel").css('display','block');
			$("#"+self.widgetName+" #btnSave").css('visibility','none');
			$("#"+self.widgetName+" #btnClearAll").css('visibility','none');
			$("#"+self.widgetName+" #btnCancel").css('visibility','none');
		
		}

		//disable drop down when it is componentWizard mode
		if(self.mode == "componentWizard"){
			$("#"+self.widgetName+" #compTypeList").prop('disabled', true).trigger("chosen:updated");;
			$("#"+self.widgetName+" #compNamesList").prop('disabled', true).trigger("chosen:updated");;
			$("#"+self.widgetName+" #compVersionsList").prop('disabled', true).trigger("chosen:updated");
		}
		else{
			$("#"+self.widgetName+" #compTypeList").prop('disabled', false).trigger("chosen:updated");;
			$("#"+self.widgetName+" #compNamesList").prop('disabled', false).trigger("chosen:updated");;
			$("#"+self.widgetName+" #compVersionsList").prop('disabled', false).trigger("chosen:updated");;
		}

		$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
		$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");

		//self.refreshPageLayout();
	}

	this.refreshPageLayout = function(){
		$(".wrapper").height(($(window).outerHeight() - $(".wrapper").offset().top - $("#actionBtns").outerHeight() - 50) + "px");
		$('#tableCompInstAddMultiple').floatThead('reflow');
	}

	function setCompKpiProducerMap(){
		if($("#"+self.widgetName+"#compVersionsList").val() == 0){
			self.kpiProducerMapArr([]);
			self.kpiGroupProducerMapArr([]);
			//self.kpiGroupProducerMapArr([]);
		}
		else{
				var componentVersionId = $("#compVersionsList").val(); //Send this to REST API for getting KPI/Producer mapping
				requestCall(uiConstants.common.SERVER_IP + "/componentKpiProducerMapping/"+componentVersionId+"?status=2", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
		}
	}

	/* Get components and version : start*/
	function setCompNames(){
		if($("#compTypeList").val() == 0){
				self.componentNamesArr({});
				$('#compNamesList').prop('disabled', true).trigger("chosen:updated");

				self.componentVersionsArr({});
				$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");
		}
		else{
			if($("#"+self.widgetName+" #compTypeList").val() == 0){
				self.componentNamesArr({});
				self.componentVersionsArr({});
			}
			else{
				var componentsObj = $.grep(self.componentsArr, function(e){ return e.componentTypeId == $("#"+self.widgetName+" #compTypeList").val(); });
				//var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#compTypeList").val(); });

				if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
				self.componentNamesArr(componentsObj[0].components);

				//to clear version when component is not selected
				self.componentVersionsArr({});
				$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");
				//to clear html body
				self.showTable(false);
				self.removeTableHeaderandData(self.tableHeaders());
			}
			$("#"+self.widgetName+" #compNamesList").trigger('chosen:updated');
			enableAddbutton();
			$('#compNamesList').prop('disabled', false).trigger("chosen:updated");
		}
	}

	function setCompVersions(){
		if($("#compNamesList").val() == 0){
			self.componentVersionsArr({});
			$('#compVersionsList').prop('disabled', true).trigger("chosen:updated");
		}
		else{
			if($("#"+self.widgetName+" #compNamesList").val() == 0){
				self.componentVersionsArr({});
			}
			else{
				var componentsObj = $.grep(self.componentNamesArr(), function(e){ return e.componentId == $("#"+self.widgetName+" #compNamesList").val(); });

				if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
				self.componentVersionsArr(componentsObj[0].versions);

				self.showTable(false);
				self.removeTableHeaderandData(self.tableHeaders());
			}
			$("#"+self.widgetName+" #compVersionsList").trigger('chosen:updated');
			enableAddbutton();
			$('#compVersionsList').prop('disabled', false).trigger("chosen:updated");
		}
	}

	function setCompAttributes(){
		if($("#"+self.widgetName+" #compVersionsList").val() == 0){
			self.compAttributesArr([]);
			self.showTable(false);
			self.removeTableHeaderandData(self.tableHeaders());
		}
		else{
			if(uiConstants.common.DEBUG_MODE)console.log("=======================Get Attributes for multiple Component Instance ====================================");
			if(uiConstants.common.DEBUG_MODE)console.log($("#"+self.widgetName+" #compVersionsList").val());
			requestCall(uiConstants.common.SERVER_IP + "/componentAttribute/"+$("#"+self.widgetName+" #compVersionsList").val(), "GET", "", "getComponentAttributes", successCallback, errorCallback);
			self.showTable(true);
		}
		enableAddbutton();
	}

	function enableAddbutton(){
		if($("#"+self.widgetName+" #compVersionsList option:selected").text() == "Select"){
			$('#'+self.widgetName+' #btnCompInstMultipleAdd').addClass('disabled');
		}
		else{
			$('#'+self.widgetName+' #btnCompInstMultipleAdd').removeClass('disabled');
		}
	}

	this.onVersionListChange = function(){
		self.typeSelected=$("#"+self.widgetName+" #compVersionsList option:selected").text();
			
			if($('#'+self.widgetName+' #compVersionsList option:selected').text() != "Select"){
				self.getConfirmOnVersionChange(self.tableHeaders(),prevVersionIdSelected,self.numInst);	
			}
			else{
				showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP, "question", "confirm", function confirmCallback(r){
					if(r){
						self.removeTableHeaderandData(self.tableHeaders());
					}
					else{
						$("#"+self.widgetName+" #compVersionsList").val(prevVersionIdSelected);
					}
				});
			}

			self.showAddRowBtn($("#"+self.widgetName+" #compVersionsList").val() == ""?1:0);
	}

	this.removeTableHeaderandData=function(){
		self.tableHeaders(self.tableHeaders());
		self.tableHeaders.removeAll();
		$("#"+self.widgetName+" #tableCompInstAddMultiple tbody").empty();
		$("#"+self.widgetName+" #btnSave").addClass('disabled');	
	}

	this.setPreviousVersion = function(){
    	prevVersionIdSelected =  $("#"+self.widgetName+" #compVersionsList :selected").val();
	    prevVersionNameSelected =  $("#"+self.widgetName+" #compVersionsList :selected").text();
    }

	this.getConfirmOnVersionChange=function(dynmicheaders,previous,numInst){
		var rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
		if(rowCounter == 0  && prevVersionIdSelected == 0){
				self.tableHeaders(dynmicheaders);
				if(self.mode == "componentWizard"){
					console.log("++++++++++++++++++++++++++++++++++++++++++++++++++++++++++");
					console.log("Number of Inst: "+numInst);
					for(var i=0;i<numInst;i++){
						self.onAddClick();
					}

	 				self.renderHandlerCompWizard();

				}
				else{
					self.onAddClick();
				}
		}else {
			
			showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP, "question", "confirm", function confirmCallback(r){
				if(r){
					self.tableHeaders(dynmicheaders);
					$('#'+self.widgetName+' #tableCompInstAddMultiple tbody').empty();
					$('#'+self.widgetName+' #btnSave').addClass('disabled');	
					if($('#'+self.widgetName+' #compVersionsList option:selected').text() != "Select"){
						self.onAddClick();
					}
				}
				else{
					$('#compVersionsList').val(previous).trigger('chosen:updated');
				}
			});
		}
	}

	/* Get components and version : end*/

	this.cancelAddScreen = function(){
		self.pageSelected("Component Instance Configuration");
		$('#'+self.widgetName+' #tableCompInstAddMultiple tbody').empty();
		params.currentViewIndex(uiConstants.common.LIST_VIEW);
	}

	this.getDynamicInstanceName = function(widgetName,indxCount){
		var instanceName;
		if(self.mode == "componentWizard"){
			var typePrefix = widgetName.substr(0,4)+widgetName.match(/\d+$/)[0];
		var typeId = $("#"+widgetName+" #compTypeList").val();
			instanceName = "CI_"+self.applicationId()+"_"+typePrefix+typeId+"_"+(indxCount+1);
		}else{
			instanceName = "";
		}
		return instanceName;
	}

	//create controls first time on addclick with tabindex
	this.createControlsOnAddClick = function(controlType,controlId,rowCounter,row,indx,defaultValue,tabIndexCounter){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlId);
		if(uiConstants.common.DEBUG_MODE)console.log("===================create controls on click============================");
		if(uiConstants.common.DEBUG_MODE)console.log(controlType+" : "+controlId+" : "+defaultValue);
		//var dynCIName='componentInstance_"+self.widgetName+"_"+(getTimeStamp+1)+;

		if(controlType == 'label'){
			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.widgetName+controlType+rowCounter+"'>"+(rowCounter+1)+"</label></td>");			
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim != 'Tags'){
	 		if(controlIdAfterTrim == 'Name'){
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+self.getDynamicInstanceName(self.widgetName,rowCounter)+"' class='col-xs-10 form-control'></td>");	
	 		}
	 		/*else if(controlIdAfterTrim == 'Description'){
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+"CompInstDescrip"+rowCounter+"' value='' class='col-xs-10 form-control'></td>");	
	 		}*/
	 		else{
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+defaultValue+"' class='col-xs-10 form-control'></td>");	
	 		}
	 	}
	 	else if(controlType == 'Password'){
	 		row.append("<td class='col-xs-2'><input type='password' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+defaultValue+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'DropDown'){
	 		row.append("<td class='col-xs-2'><select id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'/></td>");		 		
	 	}
	 	else if(controlType == 'CheckBox'){
	 		if(defaultValue == 1){
	 			row.append("<td class='col-xs-2'><input type='checkbox' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' checked class='col-xs-10'></td>");	
	 		}
	 		else{
	 			row.append("<td class='col-xs-2'><input type='checkbox' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10'></td>");	
	 		}
	 	}
	 	else if(controlType == 'SuggesterBox' && self.mode != "componentWizard"){
	 		row.append("<td class='col-xs-2'><select id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='chosen form-control col-xs-10'/></td>");		 		
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){					    			
	 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'delete'){
	 		row.append("<td class='col-xs-1 deleteRow' id='"+self.widgetName+controlType+rowCounter+"'><span type='button' title='Delete' class='glyphicon glyphicon-remove buttondelete'></span><span id='"+self.widgetName+'compInst'+rowCounter+"' style='display: none;'></span><span id='" + self.widgetName+'hostAddrPort'+rowCounter+"' style='display: none;'></span></td>");

	 	}
	}

	//create controls depending on the type
	this.createControls = function(controlType,controlId,rowCounter,row,indx,defaultValue,tabIndexCounter){

		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlId);
		
		if(controlType == 'label' && controlId == 'Sl.No.'){
			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.widgetName+controlType+rowCounter+"'></label></td>");
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim != 'Tags'){
	 		if(controlIdAfterTrim == 'Name'){
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+self.getDynamicInstanceName(self.widgetName,indx)+"' class='col-xs-10 form-control'></td>");	
	 		}
	 		/*else if(controlIdAfterTrim == 'Description'){
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+"CompInstDescrip"+rowCounter+"' value='' class='col-xs-10 form-control'></td>");	
	 		}*/
	 		else{
	 			row.append("<td class='col-xs-2'><input type='text' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+defaultValue+"' class='col-xs-10 form-control'></td>");
	 		}
	 	}
	 	else if(controlType == 'Password'){
	 		row.append("<td class='col-xs-2'><input type='password' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' value='"+defaultValue+"' class='col-xs-10 form-control'></td>");	
	 	}
	 	else if(controlType == 'DropDown'){
	 		row.append("<td class='col-xs-2'><select id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'/></td>");		 		
	 	}
	 	else if(controlType == 'CheckBox'){
	 		if(defaultValue == 1){
	 			row.append("<td class='col-xs-2'><input type='checkbox' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' checked class='col-xs-10'></td>");	
	 		}
	 		else{
	 			row.append("<td class='col-xs-2'><input type='checkbox' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10'></td>");	
	 		}
	 	}
	 	else if(controlType == 'SuggesterBox'  && self.mode != "componentWizard"){
	 		row.append("<td class='col-xs-2'><select id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='chosen form-control col-xs-10'/></td>");		 		
	 	}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){	
	 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+self.widgetName+controlIdAfterTrim+rowCounter+"' class='col-xs-10 form-control'></td>");				    			 		
	 	}
	}

	this.assignValuesToControls = function(controlType,controlName,rowCounter,_listValues,isMandate,cells,indx){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(controlName);
		if(controlType == 'label' && controlIdAfterTrim == 'Sl.No.'){
    		$("#"+self.widgetName+controlType+rowCounter).text(cells[indx]||"");
    	}else if(controlType == 'TextBox'){
    		var cname = cells[indx] || "";
    		if(cname == "" && isMandate == 1){
    			self.manageErrorMessage('push',rowCounter, uiConstants.common.ERROR_FIELD_REQUIRED);
    		}/*else if(nameValidation(cname) == 0){
    			self.manageErrorMessage('push',rowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
    		}	*/				    			
	 		$("#"+self.widgetName+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}else if(controlType == 'Password'){
	 		var cname = cells[indx] || "";
    		if(cname == "" && isMandate == 1){
    			self.manageErrorMessage('push',rowCounter, uiConstants.common.ERROR_FIELD_REQUIRED);
    		}
	 		$("#"+self.widgetName+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}
	 	else if(controlType == 'DropDown'){
	 		self.addDataListOption(controlType,controlIdAfterTrim+rowCounter, cells[indx]||"",_listValues);
	 	}
	 	else if(controlType == 'CheckBox'){
	 		var checkVal=(cells[indx] == 1)?true:false;
	 		$("#"+self.widgetName+controlIdAfterTrim+rowCounter).prop("checked",checkVal);
	 	}
	 	else if(controlType == 'SuggesterBox'  && self.mode != "componentWizard"){
	 		//console.log("addValuestothecontrol : "+self.widgetName+controlIdAfterTrim+rowCounter);
			self.addDataListOption(controlType,controlIdAfterTrim+rowCounter, cells[indx]||"",[])
 		}
	 	else if(controlType == 'TextBox' && controlIdAfterTrim == 'Tags'){
	 		var ctag = cells[indx] || "";
	 		if(ctag != ""){
	 			if(ctag.trim().endsWith(","))
    				self.manageErrorMessage('push',rowCounter, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
	 			else if(tagValidationWithComma(ctag) == 0)
    				self.manageErrorMessage('push',rowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
    		}
    		if(uiConstants.common.DEBUG_MODE)console.log(ctag);	
	 		$("#"+self.widgetName+controlIdAfterTrim+rowCounter).val(cells[indx]||"");
	 	}
	}

	this.onAddClick = function(){
		 //add new blank row initially on click of add button
		 if(uiConstants.common.DEBUG_MODE)console.log("=========================ComponentInstance Multiple Addclick =============================");
		 if(uiConstants.common.DEBUG_MODE)console.log(self.tableHeaders());
		 
		 var rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
		 var lastTabIndex = 0;
		 var tagsStr = "";

		 $('#divInstDet [tabindex]').attr('tabindex', function (a, b) {
		    lastTabIndex = Math.max(lastTabIndex, +b);
		});

		 var tabIndexCounter = lastTabIndex + (rowCounter * self.tableHeaders().length);
		 if(self.mode == "componentWizard"){
		 	self.noInstancesArr.splice(self.rowIndex(), 1, rowCounter+1);
		 	self.instanceDetailArr()[self.rowIndex()].numberInst = rowCounter+1;
		}
		 var row = $('<tr class="" id="'+self.widgetName+'row_'+rowCounter+'"/>');
		 if(uiConstants.common.DEBUG_MODE)console.log(rowCounter);
		 if(uiConstants.common.DEBUG_MODE)console.log(row);

		 if(rowCounter <= self.multiRowAddLimit()-2){
	 		 for (var i = 0; i < self.tableHeaders().length; i++) {	
	 			self.createControlsOnAddClick(self.tableHeaders()[i].attributeType,self.tableHeaders()[i].attributeName,rowCounter,row,i,self.tableHeaders()[i].attributeDefaultValue,tabIndexCounter);
	 			tabIndexCounter++;
			 }		 
			 $("#"+self.widgetName+" #tableCompInstAddMultiple tbody").append(row);


			 self.deleteRowBind(self.widgetName);
			 for(var i=0;i<self.tableHeaders().length;i++){

			 	if(self.tableHeaders()[i].attributeType == 'DropDown'){
			 		self.addDataListOption('DropDown',self.tableHeaders()[i].attributeName+rowCounter, 'Select',self.tableHeaders()[i].attributeOptions); 					
			 	}
			 	else if(self.tableHeaders()[i].attributeType == 'SuggesterBox'  && self.mode != "componentWizard"){
				 	self.addDataListOption('SuggesterBox',self.tableHeaders()[i].attributeName+rowCounter, 'Select',[]);
				}
			 }

			 var type = "";



			if(params.compTypeInstArr && params.compTypeInstArr() && params.compTypeInstArr()['componentTypeInstances'] && params.compTypeInstArr()['componentTypeInstances'].length && params.compTypeInstArr()['componentTypeInstances'][params.instTypeIndex()]){
				var compInstArr = params.compTypeInstArr()['componentTypeInstances'][params.instTypeIndex()].componentInstances;
				for(var compInst in compInstArr){
					$("#"+self.widgetName+"Name"+compInst).val(compInstArr[compInst].componentInstanceName);
					//$("#"+self.widgetName+"CompInstDescrip"+compInst).val(compInstArr[compInst].description);
					$("#"+self.widgetName+"compInst"+compInst).text(compInstArr[compInst].componentInstanceId);
				}
			}

 			if(compTypeInstArr[rowCounter] && self.mode == "componentWizard" && self.compInstanceMode() == "Edit"){
 				for(var compAttrib in compTypeInstArr[rowCounter].attributes){
				 	type = compTypeInstArr[rowCounter].attributes[compAttrib].attributeType;

					if(type.toUpperCase() == 'PASSWORD' || type.toUpperCase() == 'TEXTBOX'){
						$("#"+self.widgetName + compTypeInstArr[rowCounter].attributes[compAttrib].attributeName + rowCounter).val(compTypeInstArr[rowCounter].attributes[compAttrib].attributeValue);
					}
					else if(type.toUpperCase() == 'DROPDOWN'){
						$("#"+self.widgetName + compTypeInstArr[rowCounter].attributes[compAttrib].attributeName + rowCounter + " option").filter(function () { return $(this).html() == compTypeInstArr[rowCounter].attributes[compAttrib].attributeValue; }).prop('selected', true);
					}
					else if(type.toUpperCase() == 'CHECKBOX'){
						$("#"+self.widgetName + compTypeInstArr[rowCounter].attributes[compAttrib].attributeName + rowCounter).prop('checked',compTypeInstArr[rowCounter].attributes[compAttrib].attributeValue);				
					}
				 }

				 tagsStr = "";
				 for(var tag in compTypeInstArr[rowCounter].tags){
				 	tagsStr = tagsStr + ", " + compTypeInstArr[rowCounter].tags[tag].tagName;
				 }
 			 	 $("#"+self.widgetName + "Tags" + rowCounter).val(tagsStr.substring(2));
 			}

			 self.bindTableRowListner("row_"+rowCounter,"Name");//self.tableHeaders()[1].attributeName
			 self.enableButton();

		 }else{
			showMessageBox(uiConstants.common.RECORDS_PASTE_LIMIT_EXCEEDED, "error");			
		 }

		if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length == 1){
			$("#tableCompInstAddMultiple tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
		}
		else{
			$("#tableCompInstAddMultiple tr span.buttondelete").removeClass("confButtonDisabled");
		}

		//self.refreshPageLayout();
	}

	this.addDataListOption = function(type, _class, _val ,_listValues){
		var controlIdAfterTrim=trimSpacesReplaceUnderscore(_class);
		if(type == 'DropDown'){
			$('<option>', {text: 'Select', value: ''}).appendTo("#"+self.widgetName+controlIdAfterTrim);
	 		_listValues.forEach(function(item){
	 			$('<option>', {value: item.attributeOptionId, text: item.attributeOptionName, name: item.attributeOptionName}).appendTo("#"+self.widgetName+controlIdAfterTrim);
      		});
      		$("#"+self.widgetName+controlIdAfterTrim+' option').filter(function() { 
			    if(($(this).text() === _val)) {return true;}			    
			}).prop('selected', true);
			
			if($("#"+self.widgetName+controlIdAfterTrim).val() == ""){self.manageErrorMessage('push', controlIdAfterTrim.substr(controlIdAfterTrim.length-1), "Please select valid item from the list.");}
 		}
 		else if(type == 'SuggesterBox'  && self.mode != "componentWizard"){
		
 			if(uiConstants.common.DEBUG_MODE)console.log(type+"-------"+_class+"-----"+_val);
 			if(uiConstants.common.DEBUG_MODE)console.log("++++++++++++++++++++++++++++++++++");
 			
 			var flag = false;
 			$('<option>', {value: '', text: 'Select', name: 'Select'}).appendTo('#'+self.widgetName+controlIdAfterTrim);
 				
 			self.applicationsArr().forEach(function(item){
      			if(_val == item.applicationName){
      				$('<option>', {value: item.applicationId, text: item.applicationName, name: item.applicationName, selected:true}).appendTo('#'+self.widgetName+controlIdAfterTrim);
      				flag = true;
      			}else{
      				$('<option>', {value: item.applicationId, text: item.applicationName, name: item.applicationName}).appendTo('#'+self.widgetName+controlIdAfterTrim);	
      			}	
      		});

      		/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#"+self.widgetName+_class+"_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});

			/*Jquery chosen start*/
			if($("#"+self.widgetName+controlIdAfterTrim).chosen().val() != "" && flag == false){self.manageErrorMessage('push', controlIdAfterTrim.substr(controlIdAfterTrim.length-1), uiConstants.componentInstanceConfig.APPLICATION_NAME_SELECTION_REQUIED);}
 			if(self.mode == "componentWizard"){
 				$("#"+self.widgetName+controlIdAfterTrim).val(self.applicationId()).prop('disabled', true).trigger('chosen:updated');
 				$("#"+self.widgetName+controlIdAfterTrim+"_chosen").attr('title',$("#"+self.widgetName+controlIdAfterTrim+" option:selected").text());
 			}
 		}
	}

	//paste listner on every rows Application name filed and on rest of fileds treat as normal copy&paste.
	this.bindTableRowListner = function(rowid,firstColumnAttributeName){//first attribute name should always be textinput
		if(uiConstants.common.DEBUG_MODE)console.log("====================================ComponentInstance Pasted data===============================");		
		if(uiConstants.common.DEBUG_MODE)console.log(rowid+" : "+firstColumnAttributeName);
		 $('#'+self.widgetName+'row_0 input ,#'+self.widgetName+'row_0 select ').on('change', function(e){
		 	$("#"+self.widgetName+" #btnClearAll").removeClass('disabled');	

		 	var clearflag=true;
			for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
			 	 if($("#"+self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[xindex].attributeName)+"0").val() != ""){
			 	 	 console.log("Component instance column name : "+"#"+self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[xindex].attributeName)+"0");
			 	 	 clearflag=false;
			 	 }
			}
			if(clearflag){ 
			 	$("#"+self.widgetName+" #btnClearAll").addClass('disabled');
			}
		 });

		  if(uiConstants.common.DEBUG_MODE)console.log($("#"+self.widgetName+' #'+self.widgetName+rowid+'>td>#'+self.widgetName+trimSpacesReplaceUnderscore(firstColumnAttributeName)+indexID));

		 //on paste listener
		 var indexID = rowid.split("_")[1];
		 if(uiConstants.common.DEBUG_MODE)console.log($("#"+self.widgetName+' #'+self.widgetName+rowid+'>td>#'+self.widgetName+trimSpacesReplaceUnderscore(firstColumnAttributeName)+indexID));
		
		
		 $("#"+self.widgetName+' #'+self.widgetName+rowid+'>td>#'+self.widgetName+trimSpacesReplaceUnderscore(firstColumnAttributeName)+indexID).on('paste', function(e){
		 	var curObj = this;
		 	showLoadingContainer();

		 	e.stopPropagation();
		    e.preventDefault(); 	    

		    $("#"+self.widgetName+" #btnClearAll").removeClass('disabled');	

			if (e.originalEvent.clipboardData) { 
				var data = (e.originalEvent || e).clipboardData.getData('text/plain');
				var inputId = e.target.id;
			} else if (window.clipboardData) { 
				var data = window.clipboardData.getData('Text');
				var inputId = window.event.srcElement.id;
			}
			//data = data.slice(1, -1);
			
			setTimeout(function () {
				if(uiConstants.common.DEBUG_MODE)console.log(data);

			   	if(data != null){
			   		//clearing old vaule
			   		self.errorStack.removeAll();

			   		var crid = $(curObj).parent().parent().attr('id');
			   		$("#"+self.widgetName+" #"+crid).addClass('empty');
			   		var curRowIndex = $(curObj).parent().parent().index();
					
			   		data.replace(/\n$/, "");
					var rows = data.split("\n");
					var table = $("#"+self.widgetName+" #tableCompInstAddMultiple tbody");
					var curType = "Select";
					var curTZ = "";				
					
					var rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
					
					
					var limitFlag = (rows.length-1) + rowCounter;				
					if(limitFlag <= self.multiRowAddLimit()){
						if(uiConstants.common.DEBUG_MODE)console.log("Total no of rows----------------->"+limitFlag);
						
							var col = rows[0].split("\t");
							
							if(uiConstants.common.DEBUG_MODE)console.log(col.length +"=="+ (self.tableHeaders().length-2));

							if(col.length <= self.tableHeaders().length-2){

								rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
								
								rows.forEach(function (y, yindex) {	

								    var cells = y.split("\t");
								    if(uiConstants.common.DEBUG_MODE)console.log(cells);					    
								    var currentRowCounter = rowCounter + yindex;

								    var lastTabIndex = 0;


									 $('#divInstDet [tabindex]').attr('tabindex', function (a, b) {
									    lastTabIndex = Math.max(lastTabIndex, +b);
									});

								    var tabIndexCounter = lastTabIndex + (currentRowCounter * self.tableHeaders().length);
								    var row = $("<tr class='' id='"+self.widgetName+"row_"+currentRowCounter+"'/>");
								    
								    if(yindex < rows.length-1){					    	
									    if(cells.length != self.tableHeaders().length-2){
									    	self.manageErrorMessage('push',currentRowCounter, uiConstants.common.CELL_VALUES_SEPERATION);
									    }

									    //control creation start
								    	for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
										 	if(self.tableHeaders()[xindex].type != 'delete'){
										 		self.createControls(self.tableHeaders()[xindex].attributeType,self.tableHeaders()[xindex].attributeName,currentRowCounter,row,xindex,self.tableHeaders()[xindex].attributeDefaultValue, tabIndexCounter);
										 	}
										 	tabIndexCounter++;
								    	};					   
									    row.append("<td class='col-xs-1 deleteRow' id='"+self.widgetName+self.tableHeaders()[xindex].attributeType+currentRowCounter+"'><span type='button' title='Delete' class='glyphicon glyphicon-remove buttondelete'></span><span id='"+self.widgetName+'compInst'+currentRowCounter+"' style='display: none;'></span><span id='" + self.widgetName+'hostAddrPort'+rowCounter+"' style='display: none;'></span></td>");
									    
									    //table.append(row);
									    $(".empty").after(row);
									    table.find('.empty').removeClass('empty').next().addClass('empty');

									    self.deleteRowBind(self.widgetName);
										self.bindTableRowListner("row_"+currentRowCounter,self.tableHeaders()[1].attributeName);	

									    var tarr = [currentRowCounter];
									    cells = tarr.concat(cells);
									    if(uiConstants.common.DEBUG_MODE)console.log(cells);

									    //value assign to current row controls :start
									    for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {	
									    	//value assign
									    	self.assignValuesToControls(self.tableHeaders()[xindex].attributeType,self.tableHeaders()[xindex].attributeName,currentRowCounter,self.tableHeaders()[xindex].attributeOptions,self.tableHeaders()[xindex].isMandatory,cells,xindex);
									    }
									    //value assign to current row controls :end
									}
								});
							}else{
								
								showMessageBox(uiConstants.common.COLUMNS_MISMATCH, "error");	
								return false;
							}	

					}else{
						showMessageBox(uiConstants.common.ROW_LIMIT_EXCEEDED, "error");
						self.enableButton();
						//self.onAddClick();
					}

					if(limitFlag <= self.multiRowAddLimit()){
						$("#"+self.widgetName+" #tableCompInstAddMultiple tbody").find('.empty').removeClass('empty');
						$("#"+self.widgetName+" #"+crid).remove();

						if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length == 1){
							$("#tableCompInstAddMultiple tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
						}

						self.chnageRowAndCellId();
						self.validateElementsValue();
						
						if(self.errorStack().length){	
							self.manageErrorMessage('print');
							if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());								
						}
					}
				}
				removeLoadingContainer();

				//}
		 	}, 1);

			
		});
	}

	//enabling buttons based on conditions
	this.enableButton = function(){
		if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length > 0){
			$('#'+self.widgetName+' #btnCompInstMultipleAdd').removeClass('disabled');
			$('#'+self.widgetName+' #btnSave').removeClass('disabled');
		}
	}

	//after adding or pasting dynamic rows in to grid, delete row listner binding.
	this.deleteRowBind = function(deleteRowId){
		$('#'+deleteRowId+' tr').on('click', '.buttondelete', function(e){
			var deleteIndex = e.target.parentNode.parentNode.rowIndex-1;
			if(uiConstants.common.DEBUG_MODE)console.log("========================================Delete row listener binding==================================");
			if(uiConstants.common.DEBUG_MODE)console.log($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length);
		 		
		 	if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length == 0){
	 			self.disableButton();
	 		}else if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length == 1){
	 			return;
	 		}
	 		else
	 		{
	 			var curObj = this;
	 			showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_DELETE_INSTANCE, "question", "confirm", function confirmCallback(confirmDeleteInstance){
					if(confirmDeleteInstance){
						self.deletedCompInstArr.push(parseInt($("#"+self.widgetName + "compInst" + deleteIndex).text()) || 0);
	 					$(curObj).parent().parent().remove();
						self.instanceDeleted([deleteRowId+'delete'+deleteIndex, self.rowIndex ? self.rowIndex() : deleteIndex]);

						if(self.mode == "componentWizard"){
							//var rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
						 	self.noInstancesArr.splice(self.rowIndex(), 1, $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length);
						 	self.instanceDetailArr()[self.rowIndex()].numberInst = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
						}

						//self.noInstancesArr.splice(self.rowIndex(),1, $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length);
				 		//changing each row's Id and childrens id
				 		if(uiConstants.common.DEBUG_MODE)console.log($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length);
				 		self.chnageRowAndCellId();

				 		if($('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length == 1){
							$("#tableCompInstAddMultiple tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
						}
					}
				});
	 		}

			 		
			

			
		});

		
	}
	
	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(){
		self.errorStack.removeAll();
		self.requestDataArray = [];
		var attributeArray = [];
		self.nonGroupkpiProdMapArr = [];
		self.groupkpiProdMapArr = [];
		self.getKPIProducerMappingList();	

		$('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').each(function(i){
			var cobj = {'index': i+1};
			
	        $(this).children('td').each(function(j){
	        	var obj = {};
	        	if((self.tableHeaders()[j].attributeType == 'TextBox' || self.tableHeaders()[j].attributeType == 'Password') && trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName) != 'Tags'){
 					var cellValue = $(this).children('input').val();
 					
	            	if(self.tableHeaders()[j].isMandatory && cellValue == ""){
		    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" value is required");
		    		}
		    		else if(cellValue && self.tableHeaders()[j].attributeMinLength && cellValue.length < parseInt(self.tableHeaders()[j].attributeMinLength)){
						self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[j].attributeName+" should be of minimum " + self.tableHeaders()[j].attributeMinLength + " characters length");	
					}
					else if(cellValue && self.tableHeaders()[j].attributeMaxLength && cellValue.length > parseInt(self.tableHeaders()[j].attributeMaxLength)){
						self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[j].attributeName+" should be of maximum " + self.tableHeaders()[j].attributeMaxLength + " characters length");	
					}
					else if(cellValue && self.tableHeaders()[j].attributeRegEx && cellValue != (cellValue.match(self.tableHeaders()[j].attributeRegEx) ? cellValue.match(self.tableHeaders()[j].attributeRegEx)[0] : null)){
						self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[j].attributeName+" does not match the Regex pattern " + self.tableHeaders()[j].attributeRegEx);	
					}


	    			/*else if(isNaN(parseInt(cellValue))){// if string
						if((cellValue != "" && self.tableHeaders()[j].attributeMinLength != "") && cellValue.length  < self.tableHeaders()[j].attributeMinLength){
							self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be less than minimun character length "+ self.tableHeaders()[j].attributeMinLength);	
			    		}
			    		else if((cellValue != "" && self.tableHeaders()[j].attributeMaxLength != "") && cellValue.length > self.tableHeaders()[j].attributeMaxLength){ 
			    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be greater than maximum character length "+ self.tableHeaders()[j].attributeMaxLength);
			    		}
			    		
					}
					else if(!isNaN(parseInt(cellValue))){// if number
						if((cellValue != "" && self.tableHeaders()[j].attributeMinLength != "") && parseInt(cellValue)  < self.tableHeaders()[j].attributeMinLength){
			    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be less than minimun value "+ self.tableHeaders()[j].attributeMinLength);
			    		}
			    		else if((cellValue != "" && self.tableHeaders()[j].attributeMaxLength != "") && parseInt(cellValue) > self.tableHeaders()[j].attributeMaxLength){ 
			    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be greater than maximum value "+ self.tableHeaders()[j].attributeMaxLength);	
			    		}
			    		
					}*/
		    		/*else if(cellValue != "" && cellValue  < self.tableHeaders()[j].attributeMinLength){
		    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be less than minimun length "+ self.tableHeaders()[j].attributeMinLength);
		    		}
		    		else if(cellValue != "" && cellValue > self.tableHeaders()[j].attributeMaxLength){ 
		    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[j].attributeName+" should not be greater than maximum length "+ self.tableHeaders()[j].attributeMaxLength);
		    		}*/
		    		/*else if(cellValue != "" && self.tableHeaders()[j].attributeRegEx != undefined && cellValue.match(self.tableHeaders()[j].attributeRegEx) == null){
		    			self.manageErrorMessage('push',cobj.index-1,"Invalid "+ self.tableHeaders()[j].attributeName+" value");
		    		}*/
	            	if(self.tableHeaders()[j].attributeName == "Name" && j == 1){
		    			cobj['componentInstanceName'] = trimSpacesReplaceSingleBlankspace($(this).children('input').val());
		    		}
		    		/*else if(self.tableHeaders()[j].attributeName == "Description" && j == 2){
		    			cobj['description'] = $(this).children('input').val().trim();
		    		}*/
		    		else{
	            		obj['attributeId'] = self.tableHeaders()[j].attributeId;
	            		obj['attributeValue'] = isNaN($(this).children('input').val()) ? $(this).children('input').val() : $(this).children('input').val();
	            		obj['attributeName'] = self.tableHeaders()[j]['attributeName'];
	            		attributeArray.push(obj);  
	            	}

 				}else if(self.tableHeaders()[j].attributeType == 'DropDown'){	  
 					var aTypeLen = self.tableHeaders()[j].attributeOptions.length;       	 
	            	 if(self.tableHeaders()[j].isMandatory == 1 && $(this).children('select').val() == ""){
	            	 	self.manageErrorMessage('push',cobj.index-1, "Please select value for "+self.tableHeaders()[j]['attributeName']);
	            	 }else{
	            	 	obj['attributeId'] = self.tableHeaders()[j].attributeId;
	            		obj['attributeValue'] = isNaN($(this).children('select').val()) ? $(this).children('select').val() : $(this).children('select').val();
	            	 	obj['attributeName'] = self.tableHeaders()[j]['attributeName'];
	            	 	attributeArray.push(obj);
	            	 }
 				}else if(self.tableHeaders()[j].attributeType == 'CheckBox'){	 
            	 	obj['attributeId'] = self.tableHeaders()[j].attributeId;
            		obj['attributeValue'] =parseInt($(this).children('input').prop('checked') == true?1:0);
            	 	obj['attributeName'] = self.tableHeaders()[j]['attributeName'];
            	 	attributeArray.push(obj);
 				}
 				else if(self.tableHeaders()[j].attributeType == 'TextBox' && self.tableHeaders()[j].attributeName == 'Tags'){
	            	var ctag = $(this).children('input').val();
	            	if(ctag != ""){
			 			if(ctag.trim().endsWith(","))
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
			 			else if(tagValidationWithComma(ctag) == 0)
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		    		}
	            	var taglist = $(this).children('input').val();
	            	if(taglist != ""){
	            		taglist = taglist.split(",");
	            		var tagObj = [];
	            		for (var i = 0; i < taglist.length; i++) {
	            			if($.grep(tagObj, function(evt){ return evt.tagName == taglist[i].trim(); }).length>0){
		    					self.manageErrorMessage('push',cobj.index-1, uiConstants.common.DUPLICATE_TAGS);
		    					break;
	            			}
	            			tagObj.push({"tagId":null, "tagName":taglist[i].trim(), "tagOperation":"add"});
	            		};
	            	}
	            	cobj['tags'] = tagObj;
	           }	
	           else if(self.tableHeaders()[j].attributeType == 'SuggesterBox'  && self.mode != "componentWizard"){
	           		 var aTypeLen = $(this).children('select').val().length;	            	 
	            	 if(aTypeLen > 0){
	            	 	cobj['applicationIds'] = [parseInt($(this).children('select').val())];
	            	 }else{
	            	 	self.manageErrorMessage('push',cobj.index-1, uiConstants.componentInstanceConfig.APPLICATION_NAME_SELECTION_REQUIED);
	            	 }
	           }	
	        }); 	 


	        //self.getKPIProducerMappingList();	
	        
				cobj['componentTypeId'] =  parseInt($('#'+self.widgetName+' #compTypeList').val());
				cobj['componentId'] =  parseInt($('#'+self.widgetName+' #compNamesList').val());
				cobj['componentVersionId'] =  parseInt($('#'+self.widgetName+' #compVersionsList').val());
				cobj['clusterIds'] = [];
				cobj['monitor'] = 0;
				//cobj['kpiDetails'] = self.nonGroupkpiProdMapArr;
				//cobj['kpiGroups'] = [];
				cobj['status'] = 1;
				cobj['attributes'] = attributeArray;
				
				self.requestDataArray.push(cobj);
				attributeArray = [];
	    });
	}

	this.getKPIProducerMappingList = function(){
		for(kpi in self.kpiProducerMapArr()){
			if(self.kpiProducerMapArr()[kpi].kpiId){
				self.nonGroupkpiProdMapArr.push({
			        "kpiId": parseInt(self.kpiProducerMapArr()[kpi].kpiId),
			        "producerId": parseInt(self.kpiProducerMapArr()[kpi].producers.defaultId),
			        "collectionInterval": parseInt(self.kpiProducerMapArr()[kpi].collectionInterval),
			        "kpiProducerMappingId": $.grep(self.kpiProducerMapArr()[kpi].producers.all, function(e){return e.producerId == self.kpiProducerMapArr()[kpi].producers.defaultId; })[0].kpiProducerMappingId,
	        		"status": parseInt(self.kpiProducerMapArr()[kpi].status)
			    });
			}
		}

		for(kpi in self.kpiGroupProducerMapArr()){
			if(self.kpiGroupProducerMapArr()[kpi].kpiGroupId){
				self.groupkpiProdMapArr.push({
			        "kpiGroupId": parseInt(self.kpiGroupProducerMapArr()[kpi].kpiGroupId),
			        "kpiGroupValues": [],
			        "producerId": parseInt(self.kpiGroupProducerMapArr()[kpi].producers.defaultId),
			        "collectionInterval": parseInt(self.kpiGroupProducerMapArr()[kpi].collectionInterval),
			        "kpiProducerMappingId": $.grep(self.kpiGroupProducerMapArr()[kpi].producers.all, function(e){return e.producerId == self.kpiGroupProducerMapArr()[kpi].producers.defaultId; })[0].kpiProducerMappingId,
	        		"status": parseInt(self.kpiGroupProducerMapArr()[kpi].status)
			    });
			}
		}
	}
	//managing all validation error message at pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount){		
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			//if(addRecordCount>1)
				var messageStr = addRecordCount+" "+uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCE+" and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			//else
			//	var messageStr = addRecordCount+" application is successfully added and "+unsavecnt +" have failed. \n";

				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						messageStr += "Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";
			self.errorStack().forEach(function(item,index){
			if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				/*if(item.rowno == -1){					
					messageStr += item.message +"\n";					
					self.enableButton();					
					if(DEBUG_MODE)console.log("count not match while copy");
				}else */
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					messageStr += "Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}

	//Changing row and col elements ID's when any delete or append/prepend operation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	var rowCnt = 0;
    	$('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').each(function(i){
	        $(this).children('td').each(function(j){

	        	if(self.tableHeaders()[j].attributeType == 'label'){
	        		$(this).children('label').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	            	$(this).children('label').text(rowCnt+1);
	        	}else if(self.tableHeaders()[j].attributeType == 'TextBox' || self.tableHeaders()[j].attributeType == 'Password'){
	        		$(this).children('input').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	        	}else if(self.tableHeaders()[j].attributeType == 'DropDown'){
	        		$(this).children('select').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeName)+rowCnt);
	        	}else if(self.tableHeaders()[j].attributeType == 'delete'){
 					$(this).attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[j].attributeType)+rowCnt);
 					//$(this).children().eq(2).attr('id',self.widgetName+"compInst"+rowCnt);
 					$(this).find("span:eq(1)").attr('id',self.widgetName+"compInst"+rowCnt);
 					$(this).find("span:eq(2)").attr('id',self.widgetName+"hostAddrPort"+rowCnt);
 				}
	        });
	        $(this).attr('id',self.widgetName+'row_'+rowCnt);
	        rowCnt++;
	    });

    }

    //clearing all rows with confirmation of user's. 
	this.onClearAll = function(){
		var rowCounter = $('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').length;
		if(rowCounter > 1){		
			showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS, "question", "confirm", function confirmCallback(r){
			    if (r == true) {
			        $('#'+self.widgetName+' #tableCompInstAddMultiple tbody').empty();
					self.onAddClick();
					 $('#'+self.widgetName+' #btnClearAll').addClass('disabled');	
			    }
			});
		}
		else if(rowCounter == 1 ){			
			showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS_CONTENT, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#'+self.widgetName+' #tableCompInstAddMultiple tbody').empty();
					self.onAddClick();
					 $('#'+self.widgetName+' #btnClearAll').addClass('disabled');	
					
			    }
			});
		}		
	}

	//disabling buttons based on conditions
	this.disableButton = function(){
		if($('#'+self.widgetName+' #tableMultipleAdd tbody tr').length == 0){
			$('#'+self.widgetName+' #btnSave').addClass('disabled');
			$('#'+self.widgetName+' #btnClearAll').addClass('disabled');			
		}
	}

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
		self.requestDataArray = [];
		//for(var i=0;i<3;i++){
    	self.validateElementsValue();

		if(self.errorStack().length){	
			self.manageErrorMessage('print');
		}else{
			self.reqRecordCounter(self.requestDataArray.length);
			var finalObj = {'componentInstances':self.requestDataArray,'hostDetails':[], 'kpiDetails': self.nonGroupkpiProdMapArr, 'kpiGroups': self.groupkpiProdMapArr, 'isAddMultiple': 1};
			if(uiConstants.common.DEBUG_MODE)console.log("==================== Add Multiple ComponentInstance===================");
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
			var url = uiConstants.common.SERVER_IP+"/componentInstance";
			requestCall(url, 'POST', JSON.stringify(finalObj), 'addComponentInstances', successCallback, errorCallback);	
		}
    }	

    //Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
    	var succnt = 0;
    	var failcnt = 0;
    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
    	if(self.reqRecordCounter() == res.length){
    		for (var i = 0; i < res.length; i++) {
    			//if(DEBUG_MODE)console.log(res[i]);
    			//if(DEBUG_MODE)console.log(res[i].index);
    			//var rid = res[i].index-1;
    			var rid = parseInt(res[i].objectId)-1;
    			if(res[i].responseStatus == uiConstants.common.CONST_SUCCESS){
    				$('#'+self.widgetName+' #tableCompInstAddMultiple tbody #'+self.widgetName+'row_'+rid).remove();
    				succnt++;	
    			}else{
    				self.manageErrorMessage('push',failcnt, res[i].message);
    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+handleServiceErrorMsgs(uiConstants.common.CONST_COMPINST,res[i].errorCode,res[i].message));
    				failcnt++;
    			}
    		};

    		//changing row index and number
    		self.manageErrorMessage('print',"","",succnt);
    		self.chnageRowAndCellId();
    		
    	}else{
    		
    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
    	}
    }


	function successCallback(data, reqType) {
		if(reqType === "getComponentAttributes"){
			var res = data.result;
			if(data.responseStatus == "failure"){
					if(res != undefined && res[0] != undefined)
						showMessageBox(res[0].message, "error");
					else
						showMessageBox(ERROR_GET_COMP_INSTANCES_ATTRIBUTES, "error");
			}
			else{
					self.compAttributesArr(data.result);
					//add item at first of observableArray

					if(self.mode != "componentWizard"){
						self.compAttributesArr.unshift( {
												      "attributeId": 0,
												      "attributeName": "Application",
												      "attributeType": "SuggesterBox",
												      "isMandatory": 1,
												      "isCustom": 0
												    });
					}
					/*self.compAttributesArr.unshift( {
											      "attributeId": 0,
											      "attributeName": "Description",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    });*/
					self.compAttributesArr.unshift( {
											      "attributeId": 0,
											      "attributeName": "Name",
											      "attributeType": "TextBox",
											      "isMandatory": 1,
											      "isCustom": 0
											    });
					self.compAttributesArr.unshift( {
											      "attributeId": 0,
											      "attributeName": "Sl.No.",
											      "attributeType": "label",
											      "isMandatory": 0,
											      "isCustom": 0
											    });

					//add item at last of observableArray
					self.compAttributesArr.push( {
											      "attributeId": self.compAttributesArr().length,
											      "attributeName": "Tags",
											      "attributeType": "TextBox",
											      "isMandatory": 0,
											      "isCustom": 0
											    });

					//add item at last of observableArray
					self.compAttributesArr.push( {
											      "attributeId": self.compAttributesArr().length+1,
											      "attributeName": "",
											      "attributeType": "delete",
											      "isMandatory": 0,
											      "isCustom": 0
											    });
					self.tableHeaders(self.compAttributesArr());
					self.onVersionListChange();

			}
		}
		else if(reqType === "addComponentInstances"){
			if(uiConstants.common.DEBUG_MODE){
				if(uiConstants.common.DEBUG_MODE)console.log("=======================Add ComponentInstance Result Handler =====================================");
				if(uiConstants.common.DEBUG_MODE)console.log(data);
			}
			var res = data.result;
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCES, "error");
			}
			else{
				showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCE);
				$('#'+self.widgetName+' #tableCompInstAddMultiple tbody').empty();
				self.cancelAddScreen();
			}
		}
		else if(reqType === "getKpiProducerMapping"){
			if(data.result.kpiDetails){
				self.kpiProducerMapArr($.grep(data.result.kpiDetails, function(e){ return  e.producers.defaultId!=undefined; }));
				self.kpiGroupProducerMapArr($.grep(data.result.kpiGroups, function(e){ return  e.producers.defaultId!=undefined; }));
			}
		}
	}

	function errorCallback(reqType) {
		if(reqType === "getComponentAttributes"){
			showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_LIST, "error");
		}else if(reqType === "addComponentInstances"){
			showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCES, "error");
		}
	}
}

Addmultiplecomponentinstancegeneral.prototype.dispose = function() { };
return { viewModel: Addmultiplecomponentinstancegeneral, template: templateMarkup };

});
