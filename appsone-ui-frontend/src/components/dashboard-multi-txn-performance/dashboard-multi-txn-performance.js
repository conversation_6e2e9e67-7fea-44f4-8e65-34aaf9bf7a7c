define(['jquery','knockout','bootstrap','bootstrap-switch','d3','c3','fusionCharts','text!./dashboard-multi-txn-performance.html','hasher'], function($,ko,bootstrap,bs,d3,c3,fusioncharts,templateMarkup,hasher) {

  function MutliTxnPerformance(params) {
    this.message = ko.observable('Hello from the '+params.podTitle+' component!'); 
    var self = this;
    this.isModal = ko.observable(params.isModal);
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.currentMode = ko.observable("DC");
    this.currentView = ko.observable("Graph");
    this.isModal = ko.observable(params.isModal);
    this.isGrpahView = ko.observable(true);

    /*POD Grpah related observables*/
    this.MTPPodId = ko.observable(params.podId+'_performance-chart');
    this.ModalMTPPodId = ko.observable(params.podId+'_Modal__performance-chart');
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.chartPaletteColors = ko.observable("#0075c2,#1aaf5d,#ff4000,#0000ff,#524e4e"); //RGB color code seprated by comma string
    this.timeWindow = ko.observableArray(["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"]);
    this.transactionVolume = ko.observableArray([
                        ["Txn1",15,11,36,87,97,4,7,28,43,22,11,41,3],
                        ["Txn2",5,4,7,28,41,4,7,28,13,22,11,41,3],
                        ["Txn3",52,1,6,87,42,7,28,73,43,22,11,41,3],
                        ["Txn4",11,12,69,81,97,4,7,28,4,7,2,43,3],
                        ["Txn5",45,19,39,7,97,4,7,28,49,4,7,8,43]
                  ]);
    this.DCresponseTime = ko.observableArray([
                        ["Txn1",15,11,36,87,97,4,7,28,43,22,11,41,3],
                        ["Txn2",5,4,7,28,41,4,7,28,13,22,11,41,3],
                        ["Txn3",52,1,6,87,42,7,28,73,43,22,11,41,3],
                        ["Txn4",11,12,69,81,97,4,7,28,4,7,2,43,3],
                        ["Txn5",45,19,39,7,97,4,7,28,49,4,7,8,43]
                  ]);
    this.EUMresponseTime = ko.observableArray([
                        ["Txn1",15,11,36,87,97,4,7,28,43,22,11,41,3],
                        ["Txn2",5,4,7,28,41,4,7,28,13,22,11,41,3],
                        ["Txn3",52,1,6,87,42,7,28,73,43,22,11,41,3],
                        ["Txn4",11,12,69,81,97,4,7,28,4,7,2,43,3],
                        ["Txn5",45,19,39,7,97,4,7,28,49,4,7,8,43]
                  ]);

    this.txnPerfGridHeaders = ko.observableArray([
        {'columnTitle':'time', 'displayName':'Time' ,'class': 'col-xs-2 textOverflowOmmiter'},
        {'columnTitle':'name', 'displayName':'Name' ,'class': 'col-xs-2 textOverflowOmmiter'},
        {'columnTitle':'volume', 'displayName':'Volume', 'class': 'col-xs-2 textOverflowOmmiter'},
        {'columnTitle':'dcResTime', 'displayName':'DC Response Time(ms)' ,'class': 'col-xs-3 textOverflowOmmiter'},
        {'columnTitle':'eumResTime', 'displayName':'EUM Response Time(ms)' ,'class': 'col-xs-3 textOverflowOmmiter'},     
      
    ]);

    this.txnPerfGridData = ko.observableArray([
    	{'time':'10:00', 'name':'Txn-1', 'volume':'23', 'dcResTime':'22', 'eumResTime':'20'},
    	{'time':'10:00', 'name':'Txn-2', 'volume':'23', 'dcResTime':'22', 'eumResTime':'20'},
    	{'time':'10:00', 'name':'Txn-3', 'volume':'23', 'dcResTime':'22', 'eumResTime':'20'},
    	{'time':'10:00', 'name':'Txn-4', 'volume':'23', 'dcResTime':'22', 'eumResTime':'20'},
    	{'time':'10:00', 'name':'Txn-5', 'volume':'23', 'dcResTime':'22', 'eumResTime':'20'},
      
    ]);


    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podType);

    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);

        self.initChart(self.MTPPodId(),self.currentPodBodyHeight()-5,self.currentPodBodyWidth()-5,"Time","Volume",self.chartPaletteColors(),self.timeWindow(),self.transactionVolume());
        self.bindExportCSVListner("#"+self.podId()+"_exportCSV" );
        
    }

    this.bindExportCSVListner = function(iconId){
        /*Export as csv button listner*/
        $(iconId).on( "click", function() {
          if(uiConstants.common.DEBUG_MODE) console.log(self.podId()+"_exportCSV");
          JSONToCSVConvertor(self.graphDataSet(),self.podTitle(), "Appsone_", true); //(dataset, reporrtTitle, FileName+ReportTilte, Labelflag)
        });
    }

     this.modalRenderHandler = function(){
        self.currentPodBodyHeight($('body').height()*0.80);
        self.currentPodBodyWidth($('body').width()*0.85);
        if(uiConstants.common.DEBUG_MODE) console.log(self.currentPodBodyHeight());        
        self.initChart(self.ModalMTPPodId(),self.currentPodBodyHeight()-30,self.currentPodBodyWidth()-5,"Time","Volume",self.chartPaletteColors(),self.timeWindow(),self.transactionVolume());
        self.bindExportCSVListner("#"+self.podId()+"_Modal_exportCSV" );
        

        self.bindGridGraphSwicthListner();   
    }

    this.bindGridGraphSwicthListner = function(){
        //intilize switch.
        $("#expandGridGraphFilter_pod_"+self.podId()).bootstrapSwitch('state', true);
        

      



        //Switch listner for change the view b/w Graph and Grid
        $("#expandGridGraphFilter_pod_"+self.podId()).on('switchChange.bootstrapSwitch', function (event, state) {
            if(uiConstants.common.DEBUG_MODE) console.log(event);
            if(uiConstants.common.DEBUG_MODE) console.log(this.id+"----->"+state);
            if(state){ console.log("Graph View");}
            else{ console.log("Grid View");}
            self.isGrpahView(state);
        });
    }

    this.initChart = function(chartCont, chartHeight, chartWidth,xAxisLabel,yAxisLabel,chartPaletteColors,timeWindowSet,transactionVolume){
        /*Parameters:-
            chartCont :- container Id where Chart will render
            chartHeight :- chart canvas height
            chartWidth :- chart canvas width
            xAxisLabel  :- chart X Axis Label
            yAxisLabel :- chart Y AXis Label
            chartPaletteColors :- chart different line color codes(hex) within string seprated by comma(,)
            timeWindowSet :- chart x AXis Data set Array(for Ex - Time window)
            transactionVolume :- chart actual entity data set(Array of object)
        */
            
        var chart = c3.generate({
            bindto: document.getElementById(chartCont),
            size: {
              width: parseInt(chartWidth),
              height: parseInt(chartHeight),
            },
            grid: {
              focus: {
                show: false
              }
            },
            area: {
              zerobased: true
            },
            point: {
              show: true
            }, 
            data: {
                  columns: transactionVolume,
                  //groups: [['Txn1','Txn2','Txn3','Txn4','Txn5']],
                  type: 'spline', // 'line', 'spline', 'step', 'area', 'area-step' are also available to stack
                  colors: {
                      'Txn1': 'rgb(31, 119, 180)',
                      'Txn2': 'rgb(255, 127, 14)',
                      'Txn3': 'rgb(44, 160, 44)',
                      'Txn3': 'rgb(148, 103, 189)',
                      'Txn5':'rgb(140, 86, 75)',                      
                  },

              },
              zoom:{
                enabled: false,
              },
              padding: {
                //bottom: 40,
                //top: 5,
                //right: 0,
                //left : 0,

              },
            legend: {
                  show: true
              },
              axis: {
                rotated: false,
                y: {
                  show:true,
                  type: 'category',
                  label: {
                    text: yAxisLabel,
                    position: 'inner-top',   // inner-top,inner-middle,inner-bottom,outer-top,outer-middle,outer-bottom

                  },
                  //min: 0,
                  //max: Math.max.apply(Math,tickYValues),
                  tick: {
                      //min: 0,
                      //max: 5,
                      //format: d3.format('d'),
                      //values: [tickYValues],
                      /*format: function(d){
                           if(d%2==0) return d;
                      },*/
                    },

                },
                x: {
                  show:true,
                  type: 'category',
                  categories: timeWindowSet,
                  tick: {
                  rotate: -90,
                  multiline: false,
                        culling: {
                                //max: 7,// the number of tick texts will be adjusted to less than this value
                                //count: 7,

                            }
                          // for normal axis, default on
                          // for category axis, default off
                      },
                  label: {
                    text: xAxisLabel,
                    position: 'inner-center',// inner-right,inner-center,inner-left,outer-right,outer-center,outer-left
                  },
                  height:50,
                },
              },
              tooltip: {
                  grouped:true,

                  contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
                      var $$ = this, config = $$.config,
                          titleFormat = config.tooltip_format_title || defaultTitleFormat,
                          nameFormat = config.tooltip_format_name || function (name) { return name; },
                          valueFormat = config.tooltip_format_value || defaultValueFormat,
                          text, i, title, value, name, bgcolor;
                      for (i = 0; i < d.length; i++) {
                          if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

                          if (! text) {
                              title = titleFormat ? titleFormat(d[i].x) : d[i].x;
                              text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='4'>Time - " + title + "</th></tr>" : "");
                          }

                          name = nameFormat(d[i].name);
                          value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
                          bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);


                        if(i == 0){
                              text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                              text += "<td class='name'><b>Name</b> &nbsp&nbsp</td>";
                              text += "<td class='value'><b>Volume</b></td>";
                              text += "<td class='value'><b>DC ResTime(ms)</b></td>";
                              text += "<td class='value'><b>EUM ResTime(ms)</b></td>";
                              text += "</tr>";
                         }
                          text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
                          text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "&nbsp&nbsp</td>";
                          text += "<td class='value'>" + value + "</td>";
                          text += "<td class='value'>" + self.DCresponseTime()[i][d[i].index+1] + "</td>";
                          text += "<td class='value'>" + self.EUMresponseTime()[i][d[i].index+1] + "</td>";
                          text += "</tr>";
                        

                      }
                      return text + "</table>";
                  }


              }
        });
    }
  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  MutliTxnPerformance.prototype.dispose = function() { };
  
  return { viewModel: MutliTxnPerformance, template: templateMarkup };

});