<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divKpiAddEdit">
	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>

			<div id="divKpiName" class="form-group form-required" >
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: kpiName,attr:{'title':uiConstants.kpiConfig.KPI_MULTIPLE_NAME_LENGTH_ERROR}" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
				</div>
			</div>

			<div id="divKpiDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description<span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: kpiDescription,attr:{'title':uiConstants.kpiConfig.KPI_DESC_INFO}" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<!--ko if: currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && currentViewIndex() != uiConstants.common.CLONE_VIEW -->
			<div id="divKpiAliasName" class="form-group form-required"  >
				<label class="control-label col-sm-2">Alias Name</label>
				<div class="col-sm-4">
					<div class="col-sm-4">
						<span data-bind="text: kpiAliasName, style: {fontWeight:'bold'}"></span>
					</div>
				</div>
			</div>
			<!--/ko -->
			
			<div class="form-group" id="divKpiType">
				<label class="control-label col-sm-2">Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="kpiTypeList" data-bind="options: kpiTypeArr(),  optionsText: 'kpiType', optionsValue: 'kpiTypeId', optionsCaption: 'Select', event: {change: getKpiGroups}" required="true" >
					</select>
				</div>
			</div>

			
 			<div class="form-group" id="divKpiDataType" data-bind="visible: showDataTypeField">
			   <label class="control-label col-sm-2" >Data Type <span class="mandatoryField">*</span></label>
			   <div class="col-sm-4">
				   <select class="chosen form-control" id="kpiDataType" data-bind="options: kpiDataTypeArr(),  optionsText: 'dataTypeName', optionsValue: 'kpiDataTypeId', optionsCaption: 'Select', attr:{disabled: showDataType}">
					</select>
			   </div>
			</div>
			
			
			<div id="divKpiUnits" class="form-group" data-bind="visible: showFields" >
			   <label class="control-label col-sm-2" >KPI Units <span class="mandatoryField">*</span></label>
			   <div class="col-sm-4">
				   	<select class="chosen form-control" id="kpiUnitsList" data-bind="options: kpiUnitsArr,  optionsText: 'kpiUnitName', optionsValue: 'kpiUnitId', optionsCaption: 'Select'"> 		
					</select>
			   </div>
			</div>

			<div id="divClusterOpern" class="form-group" data-bind="visible: showFields">
			   <label class="control-label col-sm-2" >Cluster Operation <span class="mandatoryField">*</span></label>
			   <div class="col-sm-4">
				   	<select class="chosen form-control" id="kpiClusterOperation" data-bind="options: kpiClusterOpernArr(),  optionsText: 'clusterOperationName', optionsValue: 'clusterOperationId', optionsCaption: 'Select'">
					</select>
			   </div>
			</div>

			<div id="divKpiGroup" class="form-group">
			   <label class="control-label col-sm-2" >KPI Group</label>
			   <div class="col-sm-4">
				   	<select class="chosen form-control" id="kpiGroupList"  data-bind="options: kpiGroupArr,  optionsText: 'name', optionsValue: 'id', optionsCaption: 'Select'">  
					</select>
			   </div>
			</div>

			<div id="tagsSingleApp" class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="kpi-tokenfield-typeahead" data-bind="value: tags" >
				</div>
			</div>

			<div id="divAppStatus" class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="kpiStatus" data-bind="checked: kpiStatus"> -->
					<input type="checkbox" id="kpiStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="kpiStatus">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5, event:{click: addEditKpi}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelAddScreen}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>
