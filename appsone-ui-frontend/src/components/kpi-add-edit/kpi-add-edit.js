define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead', 'text!./kpi-add-edit.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon) {

	function KpiAddEdit(params){

		/*1:ADD,3:EDIT,4:CLONE,5:VIEW*/
		var self = this;
		var commonTagsArr = [];
		var tagsToDeleteArr = [];

		var tagToEdit = "";
		var tagLabel = "";
		var tagEditedOldName = [];
		var tagEditedNewName = [];

		var tagsNewToAddArr = [];
		var appTagLoaded = 0;
		var tagsNameArr = [];
		var previousKpiTypeId;
		var previousKpiGroupId;
		this.currentViewIndex = ko.observable(params.currentViewIndex());
		this.selectedConfigRows = ko.observableArray(params.selectedConfigRows());
		this.kpiTypeArr = ko.observableArray(params.kpiTypeArr());
		this.kpiDataTypeArr = ko.observableArray();//need to get in params
		this.kpiClusterOpernArr = ko.observableArray(params.kpiClusterOpernArr());
		this.kpiUnitsArr = ko.observableArray([]);//params.kpiUnitsArr()
		this.appTagArr = ko.observableArray();
		//this.appTagAutoCompleteArr = ko.observableArray();//need to chk and remove
		this.configTagAutoCompleteArr = ko.observableArray();//this need to be used

		this.copyTagsArr = ko.observableArray();

		this.kpiName = ko.observable();
		this.kpiAliasName = ko.observable();
		this.kpiDescription = ko.observable();
		this.tags = ko.observable();
		this.commonTags = ko.observable();

		this.showMultipleKpiNames = ko.observable("");

		this.isCustom = ko.observable(0);
		this.kpiId = ko.observable(0);
		this.kpiStatus = ko.observable(true);
		this.showDataType = ko.observable(true);
		//this.selectedType = ko.observable("");
		this.isMultiEdit = ko.observable(true);

		this.pageSelected = params.pageSelected;

		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.showFields = ko.observable(false);
  		this.showDataTypeField = ko.observable(true);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.kpiGroupArr = ko.observableArray();

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#kpiTypeList").trigger('chosen:updated');
			$("#kpiClusterOperation").trigger('chosen:updated');

			$("#kpiStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#kpiStatus').bootstrapSwitch('state', self.kpiStatus()); 

			$("#kpiStatus").on('switchChange.bootstrapSwitch', function () {
				self.kpiStatus($('#kpiStatus').bootstrapSwitch('state')?1:0);
			});

			$("#txtName").focus();

			commonTagsArr = [];
			
			newCommonTagsArr = [];
			tagsToDeleteArr = [];

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["kpiName"]));
			}

			//on change event of kpi type 
			$('#kpiTypeList').on('chosen:showing_dropdown', function () {
				previousKpiTypeId = this.value;
				previousKpiGroupId = $("#kpiGroupList").val();
				console.log(previousKpiTypeId);
				console.log($("#kpiGroupList").val());
			}).on('change', function(){
			    //$("#kpiTypeList").blur();

			    debugger;

			    if(previousKpiTypeId == "" || previousKpiGroupId == ""){
		    		self.getKpiDataTypelist();
					self.showDataType($('#kpiTypeList').val() != ""?false:true);
					$("#kpiDataType").trigger('chosen:updated');
					$("#kpiGroupList").val("").trigger('chosen:updated');
					self.displayFieldsIfCore($('#kpiTypeList option:selected').text());
					self.showDataTypeField(slugify($('#kpiTypeList option:selected').text()) != "availability" && slugify($('#kpiTypeList option:selected').text()) != "filewatch");
		    	}
		    	else{
		    		showMessageBox(uiConstants.kpiConfig.CONFIRM_KPI_TYPE_CLEAR, "question", "confirm", function confirmCallback(confirmClear){
						if (confirmClear){
							self.getKpiDataTypelist();
							self.showDataType($('#kpiTypeList').val() != ""?false:true);
							$("#kpiDataType").trigger('chosen:updated');
							$("#kpiGroupList").val("").trigger('chosen:updated');
							self.displayFieldsIfCore($('#kpiTypeList option:selected').text());
							self.showDataTypeField(slugify($('#kpiTypeList option:selected').text()) != "availability" && slugify($('#kpiTypeList option:selected').text()) != "filewatch");
			    		}
			    		else{
			    			$("#kpiTypeList").val(previousKpiTypeId).trigger('chosen:updated');
			    			self.getKpiGroups();
							$("#kpiGroupList").val(previousKpiGroupId).trigger('chosen:updated');
			    		}
			    	});
		    	}

				if(slugify($('#kpiTypeList option:selected').text()) == "availability" || slugify($('#kpiTypeList option:selected').text()) == "filewatch"){
					$("#kpiDataType").val(self.kpiDataTypeArr()[0].kpiDataTypeId).trigger('chosen:updated');
				}			
			});
			
			self.displayTextTypeCases();
		
			//Token related events :Start
			$('#kpi-tokenfield-typeahead')
				// modified tags changes

				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#kpi-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})


				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#kpi-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});
			//

			//Token related events :End

			requestCall(uiConstants.common.SERVER_IP + "/tag?type=KPI", "GET", "", "getKpiTagList", successCallback, errorCallback);
		}

		this.getKpiGroups = function(){
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				var selGroupsArr = [];
				if(self.selectedConfigRows().length > 1){
					for(var selObj in self.selectedConfigRows()){
						if(selGroupsArr.indexOf(self.selectedConfigRows()[selObj].kpiGroupId) == -1){
							selGroupsArr.push(self.selectedConfigRows()[selObj].kpiGroupId);
						}
					}

					if(selGroupsArr.length == self.selectedConfigRows().length){
						self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", [self.selectedConfigRows()[0].kpiGroupId], true), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
					}
					else{
						self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", null, false), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
					}
				}
				else{
					self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", [self.selectedConfigRows()[0].kpiGroupId], true), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
				}
			}
			else{
				self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", null, false), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
			}

			$("#kpiGroupList").trigger('chosen:updated');
		}

		this.displayTextTypeCases=function(){
			$('#kpiDataType').on('change',function(){
				debugger;
				self.setKpiUnits($('#kpiDataType').val());
				if($('#kpiDataType option:selected').text() === uiConstants.common.CONST_TEXT && $('#kpiTypeList option:selected').text() != uiConstants.common.CONST_CORE){
					$('#kpiClusterOperation option').filter(function () { return $(this).html() === uiConstants.common.CONST_NONE;}).prop('selected', true).trigger('chosen:updated');
				}
			});

			$('#kpiUnitsList').on('change',function(){
				if($('#kpiDataType option:selected').text() == uiConstants.common.CONST_TEXT && $('#kpiUnitsList option:selected').text() === uiConstants.common.CONST_TEXT){
					$('#kpiClusterOperation option').filter(function () { return $(this).html() === uiConstants.common.CONST_NONE;}).prop('selected', true);
					$('#kpiClusterOperation').prop('disabled',true).trigger('chosen:updated');
				}
				else{
					$('#kpiClusterOperation').val("");
					$('#kpiClusterOperation').prop('disabled',false).trigger('chosen:updated');
				}
			});
		}

		this.setKpiUnits= function(dataTypeId){
			self.kpiUnitsArr([]);
	    	for(indx in self.kpiDataTypeArr()){
	    		if(self.kpiDataTypeArr()[indx].kpiDataTypeId == dataTypeId){
	    			if(self.kpiDataTypeArr()[indx].kpiUnits.length !=0){
		    			self.kpiUnitsArr(self.kpiDataTypeArr()[indx].kpiUnits);
		    		}
	    			break;
	    		}
			}

			$("#kpiUnitsList").trigger('chosen:updated');
		}

		this.displayFieldsIfCore = function(selectedType){
			self.showFields(selectedType === uiConstants.common.CONST_CORE?true:false);
		}

		/*Filter search Change events : START*/
		this.getKpiDataTypelist = function(){
			if($('#kpiTypeList').val() != ""){
				var dataTypeObj = $.grep(self.kpiTypeArr(), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); });		
				self.kpiDataTypeArr(dataTypeObj[0].dataType);

				$("#kpiDataType").trigger('chosen:updated');
			}
			else{
				$("#kpiDataType").val("").trigger('chosen:updated');
			}
		}
		/*END*/

		/*Get tagid from tagname : START*/
		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return null;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(appTag in self.appTagArr()){
				if(self.appTagArr()[appTag].tagName.trim() == tagName.trim()){
					return self.appTagArr()[appTag].tagId;
				}
			}
			return null;
		}
		/*END*/

		function onMastersLoad(){
			console.log("==================================Master load=========================================");
			if(appTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					if(params.selectedConfigRows().length == 1){ // for single application edit
						editSingleKpi(params.selectedConfigRows(),uiConstants.common.EDIT_VIEW);
						if(!params.selectedConfigRows()[0].status){ //if the application is inactive
							setKpisUneditable(true);
						}
					}
					else{
						editMultipleKpis(params.selectedConfigRows());
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneKpi(params.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					viewApplication(params.selectedConfigRows());
				}
			}
		}

		function viewApplication(obj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleKpi(obj,uiConstants.common.READ_VIEW);
			setKpisUneditable(false);			
		}

		function setKpisUneditable(isInactiveEdit){
			$('#kpiTypeList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiTypeList").css("cursor", "default");

			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);

			$('#dataTypeList').prop('disabled', true);
			$("#dataTypeList").css("cursor", "default");

			$('#kpiUnitsList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiUnitsList").css("cursor", "default");

			$('#kpiClusterOperation').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiClusterOperation").css("cursor", "default");

			$('#kpiGroupList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiGroupList").css("cursor", "default");

			$('#kpi-tokenfield-typeahead').tokenfield('readonly');
			if(!isInactiveEdit){
				$("[name='kpiStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divKpiAddEdit .chosen-container b").css("display", "none");
		}

		this.showFieldVisibilityForOOB = function(){
			$('#divKpiType').css('display','none');
			$('#divKpiDataType').css('display', 'none');
			$('#divKpiName').css('display', 'none');
			$('#divKpiDescription').css('display', 'none');
			$('#divKpiUnits').css('display', 'none');
			$('#divClusterOpern').css('display', 'none');
			$('#divKpiGroup').css('display', 'none');
			$('#divAppStatus').css('display', 'none');
		}

		this.showFieldVisibilityForCustom = function(){
			$('#divKpiType').css('display','none');
			$('#divKpiDataType').css('display', 'none');
			$('#divKpiName').css('display', 'none');
			$('#divKpiDescription').css('display', 'none');
		}

		this.setKpisUneditableForOOB = function(){
			$('#kpiTypeList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiTypeList").css("cursor", "default");

			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);

			$('#dataTypeList').prop('disabled', true);
			$("#dataTypeList").css("cursor", "default");

			$('#kpiUnitsList').prop('disabled', true).trigger('chosen:updated');;
			//$("#kpiUnitsList").css("cursor", "default");

			$('#kpiClusterOperation').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiClusterOperation").css("cursor", "default");

			$('#kpiGroupList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiGroupList").css("cursor", "default");
			
			//document.getElementById("kpiStatus").disabled = true;
			$("[name='kpiStatus']").bootstrapSwitch('disabled',true);
		}

		this.setKpisUneditableForCustom = function(){
			$('#kpiTypeList').prop('disabled', true).trigger('chosen:updated');
			//$("#kpiTypeList").css("cursor", "default");

			$('#dataTypeList').prop('disabled', true);
			$("#dataTypeList").css("cursor", "default");
		}

		function editSingleKpi(Obj,operation){
			console.log("=====================KPI Edit single==========================");

			if(uiConstants.common.DEBUG_MODE)console.log(Obj[0]);
			//var tagsObjArr = [];

			for(var tagObj in Obj[0].tags){
				//tagsObjArr.push(Obj[0].tags[tagObj].tagName);
				tagsNameArr.push(Obj[0].tags[tagObj].tagName);
			}

			//disable fields on edit
			self.kpiId(Obj[0].kpiId);
			self.kpiAliasName(Obj[0].kpiAliasName);
			self.kpiName(Obj[0].kpiName);
			self.kpiDescription(Obj[0].description);
		
			$("#kpiTypeList").val(Obj[0].kpiTypeId).trigger('chosen:updated');
			self.getKpiGroups();
			self.displayFieldsIfCore($('#kpiTypeList option:selected').text());
			self.showDataTypeField(slugify($('#kpiTypeList option:selected').text()) != "availability" && slugify($('#kpiTypeList option:selected').text()) != "filewatch");
			self.getKpiDataTypelist();

			self.kpiStatus(Obj[0].status);
			$('#kpiStatus').bootstrapSwitch('state',self.kpiStatus());

			if(operation == uiConstants.common.EDIT_VIEW){
				if(Obj[0].isCustom){
					self.setKpisUneditableForCustom();
					self.isCustom(1);
				}
				else{
					self.setKpisUneditableForOOB();
					self.isCustom(0);
				}
			}

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$('#divKpiAliasName').prop('disbled',true);
			}

			$("#kpiDataType").val(Obj[0].kpiDataTypeId).trigger('chosen:updated');
			$("#kpiDataType").trigger('change');

			
			if(Obj[0].kpiUnitId != 0){
				$("#kpiUnitsList").val(Obj[0].kpiUnitId).trigger('chosen:updated');
			}else{
				$("#kpiUnitsList selected:option").text("Select").trigger('chosen:updated');;
			}
			//manually triggering click event
			if($('#kpiDataType option:selected').text() != "Text" && $('#kpiUnitsList option:selected').text() === "Text"){
				$('#kpiUnitsList').trigger('change');
			}

			debugger;
			if(Obj[0].clusterOperationId != 0){
				$("#kpiClusterOperation").val(Obj[0].clusterOperationId).trigger('chosen:updated');
			}else{
				$("#kpiClusterOperation  selected:option").text("Select").trigger('chosen:updated');
			}

			if(Obj[0].kpiGroupId != 0){
				$("#kpiGroupList").val(Obj[0].kpiGroupId).trigger('chosen:updated');
			}else{
				$("#kpiGroupList selected:option").text("Select").trigger('chosen:updated');
			}

			$('#kpi-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
		}

		function cloneKpi(Obj){
			console.log("=====================KPi Cloning==========================");
			if(uiConstants.common.DEBUG_MODE)console.log(Obj[0]);
			var tagsObjArr = [];

			for(var tagObj in Obj[0].tags){
				tagsObjArr.push(Obj[0].tags[tagObj].tagName);
			}

			$("#kpiTypeList").val(Obj[0].kpiTypeId);
			$('#kpiTypeList').prop('disabled', true).trigger('chosen:updated');
			self.getKpiGroups();
			self.displayFieldsIfCore($('#kpiTypeList option:selected').text());
			self.showDataTypeField(slugify($('#kpiTypeList option:selected').text()) != "availability" && slugify($('#kpiTypeList option:selected').text()) != "filewatch");
			self.getKpiDataTypelist();
			self.showDataType($('#getKpiDataTypelist').val() != ""?false:true);
			$("#kpiDataType").trigger('chosen:updated');

			$("#kpiDataType").val(Obj[0].kpiDataTypeId).trigger('chosen:updated');
			self.isCustom(1);

			$("#kpiDataType").trigger('change');
			
			if(Obj[0].kpiUnitId != 0){
				$("#kpiUnitsList").val(Obj[0].kpiUnitId).trigger('chosen:updated');;
			}else{
				$("#kpiUnitsList selected:option").text("Select").trigger('chosen:updated');;
			}

			//manually triggering click event
			if($('#kpiDataType option:selected').text() != "Text" && $('#kpiUnitsList option:selected').text() === "Text"){
				$('#kpiUnitsList').trigger('change');
			}

			if(Obj[0].clusterOperationId != 0){
				$("#kpiClusterOperation").val(Obj[0].clusterOperationId).trigger('chosen:updated');
			}else{
				$("#kpiClusterOperation  selected:option").text("SUM").trigger('chosen:updated');
			}

			//if(Obj[0].kpiGroupId != 0){
				if($.grep(self.kpiGroupArr(), function(e){ return  e.id == self.selectedConfigRows()[0].kpiGroupId; }).length){
					$("#kpiGroupList").val(self.selectedConfigRows()[0].kpiGroupId).trigger('chosen:updated');
				}
				else{
					$("#kpiGroupList selected:option").text("Select").trigger('chosen:updated');
				}
			/*}else{
				$("#kpiGroupList selected:option").text("Select");
			}*/


			$('#kpi-tokenfield-typeahead').tokenfield('setTokens', tagsObjArr);

			if(Obj[0].status == 0){
				self.kpiStatus(!Obj[0].status);
			}
			else{
				self.kpiStatus(Obj[0].status);
			}
			$('#kpiStatus').bootstrapSwitch('state',self.kpiStatus());
		}

		function editMultipleKpis(kpiObj){
			console.log("====================== Kpi on multi edit========================");
			console.log(kpiObj);

			self.isMultiEdit(false);
			$('#divKpiAliasName').css('display','none');
			
			if(kpiObj[0].isCustom){
				self.showFieldVisibilityForCustom();
				self.setKpisUneditableForCustom();
			}
			else{
				self.showFieldVisibilityForOOB();
				self.setKpisUneditableForOOB();
			}

			$("#kpiTypeList").val(kpiObj[0].kpiTypeId).trigger('chosen:updated');
			self.displayFieldsIfCore($('#kpiTypeList option:selected').text());
			self.showDataTypeField(slugify($('#kpiTypeList option:selected').text()) != "availability" && slugify($('#kpiTypeList option:selected').text()) != "filewatch");

			var kpiCommonDataType = []  
			var kpiCommonStatusArr = [];
			var kpiCommonClusterOpernArr = [];
			var kpiCommonGroupArr = [];
			var kpiCommonUnitsArr = [];
			var kpiNameArr = [];

			commonTagsArr = [];
			uncommonTagsArr = [];
						
			kpiCommonStatusArr.push(kpiObj[0].status);
			kpiCommonClusterOpernArr.push(kpiObj[0].clusterOperationId);
			kpiCommonGroupArr.push(kpiObj[0].kpiGroupId);
			kpiCommonUnitsArr.push(kpiObj[0].kpiUnitId);
			kpiCommonDataType.push(kpiObj[0].kpiDataTypeId);
			kpiNameArr.push(kpiObj[0].kpiName);

			//Common tokenfield for multiple edit
			/*var commonEngine = new Bloodhound({	
				local: self.configTagAutoCompleteArr(),
				datumTokenizer: function(d) {
					return Bloodhound.tokenizers.whitespace(d.value);
				},
				queryTokenizer: Bloodhound.tokenizers.whitespace
			});

			commonEngine.initialize();*/

			$('#kpi-tokenfield-typeahead').tokenfield({
				autocomplete: {
				source: self.configTagAutoCompleteArr(),
				delay: 100
				},
				createTokensOnBlur: true
			});

			$('#kpi-tokenfield-typeahead-tokenfield').on("keyup", function(e){
				if(e.which == 13 && $(this).val().trim() != ""){
					$("#kpi-tokenfield-typeahead-tokenfield").blur();		
				}
			});

			var tagsArr = [];
			var allTagsArr = [];
			var tagsObjArr = [];

			for(obj in kpiObj){
				tagsObjArr = [];
				for(tab in kpiObj[obj].tags){

				if(uiConstants.common.DEBUG_MODE)console.log(kpiObj[obj].tags[tab].tagName);
					tagsObjArr.push(kpiObj[obj].tags[tab].tagName);
					//if (allTagsArr.indexOf(Obj[obj].tags[tab].tagName)==-1) allTagsArr.push(Obj[obj].tags[tab].tagName);
				}
				tagsArr.push(tagsObjArr);
			}

			commonTagsArr = tagsArr.shift().filter(function(v) {
			    return tagsArr.every(function(a) {
			        return a.indexOf(v) !== -1;
			    });
			});

			$('#kpi-tokenfield-typeahead').tokenfield('setTokens', commonTagsArr);

			for(obj in kpiObj){
				
				$('#txtKpiNames').append(","+kpiObj[obj].kpiName);	

				if(kpiCommonDataType.length<=1){
					if(kpiCommonDataType.indexOf(kpiObj[obj].kpiDataTypeId) == -1){
						kpiCommonDataType.push(kpiObj[obj].kpiDataTypeId);	
					}
				}

				if(kpiCommonUnitsArr.length<=1){
					if(kpiCommonUnitsArr.indexOf(kpiObj[obj].kpiUnitId) == -1){
						kpiCommonUnitsArr.push(kpiObj[obj].kpiUnitId);	
					}
				}

				if(kpiCommonGroupArr.length<=1){
					if(kpiCommonGroupArr.indexOf(kpiObj[obj].kpiGroupId) == -1){
						kpiCommonGroupArr.push(kpiObj[obj].kpiGroupId);	
					}
				}

				if(kpiCommonClusterOpernArr.length<=1){
					if(kpiCommonClusterOpernArr.indexOf(kpiObj[obj].clusterOperationId) == -1){
						kpiCommonClusterOpernArr.push(kpiObj[obj].clusterOperationId);	
					}
				}

				if(kpiCommonStatusArr.length<=1){
					if(kpiCommonStatusArr.indexOf(kpiObj[obj].status) == -1){
						kpiCommonStatusArr.push(kpiObj[obj].status);	
					}
				}
			}
			$('#txtKpiNames').text($('#txtKpiNames').text().substring(1));//removing comma(,) from front of string

			if(kpiCommonDataType.length == 1){
				$("#kpiDataType").val(kpiObj[0].kpiDataTypeId).trigger('chosen:updated');
				self.getKpiDataTypelist(); 
			}

			if(kpiCommonUnitsArr.length == 1){
				self.setKpiUnits(kpiObj[0].kpiDataTypeId);
				$("#kpiUnitsList").val(kpiObj[0].kpiUnitId).trigger('chosen:updated');;
			}
			else{
				$("#kpiUnitsList selected:option").text("Select").trigger('chosen:updated');;
				$("#kpiUnitsList").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
			}
			//manually triggering click event
			if($('#kpiDataType option:selected').text() != "Text" && $('#kpiUnitsList option:selected').text() === "Text"){
				$('#kpiUnitsList').trigger('change');
			}

			if(kpiCommonGroupArr.length == 1){
				$("#kpiGroupList").val(kpiObj[0].kpiGroupId);
			}
			else{
				$("#kpiGroupList selected:option").text("Select").trigger('chosen:updated');
				//$("#kpiGroupList").val("Select");
				$("#kpiGroupList").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
			}

			if(kpiCommonClusterOpernArr.length == 1){
				$("#kpiClusterOperation").val(kpiObj[0].clusterOperationId).trigger('chosen:updated');
			}
			else{
				$("#kpiClusterOperation selected:option").text("Select").trigger('chosen:updated');
				$("#kpiClusterOperation").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
			}
			//

			if(kpiCommonStatusArr.length == 1){
				//$('#kpiStatus').prop('checked', kpiObj[0].status);
				$('#kpiStatus').bootstrapSwitch('state',kpiObj[0].status);

				if(!kpiObj[0].status){ //is status is inactive
					setKpisUneditable(true);
				}
			}
			else{
				//$("#kpiStatus").prop("indeterminate", true);
				$('#kpiStatus').bootstrapSwitch('indeterminate', true);
				setKpisUneditable(true);
			}
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		this.updateMultipleKpis = function(){
			var kpiObjArr = [];
			var kpiData;
			var kpiObj;

			var tagsObjArr = [];
			var tagsArr = [];
			var statusFlag = $('#kpiStatus').bootstrapSwitch('state')?1:0;
        	var appsArr = [];

        	self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			if(uiConstants.common.DEBUG_MODE)console.log("self.selectedConfigRows()"+self.selectedConfigRows().length);
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());


			/*if(containsDuplicate($("#common-kpi-tokenfield-typeahead").val())){ 
				self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
			}
			else if($('#kpiTypeList').val() == "Select" ){
				self.errorMsg(uiConstants.common.APPLICATION_TYPE_REQUIRED);
			}
			else{*/
				
			for(obj in self.selectedConfigRows()){
				self.copyTagsArr([]);


				if($('#kpiStatus').bootstrapSwitch('indeterminate')){
        			statusFlag = self.selectedConfigRows()[obj].status;
        		}

        		
					//tag code :start
					tagsArr = [];
					if(self.tags() && self.tags().trim().length == 1){
						tagsArr.push(self.tags());
					}

					else if(self.tags() && self.tags().trim().length > 1){
						tagsArr = self.tags().split(",");
					}

					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, 
								"tagName":tagsArr[tag].trim(), 
								"tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), 
								"tagName":tagsArr[tag].trim(),
								"tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), 
							"tagName":tagsToDeleteArr[tag].trim(), 
							"tagOperation":"delete"});
					}



					for(var tag in self.selectedConfigRows()[obj].tags){
						console.log(self.selectedConfigRows()[obj].tags[tag].tagName.trim());
						console.log(tagsToDeleteArr);
						console.log(tagsArr);
						if(tagsToDeleteArr.indexOf(self.selectedConfigRows()[obj].tags[tag].tagName.trim()) == -1 && tagsArr.indexOf(self.selectedConfigRows()[obj].tags[tag].tagName.trim()) == -1){
							tagsObjArr.push({
								"tagId":self.selectedConfigRows()[obj].tags[tag].tagId,
								"tagName":self.selectedConfigRows()[obj].tags[tag].tagName.trim(), 
								"tagOperation":"none"});
						}
					}
					//tag code :end
					console.log("================in edit multiple check================");
					console.log(self.selectedConfigRows()[obj].isCustom);
					if(self.selectedConfigRows()[obj].isCustom){
						kpiObj = {
									"index": parseInt(obj) + 1,
									"kpiId": self.selectedConfigRows()[obj].kpiId,
									"kpiTypeId": self.selectedConfigRows()[obj].kpiTypeId,
									"kpiName": self.selectedConfigRows()[obj].kpiName,
									"description": self.selectedConfigRows()[obj].description,
									"kpiDataTypeId": self.selectedConfigRows()[obj].kpiDataTypeId,
									"clusterOperationId": ($("#kpiClusterOperation option:selected").val()!= "" && $("#kpiClusterOperation option:selected").val()!= "Select")?parseInt($("#kpiClusterOperation option:selected").val()):0,
									"kpiGroupId": ($("#kpiGroupList option:selected").val()!= null && $("#kpiGroupList option:selected").val()!= "" && $("#kpiGroupList option:selected").val()!= "Select")?parseInt($("#kpiGroupList option:selected").val()):0,
									"kpiUnitId": ($("#kpiUnitsList option:selected").val()!= "" && $("#kpiUnitsList option:selected").val()!= "Select")?parseInt($("#kpiUnitsList option:selected").val()):0,								
								 	"tags":tagsObjArr,
								 	"status" : statusFlag,
								 	"isCustom" : 1
								 };
						kpiObjArr.push(kpiObj);
								 
					}else{
						kpiObj = {
									"index": parseInt(obj) + 1,
									"kpiId": self.selectedConfigRows()[obj].kpiId,
									"kpiTypeId": self.selectedConfigRows()[obj].kpiTypeId,
									"kpiName": self.selectedConfigRows()[obj].kpiName,
									"description": self.selectedConfigRows()[obj].description,
									"kpiDataTypeId": self.selectedConfigRows()[obj].kpiDataTypeId,
									"clusterOperationId": self.selectedConfigRows()[obj].clusterOperationId,
									"kpiGroupId":  self.selectedConfigRows()[obj].kpiGroupId,
									"kpiUnitId":  self.selectedConfigRows()[obj].kpiUnitId,
									"tags": tagsObjArr,
									"status" : self.selectedConfigRows()[obj].status,
									"isCustom" : 0
								 };
						kpiObjArr.push(kpiObj);
					}
		   	}//end of for loop

			if(self.errorMsg() == ""){
	   			kpiData = {"kpis":kpiObjArr};	
				if(uiConstants.common.DEBUG_MODE)console.log("=====================Update Multiple Kpis=============================");		
				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(kpiData));
				requestCall(uiConstants.common.SERVER_IP + "/kpiDetail", "PUT", JSON.stringify(kpiData), "editMultipleKpi", successCallback, errorCallback);
			}
	    }

		//Adding/Updating single application
		this.addEditKpi = function(){
			if(uiConstants.common.DEBUG_MODE)console.log("################");
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows().length);
			this.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var kpiObjArr = [];
			var kpiData;

			$("#txtName").val($("#txtName").val().trim());
			$("#divKpiDescription #txtDescription").val($("#divKpiDescription #txtDescription").val().trim());

			if(self.selectedConfigRows().length > 1){
				self.updateMultipleKpis();
			}
			else{
				if($("#divKpiAddEdit #kpiTypeList").val() == ""){
					showError("#divKpiAddEdit #kpiTypeList_chosen", uiConstants.common.SELECT_KPI_TYPE);
					showError("#divKpiAddEdit #kpiTypeList_chosen span", uiConstants.common.SELECT_KPI_TYPE);
			    	self.errorMsg("#divKpiAddEdit #kpiTypeList_chosen");
				}
				if($("#divKpiAddEdit #txtName").val().length < 2){
					showError("#divKpiAddEdit #txtName", uiConstants.common.NAME_REQUIRED);
			    	self.errorMsg("#divKpiAddEdit #txtName");
				}
				else if($("#divKpiAddEdit #txtName").val().length > 45){
					showError("#divKpiAddEdit #txtName", uiConstants.common.NAME_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divKpiAddEdit #txtName");
				}
				else if(!nameValidation($("#divKpiAddEdit #txtName").val())){
					showError("#divKpiAddEdit #txtName", uiConstants.common.NAME_INVALID_ERROR);
			    	self.errorMsg("#divKpiAddEdit #txtName");
				}
				if($("#divKpiDescription #txtDescription").val().trim() == ""){
					showError("#divKpiAddEdit #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    	self.errorMsg("#divKpiAddEdit #txtDescription");
				}
				else if($("#divKpiDescription #txtDescription").val().length < 25){
					showError("#divKpiAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    	self.errorMsg("#divKpiAddEdit #txtDescription");
				}
				else if($("#divKpiDescription #txtDescription").val().length > 256){
					showError("#divKpiAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divKpiAddEdit #txtDescription");
				}
				if($('#divKpiAddEdit #kpiDataType').val() == "" ){
					showError("#divKpiAddEdit #kpiDataType_chosen", uiConstants.kpiConfig.KPI_DATATYPE_REQUIRED);
					showError("#divKpiAddEdit #kpiDataType_chosen span", uiConstants.kpiConfig.KPI_DATATYPE_REQUIRED);
			    	self.errorMsg("#divKpiAddEdit #kpiDataType_chosen");
				}
				if($("#divKpiAddEdit #kpiUnitsList").val() == "" && $('#divKpiAddEdit #kpiTypeList option:selected').text().toUpperCase() == "CORE"){
					showError("#divKpiAddEdit #kpiUnitsList_chosen", uiConstants.kpiConfig.KPI_UNITS_REQUIRED);
					showError("#divKpiAddEdit #kpiUnitsList_chosen span", uiConstants.kpiConfig.KPI_UNITS_REQUIRED);
			    	self.errorMsg("#divKpiAddEdit #kpiUnitsList_chosen");
				}
				if($("#divKpiAddEdit #kpiClusterOperation").val() == "" && $('#divKpiAddEdit #kpiTypeList option:selected').text().toUpperCase() == "CORE"){
					showError("#divKpiAddEdit #kpiClusterOperation_chosen", uiConstants.kpiConfig.KPI_CLUSTEROPERN_REQUIRED);
					showError("#divKpiAddEdit #kpiClusterOperation_chosen span", uiConstants.kpiConfig.KPI_CLUSTEROPERN_REQUIRED);
			    	self.errorMsg("#divKpiAddEdit #kpiClusterOperation_chosen");
				}

				removeError("#divKpiAddEdit .tokenfield");
				removeError("#divKpiAddEdit #kpi-tokenfield-typeahead-tokenfield");
			
				if(containsDuplicate($("#kpi-tokenfield-typeahead").val())){
					//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
					showError("#divKpiAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
					showError("#divKpiAddEdit #kpi-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
				    self.errorMsg("#divKpiAddEdit .tokenfield");
				}
				else{

					console.log("++++++++++++++++++++++++++++=kpi tags check+++++++++++++++++++++++++++");
					console.log(self.tags());

					if(self.tags() && self.tags().trim().length == 1){
						tagsArr.push(self.tags());
					}
					else if(self.tags() && self.tags().trim().length > 1){
						//if(self.tags() != undefined)
						tagsArr = self.tags().split(",");
					}	

					for(var t in tagsArr){
						if(tagsArr[t].trim().length < 2){
							showError("#divKpiAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
							showError("#divKpiAddEdit #kpi-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						    self.errorMsg("#divKpiAddEdit .tokenfield");
							break;
						}
						else if(tagsArr[t].trim().length > 45){
							showError("#divKpiAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
							showError("#divKpiAddEdit #kpi-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						    self.errorMsg("#divKpiAddEdit .tokenfield");
							break;
						}
						else if(!tagValidation(tagsArr[t].trim())){
							showError("#divKpiAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
							showError("#divKpiAddEdit #kpi-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
						    self.errorMsg("#divKpiAddEdit .tokenfield");
							break;
						}
					}

					if(self.errorMsg() == ""){

						for(var tag in tagsArr){
							//new tag changes
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":0, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							/*else if(tagsToDeleteArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"delete"});
							}*/
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}

						var kpiObj = {
							"index":1,
							"kpiId": self.kpiId(),
							"kpiTypeId":  parseInt($("#kpiTypeList option:selected").val()),
							"kpiName": self.kpiName(),
							"description": self.kpiDescription().trim(),
							"kpiDataTypeId": parseInt($("#kpiDataType option:selected").val()),
							"clusterOperationId": ($("#kpiClusterOperation option:selected").val()!= "" && $("#kpiClusterOperation option:selected").val()!= "Select")?parseInt($("#kpiClusterOperation option:selected").val()):0,
							"kpiGroupId": ($("#kpiGroupList option:selected").val()!= "" && $("#kpiGroupList option:selected").val()!= "Select")?parseInt($("#kpiGroupList option:selected").val()):0,
							"kpiUnitId":  ($("#kpiUnitsList option:selected").val()!= "" && $("#kpiUnitsList option:selected").val()!= "Select")?parseInt($("#kpiUnitsList option:selected").val()):0,
							"tags": tagsObjArr,
							"status" : $('#kpiStatus').bootstrapSwitch('state')?1:0,
							"isCustom" : parseInt(self.isCustom())}

						kpiObjArr.push(kpiObj);
						kpiData = {"kpis":kpiObjArr};

						if(uiConstants.common.DEBUG_MODE)console.log("=======================Single add/edit kpi ===========================");
						if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(kpiData));

						if(self.kpiId() == 0)
							requestCall(uiConstants.common.SERVER_IP + "/kpiDetail", "POST", JSON.stringify(kpiData), "addSingleKpi", successCallback, errorCallback);
						else
							requestCall(uiConstants.common.SERVER_IP + "/kpiDetail", "PUT", JSON.stringify(kpiData), "editSingleKpi", successCallback, errorCallback);
					}
				}
			}
		}

		this.cancelAddScreen = function(){
			params.selectedConfigRows([]);
			
			params.currentViewIndex(uiConstants.common.LIST_VIEW);
			if(params.currentViewIndex(uiConstants.common.LIST_VIEW))
				self.pageSelected("KPI Configuration");
		}

		function successCallback(data, reqType) {
			 if(reqType === "getKpiTagList"){
				self.appTagArr(data.result);

				for(var tagData in self.appTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.appTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.appTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.appTagArr()[tagData].tagName);

				}

				/*var engine = new Bloodhound({	
					//local: self.appTagAutoCompleteArr(),
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#kpi-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#kpi-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#kpi-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#kpi-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				appTagLoaded = 1;
				onMastersLoad();
			}
			else if (reqType === "addSingleKpi") {
				var res = data.result;
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_KPI,res[0].errorCode,res[0].message), "error");
					}else{
						showMessageBox(uiConstants.kpiConfig.ERROR_ADD_KPI, "error");
					}
				}
				else{
					showMessageBox(uiConstants.kpiConfig.SUCCESS_ADD_KPI);
					self.cancelAddScreen();
					params.curPage(1);
				}

			}
			else if (reqType === "editSingleKpi") {
				var res = data.result;
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_KPI,res[0].errorCode,res[0].message), "error");
					}
					else{
						showMessageBox(uiConstants.kpiConfig.ERROR_EDIT_KPI, "error");
					}
					
				}else{
					showMessageBox(uiConstants.kpiConfig.SUCCESS_UPDATE_KPI);
					self.cancelAddScreen();
					params.curPage(1);
				}
			}
			else if(reqType === "editMultipleKpi"){
				var res = data.result;
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_KPI,res[0].errorCode,res[0].message), "error");
					}
					else{
						showMessageBox(uiConstants.kpiConfig.ERROR_EDIT_KPI, "error");
					}
				}else{
					showMessageBox(uiConstants.kpiConfig.SUCCESS_MULTIPLE_UPDATE_KPIS);
					self.cancelAddScreen();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getApplicationTag"){
				showMessageBox(uiConstants.kpiConfig.ERROR_GET_KPI_TAGS, "error");
			}
			else if(reqType === "addSingleApp"){
				showMessageBox(uiConstants.kpiConfig.ERROR_ADD_KPI, "error");
			}
			else if(reqType === "editSingleApp"){
				showMessageBox(uiConstants.kpiConfig.ERROR_EDIT_KPI, "error");
			}
			else if(reqType === "editMultipleApp"){
				showMessageBox(uiConstants.kpiConfig.ERROR_EDIT_MULTIPLE_KPIS, "error");
			}
		}

	}
	KpiAddEdit.prototype.dispose = function() { };
	return { viewModel: KpiAddEdit, template: templateMarkup };
});
