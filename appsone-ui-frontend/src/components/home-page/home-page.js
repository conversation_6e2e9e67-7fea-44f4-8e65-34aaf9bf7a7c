define(['jquery','jquery-ui','knockout','text!./home-page.html','hasher','ui-constants','ui-common'], function($,jqueryUI,ko,templateMarkup,hasher,uiConstants,uicommon) {

  function HomePage(params) {
    var self = this;
    $("#slide-nav").css("display","block"); //top-bar
    //$("#side-nav").css("display","block"); //side-bar

    this.renderHandler = function(){ 
      if(uiConstants.common.DEBUG_MODE) console.log("home page");
    };

    this.successCallback = function(data, reqType) {}

    this.errorCallback = function(reqType) {}
  }



  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  HomePage.prototype.dispose = function() { };
  
  return { viewModel: HomePage, template: templateMarkup };

});