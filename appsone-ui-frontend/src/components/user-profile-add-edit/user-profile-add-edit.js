define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','typeahead','text!./user-profile-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','jQuery-plugins','floatThead'], function($,ko,jc,bt,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,jQueryPlugins,floatThead) {

	function UserProfileAddEdit(params) {
		var self = this;
		
		this.applicationsArr = ko.observableArray();
		this.screensArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.userFirstName = ko.observable("");
		this.userLastName = ko.observable("");
		this.phoneCode = ko.observable("");
		this.phoneNum = ko.observable("");
		this.emailId = ko.observable("");
		this.userName = ko.observable("");
		this.password = ko.observable("");
		this.confirmPassword = ko.observable("");
		this.oldPassword = ko.observable("");
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.currentViewIndex = params.currentViewIndex;
		this.lockedReason = ko.observable();
		this.configStatus = ko.observable(true);
		var applicationsLoaded = 0;
		this.pageSelected = params.pageSelected;
		var appArr = [];
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		this.availableApplicationArr = ko.observableArray();
		this.selectedApplicationArr = ko.observableArray();
  		this.enableAddApplicationBtn = ko.observable(false);
  		this.enableRemoveApplicationBtn = ko.observable(false);
  		this.isRoleSelected = ko.observable(false);
  		this.isIndividualProfileView = ko.observable(params.isIndividualProfileView);
  		this.selectedRole = ko.observable();
  		this.showApplications = ko.observable(false);
  		this.showPermissions = ko.observable(false);
  		self.unlockProfile = ko.observable(false);
		this.userRolesArr = self.isIndividualProfileView() ? ko.observableArray() : params.userRolesArr;
		this.multipleLoginStatus = ko.observable(1);
		this.getUserPermissions = ko.observable(true);
		this.getRoleName = ko.observable();

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtFirstName").focus();

			$(".positive-integer").numeric({ decimal: false, negative: false }, function() { alert("Positive integers only"); this.value = ""; this.focus(); });

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			//$("#configStatus").bootstrapSwitch();

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$("#multipleLoginStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Yes",
				'offText': "No"
			});

			$("#multipleLoginStatus").on('switchChange.bootstrapSwitch', function () {
				self.multipleLoginStatus($('#multipleLoginStatus').bootstrapSwitch('state')?1:0);
			});

			if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());
			}

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["userFirstName"]));
			}

			$("div").on("click", "#selAllAvailApplication", function(e){
				$("#availableApplicationList .checkList").prop("checked", $("#selAllAvailApplication").prop("checked"));
				self.enableAddApplicationBtn($("#availableApplicationList .checkList:checked").length);
			});

			$("div").on("change", "#availableApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllAvailApplication").prop("checked", self.availableApplicationArr().length == $("#availableApplicationList .checkList:checked").length);
					self.enableAddApplicationBtn($("#availableApplicationList .checkList:checked").length);
				}
	        });

	        $("div").on("click", "#selAllSelApplication", function(e){
				$("#selectedApplicationList .checkList").prop("checked", $("#selAllSelApplication").prop("checked"));
				self.enableRemoveApplicationBtn($("#selectedApplicationList .checkList:checked").length);
			});

			$("div").on("click", "#selectedApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllSelApplication").prop("checked", self.selectedApplicationArr().length == $("#selectedApplicationList .checkList:checked").length);
					self.enableRemoveApplicationBtn($("#selectedApplicationList .checkList:checked").length);
				}
			});

			$('#configStatus').on('switchChange.bootstrapSwitch', function (event, state) {
				if(self.lockedReason() != uiConstants.common.NOT_APPLICABLE){
					document.getElementById("chkUnlockProfile").disabled = !self.configStatus();
				}
			});

			requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=1", "GET", "", "getApplications", successCallback, errorCallback);
			//requestCall(uiConstants.common.SERVER_IP + "/screensList", "GET", "", "getScreenList", successCallback, errorCallback);

			/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				var roleId = self.selectedConfigRows()[0].userRoleId;
				requestCall(uiConstants.common.SERVER_IP + "/config/screens?roleId="+roleId, "GET", "", "getScreenList", successCallback, errorCallback);

				//requestCall("http://www.mocky.io/v2/580494bf240000672f135cc9?callback=?", "GET", "", "getScreenList", successCallback, errorCallback);
			}
			else{*/
				requestCall(uiConstants.common.SERVER_IP + "/config/screens", "GET", "", "getScreenList", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/580494bf240000672f135cc9?callback=?", "GET", "", "getScreenList", successCallback, errorCallback);
			//}
		}

		this.handleAuthorizationChk = function(chkId){
			console.log($("#delete"+chkId).prop("checked"));
			if($("#delete"+chkId).prop("checked") || $("#create"+chkId).prop("checked") || $("#update"+chkId).prop("checked")){
				$("#read"+chkId).prop("checked", true);
			}

			return true;
		}

		this.addToSelectedApplication = function(){
			var availArr = $('#availableApplicationList').getSelectedValues();

			var applicationObj;
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableApplicationArr());

			for(arr in availArr){
				applicationObj = $.grep(self.availableApplicationArr(), function(e){ return e.id == availArr[arr]; });
				self.availableApplicationArr.splice(self.availableApplicationArr.indexOf(applicationObj[0]), 1);
				self.selectedApplicationArr.push(applicationObj[0]);
			}

			$('#selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableAddApplicationBtn(false);
		}

		this.addToAvailableApplication = function(){
			var selArr = $('#selectedApplicationList').getSelectedValues();
			var applicationObj;
			for(arr in selArr){
				applicationObj = $.grep(self.selectedApplicationArr(), function(e){ return e.id == selArr[arr]; });
				self.selectedApplicationArr.splice(self.selectedApplicationArr.indexOf(applicationObj[0]), 1);
				self.availableApplicationArr.push(applicationObj[0]);
			}

			$('#selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableRemoveApplicationBtn(false);
		}

		this.onRoleChange = function(){
			if($("#rolesList").val() != "0"){
				self.getUserPermissions(false);
				getUserRolePermissions($("#rolesList").val(), true);
				self.getRoleName($("#rolesList option:selected").text());

			}
			else{
				self.isRoleSelected(false);
			}
		}

		function uncheckApplication(){
			$("#selectedApplicationList .checkList").prop("checked",false);
			$("#availableApplicationList .checkList").prop("checked",false);
			$("#selAllAvailApplication").prop("checked",false);
			$("#selAllSelApplication").prop("checked",false);
		}

        self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		//Adding/Updating single user profile
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			self.userFirstName(self.userFirstName().trim());
			self.userLastName(self.userLastName().trim());

			if(!self.isIndividualProfileView()){
				if(self.userFirstName() == undefined || self.userFirstName() == ""){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_REQUIRED);
					showError("#divUserProfileAddEdit #txtFirstName", uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_REQUIRED);
				    self.errorMsg("#divUserProfileAddEdit #txtFirstName");
				}
				else if(self.userFirstName().length < 2){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_MIN_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtFirstName", uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_MIN_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtFirstName");
				}
				else if(self.userFirstName().length > 20){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_MAX_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtFirstName", uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_MAX_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtFirstName");
				}
				else if(!nameValidation(self.userFirstName())){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_INVALID_ERROR);
					showError("#divUserProfileAddEdit #txtFirstName", uiConstants.userProfileConfig.USER_PROFILE_FIRST_NAME_INVALID_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtFirstName");
				}
				if(self.userLastName() != "" && self.userLastName().length < 2){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_MIN_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtLastName", uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_MIN_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtLastName");
				}
				else if(self.userLastName() != "" && self.userLastName().length > 20){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_MAX_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtLastName", uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_MAX_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtLastName");
				}
				else if(self.userLastName() != "" && !nameValidation(self.userLastName())){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_INVALID_ERROR);
					showError("#divUserProfileAddEdit #txtLastName", uiConstants.userProfileConfig.USER_PROFILE_LAST_NAME_INVALID_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtLastName");
				}
				/*else if(self.phoneCode() == undefined || self.phoneCode() == ""){
					self.errorMsg(uiConstants.common.PHONE_COUNTRY_CODE_REQUIRED);
				}
				else if(self.phoneNum() == undefined || self.phoneNum() == ""){
					self.errorMsg(uiConstants.common.PHONE_NUMBER_REQUIRED);
				}*/
				if(self.emailId() == undefined || self.emailId() == ""){
					//self.errorMsg(uiConstants.common.EMAIL_ID_REQUIRED);
					showError("#divUserProfileAddEdit #txtEmailId", uiConstants.common.EMAIL_ID_REQUIRED);
				    self.errorMsg("#divUserProfileAddEdit #txtEmailId");
				}
				else if(!emailValidation(self.emailId())){
					//self.errorMsg(uiConstants.common.INVALID_EMAIL_ID);
					showError("#divUserProfileAddEdit #txtEmailId", uiConstants.common.INVALID_EMAIL_ID);
				    self.errorMsg("#divUserProfileAddEdit #txtEmailId");
				}
				if(self.userName() == undefined || self.userName() == ""){
					//self.errorMsg(uiConstants.userProfileConfig.USER_NAME_REQUIRED);
					showError("#divUserProfileAddEdit #txtUserName", uiConstants.userProfileConfig.USER_NAME_REQUIRED);
				    self.errorMsg("#divUserProfileAddEdit #txtUserName");
				}
				else if(self.userName().length < 5){
					//self.errorMsg(uiConstants.userProfileConfig.USER_NAME_MIN_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtUserName", uiConstants.userProfileConfig.USER_NAME_MIN_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtUserName");
				}
				else if(self.userName().length > 10){
					//self.errorMsg(uiConstants.userProfileConfig.USER_NAME_MAX_LENGTH_ERROR);
					showError("#divUserProfileAddEdit #txtUserName", uiConstants.userProfileConfig.USER_NAME_MAX_LENGTH_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtUserName");
				}
				else if(!userNameValidation(self.userName())){
					//self.errorMsg(uiConstants.userProfileConfig.USER_NAME_INVALID_ERROR);
					showError("#divUserProfileAddEdit #txtUserName", uiConstants.userProfileConfig.USER_NAME_INVALID_ERROR);
				    self.errorMsg("#divUserProfileAddEdit #txtUserName");
				}
				/*else if(self.password() == undefined || self.password() == ""){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_REQUIRED);
				}
				else if(self.password().length < 8){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MIN_LENGTH_ERROR);
				}
				else if(self.password().length > 15){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MAX_LENGTH_ERROR);
				}
				else if(!passwordValidation(self.password())){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_INVALID_PASSWORD);
				}
				else if(self.confirmPassword() == undefined || self.confirmPassword() == ""){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_CONFIRM_PASSWORD_REQUIRED);
				}
				else if(self.password() != self.confirmPassword()){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORDS_MISMATCH);
				}*/
				if(self.showApplications() && $("#selectedApplicationList").getAllValues().length == 0){
					//self.errorMsg(uiConstants.common.SELECT_APPLICATION_MULT_OPTION_MSG);
					showError("#divUserProfileAddEdit #selectedApplicationList", uiConstants.common.SELECT_APPLICATION_MULT_OPTION_MSG);
				    self.errorMsg("#divUserProfileAddEdit #selectedApplicationList");
				}
				if($("#rolesList").val() == 0){
					//self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_ROLE_REQUIRED);
					showError("#divUserProfileAddEdit #rolesList_chosen", uiConstants.userProfileConfig.USER_PROFILE_ROLE_REQUIRED);
					showError("#divUserProfileAddEdit #rolesList_chosen span", uiConstants.userProfileConfig.USER_PROFILE_ROLE_REQUIRED);
				    self.errorMsg("#divUserProfileAddEdit #rolesList_chosen");
				}
				else{
					removeError("#divUserProfileAddEdit #rolesList_chosen");
					removeError("#divUserProfileAddEdit #rolesList_chosen span");
				}
				if(self.errorMsg() == ""){
					var permissionsArr = [];
					var screenId = 0;

					for(var screen in self.screensArr()){
						screenId = self.screensArr()[screen].screenId;

						permissionsArr.push({
							"screenId": screenId,
							"readEnabled": $("#read"+screenId).prop("checked") ? 1 : 0,
							"createEnabled": $("#create"+screenId).prop("checked") ? 1 : 0,
							"updateEnabled": $("#update"+screenId).prop("checked") ? 1 : 0,
							"deleteEnabled": $("#delete"+screenId).prop("checked") ? 1 : 0
						});
					}

					/*var ivStr = getRandomString();
					var passwd = getEncodedText(ivStr, self.password());

					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.selectedConfigRows()[0].password == self.password()){
						ivStr = "";
						passwd = "";
					}*/

					var forcePasswdChange = 0;

					//if(self.selectedConfigRows()[0] && (self.selectedConfigRows()[0].status == 0 || self.selectedConfigRows()[0].userLocked != 0)){
						if(self.unlockProfile() && self.configStatus()){
							forcePasswdChange = 1;
						}
				//	}

					var userProfileObj = {
						"index":1,
						"userFirstName": self.userFirstName().trim(),
						"userLastName": self.userLastName().trim(),
						"phoneCountryCode": self.phoneCode(),
						"phoneNumber": self.phoneNum(),
						"userName": self.userName(),
						"allowMultipleLogin": self.multipleLoginStatus(),
						"forcePasswordChange": forcePasswdChange,
						//"password": passwd,
						"initializationVector": ivStr,
						"emailId": self.emailId(),
						"applicationIds": self.showApplications() ? $("#selectedApplicationList").getAllValues().map(function (x){return parseInt(x);}) : [],
						"userRoleId": $("#rolesList").val(),
						"permissions": permissionsArr,
						"status" : self.configStatus()?1:0};

					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(userProfileObj));

					if(self.configId() == 0)
						requestCall(uiConstants.common.SERVER_IP + "/userProfile", "POST", JSON.stringify(userProfileObj), "addSingleConfig", successCallback, errorCallback);
					else
						requestCall(uiConstants.common.SERVER_IP + "/userProfile/" + self.configId(), "PUT", JSON.stringify(userProfileObj), "editSingleConfig", successCallback, errorCallback);
				}
			}
			else{
				if(self.oldPassword() == undefined || self.oldPassword() == ""){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_OLD_PASSWORD_REQUIRED);
				}
				else if(self.password() == undefined || self.password() == ""){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_NEW_PASSWORD_REQUIRED);
				}
				else if(self.oldPassword() == self.password()){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_OLD_NEW_PASSWORD_SAME_ERROR);
				}
				else if(self.password().length < 8){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MIN_LENGTH_ERROR);
				}
				else if(self.password().length > 15){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MAX_LENGTH_ERROR);
				}
				else if(!passwordValidation(self.password())){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_INVALID_PASSWORD);
				}
				else if(self.confirmPassword() == undefined || self.confirmPassword() == ""){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_CONFIRM_PASSWORD_REQUIRED);
				}
				else if(self.password() != self.confirmPassword()){
					self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_PASSWORDS_MISMATCH);
				}
				else if(self.errorMsg() == ""){
					//This is same as change password page
					var ivStr = getRandomString();
					var oldPasswd = getEncodedText(ivStr, self.oldPassword());
          			var newPasswd = getEncodedText(ivStr, self.password());

					var userProfileObj = {
			            "oldPassword": oldPasswd,
			            "newPassword": newPasswd,
			            "initializationVector": ivStr};

					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(userProfileObj));
          			requestCall(uiConstants.common.SERVER_IP + "/password/reset", "PUT", JSON.stringify(userProfileObj), "indivUserProfileChange", successCallback, errorCallback);
				}
			}
		}

		this.resetUserPassword = function(){
			showMessageBox(uiConstants.userProfileConfig.CONFIRM_USER_PASSWORD_RESET, "question", "confirm", function confirmCallback(confirmReset){
	        	if(confirmReset){
		        	requestCall(uiConstants.common.SERVER_IP + "/reserPassword/" + self.configId(), "PUT", null, "resetPassword", successCallback, errorCallback);
	        	}
	        });
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.selectedConfigRows()[0].userName == window.userName()){
				$("[name='configStatus']").bootstrapSwitch('disabled',true);

				$("[name='configStatus']").closest('.bootstrap-switch')
		    			.attr('title', uiConstants.userProfileConfig.LOGGED_IN_USER_STATUS_DISABLED_MSG).tooltip();

			}
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtFirstName').prop('readonly', true);
			$('#txtLastName').prop('readonly', true);
			$('#txtPhoneCode').prop('readonly', true);
			$('#txtPhoneNum').prop('readonly', true);
			$('#txtEmailId').prop('readonly', true);
			$('#txtUserName').prop('readonly', true);

			if(!self.isIndividualProfileView()){
				$('#txtPassword').prop('readonly', true);
				$('#txtConfirmPassword').prop('readonly', true);

				$("#appDiv").find("input,button,select").attr("disabled", "disabled");
				$("#availableApplicationList").addClass("checklist-disabled");
				$("#selectedApplicationList").addClass("checklist-disabled");
				$("#rolesList").prop('disabled', true).trigger("chosen:updated");
				$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);
			}

			if(isInactiveEdit){
				$("[name='configStatus']").bootstrapSwitch('disabled',false);
			}
			else{
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
			}
			$("#divUserProfileAddEdit .chosen-container b").css("display", "none");
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function getUserRolePermissions(roleId, isUserAction){
			if($('tbody tr td input[type="checkbox"]').hasClass('authorizationChk')){
				$('tbody tr td input[type="checkbox"]').prop({'checked': false, 'disabled': false});
			}

			var roleObj = $.grep(self.userRolesArr(), function(e){ return e.userRoleId == roleId; })[0];

			if(isUserAction){
				self.multipleLoginStatus(roleObj.allowMultipleLogin);
				if(self.isIndividualProfileView()){
					$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());
				}
			}
			

			if(!self.getUserPermissions()){
				if(!self.isIndividualProfileView()){
					$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',false);
					self.multipleLoginStatus(1);
					$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());

					$("[name='multipleLoginStatus']").closest('.bootstrap-switch')
		    		.attr('title', "").tooltip();

					if(roleObj.allowMultipleLogin == 0){
		        		//self.multipleLoginStatus(0);
						$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());

						$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);
						$("[name='multipleLoginStatus']").closest('.bootstrap-switch')
		    			.attr('title', uiConstants.userProfileConfig.MULTIPLE_LOGIN_OPTION_DISABLED_MSG).tooltip();

					}
					
				}
			}

			//showApplications
			if(roleObj.isAdmin == 1 || roleObj.isUserManager == 1){
				self.showApplications(false);
				self.showPermissions(false);
			}
			else{
				self.showApplications(true);
				self.showPermissions(true);
			}

			//if(roleId == 0){
			//	self.isRoleSelected(false);
			//}
			//else{
				self.isRoleSelected(true);

				/*$(".wrapper").scroll(function(){
					var translate = "translate(0,"+this.scrollTop+"px)";
					this.querySelector("thead").style.transform = translate;
	            });*/
            
				requestCall(uiConstants.common.SERVER_IP + "/role/permissions/"+roleId, "GET", "", "getUserRolePermissions", successCallback, errorCallback);

				//requestCall("http://www.mocky.io/v2/5804b37413000067036ab662?callback=?", "GET", "", "getUserRolePermissions", successCallback, errorCallback);
			//}
		}

		function getUserAdditionalPermissions(){
			requestCall(uiConstants.common.SERVER_IP + "/user/permissions/"+self.selectedConfigRows()[0].userProfileId, "GET", "", "getUserAdditionalPermissions", successCallback, errorCallback);
		}

		self.getRoleName.subscribe(function() {
			var $tab = $('#divUserProfileAddEdit table');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});
		});

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configObj[0]));
			
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.userFirstName("");
				self.userLastName("");
				self.userName("");
				self.emailId("");
				self.phoneCode("");
				self.phoneNum("");
				self.password("");
				self.confirmPassword("");
				self.selectedRole("");
				self.lockedReason("");
				self.getRoleName("");
			}
			else{
				self.userFirstName(configObj[0].userFirstName);
				self.userLastName(configObj[0].userLastName);
				self.configId(configObj[0].userProfileId);
				self.userName(configObj[0].userName);
				self.emailId(configObj[0].emailId);
				self.phoneCode(configObj[0].phoneCountryCode);
				self.phoneNum(configObj[0].phoneNumber);
				self.password(self.isIndividualProfileView() ? "" : configObj[0].password);
				self.confirmPassword(self.isIndividualProfileView() ? "" : configObj[0].password);
				self.selectedRole(configObj[0].userRoleName);
				self.lockedReason(configObj[0].lockedReason || uiConstants.common.NOT_APPLICABLE);
				self.getRoleName(configObj[0].userRoleName);
			}
			
			self.configStatus(configObj[0].status);

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
				if(self.lockedReason() != uiConstants.common.NOT_APPLICABLE && !self.configStatus()){
					document.getElementById("chkUnlockProfile").disabled = true;
				}
			}

			self.multipleLoginStatus(configObj[0].allowMultipleLogin);

			if(self.isIndividualProfileView()){
				setConfigUneditable(false);
			}
			else{
				/*self.multipleLoginStatus(0);
					$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());

					$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);
					$("[name='multipleLoginStatus']").closest('.bootstrap-switch')
	    			.attr('title', uiConstants.userProfileConfig.MULTIPLE_LOGIN_OPTION_DISABLED_MSG).tooltip();

*/

				$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());
				var roleObj = $.grep(self.userRolesArr(), function(e){ return e.userRoleId == configObj[0].userRoleId; })[0];

				if(roleObj.allowMultipleLogin == 0){
					//self.multipleLoginStatus(0);
					$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());

					$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);
					$("[name='multipleLoginStatus']").closest('.bootstrap-switch')
	    			.attr('title', uiConstants.userProfileConfig.MULTIPLE_LOGIN_OPTION_DISABLED_MSG).tooltip();
				}

				$('#configStatus').bootstrapSwitch('state',self.configStatus());
				console.log(self.userRolesArr());
				$("#rolesList").val(configObj[0].userRoleId).trigger('chosen:updated');
			}
		}

		this.cancelConfig = function(){
			if(self.isIndividualProfileView()){
        		hasher.setHash(curHasher);
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("User Profile Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function onMastersLoad(){
			if(applicationsLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
					if(!self.selectedConfigRows()[0].status){
						setConfigUneditable(true);
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getApplications"){
				self.applicationsArr(data.result);
				self.availableApplicationArr([]);
				self.selectedApplicationArr([]);

	        	for(app in self.applicationsArr()){
					self.availableApplicationArr.push({
						"id": self.applicationsArr()[app].applicationId,
						"name": self.applicationsArr()[app].applicationName
					});
				}

				$('#availableApplicationList').checklistbox({
				    data: self.availableApplicationArr()
				});

				$('#selectedApplicationList').checklistbox({
				    data: self.selectedApplicationArr()
				});

				if(self.currentViewIndex() != uiConstants.common.LIST_VIEW && self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
			        if(!self.isIndividualProfileView()){
			        	var inactiveAppsArr = removeArrayObjElements(self.selectedConfigRows()[0].applications, "applicationId", self.availableApplicationArr(), "id", true);
				        
				        if(inactiveAppsArr.length>0){
				        	for(inactiveApp in inactiveAppsArr){
				        		self.availableApplicationArr().push({
				        			"id": inactiveAppsArr[inactiveApp].applicationId, 
				        			"name": inactiveAppsArr[inactiveApp].applicationName,
				        			"isActive": 0
				        		});
				        	}

				        	$('#availableApplicationList').checklistbox({
							    data: self.availableApplicationArr()
							});
				        }

				        for(application in self.selectedConfigRows()[0].applications){
							$("#availableApplicationList .checkList[value=" + self.selectedConfigRows()[0].applications[application].applicationId + "]").prop("checked",true);
						}
						self.addToSelectedApplication();
			        }
			        else{
			        	for(application in self.selectedConfigRows()[0].applications){
							self.selectedApplicationArr().push({
			        			"id": self.selectedConfigRows()[0].applications[application].applicationId, 
			        			"name": self.selectedConfigRows()[0].applications[application].applicationName
			        		});
						}
			        	
			        	
			        	$('#indivProfViewSelectedAppList').checklistbox({
						    data: self.selectedApplicationArr()
						});
			        	$("#indivProfViewSelectedAppList .checkList").css("display","none");
			        	$("#indivProfViewSelectedAppList").css({"padding":"5px", "margin-left":"14px"});
			        }
			        
				}
				
				applicationsLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getScreenList"){
				self.screensArr(data.result);
				//screenListLoaded = 1;

				//onMastersLoad();

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					console.log(self.userRolesArr());
					if(!self.isIndividualProfileView()){
						getUserRolePermissions(self.selectedConfigRows()[0].userRoleId, false);
					}
					else{
						requestCall(uiConstants.common.SERVER_IP + "/userRoles?sortBy=userRoleName&orderBy=asc", "GET", "", "getUserRoles", successCallback, errorCallback);
						//requestCall("http://www.mocky.io/v2/5808a13510000061004c626b?callback=?", "GET", "", "getUserRoles", successCallback, errorCallback);
					}
				}
			}
			else if(reqType === "getUserRoles"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.userRolesArr(data.result.roles);
				getUserRolePermissions(self.selectedConfigRows()[0].userRoleId, false);
			}
  			else if(reqType === "getUserRolePermissions"){
  				if(data.result.permissions.length > 0){
  					if(!self.isIndividualProfileView()){
  						for(var userRolePermission in data.result.permissions){
	  						$("#read"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].readEnabled == 1,
	  							"disabled": data.result.permissions[userRolePermission].readEnabled == 1
	  						});
	  						$("#create"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].createEnabled == 1,
	  							"disabled": data.result.permissions[userRolePermission].createEnabled == 1
	  						});
	  						$("#update"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].updateEnabled == 1,
	  							"disabled": data.result.permissions[userRolePermission].updateEnabled == 1
	  						});
	  						$("#delete"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].deleteEnabled == 1,
	  							"disabled": data.result.permissions[userRolePermission].deleteEnabled == 1
	  						});
	  					}
  					}
  					else{
  						for(var userRolePermission in data.result.permissions){
	  						$("#glyphRead"+data.result.permissions[userRolePermission].screenId).addClass(data.result.permissions[userRolePermission].readEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphCreate"+data.result.permissions[userRolePermission].screenId).addClass(data.result.permissions[userRolePermission].createEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphUpdate"+data.result.permissions[userRolePermission].screenId).addClass(data.result.permissions[userRolePermission].updateEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphDelete"+data.result.permissions[userRolePermission].screenId).addClass(data.result.permissions[userRolePermission].deleteEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  					}
  					}
  				}
  				if(self.getUserPermissions() && self.currentViewIndex()!= uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex()!= uiConstants.common.CLONE_VIEW){
  					getUserAdditionalPermissions();
  				}
  			}
  			else if(reqType === "getUserAdditionalPermissions"){
  				if(data.result.permissions.length > 0){
  					if(!self.isIndividualProfileView()){
  						for(var userRolePermission in data.result.permissions){
	  						$("#read"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].readEnabled == 1
	  						});
	  						$("#create"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].createEnabled == 1
	  						});
	  						$("#update"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].updateEnabled == 1
	  						});
	  						$("#delete"+data.result.permissions[userRolePermission].screenId).prop({
	  							"checked": data.result.permissions[userRolePermission].deleteEnabled == 1
	  						});
	  					}
  					}
  					else{
  						for(var userRolePermission in data.result.permissions){
	  						$("#glyphRead"+data.result.permissions[userRolePermission].screenId).removeClass("glyphicon-ok").removeClass("glyphicon-remove").addClass(data.result.permissions[userRolePermission].readEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphCreate"+data.result.permissions[userRolePermission].screenId).removeClass("glyphicon-ok").removeClass("glyphicon-remove").addClass(data.result.permissions[userRolePermission].createEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphUpdate"+data.result.permissions[userRolePermission].screenId).removeClass("glyphicon-ok").removeClass("glyphicon-remove").addClass(data.result.permissions[userRolePermission].updateEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  						$("#glyphDelete"+data.result.permissions[userRolePermission].screenId).removeClass("glyphicon-ok").removeClass("glyphicon-remove").addClass(data.result.permissions[userRolePermission].deleteEnabled ? "glyphicon-ok" : "glyphicon-remove");
	  					}
  					}
  				}

  				if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows()[0] && self.selectedConfigRows()[0].status == 0)){
					$("#divAuthorization").find("input").attr("disabled", "disabled");
  				}
  			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userProfileConfig.ERROR_ADD_USER_PROFILE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userProfileConfig.SUCCESS_ADD_USER_PROFILE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userProfileConfig.ERROR_UPDATE_USER_PROFILE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userProfileConfig.SUCCESS_UPDATE_USER_PROFILE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "indivUserProfileChange"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userProfileConfig.ERROR_UPDATE_USER_PROFILE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userProfileConfig.SUCCESS_UPDATE_USER_PROFILE);
	    			localStorage.apptoken = data.authToken;
					self.cancelConfig();
				}
			}
			else if(reqType === "resetPassword"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userProfileConfig.ERROR_RESET_USER_PASSWORD, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userProfileConfig.SUCCESS_UPDATE_USER_PASSWORD_RESET);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
  			else if(reqType === "getScreenList"){
  				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_SCREEN_LIST, "error");
  			}
  			else if(reqType === "getUserRolePermissions"){
				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_USER_ROLE_PERMISSION, "error");
  			}
  			else if(reqType === "getUserAdditionalPermissions"){
				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_USER_ADDITIONAL_PERMISSION, "error");
  			}
  			else if(reqType === "getUserRoles"){
  				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_USER_ROLES, "error");
  			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.userProfileConfig.ERROR_ADD_USER_PROFILE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.userProfileConfig.ERROR_UPDATE_USER_PROFILE, "error");
			}
			else if(reqType === "indivUserProfileChange"){
				showMessageBox(uiConstants.userProfileConfig.ERROR_UPDATE_USER_PROFILE, "error");
			}
			else if(reqType === "resetPassword"){
				showMessageBox(uiConstants.userProfileConfig.ERROR_RESET_USER_PASSWORD, "error");
			}
		}
	}

	UserProfileAddEdit.prototype.dispose = function() { };
	return { viewModel: UserProfileAddEdit, template: templateMarkup };
});