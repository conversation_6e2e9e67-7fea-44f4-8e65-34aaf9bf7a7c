<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

 <div class="panel panel-default" id="divUserProfileAddEdit">
	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">First Name <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<input type="text" class="form-control" id="txtFirstName" data-bind="value: userFirstName" placeholder="Enter First Name" max="45" autofocus required>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: userFirstName"></span>
					<!-- /ko-->
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Last Name </label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<input type="text" class="form-control" id="txtLastName" data-bind="value: userLastName" placeholder="Enter Last Name" max="45" autofocus required>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: userLastName"></span>
					<!-- /ko-->
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">User Name <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<input type="text" class="form-control" id="txtUserName" data-bind="value: userName" placeholder="Enter User Name" max="45" autofocus required>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: userName"></span>
					<!-- /ko-->
				</div>

				<!-- ko if: currentViewIndex() == uiConstants.common.EDIT_VIEW && uiConstants.common.USER_ROLE.toLowerCase() == 'admin' -->
					<button class="btn-small" data-bind="event:{click: function(){resetUserPassword()}}" type ="button">Reset Password</button>
				<!-- /ko -->
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Phone</label>

				<!-- ko ifnot: isIndividualProfileView() -->
					<div class="col-sm-1" style="display: flex;">
						<label style="margin-right:3px; padding-top: 5px;">+ </label>
						<input type="number" class="form-control positive-integer" id="txtPhoneCode" data-bind="value: phoneCode" max="4" autofocus required>
					</div>

					<div class="col-sm-3" style="display: flex; padding-left: 0px;">
						<input type="number" style="margin-left: 0px;" class="form-control positive-integer" id="txtPhoneNum" data-bind="value: phoneNum" max="10" autofocus required>
					</div>
				<!-- /ko-->

				<!-- ko if: isIndividualProfileView() -->
					<div class="col-sm-4">
						<span data-bind="text: '(+'+phoneCode()+') '+phoneNum()"></span>
					</div>
				<!-- /ko-->
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Email ID <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<input type="text" class="form-control" id="txtEmailId" data-bind="value: emailId" placeholder="Enter Email ID" max="60" autofocus required>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: emailId"></span>
					<!-- /ko-->
				</div>
			</div>

			<!-- ko ifnot: isIndividualProfileView() -->
				<!-- <div class="form-group form-required">
					<label class="control-label col-sm-2">Password <span class="mandatoryField">*</span></label>
					<div class="col-sm-4">
						<input type="password" class="form-control" id="txtPassword" data-bind="value: password" placeholder="Enter Password" max="45" autofocus required>
					</div>
				</div>

				<div class="form-group form-required">
					<label class="control-label col-sm-2">Confirm Password <span class="mandatoryField">*</span></label>
					<div class="col-sm-4">
						<input type="password" class="form-control" id="txtConfirmPassword" data-bind="value: confirmPassword" placeholder="Re-enter Password" max="45" autofocus required>
					</div>
				</div> -->
			<!-- /ko-->

			<!-- ko if: isIndividualProfileView() -->
				<div class="form-group form-required">
					<fieldset class="config-border col-sm-6" style="margin-left: 20px">
					    <legend class="config-border">Change Password:</legend>
				    	<div class="form-group form-required">
							<label class="control-label col-sm-4" style="margin-left: -20px;">Old Password <span class="mandatoryField">*</span></label>
							<div class="col-sm-8">
								<input type="password" class="form-control" id="txtOldPassword" data-bind="value: oldPassword" placeholder="Enter Old Password" max="45" autofocus required>
							</div>
						</div>

						<div class="form-group form-required">
							<label class="control-label col-sm-4" style="margin-left: -20px;">New Password <span class="mandatoryField">*</span></label>
							<div class="col-sm-8">
								<input type="password" class="form-control" id="txtPassword" data-bind="value: password" placeholder="Enter New Password" max="45" autofocus required>

							</div>

								<span class="glyphicon glyphicon-question-sign glyphicon-question-style" style="left: -10px; top: 7px;" title="Password should contain minimum 8 & maximum 15 characters with atleast one of each – special character, numeral, lower case and upper case alphabets"></span>

						</div>

						<div class="form-group form-required">
							<label class="control-label col-sm-4" style="margin-left: -20px;">Confirm Password <span class="mandatoryField">*</span></label>
							<div class="col-sm-8">
								<input type="password" class="form-control" id="txtConfirmPassword" data-bind="value: confirmPassword" placeholder="Re-enter Password" max="45" autofocus required>
							</div>
						</div>
					</fieldset>
				</div>
			<!-- /ko-->

			<!-- <div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
						<input type="checkbox" id="configStatus" data-bind="checked: configStatus">

						<span data-bind="text: configStatus ? 'Active' : 'Inactive'"></span>
				</div>
			</div> -->

			<!-- ko ifnot: isIndividualProfileView() -->
				<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
					<label class="control-label col-sm-2" >Status </label>
					<div class="col-sm-4">

							<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

							<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">

					</div>
				</div>
			<!-- /ko-->

			<!-- ko ifnot: isIndividualProfileView() -->
				<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
					<label class="control-label col-sm-2" >Locked Reason </label>
					<div class="col-sm-4">
						<span data-bind="text: lockedReason"></span>

						<!-- ko ifnot: lockedReason() == uiConstants.common.NOT_APPLICABLE -->
							<span data-bind="visible: currentViewIndex() != 5" style="margin-left: 15px;"><input type="checkbox" id="chkUnlockProfile" data-bind="checked: unlockProfile"> Unlock</span>
						<!-- /ko-->
					</div>
				</div>
			<!-- /ko-->

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Role <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<select class="chosen form-control" id="rolesList" data-bind="foreach : userRolesArr, event:{change: onRoleChange}" data-placeholder=" ">
							<!-- ko if: $index() == 0 -->
								<option data-bind="value: 0, text: uiConstants.common.SELECT_USER_ROLE"></option>
							<!-- /ko-->

							<option data-bind="value: $data.userRoleId, text: $data.userRoleName"></option>
						</select>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: selectedRole"></span>
					<!-- /ko-->
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Allow Multiple Login for User <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<!-- ko ifnot: isIndividualProfileView() -->
						<input type="checkbox" id="multipleLoginStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="multipleLoginStatus">
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<span data-bind="text: multipleLoginStatus() ? 'Yes' : 'No'"></span>
					<!-- /ko-->
				</div>
			</div>

			<div class="form-group form-required" data-bind="visible:getRoleName() == 'Admin'">

				<label class="control-label col-sm-2">Application</label>
				<div class="col-sm-4">
					<label>All applications available</label>
				</div>
			</div>

			<div class="form-group form-required"  data-bind="visible:getRoleName() == 'Admin'">
				<label class="control-label col-sm-2">Authorization</label>
				<div class="col-sm-4">
					<label>All authorization available</label>
				</div>
			</div>

				<div class="form-group form-required" data-bind="visible: showApplications()">
					<label class="control-label col-sm-2">Application <span data-bind="visible: !isIndividualProfileView()" class="mandatoryField">*</span></label>
					<!-- ko ifnot: isIndividualProfileView() -->
						<div id="appDiv" class="col-sm-6">
							<table class="checklist-div-table">
								<tr>
									<td style="width: 270px">
										<label style="margin-left: 5px;">Available:</label>
										<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
											<label style="font-weight: normal;"><input type="checkbox" id="selAllAvailApplication" style="margin-left: 5px;" data-bind="attr: {disabled: availableApplicationArr().length == 0}">Select All</input></label>
										</div>
									</td>

									<td style="width: 40px"></td>

									<td style="width: 270px">
										<label style="margin-left: 5px;">Selected:</label>
										<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
											<label style="font-weight: normal;"><input type="checkbox" id="selAllSelApplication" style="margin-left: 5px;" data-bind="attr: {disabled: selectedApplicationArr().length == 0}">Select All</input></label>
										</div>
									</td>
								</tr>

								<tr>
									<td>
										<div class="inner-div-container" id="availableApplicationList"></div>
									</td>

									<td style="padding: 5px;">
										<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddApplicationBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddApplicationBtn()}, event:{click: addToSelectedApplication}"></button>
										<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveApplicationBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveApplicationBtn()}, event:{click: addToAvailableApplication}"></button>
									</td>

									<td>
										<div class="inner-div-container" id="selectedApplicationList"></div>
									</td>
								</tr>
							</table>
						</div>
					<!-- /ko-->

					<!-- ko if: isIndividualProfileView() -->
						<div class="col-sm-4 inner-div-container" id="indivProfViewSelectedAppList"></div>
					<!-- /ko-->

					<!-- <label class="control-label col-sm-2">Application <span class="mandatoryField">*</span></label>
					<div id="appDiv" class="col-sm-4">
						<label style="font-weight: normal;" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW"><input type="checkbox" id="selAllApps" style="margin-left: 5px;">Select All</input></label>
						<div class="inner-div-container" id="applicationsList"></div>
					</div> -->
				</div>

			<!-- ko if: isRoleSelected() -->
				<div id="divAuthorization" class="form-group form-required" data-bind="visible: showPermissions()">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading" style="margin-bottom: 10px;">Authorization</div>
						<div class="panel-body wrapper-scroll-table" style="padding-top: 0px; padding-bottom: 0px; height: 500px;">
							<table class="table table-hover table-striped a1-inner-table" style="width: auto;">
								<thead>
									<tr class="a1-inner-table-thead">
										<th style="width: 100%">Screens</th>
										<th style="min-width: 100px;">Read</th>
										<th style="min-width: 100px;">Create</th>
										<th style="min-width: 100px;">Update</th>
										<th style="min-width: 100px;">Delete</th>
									</tr>
								</thead>

								<tbody data-bind="foreach: screensArr, attr: {id: $data.screenId}">
									<tr>
										<td data-bind="text: $data.screenName"></td>

										<!-- ko ifnot: $parent.isIndividualProfileView() -->
											<td style="text-align:center">
												<input type="checkbox" class="authorizationChk" data-bind="attr: {'id': 'read'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
											</td>
											<td style="text-align:center">
												<input type="checkbox" class="authorizationChk" data-bind="attr: {'id': 'create'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
											</td>
											<td style="text-align:center">
												<input type="checkbox" class="authorizationChk" data-bind="attr: {'id': 'update'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
											</td>
											<td style="text-align:center">
												<!-- ko if: slugify($data.screenName).indexOf('role') == -1 -->
													<span style="font-style: italic;">--NA--</span>
												<!-- /ko -->

												<!-- ko if: slugify($data.screenName).indexOf('role') != -1 -->
													<input type="checkbox" class="authorizationChk" data-bind="attr: {'id': 'delete'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
												<!-- /ko -->
											</td>
										<!-- /ko-->

										<!-- ko if: $parent.isIndividualProfileView() -->
											<td style="text-align:center">
												<span data-bind="attr: {'id': 'glyphRead'+$data.screenId}" class="glyphicon"></span>
											</td>
											<td style="text-align:center">
												<span data-bind="attr: {'id': 'glyphCreate'+$data.screenId}" class="glyphicon"></span>
											</td>
											<td style="text-align:center">
												<span data-bind="attr: {'id': 'glyphUpdate'+$data.screenId}" class="glyphicon"></span>
											</td>
											<td style="text-align:center">

												<!-- ko if: slugify($data.screenName).indexOf('role') == -1 -->
													<span style="font-style: italic;">--NA--</span>
												<!-- /ko -->

												<!-- ko if: slugify($data.screenName).indexOf('role') != -1 -->
													<span data-bind="attr: {'id': 'glyphDelete'+$data.screenId}" class="glyphicon"></span>
												<!-- /ko -->
											</td>
										<!-- /ko-->
									</tr>
								</tbody>
							</table>
						</div>

					</div>
				</div>
			<!-- /ko-->

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>