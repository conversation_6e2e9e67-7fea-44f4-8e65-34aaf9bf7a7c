<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

 <div class="panel panel-default" id="divClusterAddEdit">
	<!-- <div class="panel-heading" data-bind="visible: !isModal()"><h4><span data-bind="text: pageSelected"></span></h4></div> -->

	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<!-- <div id="divClusterDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: description" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div> -->

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Component Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="compTypeList" data-bind="event:{change: function(){checkInactive($data.componentsArr(), '#compTypeList', 'componentTypeId')}}, foreach : componentsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_TYPE"></option>
						<!-- /ko-->

						<option data-bind="value: $data.componentTypeId, text: $data.componentType, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Component <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="compNamesList" data-bind="event:{change: function(){checkInactive($data.componentNamesArr(), '#compNamesList', 'componentId')}}, foreach : componentNamesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_NAME"></option>
						<!-- /ko-->
						
						<option data-bind="value: $data.componentId, text: $data.componentName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>

				<button type="button" class="glyphicon glyphicon-eye-open" style="top: 5px;" data-toggle="modal" data-target="#idModal" data-bind="visible: isFirstModal(), attr: {disabled: !enableViewClusterOp(), title: uiConstants.common.VIEW_CLUSTER_OPERTATION}, css: {confButtonDisabled: !enableViewClusterOp()}, event:{click: viewClusterOperation}"></button>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Application</label>
				<div id="appDiv" class="col-sm-6">
					<table class="checklist-div-table">
						<tr>
							<td style="width: 270px">
								<label style="margin-left: 5px;">Available:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" id="selAllAvailApplication" style="margin-left: 5px;" data-bind="attr: {disabled: availableApplicationArr().length == 0}">Select All</input></label>
								</div>
							</td>

							<td style="width: 40px"></td>

							<td style="width: 270px">
								<label style="margin-left: 5px;">Selected:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" id="selAllSelApplication" style="margin-left: 5px;" data-bind="attr: {disabled: selectedApplicationArr().length == 0}">Select All</input></label>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<div class="inner-div-container" id="availableApplicationList"></div>
							</td>

							<td style="padding: 5px;">
								<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddApplicationBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddApplicationBtn()}, event:{click: addToSelectedApplication}"></button>
								<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveApplicationBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveApplicationBtn()}, event:{click: addToAvailableApplication}"></button>
							</td>

							<td>
								<div class="inner-div-container" id="selectedApplicationList"></div>
							</td>
						</tr>
					</table>
				</div>
				<!-- <label class="control-label col-sm-2">Application <span class="mandatoryField">*</span></label>
				<div id="appDiv" class="col-sm-4">
					<label style="font-weight: normal;" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW"><input type="checkbox" id="selAllApps" style="margin-left: 5px;">Select All</input></label>
					<div class="inner-div-container" id="applicationsList"></div>
				</div> -->
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Component Instance</label>
				<div id="compInstDiv" class="col-sm-6">
					<table class="checklist-div-table">
						<tr>
							<td style="width: 270px">
								<label style="margin-left: 5px;">Available:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" id="selAllAvailCompInst" style="margin-left: 5px;" data-bind="attr: {disabled: availableCompInstArr().length == 0}">Select All</input></label>
								</div>
							</td>

							<td style="width: 40px"></td>

							<td style="width: 270px">
								<label style="margin-left: 5px;">Selected:</label>
								<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW">
									<label style="font-weight: normal;"><input type="checkbox" id="selAllSelCompInst" style="margin-left: 5px;" data-bind="attr: {disabled: selectedCompInstArr().length == 0}">Select All</input></label>
								</div>
							</td>
						</tr>

						<tr>
							<td>
								<div class="inner-div-container" id="availableCompInstList"></div>
							</td>

							<td style="padding: 5px;">
								<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddCompInstBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddCompInstBtn()}, event:{click: addToSelectedCompInst}"></button>
								<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveCompInstBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveCompInstBtn()}, event:{click: addToAvailableCompInst}"></button>
							</td>

							<td>
								<div class="inner-div-container" id="selectedCompInstList"></div>
							</td>
						</tr>
					</table>
				</div>
				<!-- <label class="control-label col-sm-2">Component Instance <span class="mandatoryField">*</span></label>
				<div id="compInstDiv" class="col-sm-4">
					<label style="font-weight: normal;" data-bind="visible: currentViewIndex() != READ_VIEW"><input type="checkbox" id="selAllCompInst" style="margin-left: 5px;">Select All</input></label>
					<button type="button" class="glyphicon glyphicon-refresh" data-bind="visible: currentViewIndex() != READ_VIEW, event:{click: loadComponentInstances}, attr:{'title': 'Load Component Instances'}" style="float: right; margin-right: 5px;"></button>
					<div class="inner-div-container" style="height: 150px" id="compInstList"></div>
				</div> -->
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="cluster-tokenfield-typeahead" data-bind="value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->
					
					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>

			<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
		   		<div class="modal-dialog modal-lg">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4>Cluster Operation for Component: <span data-bind="text: selCompName"></span></h4>
			            </div>	
			            <!-- <div data-bind="component: comp"> -->
			            <div class="modal-body" style="height: 40em;">
			            	<div class="col-sm-12 wrapper-scroll-table" style="height: 37em;">
				        		<table class="table table-bordered table-hover table-striped" style="width:100%;">
									<thead>
										<tr class="a1-inner-table-thead">
											<th>KPI</th>
											<th>Cluster Operation</th>
											<th>Data Type</th>
										</tr>
									</thead>

									<tbody data-bind="foreach: clusterOpGridData">
										<tr>
											<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.kpiName, attr:{'title': $data.kpiName}"></td>

											<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.clusterOperation, attr:{'title': $data.clusterOperation}"></td>

											<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.kpiDataType, attr:{'title': $data.kpiDataType}"></td>
										</tr>
									</tbody>
								</table>
							</div>
			        	</div>
			        </div>
		        </div> <!-- /.modal-content -->
		    </div>
		</form>
	</div>
</div>