define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./cluster-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,floatThead) {

	function ClusterAddEdit(params) {
		var self = this;
		
		this.componentNamesArr = ko.observableArray([{}]);
		this.applicationsArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(true);
		var configTagLoaded = 0;
		var applicationsLoaded = 0;
		var compTypeVersionLoaded = 0;
		this.pageSelected = params.pageSelected;
  		this.displayComponent = ko.observable(false);
		var appArr = [];
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		this.enableViewClusterOp = ko.observable(false);
  		this.clusterOpGridData = ko.observableArray();
  		this.selCompName = ko.observable();
		this.componentTypeId = ko.observable("0");
		this.componentNameId = ko.observable("0");
		this.isModal = ko.observable(false);
		this.isFirstModal = ko.observable(true);
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		this.availableApplicationArr = ko.observableArray();
		this.selectedApplicationArr = ko.observableArray();
  		this.enableAddApplicationBtn = ko.observable(false);
  		this.enableRemoveApplicationBtn = ko.observable(false);
		this.availableCompInstArr = ko.observableArray();
		this.selectedCompInstArr = ko.observableArray();
  		this.enableAddCompInstBtn = ko.observable(false);
  		this.enableRemoveCompInstBtn = ko.observable(false);
  		this.setSelCompInstances = ko.observable(true);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.componentsArr = ko.observableArray(getComponentsArr());

		//this.description = ko.observable();
  		var previousComponentTypeId;
  		var previousComponentId;
		var rowToEdit;

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			$('#idModal').on('shown.bs.modal', function (e) {
				var $tab = $('#divClusterAddEdit table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			});

			if(params.isModal){
				self.isModal(true);
				self.isFirstModal(false);
			}
			else{
				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
					self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["clusterName"]));
				}
			}

			$('.panel-body #cluster-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {

					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#cluster-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#cluster-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			/*$(".panel-body #compTypeList").on('change', function () {
				self.componentTypeId($(this).val());
				self.componentNameId("0");
		    	setCompNames();
			});*/

			$(".panel-body #compTypeList").on('chosen:showing_dropdown', function () {
			        previousComponentTypeId = this.value;
			    }).on('change', function () {
					if($('#selectedCompInstList').getAllValues().length == 0){
			    		previousComponentTypeId = this.value;
			    		self.componentTypeId($(this).val());
			    		setCompNames();
			    	}
			    	else{
			    		showMessageBox(uiConstants.clusterConfig.CONFIRM_COMPTYPE_CLEAR_COMPINST, "question", "confirm", function confirmCallback(confirmClear){
							if (confirmClear){
								self.componentTypeId($("#compTypeList").val());
								clearCompInstances();
								setCompNames();
				    		}
				    		else{
				    			$("#compTypeList").val(previousComponentTypeId).trigger('chosen:updated');
				    		}
				    	});
			    	}
				//$("#compNamesList_chosen span").first().removeClass("inactiveOptionClass");
			});

			$(".panel-body #compNamesList").on('chosen:showing_dropdown', function () {
			        previousComponentId = this.value;
			    }).on('change', function () {
					if($('#selectedCompInstList').getAllValues().length == 0){
			    		previousComponentId = this.value;
						self.componentNameId($(this).val());
			    		self.loadComponentInstances();
			    	}
			    	else{
			    		showMessageBox(uiConstants.clusterConfig.CONFIRM_COMPNAME_CLEAR_COMPINST, "question", "confirm", function confirmCallback(confirmClear){
							if (confirmClear){
								self.componentNameId($("#compNamesList").val());
								clearCompInstances();
				    			self.loadComponentInstances();
				    		}
				    		else{
				    			$("#compNamesList").val(previousComponentId).trigger('chosen:updated');
				    		}
				    	});
			    	}

			    	if(self.componentNameId() == 0){
						self.enableViewClusterOp(false);
					}
					else{
						self.enableViewClusterOp(true);
					}
				//$("#compNamesList_chosen span").first().removeClass("inactiveOptionClass");
			});

		    

			$("div").on("click", "#selAllAvailApplication", function(e){
				$("#availableApplicationList .checkList").prop("checked", $("#selAllAvailApplication").prop("checked"));
				self.enableAddApplicationBtn($("#availableApplicationList .checkList:checked").length);
			});

			$("div").on("change", "#availableApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllAvailApplication").prop("checked", self.availableApplicationArr().length == $("#availableApplicationList .checkList:checked").length);
					self.enableAddApplicationBtn($("#availableApplicationList .checkList:checked").length);
				}
	        });


	        $("div").on("click", "#selAllSelApplication", function(e){
				$("#selectedApplicationList .checkList").prop("checked", $("#selAllSelApplication").prop("checked"));
				self.enableRemoveApplicationBtn($("#selectedApplicationList .checkList:checked").length);
			});

			$("div").on("click", "#selectedApplicationList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllSelApplication").prop("checked", self.selectedApplicationArr().length == $("#selectedApplicationList .checkList:checked").length);
					self.enableRemoveApplicationBtn($("#selectedApplicationList .checkList:checked").length);
				}
			});

			$("div").on("click", "#selAllAvailCompInst", function(e){
				$("#availableCompInstList .checkList").prop("checked", $("#selAllAvailCompInst").prop("checked"));
				self.enableAddCompInstBtn($("#availableCompInstList .checkList:checked").length);
			});

			$("div").on("change", "#availableCompInstList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllAvailCompInst").prop("checked", self.availableCompInstArr().length == $("#availableCompInstList .checkList:checked").length);
					self.enableAddCompInstBtn($("#availableCompInstList .checkList:checked").length);
				}
	        });


	        $("div").on("click", "#selAllSelCompInst", function(e){
				$("#selectedCompInstList .checkList").prop("checked", $("#selAllSelCompInst").prop("checked"));
				self.enableRemoveCompInstBtn($("#selectedCompInstList .checkList:checked").length);
			});

			$("div").on("click", "#selectedCompInstList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllSelCompInst").prop("checked", self.selectedCompInstArr().length == $("#selectedCompInstList .checkList:checked").length);
					self.enableRemoveCompInstBtn($("#selectedCompInstList .checkList:checked").length);
				}
			});

			$('#compNamesList').prop('disabled', true).trigger("chosen:updated");

			requestCall(uiConstants.common.SERVER_IP + "/tag?type=Cluster", "GET", "", "getClusterTag", successCallback, errorCallback);
			//requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getApplications", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575b0eb6110000b421539fbb?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575bc3700f0000610e4fc988?callback=?", "GET", "", "getApplications", successCallback, errorCallback);
		}

		function getComponentsArr(){
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				return getMasterList(params.componentsArr(), "componentTypeId", [self.selectedConfigRows()[0].componentTypeId], true);
			}
			else{
				return getMasterList(params.componentsArr(), "componentTypeId", null, false);
			}
		}

		function clearCompInstances(){
	    	self.selectedCompInstArr([]);
			self.availableCompInstArr([]);

			$('#selectedCompInstList').checklistbox({
			    data: self.selectedCompInstArr()
			});

			$('#availableCompInstList').checklistbox({
			    data: self.availableCompInstArr()
			});

			$("#selAllAvailCompInst").prop("checked",false);
			$("#selAllSelCompInst").prop("checked",false);
	    }

		this.addToSelectedApplication = function(){
			var availArr = $('#availableApplicationList').getSelectedValues();

			var applicationObj;
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableApplicationArr());

			for(arr in availArr){
				applicationObj = $.grep(self.availableApplicationArr(), function(e){ return e.id == availArr[arr]; });
				self.availableApplicationArr.splice(self.availableApplicationArr.indexOf(applicationObj[0]), 1);
				self.selectedApplicationArr.push(applicationObj[0]);
			}

			$('#selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableAddApplicationBtn(false);
			self.loadComponentInstances();
		}

		this.addToAvailableApplication = function(){
			var selArr = $('#selectedApplicationList').getSelectedValues();
			var applicationObj;
			for(arr in selArr){
				applicationObj = $.grep(self.selectedApplicationArr(), function(e){ return e.id == selArr[arr]; });
				self.selectedApplicationArr.splice(self.selectedApplicationArr.indexOf(applicationObj[0]), 1);
				self.availableApplicationArr.push(applicationObj[0]);
			}

			$('#selectedApplicationList').checklistbox({
			    data: self.selectedApplicationArr()
			});

			$('#availableApplicationList').checklistbox({
			    data: self.availableApplicationArr()
			});

			uncheckApplication();
			self.enableRemoveApplicationBtn(false);
			self.loadComponentInstances();
		}

		function uncheckApplication(){
			$("#selectedApplicationList .checkList").prop("checked",false);
			$("#availableApplicationList .checkList").prop("checked",false);
			$("#selAllAvailApplication").prop("checked",false);
			$("#selAllSelApplication").prop("checked",false);
		}

		this.addToSelectedCompInst = function(){
			var availArr = $('#availableCompInstList').getSelectedValues();

			var compInstObj;
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableCompInstArr());

			for(arr in availArr){
				compInstObj = $.grep(self.availableCompInstArr(), function(e){ return e.id == availArr[arr]; });
				self.availableCompInstArr.splice(self.availableCompInstArr.indexOf(compInstObj[0]), 1);
				self.selectedCompInstArr.push(compInstObj[0]);
			}

			$('#selectedCompInstList').checklistbox({
			    data: self.selectedCompInstArr()
			});

			$('#availableCompInstList').checklistbox({
			    data: self.availableCompInstArr()
			});

			uncheckCompInst();
			self.enableAddCompInstBtn(false);
		}

		this.addToAvailableCompInst = function(){
			var selArr = $('#selectedCompInstList').getSelectedValues();
			var compInstObj;
			for(arr in selArr){
				compInstObj = $.grep(self.selectedCompInstArr(), function(e){ return e.id == selArr[arr]; });
				self.selectedCompInstArr.splice(self.selectedCompInstArr.indexOf(compInstObj[0]), 1);
				self.availableCompInstArr.push(compInstObj[0]);
			}

			$('#selectedCompInstList').checklistbox({
			    data: self.selectedCompInstArr()
			});

			$('#availableCompInstList').checklistbox({
			    data: self.availableCompInstArr()
			});

			uncheckCompInst();
			self.enableRemoveCompInstBtn(false);
		}

		function uncheckCompInst(){
			$("#selectedCompInstList .checkList").prop("checked",false);
			$("#availableCompInstList .checkList").prop("checked",false);
			$("#selAllAvailCompInst").prop("checked",false);
			$("#selAllSelCompInst").prop("checked",false);
		}

        $("div").on("click", "#selAllCompInst", function(e){
			$("#compInstList .checkList").prop("checked", $("#selAllCompInst").prop("checked"));
		});

		$("div").on("change", "#compInstList .checkList", function(e){
			if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
				$("#selAllCompInst").prop("checked", self.availableCompInstArr().length == $("#compInstList .checkList:checked").length);
			}
        });

        this.checkInactive = function(dataObj, configElement, configId){
        	//optionsCheckInactive(dataObj, configElement, configId);
        }

        self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		//Adding/Updating single cluster
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];

			self.configName(self.configName().trim());
			//$("#divClusterDescription #txtDescription").val($("#divClusterDescription #txtDescription").val().trim());

			if(self.configName() == undefined || self.configName() == ""){
				//self.errorMsg(uiConstants.clusterConfig.CLUSTER_NAME_REQUIRED);
				showError("#divClusterAddEdit #txtName", uiConstants.clusterConfig.CLUSTER_NAME_REQUIRED);
			    self.errorMsg("#divClusterAddEdit #txtName");
			}
			else if(self.configName().length < 2){
				//self.errorMsg(uiConstants.clusterConfig.CLUSTER_NAME_MIN_LENGTH_ERROR);
				showError("#divClusterAddEdit #txtName", uiConstants.clusterConfig.CLUSTER_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divClusterAddEdit #txtName");
			}
			else if(self.configName().length > 45){
				//self.errorMsg(uiConstants.clusterConfig.CLUSTER_NAME_MAX_LENGTH_ERROR);
				showError("#divClusterAddEdit #txtName", uiConstants.clusterConfig.CLUSTER_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divClusterAddEdit #txtName");
			}
			else if(!nameValidation(self.configName())){
				//self.errorMsg(uiConstants.clusterConfig.CLUSTER_NAME_INVALID_ERROR);
				showError("#divClusterAddEdit #txtName", uiConstants.clusterConfig.CLUSTER_NAME_INVALID_ERROR);
			    self.errorMsg("#divClusterAddEdit #txtName");
			}
			/*else if($("#divClusterDescription #txtDescription").val() == ""){
				self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
			}
			else if($("#divClusterDescription #txtDescription").val().length < 25){
				self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			}
			else if($("#divClusterDescription #txtDescription").val().length > 256){
				self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			}*/
			if(self.componentTypeId() == "0"){
				//self.errorMsg(uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				showError("#divClusterAddEdit #compTypeList_chosen", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				showError("#divClusterAddEdit #compTypeList_chosen span", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
			    self.errorMsg("#divClusterAddEdit #compTypeList_chosen");
			}
			else{
				removeError("#divClusterAddEdit #compTypeList_chosen");
				removeError("#divClusterAddEdit #compTypeList_chosen span");
			}
			if(self.componentNameId() == "0"){
				//self.errorMsg(uiConstants.common.SELECT_COMPONENT_MSG);
				showError("#divClusterAddEdit #compNamesList_chosen", uiConstants.common.SELECT_COMPONENT_MSG);
				showError("#divClusterAddEdit #compNamesList_chosen span", uiConstants.common.SELECT_COMPONENT_MSG);
			    self.errorMsg("#divClusterAddEdit #compNamesList_chosen");
			}
			else{
				removeError("#divClusterAddEdit #compNamesList_chosen");
				removeError("#divClusterAddEdit #compNamesList_chosen span");
			}

			removeError("#divClusterAddEdit .tokenfield");
			removeError("#divClusterAddEdit #cluster-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divClusterAddEdit #cluster-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divClusterAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divClusterAddEdit #cluster-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divClusterAddEdit .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divClusterAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divClusterAddEdit #cluster-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divClusterAddEdit .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divClusterAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divClusterAddEdit #cluster-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divClusterAddEdit .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divClusterAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divClusterAddEdit #cluster-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divClusterAddEdit .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}

					var compObj = {
						"index":1,
						"clusterName": self.configName().trim(),
						//"description": self.description(),
						"componentTypeId": parseInt(self.componentTypeId()),
						"componentId":  parseInt(self.componentNameId()),
						"applicationIds": $("#selectedApplicationList").getAllValues().map(function (x){return parseInt(x);}),
						"componentInstanceIds": $("#selectedCompInstList").getAllValues().map(function (x){return parseInt(x);}),
						"tags": tagsObjArr,
						"status" : self.configStatus()?1:0};

					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(compObj));

					if(self.configId() == 0)
						requestCall(uiConstants.common.SERVER_IP + "/cluster", "POST", JSON.stringify(compObj), "addSingleConfig", successCallback, errorCallback);
					else
						requestCall(uiConstants.common.SERVER_IP + "/cluster/" + self.configId(), "PUT", JSON.stringify(compObj), "editSingleConfig", successCallback, errorCallback);
				}
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			//$('#txtDescription').prop('readonly', true);
			$('#cluster-tokenfield-typeahead').tokenfield('readonly');
			$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
			$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
			$("#appDiv").find("input,button,select").attr("disabled", "disabled");
			$("#availableApplicationList").addClass("checklist-disabled");
			$("#selectedApplicationList").addClass("checklist-disabled");
			$("#availableCompInstList").addClass("checklist-disabled");
			$("#selectedCompInstList").addClass("checklist-disabled");

			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divClusterAddEdit .chosen-container b").css("display", "none");
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);
			
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				//self.description("");
			}
			else{
				self.configName(configObj[0].clusterName);
				self.configId(configObj[0].clusterId);
				//self.description(configObj[0].description);
			}

			

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(self.componentNamesArr());
				/*if(!self.componentsArr().find( function( ele ) {return ele.componentTypeId && ele.componentTypeId === configObj[0].componentTypeId;} )){
					self.componentsArr.push({
						"componentTypeId": configObj[0].componentTypeId,
						"componentType": configObj[0].componentType,	
						"isActive": false
					});
					$("#compTypeList_chosen span").first().addClass("inactiveOptionClass");
				}*/

				if($.grep(self.componentsArr(), function(e){ return  e.componentTypeId == configObj[0].componentTypeId; }).length){
					$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
					self.componentTypeId(configObj[0].componentTypeId);

				}
				else{
					$("#compTypeList").val("0").trigger('chosen:updated');
					self.componentTypeId("0");
				}

				setCompNames();
				if($.grep(self.componentNamesArr(), function(e){ return  e.componentId == configObj[0].componentId; }).length){
					$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
				}
				else{
					$("#compNamesList").val("0").trigger('chosen:updated');
				}

				/*if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === configObj[0].componentId;} )){
					self.componentNamesArr.push({
						"componentId": configObj[0].componentId,
						"componentName": configObj[0].componentName,
						"isActive": false
					});
					$("#compNamesList_chosen span").first().addClass("inactiveOptionClass");
				}
				$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');*/
			}
			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
			

				if(!self.componentsArr().find( function( ele ) {return ele.componentTypeId && ele.componentTypeId === configObj[0].componentTypeId;} )){
					$("#compTypeList").val("0").trigger('chosen:updated');
				}
				else{
					$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
				}

				self.componentTypeId(configObj[0].componentTypeId);
				setCompNames();

				if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === configObj[0].componentId;} )){
					$("#compNamesList").val("0").trigger('chosen:updated');
				}
				else{
					$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
				}
			}

			self.loadComponentInstances();
			self.componentNameId(configObj[0].componentId);
			self.enableViewClusterOp(true);
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#cluster-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
		}

		this.viewClusterOperation = function(){
			var selCompId = $("#compNamesList").val();
			self.selCompName($("#compNamesList option:selected").text());			
			
			requestCall(uiConstants.common.SERVER_IP + "/clusterOperation/" + selCompId, "GET", "", "getClusterOperations", successCallback, errorCallback);
		}

		this.cancelConfig = function(){
			if(self.isModal()){
				$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Cluster Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function setCompNames(){
			//self.componentNamesArr({});
			self.enableViewClusterOp(false);

			if($("#compTypeList").val() == 0){
				self.componentNamesArr({});
				$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
			}
			else{
				var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == self.componentTypeId(); });
				if(componentsObj[0] && componentsObj[0].components){
					if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);

					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						self.componentNamesArr(getMasterList(componentsObj[0].components, "componentId", [self.selectedConfigRows()[0].componentId], true));
					}
					else{
						self.componentNamesArr(getMasterList(componentsObj[0].components, "componentId", null, false));
					}
				}
				$('#compNamesList').prop('disabled', false).trigger("chosen:updated");
			}
			
			$(".panel-body #compNamesList").trigger('chosen:updated');
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onMastersLoad(){
			//if(configTagLoaded == 1){
			//if(compTypeVersionLoaded == 1 && applicationsLoaded == 1){
			if(applicationsLoaded == 1 && configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
					if(!self.selectedConfigRows()[0].status){ //if the component is inactive
						setConfigUneditable(true);
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		/*function setCompInstList(){
			var compInstArr = [];

			for(compInst in self.compInstsArr()){
				compInstArr.push({
					"id": self.compInstsArr()[compInst].componentInstanceId,
					"name": self.compInstsArr()[compInst].componentInstanceName
				});
			}
			$("#compInstList").checklistbox({
	            data: compInstArr
	        });
		}*/

		this.loadComponentInstances = function(){
			if($("#compNamesList").val() != "0" && $("#selectedApplicationList").getAllValues().length>0){
				var appsArr = $("#selectedApplicationList").getAllValues();
				var applicationQueryString = "";
				for(var app in appsArr){
					applicationQueryString = applicationQueryString + "&applicationId=" + appsArr[app];
				}
				requestCall(uiConstants.common.SERVER_IP + "/componentInstanceForApplication?status=2&markInactive=1&componentId="+ $("#compNamesList").val() + applicationQueryString, "GET", "", "getComponentInstances", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/57a8601d11000085131d4512?callback=?", "GET", "", "getComponentInstances", successCallback, errorCallback);
			}
			else if($('#selectedCompInstList').getAllValues().length>0 || $('#availableCompInstList').getAllValues().length>0){
				clearCompInstances();
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getClusterTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #cluster-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#cluster-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('.panel-body #cluster-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#cluster-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getApplications"){
				self.applicationsArr(data.result);
				self.availableApplicationArr([]);
				self.selectedApplicationArr([]);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selAppIdsArr = [];

					for(var app in self.selectedConfigRows()[0].applications){
						selAppIdsArr.push(self.selectedConfigRows()[0].applications[app].applicationId);
					}

					self.applicationsArr(getMasterList(data.result, "applicationId", selAppIdsArr, true));
				}
				else{
					self.applicationsArr(getMasterList(data.result, "applicationId", null, false));
				}

	        	for(app in self.applicationsArr()){
					self.availableApplicationArr.push({
						"id": self.applicationsArr()[app].applicationId,
						"name": self.applicationsArr()[app].applicationName
					});
				}

				$('#availableApplicationList').checklistbox({
				    data: self.availableApplicationArr()
				});

				$('#selectedApplicationList').checklistbox({
				    data: self.selectedApplicationArr()
				});

				if(self.currentViewIndex() != uiConstants.common.LIST_VIEW && self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
			        var inactiveAppsArr = removeArrayObjElements(self.selectedConfigRows()[0].applications, "applicationId", self.availableApplicationArr(), "id", true);
			        
			        if(inactiveAppsArr.length>0){
			        	for(inactiveApp in inactiveAppsArr){
			        		self.availableApplicationArr().push({
			        			"id": inactiveAppsArr[inactiveApp].applicationId, 
			        			"name": inactiveAppsArr[inactiveApp].applicationName,
			        			"isActive": 0
			        		});
			        	}

			        	$('#availableApplicationList').checklistbox({
						    data: self.availableApplicationArr()
						});
			        }

			        for(application in self.selectedConfigRows()[0].applications){
						$("#availableApplicationList .checkList[value=" + self.selectedConfigRows()[0].applications[application].applicationId + "]").prop("checked",true);
					}
					self.addToSelectedApplication();
				}
				
				applicationsLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getComponentInstances"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				self.availableCompInstArr([]);
				//self.selectedCompInstArr([]);

				debugger;

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selCompInstIdsArr = [];

					for(var compInst in self.selectedConfigRows()[0].componentInstances){
						selCompInstIdsArr.push(self.selectedConfigRows()[0].componentInstances[compInst].componentInstanceId);
					}

					data.result = getMasterList(data.result, "componentInstanceId", selCompInstIdsArr, true);
				}
				else{
					data.result = getMasterList(data.result, "componentInstanceId", null, false);
				}


				for(compInst in data.result){
					self.availableCompInstArr.push({
						"id": data.result[compInst].componentInstanceId,
						"name": data.result[compInst].componentInstanceName
					});
				}

				if(self.selectedCompInstArr().length > 0){
					self.selectedCompInstArr(removeArrayObjElements(self.selectedCompInstArr(), "id", self.availableCompInstArr(), "id", false));
					self.availableCompInstArr(removeArrayObjElements(self.availableCompInstArr(), "id", self.selectedCompInstArr(), "id", true));
				}

				$('#availableCompInstList').checklistbox({
				    data: self.availableCompInstArr()
				});

				$('#selectedCompInstList').checklistbox({
				    data: self.selectedCompInstArr()
				});

				if(self.setSelCompInstances() && self.currentViewIndex() != uiConstants.common.LIST_VIEW && self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
			        var inactiveCompInstsArr = removeArrayObjElements(self.selectedConfigRows()[0].componentInstances, "componentInstanceId", self.availableCompInstArr(), "id", true);
			        
			        if(inactiveCompInstsArr.length>0){
			        	for(inactiveCompInst in inactiveCompInstsArr){
			        		self.availableCompInstArr().push({
			        			"id": inactiveCompInstsArr[inactiveCompInst].componentInstanceId, 
			        			"name": inactiveCompInstsArr[inactiveCompInst].componentInstanceName,
			        			"isActive": 0
			        		});
			        	}

			        	$('#availableCompInstList').checklistbox({
						    data: self.availableCompInstArr()
						});
			        }

			        self.setSelCompInstances(false);
			        for(componentInst in self.selectedConfigRows()[0].componentInstances){
						$("#availableCompInstList .checkList[value=" + self.selectedConfigRows()[0].componentInstances[componentInst].componentInstanceId + "]").prop("checked",true);
					}
					self.addToSelectedCompInst();

					if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
						$("#compInstDiv").find("input,button,select").attr("disabled", "disabled");
					}
				}
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_CLUSTER,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.clusterConfig.ERROR_ADD_CLUSTER, "error");
					}
				}
				else{
					showMessageBox(uiConstants.clusterConfig.SUCCESS_ADD_CLUSTER);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_CLUSTER,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.clusterConfig.ERROR_UPDATE_CLUSTER, "error");
					}
				}
				else{
					showMessageBox(uiConstants.clusterConfig.SUCCESS_UPDATE_CLUSTER);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "getClusterOperations"){
				self.clusterOpGridData(data.result);

				

			}
			/*else if(reqType === "getCompTypeVersion"){
				if(DEBUG_MODE)console.log(data.result);
				self.componentsArr(data.result);

				$("#compTypeList").trigger('chosen:updated');

				compTypeVersionLoaded = 1;
				onMastersLoad();
			}*/
		}

		function errorCallback(reqType) {
			if(reqType === "getClusterTag"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_CLUSTER_TAGS, "error");
			}
  			else if(reqType === "getApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
			else if(reqType === "getComponentInstances"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_COMP_INSTANCES, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.clusterConfig.ERROR_ADD_CLUSTER, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.clusterConfig.ERROR_UPDATE_CLUSTER, "error");
			}
			else if(reqType === "getClusterOperations"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_CLUSTER_OPERATIONS, "error");
			}
  			/*else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}*/
		}
	}

	ClusterAddEdit.prototype.dispose = function() { };
	return { viewModel: ClusterAddEdit, template: templateMarkup };
});