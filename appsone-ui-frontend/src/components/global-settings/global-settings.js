define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead', 'text!./global-settings.html','hasher','validator','ui-constants','ui-common','jQuery-plugins','fsstepper'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,jQueryPlugins,fsstepper) {

	function GlobalSettingsView(params) {
		var self = this;
		this.multipleLoginStatus = ko.observable(1);
		this.pwdExpiryDays = ko.observable("");
		this.pwdExpiryIntimationDays = ko.observable("");
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");

		this.renderHandler=function(){
			$("#multipleLoginStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Yes",
				'offText': "No"
			});

			$("#multipleLoginStatus").on('switchChange.bootstrapSwitch', function () {
				self.multipleLoginStatus($('#multipleLoginStatus').bootstrapSwitch('state')?1:0);
			});

			window.currentPageMode = "AddUpdate";

			$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });

			requestCall(uiConstants.common.SERVER_IP + "/globalSettings", "GET", "", "getGlobalSettings", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/583bfcdc290000a7016ec990?callback=?", "GET", "", "getGlobalSettings", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		self.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			if(self.pwdExpiryDays().trim() == ""){
				//self.errorMsg(uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_REQUIRED);
				showError("#globalSettingsAddEdit #txtPwdExpiryDays", uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_REQUIRED);
			    self.errorMsg("#globalSettingsAddEdit #txtPwdExpiryDays");
			}
			else if(self.pwdExpiryDays()<=0){
				//self.errorMsg(uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_MIN_ERROR);
				showError("#globalSettingsAddEdit #txtPwdExpiryDays", uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_MIN_ERROR);
			    self.errorMsg("#globalSettingsAddEdit #txtPwdExpiryDays");
			}
			
			if(self.pwdExpiryIntimationDays().trim() == ""){
				//self.errorMsg(uiConstants.globalSettingsConfig.PWD_EXPIRY_INTIMATION_DAYS_REQUIRED);
				showError("#globalSettingsAddEdit #txtPwdExpiryIntimationDays", uiConstants.globalSettingsConfig.PWD_EXPIRY_INTIMATION_DAYS_REQUIRED);
			    self.errorMsg("#globalSettingsAddEdit #txtPwdExpiryIntimationDays");
			}
			else if(self.pwdExpiryIntimationDays()<=0){
				//self.errorMsg(uiConstants.globalSettingsConfig.PWD_EXPIRY_INTIMATION_DAYS_MIN_ERROR);
				showError("#globalSettingsAddEdit #txtPwdExpiryIntimationDays", uiConstants.globalSettingsConfig.PWD_EXPIRY_INTIMATION_DAYS_MIN_ERROR);
			    self.errorMsg("#globalSettingsAddEdit #txtPwdExpiryIntimationDays");
			}
			
			if(self.pwdExpiryDays().trim() != "" && self.pwdExpiryIntimationDays().trim() != "" && self.pwdExpiryIntimationDays()>=self.pwdExpiryDays()){
				//self.errorMsg(uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_ERROR);
				showError("#globalSettingsAddEdit #txtPwdExpiryIntimationDays", uiConstants.globalSettingsConfig.PWD_EXPIRY_DAYS_ERROR);
			    self.errorMsg("#globalSettingsAddEdit #txtPwdExpiryIntimationDays");
			}
			
			if(self.errorMsg() == ""){
				var globalSettingsObj = {
					"allowMultipleLogin": self.multipleLoginStatus(),
					"passwordExpiryDays": parseInt(self.pwdExpiryDays()),
					"passwordExpiryIntimationDays": parseInt(self.pwdExpiryIntimationDays())
				};

				requestCall(uiConstants.common.SERVER_IP + "/globalSettings", "PUT", JSON.stringify(globalSettingsObj), "updateGlobalSettings", successCallback, errorCallback);
			}
		}

		self.cancelConfig = function(){
			window.currentPageMode = "";
			hasher.setHash("#"+(window.koPreviousHash() || ""));
		}

		function successCallback(data, reqType) {
			if(reqType === "getGlobalSettings"){
				if(data.result){
					$('#multipleLoginStatus').bootstrapSwitch('state', data.result.allowMultipleLogin);
					self.pwdExpiryDays(data.result.passwordExpiryDays.toString());
					self.pwdExpiryIntimationDays(data.result.passwordExpiryIntimationDays.toString());
				}
			}
			else if(reqType === "updateGlobalSettings"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_GLOBAL_SETTINGS,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.globalSettingsConfig.ERROR_UPDATE_GLOBAL_SETTINGS, "error");
					}
				}
				else{
					showMessageBox(uiConstants.globalSettingsConfig.SUCCESS_UPDATE_GLOBAL_SETTINGS);
					self.cancelConfig();
					//params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getGlobalSettings"){
  				showMessageBox(uiConstants.globalSettingsConfig.ERROR_GET_GLOBAL_SETTINGS, "error");
  			}
  			else if(reqType === "updateGlobalSettings"){
  				showMessageBox(uiConstants.globalSettingsConfig.ERROR_UPDATE_GLOBAL_SETTINGS, "error");
  			}
  		}
	}

	GlobalSettingsView.prototype.dispose = function() { };
	return { viewModel: GlobalSettingsView, template: templateMarkup };
});