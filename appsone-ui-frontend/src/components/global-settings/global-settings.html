<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="globalSettingsAddEdit">

 	<div class="configPanel panel-heading"><h4>Global Settings</h4></div>
 	
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-3">Allow Multiple Login for Role <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="checkbox" id="multipleLoginStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="multipleLoginStatus">
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-3">Password Expiry Days <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="number" class="form-control positive-integer" id="txtPwdExpiryDays" data-bind="value: pwdExpiryDays" placeholder="Enter Days">
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-3">Password Expiry Intimation Days <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="number" class="form-control positive-integer" id="txtPwdExpiryIntimationDays" data-bind="value: pwdExpiryIntimationDays" placeholder="Enter Days">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>