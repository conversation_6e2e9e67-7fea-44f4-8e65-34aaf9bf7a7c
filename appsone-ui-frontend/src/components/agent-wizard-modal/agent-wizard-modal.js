define(['knockout', 'text!./agent-wizard-modal.html','ui-common'], function(ko, templateMarkup, uicommon){
    function Agentwizardmodal(params) {
    	var self = this;
        this.errorMsg = ko.observable("");

    	self.initialize  = function(){
    		self.defaultComponentAgentDataCaptureOption = ko.observable(uiConstants.agentConfig.REMOTE);
    		self.componentAgentArr = ko.observableArray();
            self.componentAgentCapturingOptionSelectedLabel = ko.computed(function(){
                if(self.defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.REMOTE){ 
                    return uiConstants.agentConfig.COMPONENTAGENTCAPTURED_REMOTE_LABEL;
                }
                else if(self.defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.LOCAL){
                    return uiConstants.agentConfig.COMPONENTAGENTCAPTURED_LOCAL_LABEL;
                }
                return "";
            });
            params.childViewModel(self);
            self.componentInstanceIDs = params && params.componentInstanceIDs || ko.observableArray();
    	}

    	self.initialize();

        self.renderHandler = function(){
    		/*This call will get the initial list of all the existing component agents on load of the agent wizard modal*/
            if(self.defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.REMOTE){
                requestCall(uiConstants.common.SERVER_IP + "/wizard/agents", "GET","", "getComponentAgent", self.successCallBack, self.errorCallBack);
            }
            
    	};

        self.onSubmit = function(){
            self.errorMsg("");
            var dataObj = {};
            var selectedCompAgent = $("#list_componentAgentDataCaptured").val();
            if(self.defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.REMOTE){
                if(selectedCompAgent >=1 || selectedCompAgent.trim() !==""){
                    
                    dataObj.agentType= "pull";
                    dataObj.agentId= (selectedCompAgent == "")?0:selectedCompAgent;
                    dataObj.componentInstanceIds = self.componentInstanceIDs();

                    console.log("----------------------Remote call with existing agent----------------------");
                    console.log(JSON.stringify(dataObj));
                    
                    requestCall(uiConstants.common.SERVER_IP + "/wizard/agent", "POST", JSON.stringify(dataObj), "remoteWithSelectedAgent", self.successCallBack, self.errorCallBack );
                }
                else if(selectedCompAgent === ""){

                    dataObj.agentType= "pull";
                    dataObj.agentId= (selectedCompAgent == "")?0:selectedCompAgent;
                    dataObj.componentInstanceIds = self.componentInstanceIDs();


                    console.log("----------------------Remote call without existing agent----------------------");
                    console.log(JSON.stringify(dataObj));

                    requestCall(uiConstants.common.SERVER_IP + "/wizard/agent", "POST", JSON.stringify(dataObj), "remoteWithNoAgent", self.successCallBack, self.errorCallBack );
                }
                else{
                    //Error Message, if required
                }
            }
            else if(self.defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.LOCAL){
                
                dataObj.agentType= "push";
                dataObj.agentId= 0;
                dataObj.componentInstanceIds =self.componentInstanceIDs();

                console.log("----------------------Local call ----------------------");
                console.log(JSON.stringify(dataObj));
                
                requestCall(uiConstants.common.SERVER_IP + "/wizard/agent", "POST", JSON.stringify(dataObj), "localWithNoAgent", self.successCallBack, self.errorCallBack );
            }
        };

        /*
            Subscribed to topic "OKButtonClicked". When this topic is published, this subscriber will get triggered
        */
        uicommon.postbox.subscribe(function(value){
            uicommon.postbox.publish(self.onSubmit, "CallBack On click of Ok Button");    
        }, "OKButtonClicked");
        
        /*Subscriber which will be invoked on the toggling of  LOCAL/REMOTE radio buttons
        * Each time when REMOTE is selected, refresh the Component agent list
        */
        self.defaultComponentAgentDataCaptureOption.subscribe(function(value){
            if(value === uiConstants.agentConfig.REMOTE){
                requestCall(uiConstants.common.SERVER_IP + "/wizard/agents", "GET","", "getComponentAgent", self.successCallBack, self.errorCallBack);
            }         
        });

        self.refreshAgentList =function(){
            uicommon.postbox.publish("Refresh the Agent List View Now", "refreshAgentListView");
            console.log("called : refresh gent list");
        };

        /*
        * Result handler success call back
        */
        self.successCallBack = function(data, reqType){
            if(reqType === "getComponentAgent"){
                self.componentAgentArr(data.result);
            }
            else if(reqType === "remoteWithSelectedAgent"){
                if(data.responseStatus === "success"){
                    self.refreshAgentList();
                    uicommon.postbox.publish([], "addedComponentInstanceDetailResultData"); //once agent added sucessfully reset resultant array got from compInst Tab   
                    
                }
                else if(data.responseStatus == uiConstants.common.CONST_FAILURE){
                    //$('#rootwizard').bootstrapWizard('previous');
                     self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
                    $("#messageDialogBoxComponent").modal("show");
                }
                
            }
            else if(reqType === "remoteWithNoAgent"){
                if(data.responseStatus === "success"){
                    self.refreshAgentList();    
                    uicommon.postbox.publish([], "addedComponentInstanceDetailResultData");
                }
                else if(data.responseStatus == uiConstants.common.CONST_FAILURE){
                   // $('#rootwizard').bootstrapWizard('previous');
                     self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
                    $("#messageDialogBoxComponent").modal("show");
                }  
            }
            else if(reqType === "localWithNoAgent"){
                if(data.responseStatus === "success"){
                    self.refreshAgentList();    
                    uicommon.postbox.publish([], "addedComponentInstanceDetailResultData");
                }
                else if(data.responseStatus == uiConstants.common.CONST_FAILURE){
                    //$('#rootwizard').bootstrapWizard('previous');
                      self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
                    $("#messageDialogBoxComponent").modal("show");
                }
            }
        };

        /*  Result handler error call back
        */
        self.errorCallBack = function(reqType){
            if(reqType === "getComponentAgent"){
                //$('#rootwizard').bootstrapWizard('previous');
                $('#messageDialogBoxComponent').modal('hide'); /*Do not open another modal when one is visible, hence hide it first*/
                showMessageBox(uiConstants.agentConfig.ERROR_GET_COMPONENT_AGENTS, "error");    
            }
            else if(reqType === "remoteWithSelectedAgent"){
                //$('#rootwizard').bootstrapWizard('previous');
                 $("#messageDialogBoxComponent").modal("show");
                self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
               
            }
            else if(reqType === "remoteWithNoAgent"){
               // $('#rootwizard').bootstrapWizard('previous');  
                $("#messageDialogBoxComponent").modal("show");
                self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
               
            }
            else if(reqType === "localWithNoAgent"){
               // $('#rootwizard').bootstrapWizard('previous');
                 $("#messageDialogBoxComponent").modal("show");
                self.errorMsg(uiConstants.agentConfig.ERROR_COMPONENT_AGENT_FAILURE);
              
            }
        };
    }
    
    Agentwizardmodal.prototype.dispose = function() { };

return { viewModel: Agentwizardmodal, template: templateMarkup };
});