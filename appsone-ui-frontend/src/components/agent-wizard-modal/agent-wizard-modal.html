<div data-bind="template: {afterRender: renderHandler}"></div>
<div class="form-inline">
	<div class="form-group" style="line-height:2;">
		<label class="control-label" data-bind="text: uiConstants.agentConfig.LOCAL_REMOTE_CHECKBOX_LABEL" for="rb_componentAgentDataCaptureOption"> </label>
		<div id="rb_componentAgentDataCaptureOption">
			<span class="col-xs-6">
				<input type="radio" name="componentAgentDataCaptureOption" data-bind="checked: defaultComponentAgentDataCaptureOption, value: uiConstants.agentConfig.LOCAL">
					<label data-bind="text: uiConstants.agentConfig.LOCAL"></label>
				</input>
			</span>
			<span class="col-xs-6">
				<input type="radio" name="componentAgentDataCaptureOption" data-bind="checked: defaultComponentAgentDataCaptureOption, value: uiConstants.agentConfig.REMOTE">
					<label data-bind="text: uiConstants.agentConfig.REMOTE"></label>
				</input>
			</span>
		</div>
	</div>
	<label class="control-label" data-bind="text: componentAgentCapturingOptionSelectedLabel"> </label>
	<br />
	<div class="form-group" style="line-height:3;">
		<select data-bind="	visible: defaultComponentAgentDataCaptureOption() === uiConstants.agentConfig.REMOTE,
							options: componentAgentArr,
							optionsValue: function(item){ return item.agentId; },
							optionsText: function(item){ return item.agentName + ' [' + item.host + ']' },
							optionsCaption: uiConstants.common.SELECT_COMPONENT_AGENT
						  "
			  	id="list_componentAgentDataCaptured", class="form-control" style="width: auto; border: 1px solid #ddd !important;border-bottom: 2px solid #ddd !important; padding: 0">
		</select>	

		<!-- ko if: errorMsg() != '' -->
			<span class="errorMessageField" data-bind="text: errorMsg()"></span>
		<!-- /ko-->
	</div>		  
</div>	