<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divCompAddEdit">
	<!-- <div class="panel-heading" data-bind="visible: !isModal()"><h4><span data-bind="text: pageSelected"></span></h4></div> -->

	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
	
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Component Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="compTypeList" data-bind="foreach : componentsArr" data-placeholder="" >
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
						<!-- /ko-->
						
						<option data-bind="value: $data.componentTypeId, text: $data.componentType, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>

				<!-- ko if: currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || ((currentViewIndex() == uiConstants.common.EDIT_VIEW || currentViewIndex() == uiConstants.common.CLONE_VIEW) && selectedConfigRows && selectedConfigRows()[0] && selectedConfigRows()[0].status == 1) -->
					<button id="modalCompType" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="visible : isFirstModal()" data-toggle="modal" data-target="#idModal" title="Add Component Type"></button>
				<!-- /ko-->
			</div>

			<!-- <div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: compName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>
 -->
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4" data-bind="visible: currentViewIndex() != uiConstants.common.EDIT_VIEW || isModal()">
					<select class="chosen form-control" id="compNameList" data-bind="event:{change: function(){checkInactive(compNamesArr(), '#compNameList', 'componentId')}}, foreach : compNamesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT"></option>
						<!-- /ko-->
						<option data-bind="value: $data.componentId, text: $data.componentName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>

				<div class="col-sm-4" data-bind="visible: currentViewIndex() == uiConstants.common.EDIT_VIEW && !isModal()">
				<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}">
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Version <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="versionList" data-bind="foreach : versionsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT"></option>
						<!-- /ko-->
						<option data-bind="value: $data.versionId, text: $data.version, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
					</select>
				</div>
			</div>

			<div id="divCompDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: description" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="component-tokenfield-typeahead" data-bind="value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="compStatus" data-bind="checked: compStatus"> -->

					<input type="checkbox" id="compStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="compStatus">

				</div>
			</div>

			<div class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Attributes</div>
					<div class="panel-body">
						
						<!-- <div class="col-xs-4" style="width:100%;padding-left:0px;padding-right: 0px;"> -->
							<!-- <div class="text-right" style="padding-bottom: 2px"> -->
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : showAddDelBtns, event:{click: addAttribute.bind($data, true)}" title="Add Attribute"></button>

								<span class="field-note-msg" style="padding-left: 10px;">Note: Min/Max characters for HostAddress attribute is mandatory</span>
							<!-- </div> -->

							<!-- <div id="attribDiv" class="inner-div-container"> -->
							<div id="atrribListScroller" class="wrapper-scroll-table" style="width: 100%; height: 300px; position: relative;">
								<table id="compAtribsList" class="table table-hover table-striped table-sm" style="margin-right: 50px;">
									<thead>
										<tr class="a1-inner-table-thead">
											<th class="textOverflowOmmiter col-xs-2" title="Allows user to enter or select attribute name">Attribute Name <span class="mandatoryField">*</span></th>
											<th class="textOverflowOmmiter col-xs-2" title="Allows user to select type of an attribute">Attribute Type <span class="mandatoryField">*</span></th>
											<th class="textOverflowOmmiter" style="width: 90px;" title="Attribute is considered as mandatory if checkbox is checked">Mandatory</th>
											<th class="textOverflowOmmiter" title="Allows multiple values to enter for dropdown type attribute">Options</th>
											<th class="textOverflowOmmiter col-xs-2" title="Allows to provide default value to an attribute">Default Value</th>
											<th class="textOverflowOmmiter" title="Allows minimum value to an attribute">Min. Characters</th>
											<th class="textOverflowOmmiter" title="Allows maxmimum value to an attribute">Max. Characters</th>
											<th class="textOverflowOmmiter" title="Allows regular expressions which should be in the format /^[a-z]$/">Regex</th>
											<th data-bind="visible:0">
											</th>
											<th data-bind="visible:0">
											</th>	
											<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
												<th class="textOverflowOmmiter" style="width: 30px;"></th>
											<!-- /ko-->
										</tr>
									</thead>
									<tbody data-bind="foreach : attributesArr">
										<tr data-bind="css:{'disabled':!$data.isCustom && $parent.currentViewIndex() != uiConstants.common.CLONE_VIEW} ,style:{'opacity': $data.isCustom?'' :0.9}">
											<td style="width: 250px;">
												<!-- <input type="text" class="col-xs-12" data-bind="value: $data.attributeName, attr: {id: 'attrib'+$index()}" placeholder="Enter Name"></input> -->

												<!-- <select class="chosen" data-bind="enable: $parent.currentViewIndex() != uiConstants.common.READ_VIEW, foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" "> -->
												<!-- <span data-bind="text: $parent.enableAttribs()"></span> -->
												<select class="chosen" data-bind="enable: $parent.enableAttribs(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()} ,event:{change: function(){$parent.onAttributeListItemSelection($index())}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose($index())}}"> <!--enable: $parent.currentViewIndex() != uiConstants.common.READ_VIEW, event: {'chosen:showing_dropdown': function(){$parent.onChosenDropdownOpen('attribList'+$index())}, 'chosen:hiding_dropdown': function(){$parent.onChosenDropdownClose('attribList'+$index())}} -->
													<!-- ko if: $index() == 0 -->
														<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT"></option>
													<!-- /ko-->
													<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
												</select>
											</td>
											<td>
												<select class="chosen form-control attribTypeChosen" data-bind="options: $parent.attributesType,  optionsText: 'attributeType', optionsCaption: 'Select', attr: {'id': 'attribType'+$index()},event: {change:function(){$parent.displayOptions($index())}}"></select>
											</td>
											<td style="text-align:center">
												<input type="checkbox" data-bind="checked: $data.isMandatory, attr: {'id': 'attribMandatory'+$index()}"></input>
											</td>
											<td >
												<input type="text"  class="form-control multiple-options" data-bind="attr: {'id': 'attribMultipleValues'+$index()},'tokenfield:createdtoken': $parent.onCreateOptions.bind(event,$index()),'tokenfield:removedtoken':$parent.onDeleteOptions.bind(event,$index()),value: $data.attributeOptions" >
											</td>
											<td>
												<input type="text" class="form-control" data-bind=" attr: {'id': 'attribDefaultValue'+$index()},value:attributeDefaultValue">
											</td>
											
											<td >
												<input type="number" class="form-control" data-bind=" attr: {'id': 'attribMinLength'+$index()},value:attributeMinLength" min="0" max="99">
											</td>
											<td >
												<input type="number" class="form-control" data-bind=" attr: {'id': 'attribMaxLength'+$index()},value:attributeMaxLength" min="0" max="99">
											</td>
											<td>
												<input type="text" class="form-control" data-bind=" attr: {'id': 'attribRegExp'+$index()},value:attributeRegEx  ">
											</td>
											
											<td >
												<span type="button" class="glyphicon glyphicon-remove buttondelete" title="Delete" style="width: 15px;" data-bind="attr: {'id': 'attribDelBtn'+$index()}" ></span>
											</td>

											<td data-bind="visible:0">
												<input type="text" class="form-control" data-bind=" attr: {'id': 'attribMappingId'+$index()},value:componentAttributeMappingId">
											</td>
											<td data-bind="visible:0">
												<input type="text" class="form-control" data-bind=" attr: {'id': 'attribIsCustom'+$index()},value:isCustom">
											</td>
											
										</tr>
									</tbody>
								</table>
							</div>
							<!-- </div> -->
						<!-- </div> -->
					 </div>
				</div>
			</div> 

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditComponent}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelComponent}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>

			<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
		   		<div class="modal-dialog modal-lg">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4><span data-bind="text: modalTitle"></span></h4>
			            </div>	
			            <!-- <div data-bind="component: comp"> -->
			            <div data-bind="if : displayComponent">
			        		<comp-type-add-edit params="{isModal:true, compType: compType, currentViewIndex: uiConstants.common.ADD_SINGLE_VIEW}"></comp-type-add-edit>
			        	</div>
			        </div>
		        </div> <!-- /.modal-content -->
		    </div>
<!-- 
		    ($parent.currentViewIndex() != uiConstants.common.READ_VIEW && ($data.isCustom)) ||  ($parent.currentViewIndex() == uiConstants.common.EDIT_VIEW  && $parent.compStatus == 0) -->
		</form>
	</div>
</div>