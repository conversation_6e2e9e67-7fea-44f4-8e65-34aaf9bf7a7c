define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead','text!./component-add-edit.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

	function ComponentAddEdit(params) {	
		var self = this;

		this.componentsArr = params.componentsArr;
		this.compNamesArr = ko.observableArray([{}]);
		this.versionsArr = ko.observableArray([{}]);
		this.compTagArr = ko.observableArray();
		this.compTagAutoCompleteArr = ko.observableArray();
		this.compId = ko.observable(0);
		this.compName = params.compName;
		this.tags = ko.observable();
		this.currentViewIndex = params.isModal ? ko.observable(uiConstants.common.ADD_SINGLE_VIEW) : params.currentViewIndex;
		this.compStatus = ko.observable(true);
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		var compTagLoaded = 0;
		var attributeTypeListLoaded = 0;
		var attributeTypeLoaded = 0;
		var compTypeVersionLoaded = 0;
		var optionFound = true;
		var compNameOptionFound = true;
		this.pageSelected = params.pageSelected;
  		this.displayComponent = ko.observable(false);
  		this.modalTitle = ko.observable();
  		this.compType = ko.observable("");
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var deletedRows = [];
		this.isModal = ko.observable(false);
		this.isFirstModal = ko.observable(true);
		this.componentTypeId = ko.observable("0");
		this.configName = ko.observable("");
		this.attributesArr = ko.observableArray();
		this.attributesMasterArr = ko.observableArray();
		this.attributesType = ko.observableArray();
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.selAttributeObj=ko.observable();
  		this.enableAttribs=ko.observable(true);
  		this.showAddDelBtns=ko.observable(1);
  		this.clonedVersionId=ko.observable();
  		this.configTagAutoCompleteCopyArr = ko.observableArray();

  		this.attributeOptions = ko.observable();
  		this.attributeOptionsArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.description = ko.observable();
  		var attributeOptionsNewToAddArr = [];
  		var attributeOptionsToDelArr = [];
  		var attributeOptionsEditArr = [];
  		var previousCompTypeId;
  		var previousCompId;
  		var previousCompName;
  		var previousCompVersionId;
  		var previousCompVersion;
		//this.attributeOptionsAutoCompleteArr = ko.observableArray();


		var attribOptionsBH = new Bloodhound({	
			local: [],//self.attributeOptionsAutoCompleteArr(),
			datumTokenizer: function(d) {
				return Bloodhound.tokenizers.whitespace(d.value);
			},
			queryTokenizer: Bloodhound.tokenizers.whitespace
		});

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
      		var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_COMPTYPE_LINK; });
      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
				$("#modalCompType").css("visibility","hidden");
      		}
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			var $tab = $('#compAtribsList');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			$("#txtName").focus();

			$("#compStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#compStatus').bootstrapSwitch('state', self.compStatus());

			$("#compStatus").on('switchChange.bootstrapSwitch', function () {
				self.compStatus($('#compStatus').bootstrapSwitch('state')?1:0);
			});

			if(params.isModal){
				self.isModal(true);
				self.isFirstModal(false);
			}
			else{
				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
					self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["componentName", "componentVersion"]));
				}
			}

			//token field implementation for attribute options
			attribOptionsBH.initialize();
			///end

			/*$('#listgrid tbody').on('click', 'tr', function(e){
		    	if(e.target.parentNode.rowIndex != undefined)
		    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
			});*/

			$("#idModal").on('show.bs.modal', function () {
				self.modalTitle("Add Component Type");
				self.displayComponent(true);
			});

			$("#idModal").on('hidden.bs.modal', function () {
				self.displayComponent(false);
		        if(self.compType() && self.compType() != ""){
		        	getCompType();
		        }
		        self.isModal(false);
		    });

		    $("#compAtribsList tbody").on('click', '.buttondelete', function(e){
		    	if(!$(this).hasClass("confButtonDisabled")){

			    	//var confirmDelete =
			    	var tableObj = this;
			    	showMessageBox(uiConstants.componentConfig.CONFIRM_ATTRIBUTE_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
						if (confirmDelete) {
						 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
						 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);

						 	//self.attributesArr.splice(rowToDelete,1);

						 	deletedRows.push(rowToDelete);

						 	$(tableObj).closest('tr').css("display","none");
						}
					});
		    	}
			});

			/*function confirmCallback(confirmVal){
				alert(confirmVal);
			}*/

			$('.panel-body #component-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.compTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.compTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.compTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#component-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.compTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.compTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.compTagAutoCompleteArr.sort();
					}

					$('#component-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.compTagAutoCompleteArr()
					});
				});

			$("#compNameList_chosen .chosen-search").find("input").on("keyup", function (evt) {
				if($(this).val() != undefined && $(this).val() != ""){
		        	$("#compNameList_chosen span").text($(this).val());
		        	setCompVersionsByCompName($("#compNameList_chosen span")[0].innerHTML);
				}

				if(evt.which == 13){
					$("#compNameList_chosen").parent().trigger("click");
				}
		    });

			$("#versionList_chosen .chosen-search").find("input").on("keyup", function (evt) {

				if($(this).val() != undefined && $(this).val() != ""){

		        	$("#versionList_chosen span").text($(this).val());
				}

				if(evt.which == 13){
					$("#versionList_chosen").parent().trigger("click");
				}
		    });


			$("#compNameList").chosen({}).on('chosen:showing_dropdown', function(){
				if($("#compNameList_chosen span").text() != uiConstants.common.ENTER_SELECT){
					$("#compNameList_chosen .chosen-search").find("input").val($("#compNameList_chosen span")[0].innerHTML);
				}
			});

			$("#versionList").chosen({}).on('chosen:showing_dropdown', function(){
				if($("#versionList_chosen span").text() != uiConstants.common.ENTER_SELECT){
					$("#versionList_chosen .chosen-search").find("input").val($("#versionList_chosen span")[0].innerHTML);
				}
			});
		    /*$("#compNameList_chosen .chosen-search").find("input").on("keyup", function (evt) {
				if($(this).val() != undefined && $(this).val() != ""){
		        	$("#compNameList_chosen span").text($(this).val());*/



        	$("#compNameList").chosen({}).on('chosen:hiding_dropdown', function(){
        		var selCompNameTxt = "";

        		if(!$('#compNameList_chosen .chosen-results').find("li.active-result.result-selected")){
        			selCompNameTxt = $('#compNameList_chosen .chosen-search input[type="text"]').val();
        		}
           		
	           	if(selCompNameTxt!=""){
	           		compNameOptionFound = false;
	           		$('#compNameList > option').each(function(){
	 					if($(this).text()==selCompNameTxt) {
	 						compNameOptionFound = true;
	 						$(this).parent('select').val($(this).val());
	 						$("#compNameList").val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});

		    $("#versionList").chosen({}).on('chosen:hiding_dropdown', function(){
           		var selVersionTxt = "";

        		if(!$('#versionList_chosen .chosen-results').find("li.active-result.result-selected")){
        			selVersionTxt = $('#versionList_chosen .chosen-search input[type="text"]').val();
        		}

	           	if(selVersionTxt!=""){

	           		optionFound = false;
	           		$('#versionList > option').each(function(){
	 					if($(this).text()==selVersionTxt) {
	 						optionFound = true;
	 						$(this).parent('select').val($(this).val());
	 						$("#versionList").val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});

			if(uiConstants.common.DEBUG_MODE)console.log(self.componentsArr());
		    /*$("#compTypeList").on('change', function () {
		    	
			});*/

			$('.panel-body #compTypeList').on('chosen:showing_dropdown', function () {
				previousCompTypeId = $("#compTypeList").val();
			}).on('change', function() {
		    	if(previousCompTypeId == 0){
					self.componentTypeId($("#compTypeList").val());
					setCompNames($("#compTypeList").val());
		    	}
		    	 else{
		    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClear){
						if(confirmClear){
							self.componentTypeId($("#compTypeList").val());
							setCompNames($("#compTypeList").val());
						}
						else{
			    			$("#compTypeList").val(previousCompTypeId).trigger("chosen:updated");
						}
	    			});
			   	}
			});

			/*$(".panel-body #compNameList").on('change', function () {
		    	compNameOptionFound = true;
		    	setCompVersions($(this).val());
			});*/

			$('.panel-body #compNameList').on('chosen:showing_dropdown', function () {
				previousCompId = $("#compNameList").val();
				previousCompName = $("#compNameList_chosen span")[0].innerHTML;
			}).on('chosen:hiding_dropdown', function() {
		        if(previousCompName != $("#compNameList_chosen span")[0].innerHTML){
		        	if(previousCompId == 0){
						compNameOptionFound = true;
			    		setCompVersionsByCompName($("#compNameList_chosen span")[0].innerHTML);
			    	}
			    	else{
			    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_CHANGE, "question", "confirm", function confirmCallback(confirmClear){
							if(confirmClear){
								compNameOptionFound = true;
			    				setCompVersionsByCompName($("#compNameList_chosen span")[0].innerHTML);
							}
							else{
				    			//$("#compNameList").filter(function () { return $(this).html() == previousCompName; }).prop('selected', true).trigger("chosen:updated"); 
				    			$("#compNameList_chosen span")[0].innerHTML = previousCompName;
							}
		    			});
				   	}
		        }
			});

			$('.panel-body #versionList').on('chosen:showing_dropdown', function () {
				previousCompVersionId = $("#versionList").val();
				previousCompVersion = $("#versionList_chosen span")[0].innerHTML;
			}).on('chosen:hiding_dropdown', function() {
		        if(previousCompVersion != $("#versionList_chosen span")[0].innerHTML){
		        	if(previousCompVersionId == 0){
						optionFound = true;
			    	}
			    	 else{
			    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_COMP_VERSION_CHANGE, "question", "confirm", function confirmCallback(confirmClear){
							if(confirmClear){
								optionFound = true;
							}
							else{
				    			$("#versionList").val(previousCompVersionId).trigger("chosen:updated");
				    			$("#versionList_chosen span")[0].innerHTML = previousCompVersion;
							}
		    			});
				   	}
		        }
			});

			/*$(".panel-body #versionList").on('change', function () {
		    	optionFound = true;
			});*/

			//.tokenfield()
			getCompType();
			requestCall(uiConstants.common.SERVER_IP + "/tag?type=Component", "GET", "", "getComponentTag", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/attribute", "GET", "", "getAttributeList", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/attributeType", "GET", "", "getAttributeType", successCallback, errorCallback);
		}

		this.displayOptions =function(indx){		
			$("#attribMultipleValues"+ indx).tokenfield('setTokens', []);
			$("#attribMultipleValues"+ indx).tokenfield('readonly');

			switch($("#attribType"+ indx+" option:selected").text().toUpperCase()){
				case "DROPDOWN":
								$("#attribMultipleValues"+ indx).tokenfield('writeable');
								$("#attribMinLength"+ indx).val("");
								$("#attribMaxLength"+ indx).val("");
								$("#attribRegExp"+ indx).val("");
								$("#attribMinLength"+ indx).prop('disabled',true);
								$("#attribMaxLength"+ indx).prop('disabled',true);
								$("#attribRegExp"+ indx).prop('disabled',true);
								$("#attribDefaultValue"+ indx).prop('type','text');	
								break;
				case "CHECKBOX":
								$("#attribMinLength"+ indx).prop('disabled',true);
								$("#attribMaxLength"+ indx).prop('disabled',true);
								$("#attribRegExp"+ indx).prop('disabled',true);
								$("#attribDefaultValue"+ indx).prop('type','text');	
								break;
				case "PASSWORD":
								$("#attribDefaultValue"+ indx).prop('type','password');	
								$("#attribMinLength"+ indx).prop('disabled',false);
								$("#attribMaxLength"+ indx).prop('disabled',false);
								$("#attribRegExp"+ indx).prop('disabled',false);
								break;
						default:
								$("#attribMinLength"+ indx).prop('disabled',false);
								$("#attribMaxLength"+ indx).prop('disabled',false);
								$("#attribRegExp"+ indx).prop('disabled',false);
								$("#attribDefaultValue"+ indx).prop('type','text');									
			}
		}

		/*this.onChosenDropdownOpen = function(elementId){
			onChosenDropdownOpen(elementId);
	    };

	    this.onChosenDropdownClose = function(elementId){
			onChosenDropdownClose(elementId);
	    };*/

	    this.onCreateOptions = function(e,indx){
			if(!($.grep(self.attributeOptionsArr(), function(evt){ return evt.value == e.attrs.label.trim(); })[0])){
				attributeOptionsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
			}
		}

		this.onDeleteOptions = function(e,indx){
			if(attributeOptionsEditArr.indexOf(e.attrs.label.trim()) !=-1 && attributeOptionsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
				attributeOptionsToDelArr.push(e.attrs.label.trim()); //Push tag label
			}
			attributeOptionsNewToAddArr.splice(attributeOptionsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
		}

	    this.onChosenDropdownClose = function(indx){
			self.selAttributeObj($.grep(self.attributesMasterArr(), function(e){ return e.attributeName == $('#attribList'+indx + "_chosen span")[0].innerHTML.trim(); })[0]);
	   		if(self.selAttributeObj() == undefined){
	   			$("#attribType"+ indx).prop('disabled',false).trigger('chosen:updated');
	   			$("#attribType"+indx+" option").filter(function () { return $(this).html() == 'Select';}).prop('selected', true).trigger('chosen:updated');
	   		}
	    }

	    this.onAttributeListItemSelection = function(indx){
	    	self.selAttributeObj($.grep(self.attributesMasterArr(), function(e){ return e.attributeName == $('#attribList'+indx + "_chosen span")[0].innerHTML.trim(); })[0]);
	    	
	    	jQuery(".chosen").chosen({
				search_contains: true	
			});
	    	if(self.selAttributeObj().isCustom){
	    		$("#attribType"+ indx).prop('disabled',false).trigger('chosen:updated');
	    	}
	    	else{
	    		$("#attribType"+ indx).prop('disabled',true).trigger('chosen:updated');
	    		$("#attribMultipleValues"+ indx).tokenfield('readonly');
	    	}
	    	//self.selAttributeObj($.grep(self.attributesMasterArr(), function(e){ return e.attributeName == $('#attribList'+indx + "_chosen span")[0].innerHTML.trim(); })[0]);
	    	$("#attribType"+indx+" option").filter(function () { return $(this).html() == self.selAttributeObj().attributeType;}).prop('selected', true).trigger('chosen:updated');
			$("#attribType"+indx).trigger('change');

			//display option values for the particular attribute selection
			attributeOptionsEditArr=[];
			for(var optionObj in self.selAttributeObj().attributeOptions){
				attributeOptionsEditArr.push(self.selAttributeObj().attributeOptions[optionObj].attributeOptionName);
			}

			$('#attribMultipleValues'+indx).tokenfield({
				typeahead: [null, { source: attribOptionsBH.ttAdapter() }]
			});

			//attribute option changes
			$('#attribMultipleValues'+indx).trigger('tokenfield:createdtoken');
			$('#attribMultipleValues'+indx).trigger('tokenfield:removedtoken');
			$('#attribMultipleValues'+indx).tokenfield('setTokens', attributeOptionsEditArr);
	    }

		function getCompType(){
			requestCall(uiConstants.common.SERVER_IP + "/component?status=2&markInactive=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575b0eb6110000b421539fbb?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
		}

		function setCompNames(compTypeId){
			self.compNamesArr([{}]);
			self.versionsArr({});

	    	/*for(compType in self.componentsArr()){
	    		if(self.componentsArr()[compType].componentTypeId == compTypeId){
	    			if(self.componentsArr()[compType].components.length !=0){
		    			self.compNamesArr(self.componentsArr()[compType].components);
		    		}
	    			break;
	    		}
			}*/

			debugger;

			var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == compTypeId; });
			
			if(componentsObj[0] && componentsObj[0].components && componentsObj[0].components.length){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.compNamesArr(getMasterList(componentsObj[0].components, "componentId", [self.selectedConfigRows()[0].componentId], true));
				}
				else{
					self.compNamesArr(getMasterList(componentsObj[0].components, "componentId", null, false));
				}
			}
			else{
				self.compNamesArr({});
			}


			if(uiConstants.common.DEBUG_MODE)console.log(document.getElementById("compTypeList").value);
			$("#compNameList").trigger('chosen:updated');
			$("#versionList").trigger('chosen:updated');
		}

		function setCompVersions(compNameId){
			self.versionsArr({});	
	    	for(compName in self.compNamesArr()){
	    		if(self.compNamesArr()[compName].componentId == compNameId){
		    		self.versionsArr(self.compNamesArr()[compName].versions);
	    			break;
	    		}
			}

			if(uiConstants.common.DEBUG_MODE)console.log(self.versionsArr());
			$("#versionList").trigger('chosen:updated');
		}

		function setCompVersionsByCompName(compName){
			self.versionsArr({});	
	    	for(comp in self.compNamesArr()){
	    		if(self.compNamesArr()[comp].componentName == compName){
		    		self.versionsArr(self.compNamesArr()[comp].versions);
	    			break;
	    		}
			}

			if(uiConstants.common.DEBUG_MODE)console.log(self.versionsArr());
			$("#versionList").trigger('chosen:updated');
		}


		function onMastersLoad(){
			if(self.componentsArr().length>0 && compTagLoaded == 1 && attributeTypeListLoaded == 1 && attributeTypeLoaded == 1 && compTypeVersionLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					self.addAttribute(false);

					$('#compAtribsList').floatThead('reflow');

					$("#attribList0").prop('disabled', true).trigger("chosen:updated");
	   				$("#attribType0").prop('disabled', true).trigger("chosen:updated");
					$("#attribMultipleValues0").tokenfield('readonly');

					//$("#attribList0 option:contains(HostAddress), option:contains(Host Address)").attr('selected', 'selected');

					$('#attribList0 option:contains(Host)').filter(function(){
					    return $(this).text() == "Host Address" || $(this).text() == "HostAddress";
					}).attr('selected', 'selected');
					$("#attribList0").trigger('chosen:updated');
					self.onAttributeListItemSelection(0);
					$("#attribDelBtn0").addClass("confButtonDisabled");
					$("#attribMandatory0").prop({
						'checked': true,
						'disabled': true
					});
				}

				else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleComponent(self.selectedConfigRows());
					if(!self.selectedConfigRows()[0].status){ //if the component is inactive
						setComponentUneditable(true,self.selectedConfigRows());
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneComponent(self.selectedConfigRows());
					$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewComponent(self.selectedConfigRows());
				}
			}
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		//Adding/Updating single component
		this.addEditComponent = function(){
			this.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();
			
			var tagsArr = [];
			var tagsObjArr = [];
			//var compData;
			var compAttribsArr = [];
			//------------------------
			var optionsArr = [];
			var attributeOptionsNewToAddArr=[];

			$("#divCompDescription #txtDescription").val($("#divCompDescription #txtDescription").val().trim());
			$("#compNameList_chosen span")[0].innerHTML = $("#compNameList_chosen span")[0].innerHTML.trim();
			
			if(self.componentTypeId() == "0"){
				//self.errorMsg(uiConstants.common.SELECT_COMPONENT_TYPE_MSG);

				/*$('html, body').animate({
			        scrollTop: ($("#attribRegExp0").offset().left)
			    }, 300);*/

				//use scrollLeft to scroll with horizontal scrollbar
			    //$("#attribsDiv").scrollLeft($("#attribDefaultValue0").offset().left);

			   // scrollToPos(0, 300, '#compNameList_chosen');

				//showErrorMessage("compTypeList", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				showError("#divCompAddEdit #compTypeList_chosen", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				showError("#divCompAddEdit #compTypeList_chosen span", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
			    self.errorMsg("#divCompAddEdit #compTypeList_chosen");
			}
			else{
				removeError("#divCompAddEdit #compTypeList_chosen");
				removeError("#divCompAddEdit #compTypeList_chosen span");
			}


			/*else if($("#txtName").val().length < 2){
				self.errorMsg(uiConstants.componentConfig.uiConstants.componentConfig.COMPONENT_NAME_MIN_LENGTH_ERROR);
			}
			else if($("#txtName").val().length > 45){
				self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_MAX_LENGTH_ERROR);
			}
			else if(!nameValidation($("#txtName").val())){
				self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
			}*/

			if($("#compNameList_chosen span")[0].innerHTML == uiConstants.common.ENTER_SELECT){
				//self.errorMsg(uiConstants.common.ENTER_SELECT_COMPONENT_NAME_MSG);
				showError("#divCompAddEdit #compNameList_chosen", uiConstants.common.ENTER_SELECT_COMPONENT_NAME_MSG);
				showError("#divCompAddEdit #compNameList_chosen span", uiConstants.common.ENTER_SELECT_COMPONENT_NAME_MSG);
			    self.errorMsg("#divCompAddEdit #compNameList_chosen");
			}
			else if($("#compNameList_chosen span")[0].innerHTML.length < 2){
				//self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_MIN_LENGTH_ERROR);
				showError("#divCompAddEdit #compNameList_chosen", uiConstants.componentConfig.COMPONENT_NAME_MIN_LENGTH_ERROR);
				showError("#divCompAddEdit #compNameList_chosen span", uiConstants.componentConfig.COMPONENT_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divCompAddEdit #compNameList_chosen");
			}
			else if($("#compNameList_chosen span")[0].innerHTML.length > 45){
				//self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_MAX_LENGTH_ERROR);
				showError("#divCompAddEdit #compNameList_chosen", uiConstants.componentConfig.COMPONENT_NAME_MAX_LENGTH_ERROR);
				showError("#divCompAddEdit #compNameList_chosen span", uiConstants.componentConfig.COMPONENT_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divCompAddEdit #compNameList_chosen");
			}
			else if(!nameValidation($("#compNameList_chosen span")[0].innerHTML)){
				//self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
				showError("#divCompAddEdit #compNameList_chosen", uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
				showError("#divCompAddEdit #compNameList_chosen span", uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
			    self.errorMsg("#divCompAddEdit #compNameList_chosen");
			}
			else{
				removeError("#divCompAddEdit #compNameList_chosen");
				removeError("#divCompAddEdit #compNameList_chosen span");
			}

			if($("#versionList_chosen span")[0].innerHTML == uiConstants.common.ENTER_SELECT){
				//self.errorMsg(uiConstants.common.ENTER_SELECT_VERSION_MSG);
				showError("#divCompAddEdit #versionList_chosen", uiConstants.common.ENTER_SELECT_VERSION_MSG);
				showError("#divCompAddEdit #versionList_chosen span", uiConstants.common.ENTER_SELECT_VERSION_MSG);
			    self.errorMsg("#divCompAddEdit #versionList_chosen");
			}
			else{
				removeError("#divCompAddEdit #versionList_chosen");
				removeError("#divCompAddEdit #versionList_chosen span");
			}
			if($("#divCompDescription #txtDescription").val() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divCompDescription #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divCompDescription #txtDescription");
			}
			else if($("#divCompDescription #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divCompDescription #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divCompDescription #txtDescription");
			}
			else if($("#divCompDescription #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divCompDescription #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divCompDescription #txtDescription");
			}
			
			removeError("#divCompAddEdit .tokenfield");
			removeError("#divCompAddEdit #component-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divCompAddEdit #component-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divCompAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divCompAddEdit #component-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divCompAddEdit .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1){
					tagsArr.push(self.tags());
				}

				else if(self.tags() && self.tags().trim().length > 1){
					tagsArr = self.tags().split(",");
				}

				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divCompAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divCompAddEdit #component-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divCompAddEdit .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divCompAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divCompAddEdit #component-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divCompAddEdit .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divCompAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divCompAddEdit #component-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divCompAddEdit .tokenfield");
						break;
					}
				}
			}

			for(attrib in self.attributesArr()){
				var optionsObjArr = [];
				$("#attribRegExp"+attrib).val($("#attribRegExp"+attrib).val().trim());

				if(uiConstants.common.DEBUG_MODE)console.log(deletedRows.indexOf(parseInt(attrib)) + typeof(attrib));
				$("#attribList"+attrib + "_chosen span")[0].innerHTML = $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim();

				if(deletedRows.indexOf(parseInt(attrib)) == -1){		
					$("#attribDefaultValue"+attrib).val($("#attribDefaultValue"+attrib).val().trim());
					$("#attribMinLength"+attrib).val($("#attribMinLength"+attrib).val().trim());
					$("#attribMaxLength"+attrib).val($("#attribMaxLength"+attrib).val().trim());

					var defaultValue=$("#attribDefaultValue"+attrib).val();
					var attributeName=$("#attribList"+attrib + "_chosen span")[0].innerHTML.trim();

					if($("#attribList"+attrib + "_chosen span")[0].innerHTML == uiConstants.common.ENTER_SELECT){
						//self.errorMsg(uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen", uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen span", uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
					    self.errorMsg("#divCompAddEdit #attribList"+attrib + "_chosen");
					}
					else if($("#attribList"+attrib + "_chosen span")[0].innerHTML.trim().length < 2){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
					    self.errorMsg("#divCompAddEdit #attribList"+attrib + "_chosen");
					}
					else if($("#attribList"+attrib + "_chosen span")[0].innerHTML.trim().length > 45){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
					    self.errorMsg("#divCompAddEdit #attribList"+attrib + "_chosen");
					}
					else if(!nameValidation($("#attribList"+attrib + "_chosen span")[0].innerHTML)){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
					    self.errorMsg("#divCompAddEdit #attribList"+attrib + "_chosen");
					}
					else if($.grep(compAttribsArr, function(evt){ return evt.attributeName ==  $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(); }).length > 0){
						//self.errorMsg(uiConstants.componentConfig.DUPLICATE_ATTRIBUTE_NAME_ERROR+" for attribute '"+attributeName+"'");
						showError("#divCompAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.DUPLICATE_ATTRIBUTE_NAME_ERROR);
						showError("#divCompAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.DUPLICATE_ATTRIBUTE_NAME_ERROR);
					    self.errorMsg("#divCompAddEdit #attribList"+attrib + "_chosen");
					}
					else{
						removeError("#divCompAddEdit #attribList"+attrib + "_chosen");
						removeError("#divCompAddEdit #attribList"+attrib + "_chosen span");
					}

					if($("#attribType"+attrib+" option:selected").text() == uiConstants.common.SELECT){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_TYPE_REQUIRED_ERROR+" for attribute '"+attributeName+"'");
						showError("#divCompAddEdit #attribType"+attrib+"_chosen", uiConstants.componentConfig.ATTRIBUTE_TYPE_REQUIRED_ERROR);
						showError("#divCompAddEdit #attribType"+attrib+"_chosen span", uiConstants.componentConfig.ATTRIBUTE_TYPE_REQUIRED_ERROR);
					    self.errorMsg("#divCompAddEdit #attribType"+attrib+"_chosen");
					}
					else if($("#attribType"+ attrib+" option:selected").text().toUpperCase() == "DROPDOWN" && $("#attribMultipleValues"+attrib).val() == ""){
						//self.errorMsg("Please provide dropdown options for Attribute Name '"+attributeName+"'");
						showError("#divCompAddEdit #attribMultipleValues"+attrib, "Please provide dropdown options");
					    self.errorMsg("#divCompAddEdit #attribMultipleValues"+attrib);
					}
					else if(containsDuplicate($("#attribMultipleValues"+attrib).val())){
						//self.errorMsg(uiConstants.componentConfig.DUPLICATE_OPTIONS+" for attribute '"+attributeName+"'");
						showError("#divCompAddEdit #attribMultipleValues"+attrib, uiConstants.componentConfig.DUPLICATE_OPTIONS);
					    self.errorMsg("#divCompAddEdit #attribMultipleValues"+attrib);
					}
					if(($("#attribType"+attrib+" option:selected").text().toUpperCase() == "TEXTBOX" || $("#attribType"+attrib+" option:selected").text().toUpperCase() == "PASSWORD") && $("#attribMandatory"+attrib).prop('checked') == true && ($("#attribMinLength"+attrib).val() == "" || $("#attribMaxLength"+attrib).val() == "") ){
						//self.errorMsg("Please enter Min and Max values for Attribute Name '"+attributeName+"'");
						showError("#divCompAddEdit #attribMinLength"+attrib, "Please enter Min value");
						showError("#divCompAddEdit #attribMaxLength"+attrib, "Please enter Max value");
					    self.errorMsg("#divCompAddEdit #attribMinLength"+attrib);
					}
					
					self.attributeOptions($("#attribMultipleValues"+attrib).val());
					if(self.attributeOptions() && self.attributeOptions().trim().length == 1){
						optionsArr.push(self.attributeOptions());
					}

					else if(self.attributeOptions() && self.attributeOptions().trim().length > 1){
						optionsArr = self.attributeOptions().split(",");
					}
					if(defaultValue && $("#attribMinLength"+attrib).val() && defaultValue.length < parseInt($("#attribMinLength"+attrib).val())){
						//self.errorMsg("Default value character length for "+attributeName+" should not be less than minimun character length");
						showError("#divCompAddEdit #attribDefaultValue"+attrib, "Default value character length should not be less than minimun character length");
					    self.errorMsg("#divCompAddEdit #attribDefaultValue"+attrib);
					}
					else if(defaultValue && $("#attribMaxLength"+attrib).val() && defaultValue.length > parseInt($("#attribMaxLength"+attrib).val())){
						//self.errorMsg("Default value character length for "+attributeName+" should not be more than maximum character length");
						showError("#divCompAddEdit #attribDefaultValue"+attrib, "Default value character length should not be more than maximum character length");
					    self.errorMsg("#divCompAddEdit #attribDefaultValue"+attrib);
					}
					else if($("#attribMinLength"+attrib).val() && $("#attribMaxLength"+attrib).val() && parseInt($("#attribMinLength"+attrib).val()) > parseInt($("#attribMaxLength"+attrib).val())){
						//self.errorMsg("Minimum character length for "+attributeName+" should not be more than maximum character length");
						showError("#divCompAddEdit #attribMinLength"+attrib, "Minimum character length should not be more than maximum character length");
						showError("#divCompAddEdit #attribMaxLength"+attrib, "Maximum character length should not be less than minimun character length");
					    self.errorMsg("#divCompAddEdit #attribMinLength"+attrib);
					}
					if($("#attribRegExp"+attrib).val()){
						try{
							new RegExp($("#attribRegExp"+attrib).val());
						}
						catch(err){
							//self.errorMsg("Invalid Regex pattern for "+attributeName);
							showError("#divCompAddEdit #attribRegExp"+attrib, "Invalid Regex pattern");
					   		self.errorMsg("#divCompAddEdit #attribRegExp"+attrib);
						}
					}
					/*else if(isNaN(parseInt(defaultValue))){//string
						if((defaultValue != ""  && $("#attribMinLength"+attrib).val() != "") && defaultValue.length  < $("#attribMinLength"+attrib).val()){
							self.errorMsg("Default value for "+attributeName+" should not be less than minimun character length "+$("#attribMinLength"+attrib).val());
							break;
						}
						else if((defaultValue != "" && $("#attribMaxLength"+attrib).val() && $("#attribMinLength"+attrib).val() != "") && $("#attribMinLength"+attrib).val()  > $("#attribMaxLength"+attrib).val()){
							self.errorMsg("Minimum character length should not be greater than maximum character length for attribute "+attributeName);
							break;
						}
						else if((defaultValue != "" && $("#attribMaxLength"+attrib).val()) && defaultValue.length  > $("#attribMaxLength"+attrib).val()){
							self.errorMsg("Default value for "+attributeName+" should not be more than maximum character length "+$("#attribMaxLength"+attrib).val());
							break;
						}
						else if((defaultValue != ""  && $("#attribRegExp"+attrib).val() != "") && defaultValue.match($("#attribRegExp"+attrib).val())){
							self.errorMsg("Invalid Default value for "+attributeName+" rather the value should be of "+$("#attribRegExp"+attrib).val());
							break;
						}
						
					}
					else if(!isNaN(parseInt(defaultValue))){//number
						if((defaultValue != "" && $("#attribMinLength"+attrib).val() != "") && parseInt(defaultValue)  < $("#attribMinLength"+attrib).val()){
							self.errorMsg("Default value for "+attributeName+" should not be less than minimun value "+$("#attribMinLength"+attrib).val());
							break;
						}
						else if((defaultValue != "" && $("#attribMaxLength"+attrib).val() && $("#attribMinLength"+attrib).val() != "") && $("#attribMinLength"+attrib).val()  > $("#attribMaxLength"+attrib).val()){
							self.errorMsg("Minimum value should not be greater than maximum value for attribute "+attributeName);
							break;
						}
						else if((defaultValue != "" && $("#attribMaxLength"+attrib).val() != "") && parseInt(defaultValue)  > $("#attribMaxLength"+attrib).val()){
							self.errorMsg("Default value for "+attributeName+" should not be more than maximum value "+$("#attribMaxLength"+attrib).val());
							break;
						}
						else if((defaultValue != ""  && $("#attribRegExp"+attrib).val() != "") && defaultValue.match($("#attribRegExp"+attrib).val())){
							self.errorMsg("Invalid Default value for "+attributeName+" rather the value should be of "+$("#attribRegExp"+attrib).val());
							break;
						}
						
					}*/


					for(var t in optionsArr){
						if(optionsArr[t].trim().length < 2){
							//self.errorMsg(uiConstants.componentConfig.OPTION_MIN_LENGTH_ERROR);
							showError("#divCompAddEdit #attribMultipleValues"+attrib, uiConstants.componentConfig.OPTION_MIN_LENGTH_ERROR);
					    	self.errorMsg("#divCompAddEdit #attribMultipleValues"+attrib);
						}
						else if(!tagValidation(optionsArr[t].trim())){
							//self.errorMsg(uiConstants.componentConfig.INVALID_OPTION_NAME);
							showError("#divCompAddEdit #attribMultipleValues"+attrib, uiConstants.componentConfig.INVALID_OPTION_NAME);
					    	self.errorMsg("#divCompAddEdit #attribMultipleValues"+attrib);
						}
					}

					for(var options in optionsArr){
						if(attributeOptionsNewToAddArr.indexOf(optionsArr[options].trim()) != -1){
							optionsObjArr.push({"attributeOptionId":0, "attributeOptionName":optionsArr[options].trim()});
						}
						else{
							optionsObjArr.push({"attributeOptionId":getAttributeOptionIdByName(optionsArr[options].trim()), "attributeOptionName":optionsArr[options].trim()});
						}
					}

					var attribObj = $.grep(self.attributesMasterArr(), function(evt){ return evt.attributeName == $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(); })[0];
					
					compAttribsArr.push({
						"attributeName": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(),
        				"attributeId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
        				"attributeTypeId":  parseInt($("#attribType"+attrib).val()),
        				"attributeType":  $("#attribType"+attrib+" option:selected").text(),
        				"isMandatory": $("#attribMandatory"+attrib).prop('checked')?1:0,
        				"attributeDefaultValue": $("#attribDefaultValue"+attrib).val(),
        				"attributeMinLength": $("#attribMinLength"+attrib).val() == ""?0:parseInt($("#attribMinLength"+attrib).val()),
        				"attributeMaxLength": $("#attribMaxLength"+attrib).val() == ""?0:parseInt($("#attribMaxLength"+attrib).val()),
        				"attributeRegEx":  $("#attribRegExp"+attrib).val(),
        				"attributeOptions":  $("#attribType"+attrib+" option:selected").text() == "DropDown"? optionsObjArr:[],
        				"componentAttributeMappingId":  $("#attribMappingId"+attrib).val() == "" ? 0: parseInt($("#attribMappingId"+attrib).val()),
							"isCustom": $("#attribIsCustom"+attrib).val() == "" ? 1: parseInt($("#attribIsCustom"+attrib).val())
						});
				}
			}

			if(this.errorMsg() == ""){
				if(uiConstants.common.DEBUG_MODE)console.log(tagsNewToAddArr);
				if(uiConstants.common.DEBUG_MODE)console.log(tagsToDeleteArr);

				for(var tag in tagsArr){
					if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
						tagsObjArr.push({"tagId":0, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
					}
					else{
						tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
					}
				}

				for(tag in tagsToDeleteArr){
					tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
				}

				var componentObj = $.grep(self.compNamesArr(), function(evt){ return evt.componentName == $("#compNameList_chosen span")[0].innerHTML.trim() });
				var selVersionObjArr = [];
				selVersionObjArr = $.grep(self.versionsArr(), function(evt){ return evt.version == $("#versionList_chosen span")[0].innerHTML; });

				var compObj = {
					"index":1,
					"componentTypeIds": [parseInt(self.componentTypeId())],
					"componentName": (self.currentViewIndex() == uiConstants.common.EDIT_VIEW && !self.isModal()) ? self.configName() : $("#compNameList_chosen span")[0].innerHTML.split("(Inactive)")[0].trim(),
					"componentId": componentObj.length ? componentObj[0].componentId : 0,
					"componentVersion": {
						"versionId":selVersionObjArr.length ? selVersionObjArr[0].versionId : 0, 
						"version":$("#versionList_chosen span")[0].innerHTML
					},
					"description": self.description(),
					"componentAttributes": compAttribsArr,
					"tags": tagsObjArr,
					"status" : self.compStatus()?1:0,
					"clonedFlag": (self.currentViewIndex() == uiConstants.common.CLONE_VIEW)?1:0 ,
					"clonedVersionId": self.clonedVersionId()};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(compObj));
				var componentId = 0;
				if(compNameOptionFound){
					componentId = $("#compNameList").val();
				}

				
				console.log("Ismodal : "+self.isModal()+" "+self.currentViewIndex() +" componentId : "+componentId);

				if(self.isModal() || self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || (self.currentViewIndex() == uiConstants.common.CLONE_VIEW)){
					if(uiConstants.common.DEBUG_MODE)console.log("++++++++++ ADD +++++++++++++++++");
					if(uiConstants.common.DEBUG_MODE)console.log(compObj);
					requestCall(uiConstants.common.SERVER_IP + "/component", "POST", JSON.stringify(compObj), "addSingleComponent", successCallback, errorCallback);
				}	
				else{
					if(uiConstants.common.DEBUG_MODE)console.log("++++++++++ EDIT +++++++++++++++++");
					if(uiConstants.common.DEBUG_MODE)console.log(compObj);
					requestCall(uiConstants.common.SERVER_IP + "/component/" + componentId, "PUT", JSON.stringify(compObj), "editSingleComponent", successCallback, errorCallback);
				}
			}
		}
		
		function editSingleComponent(compObj){
			setConfigValues(compObj);
			setComponentUneditable(false,compObj);	
		}

		function viewComponent(compObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleComponent(compObj);			
		}

		function setComponentUneditable(isInactiveEdit,obj){
			//$('#txtName').prop('readonly', true);
			var selAttribsArr = obj[0].componentAttributes;
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW ){
				$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
				$('#versionList').prop('disabled', true).trigger("chosen:updated");
				$('#compNameList').prop('disabled', true).trigger("chosen:updated");
				$('#txtDescription').prop('readonly', true);
				$('#component-tokenfield-typeahead').tokenfield('readonly');
				$("#compAtribsList").find("input,button,select").attr("disabled", "disabled");
				$('#attribList'+ attrib).trigger("chosen:updated");
				if(!isInactiveEdit){
					//document.getElementById("compStatus").disabled = true;
					$("[name='compStatus']").bootstrapSwitch('disabled',true);
					self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
				}
				self.enableAttribs(false);
				for(var attrib in selAttribsArr){		
					$('#attribList'+ attrib).trigger("chosen:updated");
					$("#attribDelBtn"+ attrib).css('visibility','hidden');
				}

				self.showAddDelBtns(0);

				$("#divCompAddEdit .chosen-container b").css("display", "none");
			}
			else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.compStatus() ==false){
				$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
				$('#versionList').prop('disabled', true).trigger("chosen:updated");
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#component-tokenfield-typeahead').tokenfield('readonly');
				$("#compAtribsList").find("input,button,select").attr("disabled", "disabled");
				$('#attribList'+ attrib).trigger("chosen:updated");

				for(var attrib in selAttribsArr){			
					if(selAttribsArr[attrib].isCustom == 1){
						self.enableAttribs(false);
						$('#attribList'+ attrib).trigger("chosen:updated");
						$("#attribMultipleValues"+ attrib).tokenfield('readonly');
						$("#attribMinLength"+ attrib).prop('disabled',true);
						$("#attribMaxLength"+ attrib).prop('disabled',true);
						$("#attribRegExp"+ attrib).prop('disabled',true);
						$("#attribDefaultValue"+ attrib).prop('disabled',true);	
						$("#attribMandatory"+ attrib).prop('disabled',true);
						$("#attribDelBtn"+ attrib).css('visibility','hidden');
					}
					else{
						$("#attribDelBtn"+ attrib).css('visibility','hidden');
					}
				}
				self.showAddDelBtns(0);
				if(!isInactiveEdit){
					//document.getElementById("compStatus").disabled = false;
					$("[name='compStatus']").bootstrapSwitch('disabled',false);
				}
			}else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.compStatus() == true){
				$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
				if(self.selectedConfigRows()[0].isCustom == 0){
					$('#versionList').prop('disabled', true).trigger("chosen:updated");
					$('#txtName').prop('readonly', true);
				}
				
				//$('#component-tokenfield-typeahead').tokenfield('readonly');
				for(var attrib in selAttribsArr){
					if(selAttribsArr[attrib].isCustom  == 1){
						self.enableAttribs(true);
						$('#attribList'+ attrib).trigger("chosen:updated");
						$("#attribDelBtn"+ attrib).css('visibility','block');
					}
					else{
						$('#txtDescription').prop('readonly', true);
						$("#compAtribsList").find("input,button,select").attr("disabled", "disabled");
						$('#attribList'+ attrib).trigger("chosen:updated");

						$("#attribDelBtn"+ attrib).css('visibility','hidden');
					}
				}
				self.showAddDelBtns(1);

			}
			else{
				$('#compTypeList').prop('disabled', false).trigger("chosen:updated");
				$('#versionList').prop('disabled', false).trigger("chosen:updated");
				self.enableAttribs(true);
				self.showAddDelBtns(1);
				for(var attrib in selAttribsArr){			
					$("#attribDelBtn"+ attrib).css('visibility','block');
				}
			}
		}

		function cloneComponent(compObj){
			setConfigValues(compObj);
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigValues(compObj){
			if(uiConstants.common.DEBUG_MODE)console.log("===================COMPONENT EDIT============================");
			if(uiConstants.common.DEBUG_MODE)console.log(compObj[0]);
			self.attributesArr([]);

			self.clonedVersionId(compObj[0].componentVersionId);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.description("");
			}
			else{
				self.description(compObj[0].description);
			}

			for(var tagObj in compObj[0].tags){
				tagsNameArr.push(compObj[0].tags[tagObj].tagName);
			}

			var selAttribsArr = compObj[0].componentAttributes;
			for(var attrib in selAttribsArr){
				attributeOptionsEditArr=[];
				self.attributesArr.push({
					"attributeId": parseInt(selAttribsArr[attrib].attributeId),
					"attributeName": selAttribsArr[attrib].attributeName,
					"attributeTypeId":  parseInt(selAttribsArr[attrib].attributeTypeId),
					"attributeType": selAttribsArr[attrib].attributeType,
					"isMandatory": selAttribsArr[attrib].isMandatory,
					"attributeDefaultValue": selAttribsArr[attrib].attributeDefaultValue,
    				"attributeMinLength": selAttribsArr[attrib].attributeMinLength,
    				"attributeMaxLength":selAttribsArr[attrib].attributeMaxLength,
    				"attributeRegEx":  selAttribsArr[attrib].attributeRegEx,
    				"attributeOptions":  selAttribsArr[attrib].attributeOptions,
    				"componentAttributeMappingId": selAttribsArr[attrib].componentAttributeMappingId,
    				"isCustom": selAttribsArr[attrib].isCustom
				});

				jQuery(".chosen").chosen({
					search_contains: true	
				});

				setChosenKeyInput(attrib);

				$('#attribMultipleValues'+attrib).tokenfield({
					typeahead: [null, { source: attribOptionsBH.ttAdapter() }]
				});
				//attribute option changes
				for(var optionObj in self.attributesArr()[attrib].attributeOptions){
					attributeOptionsEditArr.push(self.attributesArr()[attrib].attributeOptions[optionObj].attributeOptionName);
				}
				
				$("#attribList"+ attrib).val(selAttribsArr[attrib].attributeId).trigger('chosen:updated');
				

				if(slugify($("#attribList"+attrib+" option:selected").text()) == "hostaddress" || slugify($("#attribList"+attrib+" option:selected").text()) == "host address"){
					$("#attribDelBtn"+attrib).addClass("confButtonDisabled");
				}

				self.onAttributeListItemSelection(attrib);
				self.onChosenDropdownClose(attrib);
								
				$("#attribType"+attrib+" option").filter(function () { return $(this).html() == selAttribsArr[attrib].attributeType;}).prop('selected', true).trigger('chosen:updated');
				$("#attribType"+ attrib).trigger('change');
				$("#attribMandatory"+attrib).prop("checked", selAttribsArr[attrib].isMandatory == 1);
				
				//attribute option changes
				$('#attribMultipleValues'+attrib).bind('tokenfield:createdtoken');
				$('#attribMultipleValues'+attrib).bind('tokenfield:removedtoken');
				$('#attribMultipleValues'+attrib).tokenfield('setTokens', attributeOptionsEditArr);
			}

			$(".attribTypeChosen").trigger('chosen:updated');

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			self.compId(compObj[0].componentId);
			self.componentTypeId(compObj[0].componentTypeId);

			/*if(!self.componentsArr().find( function( ele ) {return ele.componentTypeId && ele.componentTypeId === compObj[0].componentTypeId;} )){
				self.componentsArr.push({
					"componentTypeId": compObj[0].componentTypeId,
					"componentType": compObj[0].componentType,
					"components" : [],
					"isActive": false
				});
				$("#compTypeList_chosen span").first().addClass("inactiveOptionClass");
			}*/

			//$("#compTypeList").val(compObj[0].componentTypeId).trigger('chosen:updated');

			if($.grep(self.componentsArr(), function(e){ return  e.componentTypeId == compObj[0].componentTypeId; }).length){
				$("#compTypeList").val(compObj[0].componentTypeId).trigger('chosen:updated');
			}
			else{
				$("#compTypeList").val("0").trigger('chosen:updated');
			}

			setCompNames($("#compTypeList").val());
			
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				$("#compNameList").val(0).trigger('chosen:updated');
			}
			else{
				self.configName(compObj[0].componentName);

				if($.grep(self.compNamesArr(), function(e){ return  e.componentId == compObj[0].componentId; }).length){
					$("#compNameList").val(compObj[0].componentId).trigger('chosen:updated');
				}
				else{
					$("#compNameList").val("0").trigger('chosen:updated');
				}

				$("#compNameList_chosen span")[0].innerHTML = $("#compNameList_chosen span")[0].innerHTML.split("(Inactive)")[0].trim();


				/*if(!self.compNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === compObj[0].componentId;} )){
					self.compNamesArr.push({
						"componentId": compObj[0].componentId,
						"componentName": compObj[0].componentName,
						"isActive": false
					});
					$("#compNameList_chosen span").first().addClass("inactiveOptionClass");
				}

				if(uiConstants.common.DEBUG_MODE)console.log(self.compNamesArr());
				$("#compNameList").val(compObj[0].componentId).trigger('chosen:updated');*/
				setCompVersions($("#compNameList").val());

				var selVersionObj = {
						"versionId": compObj[0].componentVersionId,
						"version": compObj[0].componentVersion,
						"isActive": false
					};

				if(self.versionsArr() == undefined){
					self.versionsArr([selVersionObj]);
					$("#versionList_chosen span").first().addClass("inactiveOptionClass")
				}
				else if(!self.versionsArr().find( function( ele ) {return ele.versionId && ele.versionId === compObj[0].componentVersionId;} )){
					self.versionsArr.push(selVersionObj);
					$("#versionList_chosen span").first().addClass("inactiveOptionClass");
				}

				if(uiConstants.common.DEBUG_MODE)console.log(self.versionsArr());

				$("#versionList").val(compObj[0].componentVersionId).trigger('chosen:updated');
			}
			
			self.compStatus(compObj[0].status);

			$('#compStatus').bootstrapSwitch('state',self.compStatus());

			if(compObj[0].isCustom === 0){
				//document.getElementById("compStatus").disabled = true;
				$("[name='compStatus']").bootstrapSwitch('disabled',true);
				$('#txtName').prop('disabled', true);
			}
			else{
				$('#txtName').prop('disabled', false);
			}

			$('#component-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
		}

		function setChosenKeyInput(indx){
			$("#attribList"+indx).chosen({}).on('chosen:showing_dropdown', function(){
				if($("#attribList"+indx+"_chosen span").text() != uiConstants.common.ENTER_SELECT){
					$("#attribList"+indx+"_chosen .chosen-search").find("input").val($("#attribList"+indx+"_chosen span").text());
				}
			});

			$("#attribList"+indx+"_chosen .chosen-search").find("input").on("keyup", function (evt) {
				var attribIndex = ($(this).parent().parent().parent().attr('id')).substring(10).split("_")[0];//.parentNode.parentNode.attr('id'));
				
				if($(this).val() != undefined && $(this).val() != ""){

		        	$("#attribList"+indx+"_chosen span").text($(this).val());
				}

				if(evt.which == 13){
					$("#attribList"+indx+"_chosen").parent().trigger("click");
				}
		    });

			$("#attribList"+indx).chosen({}).on('chosen:hiding_dropdown', function(){
				//console.log($(this).attr('id'));
				var attrIndex = $(this).attr('id');
				var attribNameTxt = "";

        		if(!$('#compNameList_chosen .chosen-results').find("li.active-result.result-selected")){
        			attribNameTxt = $('#attribList'+indx+'_chosen .chosen-search input[type="text"]').val();
           		}

	           	if(attribNameTxt!=""){

	           		$('#attribList'+indx+' > option').each(function(){
	           			if($(this).text()==attribNameTxt) {
	 						$(this).parent('select').val($(this).val());
	 						$("#attribList"+indx).val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});
		}

		this.cancelComponent = function(){
			if(self.isModal()){
				$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Component Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		this.addAttribute = function(scrollPage){
			self.attributesArr.push({"componentAttributeMappingId":0,"attributeId":0,"attributeName":"", "attributeTypeId":0, "attributeType":"", "isMandatory":0,"attributeDefaultValue":"","attributeOptions":[],"attributeMinLength":"","attributeMaxLength":"","attributeRegEx":"","isCustom":1});
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			var attribIndex = self.attributesArr().length-1;
			setChosenKeyInput(attribIndex);
			
			/*$("#attribList"+ (self.attributesArr().length-1) +"_chosen .chosen-search").find("input").on("keyup", function (evt) {
				var attribIndex = ($(this).parent().parent().parent().attr('id')).substring(10).split("_")[0];//.parentNode.parentNode.attr('id'));
				
				if($(this).val() != undefined && $(this).val() != ""){

		        	$("#attribList"+ attribIndex +"_chosen span").text($(this).val());
				}
		    });

			$("#attribList"+ (self.attributesArr().length-1)).chosen({}).on('chosen:hiding_dropdown', function(){
				//console.log($(this).attr('id'));
				var attrIndex = $(this).attr('id');
				var selCompNameTxt = $('#attribList'+ (self.attributesArr().length-1) +'_chosen .chosen-search input[type="text"]').val();
           		
	           	if(selCompNameTxt!=""){

	           		$('#attribList'+ (self.attributesArr().length-1) +' > option').each(function(){
	           			if($(this).text()==selCompNameTxt) {
	 						$(this).parent('select').val($(this).val());
	 						$("#attribList"+ (self.attributesArr().length-1)).val($(this).val()).trigger('chosen:updated');
	 					}
	 				});
	           	}
        	});*/

			//initialization with tokenfields for attribute options
        	$('#attribMultipleValues'+(self.attributesArr().length-1)).tokenfield({
				typeahead: [null, { source: attribOptionsBH.ttAdapter() }]
			});

        	console.log($("#attribList"+ (self.attributesArr().length-1) +"_chosen").position().top);
        	if(scrollPage){

        		var container = $('#atrribListScroller'),
				scrollTo = $('#attribList' + (self.attributesArr().length-1) +"_chosen");

				container.animate({
					scrollTop: scrollTo.offset().top - container.offset().top + container.scrollTop()
				});

        	//	$("#atrribListScroller").animate({scrollTop: $("#attribList"+ (self.attributesArr().length-1) +"_chosen").position().top});
				//scrollToPos($("#attribList"+ (self.attributesArr().length-1) +"_chosen").position().top, 500)
        	}

			if(uiConstants.common.DEBUG_MODE)console.log($("#versionList"));
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(compTag in self.compTagArr()){
				if(self.compTagArr()[compTag].tagName.trim() == tagName.trim()){
					return parseInt(self.compTagArr()[compTag].tagId);
				}
			}
			return 0;
		}

		function getAttributeOptionIdByName(attributeOptionName){
			if(attributeOptionName == "" || attributeOptionName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(attributeOptionName);
			for(attribs in self.attributeOptionsArr()){
				if(self.attributeOptionsArr()[attribs].attributeOptionName.trim() == attributeOptionName.trim()){
					return parseInt(self.attributeOptionsArr()[attribs].attributeOptionId);
				}
			}
			return 0;
		}

		$('#messageDialogBox').on('hidden.bs.modal', function () {
			if(self.isModal()){
				$("body").addClass("modal-open");
			}
		});

		function successCallback(data, reqType) {
			if(reqType === "getComponentTag"){
				self.compTagArr(data.result);

				for(var tagData in self.compTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.compTagArr()[tagData].tagName);
					self.compTagAutoCompleteArr.push(self.compTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.compTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.compTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #component-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#component-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('.panel-body #component-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#component-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				compTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getAttributeList"){
				self.attributesMasterArr(data.result);

				attributeTypeListLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getAttributeType"){
				self.attributesType(data.result);

				attributeTypeLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "addSingleComponent"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPONENT,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.componentConfig.ERROR_ADD_COMPONENT, "error");
					}

					
				}
				else{
					self.cancelComponent();
					showMessageBox(uiConstants.componentConfig.SUCCESS_ADD_COMPONENT);

					if(!self.isModal()){
						params.curPage(1);
					}
					else{
						self.compName($("#compNameList_chosen span")[0].innerHTML);
					}
				}
			}
			else if(reqType === "editSingleComponent"){	
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_COMPONENT,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.componentConfig.ERROR_UPDATE_COMPONENT, "error");
					}
				}
				else{
					showMessageBox(uiConstants.componentConfig.SUCCESS_UPDATE_COMPONENT);
					self.cancelComponent();
					if(!self.isModal()){
						params.curPage(1);
					}
				}
			}
			else if(reqType === "getCompTypeVersion"){
				var selCompTypeId = $("#compTypeList").val();

				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				//self.componentsArr(data.result);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.componentsArr(getMasterList(data.result, "componentTypeId", [self.selectedConfigRows()[0].componentTypeId], true));
				}
				else{
					self.componentsArr(getMasterList(data.result, "componentTypeId", null, false));
				}

				$("#compTypeList").trigger('chosen:updated');

				if(self.compType() == ""){

					compTypeVersionLoaded = 1;
					onMastersLoad();
				}
				else{
					$("#compTypeList").val(selCompTypeId).trigger('chosen:updated');
				}

				self.compType("");
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getComponentTag"){
				showMessageBox(uiConstants.componentConfig.ERROR_GET_COMPONENT_TAGS, "error");
			}
			else if(reqType === "getAttributeList"){
				showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_LIST, "error");
			}
			else if(reqType === "getAttributeType"){
				showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_TYPES, "error");
			}
			else if(reqType === "addSingleComponent"){
				showMessageBox(uiConstants.componentConfig.ERROR_ADD_COMPONENT, "error");
			}
			else if(reqType === "editSingleComponent"){
				showMessageBox(uiConstants.componentConfig.ERROR_UPDATE_COMPONENT, "error");
			}
			else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}
		}
	}

	ComponentAddEdit.prototype.dispose = function() { };
	return { viewModel: ComponentAddEdit, template: templateMarkup };
});