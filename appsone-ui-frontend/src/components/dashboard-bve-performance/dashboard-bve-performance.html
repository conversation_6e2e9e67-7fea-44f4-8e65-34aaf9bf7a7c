<div data-bind="template: {afterRender: renderHandler}">
	<!-- ko if: showChart-->
	<div data-bind=" component:{ name: chartComponentName, 
			 				 	 params: { 'podId': podId, 
				 				 		   'chartDataObj' : chartDataObj,
				 				 		   'chartContId':chartContId
		 				 		   		 }
 				 		   	   }" >
   	</div>
	<!-- /ko -->

	<!-- ko if: noDataAvailable -->
<!-- 	<div class="alert alert-info">
	  No Data Available for Business Value Extractor(BVE). Please try after some time
	</div> -->
	<!-- /ko -->

	<!-- ko if: noDataAvailable-->
		<div class="alert alert-info">
			Please select a transaction and a corresponding extractor to see BVE data
		</div>
	<!-- /ko -->

	<!-- ko if: showExpandableView -->
	<dashboard-pod-expandedview params="podId: podId, 
										dualView: dualView, 
										podTitle: podTitle,
										chartComponentName: 'single-axis-line-chart',
									 	'chartDataObj': chartDataObjForExpandableView,
									 	offSet: offSet,
										timeUnit: timeUnit">
	</dashboard-pod-expandedview>
	<!-- /ko --> 
</div>
		 				 		   	
		

	
	


