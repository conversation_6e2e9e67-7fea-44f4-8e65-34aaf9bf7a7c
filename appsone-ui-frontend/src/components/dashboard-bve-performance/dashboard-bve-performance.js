define(['jquery','knockout', 'text!./dashboard-bve-performance.html', 'knockout-es5','d3','c3','ui-constants','ui-common'], function($, ko, templateMarkup, koES5, d3, c3, uiConstants, uiCommon){

	function DashboardBvePerformance(params) {
    	var self = this;
    	
    	this.timeUnit = params.timeUnit;
    	this.offSet = params.offSet;
    	this.podId = params.podId;
    	this.dualView = params.dualView;
	    this.podTitle = params.podTitle;
	    this.podName = params.podName;
	    this.selectedMonitoringType = params.monitorType;
	    this.isModal = params.isModal;
	    this.isGraphView = true;
	    this.chartComponentName = "single-axis-line-chart";

	    //this.LCPodId = ko.observable(params.podId+'_lines-chart');
	    this.LCPodId = 'bve_'+this.podId;
	    //this.ModalLCPodId = params.podId+'_Modal_lines-chart';
	    this.ModalLCPodId = "bve_"+ this.podId;
	    this.currentPodBodyHeight = '';
	    this.currentPodBodyWidth = '';
	    this.chartPaletteColors = "#0075c2,#1aaf5d,#ff4000,#0000ff,#524e4e"; //RGB color code seprated by comma string
	    
	    this.timeWindow = [];
	   /* this.transactionVolume = [];
	    this.responseTime = [];*/
	//    this.slowTxnGridHeaders = [];
	//    this.slowTxnGridData = [];
	    this.chartDataObj = {};
	    this.chartDataObjForExpandableView = {};
	    this.showChart = false;
	    this.showExpandableView = true;
	    this.transactionExtractorSelected = false;

	    params.podVMContainer = self;

	    koES5.track(this);

	    this.chartContId = ko.observable("chartContId_" + self.podId);
	    this.noDataAvailable = ko.computed(function(){
	    	var $chartContId = $("#"+self.chartContId());
	      	if(self.transactionExtractorSelected){
	    		self.showChart = true;
	    		return false;
	    	}
	    	else{
	    		self.showChart = false;
	    		return true;
	    	}
	    });

	    this.renderHandler = function(){  
    	    self.currentPodBodyHeight = $('#pod_'+params.podId).height()-35;
	        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
	        self.currentPodBodyWidth = $('#pod_'+params.podId).width()-35;

	        self.getGraphData();
	    }

	    this.getGraphData = function(){
	    	if(self.podName === "BVE"){
	    		var queryParams = '';

	    		self.selectedMonitoringType.forEach(function(item){
	    			queryParams += item +'&';
	    		});

	    		//var url = uiConstants.common.SERVER_IP+"/application/1/dashboardData?dataType=timeSeries&scenarioType=transactionAggregate&fromTime=1480422840000&toTime=1480424340000&monitoringType=EUE&aggregateLevel=1&pageSize=0&pageNumber=0&aggregateBy=something&orderBy=asc"
	    		var url = "http://www.mocky.io/v2/5847cb5b3f0000402bfe6a14?callback=?";

	    		self.transactionExtractorSelected = Boolean(params.dashboardObject.selectedTransaction && params.dashboardObject.selectedExtractor);
	    		if(self.transactionExtractorSelected){
	    			requestCall(url,"GET", '',"BVE",self.successCallBack, self.errorCallBack);
	    		}
	    		else{
	    			console.log("Please select a transaction and a corresponding extractor to see BVE data");
	    			return;
	    		}

	        	/*requestCall(url,"GET", '',"BVE",self.successCallBack, self.errorCallBack);*/
	    	}
	    }

    	this.successCallBack = function(data, reqType){
    		
    		if(reqType === "BVE"){
		    	if(data.responseStatus === "success"){
		    		var dataResult = data.graphData;
		    		var yAxisDataSet = dataResult.yAxis[0];
        			var xAxisLabel = dataResult.xAxis[0].label;
        			var yAxisLabel = yAxisDataSet.label;
        			
        			//This will be loaded based on a calculation
        			var timeWindow = ["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"];//self.getTimeWindow(dataResult.startTime, dataResult.collationInterval, "3600");//dataResult.timeSeries;
        			var responseTime  = [];
        			
        			var bveList    = [];
        			var responseTimeForChart = [];

        			var index = 0;
        			$.each(yAxisDataSet.data, function(item){
        				var self = this;
        				var currentKey = item;//Object.keys(self)[0];

        				bveList.push(currentKey);
        				responseTime.push(self);

        				responseTimeForChart[index] = [];
        				responseTimeForChart[index].push(bveList[index]);
        				responseTimeForChart[index].push.apply(responseTimeForChart[index],responseTime[index]);
        				index++;

        			});

        			self.chartDataObj = {
        				chartHeight: self.currentPodBodyHeight-5,
        				chartWidth: self.currentPodBodyWidth-5,
        				xAxisLabel: xAxisLabel,
        				yAxisLabel: yAxisLabel,
        				chartPaletteColors: self.chartPaletteColors,
        				timeWindowSet: timeWindow,
        				yaxisDataSet: responseTimeForChart,
        				podName: reqType,
        				gridHeader: [
					      {'columnTitle':'bve_column_1', 'displayName':'200' },
					      {'columnTitle':'bve_column_2', 'displayName':'300' },
                          {'columnTitle':'bve_column_3', 'displayName':'400' },
                          {'columnTitle':'bve_column_4', 'displayName':'500' }
					    ],
        				gridBody: responseTimeForChart
    				};

    				self.chartDataObjForExpandableView = $.extend({},self.chartDataObj);// Clonning object for expandable View Chart/Grid with a different width & Height

    				self.chartDataObjForExpandableView.chartHeight = $('body').height()*0.70;
	        		self.chartDataObjForExpandableView.chartWidth = $('body').width()*0.85;

    				self.showChart = true;
		    	}
	        }
    	};

    	this.errorCallBack = function(reqType){
    		if(reqType === "BVE"){
    			console.log("Error in getting Graph Data for POD - "+ "BVE");
	        }	        
    	};
	}

    DashboardBvePerformance.prototype.dispose = function() {}

    return { viewModel: DashboardBvePerformance, template: templateMarkup };

});

