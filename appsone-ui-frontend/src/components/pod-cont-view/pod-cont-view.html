
<div class="device-xs visible-xs"></div>
<div class="device-sm visible-sm"></div>
<div class="device-md visible-md"></div>
<div class="device-lg visible-lg"></div>
<div class="device-xl visible-xl"></div>

<div data-bind="if : podList.length > 0">
    <div class="podListContainer" data-bind="template: {afterRender: renderHandler}">
        <div id="podListSortable" class="grid-stack grid-stack-N" data-gs-animate="yes" data-bind="foreach : podList">
        
            <!-- Check View Data -->
         <!--    <span data-bind="text: console.log('Inside Pod Container View - 1')"></span>  
            <span data-bind="text: console.log($data)"></span>   -->
            <!-- Check View Data -->

            <!-- POD Container Level Created For Each Level-->
            <!-- This div creates containers for each pod. So if we have 5 pods, we have 5 containers-->
            <!-- Use style="border: 1px solid black;" to view them-->
            <div class="podListItems panel-resizable grid-stack-item" 
                 data-bind="attr:{ 'id' : 'pod_'+$data.podId, 
                                   'data-gs-x':$data.xPosition, 
                                   'data-gs-y':$data.yPosition,
                                   'data-gs-width':$data.actualwidth, 
                                   'data-gs-height': $data.actualHeight,
                                   'data-gs-min-height':$data.min_height, 
                                   'data-gs-min-width':$data.min_width
                                 } ">

            <!-- This is the actual pod module level for each pod with all the box shadows applied-->
            <!-- Apply style="border: 1px solid red;" to see border-->                     
            <div class="pod panel panel-default DBPod grid-stack-item-content">
                    <div class="panel-heading podHeader">
                        <h4 class="panel-title">
                            <div>
                                <span class="clspan" data-toggle="tooltip" data-placement="bottom" 
                                    data-bind=" attr : { title : $data.podTitle }, 
                                                text : $data.podTitle">
                                </span>

                                <!-- POD Header level. Holds all the POD level menu items except POD title-->

                                <!-- Check View Data -->
                    <!--             <span data-bind="text: console.log('Inside Pod Container View - 2')"></span>  
                                <span data-bind="text: console.log($data)"></span>  
                                 <span data-bind="text: console.log($parents[0])"></span>   -->
                                <!-- Check View Data -->

                                <!-- Here $parents[0] refers to the view model object-->


                                <ul class="symbols panel-actions">
                                     <li data-bind=" if: $parents[0].podIconSupport($data.podName, 'fullScreen'),
                                                    attr: { 'data-target':'#podModal_'+$data.podId ,
                                                            'id': $data.podId+'_fullscreen',
                                                            'data-component-name': $parents[0].getComponentName($data.podName)
                                                          },
                                                    click : $parents[0].fullscreenEvent
                                    "> 
                                        <span role="button"><i class="glyphicon glyphicon-fullscreen"></i></span>
                                    </li>

                                    <li class="dropdown">
                                        <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                                            <i class="glyphicon glyphicon-cog"></i>
                                        </span>
                                        <ul class="dropdown-menu pull-right" role="menu" data-bind="style : {'min-width':'100px',  'padding':'5px'}">    
                                            <li class="dropdown-submenu pull-left" data-bind="if : $parents[0].podIconSupport($data.podName, 'DcEum')">
                                                <span tabindex="-1"><i class="glyphicon glyphicon-triangle-left"></i>Mode</span>
                                                <ul class="dropdown-menu" data-bind=" foreach: $parents[0].monitoringTypes,
                                                                                      style : {'min-width':'100px', 'padding':'5px'}">
                                                    <li>
                                                        <input type="checkbox" data-bind="value: $data.monitorType, 
                                                                                          attr :{ 'class': 'monitoring-type-'+$data.podId, 
                                                                                                  'name': 'monitoring-type-'+$data.podId
                                                                                                 },
                                                                                          checked: $parents[1].defaultMonitoringType" />
                                                        <label data-bind="text: $data.label,
                                                                          style: {'padding': '4px', 'vertical-align':'top'}">
                                                        </label>
                                                    </li>
                                                    <br/>
                                                </ul>
                                            </li>
                                        </ul>
                                    </li>

                                    <li data-bind="if: $parents[0].currentDashboard == 'custom', click: $parent.removePodFromDashBoard">
                                        <span role="button"><i class="glyphicon glyphicon-remove"></i></span>
                                    </li>

                                    <li class="dropdown" style="display:none;">
                                        <span class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false">
                                            <i class="glyphicon glyphicon-download-alt"></i>
                                        </span>
                                        <ul class="dropdown-menu pull-right" role="menu" data-bind="style : {'min-width':'100px', 'padding':'5px'}">
                                            <li data-bind="attr:{'id': $data.podId+'_exportPDF'}"><span>PDF Document</span></li>
                                            <li data-bind="attr:{'id': $data.podId+'_exportPNG'}"><span>PNG Image</span></li>                                        
                                            <li data-bind="attr:{'id': $data.podId+'_exportCSV'}"><span>CSV Document</span></li>
                                        </ul>
                                    </li>

                                    <!-- <li data-bind="attr:{'id': $data.podId+'_arrow','data-target': '#'+$data.podId+'_podBody'}" data-toggle="collapse"><span class="close-link" role="button"><i class="glyphicon glyphicon-chevron-down"></i></span></li> -->
                                </ul>
                            </div>
                        </h4>            
                    </div>

                    <!-- Actual POD body-->
                    <div class="panel-collapse collapse in podbody" data-bind="attr :{'id': 'podBody_'+$data.podId}">
                        <div class="panel-body" data-bind="if : $parents[0].podList.length > 0">            
                            
                           <!-- Check View Data -->
                                <span data-bind="text: console.log('Inside Pod Container View - 3')"></span>  
                                <span data-bind="text: console.log($data)"></span>  
                                 <span data-bind="text: console.log($parents[0].getComponentName($data.podName))"></span>  
                           <!-- Check View Data -->






                            <div data-bind="component:{ name: $parents[0].getComponentName($data.podName),
                                                        params: { 'isModal':false,
                                                                  'podId':$data.podId, 
                                                                  'podTitle': $data.podTitle, 
                                                                  'podName': $data.podName,
                                                                  'monitorType': $parents[0].defaultMonitoringType,
                                                                  'offSet': $parents[0].offSet,
                                                                  'timeUnit': $parents[0].timeUnit,
                                                                  'dashboardObject': $parents[0].dashboardObject
                                                                }
                                                      }, 
                                            style:{'height':'auto'} ">
                           </div>
                        </div>
                    </div>
                    <!-- Actual POD body-->

                </div>
                
            </div>          
        </div>
    </div>
</div>