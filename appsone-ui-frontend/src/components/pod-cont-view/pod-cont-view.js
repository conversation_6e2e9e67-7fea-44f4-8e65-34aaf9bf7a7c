define(['lodash','jquery','bootstrap','bootstrap-switch','jquery-ui','jquery-ui-touch-punch','gridstack','knockout','text!./pod-cont-view.html','hasher','ui-constants','ui-common','knockout-es5','moment'], function(lodash,$,bootstrap,bs,jqueryUI,jqueryUITouch,gridstack,ko,templateMarkup,hasher,uiConstants,uicommon, koES5, mmnt) {

  function PODCONTVIEW(params) {

    var self = this;
    
    /* KO ES5 Tracker Start
    *  All the properties which needs to be tracked as KO Observable, should be grouped together 
    */    
    
    self.podList = params.podList;
    self.currentDashboard = params.currentDashboard;
    self.disablePlaceHolder = 1;
    self.currentComponent = "dashboard-grid-view";
    self.selectedCustomPOD;
    self.dashboardMenuObject = params.dashboardObject() || {};
    self.offSet = params.dashboardObject().timeZoneOffset || new Date().getMilliseconds();
    self.timeUnit = params.dashboardObject().selectedTime;
    self.dashboardObject = params.dashboardObject();

    self.monitoringTypes = [
      {
        monitorType: 'DC',
        label: 'DC'
      },
      {
        monitorType: 'EUM',
        label: 'EUM'
      }
    ];
    self.podParams = {};
    self.dualView = false;
    self.defaultMonitoringType = [self.monitoringTypes[0].monitorType]; // default checkbox selected is DC


    
    koES5.track(this);

    /* KO ES5 Tracker End*/
    
    if(uiConstants.common.DEBUG_MODE) console.log(self.currentDashboard);
    if(uiConstants.common.DEBUG_MODE) console.log(self.podList);



    this.podIconSupport = function(type, icon){
      return podIconValidation(type, icon);
    }

    /**
     * This method returns the KO Component Name which will be binded to the POD based on passed POD Type
     * @param  {String} podName - POD  Type
     * @return {String} componentNameForGivenType - The KO Component Name for the given POD Type
     * @todo : 1. Split Grid & Alert POD
     */

     
    this.getComponentName = function(podName) {
      var componentNameForGivenType;

      if(podName === "Alerts_Summary" ){
        componentNameForGivenType = "dashboard-alerts-summary";
        self.dualView = true;
      }
      else if(podName === "grid"){
        componentNameForGivenType = "dashboard-grid-view";
        self.dualView = false;
      }
      else if(podName === "Aggregated_Transactions"){
        componentNameForGivenType = "dashboard-aggregated-txns";
      }
      else if(podName === "Topology" || podName === "doghunt"){
        componentNameForGivenType = "topology-view";
        self.dualView = false;
      }
      else if(podName === "KPI_Performance" || podName === "Appserver KPI Performance"){
        componentNameForGivenType = "dashboard-kpi-performance";
        self.dualView = true;
      }
      else if(podName === "Application_Health"){
        componentNameForGivenType = "health-view";
         self.dualView = false;
      }
      else if(podName === "Transaction_Health" || podName === "health"){
        componentNameForGivenType = "dashboard-transaction-health";
        self.dualView = true;
      }
      else if(podName === "Slow_Transactions" || podName === "High_Volume_Transactions"){
        componentNameForGivenType = "dashboard-txn-performance";
        self.dualView = false;
      }
      else if(podName === "Transaction_Performance"){
        componentNameForGivenType = "dashboard-multi-txn-performance";
        self.dualView = false;
      }
      else if(podName === "real"){
        componentNameForGivenType = "real-time-chart";
        self.dualView = false;
      }
      else if(podName === "BVE"){
        componentNameForGivenType = "dashboard-bve-performance";
        self.dualView = false;
      }
      
      if(uiConstants.common.DEBUG_MODE) console.log("Pod Type:"+ podName +";"+ "Component Binding: " + componentNameForGivenType +"\n");
      return componentNameForGivenType; 
  }


  this.renderHandler = function(){ 
    //intialize switch in the all pod's.
    $('.BSswitch').bootstrapSwitch('state', true);      

    //Toggle fullscreen
    /*$(".podFullScreen").click(function (e) {
        e.preventDefault();
        
        var $this = $(this);
    
        if ($this.children('i').hasClass('glyphicon-resize-full'))
        {
            $this.children('i').removeClass('glyphicon-resize-full');
            $this.children('i').addClass('glyphicon-resize-small');
        }
        else if ($this.children('i').hasClass('glyphicon-resize-small'))
        {
            $this.children('i').removeClass('glyphicon-resize-small');
            $this.children('i').addClass('glyphicon-resize-full');
        }
        $(this).closest('.pod').toggleClass('panel-fullscreen').toggleClass('grid-stack-item-content');
        $('body').toggleClass('pod-open');

      });*/


    //Gridstak lib for Drag & Drop and REsize configurations
    if(self.currentDashboard == "custom"){
      disableDrag = false;
      disableResize = false;
    }
    else{         
      disableDrag = true;
      disableResize = true;
    }

    $('.grid-stack').gridstack({
      float: false,
      draggable : {
        scroll:false,
        handle:'.podHeader',
      },
      animate: true,
      disableDrag:disableDrag,
      disableResize:disableResize,
      verticalMargin: 10,
      width: 12,
      alwaysShowResizeHandle: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent),
      resizable: {
        handles: 'e, se, s, sw, w, n'
      },
      rtl: 'auto',
    });   

  };



      this.resizeGrid = function () {
        var grid = $('.grid-stack').data('gridstack');

        if (self.isBreakpoint('xs')) {
          $('#grid-size').text('One column mode');
          
        } 
        else if (self.isBreakpoint('sm')) {
          grid.setGridWidth(3);
          $('#grid-size').text(3);
        } 
        else if (self.isBreakpoint('md')) {
          grid.setGridWidth(6);
          $('#grid-size').text(6);
        } 
        else if (self.isBreakpoint('lg')) {
          grid.setGridWidth(12);
          $('#grid-size').text(12);
        }
        else if (self.isBreakpoint('xl')) {
          grid.setGridWidth(18);
          $('#grid-size').text(18);
        }
      };

      this.isBreakpoint = function(alias) {
        return $('.device-' + alias).is(':visible');
      }


      this.fullscreenEvent = function(data){  
        self.podId = data.podId; 
        var modalID = 'podModal_'+data.podId;
        var podID = data.podId;

        self.currentComponent = self.getComponentName(data.podName);  
        
        $('#'+modalID).appendTo("body").modal('show');

      }         
    }
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  PODCONTVIEW.prototype.dispose = function() { };
  
  return { viewModel: PODCONTVIEW, template: templateMarkup };

});