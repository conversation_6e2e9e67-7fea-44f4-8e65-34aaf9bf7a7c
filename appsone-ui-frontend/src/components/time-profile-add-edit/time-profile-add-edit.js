define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./time-profile-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','jQuery-plugins','fsstepper','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,jQueryPlugins,fsstepper,floatThead) {

	function TimeProfileAddEdit(params) {
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = ko.observableArray();
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableConfig = ko.observable(true);
  		this.modalTitle = ko.observable();
  		this.contentType = ko.observable();
  		this.dayOptionsArr = ko.observableArray();
  		this.weekDaysArr = ko.observableArray();//["SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"]);
  		//this.addedModalTimeProfile = params.addedModalTimeProfile;
  		this.isModal = ko.observable(false);
  		this.dayWiseSlotsArr = ko.observableArray();
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		
		var configTagLoaded = 0;
		//var dayOptionsLoaded = 0;
		var weekDaysLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
		var isErrorExists = 0;

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			//$("input[type=number]").stepper();

			var $tab = $('#timeProfileAddEditTable table');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			$(".floatThead-floatContainer").css("padding-left", "0px");
			$(".floatThead-floatContainer").css("padding-right", "0px");

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			if(params.isModal){
				self.isModal(true);
			}
			self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW && self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			$('#time-tokenfield-typeahead')
			.on('tokenfield:createdtoken', function (e) {

				if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
					tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
				}
				var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
				//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
				if(tagIndex != -1){
					self.configTagAutoCompleteArr.splice(tagIndex, 1);
				}

				$('#time-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			})

			.on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			.on('tokenfield:removedtoken', function (e) {
				if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
					tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
				}
				tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
				var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

				if(tagIndex != -1){
					self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
					self.configTagAutoCompleteArr.sort();
				}

				$('#time-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			});

	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=TimeProfile", "GET", "", "getTimeProfileTag", successCallback, errorCallback);
	        //requestCall(uiConstants.common.SERVER_IP + "/dayoptions", "GET", "", "getDayOptions", successCallback, errorCallback);
	        requestCall(uiConstants.common.SERVER_IP + "/days", "GET", "", "getWeekDays", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		this.addTimeSlot = function(parentIndx, indx){
			var daySlot = self.dayWiseSlotsArr()[parentIndx];
			daySlot.splice((indx+1),0,{});

			if(daySlot.length == 1){
				$("#removeTimeSlot"+parentIndx+"0").css("visibility", "hidden");
			}
			else{
				$("#removeTimeSlot"+parentIndx+"0").css("visibility", "visible");
			}
			self.dayWiseSlotsArr.splice(parentIndx, 1, daySlot);

			for(var slot in self.dayWiseSlotsArr()[parentIndx]){
				$("#fromHour"+parentIndx+slot).prop('readonly', !$("#chkDay"+parentIndx).prop("checked"));
				$("#fromMinute"+parentIndx+slot).prop('readonly', !$("#chkDay"+parentIndx).prop("checked"));
				$("#toHour"+parentIndx+slot).prop('readonly', !$("#chkDay"+parentIndx).prop("checked"));
				$("#toMinute"+parentIndx+slot).prop('readonly', !$("#chkDay"+parentIndx).prop("checked"));

				onHourMinuteChange();
			}
		}

		this.removeTimeSlot = function(parentIndx, indx){
			showMessageBox(uiConstants.timeProfile.CONFIRM_TIME_SLOT_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if(confirmDelete){
					var daySlot = self.dayWiseSlotsArr()[parentIndx];
					daySlot.splice(indx, 1);
					self.dayWiseSlotsArr.splice(parentIndx, 1, daySlot);

					if(daySlot.length == 1){
						$("#removeTimeSlot"+parentIndx+"0").css("visibility", "hidden");
					}
					else{
						$("#removeTimeSlot"+parentIndx+"0").css("visibility", "visible");
					}
				}
			});
		}

		function checkCoverageWindowValidity(cwSHr, cwSMin, cwEHr, cwEMin, cwObj, indx){
				var startDateToUpate = new Date("2016", "0", "1", cwSHr, cwSMin);
				var endDateToUpate = new Date("2016", "0", "1", cwEHr, cwEMin);
				
				var startDateToCheckWith;
				var endDateToCheckWith;
				
				/*if(cwSDay==cwEDay){
					if(endDateToUpate<startDateToUpate){
						coverageValidFlag=false;
						return amlconstants.COVERAGE_WINDOW_RANGE_ERROR;
					}
				}*/
				
				var updateStartDatesArr = [];
				var updateEndDatesArr = [];
				
				var chechWithStartDatesArr = [];
				var checkWithEndDatesArr = [];
				
				for(var obj in cwObj){
					if(obj != indx){
						startDateToCheckWith=new Date("2016", "0", "1", cwObj[obj].startHour, cwObj[obj].startMinute);
						endDateToCheckWith=new Date("2016", "0", "1", cwObj[obj].endHour, cwObj[obj].endMinute);
						
						
						if((startDateToUpate>=startDateToCheckWith && startDateToUpate<=endDateToCheckWith) || endDateToUpate>=startDateToCheckWith && endDateToUpate<=endDateToCheckWith){
							return cwSHr+"_"+cwSMin+"_"+cwEHr+"_"+cwEMin;
						}
					}
				}
				return "";
			}

		/*this.handleTimeType = function(timeType){
			showMessageBox(uiConstants.timeProfile.CONFIRM_TIME_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmChange){
				if(confirmChange) {
					

					if(slugify(timeType) == "daily"){
						$(".chkDay").prop("checked", true);
						$(".chkDay").prop("disabled", true);


						for(var weekDay in self.weekDaysArr()){
							self.clearTimeSettings(weekDay, true);
						}
					}
					else{
						$(".chkDay").prop("checked", false);
						$(".chkDay").prop("disabled", false);

						for(var weekDay in self.weekDaysArr()){
							self.clearTimeSettings(weekDay, false);
						}
					}
				}
				else{
					if(slugify(timeType) == "days"){
						timeType = "Daily";
					}
					else{
						timeType = "Days";
					}
					$("input[name='timeType'][value='"+timeType+"']").prop('checked', true);
				}
			});

			return true;
		}*/

		this.clearTimeSettings = function(chkDayIndex, chkState){
			//if(chkState){
				
			//}

			for(var slot in self.dayWiseSlotsArr()[chkDayIndex]){
				/*$("#fromHour"+chkDayIndex+slot).val("");
				$("#fromMinute"+chkDayIndex+slot).val("");
				$("#toHour"+chkDayIndex+slot).val("");
				$("#toMinute"+chkDayIndex+slot).val("");*/

				$("#fromHour"+chkDayIndex+slot).prop('readonly', !chkState);
				$("#fromMinute"+chkDayIndex+slot).prop('readonly', !chkState);
				$("#toHour"+chkDayIndex+slot).prop('readonly', !chkState);
				$("#toMinute"+chkDayIndex+slot).prop('readonly', !chkState);
			}
		}

		this.tableScrollTop = function(element){
        	if(isErrorExists == 0){
				$("#timeProfileAddEditTable").animate({
				    scrollTop: $(element).offset().top - $("#timeProfileAddEditTable").offset().top + $("#timeProfileAddEditTable").scrollTop() - 100
				});

				isErrorExists = 1;
			}
        }

       	//Adding/Updating single time profile
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var configData;
			var timeConflictVal = "";
			isErrorExists = 0;

			$("#divTimeProfile #txtName").val($("#divTimeProfile #txtName").val().trim());
			$("#divTimeProfile #txtDescription").val($("#divTimeProfile #txtDescription").val().trim());

			if($("#divTimeProfile #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.timeProfile.TIME_PROFILE_NAME_REQUIRED);

				showError("#divTimeProfile #txtName", uiConstants.timeProfile.TIME_PROFILE_NAME_REQUIRED);
			    self.errorMsg("#divTimeProfile #txtName");
			}
			else if($("#divTimeProfile #txtName").val().length < 2){
				//self.errorMsg(uiConstants.timeProfile.TIME_PROFILE_NAME_MIN_LENGTH_ERROR);

				showError("#divTimeProfile #txtName", uiConstants.timeProfile.TIME_PROFILE_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divTimeProfile #txtName");
			}
			else if($("#divTimeProfile #txtName").val().length > 45){
				//self.errorMsg(uiConstants.timeProfile.TIME_PROFILE_NAME_MAX_LENGTH_ERROR);

				showError("#divTimeProfile #txtName", uiConstants.timeProfile.TIME_PROFILE_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divTimeProfile #txtName");
			}
			else if(!nameValidation($("#divTimeProfile #txtName").val())){
				//self.errorMsg(uiConstants.timeProfile.TIME_PROFILE_NAME_INVALID_ERROR);

				showError("#divTimeProfile #txtName", uiConstants.timeProfile.TIME_PROFILE_NAME_INVALID_ERROR);
			    self.errorMsg("#divTimeProfile #txtName");
			}

			if($("#divTimeProfile #txtDescription").val().trim() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);

				showError("#divTimeProfile #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divTimeProfile #txtDescription");
			}
			else if($("#divTimeProfile #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);

				showError("#divTimeProfile #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divTimeProfile #txtDescription");
			}
			else if($("#divTimeProfile #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);

				showError("#divTimeProfile #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divTimeProfile #txtDescription");
			}
			
			removeError("#divTimeProfile .tokenfield");
			removeError("#divTimeProfile #time-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divTimeProfile #time-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divTimeProfile .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divTimeProfile #time-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divTimeProfile .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divTimeProfile .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divTimeProfile #time-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divTimeProfile .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divTimeProfile .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divTimeProfile #time-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divTimeProfile .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divTimeProfile .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divTimeProfile #time-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divTimeProfile .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}
				}
			}

			var weekDayHourMinArr = [];
			var fromTimeInMinutes = 0;
			var toTimeInMinutes = 0;
			var timeSlotsArr = [];
			var isTimeErrorExists = 0;

			for(var weekDay in self.weekDaysArr()){
				timeSlotsArr = [];

				if($("#chkDay"+weekDay).prop("checked")){

					/*fromTimeInMinutes = (parseInt($("#fromHour"+weekDay).val())*60) + parseInt($("#fromMinute"+weekDay).val());
					toTimeInMinutes = (parseInt($("#toHour"+weekDay).val())*60) + parseInt($("#toMinute"+weekDay).val());
					if($("#fromHour"+weekDay).val() == "" || $("#fromMinute"+weekDay).val() == "" || $("#toHour"+weekDay).val() == "" || $("#toMinute"+weekDay).val() == ""){
						self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
						break;
					}
					else if(fromTimeInMinutes>toTimeInMinutes){
						self.errorMsg(uiConstants.timeProfile.INVALID_FROM_TO_TIME_RANGE + self.weekDaysArr()[weekDay].name);
						break;
					}
					else{
						var dayMapId = [];

						if(self.selectedConfigRows && self.selectedConfigRows() && self.selectedConfigRows().length){
							dayMapId = $.grep(self.selectedConfigRows()[0].coverageWindowlist, function(evt){ return evt.day == self.weekDaysArr()[weekDay].name});
						}

						//configObj[0].coverageWindowlist
						weekDayHourMinArr.push({
							"dayId": self.weekDaysArr()[weekDay].masterId,
							"id": dayMapId.length ? dayMapId[0].id : 0,
							"startHour": $("#fromHour"+weekDay).val(),
							"startMinute": $("#fromMinute"+weekDay).val(),
							"endHour": $("#toHour"+weekDay).val(),
							"endMinute": $("#toMinute"+weekDay).val()
						});
					}*/


					for(var slot in self.dayWiseSlotsArr()[weekDay]){
			    		isTimeErrorExists = 0;

						fromTimeInMinutes = (parseInt($("#fromHour"+weekDay+slot).val())*60) + parseInt($("#fromMinute"+weekDay+slot).val());
						toTimeInMinutes = (parseInt($("#toHour"+weekDay+slot).val())*60) + parseInt($("#toMinute"+weekDay+slot).val());
					

						if($("#fromHour"+weekDay+slot).val() == ""){
							//self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
							//break;

			    			self.tableScrollTop("#timeProfileAddEditTable #fromHour"+weekDay+slot);
							showError("#divTimeProfile #fromHour"+weekDay+slot, uiConstants.timeProfile.FROM_HOUR_VALUE_REQUIRED);
							isTimeErrorExists = 1;
						}

						if($("#fromMinute"+weekDay+slot).val() == ""){
							//self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
							//break;

			    			self.tableScrollTop("#timeProfileAddEditTable #fromMinute"+weekDay+slot);
							showError("#divTimeProfile #fromMinute"+weekDay+slot, uiConstants.timeProfile.FROM_MINUTE_VALUE_REQUIRED);
							isTimeErrorExists = 1;

						}

						if($("#toHour"+weekDay+slot).val() == ""){
							//self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
							//break;

			    			self.tableScrollTop("#timeProfileAddEditTable #toHour"+weekDay+slot);
							showError("#divTimeProfile #toHour"+weekDay+slot, uiConstants.timeProfile.TO_HOUR_VALUE_REQUIRED);
							isTimeErrorExists = 1;

						}

						if($("#toMinute"+weekDay+slot).val() == ""){
							//self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
							//break;

			    			self.tableScrollTop("#timeProfileAddEditTable #toMinute"+weekDay+slot);
							showError("#divTimeProfile #toMinute"+weekDay+slot, uiConstants.timeProfile.TO_MINUTE_VALUE_REQUIRED);
							isTimeErrorExists = 1;

						}

						if($("#fromHour"+weekDay+slot).val() != "" && $("#fromMinute"+weekDay+slot).val() != "" && $("#toHour"+weekDay+slot).val() != "" && $("#toMinute"+weekDay+slot).val() != ""){
							 if(fromTimeInMinutes>toTimeInMinutes){
								//self.errorMsg(uiConstants.timeProfile.INVALID_FROM_TO_TIME_RANGE + self.weekDaysArr()[weekDay].name);
								//break;

			    				self.tableScrollTop("#timeProfileAddEditTable #fromHour"+weekDay+slot);

								showError("#divTimeProfile #fromHour"+weekDay+slot, uiConstants.timeProfile.INVALID_FROM_TO_TIME_RANGE);
								showError("#divTimeProfile #fromMinute"+weekDay+slot, uiConstants.timeProfile.INVALID_FROM_TO_TIME_RANGE);
								showError("#divTimeProfile #toHour"+weekDay+slot, uiConstants.timeProfile.INVALID_TO_FROM_TIME_RANGE);
								showError("#divTimeProfile #toMinute"+weekDay+slot, uiConstants.timeProfile.INVALID_TO_FROM_TIME_RANGE);
				    			self.errorMsg("#divTimeProfile #fromHour"+weekDay+slot);
			    				isTimeErrorExists = 1;

							}
						}

						//else{
							//fromTimeInMinutes = (parseInt($("#fromHour"+weekDay+slot).val())*60) + parseInt($("#fromMinute"+weekDay+slot).val());
							//toTimeInMinutes = (parseInt($("#toHour"+weekDay+slot).val())*60) + parseInt($("#toMinute"+weekDay+slot).val());
							/*if($("#fromHour"+weekDay+slot).val() == "" || $("#fromMinute"+weekDay+slot).val() == "" || $("#toHour"+weekDay+slot).val() == "" || $("#toMinute"+weekDay+slot).val() == ""){
								self.errorMsg(uiConstants.timeProfile.FROM_TO_VALUE_REQUIRED + self.weekDaysArr()[weekDay].name);
								break;
							}
							else if(fromTimeInMinutes>toTimeInMinutes){
								self.errorMsg(uiConstants.timeProfile.INVALID_FROM_TO_TIME_RANGE + self.weekDaysArr()[weekDay].name);
								break;
							}*/
							//else{
								var dayMapId = [];

								if(self.selectedConfigRows && self.selectedConfigRows() && self.selectedConfigRows().length){
									dayMapId = $.grep(self.selectedConfigRows()[0].coverageWindowlist, function(evt){ return evt.day == self.weekDaysArr()[weekDay].name});
								}

								//configObj[0].coverageWindowlist
								timeSlotsArr.push({
									"startHour": $("#fromHour"+weekDay+slot).val(),
									"startMinute": $("#fromMinute"+weekDay+slot).val(),
									"endHour": $("#toHour"+weekDay+slot).val(),
									"endMinute": $("#toMinute"+weekDay+slot).val(),
									"isTimeErrorExists": isTimeErrorExists
								});
							//}
						//}
						

					}


					for(var slot in self.dayWiseSlotsArr()[weekDay]){

						timeConflictVal = checkCoverageWindowValidity(timeSlotsArr[slot].startHour, timeSlotsArr[slot].startMinute, timeSlotsArr[slot].endHour, timeSlotsArr[slot].endMinute, timeSlotsArr, slot);

						if(timeConflictVal != "" && timeSlotsArr[slot].isTimeErrorExists == 0){
							//var conflictedTimeArr = timeConflictVal.split("_");
							//self.errorMsg("The time (" + conflictedTimeArr[0]+":"+conflictedTimeArr[1]+" to "+conflictedTimeArr[2]+":"+conflictedTimeArr[3] + ") for the day: " + self.weekDaysArr()[weekDay].name + " conflicts with other time slot");
			    			self.tableScrollTop("#timeProfileAddEditTable #fromHour"+weekDay+slot);
							
							showError("#divTimeProfile #fromHour"+weekDay+slot, uiConstants.timeProfile.TIME_CONFLICT_ERROR);
							showError("#divTimeProfile #fromMinute"+weekDay+slot, uiConstants.timeProfile.TIME_CONFLICT_ERROR);
							showError("#divTimeProfile #toHour"+weekDay+slot, uiConstants.timeProfile.TIME_CONFLICT_ERROR);
							showError("#divTimeProfile #toMinute"+weekDay+slot, uiConstants.timeProfile.TIME_CONFLICT_ERROR);
			    			self.errorMsg("#divTimeProfile #fromHour"+weekDay+slot);
						}
					}

					/*if(self.errorMsg() != ""){
						break;
					}*/

					weekDayHourMinArr.push({
						"dayId": self.weekDaysArr()[weekDay].masterId,
						"day": self.weekDaysArr()[weekDay].name,
						"id": dayMapId.length ? dayMapId[0].id : 0,
						"timeSlots": timeSlotsArr
					});
				}
			}
			if(self.errorMsg() == "" && isErrorExists == 0 && weekDayHourMinArr.length == 0){
				showMessageBox(uiConstants.timeProfile.ATLEAST_ONE_FROM_TO_VALUE_REQUIRED, "error");
			}

			if(self.errorMsg() == "" && isErrorExists == 0 && weekDayHourMinArr.length > 0){
				var configData = {"index":1,
					"name": self.configName(),
					"description":  self.configDescription().trim(),
					//"dayOptionId": $.grep(self.dayOptionsArr(), function(evt){ return evt.name == $("input[name='timeType']:checked").val(); })[0].masterId,//$("input[name='timeType']:checked").val(),
					"coverageWindowlist": weekDayHourMinArr,
					"tags": tagsObjArr,
					"status" : self.configStatus()?1:0};

				if(self.configId() == 0)
					requestCall(uiConstants.common.SERVER_IP + "/coverageWindow", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
				else
					requestCall(uiConstants.common.SERVER_IP + "/coverageWindow/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();

			if(self.selectedConfigRows()[0].isCustom == 0 && self.selectedConfigRows()[0].status == 1){
				$('#time-tokenfield-typeahead').tokenfield('writeable');
			}
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			setConfigUneditable();
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(){
			if(self.selectedConfigRows()[0].isCustom == 0 || self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#time-tokenfield-typeahead').tokenfield('readonly');
				$(".chkDay").prop("disabled", true);
				$("#timeProfileAddEditTable #chkboxHeader").prop("disabled", true);
				$(".add-time-slot").css("visibility", "hidden");
				$(".delete-time-slot").css("visibility", "hidden");
				//$("input[name='timeType']").prop("disabled", true);
			}
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW || self.selectedConfigRows()[0].isCustom == 0){
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divTimeProfile .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].name);
				self.configId(configObj[0].id);
				self.configDescription(configObj[0].description);
			}

			var dayChkBox;
			var coverageWindowObj;
			var enableHourMinTxt = true;

			if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.currentViewIndex() == uiConstants.common.EDIT_VIEW && (self.selectedConfigRows()[0].status == 0 || self.selectedConfigRows()[0].isCustom == 0))){
				enableHourMinTxt = false;
			}

			//$("input[name='timeType'][value=" + configObj[0].dayOption + "]").prop("checked", true);

			//$("#timeProfileAddEditTable #chkboxHeader").prop("checked", configObj[0].coverageWindowlist.length == self.weekDaysArr().length);

			for(var day in configObj[0].coverageWindowlist){
				coverageWindowObj = configObj[0].coverageWindowlist[day];

				dayChkBox = $("input[name='weekDayChk'][value=" + coverageWindowObj.day + "]");
				dayChkBox.prop("checked",true);

				for(slot in coverageWindowObj.timeSlots){

					var chkDayIndex = dayChkBox.attr("id").substring(6);

					if(slot != "0"){
						self.addTimeSlot(chkDayIndex, slot);
					}
					self.clearTimeSettings(chkDayIndex, enableHourMinTxt);

					$("#fromHour"+chkDayIndex+slot).val(coverageWindowObj.timeSlots[slot].startHour.toString().length == 1 ? ("0"+coverageWindowObj.timeSlots[slot].startHour) : coverageWindowObj.timeSlots[slot].startHour);
					$("#fromMinute"+chkDayIndex+slot).val(coverageWindowObj.timeSlots[slot].startMinute.toString().length == 1 ? ("0"+coverageWindowObj.timeSlots[slot].startMinute) : coverageWindowObj.timeSlots[slot].startMinute);
					$("#toHour"+chkDayIndex+slot).val(coverageWindowObj.timeSlots[slot].endHour.toString().length == 1 ? ("0"+coverageWindowObj.timeSlots[slot].endHour) : coverageWindowObj.timeSlots[slot].endHour);
					$("#toMinute"+chkDayIndex+slot).val(coverageWindowObj.timeSlots[slot].endMinute.toString().length == 1 ? ("0"+coverageWindowObj.timeSlots[slot].endMinute) : coverageWindowObj.timeSlots[slot].endMinute);
				}
			}

			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#time-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			self.errorMsg("");
		}

		this.cancelConfig = function(){
			if(params.isModal){
				$("#idModalAlertProfile").modal("hide");
				//$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Time Profile Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onHourMinuteChange(){
			$(".hour-box").on("input change", function (evt) {
	  			if($(this).val().length > 2){
	  				$(this).val(parseInt($(this).val()));
	  			}

	  			if($(this).val().length == 1){
	  				$(this).val("0"+$(this).val());
	  			}

	  			if(parseInt($(this).val()) > 23){
	  				$(this).val("23");
	  			}
	  			else if(parseInt($(this).val()) < 0){
	  				$(this).val("00");
	  			}
	  		});

	  		$(".minute-box").on("input change", function (evt) {
	  			if($(this).val().length > 2){
	  				$(this).val(parseInt($(this).val()));
	  			}

	  			if($(this).val().length == 1){
	  				$(this).val("0"+$(this).val());
	  			}

	  			if(parseInt($(this).val()) > 59){
	  				$(this).val("59");
	  			}
	  			else if(parseInt($(this).val()) < 0){
	  				$(this).val("00");
	  			}
	  		});

			$('#timeProfileAddEditTable table').on('click', '.chkDay', function(e){
				var length = $('.chkDay:checked').length;
				$("#chkboxHeader").prop("checked", length == self.weekDaysArr().length);
			});
		}

		this.selHeaderChkBox = function(){
			$(".chkDay").prop("checked", $("#chkboxHeader").is(':checked'));
			for(var day in self.weekDaysArr()){
				self.clearTimeSettings(day, this.checked);
			}

			return true;
		}

		function onMastersLoad(){
			if(configTagLoaded == 1 && weekDaysLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}

				$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });

				onHourMinuteChange();
				

				/*$('.hourTxt').jStepper({minValue:0, maxValue:23, minLength:2});
				$('.minTxt').jStepper({minValue:0, maxValue:59, minLength:2});*/

				$('#divContentSettings table').on('click', '.chkDay', function(e){
					var chkDayIndex = $(this).attr("id").substring(6);
					self.clearTimeSettings(chkDayIndex, $(this).prop('checked'));
				});
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getTimeProfileTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#time-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#time-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#time-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#time-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			/*else if(reqType === "getDayOptions"){
				self.dayOptionsArr(data.result);

				dayOptionsLoaded = 1;
				onMastersLoad();
			}*/
			else if(reqType === "getWeekDays"){
				self.weekDaysArr(data.result);
				for(var week in self.weekDaysArr()){
					self.dayWiseSlotsArr.splice(week, 1, [{}]);
					$("#removeTimeSlot"+week+"0").css("visibility", "hidden");
				}

				weekDaysLoaded = 1;
				onMastersLoad();

				$('#timeProfileAddEditTable table').floatThead('reflow');
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_TIME_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.timeProfile.ERROR_ADD_TIME_PROFILE, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.configName());
					}
					//  
					else{
						params.curPage(1);
					}
					self.cancelConfig();

					showMessageBox(uiConstants.timeProfile.SUCCESS_ADD_TIME_PROFILE);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_TIME_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.timeProfile.ERROR_UPDATE_TIME_PROFILE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.timeProfile.SUCCESS_UPDATE_TIME_PROFILE);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getTimeProfileTag"){
				showMessageBox(uiConstants.timeProfile.ERROR_GET_TIME_PROFILE_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.timeProfile.ERROR_ADD_TIME_PROFILE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.timeProfile.ERROR_UPDATE_TIME_PROFILE, "error");
			}
		}
	}

	TimeProfileAddEdit.prototype.dispose = function() { };
	return { viewModel: TimeProfileAddEdit, template: templateMarkup };
});