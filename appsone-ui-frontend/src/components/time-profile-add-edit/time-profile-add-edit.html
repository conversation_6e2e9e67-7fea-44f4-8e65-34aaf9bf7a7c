<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default">
 	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body" id="divTimeProfile">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="time-tokenfield-typeahead" data-bind="enable: enableConfig(), value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">

				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>
			
			<div id="divContentSettings" class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Time Settings <span class="mandatoryField">*</span></div>
					<div class="panel-body" style="padding-bottom: 0px">
						<!-- <div> -->

							<!-- ko foreach: dayOptionsArr() -->
						    	<!-- <label class="config-option-label" style="margin-left: 20px;">
  								<input type="radio" name="timeType" data-bind="value: $data.name, attr: {'checked': slugify($data.name) == 'days' ? 'checked' : false}, click: $parent.handleTimeType.bind($data, $data.name)"> <span data-bind="text: $data.name"></span>
								</label> -->
							<!-- /ko-->

							<!-- <label class="config-option-label"><input type="radio" name="timeType" data-bind="value: 'day', click: handleTimeType.bind($data, 'day')" checked><span> Day</span></label>
							<label class="config-option-label" style="margin-left: 15;"><input type="radio" name="timeType" data-bind="value: 'daily', click: handleTimeType.bind($data, 'daily')"><span> Daily</span></label> -->
						<!-- </div> -->

						<!-- <hr class="form-group-seperator" style="margin-bottom: 0px"> -->

						<div class="col-sm-12 wrapper-scroll-table" id="timeProfileAddEditTable" style="height: 400px; width: auto !important;">
							<table class="table" style="width: auto;">
								<thead>
									<tr class="a1-inner-table-thead">
										<th style="width: 130px;">
											<label class="config-option-label" style="font-weight: normal;"><input type="checkbox" id ="chkboxHeader" title="Select All" data-bind="click: selHeaderChkBox"> Select All</input></label>
										</th>
										<th style="width: 425px;"">
											<div style="display: inline-block; width: 150px">
												<label style="text-align: center; border-style: none; width: 100%">From
												</label>

												<label style="text-align: center; border-style: none; width: 100%; font-weight: normal;">(Time in 24 Hours)
												</label>
											</div>

											<div style="display: inline-block; width: 150px">
												<label style="text-align: center; border-style: none; width: 100%">To
												</label>

												<label style="text-align: center; border-style: none; width: 100%; font-weight: normal;">(Time in 24 Hours)
												</label>
											</div>
										</th>
									</tr>
							</thead>

								<tbody class="rowSpaceBottom" data-bind="foreach : weekDaysArr">
									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;">


											<input name="weekDayChk" class="chkDay" type="checkbox" data-bind="value: $data.name, attr: {id: 'chkDay'+$index()}"> <span data-bind="text: $data.name"></span>

											</label>
										</td>

										<td style="text-align: center">
											<!-- ko foreach : $parents[0].dayWiseSlotsArr()[$index()] -->

												<div style="display: inline-block; width: 150px;">
													<div style="display: inline-block;">
														<input data-bind="attr: {id: 'fromHour'+$parentContext.$index()+$index()}" class="form-control hour-box hourTxt positive-integer" style="margin-bottom: 8px;" min="0" max="23" readonly  placeholder="HH">
													</div>
													:
													<div style="display: inline-block;">
														<input data-bind="attr: {id: 'fromMinute'+$parentContext.$index()+$index()}" name="number" class="form-control minute-box minTxt positive-integer" pattern="[0-9]*" readonly placeholder="MM">
													</div>
												</div>
												
												<div style="display: inline-block; width: 250px;">

													<div style="display: inline-block;">
														<input data-bind="attr: {id: 'toHour'+$parentContext.$index()+$index()}" name="number" class="form-control hour-box hourTxt positive-integer" pattern="[0-9]*" style="margin-bottom: 8px;" readonly placeholder="HH">
													</div>
													:
													<div style="display: inline-block;">
														<input data-bind="attr: {id: 'toMinute'+$parentContext.$index()+$index()}" name="number" class="form-control minute-box minTxt positive-integer" readonly placeholder="MM">
													</div>

													<div style="display: inline-block; width: 100px">
														<button type="button" class="glyphicon glyphicon-plus add-time-slot" data-bind="attr: {id: 'addTimeSlot'+$parentContext.$index()+$index()}, event:{click: $parents[1].addTimeSlot.bind($data, $parentContext.$index(), $index())}" data-toggle="modal" title="Add Time Slot" style="margin-left: 2px; top: -1px; font-size: 12px;"></button>

														<button type="button" class="glyphicon glyphicon-remove delete-time-slot" data-bind="attr: {id: 'removeTimeSlot'+$parentContext.$index()+$index()}, event:{click: $parents[1].removeTimeSlot.bind($data, $parentContext.$index(), $index())}" data-toggle="modal" title="Delete Time Slot" style="margin-left: 2px; top: -1px; font-size: 12px;"></button>
													</div>
												</div>

												<br>
											<!-- /ko -->
										</td>
									</tr>


									<!-- <tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkMon"><span> MONDAY</span></label>
										</td>

										<td>
											<div style="display: inline-block;">
												<input data-bind="value: $parent.fromHourMin()[1]" type="number" class="form-control hour-min-box">
											</div>
											:
											<div style="display: inline-block;">
												<input type="number" class="form-control hour-min-box">
											</div>
										</td>
									</tr>

									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkTue"><span> TUESDAY</span></label>
										</td>
									</tr>

									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkWed"><span> WEDNESDAY</span></label>
										</td>
									</tr>

									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkThu"><span> THURSDAY</span></label>
										</td>
									</tr>

									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkFri"><span> FRIDAY</span></label>
										</td>
									</tr>

									<tr>
										<td>
											<label class="config-option-label" style="margin-left: 15;"><input type="checkbox" id="chkSat"><span> SATURDAY</span></label>
										</td>
									</tr> -->
								</tbody>
							</table>
						</div>
					</div>


				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>