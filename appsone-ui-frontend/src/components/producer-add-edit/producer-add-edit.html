<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divProducerAddEdit">
 	<!-- <div class="panel-heading" data-bind="visible: !isModal()"><h4><span data-bind="text: pageSelected"></span></h4></div> -->

 	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Producer Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="producerTypeList" data-bind="foreach : producerTypesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.common.SELECT_PRODUCER_TYPE"></option>
						<!-- /ko-->

						<option data-bind="value: $data.producerTypeId, text: $data.producerType"></option>
					</select>
				</div>
			</div>
			
			<div id="divCompKpiAssociation" class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Components/KPI Association</div>
					<div class="panel-body" style="padding-bottom: 0px">


						<!-- <div class="form-group form-required" data-bind="visible: !isModal()">
							<label class="control-label col-sm-2">Component Type</label>
							
						</div>

						<div class="form-group form-required" data-bind="visible: !isModal()">
							<label class="control-label col-sm-2">Component</label>
							<div class="col-sm-4">
								
							</div>
						</div>

						<div class="form-group" data-bind="visible: !isModal()">
							<label class="control-label col-sm-2">Versions </label>
							<div id="versionDiv" class="col-sm-4">
								<label style="font-weight: normal;" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW"><input type="checkbox" id="selAllVersions" style="margin-left: 5px;">Select All</input></label>
								
							</div>
						</div> -->

						

						<!-- <div class="form-group" data-bind="visible: !isModal()">
							<div class="col-sm-offset-2 col-sm-4">
								<button class="btn-small" type ="button" data-bind="event:{click: addVersionKpiMapping}, visible: curOper() == 'addkpi'">Add</button>
								<button class="btn-small" type ="button" data-bind="event:{click: updateVersionKpiMapping}, visible: curOper() == 'editkpi'">Update</button>
								<button class="btn-small" type ="button" data-bind="event:{click: cancelEditVersionKpiMapping}, visible: curOper() == 'editkpi'">Cancel</button>
							</div>
						</div> -->

						<div class="form-group">
							<!-- <div class="inner-div-container" style="margin-left: 5px; margin-right: 5px;"> -->
								<table id="compVersionListHeader" class="table table-bordered table-hover table-striped" style="width: 100%;     margin-left: 5px; margin-bottom: -2px" >
									<thead>
										<tr>
											<th class="col-xs-4 a1-inner-table-thead">Component Type <span class="mandatoryField">*</span></th>
											<th class="col-xs-4 a1-inner-table-thead">Component <span class="mandatoryField">*</span></th>
											<th class="col-xs-4 a1-inner-table-thead">Version <span class="mandatoryField">*</span></th>
											<!-- <th class="col-xs-4">KPIs</th> -->
											<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW && isCustomEdit() && !isModal()-->
												<th class="a1-inner-table-thead" style="width: 50px"></th>
											<!-- /ko-->
											<th style="width: 26px; visibility: hidden;"></th>
										</tr>

										<tr data-bind="visible: $parent.currentViewIndex() != uiConstants.common.READ_VIEW && isCustomEdit() && !isModal()">
											<td style="vertical-align: middle">
												<select class="chosen form-control" id="compTypeList" data-bind="foreach : componentsArr" data-placeholder=" ">
													<!-- ko if: $index() == 0 -->
														<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_TYPE"></option>
													<!-- /ko-->

													<option data-bind="value: $data.componentTypeId, text: $data.componentType"></option>
												</select>
											</td>

											<td style="vertical-align: middle">
												<select class="chosen form-control" id="compNamesList" data-bind="event:{change: function(){checkInactive($data.componentNamesArr(), '#compNamesList', 'componentId')}}, foreach : componentNamesArr" data-placeholder=" ">
													<!-- ko if: $index() == 0 -->
														<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_NAME"></option>
													<!-- /ko-->
													
													<option data-bind="value: $data.componentId, text: $data.componentName, css: {'inactiveOptionClass': ($data.isActive == undefined ? false : true)}"></option>
												</select>
											</td>

											<td style="vertical-align: middle">
												<div class="inner-div-container" style="height: 50px; margin-bottom: 0px;" id="versionsList"></div>
											</td>

											
												<td style="text-align:center; vertical-align: middle">
													<button type="button" class="glyphicon glyphicon-plus buttonadd" title="Add"></button>
												</td>

											<td style="visibility: hidden;">

											</td>
										</tr>

									</thead>
								</table>

								<!-- ko if: versionKpiMappingData().length > 0 -->
								<div class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
								<table id="compVersionList" class="table table-bordered table-hover table-striped" style="width:100%" >

									<tbody data-bind="foreach: versionKpiMappingData">
										<tr>
											<td class="col-xs-4" data-bind="text: $data.componentType, attr:{'title': $data.componentType}"></td>

											<td class="col-xs-4" data-bind="text: $data.componentName, attr:{'title': $data.componentName}"></td>

											<td class="col-xs-4" data-bind="text: $data.version, attr:{'title': $data.version}"></td>

											<!-- <td data-bind="text: getCommaSeparatedVal($data.kpis, ['label']), attr:{title: getCommaSeparatedVal($data.kpis, ['label'])}"></td> -->
											
											<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW && $parent.isCustomEdit() && !$parent.isModal() -->
												<td style="width: 50px; text-align:center">
													<!-- <button type="button" class="glyphicon glyphicon-edit buttonedit" title="Edit"></button> -->
													<button type="button" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
												</td>
											<!-- /ko-->
										</tr>
									</tbody>

								</table>
								</div>
								<!-- /ko-->
								
							<!-- 	 -->

							<div style="padding-right: 10px">
							<table class="table" style="width:100%; ">
								<tbody>
								
								</tbody>
							</table>
							</div>
						</div>

						<div class="form-group form-required">
							<label class="control-label col-sm-2">KPI Type </label>
							<div class="	col-sm-4">
								<select id="kpiTypeList" class="chosen form-control" data-bind="options: kpiTypesArr,  optionsText: 'kpiType', 'optionsValue': 'kpiTypeId', optionsCaption: uiConstants.common.SELECT_KPI_TYPE"></select>
							</div>
						</div>

						<div class="form-group form-required">
							<div id="kpiGroupTypes" class="col-sm-offset-2 col-sm-4">
								<label class="radio-inline">
      								<input type="radio" name="kpiGroupType" data-bind="event:{click: kpiGroupChange}" value="ungrouped" checked>KPIs
   								</label>
   								<label class="radio-inline">
      								<input type="radio" name="kpiGroupType" data-bind="event:{click: kpiGroupChange}" value="grouped" >KPI Groups
   								</label>
							</div>
						</div>

						<!-- <div class="form-group">
							<div id="kpiDiv" class="col-sm-offset-2 col-sm-4">
								<label style="font-weight: normal;" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW"><input type="checkbox" id="selAllKpis" style="margin-left: 5px;">Select All</input></label>
								<button type="button" class="glyphicon glyphicon-refresh" data-bind="event:{click: loadKpis}, attr:{'title': 'Load KPIs'}" style="float: right; margin-right: 5px;"></button>
								<div class="inner-div-container" style="height: 150px" id="kpisList"></div>
							</div>
						</div> -->

						<label class="control-label col-sm-2"><span data-bind="text: commonKpiOrGroup()"></span> <span class="mandatoryField">*</span></label>

						<div id="kpiDiv" class="col-sm-6" style="margin-bottom: 10px;">
							<table class="checklist-div-table">
								<tr>
									<td style="width: 270px">
										<label style="margin-left: 5px;">Available:</label>
										<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW && !isModal()">
											<label style="font-weight: normal;"><input type="checkbox" id="selAllAvailKpis" style="margin-left: 5px;" data-bind="attr: {disabled: availableKpisArr().length == 0}">Select All</input></label>
										</div>
									</td>

									<td style="width: 40px"></td>

									<td style="width: 270px">
										<label style="margin-left: 5px;">Selected:</label>
										<div data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW && !isModal()">
											<label style="font-weight: normal;"><input type="checkbox" id="selAllSelKpis" style="margin-left: 5px;" data-bind="attr: {disabled: selectedKpisArr().length == 0}">Select All</input></label>
										</div>
									</td>
								</tr>

								<tr>
									<td>
										<div class="inner-div-container" id="availableKpisList"></div>
									</td>

									<td style="padding: 5px;">
										<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !enableAddKpisBtn(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !enableAddKpisBtn()}, event:{click: addToSelectedKpis}"></button>
										<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !enableRemoveKpisBtn(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !enableRemoveKpisBtn()}, event:{click: addToAvailableKpis}"></button>
									</td>

									<td>
										<div class="inner-div-container" id="selectedKpisList"></div>
									</td>
								</tr>
							</table>
						</div>
					</div>
				</div>
			</div>

			<!-- ko if: slugify(producerType()) == 'script' -->
				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">Script Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Script File Name <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtScriptName" data-bind="value: scriptName" placeholder="Enter Script File Name" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Signature <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<textarea class="form-control" id="txtScriptSignature" rows="5" data-bind="value: scriptSignature" placeholder="Enter Signature" style="resize: none"></textarea>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">

									<table id="scriptParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-3">
													<span data-bind="text: 'Parameter' + ($index()+1)"></span>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="text: $data.attributeName"></option>
													</select>

												</td>
												<td class="col-xs-3">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), options: $parent.parameterTypesArr,  optionsText: 'parameterType', optionsCaption: uiConstants.producerConfig.SELECT_PARAM_TYPE, attr: {'id': 'parameterType'+$index()}"></select>
												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteScriptParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'jdbc' -->
				<div class="form-group" id="divJdbcParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">JDBC Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Query <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<textarea class="form-control" id="txtQuery" rows="5" data-bind="value: query" placeholder="Enter Query" style="resize: none"></textarea>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">JDBC Driver <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtJdbcDriver" data-bind="value: jdbcDriver" placeholder="Enter Driver Name" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">JDBC URL <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtJdbcUrl" data-bind="value: jdbcUrl" placeholder="Enter JDBC URL" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Query Results <span class="mandatoryField">*</span></label>
								<div class="col-sm-3">
									<select class="chosen form-control" id="selectQueryResult" data-bind="options: queryResultsArr,  optionsText: 'queryResult', optionsCaption: uiConstants.producerConfig.SELECT_QUERY_RESULT"></select>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="width:80%; margin-left: 5px; margin-right: 5px;">
									<table id="jdbcParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-3">
													<span data-bind="text: 'Parameter' + ($index()+1)"></span>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
													</select>

												</td>
																							
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1" style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteJdbcParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1" style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'wmi' -->

				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">Script Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Script File Name <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtShellScriptName" data-bind="value: shellScriptName" placeholder="Enter Script File Name" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Signature <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<textarea class="form-control" id="txtShellScriptSignature" rows="5" data-bind="value: shellScriptSignature" placeholder="Enter Signature" style="resize: none"></textarea>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
									<table id="scriptParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-3">
													<span data-bind="text: 'Parameter' + ($index()+1)"></span>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
													</select>

												</td>
												<td class="col-xs-3">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), options: $parent.parameterTypesArr,  optionsText: 'parameterType', optionsCaption: uiConstants.producerConfig.SELECT_PARAM_TYPE, attr: {'id': 'parameterType'+$index()}"></select>
												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteShellParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'jmx' -->

				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">JMX Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Target Object Name <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtJmxTargetObjName" data-bind="value: jmxTargetObjName" placeholder="Enter Object Name" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">JMX URL <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtJmxUrl" data-bind="value: jmxUrl" placeholder="Enter JMX URL" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Attribute Type <span class="mandatoryField">*</span></label>
								<div class="col-sm-3">
									<select class="chosen form-control" id="selectAttrType" data-bind="foreach : attributeTypesArr" data-placeholder=" ">
										<!-- ko if: $index() == 0 -->
											<option data-bind="value: 0, text: uiConstants.producerConfig.SELECT_ATTRIBUTE_TYPE"></option>
										<!-- /ko-->

										<option data-bind="value: $data.attributeTypeId, text: $data.attributeType"></option>
									</select>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
									<table id="scriptParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-4">
													<input type="text" class="form-control" data-bind="enable: $parent.enableConfig(), attr: {id: 'paramName'+$index()}" placeholder="Enter Parameter Name"></input>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
													</select>

												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteScriptParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'jppf' -->

				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">JPPF Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Server Type <span class="mandatoryField">*</span></label>
								<div class="col-sm-3">
									<select class="chosen form-control" id="selJppfServerType" data-bind="foreach : serverTypesArr" data-placeholder=" ">
										<!-- ko if: $index() == 0 -->
											<option data-bind="value: 0, text: uiConstants.producerConfig.SELECT_SERVER_TYPE"></option>
										<!-- /ko-->

										<option data-bind="value: $data.serverTypeId, text: $data.serverType"></option>
									</select>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'http' -->

				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">HTTP Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Status URL <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtHttpdStatusUrl" data-bind="value: httpdStatusUrl" placeholder="Enter URL" max="45" required>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
									<table id="scriptParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-4">
													<span data-bind="text: 'Parameter' + ($index()+1)"></span>
												</td>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
													</select>

												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteScriptParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'http_json' -->

				<div class="form-group">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">HTTP-JSON Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Status URL <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtHttpJsonStatusUrl" data-bind="value: httpJsonStatusUrl" placeholder="Enter URL" max="45" required>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
									<table class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-4">
													<input type="text" class="form-control" data-bind="enable: $parent.enableConfig(), attr: {id: 'paramName'+$index()}" placeholder="Enter Parameter Name"></input>
												</td>

												<td class="col-xs-4">
													<input type="text" class="form-control" data-bind="enable: $parent.enableConfig(), attr: {id: 'paramValue'+$index()}" placeholder="Enter Parameter Value"></input>
												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteScriptParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<!-- ko if: slugify(producerType()) == 'was' -->

				<div class="form-group" id="divScriptParams">
					<div class="panel panel-default inner-panel">
						<div class="configPanel panel-heading">WAS Parameters</div>
						<div class="panel-body">
							<div class="form-group form-required">
								<label class="control-label col-sm-2">Target Object Name <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtWasTargetObjName" data-bind="value: wasTargetObjName" placeholder="Enter Object Name" max="45" required>
								</div>
							</div>

							<div class="form-group form-required">
								<label class="control-label col-sm-2">Module Name <span class="mandatoryField">*</span></label>
								<div class="col-sm-6">
									<input type="text" class="form-control" id="txtWasModuleName" data-bind="value: wasModuleName" placeholder="Enter Module Name" max="45" required>
								</div>
							</div>

							<div class="form-group">
								<button class="glyphicon glyphicon-plus" type="button" data-bind="visible : enableConfig(), event:{click: addAttribute}" title="Add Parameter" style="margin-left: 8px;""></button>

								<div id="parametersPanel" class="inner-div-container" style="margin-left: 5px; margin-right: 5px;">
									<table id="scriptParamsList" class="table table-bordered table-hover table-striped" style="width:100%;">
										<tbody data-bind="foreach : attributesArr">
											<tr>
												<td class="col-xs-4">
													<input type="text" class="form-control" data-bind="enable: $parent.enableConfig(), attr: {id: 'paramName'+$index()}" placeholder="Enter Parameter Name"></input>
												</td>
												<td class="col-xs-4">
													<select class="chosen form-control" data-bind="enable: $parent.enableConfig(), foreach : $parent.attributesMasterArr, attr: {id: 'attribList'+$index()}" data-placeholder=" ">
														<!-- ko if: $index() == 0 -->
															<option data-bind="value: 0, text: uiConstants.common.ENTER_SELECT_ATTRIB_LIST"></option>
														<!-- /ko-->
														<option data-bind="value: $data.attributeId, text: $data.attributeName"></option>
													</select>

												</td>
												
												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button type="button" data-bind=" event: {click: $parent.deleteScriptParam.bind($data, $index())}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></button>
													</td>
												<!-- /ko-->

												<!-- ko if: $parent.enableConfig() -->
													<td class="col-xs-1"style="text-align:center">
														<button data-bind="enable: $index()!=0, css: {confButtonDisabled: $index()==0}, event: {click: $parent.moveParamUp.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-up" title="Move Up"></button>

														<button data-bind="enable: ($index() < $parent.attributesArr().length-1), css: {confButtonDisabled: ($index()==$parent.attributesArr().length-1)}, event: {click: $parent.moveParamDown.bind($data, $index())}" type="button" class="glyphicon glyphicon-arrow-down" title="Move Down"></button>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			<!-- /ko-->

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="producer-tokenfield-typeahead" data-bind="enable: enableConfig(), value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4 && !isModal()">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>