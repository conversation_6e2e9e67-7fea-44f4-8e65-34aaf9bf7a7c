define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./producer-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox) {

	function ProducerAddEdit(params) {
		var self = this;

		this.componentsArr = ko.observableArray();
		this.componentNamesArr = ko.observableArray([{}]);
		this.versionsArr = ko.observableArray();
		this.availableKpisArr = ko.observableArray();
		this.selectedKpisArr = ko.observableArray();
		this.kpiTypesArr = ko.observableArray();
		this.parameterTypesArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = ko.observableArray();
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
		this.curOper = ko.observable("addkpi");
		this.versionKpiMappingData = ko.observableArray();
		this.scriptName = ko.observable();
		this.scriptSignature = ko.observable();
		this.shellScriptName = ko.observable();
		this.shellScriptSignature = ko.observable();
		this.jmxTargetObjName = ko.observable();
		this.jmxUrl = ko.observable();
		this.wasTargetObjName = ko.observable();
		this.wasModuleName = ko.observable();
		this.query = ko.observable();
		this.jdbcDriver = ko.observable();
		this.jdbcUrl = ko.observable();
  		this.queryResultsArr = ko.observableArray();
  		this.attributeTypesArr = ko.observableArray();
  		this.serverTypesArr = ko.observableArray();
		this.httpdStatusUrl = ko.observable();
		this.httpJsonStatusUrl = ko.observable();
		//this.httpContentTypeArr = ko.observableArray(["XML","Plain Text"]);
		this.attributesArr = ko.observableArray();
		this.producerType = ko.observable();
		this.attributesMasterArr = ko.observableArray();
		this.isModal = ko.observable(false);
		this.selectedComponentTypeObj = ko.observable();
		this.selectedComponentObj = ko.observable();
		this.selectedVersionObj = ko.observable();
		this.selectedKpiObj = ko.observable();
		this.isKpiGroup = ko.observable();
		this.producerTypesArr = params.isModal ? ko.observableArray() : params.producerTypesArr;
		this.isCustomEdit = ko.observable(true);
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableAddKpisBtn = ko.observable(false);
  		this.enableRemoveKpisBtn = ko.observable(false);
  		this.allCommonKpisList = ko.observableArray();
  		this.commonKpiOrGroup = ko.observable("Common KPIs");
  		this.enableConfig = ko.observable(true);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		
		var configTagLoaded = 0;
		var kpiTypeLoaded = 0;
		var parameterTypeLoaded = 0;
		var queryResultLoaded = 0;
		var attributeTypesLoaded = 0;
		var serverTypesLoaded = 0;
		var attributeTypeListLoaded = 0;
		var producerTypeListLoaded = 0;
		var compTypeVersionLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
		var rowToEdit;
		var previousProducerType;
		var previousProducerTypeId;
		var previousKpiType;
		var versionKpiMappingDataCopy = [];
		var discardedKpiIdsArr = [];
		var checkRequired = true;

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			if(params.isModal){
				self.isModal(true);
				self.selectedComponentTypeObj = params.selectedComponentTypeObj;
				self.selectedComponentObj = params.selectedComponentObj;
				self.selectedVersionObj = params.selectedVersionObj;
				self.selectedKpiObj = params.selectedKpiObj;
				self.isKpiGroup = params.isKpiGroup;

				requestCall(uiConstants.common.SERVER_IP + "/producerType", "GET", "", "getProducerTypes", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5757b106130000480f896adf?callback=?", "GET", "", "getProducerTypes", successCallback, errorCallback);
			}
			else{
				self.selectedConfigRows = params.selectedConfigRows;

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
					self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["producerName"]));
				}
			}

			requestCall(uiConstants.common.SERVER_IP + "/component?status=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/575b0eb6110000b421539fbb?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);

			$('#producer-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {

					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#producer-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#producer-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			$("#producerTypeList").on('chosen:showing_dropdown', function () {
			        previousProducerType = $(this).children("option").filter(":selected").text();
			        previousProducerTypeId = this.value;
			    }).on('change', function () {
			    	if(self.attributesArr().length == 0){
			    		previousProducerType = $(this).children("option").filter(":selected").text();
			    		previousProducerTypeId = this.value;
			    		requestCall(uiConstants.common.SERVER_IP + "/parameterType/" + $("#producerTypeList option:selected").text(), "GET", "", "getParameterType", successCallback, errorCallback);

			    		if(slugify(previousProducerType) == "jdbc"){
			    			requestCall(uiConstants.common.SERVER_IP + "/jdbcQueryResults?status=1", "GET", "", "getQueryResult", successCallback, errorCallback);
			    			//requestCall("http://www.mocky.io/v2/57761c8910000054001c3885?callback=?", "GET", "", "getQueryResult", successCallback, errorCallback);
						}

						if(slugify(previousProducerType) == "jmx"){
							requestCall(uiConstants.common.SERVER_IP + "/jmxAttributeTypes?status=1", "GET", "", "getAttributeTypes", successCallback, errorCallback);
						}

						if(slugify(previousProducerType) == "jppf"){
							requestCall(uiConstants.common.SERVER_IP + "/jppfServerTypes?status=1", "GET", "", "getJppfServerTypes", successCallback, errorCallback);
						}
			    	}
			    	else{
			    		showMessageBox(uiConstants.producerConfig.CONFIRM_CLEAR_PARAMS, "question", "confirm", function confirmCallback(confirmClearAttrib){
							if (confirmClearAttrib){
				    			self.attributesArr([]);
				    			
				    			requestCall(uiConstants.common.SERVER_IP + "/parameterType/" + $("#producerTypeList option:selected").text(), "GET", "", "getParameterType", successCallback, errorCallback);
								//requestCall("http://www.mocky.io/v2/5746c1651000006f13aa6b84?callback=?", "GET", "", "getParameterType", successCallback, errorCallback);

								if(slugify(previousProducerType) == "jdbc"){
					    			requestCall(uiConstants.common.SERVER_IP + "/jdbcQueryResults?status=1", "GET", "", "getQueryResult", successCallback, errorCallback);
				    				//requestCall("http://www.mocky.io/v2/57761c8910000054001c3885?callback=?", "GET", "", "getQueryResult", successCallback, errorCallback);
								}

								if(slugify(previousProducerType) == "jmx"){
									requestCall(uiConstants.common.SERVER_IP + "/jmxAttributeTypes?status=1", "GET", "", "getAttributeTypes", successCallback, errorCallback);
								}

								if(slugify(previousProducerType) == "jppf"){
									requestCall(uiConstants.common.SERVER_IP + "/jppfServerTypes?status=1", "GET", "", "getJppfServerTypes", successCallback, errorCallback);
								}
				    		}
				    		else{
				    			$("#producerTypeList").val(previousProducerTypeId).trigger('chosen:updated');
				    		}
				    	});
			    	}
					self.producerType($("#producerTypeList option:selected").text());

					if(slugify(self.producerType()) == "jdbc"){
						$("#selectQueryResult").trigger('chosen:updated');

						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}

					if(slugify(self.producerType()) == "jmx"){
						$("#selectAttrType").trigger('chosen:updated');

						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}

					if(slugify(self.producerType()) == "jppf"){
						$("#selJppfServerType").trigger('chosen:updated');

						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}
				});

			$("#compTypeList").on('change', function () {
		    	setCompNames();
		    	setCompVersions();
			});

			$("#compNamesList").on('change', function () {
				$("#selAllVersions").prop("checked", false);
				$("#selAllAvailKpis").prop("checked", false);
				$("#selAllSelKpis").prop("checked", false);
		    	setCompVersions();
			});

			$("#compVersionList tbody").on('click', '.buttonedit', function(e){
				self.curOper("editkpi");
			 	rowToEdit = $(this).closest('tr').get(0).rowIndex-1;
			 	if(uiConstants.common.DEBUG_MODE)console.log(rowToEdit);
			 	
			 	setVersionKpiMapping();
				//self.loadKpis();
			});

			$("#compVersionListHeader thead").on('click', '.buttonadd', function(e){
				self.addVersionKpiMapping();
				deleteVersionKpiMappingListener();
			});

			$("#kpiTypeList").on('chosen:showing_dropdown', function () {
			        previousKpiType = this.value;
			    }).on('change', function () {
			    //$("#kpiTypeList").blur();
			    if(self.allCommonKpisList().length != 0){
			    	if(self.selectedKpisArr().length == 0){
						$("#selAllAvailKpis").prop("checked", false);
						$("#selAllSelKpis").prop("checked", false);
				    	self.clearKpisList();
				    	previousKpiType = this.value;
				    	
				    	reloadKpis();
					}
					else{
						showMessageBox(uiConstants.producerConfig.PRODUCER_MAP_TO_MIXED_KPI_TYPE_ERROR, "error");
						this.value = previousKpiType;
					}
				}
			});



			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW || self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				if(slugify(self.selectedConfigRows()[0].producerType) == "script" || slugify(self.selectedConfigRows()[0].producerType) == "wmi" || slugify(self.selectedConfigRows()[0].producerType) == "jmx" || slugify(self.selectedConfigRows()[0].producerType) == "was"){
					var producerType = self.selectedConfigRows()[0].producerType; //Send this to REST API to get parameter types
					requestCall(uiConstants.common.SERVER_IP + "/parameterType/" + producerType, "GET", "", "getParameterType", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5746c1651000006f13aa6b84?callback=?", "GET", "", "getParameterType", successCallback, errorCallback);
				}
				else{
					parameterTypeLoaded = 1;
				}

				if(slugify(self.selectedConfigRows()[0].producerType) == "jdbc"){
					requestCall(uiConstants.common.SERVER_IP + "/jdbcQueryResults?status=1", "GET", "", "getQueryResult", successCallback, errorCallback);
				}
				else{
					queryResultLoaded = 1;
				}

				if(slugify(self.selectedConfigRows()[0].producerType) == "jmx"){
					requestCall(uiConstants.common.SERVER_IP + "/jmxAttributeTypes?status=1", "GET", "", "getAttributeTypes", successCallback, errorCallback);
				}
				else{
					attributeTypesLoaded = 1;
				}

				if(slugify(self.selectedConfigRows()[0].producerType) == "jppf"){
					requestCall(uiConstants.common.SERVER_IP + "/jppfServerTypes?status=1", "GET", "", "getJppfServerTypes", successCallback, errorCallback);
				}
				else{
					serverTypesLoaded = 1;
				}
			}
			else{
				parameterTypeLoaded = 1;
				queryResultLoaded = 1;
				attributeTypesLoaded = 1;
			}

	        
	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=Producer", "GET", "", "getProducerTag", successCallback, errorCallback);
	        
	        requestCall(uiConstants.common.SERVER_IP + "/kpiDataTypes", "GET", "", "getKpiType", successCallback, errorCallback);
	        //requestCall("http://www.mocky.io/v2/5791fb2f110000671c5af63f?callback=?", "GET", "", "getKpiType", successCallback, errorCallback);
	    	requestCall(uiConstants.common.SERVER_IP + "/attribute" + ((self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || params.isModal)? '' : '?producerId=' + self.selectedConfigRows()[0].producerId), "GET", "", "getAttributeList", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/5746c1fe1000008813aa6b85?callback=?", "GET", "", "getAttributeList", successCallback, errorCallback);

			$("div").on("click", "#selAllVersions", function(e){
				$("#versionsList .checkList").prop("checked", $("#selAllVersions").prop("checked"));
				//self.clearKpisList();
			});

			$("div").on("click", "#versionsList .checkList", function(e){
				$("#selAllVersions").prop("checked", self.versionsArr().length == $("#versionsList .checkList:checked").length);
				//self.clearKpisList();
	        });

	        $("div").on("click", "#selAllAvailKpis", function(e){
				$("#availableKpisList .checkList").prop("checked", $("#selAllAvailKpis").prop("checked"));
				self.enableAddKpisBtn($("#availableKpisList .checkList:checked").length);
			});

			$("div").on("change", "#availableKpisList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllAvailKpis").prop("checked", self.availableKpisArr().length == $("#availableKpisList .checkList:checked").length);
					self.enableAddKpisBtn($("#availableKpisList .checkList:checked").length);
				}
	        });

	        $("div").on("click", "#selAllSelKpis", function(e){
				$("#selectedKpisList .checkList").prop("checked", $("#selAllSelKpis").prop("checked"));
				self.enableRemoveKpisBtn($("#selectedKpisList .checkList:checked").length);
			});

			$("div").on("click", "#selectedKpisList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllSelKpis").prop("checked", self.selectedKpisArr().length == $("#selectedKpisList .checkList:checked").length);
					self.enableRemoveKpisBtn($("#selectedKpisList .checkList:checked").length);
				}
			});

			/*$("div").find("input,button,textarea").on("mouseover", function(){
				$(this).attr("title", $(this).val());
			});

			$("form").find("select").on("mouseover", function(){
				$(this).attr("title", $(this).find('option:selected').text());
			});

			$("form").find("label").on("mouseover", function(){
				$(this).attr("title", $(this).text());
			});

			$(".chosen-container").find("span").on("mouseover", function(){
				$(this).attr("title", $(this).text());
			});*/
		}

		

		this.addToSelectedKpis = function(){
			var availArr = $('#availableKpisList').getSelectedValues();

			var kpiObj;
			var avialKpisArr = [];
			var selKpisArr = [];
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableKpisArr());

			for(arr in availArr){
				kpiObj = $.grep(self.availableKpisArr(), function(e){ return e.kpiId == availArr[arr]; });
				self.availableKpisArr.splice(self.availableKpisArr.indexOf(kpiObj[0]), 1);
				self.selectedKpisArr.push(kpiObj[0]);
			}

			for(kpi in self.availableKpisArr()){
				avialKpisArr.push({
					"id": self.availableKpisArr()[kpi].kpiId,
					"name": self.availableKpisArr()[kpi].kpiName
				});
			}

			$('#availableKpisList').checklistbox({
			    data: avialKpisArr
			});

			for(kpi in self.selectedKpisArr()){
				selKpisArr.push({
					"id": self.selectedKpisArr()[kpi].kpiId,
					"name": self.selectedKpisArr()[kpi].kpiName
				});
			}

			$('#selectedKpisList').checklistbox({
			    data: selKpisArr
			});

			uncheckKpis();
			self.enableAddKpisBtn(false);
		}

		this.addToAvailableKpis = function(){
			var selArr = $('#selectedKpisList').getSelectedValues();
			var kpiObj;
			var avialKpisArr = [];
			var selKpisArr = [];

			for(arr in selArr){
				kpiObj = $.grep(self.selectedKpisArr(), function(e){ return e.kpiId == selArr[arr]; });
				self.selectedKpisArr.splice(self.selectedKpisArr.indexOf(kpiObj[0]), 1);
				self.availableKpisArr.push(kpiObj[0]);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					if(self.selectedConfigRows()[0].kpis[0].kpiId){
						if($.grep(self.selectedConfigRows()[0].kpis, function(e){ return e.kpiId == kpiObj[0].kpiId; }).length != 0){
							discardedKpiIdsArr.push(kpiObj[0].kpiId);
						}
					}
					else{
						if($.grep(self.selectedConfigRows()[0].kpis, function(e){ return e.kpiGroupId == kpiObj[0].kpiId; }).length != 0){
							discardedKpiIdsArr.push(kpiObj[0].kpiId);
						}
					}
				}
			}

			for(kpi in self.availableKpisArr()){
				avialKpisArr.push({
					"id": self.availableKpisArr()[kpi].kpiId,
					"name": self.availableKpisArr()[kpi].kpiName
				});
			}

			$('#availableKpisList').checklistbox({
			    data: avialKpisArr
			});

			for(kpi in self.selectedKpisArr()){
				selKpisArr.push({
					"id": self.selectedKpisArr()[kpi].kpiId,
					"name": self.selectedKpisArr()[kpi].kpiName
				});
			}

			$('#selectedKpisList').checklistbox({
			    data: selKpisArr
			});

			uncheckKpis();
			self.enableRemoveKpisBtn(false);
		}


		function uncheckKpis(){
			$("#selectedKpisList .checkList").prop("checked",false);
			$("#availableKpisList .checkList").prop("checked",false);
			$("#selAllAvailKpis").prop("checked",false);
			$("#selAllSelKpis").prop("checked",false);
		}
		
		function setVersionKpiMapping(){
			var versionKpiMappingObj = self.versionKpiMappingData()[rowToEdit];
		 	$("#compTypeList").val(versionKpiMappingObj.componentTypeId).trigger('chosen:updated');
		 	setCompNames();
		
		 	$("#compNamesList").val(versionKpiMappingObj.componentId).trigger('chosen:updated');
	    	setCompVersions();
		 	
			$("#versionsList .checkList[value=" + versionKpiMappingObj.versionId + "]").prop("checked",true);
			$("#selAllVersions").prop("checked", self.versionsArr().length == $("#versionsList .checkList:checked").length);
		}

		self.deleteScriptParam = function(rowToDelete){
		    showMessageBox(uiConstants.producerConfig.CONFIRM_PARAMETER_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if (confirmDelete) {
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	changeParamPosition(0, 0, rowToDelete);
				}
			});
		};

		self.deleteJdbcParam = function(rowToDelete){
		    showMessageBox(uiConstants.producerConfig.CONFIRM_PARAMETER_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if (confirmDelete) {
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	changeParamPosition(0, 0, rowToDelete);
				}
			});
		};

		self.deleteShellParam = function(rowToDelete){
		    showMessageBox(uiConstants.producerConfig.CONFIRM_PARAMETER_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
				if (confirmDelete) {
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	changeParamPosition(0, 0, rowToDelete);
				}
			});
		};

		this.kpiGroupChange = function(){
			if($("input[name='kpiGroupType']:checked").val() == "grouped"){
				self.commonKpiOrGroup("Common Group KPIs");
			}
			else{
				self.commonKpiOrGroup("Common KPIs");
			}

			if(self.allCommonKpisList().length != 0){
				if(self.selectedKpisArr().length == 0){
			    	self.clearKpisList();
			    	reloadKpis();
				}
				else{
					showMessageBox(uiConstants.producerConfig.PRODUCER_MAP_TO_GROUPED_UNGROUPED_ERROR, "error");

					if($("input[name='kpiGroupType']:checked").val() == "grouped"){
						$("input[name='kpiGroupType'][value='ungrouped']").prop('checked', true);
					}
					else{
						$("input[name='kpiGroupType'][value='grouped']").prop('checked', true);
					}
				}
			}
			
			return true;
		}

		function changeParamPosition(from, to, deleteIndex){
			var paramNameArr = [];
			var attribValArr = [];
			var attribValIdArr = [];
			var paramTypeArr = [];

			for(var attrib in self.attributesArr()){
				if(slugify(self.producerType()) != "http_json"){
					attribValArr.push($("#attribList" + attrib + "_chosen span")[0].innerHTML);
					attribValIdArr.push($("#attribList" + attrib).val());
				}
				else{
					attribValArr.push($("#paramValue" + attrib).val());
				}

				if(slugify(self.producerType()) == "script" || slugify(self.producerType()) == "wmi"){
					paramTypeArr.push($("#parameterType" + attrib + " option:selected").text());
				}
				else if(slugify(self.producerType()) == "jmx" || slugify(self.producerType()) == "was" || slugify(self.producerType()) == "http_json"){
					paramNameArr.push($("#paramName" + attrib).val());
				}
			}

			if(uiConstants.common.DEBUG_MODE)console.log(paramNameArr);
			if(uiConstants.common.DEBUG_MODE)console.log(attribValArr);
			if(uiConstants.common.DEBUG_MODE)console.log(attribValIdArr);

			self.attributesArr([]);

			if(deleteIndex != -1){
				attribValArr.splice(deleteIndex, 1);
				if(slugify(self.producerType()) != "http_json"){
					attribValIdArr.splice(deleteIndex, 1);
				}
				if(slugify(self.producerType()) == "script" || slugify(self.producerType()) == "wmi"){
					paramTypeArr.splice(deleteIndex, 1);
				}
				else if(slugify(self.producerType()) == "jmx" || slugify(self.producerType()) == "was" || slugify(self.producerType()) == "http_json"){
					paramNameArr.splice(deleteIndex, 1);
				}
			}
			else{
				attribValArr.splice(to, 0, attribValArr.splice(from, 1)[0]);
				if(slugify(self.producerType()) != "http_json"){
					attribValIdArr.splice(to, 0, attribValIdArr.splice(from, 1)[0]);
				}
				if(slugify(self.producerType()) == "script" || slugify(self.producerType()) == "wmi"){
					paramTypeArr.splice(to, 0, paramTypeArr.splice(from, 1)[0]);
				}
				else if(slugify(self.producerType()) == "jmx" || slugify(self.producerType()) == "was" || slugify(self.producerType()) == "http_json"){
					paramNameArr.splice(to, 0, paramNameArr.splice(from, 1)[0]);
				}
			}
			

			for(var attrVal in attribValArr){
				self.attributesArr.push({"attributeId": attrib, "attributeName": "", "parameterType": ""});

				if(slugify(self.producerType()) != "http_json"){
					jQuery(".chosen").chosen({
						search_contains: true	
					});
					allowCustomAttribs(attrVal);

					if(attribValIdArr[attrVal] == 0){
						$("#attribList"+attrVal + "_chosen span")[0].innerHTML = attribValArr[attrVal];
					}
					else{
						$("#attribList"+attrVal).val(attribValIdArr[attrVal]).trigger('chosen:updated');
					}
				}
				else{
					$("#paramValue" + attrVal).val(attribValArr[attrVal]);
				}

				if(slugify(self.producerType()) == "script" || slugify(self.producerType()) == "wmi"){
					$("#parameterType"+ attrVal +" option").filter(function () { return $(this).html() == paramTypeArr[attrVal]; }).prop('selected', true).trigger('chosen:updated');
				}
				else if(slugify(self.producerType()) == "jmx" || slugify(self.producerType()) == "was" || slugify(self.producerType()) == "http_json"){
					$("#paramName" + attrVal).val(paramNameArr[attrVal]);
				}
			}
			jQuery(".chosen").chosen({
				search_contains: true	
			});
		}

		this.moveParamUp = function(curParamIndex){
			changeParamPosition(curParamIndex, curParamIndex-1, -1);
		}

		this.moveParamDown = function(curParamIndex){
			changeParamPosition(curParamIndex, curParamIndex+1, -1);
		}

		this.addAttribute = function(){
			self.attributesArr.push({"attributeId":self.attributesArr().length, "attributeName":"", "parameterType":""});
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			allowCustomAttribs(self.attributesArr().length-1);
		}

		function allowCustomAttribs(indx){
			if(slugify(self.producerType()) != "http_json"){
				$("#attribList"+ indx).chosen({}).on('chosen:showing_dropdown', function(){
					onChosenDropdownOpen("attribList"+ indx, "parametersPanel");
					if($("#attribList"+ indx +"_chosen span").text() != uiConstants.common.ENTER_SELECT_ATTRIB_LIST){
						$("#attribList"+ indx +"_chosen .chosen-search").find("input").val($("#attribList"+ indx +"_chosen span").text());
					}
				});

				$("#attribList"+ indx +"_chosen .chosen-search").find("input").on("keyup", function (evt) {
					var attrId = ($(this).parent().parent().parent().attr('id')).split("_")[0];//.parentNode.parentNode.attr('id'));
					if(uiConstants.common.DEBUG_MODE)console.log(($(this).parent().parent().parent().attr('id')));

					if($(this).val() != undefined && $(this).val() != ""){
			        	$("#"+ attrId +"_chosen span").text($(this).val());
					}

					if(evt.which == 13){
						$("#"+ attrId +"_chosen").parent().trigger("click");
					}
			    });

				$("#attribList"+ indx).chosen({}).on('chosen:hiding_dropdown', function(){
					//console.log($(this).attr('id'));
					var attrId = $(this).attr('id');
					if(uiConstants.common.DEBUG_MODE)console.log(attrId);

					onChosenDropdownClose("attribList"+ indx, "parametersPanel");

					var selCompNameTxt = "";

	        		if(!$('#'+attrId +'_chosen .chosen-results').find("li.active-result.result-selected")){
	        			selCompNameTxt = $('#'+attrId +'_chosen .chosen-search input[type="text"]').val();
	        		}

		           	if(selCompNameTxt!=""){
		           		$('#'+attrId).val("0").trigger('chosen:updated');
	           			$("#"+ attrId +"_chosen span").text(selCompNameTxt);

		           		$('#'+attrId +' > option').each(function(){
		           			if(uiConstants.common.DEBUG_MODE)console.log($(this).text());
		 					if($(this).text()==selCompNameTxt) {
		 						$(this).parent('select').val($(this).val());
		 						$('#'+attrId).val($(this).val()).trigger('chosen:updated');
		 					}
		 				});
		           	}
	        	});
			}
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

       	//Adding/Updating single component
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var componentWithVersionArr = [];
			var configData;
			var producerAttribsArr = [];
			var isErrorExists = 0;

			$("#divProducerAddEdit #txtName").val($("#divProducerAddEdit #txtName").val().trim());
			$("#divConfigDescription #txtDescription").val($("#divConfigDescription #txtDescription").val().trim());

			if($("#divProducerAddEdit #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.producerConfig.PRODUCER_NAME_REQUIRED);
				showError("#divProducerAddEdit #txtName", uiConstants.producerConfig.PRODUCER_NAME_REQUIRED);
			    self.errorMsg("#divProducerAddEdit #txtName");
			}
			else if($("#divProducerAddEdit #txtName").val().length < 2){
				//self.errorMsg(uiConstants.producerConfig.PRODUCER_NAME_MIN_LENGTH_ERROR);
				showError("#divProducerAddEdit #txtName", uiConstants.producerConfig.PRODUCER_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divProducerAddEdit #txtName");
			}
			else if($("#divProducerAddEdit #txtName").val().length > 45){
				//self.errorMsg(uiConstants.producerConfig.PRODUCER_NAME_MAX_LENGTH_ERROR);
				showError("#divProducerAddEdit #txtName", uiConstants.producerConfig.PRODUCER_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divProducerAddEdit #txtName");
			}
			else if(!nameValidation($("#divProducerAddEdit #txtName").val())){
				//self.errorMsg(uiConstants.producerConfig.PRODUCER_NAME_INVALID_ERROR);
				showError("#divProducerAddEdit #txtName", uiConstants.producerConfig.PRODUCER_NAME_INVALID_ERROR);
			    self.errorMsg("#divProducerAddEdit #txtName");
			}
			if($("#divConfigDescription #txtDescription").val().trim() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			else if($("#divConfigDescription #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			else if($("#divConfigDescription #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			if($("#producerTypeList").val() == 0){
				//self.errorMsg(uiConstants.common.SELECT_PRODUCER_TYPE_MSG);
				showError("#divProducerAddEdit #producerTypeList_chosen", uiConstants.common.SELECT_PRODUCER_TYPE_MSG);
				showError("#divProducerAddEdit #producerTypeList_chosen span", uiConstants.common.SELECT_PRODUCER_TYPE_MSG);
			    self.errorMsg("#divProducerAddEdit #producerTypeList_chosen");
			}
			else{
				removeError("#divProducerAddEdit #producerTypeList_chosen");
				removeError("#divProducerAddEdit #producerTypeList_chosen span");
			}

			if(self.errorMsg() == ""){
				if(self.versionKpiMappingData().length == 0){
					//self.errorMsg(uiConstants.producerConfig.SELECT_COMP_WITH_VERSION_MSG);
					showMessageBox(uiConstants.producerConfig.SELECT_COMP_WITH_VERSION_MSG, "error");
				    self.errorMsg("#divCompKpiAssociation");
				}

				else if($("#selectedKpisList").getAllValues().length == 0){
					//self.errorMsg(uiConstants.producerConfig.SELECT_KPI_FOR_PRODUCER);
					showError("#divProducerAddEdit #selectedKpisList", uiConstants.producerConfig.SELECT_KPI_FOR_PRODUCER);
				    self.errorMsg("#divProducerAddEdit #kpiDiv");
				}	
			}

			
			/*else if($("#compTypeList").val() == 0){
				self.errorMsg(uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
			}
			else if($("#compNamesList").val() == 0){
				self.errorMsg(uiConstants.common.SELECT_COMPONENT_MSG);
			}*/

			removeError(".tokenfield");
			removeError("#producer-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#producer-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError(".tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#producer-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg(".tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError(".tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#producer-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg(".tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError(".tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#producer-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg(".tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#producer-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg(".tokenfield");
						break;
					}
				}

				var selComponentTypeIdsArr = [];
				var selComponentIdsArr = [];
				for(attrib in self.versionKpiMappingData()){
					//if(deletedRows.indexOf(parseInt(attrib)) == -1){
						if(selComponentIdsArr.indexOf(self.versionKpiMappingData()[attrib].componentId) == -1){
							selComponentTypeIdsArr.push(self.versionKpiMappingData()[attrib].componentTypeId);
							selComponentIdsArr.push(self.versionKpiMappingData()[attrib].componentId);
						}
					//}
				}

				var selVersionKpisArr;
				var selVersionsArr;
				//var selKpisArr;
				var kpiId;
				var kpiName;


				for(var selCompId in selComponentIdsArr){
					selVersionKpisArr = $.grep(self.versionKpiMappingData(), function(e){ return e.componentId == selComponentIdsArr[selCompId]; });
					selVersionsArr = [];
					console.log(selVersionKpisArr);
					for(var selVersion in selVersionKpisArr){
						//selKpisArr = [];
						if(uiConstants.common.DEBUG_MODE)console.log(selVersionKpisArr[selVersion]);

						selVersionsArr.push({"componentVersionId": parseInt(selVersionKpisArr[selVersion].versionId),
							"componentVersion": selVersionKpisArr[selVersion].version});
/*
						if(selVersionKpisArr[selVersion].kpis[0].isKpiGroup){
							selVersionsArr.push({
								"versionId": selVersionKpisArr[selVersion].versionId,
								"kpiGroups": selKpisArr
							});
						}
						else{
							selVersionsArr.push({
								"versionId": selVersionKpisArr[selVersion].versionId,
								"kpis": selKpisArr
							});
						}*/
					}

					componentWithVersionArr.push({
							"componentTypeId": parseInt(selComponentTypeIdsArr[selCompId]),
							"componentId": parseInt(selComponentIdsArr[selCompId]),
							"componentVersions": selVersionsArr
						})
				}
				if(uiConstants.common.DEBUG_MODE)console.log(componentWithVersionArr);

				if(slugify(self.producerType()) == "script" && ($("#txtScriptName").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.SCRIPT_NAME_REQUIRED);
					showError("#divProducerAddEdit #txtScriptName", uiConstants.producerConfig.SCRIPT_NAME_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtScriptName");
				}
				if(slugify(self.producerType()) == "script" && ($("#txtScriptSignature").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.SCRIPT_SIGNATURE_REQUIRED);
					showError("#divProducerAddEdit #txtScriptSignature", uiConstants.producerConfig.SCRIPT_SIGNATURE_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtScriptSignature");
				}
				if(slugify(self.producerType()) == "jdbc" && ($("#txtQuery").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.SQL_QUERY_REQUIRED);
					showError("#divProducerAddEdit #txtQuery", uiConstants.producerConfig.SQL_QUERY_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtQuery");
				}
				if(slugify(self.producerType()) == "jdbc" && ($("#txtJdbcDriver").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.JDBC_DRIVER_REQUIRED);
					showError("#divProducerAddEdit #txtJdbcDriver", uiConstants.producerConfig.JDBC_DRIVER_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtJdbcDriver");
				}
				if(slugify(self.producerType()) == "jdbc" && ($("#txtJdbcUrl").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.JDBC_URL_REQUIRED);
					showError("#divProducerAddEdit #txtJdbcUrl", uiConstants.producerConfig.JDBC_URL_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtJdbcUrl");
				}
				if(slugify(self.producerType()) == "jdbc" && ($("#selectQueryResult option:selected").text() == uiConstants.producerConfig.SELECT_QUERY_RESULT)){
					//self.errorMsg(uiConstants.producerConfig.JDBC_QUERY_RESULT_REQUIRED);
					showError("#divProducerAddEdit #selectQueryResult_chosen", uiConstants.producerConfig.JDBC_QUERY_RESULT_REQUIRED);
					showError("#divProducerAddEdit #selectQueryResult_chosen span", uiConstants.producerConfig.JDBC_QUERY_RESULT_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #selectQueryResult_chosen");
				}
				if(slugify(self.producerType()) == "jmx" && ($("#selectAttrType option:selected").text() == uiConstants.producerConfig.SELECT_ATTRIBUTE_TYPE)){
					//self.errorMsg(uiConstants.producerConfig.JDBC_QUERY_RESULT_REQUIRED);
					showError("#divProducerAddEdit #selectAttrType_chosen", uiConstants.producerConfig.JMX_ATTR_TYPE_REQUIRED);
					showError("#divProducerAddEdit #selectAttrType_chosen span", uiConstants.producerConfig.JMX_ATTR_TYPE_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #selectAttrType_chosen");
				}
				if(slugify(self.producerType()) == "jppf" && ($("#selJppfServerType option:selected").text() == uiConstants.producerConfig.SELECT_SERVER_TYPE)){
					//self.errorMsg(uiConstants.producerConfig.JDBC_QUERY_RESULT_REQUIRED);
					showError("#divProducerAddEdit #selJppfServerType_chosen", uiConstants.producerConfig.JPPF_SERVER_TYPE_REQUIRED);
					showError("#divProducerAddEdit #selJppfServerType_chosen span", uiConstants.producerConfig.JPPF_SERVER_TYPE_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #selJppfServerType_chosen");
				}
				if(slugify(self.producerType()) == "wmi" && ($("#txtShellScriptName").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.SCRIPT_NAME_REQUIRED);
					showError("#divProducerAddEdit #txtShellScriptName", uiConstants.producerConfig.SCRIPT_NAME_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtShellScriptName");
				}
				if(slugify(self.producerType()) == "wmi" && ($("#txtShellScriptSignature").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.SCRIPT_SIGNATURE_REQUIRED);
					showError("#divProducerAddEdit #txtShellScriptSignature", uiConstants.producerConfig.SCRIPT_SIGNATURE_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtShellScriptSignature");
				}
				if(slugify(self.producerType()) == "jmx" && ($("#txtJmxTargetObjName").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.TARGET_OBJECT_NAME_REQUIRED);
					showError("#divProducerAddEdit #txtJmxTargetObjName", uiConstants.producerConfig.TARGET_OBJECT_NAME_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtJmxTargetObjName");
				}
				if(slugify(self.producerType()) == "jmx" && ($("#txtJmxUrl").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.JMX_URL_REQUIRED);
					showError("#divProducerAddEdit #txtJmxUrl", uiConstants.producerConfig.JMX_URL_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtJmxUrl");
				}
				if(slugify(self.producerType()) == "http_json" && ($("#txtHttpJsonStatusUrl").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.HTTP_JSON_URL_REQUIRED);
					showError("#divProducerAddEdit #txtHttpJsonStatusUrl", uiConstants.producerConfig.HTTP_JSON_URL_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtHttpJsonStatusUrl");
				}
				if(slugify(self.producerType()) == "http" && ($("#txtHttpdStatusUrl").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.HTTPD_URL_REQUIRED);
					showError("#divProducerAddEdit #txtHttpdStatusUrl", uiConstants.producerConfig.HTTPD_URL_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtHttpdStatusUrl");
				}
				/*else if(slugify(self.producerType()) == "http" && ($("#httpContentType option:selected").text() == uiConstants.producerConfig.SELECT_CONTENT_TYPE)){
					self.errorMsg(uiConstants.producerConfig.CONTENT_TYPE_REQUIRED);
				}*/
				if(slugify(self.producerType()) == "was" && ($("#txtWasTargetObjName").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.TARGET_OBJECT_NAME_REQUIRED);
					showError("#divProducerAddEdit #txtWasTargetObjName", uiConstants.producerConfig.TARGET_OBJECT_NAME_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtWasTargetObjName");
				}
				if(slugify(self.producerType()) == "was" && ($("#txtWasModuleName").val().trim() == "")){
					//self.errorMsg(uiConstants.producerConfig.MODULE_NAME_REQUIRED);
					showError("#divProducerAddEdit #txtWasModuleName", uiConstants.producerConfig.MODULE_NAME_REQUIRED);
				    self.errorMsg("#divProducerAddEdit #txtWasModuleName");
				}

				for(attrib in self.attributesArr()){
					if(slugify(self.producerType()) != "http_json"){
						$("#attribList"+attrib + "_chosen span")[0].innerHTML = $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim();
					}

					if((slugify(self.producerType()) == "jmx" || slugify(self.producerType()) == "was" || slugify(self.producerType()) == "http_json") && $("#paramName"+attrib).val().trim() == ""){
						//self.errorMsg(uiConstants.producerConfig.SELECT_PARAM_NAME_MSG);
						showError("#divProducerAddEdit #paramName"+attrib, uiConstants.producerConfig.SELECT_PARAM_NAME_MSG);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #paramName"+attrib).offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
					}
					if(slugify(self.producerType()) != "http_json" && $("#attribList"+attrib + "_chosen span")[0].innerHTML == uiConstants.common.ENTER_SELECT_ATTRIB_LIST){
						//self.errorMsg(uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen", uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen span", uiConstants.common.ENTER_SELECT_ATTRIB_LIST_MSG);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #attribList"+attrib + "_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
						//break;
					}
					else if(slugify(self.producerType()) != "http_json" && $("#attribList"+attrib + "_chosen span")[0].innerHTML.length < 2){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_MIN_LENGTH_ERROR);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #attribList"+attrib + "_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
						//break;
					}
					else if(slugify(self.producerType()) != "http_json" && $("#attribList"+attrib + "_chosen span")[0].innerHTML.length > 45){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_MAX_LENGTH_ERROR);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #attribList"+attrib + "_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
						//break;
					}
					else if(slugify(self.producerType()) != "http_json" && !nameValidation($("#attribList"+attrib + "_chosen span")[0].innerHTML.trim())){
						//self.errorMsg(uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen", uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
						showError("#divProducerAddEdit #attribList"+attrib + "_chosen span", uiConstants.componentConfig.ATTRIBUTE_NAME_INVALID_ERROR);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #attribList"+attrib + "_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
						//break;
					}
					else{
						removeError("#divProducerAddEdit #attribList"+attrib + "_chosen");
						removeError("#divProducerAddEdit #attribList"+attrib + "_chosen span");
					}
					
					if(slugify(self.producerType()) == "script" && ($("#parameterType"+attrib + " option:selected").text() == uiConstants.producerConfig.SELECT_PARAM_TYPE)){
						//self.errorMsg(uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen", uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen span", uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							  	scrollTop: $("#divProducerAddEdit #parameterType"+attrib+"_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
						//break;
					}
					
					if(slugify(self.producerType()) == "wmi" && ($("#parameterType"+attrib + " option:selected").text() == uiConstants.producerConfig.SELECT_PARAM_TYPE)){
						//self.errorMsg(uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen", uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen span", uiConstants.producerConfig.PARAMETER_TYPE_REQUIRED_ERROR);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #parameterType"+attrib+"_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
					}

					if(slugify(self.producerType()) == "http_json" && $("#paramValue"+attrib).val().trim() == ""){
						//self.errorMsg(uiConstants.producerConfig.ENTER_PARAM_VALUE_MSG);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen", uiConstants.producerConfig.ENTER_PARAM_VALUE_MSG);
						showError("#divProducerAddEdit #parameterType"+attrib+"_chosen span", uiConstants.producerConfig.ENTER_PARAM_VALUE_MSG);
			   			self.errorMsg("#parametersPanel");

			   			if(isErrorExists == 0){
							$("#parametersPanel").animate({
							    scrollTop: $("#divProducerAddEdit #parameterType"+attrib+"_chosen").offset().top - $("#parametersPanel").offset().top + $("#parametersPanel").scrollTop() - 80
							});
					    }
						isErrorExists = 1;
					}

					var attribObj;

					if(slugify(self.producerType()) != "http_json"){
						attribObj = $.grep(self.attributesMasterArr(), function(evt){ return evt.attributeName == $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(); })[0];
					}

					if(uiConstants.common.DEBUG_MODE)console.log(attribObj);

					if(slugify(self.producerType()) == "script"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": "Parameter"+attrib,
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(),
	        				"parameterType": $("#parameterType"+attrib + " option:selected").text()
						});
					}
					else if(slugify(self.producerType()) == "jdbc"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": "Parameter"+attrib,
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim()
						});
					}
					else if(slugify(self.producerType()) == "wmi"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": "Parameter"+attrib,
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(),
	        				"parameterType": $("#parameterType"+attrib + " option:selected").text()
						});
					}
					else if(slugify(self.producerType()) == "jmx"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": $("#paramName"+attrib).val(),
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(),
	        				"parameterType": "key_value"
						});
					}
					else if(slugify(self.producerType()) == "http"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": "Parameter"+attrib,
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim()
						});
					}
					else if(slugify(self.producerType()) == "http_json"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": $("#paramName"+attrib).val().trim(),
							"parameterValue": $("#paramValue"+attrib).val().trim()
						});
					}
					else if(slugify(self.producerType()) == "was"){
						producerAttribsArr.push({
							"parameterIndex": (parseInt(attrib) + 1),
							"parameterId": attribObj == undefined ? 0: parseInt(attribObj.attributeId),
							"parameterName": $("#paramName"+attrib).val(),
							"parameterValue": $("#attribList"+attrib + "_chosen span")[0].innerHTML.trim(),
	        				"parameterType": "key_value"
						});
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}

					var selectedKpiIdsArr = $("#selectedKpisList").getAllValues().map(function (x){return parseInt(x);});

					discardedKpiIdsArr = removeDuplicates(removeArrayElements(discardedKpiIdsArr, selectedKpiIdsArr, true));

					var configObj = {"index":1,
						"producerName": self.configName(),
						"description":  self.configDescription().trim(),
						"producerTypeId": parseInt($("#producerTypeList").val()),
						//"componentTypeId": parseInt($("#compTypeList").val()),
						"kpiTypeId": parseInt($("#kpiTypeList").val()),
						"components": componentWithVersionArr,
						//"kpiIds" : $("#selectedKpisList").getAllValues().map(function (x){return parseInt(x);}),
						"discardedKpiIds": discardedKpiIdsArr,
						"producerParameters": producerAttribsArr,
						"tags": tagsObjArr,
						"status" : self.configStatus()?1:0};

					if($("input[name='kpiGroupType']:checked").val() == "grouped"){
						configObj["kpiGroupIds"] = selectedKpiIdsArr;
					}
					else{
						configObj["kpiIds"] = selectedKpiIdsArr;
					}

					var producerTypeParameters;

					if(slugify(self.producerType()) == "script"){
						producerTypeParameters = {"scriptName": self.scriptName(),
						"scriptSignature": self.scriptSignature()};
					}
					else if(slugify(self.producerType()) == "jdbc"){
						producerTypeParameters = {"query": self.query(),
							"driver": self.jdbcDriver(),
							"url": self.jdbcUrl(),
							"queryResult": $("#selectQueryResult option:selected").text()};
					}
					else if(slugify(self.producerType()) == "wmi"){
						producerTypeParameters = {"scriptName": self.shellScriptName(),
						"scriptSignature": self.shellScriptSignature()};
					}
					else if(slugify(self.producerType()) == "jmx"){
						producerTypeParameters = {"targetObjectName": self.jmxTargetObjName(),
							"jmxUrl": self.jmxUrl(),
							"attributeType": $("#selectAttrType option:selected").text(),
							"attributeTypeId": parseInt($("#selectAttrType").val())};
					}
					else if(slugify(self.producerType()) == "jppf"){
						producerTypeParameters = {"targetObjectName": self.jmxTargetObjName(),
							"serverTypeId": parseInt($("#selJppfServerType").val())};
					}
					else if(slugify(self.producerType()) == "http"){
						producerTypeParameters = {"httpUrl": self.httpdStatusUrl()};
					}
					else if(slugify(self.producerType()) == "http_json"){
						producerTypeParameters = {"httpJsonUrl": self.httpJsonStatusUrl()};
					}
					else if(slugify(self.producerType()) == "was"){
						producerTypeParameters = {"targetObjectName": self.wasTargetObjName(),
							"wasModuleName": self.wasModuleName()};
					}

					configObj["producerTypeParameters"] = producerTypeParameters;
					configData = {"producer":configObj};

					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configData));

					if(self.configId() == 0)
						requestCall(uiConstants.common.SERVER_IP + "/producer", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
					else
						requestCall(uiConstants.common.SERVER_IP + "/producer/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
				}
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable(true);
			if(self.selectedConfigRows()[0].isCustom == 0){
				setProdParamsUneditable();
				self.isCustomEdit(false);
			}
			else{
				self.isCustomEdit(true);
			}
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			//setConfigUneditable(false);			
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(isInactiveEdit){
			$('#producerTypeList').prop('disabled', true).trigger("chosen:updated");
			if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
				$('#kpiTypeList').prop('disabled', true).trigger("chosen:updated");
				$('input[name=kpiGroupType]').prop('disabled', true);
				//$("#versionDiv").find("input,button,select").attr("disabled", "disabled");
				
				if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW || (self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.selectedConfigRows()[0].isCustom == 0)){
					$('#txtName').prop('readonly', true);
					$('#txtDescription').prop('readonly', true);
					$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
					$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
				
					if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						$('#producer-tokenfield-typeahead').tokenfield('readonly');
					}
					//$("#versionDiv").find("input,button,select").attr("disabled", "disabled");
					//$("#versionsList").addClass("checklist-disabled");
					//$("#kpiDiv").find("input,button,select").attr("disabled", "disabled");
					//$("#kpisList").addClass("checklist-disabled");

					setProdParamsUneditable();
				}

				if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.currentViewIndex() == uiConstants.common.EDIT_VIEW && (self.selectedConfigRows()[0].status == 0 || self.selectedConfigRows()[0].isCustom == 0))){
					//document.getElementById("configStatus").disabled = true;
					$("[name='configStatus']").bootstrapSwitch('disabled',true);
					self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

					$("#divProducerAddEdit .chosen-container b").css("display", "none");
				}
			}
			
		}

		function setProdParamsUneditable(){
			self.enableConfig(false);
        	if(slugify(self.producerType()) == "script"){
				//$("#txtScriptName").find("input,button,select").attr("disabled", "disabled");
				$('#txtScriptName').prop('readonly', true);
				$('#txtScriptSignature').prop('readonly', true);
			}
			else if(slugify(self.producerType()) == "jdbc"){
				$('#txtQuery').prop('readonly', true);
				$('#txtJdbcDriver').prop('readonly', true);
				$('#txtJdbcUrl').prop('readonly', true);
				$("#selectQueryResult").prop('disabled', true).trigger('chosen:updated');
			}
			else if(slugify(self.producerType()) == "wmi"){
				$("#txtShellScriptName").prop('readonly', true);
				$('#txtShellScriptSignature').prop('readonly', true);
			}
			else if(slugify(self.producerType()) == "jmx"){
				$('#txtJmxTargetObjName').prop('readonly', true);
				$("#txtJmxUrl").prop('disabled', true);
				$("#selectAttrType").prop('disabled', true).trigger('chosen:updated');

			}
			else if(slugify(self.producerType()) == "jppf"){
				$("#selJppfServerType").prop('disabled', true).trigger('chosen:updated');
			}
			else if(slugify(self.producerType()) == "http"){
				$('#txtHttpdStatusUrl').prop('readonly', true);
			}
			else if(slugify(self.producerType()) == "http_json"){
				$('#txtHttpJsonStatusUrl').prop('readonly', true);
			}
			else if(slugify(self.producerType()) == "was"){
				$('#txtWasTargetObjName').prop('readonly', true);
				$("#txtWasModuleName").prop('disabled', true);
			}
        }

		function cloneConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable(true);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			checkRequired = false;
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].producerName);
				self.configId(configObj[0].producerId);
				self.configDescription(configObj[0].description);
			}

			$("#producerTypeList").val(configObj[0].producerTypeId).trigger('chosen:updated');
			self.producerType($("#producerTypeList option:selected").text());

			//$("#compTypeList").val(configObj[0].componentTypeId).trigger('chosen:updated');
			//setCompNames();

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(self.componentNamesArr());
				if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === self.selectedConfigRows()[0].componentId;} )){
					self.componentNamesArr.push({
						"componentId": configObj[0].componentId,
						"componentName": configObj[0].componentName,
						"isActive": false
					});
					$("#compNamesList_chosen span").first().addClass("inactiveOptionClass");
				}
				//$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
			}
			/*else if(self.currentViewIndex() == CLONE_VIEW){
				if(!self.componentNamesArr().find( function( ele ) {return ele.componentId && ele.componentId === self.selectedConfigRows()[0].componentId;} )){
					$("#compNamesList").val("0").trigger('chosen:updated');
				}
				else{
					$("#compNamesList").val(configObj[0].componentId).trigger('chosen:updated');
				}
			}*/
			
			//setCompVersions();

			/*var selectedVersionsArr = self.selectedConfigRows()[0].componentVersions;
			for(versionId in selectedVersionsArr){
				$("#versionsList .checkList[value=" + selectedVersionsArr[versionId].componentVersionId + "]").prop("checked",true);
			}

			$("#selAllVersions").prop("checked", selectedVersionsArr.length == $("#versionsList").getAllValues().length);
			*/

			$("#kpiTypeList").val(configObj[0].kpiTypeId).trigger('chosen:updated');



			//if(self.currentViewIndex() != ADD_SINGLE_VIEW){
				var selectedKpisArr = [];
				if(params.isModal){
					selectedKpisArr.push(self.selectedKpiObj());
				}
				else{
					selectedKpisArr = configObj[0].kpis;
				}

				if(selectedKpisArr[0].kpiId){
					$("input[name=kpiGroupType][value=ungrouped]").prop('checked', true);
					self.commonKpiOrGroup("Common KPIs");
				}
				else{
					$("input[name=kpiGroupType][value=grouped]").prop('checked', true);
					self.commonKpiOrGroup("Common KPI Groups");
				}


			for(var selComp in configObj[0].components){
				//versionKpiObj = $.grep(self.versionKpiMappingData(), function(e){ return e.versionId == selectedVersions[selVersion].value; });

				for(var selVersion in configObj[0].components[selComp].componentVersions){
					versionKpiObjToAdd = {
						"componentTypeId": configObj[0].components[selComp].componentTypeId,
						"componentType": configObj[0].components[selComp].componentType,
						"componentId": configObj[0].components[selComp].componentId,
						"componentName": configObj[0].components[selComp].componentName + (configObj[0].components[selComp].componentStatus == 0 ? " (Inactive)" : ""),
						"versionId": configObj[0].components[selComp].componentVersions[selVersion].componentVersionId,
						"version": configObj[0].components[selComp].componentVersions[selVersion].componentVersion + (configObj[0].components[selComp].componentVersions[selVersion].status == 0 ? " (Inactive)" : "")/*,
						"kpis": selectedVersionKpis*/
					};

					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW || (self.currentViewIndex() == uiConstants.common.CLONE_VIEW && configObj[0].components[selComp].componentStatus == 1)){
						self.versionKpiMappingData.push(versionKpiObjToAdd);
					}
				}
			}

			deleteVersionKpiMappingListener();
			
			if(uiConstants.common.DEBUG_MODE)console.log(self.versionKpiMappingData());

			if(configObj[0].kpis[0].kpiId){
				self.selectedKpisArr(configObj[0].kpis);
			}
			else{
				for(var kpiGroup in configObj[0].kpis){
					self.selectedKpisArr.push({
						"kpiId": configObj[0].kpis[kpiGroup].kpiGroupId, 
						"kpiName": configObj[0].kpis[kpiGroup].kpiGroupName});
				}
			}

			self.loadKpis();



			requestCall(uiConstants.common.SERVER_IP + "/producerParameter/" + self.selectedConfigRows()[0].producerId, "GET", "", "getProducerParameters", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57595ed00f0000d018a02435?callback=?", "GET", "", "getProducerParameters", successCallback, errorCallback);
			
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#producer-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
				setConfigUneditable(true); //Certain fields in edit will be disabled
			}

			self.errorMsg("");
		}

		function setModalConfigValues(){

			if(uiConstants.common.DEBUG_MODE)console.log(self.isKpiGroup());
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedKpiObj());
			$("#compTypeList").val(self.selectedComponentTypeObj().componentTypeId).trigger('chosen:updated');
			setCompNames();
			$("#compNamesList").val(self.selectedComponentObj().componentId).trigger('chosen:updated');
			setCompVersions();

			//for(versionId in selectedVersionsArr){

			if(self.selectedVersionObj().componentVersionId){
				$("#versionsList .checkList[value=" + self.selectedVersionObj().componentVersionId + "]").prop("checked",true);
			}
			else{
				$("#versionsList .checkList").prop("checked",true);
			}
			//}
			//$("#selAllVersions").prop("checked", $("#versionsList .checkList:checked").length == 1);


			$("#kpiTypeList").val(self.selectedKpiObj().kpiTypeId).trigger('chosen:updated');

			if(self.isKpiGroup()){
				$("input[name=kpiGroupType][value=grouped]").prop('checked', true);
				self.commonKpiOrGroup("KPI Groups");
			}
			else{
				$("input[name=kpiGroupType][value=ungrouped]").prop('checked', true);
				self.commonKpiOrGroup("KPIs");
			}

			self.addVersionKpiMapping();


			//$("#kpiTypeList").val(configObj[0].kpiTypeId);
			//self.loadKpis();

			//$('#compTypeList').prop('disabled', true).trigger("chosen:updated");
			//$('#compNamesList').prop('disabled', true).trigger("chosen:updated");
			$('#kpiTypeList').prop('disabled', true).trigger("chosen:updated");
			$('input[name=kpiGroupType]').prop('disabled', true);
		}

		this.cancelConfig = function(){
			if(self.isModal()){
				$(".modal-header button").click();

				//params.modalConfigName("Producer6");
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Producer Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function setCompNames(){
			if($("#compTypeList").val() == 0){
				self.componentNamesArr({});
			}
			else{
				var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#compTypeList").val(); });

				if(componentsObj[0] && componentsObj[0].components){
					if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
					self.componentNamesArr(componentsObj[0].components);
				}
			}
			$("#compNamesList").trigger('chosen:updated');
		}

		function setCompVersions(){
			var versionArr = [];
			self.versionsArr([]);
			if($("#compNamesList").val() != 0){
				var versionsObj = $.grep(self.componentNamesArr(), function(e){ return e.componentId == $("#compNamesList").val(); });
				if(uiConstants.common.DEBUG_MODE)console.log(versionsObj);

				if(versionsObj[0] && versionsObj[0].versions){
					self.versionsArr(versionsObj[0].versions);

					for(version in self.versionsArr()){
						versionArr.push({
							"id": self.versionsArr()[version].versionId,
							"name": self.versionsArr()[version].version
						});
					}
				}
			}

			/*if(self.currentViewIndex() == EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				for(selVersion in self.selectedConfigRows()[0].componentVersions){
					if(!versionArr.find( function( ele ) {return ele.id && ele.id === self.selectedConfigRows()[0].componentVersions[selVersion].componentVersionId;} )){
						versionArr.push({
							"id": self.selectedConfigRows()[0].componentVersions[selVersion].componentVersionId,
							"name": self.selectedConfigRows()[0].componentVersions[selVersion].componentVersion,
							"isActive": false
						});
						self.versionsArr.push(versionArr[versionArr.length-1]);
					}
				}

				sortArrayObjByValue(versionArr, "name");
			}*/

			$("#versionsList").checklistbox({
	            data: versionArr
	        });

		        //self.clearKpisList();
		}

		this.clearKpisList = function(){
			var kpiArr = [];
			$("#availableKpisList").checklistbox({
	            data: kpiArr
	        });

	        return true;
		}

		this.loadKpis = function(){
			/*if($('#versionsList .checkList:checked').length == 0){
				self.errorMsg(uiConstants.common.SELECT_VERSIONS);
			}
			else */if($("#kpiTypeList").val() == "" || $("#kpiTypeList").val() == undefined){
				self.errorMsg(uiConstants.common.SELECT_KPI_TYPE_MSG);
			}
			else{
				/*for(maping in self.versionKpiMappingData()){

				}*/
				//var componentVersionsArr = $("#versionsList").getSelectedValues();//Send this versions array to REST API to get KPI list
				var compVersionQueryString = "";
				for(var compVersion in self.versionKpiMappingData()){
					compVersionQueryString = compVersionQueryString + "&componentVersionId=" + self.versionKpiMappingData()[compVersion].versionId;
				}
				requestCall(uiConstants.common.SERVER_IP + "/kpis?" + compVersionQueryString.substring(1)+"&status=2&markInactive=1", "GET", "", "getCommonKpisList", successCallback, errorCallback);
				
				/*if(self.versionKpiMappingData().length == 1){
					requestCall("http://www.mocky.io/v2/5791e1d7110000131b5af615?callback=?", "GET", "", "getCommonKpisList", successCallback, errorCallback);
				}
				else{
					requestCall("http://www.mocky.io/v2/5791e219110000011b5af617?callback=?", "GET", "", "getCommonKpisList", successCallback, errorCallback);
				}*/
			}
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function deleteVersionKpiMappingListener(){
			$("#compVersionList tbody").on('click', '.buttondelete', function(e){
				var tableObj = this;
			    showMessageBox(uiConstants.producerConfig.CONFIRM_MAPPING_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
					if (confirmDelete) {
						//alert($(tableObj).closest('tr').get(0).rowIndex);
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	//deletedRows.push(rowToDelete);
					 	//$(this).closest('tr').css("display","none");
					 	self.versionKpiMappingData.splice(rowToDelete,1);
					 	self.loadKpis();
					}
				});
			});
		}

		this.addVersionKpiMapping = function(){
			self.errorMsg("");

			if(!params.isModal){
				if($("#compTypeList").val() == 0){
					showError("#compTypeList_chosen", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
					showError("#compTypeList_chosen span", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				    self.errorMsg("#divProducerAddEdit #divCompKpiAssociation");
				}
				else{
					removeError("#compTypeList_chosen");
					removeError("#compTypeList_chosen span");
				}

				if($("#compNamesList").val() == 0){
					showError("#compNamesList_chosen", uiConstants.common.SELECT_COMPONENT_MSG);
					showError("#compNamesList_chosen span", uiConstants.common.SELECT_COMPONENT_MSG);
				    self.errorMsg("#divProducerAddEdit #divCompKpiAssociation");
				}
				else{
					removeError("#compNamesList_chosen");
					removeError("#compNamesList_chosen span");
				}

				if($('#versionsList .checkList:checked').length == 0){
					showError("#divProducerAddEdit #versionsList", uiConstants.common.SELECT_VERSIONS);
				    self.errorMsg("#divProducerAddEdit #divCompKpiAssociation");
				}

				if($('#versionsList .checkList:checked').length == 0){
					showError("#divProducerAddEdit #versionsList", uiConstants.common.SELECT_VERSIONS);
				    self.errorMsg("#divProducerAddEdit #divCompKpiAssociation");
				}
				/*else if($('#kpisList .checkList:checked').length == 0){
					self.errorMsg(uiConstants.common.SELECT_KPIS);
				}*/
			}
			
			if(self.errorMsg() == ""){
				var versionKpiObj;
				var versionKpiObjToAdd;
				//$("#versionsList .checkList").prop("checked",true);

				var selectedVersions = $("#versionsList").getSelectedItems();
				//var selectedVersionKpis;
				//var selectedAllVersionKpis;
				versionKpiMappingDataCopy = [];

				for(versionMap in self.versionKpiMappingData()){
					versionKpiMappingDataCopy.push(self.versionKpiMappingData()[versionMap]);
				}

				for(var selVersion in selectedVersions){
					//selectedVersionKpis = [];
					//selectedAllVersionKpis = [];

					/*if(params.isModal){
						if(self.isKpiGroup()){
							selectedVersionKpis.push({"value":params.selectedKpiObj().kpiGroupId,"label":params.selectedKpiObj().kpiGroupName,"isKpiGroup":true});
						}
						else{
							selectedVersionKpis.push({"value":params.selectedKpiObj().kpiId,"label":params.selectedKpiObj().kpiName,"isKpiGroup":false});
						}
					}
					else{
						for(var kpi in selectedKpis){
							selectedAllVersionKpis = $.grep(self.kpisArr(), function(e){ return e.kpiId == selectedKpis[kpi].value; })[0];
							console.log(selectedAllVersionKpis);
							if(selectedAllVersionKpis.componentVersionIds.indexOf(parseInt(selectedVersions[selVersion].value)) != -1){
								selectedVersionKpis.push(selectedKpis[kpi]);
							}
						}
					}*/

					versionKpiObj = $.grep(self.versionKpiMappingData(), function(e){ return e.versionId == selectedVersions[selVersion].value; });

					versionKpiObjToAdd = {
						"componentTypeId": $("#compTypeList").val(),
						"componentType": $("#compTypeList option:selected").text(),
						"componentId": $("#compNamesList").val(),
						"componentName": $("#compNamesList option:selected").text(),
						"versionId": selectedVersions[selVersion].value,
						"version": selectedVersions[selVersion].label/*,
						"kpis": selectedVersionKpis*/
					};

					if(versionKpiObj.length > 0){
						var versionKpiObjIndex = self.versionKpiMappingData.indexOf(versionKpiObj[0]);
						self.versionKpiMappingData.splice(versionKpiObjIndex, 1, versionKpiObjToAdd)
					}
					else{
						self.versionKpiMappingData.push(versionKpiObjToAdd);
					}
				}
				if(uiConstants.common.DEBUG_MODE)console.log(self.versionKpiMappingData());
				if(uiConstants.common.DEBUG_MODE)console.log(versionKpiMappingDataCopy);

				$("#versionsList .checkList").prop("checked",false);
				
				self.loadKpis();
			}
		}

		this.updateVersionKpiMapping = function(){
			self.addVersionKpiMapping();
			self.curOper("addkpi");
		}

		this.cancelEditVersionKpiMapping = function(){
			setVersionKpiMapping();
			setKpiList();
			self.curOper("addkpi");
			//setSelectedKpis();
		}

		function onMastersLoad(){
			if(kpiTypeLoaded == 1 && attributeTypeListLoaded == 1 && parameterTypeLoaded == 1 && queryResultLoaded == 1 && compTypeVersionLoaded == 1 && attributeTypesLoaded == 1){// && configTagLoaded == 1){
				if(params.isModal){
					if(producerTypeListLoaded == 1){
						setModalConfigValues();
					}
				}
				else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
					/*if(!self.selectedConfigRows()[0].status){
						setConfigUneditable(true);
					}*/
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		function setKpiList(){
			var availKpiArr = [];
			var selKpiArr = [];

			for(kpi in self.availableKpisArr()){
				availKpiArr.push({
					"id": self.availableKpisArr()[kpi].kpiId,
					"name": self.availableKpisArr()[kpi].kpiName
				});
			}
			$("#availableKpisList").checklistbox({
	            data: availKpiArr
	        });

	        for(kpi in self.selectedKpisArr()){
				selKpiArr.push({
					"id": self.selectedKpisArr()[kpi].kpiId,
					"name": self.selectedKpisArr()[kpi].kpiName
				});
			}



			$("#selectedKpisList").checklistbox({
	            data: selKpiArr
	        });

			if(self.isModal() || (self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.selectedConfigRows()[0].status == 0) || (self.currentViewIndex() == uiConstants.common.EDIT_VIEW && self.selectedConfigRows()[0].isCustom == 0) || self.currentViewIndex() == uiConstants.common.READ_VIEW){
	        	
	        	$("#divCompKpiAssociation").find("input,button,select").attr("disabled", "disabled");

	        	if(self.isModal()){
	        		if(self.isKpiGroup()){
	        			$("#availableKpisList .checkList[value=" + self.selectedKpiObj().kpiGroupId + "]").prop("checked",true);
	        		}
	        		else{
	        			$("#availableKpisList .checkList[value=" + self.selectedKpiObj().kpiId + "]").prop("checked",true);
	        		}

	        		self.addToSelectedKpis();

	        		$("#availableKpisList").find('input:checkbox').attr("disabled", true);
	        		$("#selectedKpisList").find('input:checkbox').attr("disabled", true);
	        	}
	        }
		}

		/*function setSelectedKpis(){
			//TODO: update this
			for(var kpi in self.versionKpiMappingData()[rowToEdit].kpis){
				$("#kpisList .checkList[value=" + self.versionKpiMappingData()[rowToEdit].kpis[kpi].value + "]").prop("checked",true);
			}

			$("#selAllKpis").prop("checked", self.kpisArr().length == $("#kpisList").getSelectedItems().length);

		}*/

		function reloadKpis(){
			var typeKpiList = [];

			if($("#kpiTypeList option:selected").text().toUpperCase() == "CORE"){
				typeKpiList = self.allCommonKpisList().coreKpis;
			}
			else if($("#kpiTypeList option:selected").text().toUpperCase() == "AVAILABILITY"){
				typeKpiList = self.allCommonKpisList().availabilityKpis;
			}
			else if($("#kpiTypeList option:selected").text().toUpperCase() == "FORENSIC"){
				typeKpiList = self.allCommonKpisList().forensicKpis;
			}
			else if($("#kpiTypeList option:selected").text().toUpperCase() == "CONFIGWATCH"){
				typeKpiList = self.allCommonKpisList().configWatchKpis;
			}
			else if($("#kpiTypeList option:selected").text().toUpperCase() == "FILEWATCH"){
				typeKpiList = self.allCommonKpisList().fileWatchKpis;
			}

			var availabilityKpisCopy = self.availableKpisArr.slice(0);

			self.availableKpisArr([]);

			console.log(typeKpiList);

			if($("input[name='kpiGroupType']:checked").val() == "grouped"){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selTxnIdsArr = [];

					for(var kpi in self.selectedConfigRows()[0].kpis){
						selTxnIdsArr.push(self.selectedConfigRows()[0].kpis[kpi].kpiId);
					}

					typeKpiList.kpiGroups = getMasterList(typeKpiList.kpiGroups, "kpiGroupId", selTxnIdsArr, true);
				}
				else{
					typeKpiList.kpiGroups = getMasterList(typeKpiList.kpiGroups, "kpiGroupId", null, false);
				}

				for(var kpiGroup in typeKpiList.kpiGroups){
					self.availableKpisArr.push({
						"kpiId": typeKpiList.kpiGroups[kpiGroup].kpiGroupId, 
						"kpiName": typeKpiList.kpiGroups[kpiGroup].kpiGroupName});
				}
			}
			else if($("input[name='kpiGroupType']:checked").val() == "ungrouped"){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selTxnIdsArr = [];

					for(var kpi in self.selectedConfigRows()[0].kpis){
						selTxnIdsArr.push(self.selectedConfigRows()[0].kpis[kpi].kpiId);
					}

					typeKpiList.kpiDetails = getMasterList(typeKpiList.kpiDetails, "kpiId", selTxnIdsArr, true);
				}
				else{
					typeKpiList.kpiDetails = getMasterList(typeKpiList.kpiDetails, "kpiId", null, false);
				}

				self.availableKpisArr(typeKpiList.kpiDetails);
			}

			console.log(availabilityKpisCopy);
			console.log(self.availableKpisArr());

			if(self.isModal()){
				var selKpiId;
				var selKpiName;
				if(self.isKpiGroup()){
					selKpiId = self.selectedKpiObj().kpiGroupId;
					selKpiName = self.selectedKpiObj().kpiGroupName;
				}
				else{
					selKpiId = self.selectedKpiObj().kpiId;
					selKpiName = self.selectedKpiObj().kpiName;
				}

				if($.grep(self.availableKpisArr(), function(e){ return e.kpiId == selKpiId; }).length==0){
					self.availableKpisArr.push({
						"kpiId": selKpiId,
						"kpiName": selKpiName
					});
				}
			}
			debugger;
			var selCommonKpis = removeArrayObjElements(self.selectedKpisArr(), "kpiId", self.availableKpisArr(), "kpiId", false);
			var confirmSelectedKpisReload = true;

			if(uiConstants.common.DEBUG_MODE)console.log(typeKpiList);
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableKpisArr());
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedKpisArr());
			if(uiConstants.common.DEBUG_MODE)console.log(selCommonKpis);

			
			var discardedKpisArr = removeArrayObjElements(self.selectedKpisArr(), "kpiId", selCommonKpis, "kpiId", true);



			if(discardedKpisArr.length != 0 && checkRequired){
				var kpiNames = "";
				for(kpiName in discardedKpisArr){
					kpiNames += ", " + discardedKpisArr[kpiName].kpiName;
				}
				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					/*confirmSelectedKpisReload = confirm(uiConstants.producerConfig.PRODUCER_ADD_KPI_DISCARD_CONFIRM
						+ kpiNames.substring(2)
						+ "\n\nDo you want to continue?");*/

					showMessageBox(uiConstants.producerConfig.PRODUCER_ADD_KPI_DISCARD_CONFIRM
						+ kpiNames.substring(2)
						+ "\n\nDo you want to continue?", "question", "confirm", function confirmCallback(confirmVal){
						kpiReloadCheck(discardedKpisArr, selCommonKpis, confirmVal, availabilityKpisCopy);
					});
				}
				else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					/*confirmSelectedKpisReload = confirm(uiConstants.producerConfig.PRODUCER_EDIT_KPI_DISCARD_CONFIRM
						+ kpiNames.substring(2)
						+ "\n\nDo you want to continue?");*/

					showMessageBox(uiConstants.producerConfig.PRODUCER_EDIT_KPI_DISCARD_CONFIRM
						+ kpiNames.substring(2)
						+ "\n\nDo you want to continue?", "question", "confirm", function confirmCallback(confirmVal){
						kpiReloadCheck(discardedKpisArr, selCommonKpis, confirmVal, availabilityKpisCopy);
					});
				}
			}

			else{
				kpiReloadCheck(discardedKpisArr, selCommonKpis, confirmSelectedKpisReload, availabilityKpisCopy);
			}
		}

		/*$("#okBtn").on('click', function () {
			self.cancelConfig();
		});*/

		function kpiReloadCheck(discardedKpisArr, selCommonKpis, confirmSelectedKpisReload, availabilityKpisCopy){
			if(confirmSelectedKpisReload){
				if(self.selectedKpisArr().length > 0){
					self.selectedKpisArr(selCommonKpis);
				}

				if(uiConstants.common.DEBUG_MODE)console.log(self.selectedKpisArr());
				
				if(self.availableKpisArr().length > 0){
					self.availableKpisArr(removeArrayObjElements(self.availableKpisArr(), "kpiId", self.selectedKpisArr(), "kpiId", true));
				}
				
				if(uiConstants.common.DEBUG_MODE)console.log(self.availableKpisArr());

				for(kpi in discardedKpisArr){
					discardedKpiIdsArr.push(discardedKpisArr[kpi].kpiId);
				}
			}
			else{
				self.availableKpisArr(availabilityKpisCopy);
				self.versionKpiMappingData(versionKpiMappingDataCopy);
				versionKpiMappingDataCopy = [];
			}

			setKpiList();
		}

		function successCallback(data, reqType) {
			if(reqType === "getProducerTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);


				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#producer-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#producer-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#producer-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#producer-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getCommonKpisList"){
				console.log(data.result.coreKpis);
				self.allCommonKpisList(data.result);
				reloadKpis();

				
				//else if(self.currentViewIndex() == ADD_SINGLE_VIEW){
					
				//}

				/*if(self.curOper() == "editkpi"){
					setSelectedKpis();
			    }*/
			}
			else if(reqType === "getKpiType"){
				self.kpiTypesArr(data.result);
				if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					$("#kpiTypeList option").filter(function () { return $(this).html().toUpperCase().startsWith('CORE');}).prop('selected', true).trigger('chosen:updated');
				}
				kpiTypeLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getParameterType"){
				self.parameterTypesArr(data.result);
				if(parameterTypeLoaded == 0){
					parameterTypeLoaded = 1;
					onMastersLoad();
				}
			}
			else if(reqType === "getQueryResult"){
				self.queryResultsArr(data.result);
				$("#selectQueryResult").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(queryResultLoaded == 0){
					queryResultLoaded = 1;
					onMastersLoad();
				}
			}
			else if(reqType === "getAttributeTypes"){
				self.attributeTypesArr(data.result);
				$("#selectAttrType").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(attributeTypesLoaded == 0){
					attributeTypesLoaded = 1;
					onMastersLoad();
				}
			}
			else if(reqType === "getJppfServerTypes"){
				self.serverTypesArr(data.result);
				$("#selJppfServerType").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(serverTypesLoaded == 0){
					serverTypesLoaded = 1;
					onMastersLoad();
				}
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_PRODUCER,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.producerConfig.ERROR_ADD_PRODUCER, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.configName());
					}
					//  
					else{
						params.curPage(1);
					}
					self.cancelConfig();

					showMessageBox(uiConstants.producerConfig.SUCCESS_ADD_PRODUCER);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_PRODUCER,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.producerConfig.ERROR_UPDATE_PRODUCER, "error");
					}
				}
				else{
					showMessageBox(uiConstants.producerConfig.SUCCESS_UPDATE_PRODUCER);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
			else if(reqType === "getAttributeList"){
				self.attributesMasterArr(data.result);
				attributeTypeListLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getProducerParameters"){
				var producerParams = data.result;
				if(slugify(self.producerType()) == "script"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.scriptName(producerParams.scriptName);
					}
					self.scriptSignature(producerParams.scriptSignature);

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);

						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');; 
						
						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');

						$("#parameterType"+ param +" option").filter(function () { return $(this).html() == producerParams.producerParameters[param].parameterType; }).prop('selected', true).trigger('chosen:updated');
						//$("#parameterType"+ param +" option:selected").text(producerParams.producerParameters[param].parameterType);
					}
				}
				else if(slugify(self.producerType()) == "jdbc"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.query(producerParams.query);
						self.jdbcDriver(producerParams.driver);
						self.jdbcUrl(producerParams.url);
						$("#selectQueryResult option").filter(function () { return $(this).html() == producerParams.queryResult; }).prop('selected', true).trigger('chosen:updated');
					}
					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);

						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');; 

						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');
					}
				}
				else if(slugify(self.producerType()) == "wmi"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.shellScriptName(producerParams.scriptName);
						self.shellScriptSignature(producerParams.scriptSignature);
					}

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);
						
						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');; 

						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');

						$("#parameterType"+ param +" option").filter(function () { return $(this).html() == producerParams.producerParameters[param].parameterType; }).prop('selected', true).trigger('chosen:updated');
					}
				}
				else if(slugify(self.producerType()) == "jmx"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.jmxTargetObjName(producerParams.targetObjectName);
						self.jmxUrl(producerParams.jmxUrl);
						$("#selectAttrType").val(producerParams.attributeTypeId).trigger('chosen:updated');
					}

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);
						
						$("#paramName" + param).val(producerParams.producerParameters[param].parameterName);
						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');

						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');
					}
				}
				else if(slugify(self.producerType()) == "jppf"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						$("#selJppfServerType").val(producerParams.serverTypeId).trigger('chosen:updated');
					}

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);
						
						$("#paramName" + param).val(producerParams.producerParameters[param].parameterName);
						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');

						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');
					}
				}
				else if(slugify(self.producerType()) == "http"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.httpdStatusUrl(producerParams.httpUrl);
					}

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);
						
						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');; 
						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');
					}
				}
				else if(slugify(self.producerType()) == "http_json"){
					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.httpJsonStatusUrl(producerParams.httpUrl);
					}

					for(var param in producerParams.producerParameters){
						$("#paramName" + param).val(producerParams.producerParameters[param].parameterName);
						$("#paramValue" + param).val(producerParams.producerParameters[param].parameterValue);
					}
				}
				else if(slugify(self.producerType()) == "was"){
					console.log(producerParams);

					if(self.currentViewIndex() != uiConstants.common.CLONE_VIEW){
						self.wasTargetObjName(producerParams.targetObjectName);
						self.wasModuleName(producerParams.wasModuleName);
					}

					for(var param in producerParams.producerParameters){
						self.attributesArr.push({"attributeId": param, "attributeName": "", "parameterType": ""});
						jQuery(".chosen").chosen({
							search_contains: true	
						});
						allowCustomAttribs(param);
						
						$("#paramName" + param).val(producerParams.producerParameters[param].parameterName);
						//$("#attribList"+ param).val(producerParams.producerParameters[param].parameterId).trigger('chosen:updated');

						$("#attribList" + param + " option:contains(" + producerParams.producerParameters[param].parameterValue + ")").filter(function() {
	    					return $(this).text() === producerParams.producerParameters[param].parameterValue;
						}).attr('selected', 'selected').trigger('chosen:updated');
					}
				}
			}
			else if(reqType === "getProducerTypes"){
				self.producerTypesArr(data.result);
				$("#producerTypeList").trigger('chosen:updated');
				producerTypeListLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getCompTypeVersion"){

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.componentsArr(data.result);
					
				}
				else{
					self.componentsArr(getMasterList(data.result, "componentTypeId", null, false));
				}

				$("#compTypeList").trigger('chosen:updated');

				compTypeVersionLoaded = 1;
				onMastersLoad();
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getProducerTag"){
				showMessageBox(uiConstants.producerConfig.ERROR_GET_PRODUCER_TAGS, "error");
			}
			else if(reqType === "getCommonKpisList"){
				showMessageBox(uiConstants.common.ERROR_GET_KPIS, "error");
			}
			else if(reqType === "getKpiType"){
				showMessageBox(uiConstants.common.ERROR_GET_KPI_TYPES, "error");
			}
			else if(reqType === "getParameterType"){
				showMessageBox(uiConstants.common.ERROR_GET_PARAMETER_TYPES, "error");
			}
			else if(reqType === "getQueryResult"){
				showMessageBox(uiConstants.common.ERROR_GET_QUERY_RESULTS, "error");
			}
			else if(reqType === "getAttributeTypes"){
				showMessageBox(uiConstants.common.ERROR_GET_ATTRIB_TYPES, "error");
			}
			else if(reqType === "getJppfServerTypes"){
				showMessageBox(uiConstants.common.ERROR_GET_SERVER_TYPES, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.producerConfig.ERROR_ADD_PRODUCER, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.producerConfig.ERROR_UPDATE_PRODUCER, "error");
			}
			else if(reqType === "getAttributeList"){
				showMessageBox(uiConstants.componentConfig.ERROR_GET_COMP_ATTRIB_LIST, "error");
			}
			else if(reqType === "getProducerParameters"){
				showMessageBox(uiConstants.producerConfig.ERROR_GET_PRODUCER_PARAMS, "error");
			}
  			else if(reqType === "getProducerTypes"){
  				showMessageBox(uiConstants.common.ERROR_GET_PRODUCER_TYPES, "error");
  			}
  			else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}
		}
	}

	ProducerAddEdit.prototype.dispose = function() { };
	return { viewModel: ProducerAddEdit, template: templateMarkup };
});