define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./kpi-list-view.html','hasher','validator','ui-constants','ui-common','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {

	function KpiListView(params) {
		var self = this;

		var kpiTableHeaders = ["Name","KPI Group","Type","Data Type","Cluster Operation","Category","Created Time","Modified Time","Modified By","Tags","Status"];
		var filterForFirstPage = false;

		var fKpiName=null;
		var fKpiGroupName=null;
		var fKpiType="0";
		var fDataType="";
		var fClusterOperation="";
		var fKpiTag=null;
		var fKpiActiveInactive="1";
		var fCategory="2";
		var fCreatedTime="";
		var fUpdatedTime="";
		var fUpdatedBy="";
		var colSortOrder = 0;
		var colToSort = "kpiName";
		var filterTxtAfterDelete;

		var listData = {};
		var statusChangeList = {};

		this.selectedConfigRows = ko.observableArray();
		this.kpiGridData = ko.observableArray();
		this.configTableHeaderObjKeys = ko.observableArray(["kpiName","kpiGroupName","kpiType","kpiDataType","clusterOperationName","isCustom","createdTime","updatedTime","updatedBy","tags","status"]);
		this.noSortColKeys = ko.observableArray(["tags"]);
		this.gridHeader = ko.observableArray();
		this.gridHeader(kpiTableHeaders);
		this.filterGridHeader = ko.observableArray(["Select","Name","KPI Group","Type","Data Type","Cluster Operation","Category","Created Time","Modified Time","Modified By","Tags","Status"]);

		this.kpiTypeArr=ko.observableArray();
		this.kpiDataTypeArr=ko.observableArray();
		this.kpiClusterOpernArr=ko.observableArray();
		this.kpiUnitsArr=ko.observableArray();
		this.kpiGroupArr=ko.observableArray();

		this.pageSelected = ko.observable("KPI Configuration");
		this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
		this.currentPage = ko.observable(0);
		this.totalRecordsPerPage = ko.observable(this.numOfPagesOption()[0]);
		this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
		//this.currentPage = ko.observable(0);
		this.currentViewIndex = ko.observable(0);
		this.recordsCountLabel = ko.observable("");
		this.kpiTypes = ko.observableArray();

		this.errorMsg = ko.observable("");
		this.isFilterOrList = ko.observable("list");
		this.showListAvailable = ko.observable("");

		this.enableAdd = ko.observable(true);
		this.enableEdit = ko.observable(false);
		this.enableClone = ko.observable(false);
		this.modifiedCols = ko.observableArray([true,true,true]);
		this.selFilterCategory = ko.observable();
		this.filtersAdded = ko.observable("");
		this.filterValueArr = ko.observableArray([]);

		this.renderHandler=function(){
			/*$(".wrapper").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
				this.querySelector("#filterRow").style.transform = translate;
            });*/

			$(window).resize(function(){
			    self.refreshPageLayout();
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$(".addOptionsContainer").offset({ top: $("#divAddOptionsList").position().top + $("#divAddOptionsList").outerHeight() });

			$('.columnsList').checklistbox({
			    data: [
			    	{"name": "Created Time", "id": 1},
			    	{"name": "Modified Time", "id": 2},
			    	{"name": "Modified By", "id": 3}
			    ]
			});
			$('.columnsList .checkList').prop("checked", true);

			$("div").on("click", "#selAllCols", function(e){
				$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
			});

			$("div").on("change", ".columnsList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
				}
	        });

			$("#searchType_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchDataType_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchClusterOperation_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			
			$("#isCustom_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#isCustom_chosen").trigger('chosen:updated');

			$("#searchActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#searchActiveInactive_chosen").trigger('chosen:updated');

			//kpi type with data type 
			requestCall(uiConstants.common.SERVER_IP + "/kpiDataTypesWithUnits", "GET", "", "getKpiTypeList", successCallback, errorCallback);
			//Cluster Operation  
			requestCall(uiConstants.common.SERVER_IP + "/clusterOperation", "GET", "", "getClusterOpernList", successCallback, errorCallback);
			//kpi units
			requestCall(uiConstants.common.SERVER_IP + "/kpiUnits", "GET", "", "getKpiUnitsList", successCallback, errorCallback);
			//kpi group
			requestCall(uiConstants.common.SERVER_IP + "/kpiGroups?isCustom=1&status=2&markInactive=1", "GET", "", "getKpiGroupList", successCallback, errorCallback);
			
			$('#searchType').on('change', function(){
				self.getKpiDataTypelist();
			    self.onFilterAdd(null, ['Data Type:']);
		    	self.onFilterAdd('Type');
			});

			$('#searchDataType').on('change', function(){
		    	self.onFilterAdd('Data Type');
			});

			$('#searchDataType').prop('disabled', true).trigger('chosen:updated');

			$('#searchKpiName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});

			$('#searchKpiGroupName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});
			/*$('#searchType').change(function(event) {
				self.getFilterListData(false);
			});
			$('#searchDataType').change(function(event) {
				self.getFilterListData(false);
			});
			$('#searchClusterOperation').change(function(event) {
				self.getFilterListData(false);
			});
			$('#isCustom').change(function(event) {
				self.getFilterListData(false);
			});
			$('#searchActiveInactive').change(function(event) {
				self.getFilterListData(false);
			});*/
			
			

			/*$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/
			$("#fCategory").val("2").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');

			self.curPage(1);
		}

		self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

		this.onHeaderClick = function(columnNum, columnName){
			if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
				$(".listViewCol").not(".noSort").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

				colSortOrder = colSortOrder ? 0 : 1;
				colToSort = columnName;
				//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
				$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

				self.getListData();
			}
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += "," + tagsData[tag].tagName;
			}

			return tagNames.substring(1);
		}

		this.msgShowListData=function(){
			if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
				self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
			}
			else{
				self.showListAvailable(uiConstants.kpiConfig.KPI_LISTS_NOT_CONFIGURED);
			}
		}

		this.toggleAddOptions = function(){
			$(".addOptionsContainer").toggle();
			document.addEventListener('click', window.outsideClickListener);
		}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.convertToLocalTime = function(getDateTime){
			return window.gmtToLocalDateTime(getDateTime);
		}

		/*Filter search Change events : START*/
		this.getKpiDataTypelist = function(){
			if($('#searchType').val() != ""){
				var dataTypeObj = $.grep(self.kpiTypeArr(), function(e){ return e.kpiTypeId == $('#searchType').val(); });		
				self.kpiDataTypeArr(dataTypeObj[0].dataType);
				$('#searchDataType').prop('disabled', false).trigger('chosen:updated');
			}
			else{
				$("#searchDataType").val("");
				$('#searchDataType').prop('disabled', true).trigger('chosen:updated');
				debugger;
			}
		}
		/*END*/

		/*List view button : START*/
		this.enableDisableAdd = function(length){
			self.enableAdd(length>0?false:true)
		}

		this.enableDisableUpdate = function(length){
			if(length>0)
				self.enableEdit(true);
			else
				self.enableEdit(false);
		};

		this.enableDisableClone = function(length){
			if(length == 1)
				self.enableClone(true);
			else
				self.enableClone(false);
		};

		function resetButtonStates(){
			self.enableDisableAdd(0);
			self.enableDisableUpdate(0);
			self.enableDisableClone(0);
		}
		/*END*/


		/*Filter or List data call : START*/
		this.getListOrFilterData = function(){
			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.getListData = function(){
			console.log("===================First time Kpi filter page===========================");
			self.isFilterOrList("list");
			resetPagination();

			if(window.globalSearchTxt){
				fKpiName = window.globalSearchTxt;
			}
			$('#searchKpiName').val(fKpiName);
			//window.globalSearchTxt = "";
			var filterOptions = "&kpiName=" +fKpiName+"&kpiGroupName=" +fKpiGroupName+"&kpiTypeId="+fKpiType+"&kpiDataType="+fDataType+"&clusterOperation="+fClusterOperation+"&tagName="+fKpiTag+"&status="+fKpiActiveInactive+"&isCustom="+fCategory+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy;
			//console.log(filterOptions);
			requestCall(uiConstants.common.SERVER_IP + "/kpiDetails?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage() + "&offset=" + self.currentPage()+filterOptions, "GET", "", "getListData", successCallback, errorCallback);
		}
		/*END*/


	    /*Pagination Methods : START*/

	    this.totalPages = ko.computed(function() {
			return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
		}, this);

		this.recordsCountLabel = ko.computed(function() {
			var recordsStartCount = 0;
			var recordsEndCount = 0;

			if(self.currentPage() != 0){
				recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
				recordsEndCount = recordsStartCount + (self.kpiGridData().length - 1);
			}
			return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
		}, this);

	    this.prevPage = function(){
			if(self.currentPage()>1)
				self.currentPage(self.currentPage()-1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
		
		this.nextPage = function(){
			if(self.currentPage()<self.totalPages())
				self.currentPage(self.currentPage()+1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.curPage = function(curPage){
			resetButtonStates();
			self.currentPage(curPage);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
		/*END*/



		this.showFilterBox = function(){
			if(!self.filterValueArr().length){
	            for(var headerTxt in self.gridHeader()){
	            	self.filterValueArr.splice(headerTxt, 0, "");
	            }
			}
			$("#filterCriteria").trigger("chosen:updated");
			$("#filterCriteria_chosen").addClass("filterFieldWidth");
			$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
			$("#filterBox").css("display", "block");

			self.onFilterCatChange();

			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.onFilterCatChange = function(){
			if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
				$('#filterCreateModTime').datetimepicker({
					format: "YYYY-MM-DD HH:00",          
					stepping: 1,
					useCurrent: true, 
					//defaultDate: null,
					showTodayButton: false,          
					collapse: true,
					sideBySide: false
				})
				.on('dp.show', function(e){
					if($('#filterCreateModTime input').val() == ""){
						$(this).data("DateTimePicker").date(moment());
					}
				})
				.on('dp.change', function(e){
					if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
						$(this).data("DateTimePicker").maxDate(moment());
						var maxHour = parseInt(moment().format("HH"));
						//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
						//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
					}
					else{
						$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
						//$(this).data("DateTimePicker").disabledHours([]);
					}

					self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();

				})
				.on('dp.hide', function(e){
					self.onFilterAdd();
				});
				$('#filterCreateModTime input').val("");
			}
		}

		this.onFilterAdd = function(parentCategory, removeSubFilterArr){
			if(removeSubFilterArr && removeSubFilterArr.length){
				var filtersAddedArr = self.filtersAdded().split("|");
				var filterFormatted = "";
				for(var i in filtersAddedArr){
					if(removeSubFilterArr.indexOf(filtersAddedArr[i].trim().split(":")[0] + ":") == -1){
						filterFormatted = filterFormatted + "|" + filtersAddedArr[i].trim();
					}
				}

				self.filtersAdded(filterFormatted.substr(1));
			}
			else{
				var filterText = self.getFiltersToAdd(parentCategory || self.selFilterCategory());

				if(!filterText.toLowerCase().startsWith("select")){
					var filterIndex = self.filtersAdded().indexOf((parentCategory || self.selFilterCategory()) + ":");
					var filterExisting = "";
					if(filterIndex != -1){
						var filterExistingEndIndex = self.filtersAdded().indexOf("|", filterIndex);
						if(filterExistingEndIndex != -1){
							filterExisting = self.filtersAdded().substr(filterIndex, (filterExistingEndIndex-filterIndex));
						}
						else{
							filterExisting = self.filtersAdded().substr(filterIndex);
						}

						self.filtersAdded(self.filtersAdded().replace(filterExisting, (parentCategory || self.selFilterCategory()) + ":" + filterText));
					}
					else{
						self.filtersAdded((self.filtersAdded() ? (self.filtersAdded() + "|") : "") + (parentCategory || self.selFilterCategory()) + ":" + filterText);
					}
				}
			}

			$('#filters-tokenfield-typeahead').tokenfield({
				delimiter: ['|']
			});

			var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fKpiName=null;
						fKpiGroupName=null;
						fKpiType="0";
						fDataType="";
						fClusterOperation="";
						fKpiTag=null;
						fKpiActiveInactive="1";
						fCategory="2";
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

		this.getFiltersToAdd = function(category){
			if(category == "Type"){
				return $("#searchType option:selected").text();
			}
			else if(category == "Data Type"){
				return $("#searchDataType option:selected").text();
			}
			else if(category == "Application"){
				return $("#fApplicationsList option:selected").text();
			}
			else if(category == "Cluster Operation"){
				return $("#searchClusterOperation option:selected").text();
			}
			else if(category == "Category"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Standard' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Custom' : 'All');
				return $("#fCategory option:selected").text();
			}
			else if(category == "Status"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
				return $("#fActiveInactive option:selected").text();
			}
			else{
				return self.filterValueArr()[self.gridHeader.indexOf(category)];
			}
		}

		/*Filter part : START*/
		this.resetFilter = function(){
			showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
				if(confirm){
					self.resetFilterConfirmed();
				}
			});
		}

		this.resetFilterConfirmed = function(){
			self.filtersAdded("");
			for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }

			$("#searchType").val("").trigger('chosen:updated');
			$('#searchDataType').val("");
			$('#searchDataType').prop('disabled', true).trigger('chosen:updated');
			$("#searchClusterOperation").val("").trigger('chosen:updated');
			$("#searchActiveInactive").val("1").trigger('chosen:updated');
			$("#isCustom").val("2").trigger('chosen:updated');
			self.errorMsg("");
			self.currentPage(1);
			self.isFilterOrList("list");

			fKpiName=null;
			fKpiGroupName=null;
			fKpiType="0";
			fDataType="";
			fClusterOperation="";
			fKpiTag=null;
			fKpiActiveInactive="1";
			fCategory="2";
			fCreatedTime="";
			fUpdatedTime="";
			fUpdatedBy="";

			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
			self.selFilterCategory("Select");
			
			var filterOptions = "&kpiName=" +fKpiName+"&kpiGroupName=" +fKpiGroupName+"&kpiTypeId="+fKpiType+"&kpiDataType="+fDataType+"&clusterOperation="+fClusterOperation+"&tagName="+fKpiTag+"&status="+fKpiActiveInactive+"&isCustom="+fCategory+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy;
			if(uiConstants.common.DEBUG_MODE)console.log(filterOptions);
			requestCall(uiConstants.common.SERVER_IP + "/kpiDetails?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1"+filterOptions, "GET", "", "getListData", successCallback, errorCallback);			
		}

		self.errorMsg.subscribe(function(errorMessage) {
        	if(errorMessage != ""){
	        	scrollToPos(0, 300);
	        }
		});

		this.getFilterListData = function(filterApplied){
			self.isFilterOrList("filter");
			filterForFirstPage = filterApplied;
			setFiltersToVariables();
			var resValue = true;
			this.errorMsg("");

			/*if($("#searchKpiName").val() != "" &&  $("#searchKpiName").val().length < 2){
				self.errorMsg("KPI "+uiConstants.common.NAME_MIN_LENGTH_ERROR);
				resValue = false;
			}
			else if($("#searchKpiName").val() != "" && $("#searchKpiName").val().length > 45){
				self.errorMsg("KPI "+uiConstants.common.NAME_MAX_LENGTH_ERROR);
				resValue = false;
			}*/


			/*else if(fKpiName != "" && fKpiName != null){
				resValue=nameValidation(fKpiName);
				if(resValue == 0){
					self.errorMsg(uiConstants.kpiProducerMap.KPI_NAME_INVALID_ERROR);
					
					resValue = false;
				}
			}
			else if(fKpiTag != "" && fKpiTag != null){
				resValue=tagValidation(fKpiTag);
				if(resValue == 0){
					self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
					resValue = false;
				}
			}*/

			resetPagination();
	
			if(resValue){
				var filterOptions = "&kpiName=" +fKpiName+"&kpiGroupName=" +fKpiGroupName+"&kpiTypeId="+fKpiType+"&kpiDataType="+fDataType+"&clusterOperation="+fClusterOperation+"&tagName="+fKpiTag+"&status="+fKpiActiveInactive+"&isCustom="+fCategory+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy;
				if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterOptions);
				if(filterApplied){
					$("#btnApplyFilter").css("display", "none");
					$("#filterOptionsBox").css("display", "none");
					$("#filterOptionsDispBtn").css("display", "");

					requestCall(uiConstants.common.SERVER_IP + "/kpiDetails?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1"+filterOptions , "GET", "", "getListData", successCallback, errorCallback);
				}
				else
					requestCall(uiConstants.common.SERVER_IP + "/kpiDetails?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +filterOptions , "GET", "", "getListData", successCallback, errorCallback);			
			}
		}

		this.onFilterOptionsDispClick = function(){
			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			
			self.refreshPageLayout();
		}

		this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

		function resetPagination(){
			if(self.totalPages() != 0){
				if(typeof self.currentPage() == "string"){
					self.currentPage(parseInt(self.currentPage()));
				}

				if(self.currentPage() == "" || isNaN(self.currentPage()))
					self.currentPage(1);
				else if(self.currentPage()>self.totalPages())
					self.currentPage(self.totalPages());
				else if(self.currentPage()<1)
					self.currentPage(1);
			}
		}

		function setFiltersToVariables(){
			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];

			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");

				if(filterCategoryArr[0] == "Name"){
					fKpiName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "KPI Group"){
					fKpiGroupName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Type"){
					fKpiType = $.grep(self.kpiTypeArr(), function(e){
						return e.kpiType == filterCategoryArr[1];
					})[0].kpiTypeId;
				}
				else if(filterCategoryArr[0] == "Cluster Operation"){
					fClusterOperation = $.grep(self.kpiClusterOpernArr(), function(e){
						return e.clusterOperationName == filterCategoryArr[1];
					})[0].clusterOperationId;
				}
				else if(filterCategoryArr[0] == "Category"){
					fCategory = filterCategoryArr[1] == 'Standard' ? 0 : (filterCategoryArr[1] == 'Custom' ? 1 : 2);
				}
				else if(filterCategoryArr[0] == "Created Time"){
					fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
				}
				else if(filterCategoryArr[0] == "Modified Time"){
					fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
				}
				else if(filterCategoryArr[0] == "Modified By"){
					fUpdatedBy = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Tags"){
					fTags = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Status"){
					fKpiActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
				}
			}
		}

		function getTypeIdByName(type){
			if(type == "Select" )
				return 0;
			for(appType in self.kpiTypes()){
				if( self.kpiTypes().name == type.trim()){
					return self.kpiTypes().id;
				}
			}
			return 0;
		}


		function getDataTypeIdByName(dataType){
			if(type == "All" || type == "Select" )
				return 0;
			for(appType in self.kpiTypes()){
				if( self.kpiTypes().name == type.trim()){
					return self.kpiTypes().id;
				}
			}
			return 0;
		}

		/*Filter part END*/


		/*List table view operations : START*/
		$('#listKpiConfiguration table').on('click', '.chkboxCol', function(e){
			self.handleChkClick();
		});

	    $('#listgrid tbody').on('dblclick', 'tr', function(e){
	    	if(e.target.parentNode.rowIndex != undefined)
	    		self.viewConfig(self.kpiGridData()[e.target.parentNode.rowIndex - 3]);
		});

		self.onNameClick = function(){
			self.viewConfig($(this)[0]);
		}

		self.handleChkClick = function(){
			var length = $('.chkboxCol:checked').length;
			self.enableDisableAdd(length);
			self.enableDisableUpdate(length);
			self.enableDisableClone(length);
			if (length == self.kpiGridData().length) {
				$("#chkboxHeader").prop("checked",true);
			}
			else {
				$("#chkboxHeader").prop("checked",false);
			}
		}

		/*END*/

		/*Switch Views : START*/
		this.editConfig = function(){
			getSelectedConfigRows(null);

			if(self.checkForSameType() == "type"){
				showMessageBox(uiConstants.kpiConfig.ERROR_MSG_SAME_TYPE, "error");
			}
			else if(self.checkForSameType() == "category"){
				showMessageBox(uiConstants.kpiConfig.ERROR_MSG_SAME_CATEGORY, "error");
			}
			else if(self.checkForSameType() == "datatype"){
				showMessageBox(uiConstants.kpiConfig.ERROR_MSG_SAME_DATATYPE_FORTYPE, "error");
			}
			else{
				self.switchView(uiConstants.common.EDIT_VIEW);
			}
		}

		this.cloneConfig = function(){
			getSelectedConfigRows(null);
			if(self.selectedConfigRows()[0].status == 0){
				showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "KPI"), "error");
			}
			else{
				self.switchView(uiConstants.common.CLONE_VIEW);
			}
		}

		this.viewConfig = function(viewObj){
			getSelectedConfigRows(viewObj);
			self.switchView(uiConstants.common.READ_VIEW);
		}

		this.addMultipleConfig = function(){
			getSelectedConfigRows(null);
			self.switchView(uiConstants.common.ADD_MULTIPLE_VIEW);
		}

		this.checkForSameType = function(){
			var kpiCommonTypeArr = [];
			var kpiCommonCategoryArr = [];
			var kpicommonDataTypeArr=[];
			var kpiObj ;

			kpiObj = self.selectedConfigRows();
			kpiCommonTypeArr.push(kpiObj[0].kpiTypeId);	
			kpiCommonCategoryArr.push(kpiObj[0].isCustom);
			kpicommonDataTypeArr.push(kpiObj[0].kpiDataTypeId);

			for(obj in kpiObj){
				if(kpiCommonTypeArr.length<=1){
					if(kpiCommonTypeArr.indexOf(kpiObj[obj].kpiTypeId) == -1){
						kpiCommonTypeArr.push(kpiObj[obj].kpiTypeId);
					}
				}

				if(kpiCommonCategoryArr.length<=1){
					if(kpiCommonCategoryArr.indexOf(kpiObj[obj].isCustom) == -1){
						kpiCommonCategoryArr.push(kpiObj[obj].isCustom);
					}
				}

				if(kpicommonDataTypeArr.length<=1){
					if(kpicommonDataTypeArr.indexOf(kpiObj[obj].kpiDataTypeId) == -1){
						kpicommonDataTypeArr.push(kpiObj[obj].kpiDataTypeId);
					}
				}

			}
			if(kpiCommonTypeArr.length != 1)
				return "type";
			else{
				
					if(kpiCommonCategoryArr.length != 1){
						return "category";
					}else{	
						if(kpiCommonTypeArr.length == 1 && kpicommonDataTypeArr.length!=1){
							return "datatype";
						}
						else{
							return "none";
						}
					}
			}
		}

		this.switchView = function (viewIndex){
			//validation to switch the page for type and category
			/*if(self.selectedConfigRows().length>1){
				if(self.checkForSameType() == "type"){
					alert("Please select same type, for KPIs edit operation!...");
				}
				else if(self.checkForSameType() == "category"){
					alert("Please select same category, for KPIs edit operation!...");
				}
				else{
					self.currentViewIndex(viewIndex);
				}
			}
			else{*/
				self.currentViewIndex(viewIndex);
			//}
				
				if(self.currentViewIndex() == uiConstants.common.LIST_VIEW){
					self.pageSelected("KPI Configuration");
				}

				else if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
					self.pageSelected("Add KPI");
				}

				else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					if(self.selectedConfigRows().length>1){
						self.pageSelected("Edit KPIs");
					}
					else{
						self.pageSelected("Edit KPI");
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					self.pageSelected("Clone KPI");
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.pageSelected("View KPI");
				}
				else if(self.currentViewIndex() == uiConstants.common.ADD_MULTIPLE_VIEW){
					self.pageSelected("Add Multiple KPIs");
				}

		}
		

		function getSelectedConfigRows(viewObj){
			self.selectedConfigRows([]);

			if(viewObj != null){
				if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
				self.selectedConfigRows.push(viewObj);
			}
			else{
				for(objData in self.kpiGridData()){
					if(uiConstants.common.DEBUG_MODE)console.log("=================================Selected KPI=========================");
					if(uiConstants.common.DEBUG_MODE)console.log(self.kpiGridData()[objData]);

					if(self.kpiGridData()[objData].isSelected){
						self.selectedConfigRows.push(self.kpiGridData()[objData]);
					}
				}			
			}
		}
		/*END*/

		/*Success and error handler : START*/
		function successCallback(data, reqType) {
				if(uiConstants.common.DEBUG_MODE)console.log("KPI List View Response---->");
				if(uiConstants.common.DEBUG_MODE)console.log(data);

				if(reqType === "getKpiTypeList"){
					self.kpiTypeArr.removeAll();		
					self.kpiTypeArr.push.apply(self.kpiTypeArr,data.result);

					$("#searchType").trigger('chosen:updated');
				}
				else if(reqType == "getClusterOpernList"){
					self.kpiClusterOpernArr.removeAll();
					self.kpiClusterOpernArr.push.apply(self.kpiClusterOpernArr,data.result);

					$("#searchClusterOperation").trigger('chosen:updated');
				}
				else if(reqType == "getKpiUnitsList"){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					self.kpiUnitsArr(data.result);
				}
				else if(reqType == "getKpiGroupList"){
					self.kpiGroupArr(data.result);
				}
				else if(reqType === "getListData"){
					$("#chkboxHeader").prop("checked",false);
					resetButtonStates();

					if(data.responseStatus == "success"){
						listData = data;
						self.totalRecords(listData.totalRecords);
						//self.kpiGridData(listData.result);

						$("#listgrid #gridDataBody").empty();
				 		$("#listgrid").trigger("update");
						self.kpiGridData(listData.result);

						var initialSortColumn = 1;
						var sortOrder = 0; //0=asc; 1=desc
						if(!$("#listgrid").hasClass("tablesorter")){
							/*if(!self.kpiGridData().length){
								self.enableFilter(false);
							}*/

							if (self.kpiGridData().length && !$("#pageNum").hasClass("select2-hidden-accessible")){
								debugger;
								$("#pageNum").select2();

								$("#pageNum").select2("open");
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
								$("#select2-pageNum-container").parent().css({
									"border": "none",
									"outline": "none"
								});

								$("#pageNum").parent().children("span").css("width", "36px");
								//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
								$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
								$("#select2-pageNum-container").css({
										"font-weight": "bold",
										"color": "#218DC0",
										"padding-left": "4px"
									});

								$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
								$(".pageNumDropDown .select2-search").css("display", "none");
								$("#select2-pageNum-results").css("overflow-x", "hidden");
								$("#pageNum").select2("close");
							}

							$("#listgrid").addClass("tablesorter")
							/*$("#listgrid").tablesorter({
								//ignoreCase : false,
								cancelSelection: false,
								headers: { 0: { sorter: false}, 10: { sorter: false} },

								widgetOptions: {
							      sortTbody_primaryRow : '.main',
							      sortTbody_sortRows   : false,
							      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
							    }
								//sortList: [[initialSortColumn, sortOrder]]
							});

							$("#listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

				            var $tab = $('#listgrid');
							$tab.floatThead({
								scrollContainer: function($table){
									return $table.closest('.wrapper');
								}
							});

							$('#listKpiConfiguration table').on('click', '#chkboxHeader', function(e){
								if (this.checked == false) {
									$(".chkboxCol").prop("checked",false);
									for(objData in self.kpiGridData()){
										self.kpiGridData()[objData].isSelected=false;
									}
								}
								else {
									$(".chkboxCol").prop("checked",true);
									
									for(objData in self.kpiGridData()){
										self.kpiGridData()[objData].isSelected=true;
									}
								}
								var length = $('.chkboxCol:checked').length;
								self.enableDisableAdd(length);
								self.enableDisableUpdate(length);
								self.enableDisableClone(length);
							});
						}
						/*else{
							$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
					 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
							
						}*/

						/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
					 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
		*/

						$("#listgrid").trigger("update");
						self.refreshPageLayout();

						if((self.currentPage() == 0 || filterForFirstPage) && self.kpiGridData().length>0){
							self.currentPage(1);
							filterForFirstPage = false;
						}
						self.msgShowListData();

					}else{
						showMessageBox(data.message, "error");
						self.showListAvailable("");							
					}

					var $tab = $('#listgrid');
					$tab.floatThead('reflow');
				}

		}

		function errorCallback(reqType) {
			if(reqType === "getListData"){
  				showMessageBox(uiConstants.common.ERROR_GET_KPIS, "error");
  			}
  			else if(reqType === "getKpiTypeList"){
				showMessageBox(uiConstants.kpiConfig.ERROR_GET_KPITYPE_LISTS, "error");
			}
			else if(reqType == "getClusterOpernList"){
				showMessageBox(uiConstants.kpiConfig.ERROR_GET_CLUSTEROPERN_LISTS, "error");
			}
			else if(reqType == "getKpiUnitsList"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_GET_KPI_UNITS);
			}
			else if(reqType == "getKpiGroupList"){
				showMessageBox(uiConstants.kpiConfig.ERROR_GET_KPI_GROUP_LISTS, "error");
			}
		}
		/*END*/

	}
	KpiListView.prototype.dispose = function() { };
	return { viewModel: KpiListView, template: templateMarkup };
});