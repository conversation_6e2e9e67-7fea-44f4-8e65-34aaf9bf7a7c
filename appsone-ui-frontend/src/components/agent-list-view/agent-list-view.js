define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./agent-list-view.html','hasher','validator','ui-constants','ui-common','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {

	function AgentListGridView(params) {
		var self = this;
		var configTableHeaders = ["Name","Type","Host","Mode","Applications","Created Time","Modified Time","Modified By","Tags","Status","Download"];
		var listData = {};
		var filterForFirstPage = false;
		var fConfigName=null;
		var fConfigActiveInactive="1";
		var fTags=null;
		var fHost=null;
		var fAgentTypeId=0;
		var fAgentMode=null;
		var fApplicationId=0;
		var fCreatedTime="";
		var fUpdatedTime="";
		var fUpdatedBy="";
		var colSortOrder = 0;
		var colToSort = "agentName";
		var filterTxtAfterDelete;
		var fHostReloaded = 0;

		this.gridHeader = ko.observableArray();
		this.filterGridHeader = ko.observableArray(["Select","Name","Type","Host","Mode","Applications","Created Time","Modified Time","Modified By","Tags","Status"]);
		this.gridData = ko.observableArray();
		this.configTableHeaderObjKeys = ko.observableArray(["agentName","agentType","host","agentMode","applications","createdTime","updatedTime","updatedBy","tags","status","download"]);
		this.noSortColKeys = ko.observableArray(["applications","tags","download"]);
		this.currentPage = ko.observable(0);
		this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
		this.totalRecordsPerPage = ko.observable();
		this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
		this.enableAdd = ko.observable(true);
		this.enableEdit = ko.observable(false);
		this.enableClone = ko.observable(false);
		this.currentViewIndex = ko.observable(uiConstants.common.LIST_VIEW);
		this.selectedConfigRows = ko.observableArray();
		this.gridHeader(configTableHeaders);
		this.errorMsg = ko.observable("");
		this.isFilterOrList = ko.observable("list");
		this.recordsCountLabel = ko.observable("");
		this.pageSelected = ko.observable("Agent Configuration");
		this.hostsArr = ko.observableArray();
		this.applicationsArr = ko.observableArray();
		this.agentTypesArr = ko.observableArray();
		this.agentModesArr = ko.observableArray();
		this.enableFilter = ko.observable(true);
		this.showListAvailable = ko.observable("");
		this.componentBindingType = ko.observable("agent-wizard-modal");
		this.isCloseRequired = ko.observable(false);
		this.modalHeaderTitle = ko.observable(uiConstants.common.WIZARD_POPUP_TITLE);
		this.componentInstanceIDs = params && params.componentInstanceIDs || ko.observableArray();
		this.mode = params && params.mode || '';
		this.unmappedCompInstancesArr = ko.observable();
		//this.addedUpdatedHost = ko.observable("");
		this.addUpdateFlag = ko.observable(false);
		this.modifiedCols = ko.observableArray([true,true,true]);
		this.selFilterCategory = ko.observable();
		this.filtersAdded = ko.observable("");
		this.filterValueArr = ko.observableArray([]);

		if(self.mode == "wizard"){
			params.agentListViewModel(self);
			this.panelTitle = params.panelTitle;
			this.applicationId=params.applicationId;
		}

		
		this.renderHandler=function(){
			/*$(".wrapper").scroll(function(){
			   var translate = "translate(0,"+this.scrollTop+"px)";
			   this.querySelector("thead").style.transform = translate;
			   this.querySelector("#filterRow").style.transform = translate;
			});*/

            
			$(window).resize(function(){
			    self.refreshPageLayout();
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$('.columnsList').checklistbox({
			    data: [
			    	{"name": "Created Time", "id": 1},
			    	{"name": "Modified Time", "id": 2},
			    	{"name": "Modified By", "id": 3}
			    ]
			});
			$('.columnsList .checkList').prop("checked", true);

			$("div").on("click", "#selAllCols", function(e){
				$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
			});

			$("div").on("change", ".columnsList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
				}
	        });

			//$("#fActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fActiveInactive").trigger('chosen:updated');

			localStorage.currentViewIndex = uiConstants.common.LIST_VIEW;
			configType = localStorage.configType = "agentConfig";

			/*

			$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/

			$("#agentListConfigDetails #listgrid tbody").on('click', '#btnDownloadProperties', function(e){
			 	var rowIndex = $(this).closest('tr').get(0).rowIndex-1;
			 	downloadAgentPropertiesConfig(self.gridData()[rowIndex]);
			});

			$("#agentListConfigDetails #listgrid tbody").on('click', '#btnDownloadJson', function(e){
			 	var rowIndex = $(this).closest('tr').get(0).rowIndex-1;
			 	self.downloadJsonConfig(rowIndex);
			});
			
			$("#fHostsList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});

			self.LoadAgentListView();

			$('#fConfigName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});

			$("#fActiveInactive").val("1").trigger('chosen:updated');
		}
		requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getApplications", successCallback, errorCallback);

		self.addUpdateFlag.subscribe(function(addUpdate) {
        	if(addUpdate){
				requestCall(uiConstants.common.SERVER_IP + "/agent/host?status=2&markInactive=1", "GET", "", "getHosts", successCallback, errorCallback);
	        }
		});

		this.onHeaderClick = function(columnNum, columnName){
			if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
				$(".listViewCol").not(".noSort").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

				colSortOrder = colSortOrder ? 0 : 1;
				colToSort = columnName;
				//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
				$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

				self.getListData();
			}
		}

		this.getApplicationNames = function(applicationsData){
			var applicationNames = "";
			for(app in applicationsData){
				applicationNames += ", " + applicationsData[app].applicationName;
			}

			return applicationNames.substring(2);
		}

		this.getUnmappedInstanceDetails=function(){
			if(self.mode === "wizard" && self.panelTitle === "Edit"){
				requestCall(uiConstants.common.SERVER_IP + "/wizard/applicationMappedInstanceIds/"+self.applicationId(), "GET", "", "getUnmappedInstanceDetails", successCallback, errorCallback);
			}
		}
		
		this.msgShowListData=function(){
			if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
				self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
			}
			else{
				self.showListAvailable(uiConstants.agentConfig.AGENT_LISTS_NOT_CONFIGURED);
			}
		}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.convertToLocalTime = function(getDateTime){
			return window.gmtToLocalDateTime(getDateTime);
		}

		this.totalPages = ko.computed(function() {
			return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
		}, this);

		this.recordsCountLabel = ko.computed(function() {
			var recordsStartCount = 0;
			var recordsEndCount = 0;

			if(self.currentPage() != 0){
				recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
				recordsEndCount = recordsStartCount + (self.gridData().length - 1);
			}
			return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
		}, this);

		this.enableDisableAdd = function(length){
			self.enableAdd(length>0?false:true)
		}

		this.enableDisableUpdate = function(length){
			if(length == 1)
				self.enableEdit(true);
			else
				self.enableEdit(false);
		};

		this.enableDisableClone = function(length){
			if(length == 1)
				self.enableClone(true);
			else
				self.enableClone(false);
		};

		this.getListOrFilterData = function(){
			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.prevPage = function(){
			if(self.currentPage()>1)
				self.currentPage(self.currentPage()-1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
	
		this.nextPage = function(){
			if(self.currentPage()<self.totalPages())
				self.currentPage(self.currentPage()+1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.curPage = function(curPage){
			resetButtonStates();
			self.currentPage(curPage);
			debugger;

			if(!self.addUpdateFlag()){
				if(self.isFilterOrList() == "list")
				self.getListData();
				else{
					self.getFilterListData(false);
				}
			}
		}

		this.showFilterBox = function(){
			if(!self.filterValueArr().length){
	            for(var headerTxt in self.gridHeader()){
	            	self.filterValueArr.splice(headerTxt, 0, "");
	            }
			}
			$("#filterCriteria").trigger("chosen:updated");
			$("#filterCriteria_chosen").addClass("filterFieldWidth");
			$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
			$("#filterBox").css("display", "block");

			self.onFilterCatChange();

			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.onFilterCatChange = function(){
			if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
				$('#filterCreateModTime').datetimepicker({
					format: "YYYY-MM-DD HH:00",          
					stepping: 1,
					useCurrent: true, 
					//defaultDate: null,
					showTodayButton: false,          
					collapse: true,
					sideBySide: false
				})
				.on('dp.show', function(e){
					if($('#filterCreateModTime input').val() == ""){
						$(this).data("DateTimePicker").date(moment());
					}
				})
				.on('dp.change', function(e){
					if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
						$(this).data("DateTimePicker").maxDate(moment());
						var maxHour = parseInt(moment().format("HH"));
						//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
						//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
					}
					else{
						$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
						//$(this).data("DateTimePicker").disabledHours([]);
					}

					self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();

				})
				.on('dp.hide', function(e){
					self.onFilterAdd();
				});
				$('#filterCreateModTime input').val("");
			}
		}

		this.onFilterAdd = function(){
			if(self.filtersAdded() == ""){
				self.filtersAdded($("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
				$('#filters-tokenfield-typeahead').tokenfield({
					delimiter: ['|']
				});
			}


			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];
			var filterCategoryFound = 0;
			
			self.filtersAdded("");
			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");
				
				if(filterCategoryArr[0].trim() == self.selFilterCategory()){
					filterCategoryArr[1] = self.getFiltersToAdd(self.selFilterCategory());
					filterCategoryFound = 1;
				}

				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCategoryArr[0].trim()+":"+filterCategoryArr[1]);

				if(filters == "0"){
					$('#filters-tokenfield-typeahead').tokenfield({
						delimiter: ['|']
					});
				}
			}

			if(filterCategoryFound == 0){
				self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + $("#filterCriteria option:selected").text() + ":" + self.getFiltersToAdd(self.selFilterCategory()));
			}
			filterCategoryFound = 0;

			var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fConfigName=null;
						fConfigActiveInactive="1";
						fTags=null;
						fHost=null;
						fAgentTypeId=0;
						fAgentMode=null;
						fApplicationId=0;
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

		this.getFiltersToAdd = function(category){
			if(category == "Type"){
				return $("#fAgentType option:selected").text();
			}
			else if(category == "Host"){
				return $("#fHostsList option:selected").text();
			}
			else if(category == "Mode"){
				return $("#fAgentMode option:selected").text();
			}
			else if(category == "Applications"){
				return $("#fApplicationList option:selected").text();
			}
			else if(category == "Status"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
				return $("#fActiveInactive option:selected").text();
			}
			else{
				return self.filterValueArr()[self.gridHeader.indexOf(category)];
			}
		}

		this.resetFilter = function(){
			showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
				if(confirm){
					self.resetFilterConfirmed();
				}
    		});
		}

		this.resetFilterConfirmed = function(){
			self.filtersAdded("");
			for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }

			$('#fAgentType').val("").trigger("chosen:updated");
			$("#fHostsList").val("0").trigger('chosen:updated');
			$("#fAgentMode option").filter(function () { return $(this).html() == uiConstants.agentConfig.SELECT_AGENT_MODE; }).prop('selected', true).trigger('chosen:updated');
			$("#fApplicationList").val("0").trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');
			$('#filterCreateModTime').val("");
			self.errorMsg("");
			self.currentPage(1);
			self.isFilterOrList("list");

			fConfigName=null;
			fConfigActiveInactive="1";
			fTags=null;
			fHost=null;
			fAgentTypeId=0;
			fAgentMode=null;
			fApplicationId=0;
			fCreatedTime="";
			fUpdatedTime="";
			fUpdatedBy="";

			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
			self.selFilterCategory("Select");

			requestCall(uiConstants.common.SERVER_IP + "/agent?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&agentName=" +null+"&status="+fConfigActiveInactive+"&tags="+fTags+"&host="+fHost+"&agentTypeId="+fAgentTypeId+"&agentMode="+fAgentMode+"&applicationId="+fApplicationId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
		}

		/*this.getClusterNames = function(clustersData){
			var clusterNames = "";
			for(cluster in clustersData){
				clusterNames += ", " + clustersData[cluster].clusterName;
			}

			return clusterNames.substring(2);
		}*/

		this.downloadAllPsaJsonConfig = function(rowIndex){
			//var agentUniqueId = self.gridData()[rowIndex].identifier;
			var psaAgentTypeId = $.grep(self.agentTypesArr(), function(e){ return slugify(e.agentType).indexOf("psagent") != -1; })[0]["agentTypeId"];
			requestCall(uiConstants.common.SERVER_IP + "/agent?limit-0&offset=0&agentTypeId="+psaAgentTypeId+"&download=1", "GET", "", "getAllJsonConfig", successCallback, errorCallback);
		}

		this.downloadJsonConfig = function(rowIndex){
			var agentUniqueId = self.gridData()[rowIndex].identifier;
			requestCall(uiConstants.common.SERVER_IP + "/agents/identifier/" + agentUniqueId, "GET", "", "getJsonConfig", successCallback, errorCallback);
		}

		this.getApplicationNames = function(applicationsData){
			var applicationNames = "";
			for(app in applicationsData){
				applicationNames += ", " + applicationsData[app].applicationName;
			}

			return applicationNames.substring(2);
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += ", " + tagsData[tag].tagName;
			}

			return tagNames.substring(2);
		}

		self.errorMsg.subscribe(function(errorMessage) {
        	if(errorMessage != ""){
	        	scrollToPos(0, 300);
	        }
		});

		this.getFilterListData = function(filterApplied){
			self.isFilterOrList("filter");
			filterForFirstPage = filterApplied;
			setFiltersToVariables();
			var resValue = true;
			var pageOffset = self.currentPage();

			this.errorMsg("");

			/*if($("#fConfigName").val() != "" && $("#fConfigName").val().length < 2){
				self.errorMsg(uiConstants.agentConfig.AGENT_NAME_MIN_LENGTH_ERROR);
				resValue = false;
			}
			else if($("#fConfigName").val() != "" &&  $("#fConfigName").val().length > 45){
				self.errorMsg(uiConstants.agentConfig.AGENT_NAME_MAX_LENGTH_ERROR);
				resValue = false;
			}*/
			/*else if(fConfigName != "" && fConfigName != null){
				resValue=nameValidation(fConfigName);
				if(resValue == 0){
					self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
					
					resValue = false;
				}
			}*/

			resetPagination();
	
			if(resValue){

				if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterApplied);
				if(filterApplied){
					pageOffset = 1;


				/*var selectedHost = uiConstants.agentConfig.SELECT_HOST;

				if(selectedHost != uiConstants.agentConfig.SELECT_HOST){
					
					$("#fHostsList option:contains(" + selectedHost + ")").attr('selected', 'selected').trigger('chosen:updated');

				}*/



				}

				$("#btnApplyFilter").css("display", "none");
				$("#filterOptionsBox").css("display", "none");
				$("#filterOptionsDispBtn").css("display", "");
				requestCall(uiConstants.common.SERVER_IP + "/agent?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ pageOffset +"&agentName=" +fConfigName+"&status="+fConfigActiveInactive+"&tags="+fTags+"&host="+fHost+"&agentTypeId="+fAgentTypeId+"&agentMode="+fAgentMode+"&applicationId="+fApplicationId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
			}
		}

		this.onFilterOptionsDispClick = function(){
			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");

			self.refreshPageLayout();
		}

		this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

		self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

		this.getListData = function(){
			/*if(self.isFilterOrList() == "filter"){
				requestCall(uiConstants.common.SERVER_IP + "/agent/host", "GET", "", "getHosts", successCallback, errorCallback);
			}*/
			self.isFilterOrList("list");
			resetPagination();

			if(window.globalSearchTxt){
				fConfigName = window.globalSearchTxt;
			}
			$('#fConfigName').val(fConfigName);
			//window.globalSearchTxt = "";
			requestCall(uiConstants.common.SERVER_IP + "/agent?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&agentName=" +fConfigName+"&status="+fConfigActiveInactive+"&tags="+fTags+"&host="+fHost+"&agentTypeId="+fAgentTypeId+"&agentMode="+fAgentMode+"&applicationId="+fApplicationId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57f368960f0000ac0b06050c?callback=?", "GET", "", "getListData", successCallback, errorCallback);
		}

		this.switchView = function (viewIndex){
			localStorage.currentViewIndex = viewIndex;
			self.currentViewIndex(viewIndex);

			if(self.currentViewIndex() == uiConstants.common.LIST_VIEW){
				self.pageSelected("Agent Configuration");
				uicommon.postbox.publish(uiConstants.common.LIST_VIEW,"changeViewToList");
			}

			else if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				self.pageSelected("Add Agent");
				uicommon.postbox.publish("checking the current View","checkCurrentView");
				uicommon.postbox.publish("View is now add","ViewIsChangedToAddEdit");
				
			}

			else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
				self.pageSelected("Edit Agent");
				uicommon.postbox.publish("checking the current View","checkCurrentView");
				uicommon.postbox.publish("View is now add","ViewIsChangedToAddEdit");
			}

			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.pageSelected("Clone Agent");
				uicommon.postbox.publish("checking the current View","checkCurrentView");
				uicommon.postbox.publish("View is now add","ViewIsChangedToAddEdit");
			}

			else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				self.pageSelected("Agent");
				uicommon.postbox.publish("checking the current View","checkCurrentView");
				uicommon.postbox.publish("View is now readonly","ViewIsChangedToAddEdit");
			}
		}

		this.editConfig = function(){
			getSelectedConfigRows(null);
			self.switchView(uiConstants.common.EDIT_VIEW);
		}

		this.cloneConfig = function(){
			getSelectedConfigRows(null);

			if(self.selectedConfigRows()[0].status == 0){
				showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "Agent"), "error");
			}
			else{
				self.switchView(uiConstants.common.CLONE_VIEW);
			}
		}

		this.viewConfig = function(viewObj){
			getSelectedConfigRows(viewObj);
			self.switchView(uiConstants.common.READ_VIEW);
		}

		function getSelectedConfigRows(viewObj){
			self.selectedConfigRows([]);

			if(viewObj != null){
				if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
				self.selectedConfigRows.push(viewObj);
			}
			else{
				for(objData in self.gridData()){
					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(self.gridData()[objData]));

					if(self.gridData()[objData].isSelected){
						self.selectedConfigRows.push(self.gridData()[objData]);
						break;
					}
				}			
			}

			localStorage.selectedConfigRows = JSON.stringify(self.selectedConfigRows());
		}

		$('#agentListConfigDetails table').on('click', '.chkboxCol', function(e){
			var rowIndex = $(this).parent().parent().index();
			var chkState = $(this).prop("checked");
			$(".chkboxCol").prop("checked",false);
			$(this).prop("checked",chkState);

			for(objData in self.gridData()){
				self.gridData()[objData].isSelected=false;
			}

			self.gridData()[rowIndex].isSelected = chkState;
			self.handleChkClick();
		});

	    $('#agentListConfigDetails #listgrid tbody').on('dblclick', 'tr', function(e){
	    	if(e.target.parentNode.rowIndex != undefined)
	    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
		});

		self.onNameClick = function(){
			self.viewConfig($(this)[0]);
		}

		self.handleChkClick = function(){
			var length = $('.chkboxCol:checked').length;
			self.enableDisableAdd(length);
			self.enableDisableUpdate(length);
			self.enableDisableClone(length);
		}

	    function resetButtonStates(){
			self.enableDisableAdd(0);
			self.enableDisableUpdate(0);
			self.enableDisableClone(0);
		}

		function setFiltersToVariables(){
			/*fConfigName = ($('#fConfigName').val() == "")?null:$('#fConfigName').val();
			fTags = ($('#fTags').val() == "")?null:$('#fTags').val();

			var selHostArr=[];
			var selHostStr=null;

			if($("#fHostsList_chosen span")[0].innerHTML != uiConstants.agentConfig.SELECT_HOST && $("#fHostsList_chosen span")[0].innerHTML != uiConstants.agentConfig.ENTER_SELECT_HOST){
				selHostArr = $.grep(self.hostsArr(), function(e){ return e == $("#fHostsList_chosen span")[0].innerHTML; });
				if(selHostArr.length){
					selHostStr = selHostArr[0].split("(").length >1 ? selHostArr[0].split("(")[1].split(")")[0].trim() : selHostArr[0];
				}
				else{
					selHostStr = $("#fHostsList_chosen span")[0].innerHTML.trim();
				}
			}

			fHost = selHostStr;//$('#fHostsList option:selected').text() == uiConstants.agentConfig.SELECT_HOST ? 0 : $('#fHostsList').val();
			fAgentTypeId = $('#fAgentType option:selected').text() == uiConstants.agentConfig.SELECT_AGENT_TYPE ? 0 : $('#fAgentType').val();
			fAgentMode = $('#fAgentMode option:selected').text() == uiConstants.agentConfig.SELECT_AGENT_MODE ? null : $('#fAgentMode option:selected').text();
			fApplicationId = $("#fApplicationList").val();
			fConfigActiveInactive = $('#fActiveInactive').val();
			fCreatedTime=localToGmtDateTime($('#createdTime input').val().trim());
			fUpdatedTime=localToGmtDateTime($('#updatedTime input').val().trim()); 
			fUpdatedBy=($('#searchModifiedBy').val() == "")?null:$('#searchModifiedBy').val();

*/



			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];

			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");

				if(filterCategoryArr[0] == "Name"){
					fConfigName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Type"){
					fAgentTypeId = $.grep(self.agentTypesArr(), function(e){
						return e.agentType == filterCategoryArr[1];
					})[0].agentTypeId;
				}
				else if(filterCategoryArr[0] == "Host"){
					fHost = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Mode"){
					fAgentMode = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Applications"){
					fApplicationId = $.grep(self.applicationsArr(), function(e){
						return e.applicationName == filterCategoryArr[1];
					})[0].applicationId;
				}
				else if(filterCategoryArr[0] == "Created Time"){
					fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
				}
				else if(filterCategoryArr[0] == "Modified Time"){
					fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
				}
				else if(filterCategoryArr[0] == "Modified By"){
					fUpdatedBy = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Tags"){
					fTags = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Status"){
					fConfigActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
				}
			}
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += "," + tagsData[tag].tagName;
			}

			return tagNames.substring(1);
		}

		function resetPagination(){
			if(self.totalPages() != 0){
				if(typeof self.currentPage() == "string"){
					self.currentPage(parseInt(self.currentPage()));
				}

				if(self.currentPage() == "" || isNaN(self.currentPage()))
					self.currentPage(1);
				else if(self.currentPage()>self.totalPages())
					self.currentPage(self.totalPages());
				else if(self.currentPage()<1)
					self.currentPage(1);
			}
		}

		self.LoadAgentListView = function(){
			requestCall(uiConstants.common.SERVER_IP + "/agent/host?status=2&markInactive=1", "GET", "", "getHosts", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/574d87091100005107a2611b?callback=?", "GET", "", "getHosts", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/agentType", "GET", "", "getAgentTypes", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57568e0f0f0000b4142effca?callback=?", "GET", "", "getAgentTypes", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/agentMode", "GET", "", "getAgentModes", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57529a890f0000161ada808d?callback=?", "GET", "", "getAgentModes", successCallback, errorCallback);
		};

		/*Publish the topic 'refreshAgentListView' when Agent List View to be refreshed*/
		uicommon.postbox.subscribe(function(value){
			self.LoadAgentListView();
			self.getListData();
		},"refreshAgentListView");

		function reloadFilterMasters(){
			if(fHostReloaded){
				fHostReloaded = 0;
				self.addUpdateFlag(false);

				if(self.isFilterOrList() == "list"){
					self.getListData();
				}
				else{
					if(fHost){
						//$("#fHostsList").val().trigger('chosen:updated');
						//$("#fHostsList option:contains(" + fHost + ")").attr('selected', 'selected').trigger('chosen:updated');
						$('#fHostsList option').filter(function () { return $(this).html() == fHost; }).attr('selected', 'selected').trigger('chosen:updated');
					}
					setFiltersToVariables();
					self.getFilterListData(true);
				}
			}
		}

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);

			if(reqType === "getListData"){
				

				resetButtonStates();

				if(data.responseStatus == "success"){
					listData = data;
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					self.totalRecords(listData.totalRecords);
					//self.gridData(listData.result);

					$("#agentListConfigDetails #listgrid #gridDataBody").empty();
			 		$("#agentListConfigDetails #listgrid").trigger("update");
					self.gridData(listData.result);

					var initialSortColumn = 1;
					var sortOrder = 0; //0=asc; 1=desc
					if(!$("#agentListConfigDetails #listgrid").hasClass("tablesorter")){
						if(!self.gridData().length){
							self.enableFilter(false);
						}
						else{

							if (!$("#pageNum").hasClass("select2-hidden-accessible")){
								debugger;
								$("#pageNum").select2();

								$("#pageNum").select2("open");
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
								$("#select2-pageNum-container").parent().css({
									"border": "none",
									"outline": "none"
								});

								$("#pageNum").parent().children("span").css("width", "36px");
								//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
								$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
								$("#select2-pageNum-container").css({
										"font-weight": "bold",
										"color": "#218DC0",
										"padding-left": "4px"
									});

								$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
								$(".pageNumDropDown .select2-search").css("display", "none");
								$("#select2-pageNum-results").css("overflow-x", "hidden");
								$("#pageNum").select2("close");
							}
						}

						$("#agentListConfigDetails #listgrid").addClass("tablesorter")
						/*$("#agentListConfigDetails #listgrid").tablesorter({
							ignoreCase : false,
							cancelSelection: false,
							headers: { 0: { sorter: false}, 5: { sorter: false}, 9: { sorter: false}, 11: { sorter: false} },

						    widgetOptions: {
						      sortTbody_primaryRow : '.main',
						      sortTbody_sortRows   : false,
						      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
						    }
							//sortList: [[initialSortColumn, sortOrder]]
						});

						$("#agentListConfigDetails #listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

						var $tab = $('#listgrid');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper');
							}
						});

						$('#unselectAll').on('click', function(e){
							window.unselectAllCheck(self.handleChkClick, '.chkboxCol');
						});
					}
					/*else{
						$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
						
					}*/

					/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
*/
					$("#agentListConfigDetails #listgrid").trigger("update");
					self.refreshPageLayout();
					
					if((self.currentPage() == 0 || filterForFirstPage) && self.gridData().length>0){
						self.currentPage(1);
						filterForFirstPage = false;
					}

					self.msgShowListData();
				}else{
					showMessageBox(data.message, "error");
					self.showListAvailable("");		
				}

				var $tab = $('#listgrid');
				$tab.floatThead('reflow');
			}
			else if(reqType === "getHosts"){
				self.hostsArr([]);

				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				if(data.result.length == 0){
					self.hostsArr([""]);
				}
				else{
					for(var host in data.result){
						if(data.result[host].hostName && data.result[host].hostAddress){
							self.hostsArr.push(data.result[host].hostName+" ("+data.result[host].hostAddress+")");
						}
						else if(data.result[host].hostName){
							self.hostsArr.push(data.result[host].hostName);
						}
						else if(data.result[host].hostAddress){
							self.hostsArr.push(data.result[host].hostAddress);
						}
					}
					/*self.hostsArr(data.result.map(function(host) {return host.hostName;}));

					if(self.hostsArr()){
						self.hostsArr(self.hostsArr().concat(data.result.map(function(host) {return host.hostAddress;})));
					}
					else{
						self.hostsArr(data.result.map(function(host) {return host.hostAddress;}));
					}*/

					/*if(self.addedUpdatedHost() != ""){
						$("#fHostsList").val("0").trigger('chosen:updated');
						self.getFilterListData(true);
						self.addedUpdatedHost("");
					}*/
				}

				self.hostsArr(removeDuplicates(self.hostsArr()));
				$("#fHostsList").trigger('chosen:updated');

				if(self.addUpdateFlag){
					fHostReloaded = 1;
        			reloadFilterMasters();
				}
			}
			else if(reqType === "getApplications"){
				if(data.result.length == 0){
					self.applicationsArr([{}]);
				}
				else{
					self.applicationsArr(data.result);
				};
				$("#fApplicationList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
				$("#fApplicationList").trigger('chosen:updated');

			}
			else if(reqType === "getAgentTypes"){
				self.agentTypesArr(data.result);
				$("#fAgentType").trigger('chosen:updated');
				$("#fAgentType_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			}
			else if(reqType === "getAgentModes"){
				self.agentModesArr(data.result);
				$("#fAgentMode").trigger('chosen:updated');
				$("#fAgentMode_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			}
			else if(reqType === "getJsonConfig"){
				downloadFile("agent-details.json", JSON.stringify(data.result, null, "\t"));
			}
			else if(reqType === "getAllJsonConfig"){
				downloadFile("agents.json", JSON.stringify(data.result, null, "\t"));
			}
			else if(reqType == "getUnmappedInstanceDetails"){
				if(uiConstants.common.DEBUG_MODE)console.log("------------------------------Agent : unmapped component instance details -----------------------");
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.unmappedCompInstancesArr(data.result);
				uicommon.postbox.publish(self.unmappedCompInstancesArr(),"addedComponentInstanceDetailResultData");
				if(self.panelTitle === "Edit"){
					params.callMethod(self.unmappedCompInstancesArr(),"Edit");
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getListData"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENTS, "error");
  			}
  			else if(reqType === "getHosts"){
  				showMessageBox(uiConstants.agentConfig.ERROR_GET_HOSTS, "error");
  			}
  			else if(reqType === "getApplications"){
  				showMessageBox(uiConstants.common.ERROR_GET_APPLICATIONS, "error");
  			}
			else if(reqType === "getAgentTypes"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENT_TYPES, "error");
			}
			else if(reqType === "getAgentModes"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENT_MODES, "error");
			}
			else if(reqType === "getJsonConfig"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENT_JSON_CONFIG, "error");
			}
			else if(reqType === "getAllJsonConfig"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_AGENT_JSON_CONFIG, "error");
			}
			else if(reqType == "getUnmappedInstanceDetails"){
				showMessageBox(uiConstants.agentConfig.ERROR_GET_UNMAPPED_COMPINSTANCES, "error");
			}
		}

		/*Subscriber Lists*/
		 /* Change the view index to specific value from where ever this topic is published*/

	    uicommon.postbox.subscribe(function(value){
	      self.currentViewIndex(value);
	      uicommon.postbox.publish("View is now a List","ViewIsChangedToList");
	    }, "changeViewToList");

		uicommon.postbox.subscribe(function(value){
			uicommon.postbox.publish(self.currentViewIndex(),"currentViewIsChecked");
		},"checkCurrentView");

	}
	AgentListGridView.prototype.dispose = function() { };
	return { viewModel: AgentListGridView, template: templateMarkup };
});