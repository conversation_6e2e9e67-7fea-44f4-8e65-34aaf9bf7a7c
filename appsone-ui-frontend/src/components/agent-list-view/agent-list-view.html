<div class="col-sm-12" id="agentListConfigDetails" data-bind="visible : currentViewIndex() == uiConstants.common.LIST_VIEW"> 
	<div class="form-inline">
		<div class="form-group" style="padding-bottom: 5px;">
			<!-- <select class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}"></select>
			<label data-bind="text:uiConstants.common.PER_PAGE"></label> -->

			<h4 data-bind="if: currentViewIndex() == uiConstants.common.LIST_VIEW"><span class="a1-page-title" data-bind="text: pageSelected"></span></h4>
			<div data-bind="template: {afterRender: renderHandler}"></div>
		</div>
		
		<div class="form-group" style="float: right;">
			<span class="mandatoryField" data-bind="text: errorMsg()"></span>
			<button type="button" id="btnFilter" class="btn listViewBtn" data-bind="event: {click: function(){showFilterBox()}}"><span class="fa fa-filter listViewBtnTxt"></span>FILTER</button>
			<!-- ko if: window.commonAddEnabled() -->
				<button type="button" class="btn listViewBtn btn-primary" id="btnAdd" data-bind="attr: {disabled: !enableAdd()}, event: {click: switchView.bind($data,uiConstants.common.ADD_SINGLE_VIEW)}"><span class="fa fa-plus listViewBtnTxt"></span>ADD</button>
			<!-- /ko -->

			<!-- ko if: window.commonUpdateEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnEdit" data-bind="attr: {disabled: !enableEdit()}, event: {click: editConfig}"><span class="fa fa-pencil-square-o listViewBtnTxt"></span>EDIT</button>
			<!-- /ko -->

			<!-- ko if: window.commonAddEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnClone" data-bind="attr: {disabled: !enableClone()}, event: {click: cloneConfig}"><span class="fa fa-clone listViewBtnTxt"></span>CLONE</button>
			<!-- /ko -->

			<button type="button" class="btn listViewBtn" style="width: auto;" id="btnClone" data-bind="attr: {title: 'Download agents.json (PSA)'}, event: {click: downloadAllPsaJsonConfig}">Download PSA Details <span class="glyphicon glyphicon-save a1-glyphicon-action-btn listViewBtnTxt" style="color: #000000;"></span></button>

			<span class="glyphicon glyphicon-cog" id="columnsOption" title="Show/Hide Columns" data-bind="event: {click: function(){toggleColumnsList()}}"></span>
			<div class="columnsListContainer">
				<span class="columnsListSpan">Columns:</span>

				<label title="Select All" class="columnsSelectAll"><input type="checkbox" id="selAllCols" style="margin-right: 5px;" checked>Select All</label>
				<hr class="horizRuleColumnsList">

				<div class="columnsList"></div>
				<button class="btn-small" type="button" style="margin-left: 5px; margin-bottom: 5px;" data-bind="event: {click : function(){modifyColumnsListBox()}}">Done</button>
				<button class="btn-small" type="button" style="margin-bottom: 5px;" data-bind="event: {click : function(){closeColumnsListBox()}}">Cancel</button>
			</div>
		</div>
	</div>
	
	<div id="filterBox" class="a1-filter-box">
		<div id="filterOptionsBox">
			<select class="chosen form-control" id="filterCriteria" data-bind="value: selFilterCategory, event: {change: function(){onFilterCatChange()}}, foreach: filterGridHeader" data-placeholder=" " >
				<option  data-bind="text: $data"></option>
			</select>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<input type="text" class="form-control filterFieldWidth" style="display: inline;" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"> 
			<!-- /ko -->

			<!-- ko if: selFilterCategory() == 'Created Time' || selFilterCategory() == 'Modified Time' -->
				<div class='input-group date filterFieldWidth' id='filterCreateModTime' style="display: inline-flex;">
		            <input type='text' class="form-control" placeholder="Pick Date/Hour" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"/>
		            <span id="calendarIcon" class="input-group-addon" title="Pick Date/Hour" style="width: 40px;">
		                <span class="glyphicon glyphicon-calendar"></span>
		            </span>
		        </div>
			<!-- /ko -->

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Type' ? 'inline-block' : 'none'}">
				<select id="fAgentType" class="chosen form-control" data-bind="options: agentTypesArr,
               optionsText: 'agentType',
               optionsValue: 'agentTypeId',
               optionsCaption: uiConstants.agentConfig.SELECT_AGENT_TYPE,
               value: filterValueArr()[gridHeader.indexOf('Type')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}"></select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Host' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fHostsList" data-bind="foreach : hostsArr,
               value: filterValueArr()[gridHeader.indexOf('Host')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="text: uiConstants.agentConfig.SELECT_HOST, value: '0'"></option>
					<!-- /ko-->
					
					<!-- ko if: $data -->
						<option data-bind="text: $data, value: ''"></option>
					<!-- /ko-->
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Mode' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fAgentMode" data-bind="foreach : agentModesArr,
               	value: filterValueArr()[gridHeader.indexOf('Mode')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="text: uiConstants.agentConfig.SELECT_AGENT_MODE"></option>
					<!-- /ko-->
					
					<option data-bind="text: $data, value: ''"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Applications' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fApplicationList" data-bind="foreach : applicationsArr,
	            value: filterValueArr()[gridHeader.indexOf('Applications')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="text: uiConstants.common.SELECT_APPLICATION, value: '0'"></option>
						<!-- /ko-->
						
						<option data-bind="text: $data.applicationName, value: $data.applicationId"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Status' ? 'inline-block' : 'none'}">
				<select id="fActiveInactive" class="chosen form-control" data-bind="value: filterValueArr()[gridHeader.indexOf('Status')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}">
					<option value="2" >All</option>
					<option value="1">Active</option>
					<option value="0">Inactive</option>
				</select>
			</div>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<button type="button" class="btn a1-action-btn" data-bind="event: {click: function(){onFilterAdd()}}">ADD</button>
			<!-- /ko-->
		</div>

		<!-- ko if: filtersAdded() -->
			<table style="margin-top: 5px;">
				<tbody>
					<tr>
						<td class="filter-table-td" style="width: 100%;">
							<input type="text" class="form-control tokenfield" id="filters-tokenfield-typeahead" style="margin-top: 5px;" data-bind="value: filtersAdded">
						</td>

						<td class="filter-table-td">
							<button type="button" class="btn a1-action-link" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL <span class="fa fa-times-circle"></span></button>
						</td>

						<td>
							<button type="button" style="width: 70px; margin-left: 6px;" class="btn btn-primary" id="btnApplyFilter" data-bind="event: {click: function(){getFilterListData(true)}}">APPLY</button>

							<span id="filterOptionsDispBtn" class="fa fa-angle-double-down a1-options-display" data-bind="event: {click: function(){onFilterOptionsDispClick()}}" style="display: none;" title="Show Filter Options"></span>
						</td>
					</tr>
				</tbody>
			</table>
			<!-- <div style="width: 80%; display: inline-block;">
				
			</div>

			<div style="width: 15%; display: inline-block;">
				<button type="button" class="btn listViewBtn" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL</button>

				


			</div> -->
		<!-- /ko -->

	</div>

	<div>
		<div class="wrapper"><!-- style="width:100%;float:left; padding-top: 5px" -->
			<table id="listgrid" class="table table-sm table-hover a1-list-grid" style="width:100%">
				<thead class="a1-list-grid-header">
                    <tr data-bind="foreach: gridHeader">
						<!-- ko if: $index() == 0 -->
							<th class="actionControl">
								<input type="image" src="/images/unselect-16.png" title="Deselect All" id="unselectAll"></input>
							</th>
						<!-- /ko -->

						<th class="listViewCol textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="css: $parent.noSortColKeys.indexOf($parent.configTableHeaderObjKeys()[$index()]) == -1 ? 'header' : 'noSort', text: $data, attr:{'width': $data=='Status'?'100px':'auto'}, event: {click: function(){$parent.onHeaderClick($index(), $parent.configTableHeaderObjKeys()[$index()])}}" ></th>
					</tr>
				</thead>

				<tbody id="gridDataBody" data-bind="foreach: gridData" class="main" style="background-color: white;">
					<tr>
						<td style="text-align:center"><input type="checkbox" class="chkboxCol" data-bind="checked: $data.isSelected, attr:{rowData: $data}" title="Select"/></td>

						<!-- <td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.agentName, attr:{'title': $data.agentName}"></td> -->

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<a data-bind="text: $data.agentName, attr:{'title': $data.agentName}, click: $parent.onNameClick"></a>
						</td>
<!-- 
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" style="padding: 5px">
							<input type="text" data-bind="value: $data.identifier, attr:{'title': $data.identifier}" style="width: 100%; border-style: none; background-color: transparent;" readonly="" />
						</td> -->

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.agentType, attr:{'title': $data.agentType}"></td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.host, attr:{'title': $data.hostName}"></td>

						<td class="textOverflowOmmiter" data-bind="text: $data.agentMode, attr:{title: $data.agentMode}" data-toggle="tooltip"
						></td>

						<td>
							<div class="text-two-line-ellipsis" data-bind="text: $data.applications, attr:{title: $data.applications}" data-toggle="tooltip"
							>
							</div>
						</td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.createdTime), attr:{'title': $parent.convertToLocalTime($data.createdTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.updatedTime), attr:{'title': $parent.convertToLocalTime($data.updatedTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.updatedBy, attr:{'title': $data.updatedBy}"></td>

						<td class="col-xs-2">
							<div class="text-two-line-ellipsis" data-bind="text: $parent.getTagNames($data.tags), attr:{title: $parent.getTagNames($data.tags)}" data-toggle="tooltip"
							>
							</div>
						</td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<!-- ko if: $data.status == 1 -->
								<span>
									<img src="/images/green-circle-12.png">
									Active
								</span>
							<!-- /ko -->

							<!-- ko if: $data.status == 0 -->
								<span>
									<img src="/images/red-circle-12.png">
									Inactive
								</span>
							<!-- /ko -->
						</td>
						
						<td style="text-align: center;">
						<!-- 	<div style="display: inline-flex; width: auto;"> -->
								<!-- <button type="button" data-toggle="tooltip" data-placement="bottom" title="Download config.properties" data-bind="attr: {disabled: !(slugify($data.dataProtocol) == 'grpc')}, css: {confButtonDisabled: !(slugify($data.dataProtocol) == 'grpc')}" class="form-control glyphicon glyphicon-save" id="btnDownloadProperties" style="width: auto;"></button> -->

								<!-- <span type="button" id="btnDownloadProperties" class="glyphicon glyphicon glyphicon-save glyphicon-style" data-bind="attr: {disabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent'), title: 'Download basic.properties'}, css: {confButtonDisabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent')}, style:{marginRight: '5px'}"></span> -->

								<span id="btnDownloadProperties" data-bind="attr: {disabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent'), title: 'Download basic.properties'}, css: {confButtonDisabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent')}, style:{marginRight: '5px'}">
									<img style="width: 18px; height: 
									18px; cursor: pointer;" src="/images/properties_64.png">
								</span>

								<!-- <span type="button" id="btnDownloadJson" class="glyphicon glyphicon glyphicon-save glyphicon-style" data-bind="attr: {disabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent'), title: 'Download agent-details.json'}, css: {confButtonDisabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent')}"></span> -->


								<span id="btnDownloadJson" data-bind="attr: {disabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent'), title: 'Download agent-details.json'}, css: {confButtonDisabled: !(slugify($data.agentType) == 'componentagent' || slugify($data.agentType) == 'psagent')}">
									<img style="width: 18px; height: 
									18px; cursor: pointer;" src="/images/json_64.png">
								</span>
								
							<!-- </div> -->
						</td>
					</tr>
				</tbody>
			</table>

			<!-- ko if: gridData().length == 0 -->
				<div colspan="11" style="text-align: center;"><h4><span data-bind="text: showListAvailable" ></span></h4></div>
			<!-- /ko -->
		</div>
	</div>

	<!-- ko if : currentViewIndex() == uiConstants.common.LIST_VIEW && gridData().length>0 -->
		<div class="config-pagination-footer">
			<label class="a1LabelFooter"></label>

			<div style="float:right;">
				<label class="a1LabelFooter" data-bind="text:uiConstants.common.PER_PAGE"></label>
				<select id="pageNum" class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}" style="display: inline; width: auto;"></select>

				<label class="a1LabelFooter" data-bind="text: recordsCountLabel()"></label>

				<span id="prevButton" class="fa fa-angle-left paginationPrev" data-bind="attr: {title: 'Previous Page', disabled: currentPage()<=1}, click: currentPage()<=1 ? '' : prevPage, css: {paginationArrowDisabled: currentPage()<=1}"></span>

				<!-- <span class="paginationLabel">Page <input type="number" min="1" class="pageNumber" data-bind="value: currentPage, event: {change: getListOrFilterData}"> of&nbsp; </span><strong class="paginationLabel" data-bind="text: totalPages()"></strong> -->
				<span class="fa fa-angle-right paginationNext" data-bind="attr: {title: 'Next Page', disabled: currentPage()==totalPages()}, click: currentPage()==totalPages() ? '' : nextPage, css: {paginationArrowDisabled: currentPage()==totalPages()}"></span>
			</div>
		</div>
	<!-- /ko -->
</div>

<div style="width:100%;float:left;" data-bind="if : currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || currentViewIndex() == uiConstants.common.EDIT_VIEW || currentViewIndex() == uiConstants.common.CLONE_VIEW || currentViewIndex() == uiConstants.common.READ_VIEW">
	<agent-add-edit params="{hostsArr: hostsArr, agentTypesArr: agentTypesArr, agentModesArr: agentModesArr, currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, curPage: curPage, pageSelected: pageSelected, mode: mode, addUpdateFlag: addUpdateFlag}"></agent-add-edit>
</div>


<message-dialog-box params="{ componentBindingType: componentBindingType, isCloseRequired: isCloseRequired, modalHeaderTitle: modalHeaderTitle, componentInstanceIDs:componentInstanceIDs}"></message-dialog-box>