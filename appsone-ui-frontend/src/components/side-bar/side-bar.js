define(['jquery','knockout','text!./side-bar.html','hasher'], function($,ko,templateMarkup,hasher) {

  function SideBar(params) {
    this.message = ko.observable('Hello from the side-bar component!'); 
    var self = this;

    this.renderHandler=function(){
     /* function populateSideBar(){
          console.log(window.sideMenuObj());


        $('#sideBarMenus').empty();
        $(sideBarMenu).appendTo('#sideBarMenus');
      }*/
    }
   /*Sidebar toggle code start*/
   $(".sideBar_wrapper").toggleClass("active");
   $(".push_menu").click(function(){
         $(".sideBar_wrapper").toggleClass("active");
         console.log($(".sideBar_wrapper").hasClass('active'));
         if($(".sideBar_wrapper").hasClass('active')){
            $(".container").css('opacity','1.0').find("*").prop("disabled", false);
         }else{
            $(".container").css('opacity','0.1').find("*").prop("disabled", true);
         }
    });

    $('.side-bar .menu li a').on('click',function() {         
        $('.side-bar .menu li a').removeClass('active');
        //$(this).removeClass('active');
        $(this).addClass('active');  

        
        
    });    
  
    /*Sidebar toggle code end*/

    }

      
    /*Validation rules for configuration end*/

  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  SideBar.prototype.dispose = function() { };
  
  return { viewModel: SideBar, template: templateMarkup };

});