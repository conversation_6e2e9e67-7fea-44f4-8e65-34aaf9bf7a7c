<div data-bind="template :{ afterRender: renderHandler} ">
	<!-- ko if: showChart-->
	<div data-bind=" component:{ name: chartComponentName, 
			 				 	 params: { 'podId': podId, 
				 				 		   'chartDataObj' : chartDataObj
		 				 		   		 }
 				 		   	   }" >
   	</div>
	<!-- /ko -->

	<!-- ko if: showExpandableView -->
	<dashboard-pod-expandedview params="podId: podId, 
										dualView: dualView, 
										podTitle: podTitle,
										chartComponentName: 'dashboard-aggregated-txns',
										'chartDataObj': chartDataObjForExpandableView,
									 	offSet: offSet,
										timeUnit: timeUnit">
	</dashboard-pod-expandedview>
	<!-- /ko --> 
</div>
