define(['jquery','knockout', 'text!./dashboard-transaction-health.html', 'knockout-es5','d3','c3'], function($, ko, templateMarkup, koES5, d3, c3){
	function DashboardTransactionHealth(params)
    {
        var self = this;

        self.showChart = false;
        self.chartComponentName = "horizontal-bar-chart";
        this.timeUnit = params.timeUnit;
        this.offSet = params.offSet;
        this.currentPodBodyHeight = '';
	    this.currentPodBodyWidth = '';
	    this.podId = params.podId;
	    this.podTitle = params.podTitle;
	    this.podName = params.podName;
	    this.chartDataObj = {};
        this.showExpandableView = true;
        this.dualView = params.dualView;
        this.chartDataObjForExpandableView = {};
        
        koES5.track(this);

        this.renderHandler = function(){

        	self.currentPodBodyHeight = $('#pod_'+params.podId).height()-35;
        	self.currentPodBodyWidth = $('#pod_'+params.podId).width()-35;

        	requestCall("http://www.mocky.io/v2/583ff36e240000471883b57a?callback=?","GET","","getTxnHealthData", self.successCallBack, self.errorCallBack);
        };

        self.successCallBack = function(data, reqType){
        	if(data.responseStatus === "success"){
        		var $data = data.graphData;
                var yAxisDataSet = $data.yAxis[0];

        		if(reqType === "getTxnHealthData"){
        			var txnStatusList = [];
        			var txnStatusCount = ['data1']; // added datum for the Chart Data
        			
        			$.each(yAxisDataSet.data, function(item){
        				var currKey = Object.keys(this)[0];
                        txnStatusList.push(currKey);
        				txnStatusCount.push(this[currKey]);
        			});

        			self.chartDataObj = {
	    				chartHeight: self.currentPodBodyHeight-5,
	    				chartWidth: self.currentPodBodyWidth-5,
	    				legends: txnStatusList,
	    				yAxisDataSet: txnStatusCount
    				};
    				
    				self.showChart = true;
				}
			}
        };

        self.errorCallBack = function(){
        	if(reqType === "getTxnHealthData"){
        		console.log("Error in getting Txn Health Data");
        	}
        };

    }
	    
    DashboardTransactionHealth.prototype.dispose = function() {}
	
	return { viewModel: DashboardTransactionHealth, template: templateMarkup };
});
