define(['jquery','knockout','jquery-chosen','bootstrap','text!./placeholder-options.html','hasher','validator','ui-constants','ui-common','checklistbox'], function($,ko,jc,bt,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox) {

	function PlaceholderOptions(params) {	
		var self = this;
		this.placeholdersArr = ko.observableArray();
		this.chkListPlaceholdersArr = ko.observableArray();
		this.selectedPlaceholdersArr = params.selectedPlaceholdersArr;
		this.screenName = params.screenName;

		this.renderHandler = function(){
			$("div").on("click", "#selAllPlaceholders", function(e){
				$("#placeholdersList .checkList").prop("checked", $("#selAllPlaceholders").prop("checked"));
			});

			$("div").on("click", "#placeholdersList .checkList", function(e){
				$("#selAllPlaceholders").prop("checked", self.chkListPlaceholdersArr().length == $("#placeholdersList .checkList:checked").length);
			});

			$('#idModalPlaceholder').on('click', '[data-dismiss="modal"]', function(e) { e.stopPropagation(); });


			if(self.screenName === "emailSMS"){
				requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/placeholders", "GET", "", "getPlaceholders", successCallback, errorCallback);
			}
			else{
				requestCall(uiConstants.common.SERVER_IP + "/placeholders/"+params.notificationTypeId(), "GET", "", "getPlaceholders", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5847f1393f0000d02ffe6a73?callback=?", "GET", "", "getPlaceholders", successCallback, errorCallback);
			}
		}

		self.addEditConfig = function(){
			self.selectedPlaceholdersArr($("#placeholdersList").getSelectedValues());
			$("#idModalPlaceholder .modal-header button").click();
		}

		self.resetConfig = function(){
			$("#selAllPlaceholders").prop("checked", false);
			$("#placeholdersList .checkList").prop("checked", false);
		}

		self.cancelConfig = function(){
			$("#idModalPlaceholder .modal-header button").click();
			//$("#idModalPlaceholder").modal("hide");


		}

		function successCallback(data, reqType) {
			if(reqType === "getPlaceholders"){
				self.placeholdersArr(data.result);

				self.placeholdersArr().forEach(function (value, i) {
				    self.chkListPlaceholdersArr.push({
						"id": value.placeholderValue,
						"name": value.placeholderName
					});
				});

				$("#placeholdersList").checklistbox({
			        data: self.chkListPlaceholdersArr()
			    });
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getPlaceholders"){
				showMessageBox(uiConstants.placeholders.ERROR_GET_PLACEHOLDER_DETAILS, "error");
			}
		}
	}

	PlaceholderOptions.prototype.dispose = function() { };
	return { viewModel: PlaceholderOptions, template: templateMarkup };
});