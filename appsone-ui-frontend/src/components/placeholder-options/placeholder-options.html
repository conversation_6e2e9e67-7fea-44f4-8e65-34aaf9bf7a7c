<div class="panel panel-default" style="margin-bottom: 0px;">
	<div class="panel-body" style="padding-bottom: 0px;">
		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
		<label style="font-weight: normal;"><input id="selAllPlaceholders" type="checkbox" style="margin-left: 5px;" data-bind="attr: {disabled: chkListPlaceholdersArr().length == 0}"> Select All</input></label>
			<div id="placeholdersList" class="inner-div-container" 
			 style="height: 300px">
			</div>

			<div class="form-group">
				<div class="col-sm-12" style="margin-top: 10px;">
					<button type ="button" class="btn" data-bind="event:{click: addEditConfig}">Save</button>
					<button type ="button" class="btn" data-bind="event:{click: resetConfig}">Reset</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}">Cancel</button>
				</div>
			</div>
		</form>
	</div>
</div>