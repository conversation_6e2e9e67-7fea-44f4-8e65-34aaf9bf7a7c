define(['jquery','knockout','jquery-chosen','text!./kpi-producer-mapping.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

	function KpiProducerMapping(params) {
		var self = this;
		this.kpiMapRowsArr = ko.observableArray();
		this.kpiProdMapArr = ko.observableArray();
		this.producerMasterArr = ko.observableArray();
		this.producerArr = ko.observableArray();
		this.kpiProduceMapArr = ko.observableArray();
		this.kpiProdMapFormattedArr = ko.observableArray();
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.pageSelected = params.pageSelected;
		this.currentViewIndex = params.currentViewIndex;
		this.kpisList = ko.observableArray([]);
		this.activeKpisList = ko.observableArray([]);
		this.selectedComponentTypeObj = ko.observable()
		this.selectedComponentObj = ko.observable();
		this.selectedKpiObj = ko.observable();
		this.isKpiGroup = ko.observable(false);
		this.selectedVersionObj = ko.observable();
		this.displayConfig = ko.observable(false);
		this.componentsArr = params.componentsArr;
		this.modalConfigName = ko.observable("");
		this.kpisExistingList = ko.observableArray();
		this.thresholdOperationsArr = ko.observableArray();
		var selectedProducerIdArr = [];
		var kpisLoaded = 0;
		var producersMapLoaded = 0;
		var thresholdOperationsLoaded = 0;
		var producersToListRow;
		var kpiGrpStartIndex = 0;
		var kpiListItem;
		var kpiGrpImgEle = "<img style='margin-right: 2px;' src='/images/group16.gif' title='KPI Group'>";

		this.renderHandler = function(){
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#idModal").on('hidden.bs.modal', function(){
				self.displayConfig(false);
				if(self.modalConfigName() != ""){
					for(kpiProducer in self.kpiMapRowsArr()){
						selectedProducerIdArr.push($("#producer"+kpiProducer).val());
					}

					requestCall(uiConstants.common.SERVER_IP + "/producerName?status=2&markInactive=1", "GET", "", "getProducersMaster", successCallback, errorCallback);
				}
			});

			$("#kpiProducerMapList tbody").on('click', '.buttondelete', function(e){

				var rowToDelete = $(this).closest('tr').get(0).rowIndex-1;
				
				//if(DEBUG_MODE)console.log(kpiProducerMapDeleteObj);
				if(!self.kpiMapRowsArr()[rowToDelete].existing || self.kpiProdMapFormattedArr()[rowToDelete].isCustom == 1){
					if(uiConstants.common.DEBUG_MODE)console.log(self.kpiMapRowsArr());//[rowToDelete].

				    showMessageBox(uiConstants.kpiProducerMap.CONFIRM_KPI_PROD_MAPPING_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
						if (confirmDelete) {
					 		if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
							//self.kpiMapRowsArr.splice(rowToDelete, 1);
							//self.kpiProdMapFormattedArr.splice(rowToDelete, 1);

							self.kpiMapRowsArr.splice(rowToDelete, 1, {"hidden":true});

							setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol');
							setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol');
					 	}
					 });
				}
			});

			self.pageSelected(self.pageSelected() + " for " + self.selectedConfigRows()[0].componentName + " " + self.selectedConfigRows()[0].componentVersion);
			
			$('#kpiProducerMapList tbody').on('click', '.chkboxMappingStatusCol', function(e){
				$("#kpiAvailAnalytics" + e.currentTarget.id.substring(6)).prop({"disabled": !this.checked, "checked": false});
				setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol');

				$("#chkboxMappingAnalyticsHeader").prop({"disabled": $('.chkboxMappingStatusCol:checked').length == 0, "checked": false});
			});

			$('#kpiProducerMapList thead').on('click', '#chkboxMappingStatusHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingStatusCol").prop("checked",false);
				}
				else {
					$(".chkboxMappingStatusCol").prop("checked",true);
				}
				$(".chkboxMappingAnalyticsCol").prop({"disabled": !this.checked, "checked": false});
				$("#chkboxMappingAnalyticsHeader").prop({"disabled": $('.chkboxMappingStatusCol:checked').length == 0, "checked": false});
			});

			$('#kpiProducerMapList tbody').on('click', '.chkboxMappingAnalyticsCol', function(e){
				setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol');
			});

			$('#kpiProducerMapList thead').on('click', '#chkboxMappingAnalyticsHeader', function(e){
				if (this.checked == false) {
					$(".chkboxMappingAnalyticsCol:enabled").prop("checked",false);
				}
				else {
					$(".chkboxMappingAnalyticsCol:enabled").prop("checked",true);
				}
			});

			//var compVersionId = self.selectedConfigRows()[0].componentVersionId;
			requestCall(uiConstants.common.SERVER_IP + "/kpis_KpiGroups?status=2&markInactive=1", "GET", "", "getKpisList", successCallback, errorCallback);
			
			var compVersionId = self.selectedConfigRows()[0].componentVersionId; //Send this to API call for getting KPI/Producer mapping
			requestCall(uiConstants.common.SERVER_IP + "/componentKpiProducerMapping/" + compVersionId+"?status=2&markInactive=1", "GET", "", "getKpiProducerMapping", successCallback, errorCallback);
				
		 	requestCall(uiConstants.common.SERVER_IP + "/thresholdOperations", "GET", "", "getOperationsList", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/5762b204100000541c8b14c3?callback=?", "GET", "", "getKpisList", successCallback, errorCallback);
		}

		function setHeaderCheckboxState(headerCheckbox, checkboxColumn){
			debugger;
			var hiddenMappingsLen = $.grep(self.kpiMapRowsArr(), function(e){console.log(e.hasOwnProperty("hidden")); return e.hasOwnProperty("hidden") == false; }).length;

			if ($(checkboxColumn + ':checked').length  == (hiddenMappingsLen) && hiddenMappingsLen!=0) {
				$(headerCheckbox).prop("checked",true);
			}
			else {
				$(headerCheckbox).prop("checked",false);

				if(hiddenMappingsLen == 0){
					$("#chkboxMappingAnalyticsHeader").prop({"disabled": true});
					$("#chkboxMappingStatusHeader").prop({"disabled": true});
				}
				else{
					$("#chkboxMappingStatusHeader").prop({"disabled": false});
					$("#chkboxMappingAnalyticsHeader").prop({"disabled": $('.chkboxMappingStatusCol:checked').length == 0});
				}
			}
		}

		function getProducersForKpi(){
			var isKpiGroup;

			if(self.isKpiGroup()){
				isKpiGroup = 1;
			}
			else{
				isKpiGroup = 0;
			}
			requestCall(uiConstants.common.SERVER_IP + "/producerMapping?componentVersionId=" + self.selectedConfigRows()[0].componentVersionId + "&isGroup=" + isKpiGroup + "&kpiId=" + (isKpiGroup ? self.selectedKpiObj().kpiGroupId : self.selectedKpiObj().kpiId), "GET", "", "getProducerMappingForKpi", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57500f34100000ac1875e9f1?callback=?", "GET", "", "getProducerMappingForKpi", successCallback, errorCallback);
		}

		/*this.onChosenDropdownOpen = function(elementId, parentDiv){

	    };*/

		this.setProducerModalParamData = function(rowIndex){
			if($("#kpiList"+rowIndex).val() == "0"){
				showMessageBox("Please select KPI to add a producer", "error");
			}
			else{
				$('#idModal').modal('show');
				var selKpiObj;
				self.modalConfigName("");
				producersToListRow = rowIndex;
				self.displayConfig(true);

				self.selectedComponentTypeObj({
					"componentTypeId":self.selectedConfigRows()[0].componentTypeId,
					"componentType":self.selectedConfigRows()[0].componentType
				});

				self.selectedComponentObj({
					"componentId":self.selectedConfigRows()[0].componentId,
					"componentName":self.selectedConfigRows()[0].componentName
				});

				self.selectedVersionObj({
					"componentVersionId":self.selectedConfigRows()[0].componentVersionId,
					"componentVersion":self.selectedConfigRows()[0].componentVersion
				});

				if($("#kpiList"+rowIndex).val().startsWith("kpi")){
					self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiId == $("#kpiList"+rowIndex).val().substring(3); })[0]);
					self.isKpiGroup(false);
				}
				else if($("#kpiList"+rowIndex).val().startsWith("group")){
					console.log(self.kpisList());
					self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiGroupId == $("#kpiList"+rowIndex).val().substring(5); })[0]);
					self.isKpiGroup(true);
				}
			}
		}

		this.getProducersMaster = function(indx){
			if(self.kpiProdMapFormattedArr()[indx] && self.kpiProdMapFormattedArr()[indx].producers.defaultId){
				return getMasterList(self.producerMasterArr(), "producerId", [self.kpiProdMapFormattedArr()[indx].producers.defaultId], true);
			}
			else{
				return getMasterList(self.producerMasterArr(), "producerId", null, false);
			}

		}

		function setKpiGroupImg(rowIndex){
			$("#kpiList"+rowIndex+"_chosen")
		    .find("li.active-result").each(function() {
		    	//debugger;
		    	//kpiListItem = $(this).parentsUntil("select");
		    	//console.log($("kpiList"+rowIndex).);

		    	/*if($.grep(self.kpisList(), function(e){ return e.kpiGroupName == kpiListItem.parent().children()[kpiListItem.attr("data-option-array-index")].innerHTML; }).length == 0){
		    		return;
		    	}*/


			    if (kpiGrpStartIndex==0 || $(this).attr("data-option-array-index") < (kpiGrpStartIndex+1)) //1 is added since the first item in the list will be the text indicating the user to select the option
			        return;

			   if($(this).find("img").length == 0){
				    $(this).prepend(kpiGrpImgEle);
				}
			});
		}

		this.onKpiLstOpen = function(rowIndex){
			$("#divMap").css("padding-bottom", "150px");
			setKpiGroupImg(rowIndex);
		}

		this.onKpiLstClose = function(rowIndex){
			//$("#divMap").css("padding-bottom", "0px");
		}

		this.configureKpiProducerMap = function(){
			self.kpiMapRowsArr.push({'existing':false});

			if(self.kpiMapRowsArr().length == 1){
				var $tab = $('table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			}
			var mapRowIndex = self.kpiMapRowsArr().length-1;
			self.producerArr.splice(self.kpiMapRowsArr().length, 1, [{}]);
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			$("#kpiList" + mapRowIndex + "_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#kpiUnit" + mapRowIndex).text(uiConstants.common.NOT_APPLICABLE);
			$("#kpiType" + mapRowIndex).text(uiConstants.common.NOT_APPLICABLE);
		
			$("#kpiList" + mapRowIndex + "_chosen .chosen-search").find("input").on("keyup", function (evt) {
				setKpiGroupImg(mapRowIndex);
			});

			$("#status"+mapRowIndex).prop('checked', true);
			//$("#divKpiProducerMap").scrollTop($("#divKpiProducerMap").scrollTop() + $$('#kpiList' + mapRowIndex + '_chosen').position().top);
			//scrollToPos($('#kpiList' + mapRowIndex + '_chosen').position().top, 500, "#divMap");

			var container = $('#divKpiProducerMap'),
			scrollTo = $('#kpiList' + mapRowIndex + '_chosen');
			container.animate({
				scrollTop: scrollTo.offset().top - container.offset().top + container.scrollTop()
			});

			setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol');
			setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol');
		}

		this.getProducersList = function(rowIndex){
			if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProdMapArr());
			var producersObj;
			self.selectedKpiObj(null);
			//var selKpiObj;

			if($("#kpiList"+rowIndex).val().startsWith("kpi")){
				self.isKpiGroup(false);
				self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiId == $("#kpiList"+rowIndex).val().substring(3); })[0]);
				producersObj = $.grep(self.kpiProdMapArr().kpiDetails, function(e){ return e.kpiId == $("#kpiList"+rowIndex).val().substring(3); })[0];
			}
			else if($("#kpiList"+rowIndex).val().startsWith("group")){
				self.isKpiGroup(true);
				self.selectedKpiObj($.grep(self.kpisList(), function(e){ return e.kpiGroupId == $("#kpiList"+rowIndex).val().substring(5); })[0]);
				producersObj = $.grep(self.kpiProdMapArr().kpiGroups, function(e){ return e.kpiGroupId == $("#kpiList"+rowIndex).val().substring(5); })[0];

				$("#kpiList"+rowIndex+"_chosen span").prepend(kpiGrpImgEle);
				
				//$("#kpiList"+rowIndex+"_chosen span").prepend(kpiGroupImg).trigger('chosen:updated');
			}

			$("#kpiList" + rowIndex + "_chosen span").prop("title",$("#kpiList" + rowIndex + "_chosen span").text());

			if(uiConstants.common.DEBUG_MODE)console.log(producersObj);

			if(producersObj){ 
				self.producerArr.splice(rowIndex, 1, producersObj.producers.defaultId == undefined ? [{}] : producersObj.producers.all);
				$("#producer"+rowIndex).trigger('chosen:updated');
				$("#collIntervalUnit"+rowIndex).trigger('chosen:updated');
				$("#thresholdOperationsList"+rowIndex).trigger('chosen:updated');

				//$("#producer1").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else{
				producersToListRow = rowIndex;
				if(self.selectedKpiObj() != null){
					getProducersForKpi();	
				}
			}

			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedKpiObj());

			$("#kpiUnit"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiUnitName == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiUnitName);
			$("#kpiType"+rowIndex).text((self.selectedKpiObj() == null || self.selectedKpiObj().kpiType == null) ? uiConstants.common.NOT_APPLICABLE : self.selectedKpiObj().kpiType);
		}

		function onMastersLoad(){
			if(kpisLoaded == 1 && producersMapLoaded == 1 && thresholdOperationsLoaded == 1){
				requestCall(uiConstants.common.SERVER_IP + "/producerName?status=2&markInactive=1", "GET", "", "getProducersMaster", successCallback, errorCallback);
			}
		}

		function setKpiProducerMapping(){
		//	alert($("#btnSave").offset().top);
		//	$("#divKpiProducerMap").height($("#btnSave").offset().top+"px");
			$("#divKpiProducerMap").height(($(window).outerHeight() - $("#divKpiProducerMap").offset().top - 80) + "px");


			$("#kpiProducerMapList").width($("#divConfigBtn").width() + ($("#divConfigBtn").width() * 0.2));
			for(kpiProducer in self.kpiProdMapFormattedArr()){
				self.kpiMapRowsArr.push({'existing':true});
				if(self.kpiProdMapFormattedArr()[kpiProducer].kpiId){
					//if(self.kpiProdMapFormattedArr()[kpiProducer].isCustom == 0){
						//console.log();
						//if(self.kpiProdMapFormattedArr()[kpiProducer].isCustom == 0){
							self.kpisExistingList.splice(kpiProducer, 1,  $.grep(self.kpisList(), function(e){ return  e.kpiId==self.kpiProdMapFormattedArr()[kpiProducer].kpiId; }));
						/*}
						else{
							self.kpisExistingList.splice(kpiProducer, 1, $.grep(self.kpisList(), function(e){ return  e.kpiId==self.kpiProdMapFormattedArr()[kpiProducer].kpiId || e.status == 1; }));
						}*/
						//$("#kpiList"+kpiProducer).trigger('chosen:updated');jQuery(".chosen").chosen({search_contains: true});
					//}
					
					$("#kpiList"+kpiProducer).val("kpi"+self.kpiProdMapFormattedArr()[kpiProducer].kpiId);
				}
				else{
					//if(self.kpiProdMapFormattedArr()[kpiProducer].isCustom == 0){
					//	console.log($("#kpiList"+kpiProducer + "_chosen span").first());

						//$("#kpiList"+kpiProducer + "_chosen span").first().addChild("<i>dfdfdf</i>").trigger('chosen:updated');

						//console.log();
						//if(self.kpiProdMapFormattedArr()[kpiProducer].isCustom == 0){
							self.kpisExistingList.splice(kpiProducer, 1, $.grep(self.kpisList(), function(e){ return  e.kpiGroupId==self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupId; }));
						/*}
						else{
							self.kpisExistingList.splice(kpiProducer, 1, $.grep(self.kpisList(), function(e){ return  e.kpiGroupId==self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupId || e.status == 1; }));
						}*/
						//$("#kpiList"+kpiProducer).trigger('chosen:updated');jQuery(".chosen").chosen({search_contains: true});
					//}

					$("#kpiList"+kpiProducer).val("group"+self.kpiProdMapFormattedArr()[kpiProducer].kpiGroupId);
				}

				//i++;
				console.log(self.kpiProdMapFormattedArr()[kpiProducer]);
				console.log(self.kpisExistingList()[kpiProducer]);
				
				$("#kpiList"+kpiProducer).trigger('chosen:updated');
				self.getProducersList(kpiProducer);
				$("#kpiAvailAnalytics"+kpiProducer).prop('checked', self.kpiProdMapFormattedArr()[kpiProducer].availableForAnalytics == 1);
				

				//addInactiveToList(self.producerArr()[kpiProducer], self.kpiProdMapFormattedArr()[kpiProducer].producers, 'producerId', 'producerName', self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId, self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultProducerName, '#producer'+kpiProducer);
				var obj = self.producerArr()[kpiProducer];
				self.producerArr.splice(kpiProducer, 1);
				self.producerArr.splice(kpiProducer, 0, obj);

				if(uiConstants.common.DEBUG_MODE)console.log(self.producerArr());


				$("#producer"+kpiProducer).val(self.kpiProdMapFormattedArr()[kpiProducer].producers.defaultId);
				//$("#producer"+kpiProducer+"_chosen").css({"width": "100px"});
				$("#producer"+kpiProducer+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#producer"+kpiProducer+"_chosen").css("width") + ' !important;' });
				$("#producer"+kpiProducer).trigger('chosen:updated');

				$("#producer"+kpiProducer+"_chosen span").prop("title",$("#producer"+kpiProducer+"_chosen span").text());


				var collInterval = self.kpiProdMapFormattedArr()[kpiProducer].collectionInterval;
				if(collInterval > 30){
					var collInterval = collInterval/60;
					$("#collIntervalUnit"+kpiProducer).val("minutes");
				}
				else{
					$("#collIntervalUnit"+kpiProducer).val("seconds");
				}

				$("#collIntervalUnit"+kpiProducer).trigger('chosen:updated');

				$("#thresholdOperationsList"+kpiProducer).val(self.kpiProdMapFormattedArr()[kpiProducer].thresholdOperationId || "0");
				$("#thresholdOperationsList"+kpiProducer).trigger('chosen:updated');

				$("#highSeverity"+kpiProducer).val(self.kpiProdMapFormattedArr()[kpiProducer].highSeverity);
				$("#collInterval"+kpiProducer).val(collInterval);
				$("#status"+kpiProducer).prop("checked", self.kpiProdMapFormattedArr()[kpiProducer].status == 1);
				//console.log($(".chosen-container").offsetWidth);
				//	$(".chosen-container").attr('style', 'width: '+(195-42)+'px !important');
			}

			for(i in self.kpiProdMapFormattedArr()){
				$("#kpiList" + i + "_chosen span").prop("title",$("#kpiList"+i + "_chosen span").text());
				$("#kpiList" + i + "_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});

				if(self.kpiProdMapFormattedArr()[i].kpiGroupId){
					$("#kpiList"+i+"_chosen span").prepend(kpiGrpImgEle);
				}
			}

			jQuery(".chosen").chosen({
				search_contains: true	
			});

			var $tab = $('table');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});


			setHeaderCheckboxState('#chkboxMappingStatusHeader', '.chkboxMappingStatusCol');
			setHeaderCheckboxState('#chkboxMappingAnalyticsHeader', '.chkboxMappingAnalyticsCol');
		}

		/*var producersChosenElementArr = [];
		function addInactiveToList(allData, dataToAdd, allDataIdKey, allDataNameKey, dataToAddId, dataToAddName, configElement){
				if(!allData.find(function( ele ) {return ele[allDataIdKey] && ele[allDataIdKey] === dataToAddId;} )){
					var obj = {};
					obj[allDataIdKey] = dataToAddId;
					obj[allDataNameKey] = dataToAddName;
					obj["isActive"] = false;
					allData.push(obj);

					producersChosenElementArr.push(configElement);

					sortArrayObjByValue(allData, allDataNameKey);
				}
				if(uiConstants.common.DEBUG_MODE)console.log(allData);
				$(configElement).trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
		}*/

		self.errorMsg.subscribe(function(errorField) {
	        if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		this.updateKpiProducerMapping = function(){
			var kpiProdMapDataNonGroupArr = [];
			var kpiProdMapDataGroupArr = [];
			var validationCounter;
			var selKpiId = 0;
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			for(kpiIndex in self.kpiMapRowsArr()){
				if(!self.kpiMapRowsArr()[kpiIndex].hasOwnProperty("hidden")){
					if($("#kpiList"+kpiIndex+" option:selected").val() == "0"){
						showError("#kpiList"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_KPI_REQUIRED);
						showError("#kpiList"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_KPI_REQUIRED);
			    		self.errorMsg("#kpiList"+kpiIndex+"_chosen");
					}
					else if($.grep(kpiProdMapDataNonGroupArr, function(e){ return e.kpiDisplayIndex == $("#kpiList"+kpiIndex+" option:selected").val(); }).length == 1){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER + ": " +  $("#kpiList"+kpiIndex+" option:selected").text());
						
						showError("#kpiList"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER);
						showError("#kpiList"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER);
			    		self.errorMsg("#kpiList"+kpiIndex+"_chosen");
					}
					else if($.grep(kpiProdMapDataGroupArr, function(e){ return e.kpiDisplayIndex == $("#kpiList"+kpiIndex+" option:selected").val(); }).length == 1){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER + ": " +  $("#kpiList"+kpiIndex+" option:selected").text());

						showError("#kpiList"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER);
						showError("#kpiList"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_SAME_KPI_DEFAULT_PRODUCER);
			    		self.errorMsg("#kpiList"+kpiIndex+"_chosen");
					}
					
					if($("#producer"+kpiIndex+" option:selected").val() == "0"){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED + " for KPI: " + $("#kpiList"+kpiIndex+" option:selected").text());

						showError("#producer"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
						showError("#producer"+kpiIndex+"_chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_PRODUCER_REQUIRED);
			    		self.errorMsg("#producer"+kpiIndex+"_chosen");
					}
					
					if($("#collInterval"+kpiIndex).val() == ""){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);

						showError("#collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_COLLECTION_INTERVAL_REQUIRED);
			    		self.errorMsg("#collInterval"+kpiIndex);
					}
					else if($("#collIntervalUnit"+kpiIndex).val() == "seconds" &&
						parseInt($("#collInterval"+kpiIndex).val()) != 15 && parseInt($("#collInterval"+kpiIndex).val()) != 30){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);

						showError("#collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_SECONDS);
			    		self.errorMsg("#collInterval"+kpiIndex);
					}
					else if($("#collIntervalUnit"+kpiIndex).val() == "minutes" &&
						(parseInt($("#collInterval"+kpiIndex).val()) < 1 || parseInt($("#collInterval"+kpiIndex).val()) >1440)){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);

						showError("#collInterval"+kpiIndex, uiConstants.kpiProducerMap.ERROR_INVALID_COLL_INTERVAL_MINUTES);
			    		self.errorMsg("#collInterval"+kpiIndex);
					}
					
					if($("#thresholdOperationsList"+kpiIndex).val() == "0" && $("#highSeverity"+kpiIndex).val() != ""){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_DEFAULT_THRESHOLD_OPERATOR_REQUIRED);

						showError("#thresholdOperationsList"+kpiIndex+"_chosen", uiConstants.kpiProducerMap.ERROR_DEFAULT_THRESHOLD_OPERATOR_REQUIRED);
						showError("#thresholdOperationsList"+kpiIndex+"chosen span", uiConstants.kpiProducerMap.ERROR_DEFAULT_THRESHOLD_OPERATOR_REQUIRED);
			    		self.errorMsg("#thresholdOperationsList"+kpiIndex);
					}
					
					if($("#thresholdOperationsList"+kpiIndex).val() != "0" && $("#highSeverity"+kpiIndex).val() == ""){
						//self.errorMsg(uiConstants.kpiProducerMap.ERROR_SEVERITY_VALUE_REQUIRED);

						showError("#highSeverity"+kpiIndex, uiConstants.kpiProducerMap.ERROR_SEVERITY_VALUE_REQUIRED);
			    		self.errorMsg("#highSeverity"+kpiIndex);
					}

					if(self.errorMsg() == ""){
						var kpiId = 0;
						var kpiGroupId = 0;
						if($("#kpiList"+kpiIndex+" option:selected").val().startsWith("kpi")){
							kpiId = $("#kpiList"+kpiIndex+" option:selected").val().substring(3);
						}
						else if($("#kpiList"+kpiIndex+" option:selected").val().startsWith("group")){
							kpiGroupId = $("#kpiList"+kpiIndex+" option:selected").val().substring(5);
						}
						
						if(kpiId != 0){
							kpiProdMapDataNonGroupArr.push({
						        "kpiId": kpiId,
						        "kpiDisplayIndex": $("#kpiList"+kpiIndex+" option:selected").val(),
						        "producerId": parseInt($("#producer"+kpiIndex+" option:selected").val()),
						        "availableForAnalytics": $("#kpiAvailAnalytics"+kpiIndex).prop('checked')?1:0,
						        "collectionInterval": $("#collIntervalUnit"+kpiIndex).val() == "minutes" ? parseInt($("#collInterval"+kpiIndex).val()) * 60 : parseInt($("#collInterval"+kpiIndex).val()),
						        "thresholdOperationId": $("#thresholdOperationsList"+kpiIndex).val() == "0" ? null : $("#thresholdOperationsList"+kpiIndex).val(),
						        "highSeverity": $("#highSeverity"+kpiIndex).val().trim(),
						        "status": $("#status"+kpiIndex).prop('checked')?1:0
						    });
						}
						else{
							kpiProdMapDataGroupArr.push({
						        "kpiGroupId": kpiGroupId,
						        "kpiDisplayIndex": $("#kpiList"+kpiIndex+" option:selected").val(),
						        "producerId": parseInt($("#producer"+kpiIndex+" option:selected").val()),
						        "availableForAnalytics": $("#kpiAvailAnalytics"+kpiIndex).prop('checked')?1:0,
						        "collectionInterval": $("#collIntervalUnit"+kpiIndex).val() == "minutes" ? parseInt($("#collInterval"+kpiIndex).val()) * 60 : parseInt($("#collInterval"+kpiIndex).val()),
						        "thresholdOperationId": $("#thresholdOperationsList"+kpiIndex).val() == "0" ? null : $("#thresholdOperationsList"+kpiIndex).val(),
						        "highSeverity": $("#highSeverity"+kpiIndex).val(),
						        "status": $("#status"+kpiIndex).prop('checked')?1:0
						    });
						}
					}
				}
				
			}

			if(self.errorMsg() == ""){
				//if(uiConstants.common.DEBUG_MODE)console.log(kpiProdMapDataArr);

				var kpiProdMapData = {
			    	"index": 1,
			    	"componentId": parseInt(self.selectedConfigRows()[0].componentId),
			    	"componentVersionId": parseInt(self.selectedConfigRows()[0].componentVersionId),
			    	"kpiDetails": kpiProdMapDataNonGroupArr,
			    	"kpiGroups": kpiProdMapDataGroupArr
				};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(kpiProdMapData));
				requestCall(uiConstants.common.SERVER_IP + "/componentKpiMapping", "PUT", JSON.stringify(kpiProdMapData), "updateKpiProducerMapping", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/572afece1300009802e2b7a2", "PUT", JSON.stringify(kpiProdMapData), "updateKpiProducerMapping", successCallback, errorCallback);
			}
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("Component Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		this.checkInactive = function(dataObj, configElement, configId){
			$(configElement+"_chosen span").prop("title",$(configElement+"_chosen span").text());
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function successCallback(data, reqType) {
			if(reqType === "getKpisList"){
				self.kpisList(data.result.kpiDetails);

				self.activeKpisList($.grep(data.result.kpiDetails, function(e){ return  e.status == 1; }));

				if(self.activeKpisList().length > 0){
					kpiGrpStartIndex = self.activeKpisList().length;
					self.kpisList(self.kpisList().concat(data.result.kpiGroups));
					self.activeKpisList(self.activeKpisList().concat($.grep(data.result.kpiGroups, function(e){ return  e.status == 1; })));
				}

				if(uiConstants.common.DEBUG_MODE)console.log(kpiGrpStartIndex);
				kpisLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getKpiProducerMapping"){
				//var producerMapDivHeight = $("body").height();
				self.kpiProdMapArr(data.result);
				//self.producerMasterArr(data.result.allProducers);
				//var kpiProducerMapping = data.result;

				if(data.result.kpiDetails){
					self.kpiProdMapFormattedArr($.grep(data.result.kpiDetails, function(e){ return  e.producers.defaultId!=undefined; }));
				}

				if(data.result.kpiGroups){
					self.kpiProdMapFormattedArr(self.kpiProdMapFormattedArr().concat($.grep(data.result.kpiGroups, function(e){ return  e.producers.defaultId!=undefined; })));
				}

				if(uiConstants.common.DEBUG_MODE)console.log(self.kpiProdMapFormattedArr());

				/*for(producer in producersChosenElementArr){
					$(producersChosenElementArr[producer] + "_chosen span").first().addClass("inactiveOptionClass").trigger('chosen:updated');
				}*/

				//$("#divKpiProducerMap").css("height", (producerMapDivHeight-51)+"px");
				//alert(producerMapDivHeight);
				//alert($(".panel-body").position().top);
				//alert($("#btnSave").offsetParent().height());

				$('#divKpiProducerMap').height(function(index, height) {
				    return window.innerHeight - $(this).offset().top-20;
				});

				producersMapLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getProducersMaster"){
				self.producerMasterArr([{"producerName" : "Select Producer", "producerId" : 0, "status": 1}]);
				self.producerMasterArr(self.producerMasterArr().concat(data.result));

				if(selectedProducerIdArr.length > 0){
					for(var producerId in selectedProducerIdArr){
						$("#producer"+producerId).trigger('chosen:updated');
						jQuery(".chosen").chosen({
							search_contains: true	
						});
					}


					for(var kpiProducer in selectedProducerIdArr){
						$("#producer"+kpiProducer).val(selectedProducerIdArr[kpiProducer]);
						$("#producer"+kpiProducer).trigger('chosen:updated');
					}
				}

				selectedProducerIdArr = [];
				//producersLoaded = 1;

				if(self.modalConfigName() != ""){
					var producerToSelectObj = $.grep(self.producerMasterArr(), function(e){return e.producerName == self.modalConfigName(); })[0];
					$("#producer"+producersToListRow).val(producerToSelectObj.producerId).trigger('chosen:updated');
					self.modalConfigName(""); 
				}
				else{
					setKpiProducerMapping();
				}
			}
			else if(reqType === "getProducerMappingForKpi"){
				var selDefaultProducerId = $("#producer"+producersToListRow).val();
				
				if(uiConstants.common.DEBUG_MODE)console.log(selDefaultProducerId);

				if(data.result.producers.all.length == 0){
					self.producerArr.splice(producersToListRow, 1, [{"value":"0"}]);
				}
				else{

					self.producerArr.splice(producersToListRow, 1, data.result.producers.all);
				}


				if(uiConstants.common.DEBUG_MODE)console.log(self.producerArr());

				/*var producerToSelectObj = $.grep(data.result.producers.all, function(e){return e.producerName == self.modalConfigName(); })[0];
				console.log(producerToSelectObj);

				if(selDefaultProducerId == "0" && self.modalConfigName()!=""){
					$("#producer"+producersToListRow).val(producerToSelectObj.producerId);
				}
				else{*/
					$("#producer"+producersToListRow).val(selDefaultProducerId);
				//}
				$("#producer"+producersToListRow+"_chosen").attr('style', function(i,s) { return s + 'width: ' + $("#producer"+producersToListRow+"_chosen").css("width") + ' !important;' });
				//$("#producer"+kpiProducer).trigger('chosen:updated');

				$("#producer"+producersToListRow).trigger('chosen:updated');
				$("#producer" + producersToListRow + "_chosen span").prop("title",$("#producer" + producersToListRow + "_chosen span").text());
				jQuery(".chosen").chosen({
					search_contains: true	
				});


				//self.modalConfigName("");
			}
			else if(reqType === "getOperationsList"){
				self.thresholdOperationsArr(data.result);
				thresholdOperationsLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "updateKpiProducerMapping"){
				var res = data.result;

				if(data.responseStatus.toUpperCase() == "FAILURE"){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined)
						showMessageBox(res[0].message, "error");
					else
						showMessageBox(data.message, "error");
				}
				else{
					showMessageBox(uiConstants.kpiProducerMap.SUCCESS_UPDATE_KPI_PRODUCER_MAP);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getKpiProducerMapping"){
				showMessageBox(uiConstants.kpiProducerMap, "error");
			}
			else if(reqType === "getKpisList"){
				showMessageBox(uiConstants.common.ERROR_GET_KPIS, "error");
			}
			else if(reqType === "getProducerMappingForKpi"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_GET_PRODUCERS_FOR_KPI, "error");
			}
			else if(reqType === "getOperationsList"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_THRESHOLD_OPERATIONS, "error");
			}
			else if(reqType === "updateKpiProducerMapping"){
				showMessageBox(uiConstants.kpiProducerMap.ERROR_UPDATE_KPI_PRODUCER_MAP, "error");
			}
		}
	}

	KpiProducerMapping.prototype.dispose = function() { };
	return { viewModel: KpiProducerMapping, template: templateMarkup };
});