<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default">
	<div class="configPanel panel-heading"><h4><span data-bind="text: pageSelected"></span></h4></div>
	<div class="panel-body">

		<form class="form-horizontal" role="form" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group">
				<div id="divMap" class="col-sm-12">
					<div id="divConfigBtn" style="padding-bottom: 2px">
						<button class="glyphicon glyphicon-plus" data-bind="event:{click: configureKpiProducerMap}" title="Add"></button>
					</div>
					<div class="wrapper-scroll-table" id="divKpiProducerMap" style="width:100%;">
						<table id="kpiProducerMapList" class="table table-bordered table-hover table-striped">
							<thead>
								<tr class="a1-inner-table-thead">
									<th class="tableHeaderOverflowOmmiter col-xs-2" rowspan="2">KPI Name <span class="mandatoryField">*</span></th>
									<th class="tableHeaderOverflowOmmiter col-xs-1" rowspan="2">KPI Unit</th>
									<th class="tableHeaderOverflowOmmiter" rowspan="2" style="width: 85px;">KPI Type</th>
									<th class="tableHeaderOverflowOmmiter" rowspan="2" style="width: 85px;"><input type="checkbox" id ="chkboxMappingAnalyticsHeader" title="Select All"/> Analytics</th>
									<th class="tableHeaderOverflowOmmiter" rowspan="2" style="width: 270px;">Default Producer <span class="mandatoryField">*</span></th>
									<th class="tableHeaderOverflowOmmiter" rowspan="2" style="width: 190px;" colspan="2">Collection Interval <span class="mandatoryField">*</span></th>
									<th colspan="2">
										Default Threshold
									</th>
									<th class="tableHeaderOverflowOmmiter" rowspan="2" style="width: 70px;"><input type="checkbox" id ="chkboxMappingStatusHeader" title="Select All"/> Status</th>
									<th class="tableHeaderOverflowOmmiter" style="width: 30px" rowspan="2"></th>
								</tr>

								<tr class="a1-inner-table-thead">
									<th>
										Operator
									</th>
									<th>
										HIGH Severity
									</th>
								</tr>
							</thead>
							<tbody data-bind="foreach : kpiMapRowsArr">
								<tr data-bind="visible: !$data.hidden">
									<td>
										<!-- ko if: !$data.existing -->
											<select class="chosen form-control" data-bind="foreach : $parent.activeKpisList, attr: {id: 'kpiList'+$index()}, event:{change: $parent.getProducersList.bind($data, $index()), 'chosen:showing_dropdown': $parent.onKpiLstOpen.bind($data,  $index()), 'chosen:hiding_dropdown': $parent.onKpiLstClose.bind($data,  $index())}" data-placeholder=" ">
												<!-- ko if: $index() == 0 -->
													<option data-bind="value: 0, text: 'Select KPI'"></option>
												<!-- /ko-->

												<!-- ko if: $data.kpiId -->
													<option data-bind="value: 'kpi'+$data.kpiId, text: $data.kpiName"></option>
												<!-- /ko-->

												<!-- ko if: $data.kpiGroupId -->
														<option data-bind="value: 'group'+$data.kpiGroupId, text: $data.kpiGroupName"></option>
												<!-- /ko-->
											</select>
										<!-- /ko-->

										<!-- ko if: $data.existing -->
											<select class="chosen" data-bind="foreach : $parent.kpisExistingList()[$index()], attr: {id: 'kpiList'+$index(), disabled: true}, event:{change: $parent.getProducersList.bind($data, $index())}" data-placeholder=" ">
												<!-- ko if: $index() == 0 -->
													<option data-bind="value: 0, text: 'Select KPI'"></option>
												<!-- /ko-->

												<!-- ko if: $data.kpiId -->
													<option data-bind="value: 'kpi'+$data.kpiId, text: $data.kpiName"></option>
												<!-- /ko-->

												<!-- ko if: $data.kpiGroupId -->
													<option data-bind="value: 'group'+$data.kpiGroupId, text: $data.kpiGroupName"></option>
												<!-- /ko-->
											</select>
										<!-- /ko-->
									</td>

									<td class="textOverflowOmmiter" style="text-align: center">
										<span data-bind="attr: {'id': 'kpiUnit'+$index()}">
										</span>
									</td>

									<td class="textOverflowOmmiter" style="text-align: center">
										<span data-bind="attr: {'id': 'kpiType'+$index()}">
										</span>
									</td>

									<td style="text-align:center" class="textOverflowOmmiter">
										<input class="chkboxMappingAnalyticsCol" type="checkbox" data-bind="attr: {'id': 'kpiAvailAnalytics'+$index()}"></input>
									</td>

									<td class="producerClass" style="display: inline-flex; width: 100%;">
										<select class="chosen form-control" data-bind="optionsValue: 'producerId', attr: {id: 'producer'+$index()}, options: $parent.getProducersMaster($index()),
	                   					optionsText: 'producerName',
	                   					value: 'producerId'" data-placeholder=" "></select>

										<button type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parent.setProducerModalParamData.bind($data, $index())}" data-toggle="modal" title="Add Producer" style="margin-left: 2px; top: -1px;"></button>
									</td>

									<td style="padding-left: 5px; border-right: none;">
										<input class="form-control" type="number" data-bind="attr: {'id': 'collInterval'+$index()}" min="1" value="1" style="width: 100%;"></input>
									</td>

									<td style="border-left: none; padding-left: 0px;">
										<select class="chosen form-control timeUnitSelector" data-bind="attr: {'id': 'collIntervalUnit'+$index()}" >
											<option value="seconds">Seconds</option>
											<option value="minutes" selected>Minute(s)</option>
										</select>
									</td>

									<td>
									<select class="chosen form-control" data-bind="attr: {id: 'thresholdOperationsList' + $index()}, foreach : $parent.thresholdOperationsArr">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: 'Select Operator'" title="Select Operator"></option>
											<!-- /ko-->

											<option data-bind="value: $data.masterId, text: $data.name, attr: {title: $data.name}"></option>
										</select>
									</td>

									<td>
										<input class="form-control" type="number" data-bind="attr: {'id': 'highSeverity'+$index()}" min="0"></input>
									</td>

									<td style="text-align:center">
										<input class="chkboxMappingStatusCol" type="checkbox" data-bind="attr: {'id': 'status'+$index()}" selected></input>
									</td>

									<td style="text-align:center">
										<!-- <span type="button" data-bind="attr: {disabled: $data.existing && $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}, css: {confButtonDisabled: $data.existing &&  $parent.kpisList()[$index()].isCustom == 0}" class="glyphicon glyphicon-remove buttondelete" title="Delete"></span> -->

										<span type="button" data-bind="attr: {disabled: $data.existing && $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}, css: {confButtonDisabled: $data.existing &&  $parent.kpiProdMapFormattedArr()[$index()].isCustom == 0}" class="glyphicon glyphicon-remove buttondelete" title="Delete" style="width: 15px;"></span>
									</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-2">
					<button id="btnSave" type ="button" class="btn btn-primary" data-bind="event:{click: updateKpiProducerMapping}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}">Cancel</button>
				</div>
			</div>

			<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" style="overflow: scroll;">
		   		<div class="modal-dialog" style="width: 80%">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4>Add Producer</h4>
			            </div>	
			            <div data-bind="if : displayConfig()">
			        		<producer-add-edit params="{isModal: true, selectedComponentTypeObj: selectedComponentTypeObj, selectedComponentObj: selectedComponentObj, selectedKpiObj: selectedKpiObj, isKpiGroup: isKpiGroup, selectedVersionObj: selectedVersionObj, componentsArr: componentsArr, currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows,
			        		modalConfigName: modalConfigName}"></producer-add-edit>
			        	</div>
			        </div>
		        </div>
		    </div>
		</form>
	</div>
</div>