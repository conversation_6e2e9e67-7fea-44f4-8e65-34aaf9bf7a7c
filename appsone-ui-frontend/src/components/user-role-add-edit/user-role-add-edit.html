<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divUserRoleAddEdit">
 	<!-- <div class="panel-heading" data-bind="visible: !isModal()"><h4><span data-bind="text: pageSelected"></span></h4></div> -->

 	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Allow Multiple Login for User</label>
				<div class="col-sm-4">
					<input type="checkbox" id="multipleLoginStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="multipleLoginStatus">
				</div>
			</div>

			<div id="divAuthorization" class="form-group form-required">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading" style="margin-bottom: 10px;">Authorization</div>
					<div class="panel-body wrapper-scroll-table" style="padding-top: 0px; padding-bottom: 0px; height: 500px;">
						<table id="permissionsTable" class="table table-hover table-striped a1-inner-table" style="width: auto;">
							<thead class="a1-inner-table-thead">
								<tr class="a1-inner-table-thead">
									<th style="width: 100%">Screens</th>
									<th style="min-width: 100px;">Read</th>
									<th style="min-width: 100px;">Create</th>
									<th style="min-width: 100px;">Update</th>
									<th style="min-width: 100px;">Delete</th>
								</tr>
							</thead>

							<tbody data-bind="foreach: screensArr, attr: {id: $data.screenId}">
								<tr>
									<td data-bind="text: $data.screenName"></td>
									<td style="text-align:center">
										<span style="display: none;" data-bind="attr:{'id': 'permissionId'+$data.screenId}"></span>
										<input type="checkbox" class="permission-chk" data-bind="attr: {'id': 'read'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
									</td>
									<td style="text-align:center">
										<input type="checkbox" class="permission-chk" data-bind="attr: {'id': 'create'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
									</td>
									<td style="text-align:center">
										<input type="checkbox" class="permission-chk" data-bind="attr: {'id': 'update'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
									</td>
									<td style="text-align:center">
									<!-- ko if: slugify($data.screenName).indexOf('role') == -1 -->
										<span style="font-style: italic;">--NA--</span>
									<!-- /ko -->

									<!-- ko if: slugify($data.screenName).indexOf('role') != -1 -->
										<input type="checkbox" class="permission-chk" data-bind="attr: {'id': 'delete'+$data.screenId}, click: $parent.handleAuthorizationChk.bind($data, $data.screenId)" selected></input>
									<!-- /ko -->
									</td>
								</tr>
							</tbody>
						</table>
					</div>

				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>