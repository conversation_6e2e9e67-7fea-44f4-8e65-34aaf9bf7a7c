define(['jquery','knockout','bootstrap-switch','typeahead','text!./user-role-add-edit.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

	function UserRoleAddEdit(params) {
		var self = this;
		
		this.screensArr = ko.observableArray();
		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable("");
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.currentViewIndex = params.currentViewIndex;
		this.pageSelected = params.pageSelected;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		this.multipleLoginStatus = ko.observable(1);

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*$(".wrapper").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
            });*/

			$("#multipleLoginStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Yes",
				'offText': "No"
			});

			$("#multipleLoginStatus").on('switchChange.bootstrapSwitch', function () {
				self.multipleLoginStatus($('#multipleLoginStatus').bootstrapSwitch('state')?1:0);
			});

			$("#txtName").focus();

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["userRoleName"]));
			}

			if(window.koGlobalMultipleLogin && window.koGlobalMultipleLogin() == 0){
				$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);

				$("[name='multipleLoginStatus']").closest('.bootstrap-switch')
        		.attr('title', uiConstants.userRoleConfig.MULTIPLE_LOGIN_OPTION_DISABLED_MSG).tooltip();
        		self.multipleLoginStatus(0);
			}

			if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				$('#multipleLoginStatus').bootstrapSwitch('state', self.multipleLoginStatus());
			}

			//requestCall("http://www.mocky.io/v2/580494bf240000672f135cc9?callback=?", "GET", "", "getScreenList", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/config/screens", "GET", "", "getScreenList", successCallback, errorCallback);
		}

        self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		//Adding/Updating single user role
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			self.configName(self.configName().trim());
			$("#divConfigDescription #txtDescription").val($("#divConfigDescription #txtDescription").val().trim());

			if(self.configName() == undefined || self.configName() == ""){
				//self.errorMsg(uiConstants.userRoleConfig.USER_ROLE_NAME_REQUIRED);
				showError("#divUserRoleAddEdit #txtName", uiConstants.userRoleConfig.USER_ROLE_NAME_REQUIRED);
			    self.errorMsg("#divUserRoleAddEdit #txtName");
			}
			else if(self.configName().length < 2){
				//self.errorMsg(uiConstants.userRoleConfig.USER_ROLE_NAME_MIN_LENGTH_ERROR);
				showError("#divUserRoleAddEdit #txtName", uiConstants.userRoleConfig.USER_ROLE_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divUserRoleAddEdit #txtName");
			}
			else if(self.configName().length > 45){
				//self.errorMsg(uiConstants.userRoleConfig.USER_ROLE_NAME_MAX_LENGTH_ERROR);
				showError("#divUserRoleAddEdit #txtName", uiConstants.userRoleConfig.USER_ROLE_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divUserRoleAddEdit #txtName");
			}
			else if(!nameValidation(self.configName())){
				//self.errorMsg(uiConstants.userRoleConfig.USER_ROLE_NAME_INVALID_ERROR);
				showError("#divUserRoleAddEdit #txtName", uiConstants.userRoleConfig.USER_ROLE_NAME_INVALID_ERROR);
			    self.errorMsg("#divUserRoleAddEdit #txtName");
			}
			if($("#divConfigDescription #txtDescription").val() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			else if($("#divConfigDescription #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			else if($("#divConfigDescription #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divConfigDescription #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divConfigDescription #txtDescription");
			}
			if($('input:checked.permission-chk').length == 0){
				//self.errorMsg(uiConstants.userRoleConfig.ERROR_SELECT_ROLE_PERMISSION);

				//$("#divUserRoleAddEdit #permissionsTable").children("*").css('title', uiConstants.userRoleConfig.ERROR_SELECT_ROLE_PERMISSION);
				$("#divUserRoleAddEdit #permissionsTable").find("td, th").attr("title", uiConstants.userRoleConfig.ERROR_SELECT_ROLE_PERMISSION);

				showError("#divUserRoleAddEdit #permissionsTable", uiConstants.userRoleConfig.ERROR_SELECT_ROLE_PERMISSION);
			    self.errorMsg("#divUserRoleAddEdit #permissionsTable");
			}
			else{
				$("#divUserRoleAddEdit #permissionsTable").find("td, th").removeAttr("title");
				removeError("#divUserRoleAddEdit #permissionsTable");
			}

			if(self.errorMsg() == ""){
				var permissionsArr = [];
				var screenId = 0;

				for(var screen in self.screensArr()){
					screenId = self.screensArr()[screen].screenId;

					permissionsArr.push({
						"id": $("#permissionId"+screenId).text(),
						"screenId": screenId,
						"readEnabled": $("#read"+screenId).prop("checked") ? 1 : 0,
						"createEnabled": $("#create"+screenId).prop("checked") ? 1 : 0,
						"updateEnabled": $("#update"+screenId).prop("checked") ? 1 : 0,
						"deleteEnabled": $("#delete"+screenId).prop("checked") ? 1 : 0
					});
				}

				var userRoleObj = {
					"index":1,
					"userRoleName": self.configName().trim(),
					"description": self.configDescription().trim(),
					"permissions": permissionsArr,
					"allowMultipleLogin": self.multipleLoginStatus()
				};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(userRoleObj));

				if(self.configId() == 0)
					requestCall(uiConstants.common.SERVER_IP + "/userRole", "POST", JSON.stringify(userRoleObj), "addSingleConfig", successCallback, errorCallback);
				else
					requestCall(uiConstants.common.SERVER_IP + "/userRole/" + self.configId(), "PUT", JSON.stringify(userRoleObj), "editSingleConfig", successCallback, errorCallback);
				}
		}

		this.handleAuthorizationChk = function(chkId){
			console.log($("#delete"+chkId).prop("checked"));
			if($("#delete"+chkId).prop("checked") || $("#create"+chkId).prop("checked") || $("#update"+chkId).prop("checked")){
				$("#read"+chkId).prop("checked", true);
			}

			return true;
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$("#divAuthorization").find("input").attr("disabled", "disabled");
			$("[name='multipleLoginStatus']").bootstrapSwitch('disabled',true);
			$("#divUserRoleAddEdit .chosen-container b").css("display", "none");
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
			}
			else{
				self.configName(configObj[0].userRoleName);
				self.configId(configObj[0].userRoleId);
				self.configDescription(configObj[0].description);
				self.multipleLoginStatus(configObj[0].allowMultipleLogin);
				$('#multipleLoginStatus').bootstrapSwitch('state', configObj[0].allowMultipleLogin);

				

			}
			//requestCall("http://www.mocky.io/v2/5804b37413000067036ab662?callback=?", "GET", "", "getUserRolePermissions", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/role/permissions/"+configObj[0].userRoleId, "GET", "", "getUserRolePermissions", successCallback, errorCallback);
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("User Role Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		/*function onMastersLoad(){
			if(screenListLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}*/

		function successCallback(data, reqType) {
			if(reqType === "getScreenList"){
				self.screensArr(data.result);

				var $tab = $('#permissionsTable');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
				//screenListLoaded = 1;

				//onMastersLoad();

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					viewConfig(self.selectedConfigRows());
				}
			}
  			else if(reqType === "getUserRolePermissions"){
  				if(data.result.permissions && data.result.permissions.length > 0){
  					var userRolePermissionsArr = data.result.permissions;

  					for(var userRolePermission in userRolePermissionsArr){
  						$("#permissionId"+userRolePermissionsArr[userRolePermission].screenId).text(userRolePermissionsArr[userRolePermission].id);
  						$("#read"+userRolePermissionsArr[userRolePermission].screenId).prop("checked", userRolePermissionsArr[userRolePermission].readEnabled == 1);
  						$("#create"+userRolePermissionsArr[userRolePermission].screenId).prop("checked", userRolePermissionsArr[userRolePermission].createEnabled == 1);
  						$("#update"+userRolePermissionsArr[userRolePermission].screenId).prop("checked", userRolePermissionsArr[userRolePermission].updateEnabled == 1);
  						$("#delete"+userRolePermissionsArr[userRolePermission].screenId).prop("checked", userRolePermissionsArr[userRolePermission].deleteEnabled
  						 == 1);
  					}
  				}
  			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_ROLE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userRoleConfig.ERROR_ADD_USER_ROLE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userRoleConfig.SUCCESS_ADD_USER_ROLE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_ROLE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.userRoleConfig.ERROR_UPDATE_USER_ROLE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.userRoleConfig.SUCCESS_UPDATE_USER_ROLE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getScreenList"){
  				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_SCREEN_LIST, "error");
  			}
  			else if(reqType === "getUserRolePermissions"){
				showMessageBox(uiConstants.userRoleConfig.ERROR_GET_USER_ROLE_PERMISSION, "error");
  			}
  			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.userRoleConfig.ERROR_ADD_USER_ROLE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.userRoleConfig.ERROR_UPDATE_USER_ROLE, "error");
			}
		}
	}

	UserRoleAddEdit.prototype.dispose = function() { };
	return { viewModel: UserRoleAddEdit, template: templateMarkup };
})