define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead', 'text!./transaction-add-edit.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

function Transactionaddedit(params) {
		var self = this;
		var masterListLoaded= 0;

		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(true);
		this.configId = ko.observable(0);
		
		//this.selectedConfigRows = params.selectedConfigRows;


		if(localStorage.selectedConfigRows)
			selectedConfigRows = JSON.parse(localStorage.selectedConfigRows);
		else
			selectedConfigRows = [];

		this.pageSelected = params.pageSelected;
		this.auditEnabled = ko.observable(false);
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.txnhttpPatternObject = ko.observable();
		this.txnTcpPatternObject = ko.observable();

		this.transactionTypeArr = params.transactionTypeArr;
		this.transactionMethodArr = params.transactionMethodArr;
		this.transactionResTypeArr = params.transactionResTypeArr;
		this.applicationId = params.applicationId;
		this.applicationName = params.applicationName;
		this.queryParamsArr = ko.observableArray();
		this.txnPatternArr = ko.observableArray();
		this.thresholdArr = ko.observableArray();
		this.tcpPatternArr = ko.observableArray();

		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();

		this.addSaveToggleLbl = ko.observable(uiConstants.transactionConfig.CONST_ADD);
		this.resetCancelToggleLbl = ko.observable(uiConstants.transactionConfig.CONST_RESET);
		this.selQueryParamArr = ko.observableArray();
		this.selectedTxnPatternRow = ko.observable(0);
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.mode = params && params.mode || '';
  		this.transactionName = ko.observable('');

  		this.bveArr=ko.observableArray();
  		this.auditExtractorArr=ko.observableArray();
  		this.extractorArr=ko.observableArray();
  		this.auditDataPartArr=ko.observableArray();
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.description = ko.observable();
		this.selectedTxnTypeId = ko.observable(0);
		this.selectedTxnType = ko.observable(0);
		this.httpPatternEditRow = ko.observable(-1);
		this.isFirstLoad = ko.observable(true);

		var typeSelected = "";
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];

  		var previousTxnTypeIdSelected = 0;
  		var transactionAuditTempArr = [];
		var counterValue=0;

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			///Jquery chosen start
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#transactionTypeList").trigger('chosen:updated');

			$('#formAddEdit').on('click', function(e){
		 		window.txnDetailsClicked(true);
		 	});

		 	$('#formAddEdit').on('change', function(){
		 		if(window.txnDetailsClicked()){
		 			window.txnDetailsChaged(true);
		 		}
		 	});

		 	$('#txtName').on('change', function(){
		 		window.txnDetailsChaged(true);
		 	});
			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			self.errorMsg("");

			$("#transactionMethodList").val(25).trigger('chosen:updated');

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW && selectedConfigRows.length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(selectedConfigRows, ["txnName"]));
			}

			self.selectedConfigNames(self.selectedConfigNames() + " for " + self.applicationName);

			self.errorMsg.subscribe(function(errorField) {
	        	if(self.firstFieldToShowErr() == ""){
		        	//scrollToPos(0, 300);
		        	self.firstFieldToShowErr(errorField);
		        	if(params.isModal){
			        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
			        }
		        	else{
			        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
		        	}
		        }
			});
	
			if(self.transactionTypeArr().length > 0 && self.transactionMethodArr().length  > 0 && self.transactionResTypeArr().length > 0){
				//onMastersLoad();
				masterListLoaded = 1;
			}

			$("#transactionTypeList").on('chosen:showing_dropdown', function () {
		        // Store the current value on focus and on change
		        previousTxnTypeIdSelected = this.value;
		        //$(this).blur();
		       // previousKpiTypeSelected =  $("#transactionTypeList option:selected").text();
		    }).change(function() {
		        // Do something with the previous value after the change
		    });

			/*$("#queryParameterList tbody").on('click', '.buttondelete', function(e){
				debugger;
				var tableObj = this;
			    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_QUERYPARAMS, "question", "confirm", function confirmCallback(confirmDelete){
	          		if (confirmDelete) {
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	self.queryParamsArr.splice(rowToDelete,1);
					}
				});
			});*/

			$("#thresholdList tbody").on('click', '.buttondelete', function(e){
				if(self.thresholdArr().length > 1){
					var tableObj = this;
				    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_THRESHOLD, "question", "confirm", function confirmCallback(confirmDelete){
		          		if (confirmDelete) {
						 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
						 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
						 	self.thresholdArr.splice(rowToDelete,1);

						 	$(".responseTypeListChosen").chosen('destroy');
			 				$(".responseTypeListChosen").trigger('chosen:updated');
						}
					});
				}
			});

			$("#tcpPatternList tbody").on('click', '.buttondelete', function(e){
				var tableObj = this;
			    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_HTTP_PATTERN, "question", "confirm", function confirmCallback(confirmDelete){
	          		if (confirmDelete) {
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	self.tcpPatternArr.splice(rowToDelete,1);
					}
				});
			});

			/*$("#txnPatternList tbody").on('click', '.buttondelete', function(e){
				var tableObj = this;
			    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_HTTP_PATTERN, "question", "confirm", function confirmCallback(confirmDelete){
	          		if (confirmDelete) {
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	self.txnPatternArr.splice(rowToDelete,1);
					 	self.clearTxnPatternList();
					}
				});
			});*/

			//bve delete button
			$("#bvePatternList tbody").on('click', '.buttondelete', function(e){
				var tableObj = this;
			    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_HTTP_PATTERN, "question", "confirm", function confirmCallback(confirmDelete){
	          		if (confirmDelete) {
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	self.bveArr.splice(rowToDelete,1);
					}
				});
			});

			//Adit extractor value delete button
			$("#divTxnAudit tbody").on('click', '.buttondelete', function(e){
				var tableObj = this;
			    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_HTTP_PATTERN, "question", "confirm", function confirmCallback(confirmDelete){
	          		if (confirmDelete) {
					 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
					 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
					 	self.auditExtractorArr.splice(rowToDelete,1);
					}
				});
			});

			
			
			$("#divQueryParams").css('display',$("#transactionMethodList option:selected").text().toUpperCase() == "GET" ? "block":"none");
			$("#transactionMethodList").on('change',function(e){
				$("#divQueryParams").css('display',$("#transactionMethodList option:selected").text().toUpperCase() == "GET" ? "block":"none");
			});


			//$("#divTxnPattern").css('display',$("#transactionTypeList option:selected").text().toUpperCase()  == uiConstants.transactionConfig.CONST_HTTP ? "block":"none");
			/*$("#transactionTypeList").on('click',function(e){
				$("#divTxnPattern").css('display',$("#transactionTypeList option:selected").text().toUpperCase() == uiConstants.transactionConfig.CONST_HTTP ? "block":"none");
			});*/
			
			//$("#divTxnTcpPattern").css('display',$("#transactionTypeList option:selected").text().toUpperCase() == uiConstants.transactionConfig.CONST_TCP ? "block":"none");
			/*$("#transactionTypeList").on('click',function(e){
				$("#divTxnTcpPattern").css('display',$("#transactionTypeList option:selected").text().toUpperCase() == uiConstants.transactionConfig.CONST_TCP ? "block":"none");
			});	*/

			//self.loadAuditDataPartOnclick($("#transactionTypeList option:selected").text().toUpperCase());

			$("#transactionTypeList").on('change',function(e){
				if(self.thresholdArr().length == 0){
					self.onThresholdAddClick();
					self.onTcpPatternAddClick();
					self.onBVEAddClick();
					//self.onClickofAddAuditValues();
				}
				self.getConfirmOnTypeChange(previousTxnTypeIdSelected);	
			});

			/*$("#txnPatternAddBtn").on('click',function(e){ 
				self.onTxnPatternAddclick(); 
			});*/

			//Set default one row for threshold,tcp pattern
			/*if((self.currentViewIndex() === uiConstants.common.ADD_SINGLE_VIEW) ){
				//$('#btnThresholdAdd').trigger('click');
				self.onThresholdAddClick();
				//$('#btnTcpPatternAdd').trigger('click');
				self.onTcpPatternAddClick();
				//$('#btnBVEAdd').trigger('click');
				self.onBVEAddClick();
				//$('#btnAuditValueAdd').trigger('click');
				self.onClickofAddAuditValues();
			}*/
			/*else if(self.currentViewIndex() === uiConstants.common.EDIT_VIEW){
				if(self.bveArr().length == 0)
					$('#btnBVEAdd').trigger('click');

				if(self.auditExtractorArr().length == 0)
					$('#btnAuditValueAdd').trigger('click');
			}
*/
			/*$("#txnPatternList tbody").on('click', '.buttonedit', function(e){
			 	rowToEdit = $(this).closest('tr').get(0).rowIndex-1;
			 	self.ontxnPatternListClick(self.txnPatternArr()[rowToEdit],rowToEdit);
			});
*/

			$('#txn-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#txn-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#txn-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			requestCall(uiConstants.common.SERVER_IP+ "/tag?type=Transaction", "GET", "", "getTransactionTag", successCallback, errorCallback);
			//assign audit data part to repected response type
		}

		this.deleteQueryParam = function(rowToDelete){
		    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_QUERYPARAMS, "question", "confirm", function confirmCallback(confirmDelete){
          		if (confirmDelete) {
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	self.queryParamsArr.splice(rowToDelete,1);
				}
			});
		}

		this.editHttpPattern = function(rowToEdit){
			self.ontxnPatternListClick(self.txnPatternArr()[rowToEdit],rowToEdit);
			self.httpPatternEditRow(rowToEdit);
		}

		this.deleteHttpPattern = function(rowToDelete){
		    showMessageBox(uiConstants.transactionConfig.DELETE_CONFIRM_HTTP_PATTERN, "question", "confirm", function confirmCallback(confirmDelete){
          		if (confirmDelete) {
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	self.txnPatternArr.splice(rowToDelete,1);
				 	self.clearTxnPatternList();
				}
			});
		}

        this.expandCollapsePanel = function(panelBody, expandCollpaseImg){
        	$(panelBody).toggle();

        	if($(panelBody).is(":hidden")){
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-bottom").addClass("glyphicon-triangle-right");
        		$(expandCollpaseImg).attr("title", "Expand");
        	}
        	else{
        		$(expandCollpaseImg).removeClass("glyphicon-triangle-right").addClass("glyphicon-triangle-bottom");
        		$(expandCollpaseImg).attr("title", "Collapse");
        	}
        }

		this.getConfirmOnTypeChange=function(previous){

			if(previous == 0){
				typeSelected=$('#transactionTypeList option:selected').text();
				self.selectedTxnTypeId($("#transactionTypeList").val());
				self.selectedTxnType($("#transactionTypeList option:selected").text().toUpperCase());
				self.loadTxnPatternOnClick(typeSelected);
				self.loadAuditDataPartOnclick(typeSelected);
				$("#responseTypeListChosen").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				/*var $tab = $('table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});*/
			}
			else{
				showMessageBox(uiConstants.transactionConfig.CONFIRM_TXN_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
					var typeTextValue = $("#transactionTypeList option:selected").text().toUpperCase();
					if(r){
						self.selectedTxnTypeId($("#transactionTypeList").val());
						self.selectedTxnType($("#transactionTypeList option:selected").text().toUpperCase());
						self.loadTxnPatternOnClick(typeTextValue);
						self.loadAuditDataPartOnclick(typeTextValue);
						self.auditExtractorArr([]);
						self.bveArr([]);


						//for(var thredhold=0; thredhold<self.thresholdArr().length-1; threshold++){
							self.thresholdArr([]);

						 	$(".responseTypeListChosen").chosen('destroy');
			 				$(".responseTypeListChosen").trigger('chosen:updated');
			 			//}

			 			self.onThresholdAddClick();

					}
					else{
						$("#transactionTypeList").val(previous).trigger('chosen:updated');
						typeSelected=$('#transactionTypeList option:selected').text();
						self.loadTxnPatternOnClick(typeSelected);
						self.loadAuditDataPartOnclick(typeSelected);
					}
				});
			}
		}

		this.loadTxnPatternOnClick = function(selectedType){
			if(selectedType == uiConstants.transactionConfig.CONST_TCP){
				self.txnPatternArr([]);
				$("#divTxnPattern").css('display',selectedType == uiConstants.transactionConfig.CONST_HTTP && self.selectedTxnTypeId() != 0 ? "block":"none");
				$("#divTxnTcpPattern").css('display',selectedType == uiConstants.transactionConfig.CONST_TCP && self.selectedTxnTypeId() != 0 ? "block":"none");

			}
			else if(selectedType == uiConstants.transactionConfig.CONST_HTTP){	
				self.tcpPatternArr([]);
				self.tcpPatternArr.push({"tcpStartPattern":"","length":"","tcpEndPattern":""});

				$("#divTxnTcpPattern").css('display',selectedType == uiConstants.transactionConfig.CONST_TCP && self.selectedTxnTypeId() != 0 ? "block":"none");
				$("#divTxnPattern").css('display',selectedType == uiConstants.transactionConfig.CONST_HTTP && self.selectedTxnTypeId() != 0 ? "block":"none");
				
			}
			else{
				$("#divTxnPattern").css('display',"none");
				$("#divTxnTcpPattern").css('display',"none");
			}


			var $tab = $('.tabListClass');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			$tab.floatThead('reflow');

			if(self.isFirstLoad()){
				self.isFirstLoad(false);
				$("#auditPanel").css("display", "none");
				$("#bvePanel").css("display", "none");
			}
		}

		this.loadAuditDataPartOnclick = function(selectedType){
			if(selectedType == uiConstants.transactionConfig.CONST_TCP){
				var txnAuditDataPartObj = $.grep(self.transactionTypeArr(), function(e){ return e.transactionTypeName == uiConstants.transactionConfig.CONST_TCP; });	
				self.auditDataPartArr( txnAuditDataPartObj[0].transactionAuditDataPart);
			}
			else{
				var txnAuditDataPartObj = $.grep(self.transactionTypeArr(), function(e){ return e.transactionTypeName == uiConstants.transactionConfig.CONST_HTTP; });	
				self.auditDataPartArr( txnAuditDataPartObj[0].transactionAuditDataPart);
			}
		}

		this.onClickofAddAuditValues = function(){
			counterValue=counterValue+1;
			self.auditExtractorArr.push({"counterId":counterValue,"extractorName":"","dataPartId":0,"extractorId":0,"extractorValueOne":"","extractorValueTwo":""});
			transactionAuditTempArr.push([]);
			self.onChangeTxnAuditDataPart(counterValue);

			$(".cmbAuditDataPartChosen").trigger('chosen:updated');
			$(".cmbExtractorChosen").trigger('chosen:updated');

			jQuery(".chosen").chosen({
				search_contains: true	
			});
		}

		this.onChangeTxnAuditDataPart=function(eleId){
			var txnAuditExtractionObj = $.grep(self.auditDataPartArr(), function(e){ if(e.dataPartId == $("#cmbAuditDataPart_"+eleId).val()) return e; });	
			transactionAuditTempArr.splice(eleId, 1, txnAuditExtractionObj[0].extractors);
			self.extractorArr(transactionAuditTempArr);

			$(".cmbExtractorChosen").trigger('chosen:updated');

			jQuery(".chosen").chosen({
				search_contains: true	
			});
		}

		this.onChangeTxnAuditExtractor=function(eleId){	

			switch($('#cmbExtractor_'+eleId+" option:selected").text()){
				  case "Query Param Extractor": $('#txtExtractorValue_'+eleId).attr("placeholder", "Enter Key");
				  								$('#txtExtractorValueTwo_'+eleId+'_Id').attr("placeholder", "");
				  								$('#txtExtractorValueTwo_'+eleId+'_Id').prop("disabled",true);
				  								$('#txtExtractorValue_'+eleId).attr("type","text");
											  	break;
				  case "Grouped Regex Extractor": $('#txtExtractorValue_'+eleId).attr("placeholder", "Enter Regular Expression");
				 								  $('#txtExtractorValueTwo_'+eleId+'_Id').attr("placeholder", "");
				  								  $('#txtExtractorValueTwo_'+eleId+'_Id').prop("disabled",true);
				  								  $('#txtExtractorValue_'+eleId).attr("type","text");
				  								  
				  								  if($("#transactionTypeList option:selected").text().toUpperCase() == uiConstants.transactionConfig.CONST_TCP){
				  								  	 $('.txtExtractorValueTwo_'+eleId+'_Id').val("");
				  								  }
				  								  break;
				  case "Form Group Extractor": $('#txtExtractorValue_'+eleId).attr("placeholder", "Enter Fieldname");
				  							   $('#txtExtractorValueTwo_'+eleId+'_Id').attr("placeholder", "");
				  							   $('#txtExtractorValueTwo_'+eleId+'_Id').prop("disabled",true);
				  							   $('#txtExtractorValue_'+eleId).attr("type","text");
				 							   break;
				  case "Form Data Extractor": $('#txtExtractorValue_'+eleId).attr("placeholder", "Enter Regular Expression");
				  							   $('#txtExtractorValueTwo_'+eleId+'_Id').attr("placeholder", "");
				  							   $('#txtExtractorValueTwo_'+eleId+'_Id').prop("disabled",true);
				  							   $('#txtExtractorValue_'+eleId).attr("type","text");
				 							   break;
				  case "Position And Length Extractor": $('#txtExtractorValue_'+eleId).attr("placeholder","Enter Offset");
				  										$('#txtExtractorValue_'+eleId).attr("type","number");

				  										$('#txtExtractorValueTwo_'+eleId+'_Id').attr("placeholder", "Enter Length");
				  										//$('.txtExtractorValueTwo_'+eleId).attr("type","number");
				  										$('#txtExtractorValueTwo_'+eleId+'_Id').prop("disabled",false);
				  										break;
			}	
			//$("#kpiTypeList").css("cursor", "default");
		}

		function onMastersLoad(){
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
				editSingleConfig(selectedConfigRows);
				if(!selectedConfigRows[0].status){ //if the component is inactive
					setConfigUneditable(true);
				}
			}

			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				cloneConfig(selectedConfigRows);
			}

			else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				if(uiConstants.common.DEBUG_MODE)console.log(selectedConfigRows);
				viewConfig(selectedConfigRows);
			}
		}

		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			//$("#txtName").val($("#txtName").val().trim());
			self.transactionName(self.transactionName().trim());
			$("#divTxnDescription #txtDescription").val($("#divTxnDescription #txtDescription").val().trim());
			self.thresholdArr.remove();
			//self.txnPatternArr.remove();
			//self.tcpPatternArr.remove();
			self.bveArr.remove();
			self.auditExtractorArr.remove();

			var selThresholdArr=[];
			var selTcpPatternObj={};
			var selBVEArr=[];
			var selTxnAuditArr=[];
			var tagsArr = [];
			var tagsObjArr = [];
			var isErrorExists = 0;


			if(self.transactionName() == undefined || self.transactionName() == ""){
				//self.errorMsg(uiConstants.transactionConfig.TXN_NAME_REQUIRED);
				showError("#txnAddEdit #txtName", uiConstants.transactionConfig.TXN_NAME_REQUIRED);
			    self.errorMsg("#txnAddEdit #txtName");
			}
			else if(self.transactionName().length < 2){
				//self.errorMsg(uiConstants.transactionConfig.TXN_NAME_MIN_LENGTH_ERROR);
				showError("#txnAddEdit #txtName", uiConstants.transactionConfig.TXN_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#txnAddEdit #txtName");
			}
			else if(self.transactionName().length > 45){
				//self.errorMsg(uiConstants.transactionConfig.TXN_NAME_MAX_LENGTH_ERROR);
				showError("#txnAddEdit #txtName", uiConstants.transactionConfig.TXN_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#txnAddEdit #txtName");
			}
			else if(!nameValidation(self.transactionName())){
				//self.errorMsg(uiConstants.transactionConfig.TXN_NAME_INVALID_ERROR);
				showError("#txnAddEdit #txtName", uiConstants.transactionConfig.TXN_NAME_INVALID_ERROR);
			    self.errorMsg("#txnAddEdit #txtName");
			}
			if($("#divTxnDescription #txtDescription").val() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divTxnDescription #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divTxnDescription #txtDescription");
			}
			else if($("#divTxnDescription #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divTxnDescription #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divTxnDescription #txtDescription");
			}
			else if($("#divTxnDescription #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divTxnDescription #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divTxnDescription #txtDescription");
			}

			if($("#txnAddEdit #transactionTypeList").val() == "0"){
				showError("#txnAddEdit #transactionTypeList_chosen", uiConstants.transactionConfig.SELECT_TXN_TYPE_TYPE_MSG);
				showError("#txnAddEdit #transactionTypeList_chosen span", uiConstants.transactionConfig.SELECT_TXN_TYPE_TYPE_MSG);
			    self.errorMsg("#txnAddEdit #transactionTypeList_chosen");
			}
			
			removeError("#txnAddEdit .tokenfield");
			removeError("#txnAddEdit #txn-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#txn-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#txnAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#txnAddEdit #txn-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#txnAddEdit .tokenfield");
			}
			else{
				//Tag validation
				if(self.tags() && self.tags().trim().length == 1){
					tagsArr.push(self.tags());
				}
				else if(self.tags() && self.tags().trim().length > 1){
					tagsArr = self.tags().split(",");
				}
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#txnAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#txnAddEdit #txn-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#txnAddEdit .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#txnAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#txnAddEdit #txn-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#txnAddEdit .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#txnAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#txnAddEdit #txn-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#txnAddEdit .tokenfield");
						break;
					}
				}
			}
			//if(self.errorMsg() == ""){
				
				if(self.errorMsg() == "" && $("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_HTTP && self.txnPatternArr().length <= 0){
					isErrorExists = 1;
					showMessageBox(uiConstants.transactionConfig.REQUEST_ADD_PATTERN, "error");
					self.errorMsg("#txnAddEdit #divTxnPattern");
					self.errorMsg("");
				}
				else if(self.errorMsg() == "" && $("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_TCP && self.tcpPatternArr().length <= 0){// || self.tcpPatternArr()[0].tcpStartPattern == "" )){
					isErrorExists = 1;
					showMessageBox(uiConstants.transactionConfig.REQUEST_ADD_PATTERN, "error");
					self.errorMsg("#txnAddEdit #divTxnTcpPattern");
					self.errorMsg("");
				}
					//http pattern validation
					/*if($("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_HTTP && self.errorMsg() == "" && self.txnPatternArr().length > 0){
						var flagStartPattern = 0;
						var rowIndx = 0;
						
						if(self.txnPatternArr().length >= 2){
							for (var i = 0; i < self.txnPatternArr().length-1; i++) {
								if((self.txnPatternArr()[i]['httpTxnConfig'].type == self.txnPatternArr()[i+1]['httpTxnConfig'].type) 
									&& (self.txnPatternArr()[i]['httpTxnConfig'].urlPattern == self.txnPatternArr()[i+1]['httpTxnConfig'].urlPattern) 
									&& ((self.txnPatternArr()[i]['httpTxnConfig'].headerPattern == null?"":self.txnPatternArr()[i]['httpTxnConfig'].headerPattern) == (self.txnPatternArr()[i+1]['httpTxnConfig'].headerPattern == null?"":self.txnPatternArr()[i+1]['httpTxnConfig'].headerPattern))
									&& ((self.txnPatternArr()[i]['httpTxnConfig'].bodyPattern == null?"":self.txnPatternArr()[i]['httpTxnConfig'].bodyPattern) == (self.txnPatternArr()[i+1]['httpTxnConfig'].bodyPattern == null?"":self.txnPatternArr()[i+1]['httpTxnConfig'].bodyPattern))){
									flagStartPattern = 1;
									rowIndx = (i+1);
									break;
								}


							}
						}
						if(self.tcpPatternArr().length<0){
							showMessageBox(uiConstants.transactionConfig.REQUEST_ADD_PATTERN+" at line "+(rowIndx+1), "error");
						}
						else if(flagStartPattern == 1){
							self.errorMsg(uiConstants.transactionConfig.ERROR_SAME_HTTP_PATTERN+" at line "+(rowIndx+1));
						}
						else{
							self.errorMsg("");
						}
					}*/

					//tcp pattern validation
					if($("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_TCP && self.tcpPatternArr().length > 0){
						var flagStartPattern = 0;
						var rowIndx = 0;
						for (var i = 0; i <= self.tcpPatternArr().length-1; i++) {
							var j=i+1;
							if($("#txtStartPattern"+i).val() == ""){
								showError("#txtStartPattern"+i, uiConstants.transactionConfig.START_PATTERN_REQUIRED);
							    self.errorMsg("#txtStartPattern"+i);
							}
							if($("#txtLength"+i).val() != "" && $("#txtEndPattern"+i).val() == "" ){
								showError("#txtEndPattern"+i, uiConstants.transactionConfig.END_PATTERN_REQUIRED);
							    self.errorMsg("#txtEndPattern"+i);
							}
							if(($("#txtStartPattern"+i).val() === $("#txtStartPattern"+j).val()) && ($("#txtLength"+i).val() === $("#txtLength"+j).val()) && ($("#txtEndPattern"+i).val() === $("#txtEndPattern"+j).val())){
								if($("#txtStartPattern"+i).val() != "" && $("#txtStartPattern"+i).val() === $("#txtStartPattern"+j).val()){
									showError("#txtStartPattern"+j, uiConstants.transactionConfig.ERROR_SAME_TCP_PATTERN);
							    	self.errorMsg("#txtStartPattern"+j);
								}

								if($("#txtLength"+i).val() != "" && $("#txtLength"+i).val() === $("#txtLength"+j).val()){
									showError("#txtLength"+j, uiConstants.transactionConfig.ERROR_SAME_TCP_PATTERN);
							    	self.errorMsg("#txtLength"+j);
								}

								if($("#txtEndPattern"+i).val() != "" && $("#txtEndPattern"+i).val() === $("#txtEndPattern"+j).val()){
									showError("#txtEndPattern"+j, uiConstants.transactionConfig.ERROR_SAME_TCP_PATTERN);
							    	self.errorMsg("#txtEndPattern"+j);
								}
							}
						}
					}

					if(self.errorMsg() == "" && $("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_TCP){
						for(patternIndx in self.tcpPatternArr()){
							selTcpPatternObj={
													"tcpStartPattern":$('#txtStartPattern'+patternIndx).val(),//self.tcpPatternArr()[patternIndx].tcpStartPattern,
													"length":  parseInt($('#txtLength'+patternIndx).val()),
													"tcpEndPattern": $('#txtEndPattern'+patternIndx).val()
											};
							var obj={
								"httpTxnConfig":null,
								"tcpTxnConfig":selTcpPatternObj
							}				
							self.txnPatternArr().push(obj);
						}
					}

					var respTimeTypeArr = [];
					for (var k = 0; k < self.thresholdArr().length; k++) {
						respTimeTypeArr.push($("#responseTypeList_"+k).val());
					}

					//Response type validation
				    //if(self.errorMsg() == ""){
						for (var k = 0; k < self.thresholdArr().length; k++) {
							//respTimeTypeArr.splice(0, 1);
							if(self.thresholdArr().length >= 2){
								for(var i=0; i<self.thresholdArr().length; i++){
									if(i!=k && respTimeTypeArr[i] == $("#responseTypeList_"+k).val()){
										showError("#txnAddEdit #responseTypeList_"+k+"_chosen", uiConstants.transactionConfig.ERROR_SAME_THRESHOLD_TYPE);
										showError("#txnAddEdit #responseTypeList_"+k+"_chosen span", uiConstants.transactionConfig.ERROR_SAME_THRESHOLD_TYPE);
							   			self.errorMsg("#txnAddEdit #responseTypeList_"+k+"_chosen");

							   			break;
									}
								}
								
							}

							if(self.thresholdArr()[k].threshold > 999999){
								showError("#txnAddEdit #txtResponseValue"+k, uiConstants.transactionConfig.ERROR_FOR_RESPONSE_VAL);
							    self.errorMsg("#txnAddEdit #txtResponseValue"+k);
							}
							else if(self.thresholdArr()[k].threshold == "" || self.thresholdArr()[k].threshold == 0){
								showError("#txnAddEdit #txtResponseValue"+k, uiConstants.transactionConfig.ERROR_FOR_EMPTY_RESPONSE_VAL);
							    self.errorMsg("#txnAddEdit #txtResponseValue"+k);
							}
						}
					//}

					//threshold validation
					if(self.errorMsg() == ""){
						for(thresholdIdx in self.thresholdArr()){
							selThresholdArr.push({
								"responseTypeId": parseInt($("#responseTypeList_"+thresholdIdx).val()),//self.thresholdArr()[thresholdIdx].responseTypeId,
								"responseType":  $("#responseTypeList_"+thresholdIdx+" option:selected").text(),
								"threshold": parseInt(self.thresholdArr()[thresholdIdx].threshold)
							});
						}

						/*if(selThresholdArr.length == 0){
							self.errorMsg(" Threshold Error : Please provide input for thresholds");
						}*/
					}

					var bveNameArr = [];
					for (var k = 0; k < self.bveArr().length; k++) {
						bveNameArr.push(self.bveArr()[k].extractorName);
					}

					for(var k=0; k<self.bveArr().length; k++){
						for(var i=0; i<self.bveArr().length; i++){
							if(i!=k && $("#txtExtractorName"+k).val() != "" && bveNameArr[i] == $("#txtExtractorName"+k).val()){
								showError("#txnAddEdit #txtExtractorName"+k, uiConstants.transactionConfig.ERROR_SAME_BVE_NAME);
					   			self.errorMsg("#txnAddEdit #txtExtractorName"+k);
					   			break;
							}
						}

						if( self.bveArr()[k].extractorName == "" && self.bveArr()[k].regExString != ""){
							//self.errorMsg("Business Value Extractor Error : "+uiConstants.transactionConfig.ERROR_EXTRACTORNAME+" at line "+(i+1));
							showError("#txnAddEdit #txtExtractorName"+k, uiConstants.transactionConfig.ERROR_EXTRACTORNAME);
					   		self.errorMsg("#txnAddEdit #txtExtractorName"+k);
						}
						if( self.bveArr()[k].extractorName != "" && self.bveArr()[k].regExString == ""){
							//self.errorMsg("Business Value Extractor Error : "+uiConstants.transactionConfig.ERROR_EXYTRACTOR_PATTERN+" at line "+(i+1));
							showError("#txnAddEdit #txtRegExpString"+k, uiConstants.transactionConfig.ERROR_EXYTRACTOR_PATTERN);
					   		self.errorMsg("#txnAddEdit #txtRegExpString"+k);
						}
					}


					//BVE validation
					/*for (i = 0; i < self.bveArr().length-1; i++) {
						if((self.bveArr()[i].extractorName != "" && self.bveArr()[i+1].extractorName != "") && (self.bveArr()[i].extractorName ==  self.bveArr()[i+1].extractorName)){
							self.errorMsg("Business Value Extractor Error : "+uiConstants.transactionConfig.ERROR_SAME_BVE_NAME+" at line "+(i+1));
						}
						
					}

					for (i = 0; i < self.bveArr().length; i++) {
						
					}*/

					//BVE data validation
					if(self.errorMsg() == ""){
						for(indx in self.bveArr()){
							if(self.bveArr()[indx].extractorName != "" && self.bveArr()[indx].regExString != ""){
								selBVEArr.push({
													"bizValueId": isNaN(parseInt(self.bveArr()[indx].bizValueId))?0:parseInt(self.bveArr()[indx].bizValueId),
													"extractorName": self.bveArr()[indx].extractorName,
													"regExString": self.bveArr()[indx].regExString
												});
							}
							
						}

						if(selBVEArr.length == 0){
							selBVEArr=[];
						}
					}


					if($('#auditEnabled').prop('checked')){
						var auditNameArr = [];
						for (var k = 0; k < self.auditExtractorArr().length; k++) {
							auditNameArr.push(self.auditExtractorArr()[k].extractorName);
						}

						for(var k=0; k<self.auditExtractorArr().length; k++){
							for(var i=0; i<self.auditExtractorArr().length; i++){
								if(i!=k && $("#txtAuditExtractorName"+k).val() != "" && auditNameArr[i] == $("#txtAuditExtractorName"+k).val()){
									showError("#txnAddEdit #txtAuditExtractorName"+k, uiConstants.transactionConfig.ERROR_SAME_BVE_NAME);
						   			self.errorMsg("#txnAddEdit #txtAuditExtractorName"+k);
						   			break;
								}
							}

							if($("#txtAuditExtractorName"+k).val() == ""){
								//self.errorMsg("Business Value Extractor Error : "+uiConstants.transactionConfig.ERROR_EXTRACTORNAME+" at line "+(i+1));
								if($("#auditPanel").is(":hidden")){
	        						self.expandCollapsePanel("#auditPanel", "#imgAttribShowHide")
						   		}

						   		showError("#txnAddEdit #txtAuditExtractorName"+k, uiConstants.transactionConfig.ERROR_EXTRACTOR_NAME);
						   		self.errorMsg("#txnAddEdit #txtAuditExtractorName"+k);
							}

							if($("#txtExtractorValue_"+self.auditExtractorArr()[k].counterId).val() == ""){
								//self.errorMsg("Business Value Extractor Error : "+uiConstants.transactionConfig.ERROR_EXTRACTORNAME+" at line "+(i+1));
								if($("#bvePanel").is(":hidden")){
	        						self.expandCollapsePanel("#bvePanel", "#imgBveAttribShowHide")
						   		}

						   		showError("#txnAddEdit #txtExtractorValue_"+self.auditExtractorArr()[k].counterId, uiConstants.transactionConfig.ERROR_EXTRACTOR_VALUE);
						   		self.errorMsg("#txnAddEdit #txtExtractorValue_"+self.auditExtractorArr()[k].counterId);
							}
						}

						//Txn Audit data validation
						for(indx in self.auditExtractorArr()){
							if(self.auditExtractorArr()[indx].extractorName != "" && $("#cmbExtractor_"+self.auditExtractorArr()[indx].counterId).val() != 0 && self.auditExtractorArr()[indx].extractorValueOne != ""){
								selTxnAuditArr.push({
													"txnAuditId": isNaN(parseInt(self.auditExtractorArr()[indx].txnAuditId))?0:parseInt(self.auditExtractorArr()[indx].txnAuditId),
													"extractorName": self.auditExtractorArr()[indx].extractorName,
													"dataPartId": parseInt($("#cmbAuditDataPart_"+self.auditExtractorArr()[indx].counterId).val()), //isNaN( parseInt($("#cmbAuditDataPart_"+indx).val()))?0:parseInt($("#cmbAuditDataPart_"+indx).val()), //self.auditExtractorArr()[indx].dataPartId,
													"extractorId": parseInt($("#cmbExtractor_"+self.auditExtractorArr()[indx].counterId).val()),//isNaN( parseInt($("#cmbExtractor_"+indx).val()))?0:parseInt($("#cmbExtractor_"+indx).val()),
													"extractorValueOne": self.auditExtractorArr()[indx].extractorValueOne,
													"extractorValueTwo": self.auditExtractorArr()[indx].extractorValueTwo
								});
							}
						}
					}

					if(self.errorMsg() == ""){

						if($('#auditEnabled').prop('checked') == true && selTxnAuditArr.length == 0){
							if($("#auditPanel").is(":hidden")){
        						self.expandCollapsePanel("#auditPanel", "#imgAttribShowHide")
					   		}

							isErrorExists = 1;
							showMessageBox("Please provide Audit Value Extractor details", "error");
							self.errorMsg("#txnAddEdit #divTxnAudit");
							self.errorMsg("");

						}
						else if(selTxnAuditArr.length == 0){
							selTxnAuditArr=[];
						}
					}

					if(isErrorExists == 0 && self.errorMsg() == ""){
						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":0, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}

						if(self.addSaveToggleLbl() != uiConstants.transactionConfig.CONST_UPDATE ){
							var compObj =  [{
								"index":1,
								"txnId": parseInt(self.configId()),
								"txnName": self.transactionName(),
								"description": self.description(),
								"txnTypeId": parseInt($("#transactionTypeList").val()),
								"txnType" : $("#transactionTypeList option:selected").text(),
								"subTransactions":  self.txnPatternArr(),
								"bizValueExtractorList" : selBVEArr,
								"txnAuditDetails" : selTxnAuditArr,
								"applicationId": parseInt(self.applicationId),
						      	"isAutoConfigured": false,
						      	"isAuditEnabled": self.auditEnabled()?true:false,//$('#auditEnabled').prop('checked')?true:false,
						      	"txnThresholds": selThresholdArr,
								"tags": tagsObjArr,
								"status" : self.configStatus()?true:false}];

							if(uiConstants.common.DEBUG_MODE)console.log("==========================Txn configuration:onsave=======================");
							if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify({"transactions": compObj}));

							if(self.configId() == 0)
								requestCall(uiConstants.common.SERVER_IP + "/transaction", "POST", JSON.stringify({"transactions": compObj}), "addSingleConfig", successCallback, errorCallback);
							else
								requestCall(uiConstants.common.SERVER_IP + "/transaction/" + self.configId(), "PUT", JSON.stringify({"transactions": compObj}), "editSingleConfig", successCallback, errorCallback);
							
						}
						else{
							self.errorMsg(uiConstants.transactionConfig.HTTP_PATTERN_UPDATE_ERROR);
						}
					}

				
			//}
			
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log("======================Txn configuration : onEdit=================================");
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}
			
			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				$("#txtName").val("");
				self.description("");
			}
			else{
				$("#txtName").val(configObj[0].txnName);
				self.transactionName(configObj[0].txnName);
				self.description(configObj[0].description);
			}
		 	window.txnDetailsChaged(false);


			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configId(0);
			}
			else{
				self.configId(configObj[0].txnId);
			}

			$("#transactionTypeList").prop('disabled',true);
			$("#transactionTypeList").val(configObj[0].txnTypeId).trigger('chosen:updated');
			self.selectedTxnTypeId(configObj[0].txnTypeId);
			self.selectedTxnType($.grep(self.transactionTypeArr(), function(evt){ return evt.transactionTypeId == configObj[0].txnTypeId; })[0].transactionTypeName.toUpperCase());

			//$("#transactionTypeList").trigger('change');
			self.loadTxnPatternOnClick($("#transactionTypeList option:selected").text().toUpperCase());
			self.loadAuditDataPartOnclick($("#transactionTypeList option:selected").text().toUpperCase());

			if($("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_HTTP && configObj[0].subTransactions != null ){
				self.txnPatternArr(configObj[0].subTransactions);
				//self.setTxnPatternValues(self.txnPatternArr()[0]);
			}

			if($("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_TCP && configObj[0].subTransactions != null ){
				if(uiConstants.common.DEBUG_MODE)console.log("----------------------------");
				if(uiConstants.common.DEBUG_MODE)console.log(configObj[0].subTransactions);
				self.tcpPatternArr(configObj[0].subTransactions);
				self.setTxnPatternValues(self.tcpPatternArr());
			}
			else{

			}

			//set threshold values
			var selRespArr = configObj[0].txnThresholds;
			for(var indx in selRespArr){
				self.thresholdArr.push({	
					"threshold": selRespArr[indx].threshold,
					"responseTypeId":  parseInt(selRespArr[indx].responseTypeId),
					"responseTypeName": selRespArr[indx].responseTypeName					
				});

				debugger;

				$("#responseTypeList_"+ indx).val(selRespArr[indx].responseTypeId).trigger('chosen:updated');

				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}

			//set bve values
			var selBVERespArr = configObj[0].bizValueExtractorList;
			for(var indx in selBVERespArr){
				self.bveArr.push({	
					"bizValueId": selBVERespArr[indx].bizValueId,
					"extractorName":  selBVERespArr[indx].extractorName,
					"regExString": selBVERespArr[indx].regExString					
				});
			}

			//set audit data values
			var selTxnAuditRespArr = configObj[0].txnAuditDetails;

			/*if(selTxnAuditRespArr.length == 0){
				$("#cmbAuditDataPart_1")[0].selectedIndex = 0;
				$('#cmbAuditDataPart_1').trigger('change');
			}
			else{*/
				for(var indx in selTxnAuditRespArr){
					self.auditExtractorArr.push({
						"counterId":counterValue++,
						"txnAuditId": selTxnAuditRespArr[indx].txnAuditId,
						"extractorName": selTxnAuditRespArr[indx].extractorName,
						"dataPartId": parseInt(selTxnAuditRespArr[indx].dataPartId),
						"extractorId": parseInt(selTxnAuditRespArr[indx].extractorId),
						"extractorValueOne": selTxnAuditRespArr[indx].extractorValueOne,
						"extractorValueTwo": selTxnAuditRespArr[indx].extractorValueTwo
					});
				}

				if(self.auditExtractorArr().length > 0){
					for (var eleId in self.auditExtractorArr()){
						$('#cmbAuditDataPart_'+self.auditExtractorArr()[eleId].counterId).val(self.auditExtractorArr()[eleId].dataPartId).trigger('chosen:updated');
						$('#cmbAuditDataPart_'+self.auditExtractorArr()[eleId].counterId).trigger('change');
						$('#cmbExtractor_'+self.auditExtractorArr()[eleId].counterId).val(self.auditExtractorArr()[eleId].extractorId).trigger('chosen:updated');
						$('#cmbExtractor_'+self.auditExtractorArr()[eleId].counterId).trigger('change');
					}

					$(".cmbAuditDataPartChosen").trigger('chosen:updated');
					$(".cmbExtractorChosen").trigger('chosen:updated');

					jQuery(".chosen").chosen({
						search_contains: true	
					});

					debugger;

					var $tab = $('.tabListClass');
					$tab.floatThead({
						scrollContainer: function($table){
							return $table.closest('.wrapper-scroll-table');
						}
					});

					$("#auditPanel").css("display", "none");
				}
			//}
			

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW && configObj[0].status == 0){
				self.configStatus(!configObj[0].status);
			}
			else{
				self.configStatus(configObj[0].status);
			}
			$('#configStatus').bootstrapSwitch('state',self.configStatus());

			self.auditEnabled(configObj[0].isAuditEnabled);
			$('#txn-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
		}

		this.setTxnPatternValues = function(data){
			if($("#transactionTypeList option:selected").text().toUpperCase() === uiConstants.transactionConfig.CONST_HTTP){
				self.addSaveToggleLbl(uiConstants.transactionConfig.CONST_UPDATE);
				self.resetCancelToggleLbl(uiConstants.common.CONST_CANCEL);
				$("#transactionMethodList").val(data['httpTxnConfig'].typeId).trigger('chosen:updated');
				$("#transactionMethodList").trigger('change');
				$("#txtUrl").val(data['httpTxnConfig'].urlPattern);
				self.queryParamsArr(data['httpTxnConfig'].queryParam);
				$("#txtHeaderPattern").val(data['httpTxnConfig'].headerPattern);
				$("#txtBodyPattern").val(data['httpTxnConfig'].bodyPattern);
			}
			else{
				$('#tcpPatternList tbody tr').each(function(i){
					$('#txtStartPattern'+i).val(data[i]['tcpTxnConfig'].tcpStartPattern);
					$('#txtLength'+i).val(data[i]['tcpTxnConfig'].length);
					$('#txtEndPattern'+i).val(data[i]['tcpTxnConfig'].tcpEndPattern);
				});
			}
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);	
			editSingleConfig(configObj);
			setConfigUneditable(false);		
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$('#txn-tokenfield-typeahead').tokenfield('readonly');
			$('#transactionTypeList').prop('disabled', true).trigger('chosen:updated');
			$('#auditEnabled').prop('disabled', true);
			$("#divTxnPattern").find("input,button,select,textarea").attr("disabled", "disabled");
			//$("#divTxnPattern").find("span").css("visibility", "hidden");

			$("#divTxnTcpPattern").find("input,button,select").attr("disabled", "disabled");
			//$("#divTxnTcpPattern").find("span").css("visibility","hidden");

			$("#divTxnThresholds").find("input,button,select").attr("disabled", "disabled");
			$(".responseTypeListChosen").attr("disabled", "disabled").trigger('chosen:updated');
			//$("#divTxnThresholds").find("span").css("visibility","hidden");

			$("#divTxnBVE").find("input,button").attr("disabled", "disabled");
			//$("#divTxnBVE").find("span").css("visibility","hidden");

			$("#divTxnAudit").find("input,button,select").attr("disabled", "disabled");
			//$("#divTxnAudit").find("span").css("visibility","hidden");
			
			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
			}

			$("#txnAddEdit .chosen-container b").css("display", "none");
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		this.cancelConfig = function(){
			selectedConfigRows=[];
			localStorage.selectedConfigRows = JSON.stringify(selectedConfigRows);
			self.pageSelected("Transaction Configuration");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
			uicommon.postbox.publish(uiConstants.common.LIST_VIEW,"changeViewToList");
			self.errorMsg("");
			counterValue=0;
		}

		this.ontxnPatternListClick = function(data,selectedRow){
			self.selectedTxnPatternRow=selectedRow;
			self.setTxnPatternValues(data);
		}

		this.onQueryParamAddClick = function(){
			self.queryParamsArr.push({"key":"","value":""});
		}

		this.onThresholdAddClick = function(){
			if(self.thresholdArr().length > self.transactionResTypeArr().length-1){
				showMessageBox(uiConstants.transactionConfig.THRESOLD_ADDLIMIT_ERROR, "error");
			}
			else{
				self.thresholdArr.push({"responseTypeId":1,"responseType":"","threshold":"5000"});
			}
			$(".responseTypeListChosen").trigger('chosen:updated');

			jQuery(".chosen").chosen({
				search_contains: true	
			});
		}

		this.onBVEAddClick = function(){
			self.bveArr.push({"extractorName":"","regExString":""});
		}

		this.onTxnPatternAddclick = function(){

			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError("#divTxnPattern input");
			if($("#divTxnPattern #transactionMethodList").val() == "0"){
			    showError("#divTxnPattern #transactionMethodList_chosen", "Please select method type");
				showError("#divTxnPattern #transactionMethodList_chosen span", "Please select method type");
			    self.errorMsg("#divTxnPattern #transactionMethodList_chosen");
			}

			if($("#txtUrl").val() == undefined || $("#txtUrl").val() == ""){
				showError("#divTxnPattern #txtUrl", "Please enter URL");
			    self.errorMsg("#divTxnPattern #txtUrl");
			}

			for(var queryParam in self.queryParamsArr()){
				if($("#divTxnPattern #txtQueryParamKey"+queryParam).val() == ""){
					showError("#divTxnPattern #txtQueryParamKey"+queryParam, "Please enter Query Parameter Key");
			   		self.errorMsg("#divTxnPattern #txtQueryParamKey"+queryParam);
				}

				if($("#divTxnPattern #txtQueryParamValue"+queryParam).val() == ""){
					showError("#divTxnPattern #txtQueryParamValue"+queryParam, "Please enter Query Parameter Value");
			   		self.errorMsg("#divTxnPattern #txtQueryParamValue"+queryParam);
				}
			}

			if(self.errorMsg() == "") {

				var flagStartPattern = 0;
				if(self.txnPatternArr().length > 0){
					var rowIndx = 0;
					
					if(self.txnPatternArr().length >= 1){
						for (var i = 0; i < self.txnPatternArr().length; i++) {
							if(self.httpPatternEditRow()!=i && (self.txnPatternArr()[i]['httpTxnConfig'].type == $("#transactionMethodList option:selected").text()) 
								&& (self.txnPatternArr()[i]['httpTxnConfig'].urlPattern == $("#txtUrl").val()) 
								&& ((self.txnPatternArr()[i]['httpTxnConfig'].headerPattern == null?"":self.txnPatternArr()[i]['httpTxnConfig'].headerPattern) == ( $("#txtHeaderPattern").val() == null?"": $("#txtHeaderPattern").val()))
								&& ((self.txnPatternArr()[i]['httpTxnConfig'].bodyPattern == null?"":self.txnPatternArr()[i]['httpTxnConfig'].bodyPattern) == ($("#txtBodyPattern").val() == null?"":$("#txtBodyPattern").val()))){
								flagStartPattern = 1;
								rowIndx = (i+1);
								break;
							}


						}
					}
					if(flagStartPattern == 1){
						//alert(uiConstants.transactionConfig.ERROR_SAME_HTTP_PATTERN+" at line "+(rowIndx+1));
						showMessageBox("Same HTTP pattern is not allowed", "error");
						self.errorMsg("#txnAddEdit #divTxnPattern");
						self.errorMsg("");
					}
				}


				if(flagStartPattern == 0){

					if(self.selQueryParamArr().length == 0){
						for(var queryParam in self.queryParamsArr()){
							self.selQueryParamArr.push({
								"key": self.queryParamsArr()[queryParam].key,
								"value": self.queryParamsArr()[queryParam].value
							});
						}
					}

					txnhttpPatternObject = {
						"typeId": parseInt($("#transactionMethodList").val()),
						"type": $("#transactionMethodList option:selected").text(),
						"urlPattern": $("#txtUrl").val(),
						"headerPattern": $("#txtHeaderPattern").val(),
						"bodyPattern": $("#txtBodyPattern").val(),  
						"queryParam": (ko.toJS(self.selQueryParamArr())),
						"queryParamFormatted": getFormattedQueryParams(ko.toJS(self.selQueryParamArr()))};

						debugger;

					if(self.addSaveToggleLbl() == uiConstants.transactionConfig.CONST_ADD){
						var obj={'httpTxnConfig':txnhttpPatternObject,
								 'tcpTxnConfig': null};
						self.txnPatternArr.push(obj);	
						self.clearTxnPatternList();
					}
					else{
						self.txnPatternArr.splice(self.selectedTxnPatternRow,1,{"httpTxnConfig":txnhttpPatternObject});	
						self.clearTxnPatternList();
					}
				}
			}

			self.httpPatternEditRow(-1);
		}

		function getFormattedQueryParams(queryParams){
			debugger;
			var queryParamsStr = "";

			for(var param in queryParams){
				queryParamsStr+=(", '"+queryParams[param].key+"'='"+queryParams[param].value+"'");
			}

			return queryParamsStr.substring(2);
		}

		this.clearTxnPatternList = function(){
			$("#transactionMethodList").val(25).trigger('chosen:updated');
			$("#txtUrl").val("");
			$("#txtHeaderPattern").val("");
			$("#txtBodyPattern").val("");
			self.selQueryParamArr.splice(0,self.queryParamsArr().length);
			self.queryParamsArr(self.selQueryParamArr());
			self.addSaveToggleLbl(uiConstants.transactionConfig.CONST_ADD);
			self.resetCancelToggleLbl(uiConstants.transactionConfig.CONST_RESET);
			self.errorMsg("");
			self.httpPatternEditRow(-1);
		}

		this.onTcpPatternAddClick = function(){
			self.tcpPatternArr.push({"tcpStartPattern":"","length":"","tcpEndPattern":""});
			var $tab = $('.tabListClass');
			$tab.floatThead('reflow');

		}

		/*Subscriber listening to the topic 'saveTransactionDetailsClickEvent' which will be invoked if this topic is published from anywhere*/
		uicommon.postbox.subscribe(function(value){
			self.addEditConfig();
		},"saveTransactionDetailsClickEvent");

		function successCallback(data, reqType) {
			if(reqType === "getTransactionTag"){
				self.configTagArr(data.result);
				/*self.configTagArr([{"tagId":1,"tagName":"test1","tagDescription":"this is test1"},
					{"tagId":2,"tagName":"test2","tagDescription":"this is test2"}]);*/

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #txn-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#txn-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('.panel-body #txn-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#txn-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				if(masterListLoaded)
					onMastersLoad();
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_TRANSACTION,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.transactionConfig.ERROR_ADD_TRANSACTION, "error");
					}
				}
				else{
					showMessageBox(uiConstants.transactionConfig.TXN_ADDED_SUCCESSFULL);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_TRANSACTION,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.transactionConfig.ERROR_UPDATE_TRANSACTION, "error");
					}
				}
				else{
					showMessageBox(uiConstants.transactionConfig.SUCCESS_UPDATE_TXN);
					self.cancelConfig();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getTransactionTag"){
				showMessageBox(uiConstants.transactionConfig.ERROR_GET_TXN_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.transactionConfig.ERROR_ADD_TRANSACTION, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.transactionConfig.ERROR_UPDATE_TRANSACTION, "error");
			}
		}


	   
	    
  	
}

Transactionaddedit.prototype.dispose = function() { };
return { viewModel: Transactionaddedit, template: templateMarkup };
});
