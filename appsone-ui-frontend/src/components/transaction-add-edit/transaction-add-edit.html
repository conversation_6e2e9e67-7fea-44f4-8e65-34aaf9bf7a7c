<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div id="txnAddEdit" class="panel panel-default">
	<div class="configPanel panel-heading" ><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
	
	<div class="panel-body"> <!-- data-bind="visible: !isModal()" -->

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: transactionName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
				
			</div>

			<div id="divTxnDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: description" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Type </label>
				<div class="col-sm-4">
					<select id="transactionTypeList" class="chosen form-control" data-bind="options: transactionTypeArr,  optionsText: 'transactionTypeName', 'optionsValue': 'transactionTypeId'"></select>
				</div> 
			</div>

			<div class="form-group" >
				<label class="control-label col-sm-2">Enable Audit</label>
				<div class="col-sm-4">
					<input type="checkbox" id="auditEnabled"  data-bind="checked: auditEnabled">
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="txn-tokenfield-typeahead" data-bind="value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->
					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<!-- ko if: selectedTxnType()==='HTTP' -->
			 <div id="divTxnPattern" class="form-group" style="display: none;">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Transaction Patterns</div>
					<div class="panel-body" style="padding:15px" >

					<!-- 	<div class="form-group form-required" >
							<label class="control-label col-sm-2"><b><h4>Patterns</h4></b></label>
							<label class="control-label col-sm-2"></label>
						</div> -->
						<div class="form-group form-required">
							<label class="control-label col-sm-2">Method </label>
							<div class="col-sm-4">
								<select id="transactionMethodList" class="chosen form-control" data-bind="foreach: transactionMethodArr">
									<!-- ko if: $index() == 0 -->
										<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
									<!-- /ko-->

									<option data-bind="value: $data.transactionMethodId, text: $data.transactionMethodName"></option>
								</select><!--  , optionsCaption: uiConstants.common.SELECT -->
							</div>
						</div>

						<div class="form-group form-required">
							<label class="control-label col-sm-2">URL <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtUrl" placeholder="Enter URL" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" >
							</div>

							<span class="a1-span-link" type ="button" data-bind="event: {click: onQueryParamAddClick}" title="Add query parameters">Add Query Parameters</span>
						</div>
						<div class="form-group form-required" id="divQueryParams" data-bind="visible: queryParamsArr().length > 0">
							<label class="control-label col-sm-2">Query Parameters</label>
							<div class="col-sm-4" style="width:50%;float:left;">
								
								<table id="queryParameterList" class="table table-hover table-striped" 
								style="width:100%">
										<thead>
											<tr>
												<th class="col-xs-3">Key</th>
												<th class="col-xs-3">Value</th>
												<!-- ko if: queryParamsArr().length > 1 -->
													<th style="width: 32px;"/>
												<!-- /ko -->
												<th style="width: 32px;"/>
												<th style="width: 22px"/>
											</tr>
										</thead>
										<tbody data-bind="foreach : queryParamsArr">
											<tr >
												<td>
													<input type="text" class="form-control" placeholder="" pattern="[a-zA-Z0-9_ ]" data-bind="value: $data.key, attr: {'id': 'txtQueryParamKey'+$index()}"> 
												</td>
												<td>
													<input type="text" class="form-control" placeholder="" pattern="[a-zA-Z0-9_ ]" data-bind="value: $data.value, attr: {'id': 'txtQueryParamValue'+$index()}">
												</td>

												<!-- ko if: $parent.queryParamsArr().length > 1 -->
													<td style="text-align:center">
														<!-- ko if: $index() < $parent.queryParamsArr().length-1 -->
															<span>AND</span>
														<!-- /ko -->
													</td>
												<!-- /ko -->

												<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
													<td style="text-align:center;     padding-left: 0px;" class="col-xs-1">
														<span type="button" class="glyphicon glyphicon-remove buttondelete" data-bind="event: {click: function(){$parent.deleteQueryParam($index())}}" title="Delete"></span>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
								</table>
							</div>
							
						</div>
						<div class="form-group form-required">
							<label class="control-label col-sm-2">Header Pattern</label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtHeaderPattern" max="45"  >
							</div>
						</div>
						<div class="form-group form-required">
							<label class="control-label col-sm-2">Body Pattern</label>
							<div class="col-sm-4">
								<!-- <input type="textarea" rows="4" cols="50" class="form-control" id="txtBodyPattern" max="100"  > --> 
								<textarea class="form-control" id="txtBodyPattern" rows="4" cols="50"></textarea>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-offset-2 col-sm-4">
								<button class="btn-small" type ="button" id="txnPatternAddBtn" data-bind="text: addSaveToggleLbl, click: onTxnPatternAddclick"></button><!--  ,click: onTxnPatternAddclick(addSaveToggleLbl) -->
								<button class="btn-small" type ="button" data-bind="click: clearTxnPatternList,text:resetCancelToggleLbl"></button>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-sm-2"></label>
							<div class="wrapper-scroll-table" style="width:100%;float:left; max-height: 200px;"> <!-- style="width:50%;float:left;" -->
								<!-- <div class="inner-div-container" style="margin-left: 5px; margin-right: 5px;"> -->
									<table id="txnPatternList" class="table table-bordered table-hover table-striped tabListClass" > <!-- data-bind="visible: txnPatternArr().length > 0" -->

										<thead>
											<tr class="a1-inner-table-thead">
												<th class="col-xs-1">Method</th>
												<th class="col-xs-2">URL</th>
												<th class="col-xs-3">Header Pattern</th>
												<th class="col-xs-3">Body Pattern</th>
												<th class="col-xs-3">Query Parameters</th>
												<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
													<th style="width: 60px;"></th>
												<!-- /ko -->
											</tr>
										</thead>
										
										<tbody data-bind="foreach : txnPatternArr">
											<tr>   <!-- data-bind="event: {click: $parent.ontxnPatternListClick($data,$index())}" -->
												<td class="textOverflowOmmiter" data-bind="text: $data['httpTxnConfig'].type != undefined? $data['httpTxnConfig'].type:'', attr: {title: $data['httpTxnConfig'].type != undefined? $data['httpTxnConfig'].type:''	}"></td>
												<td class="textOverflowOmmiter" data-bind="text: $data['httpTxnConfig'].urlPattern != undefined?$data['httpTxnConfig'].urlPattern:'', attr: {title: $data['httpTxnConfig'].urlPattern != undefined? $data['httpTxnConfig'].urlPattern:''}"></td>
												<td class="textOverflowOmmiter" data-bind="text: $data['httpTxnConfig'].headerPattern != undefined?$data['httpTxnConfig'].headerPattern:'', attr: {title: $data['httpTxnConfig'].headerPattern != undefined? $data['httpTxnConfig'].headerPattern:''}"></td>
												<td class="textOverflowOmmiter" data-bind="text: $data['httpTxnConfig'].bodyPattern != undefined?$data['httpTxnConfig'].bodyPattern:'', attr: {title: $data['httpTxnConfig'].bodyPattern != undefined? $data['httpTxnConfig'].bodyPattern:''}"></td>
												<td class="textOverflowOmmiter" data-bind="text: $data['httpTxnConfig'].queryParamFormatted != undefined?$data['httpTxnConfig'].queryParamFormatted:'', attr: {title: $data['httpTxnConfig'].queryParamFormatted != undefined? $data['httpTxnConfig'].queryParamFormatted:''}"></td>
												<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
													<td style="text-align:center" class="col-xs-1">
														<span type="button" class="glyphicon glyphicon-edit buttonedit" title="Edit" data-bind="event: {click: function(){$parent.editHttpPattern($index())}}"></span>
														<span type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete" data-bind="event: {click: function(){$parent.deleteHttpPattern($index())}}"></span>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
									</table>
								<!-- </div> -->
							</div>
						</div> 
					</div>
				</div>
			</div>
			<!-- /ko -->

			<!-- ko if: selectedTxnType()==='TCP' -->
			 <div id="divTxnTcpPattern" class="form-group" style="background-color:#F8F8F8; display: none;">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Transaction Patterns</div>
				 
					 <div class="panel-body" style="padding:15px" >
					 	 <div class="form-group form-required" id="divQueryParams">
							<!-- <label class="control-label col-sm-2">Patterns</label> -->
							<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
								<button id="btnTcpPatternAdd" class="glyphicon glyphicon-plus" type ="button" data-bind="event: {click: onTcpPatternAddClick}" title="Add patterns" style="margin-left: 20px;"></button>
							<!-- /ko-->

							<div class="col-sm-4 wrapper-scroll-table" style="width:50%;float:left; max-height: 200px;">
								<table id="tcpPatternList"  class="table table-hover table-striped tabListClass">
										<thead>
											<tr class="a1-inner-table-thead">
												<th class="col-xs-3">Start Pattern<span class="mandatoryField">*</span></th>
												<th class="col-xs-3">Length</th>
												<th class="col-xs-3">End Pattern</th>
												<th class="col-xs-1"/>
											</tr>
										</thead>
										<tbody data-bind="foreach : tcpPatternArr">
											<tr >
												<td>
													<input type="text" class="form-control" placeholder="" data-bind="attr:{'id': 'txtStartPattern'+$index()}"> 
												</td>
												<td>
													<input type="number" class="form-control" placeholder=""  data-bind="attr:{'id': 'txtLength'+$index()}">
												</td>
												<td>
													<input type="text" class="form-control"  placeholder="" data-bind="attr:{'id': 'txtEndPattern'+$index()}">
												</td>
												<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
													<td style="text-align:center" class="col-xs-1">
														<span type="button" id="btnTcpPatternDelete" class="glyphicon glyphicon-remove buttondelete"  title="Delete"></span>
													</td>
												<!-- /ko-->
											</tr>
										</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- /ko -->

			<div id="divTxnThresholds" class="form-group form-required" data-bind="visible: selectedTxnTypeId() != 0">
			<div class="panel panel-default inner-panel">
				<div class="configPanel panel-heading">Threshold</div>
					 <div class="panel-body" style="padding:15px" >
					 	<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
							<button id="btnThresholdAdd" class="glyphicon glyphicon-plus" type ="button" data-bind="event: {click: onThresholdAddClick}" title="Add Threshold" style="margin-left: 20px;"></button>
							<br>
						<!-- /ko-->
						<div class="col-sm-4" style="width:50%;float:left;" >
							<table id="thresholdList" class="table table-hover table-striped">
									<thead>
										<tr class="a1-inner-table-thead">
											<th class="col-xs-3">Response Time Type</th>
											<th class="col-xs-3">Value(Miliseconds)<span class="mandatoryField">*</span></th>
											<th class="col-xs-1"/>
										</tr>
									</thead>
									<tbody data-bind="foreach : thresholdArr">
										<tr>
											<td>
												<select class="chosen form-control responseTypeListChosen" data-bind="options: $parent.transactionResTypeArr,  optionsText: 'responseTypeName', 'optionsValue': 'responseTypeId', attr: {'id': 'responseTypeList_'+$index()}"></select>
											</td>
											<td>
												<input type="number" class="form-control" placeholder="" min="0" max="8" data-bind="value: $data.threshold, attr: {'id': 'txtResponseValue'+$index()}">
											</td>

											<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
												<td style="text-align:center" class="col-xs-1">
													<span type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete" data-bind="attr: {disabled: $parent.thresholdArr().length < 2}, css: {confButtonDisabled: $parent.thresholdArr().length < 2}"></span>
												</td>
											<!-- /ko-->
										</tr>
									</tbody>
							</table>
						</div>	
					</div>
				</div>
			</div>

			<div id="divTxnAudit" class="form-group form-required" data-bind="visible: selectedTxnTypeId() != 0">
			<div class="panel panel-default inner-panel">

				<div class="configPanel panel-heading"><a class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="attr:{'id': 'imgAttribShowHide'}, event: {click: expandCollapsePanel.bind($data, '#auditPanel', '#imgAttribShowHide')}" title="Collapse"><span class="panel-label-expand-collapse">Audit Value Extractor</span></a></div>

				<!-- <div class="configPanel panel-heading">Audit Value Extractor</div> -->
					 <div id="auditPanel" class="panel-body" style="padding:15px" data-bind="{css:{'disabled': !auditEnabled()}}">
					 	<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
							<button id="btnAuditValueAdd" class="glyphicon glyphicon-plus" type ="button" data-bind="event: {click: onClickofAddAuditValues}" title="Add Audit Values" style="margin-left: 20px;"></button>
						<!-- /ko-->

						<div class="col-sm-4 wrapper-scroll-table" style="width:100%;float:left; max-height: 200px;">
							<table class="table table-hover table-striped tabListClass">
									<thead>
										<tr class="a1-inner-table-thead">
											<th class="col-xs-4">Extractor Name<span  data-bind="visible:auditEnabled" class="mandatoryField">*</span></th>
											<th class="col-xs-4">Datapart</th>
											<th class="col-xs-6">Extractor</th>
											<th class="col-xs-6">Value1<span  data-bind="visible:auditEnabled" class="mandatoryField">*</span></th>
											<th class="col-xs-6">Value2</th>
											<th class="col-xs-1"/>
										</tr>
									</thead>
									<tbody data-bind="foreach : auditExtractorArr">
										<tr>
											<td>
												<input type="text" class="form-control" placeholder="" min="0" max="8" data-bind="value: $data.extractorName, attr: {id: 'txtAuditExtractorName'+$index()}">
											</td>
											<td>
												<select class="chosen form-control cmbAuditDataPartChosen" data-bind="options: $parent.auditDataPartArr(),  optionsText: 'dataPartName', 'optionsValue': 'dataPartId', attr: {'id': 'cmbAuditDataPart_'+$data.counterId},event:{ change: function(){$parent.onChangeTxnAuditDataPart($data.counterId)}}"></select>
											</td>
											<td>
												<select class="chosen form-control cmbExtractorChosen" data-bind="options: $parent.extractorArr()[$data.counterId],  optionsText: 'extractorName', 'optionsValue': 'extractorId', attr: {'id': 'cmbExtractor_'+$data.counterId},event:{ change: function(){$parent.onChangeTxnAuditExtractor($data.counterId)}}"></select>
											</td>
											<td>
												<input class="form-control" placeholder="" min="0" max="99999" data-bind="value: $data.extractorValueOne, attr: {'id': 'txtExtractorValue_'+$data.counterId}"  >
											</td>
											<td>
												<input type="number" class="form-control" placeholder="" min="0" max="99999" data-bind="value: $data.extractorValueTwo,attr: {'id':'txtExtractorValueTwo_'+$data.counterId+'_Id'},'disabled':$('#transactionTypeList option:selected').text().toUpperCase()==='TCP'?0:1" >
											</td> 
											<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
											<td style="text-align:center" class="col-xs-1">
												<span type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete"></span>
											</td>
											<!-- /ko-->
										</tr>
									</tbody>
							</table>
						</div>	
					</div>
				</div>
			</div>

			<div id="divTxnBVE" class="form-group form-required" data-bind="visible: selectedTxnTypeId() != 0">
			<div class="panel panel-default inner-panel">
				<div class="configPanel panel-heading"><a class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="attr:{'id': 'imgBveAttribShowHide'}, event: {click: expandCollapsePanel.bind($data, '#bvePanel', '#imgBveAttribShowHide')}" title="Collapse"><span class="panel-label-expand-collapse">Business Value Extractor</span></a></div>

					 <div id="bvePanel" class="panel-body" style="padding:15px;" >
					 	<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
							<button id="btnBVEAdd" class="glyphicon glyphicon-plus" type ="button" data-bind="event: {click: onBVEAddClick}" title="Add BVE" style="margin-left: 20px;"></button>
						<!-- /ko-->
						<div class="col-sm-4 wrapper-scroll-table" style="width:50%;float:left; max-height: 200px;">
							<table id="bvePatternList" class="table table-hover table-striped tabListClass">
									<thead>
										<tr class="a1-inner-table-thead">
											<th class="col-xs-3">Extractor Name</th>
											<th class="col-xs-3">Extractor Pattern</th>
											<th class="col-xs-1"/>
										</tr>
									</thead>
									<tbody data-bind="foreach : bveArr">
										<tr >
											<td>
												<input type="text" class="form-control" placeholder="" min="0" max="8" data-bind="value: $data.extractorName, attr: {id: 'txtExtractorName'+$index()}">
											</td>
											<td>
												<input type="text" class="form-control" placeholder="" min="0" max="8" data-bind="value: $data.regExString, attr: {id: 'txtRegExpString'+$index()}">
											</td>

											<!-- ko if: $parent.currentViewIndex() != uiConstants.common.READ_VIEW -->
												<td style="text-align:center" class="col-xs-1">
													<span type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete"></span>
												</td>
											<!-- /ko-->
										</tr>
									</tbody>
							</table>
						</div>	
					</div>
				</div>
			</div>

			<div class="form-group" data-bind="visible: mode!== 'wizard'">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>
