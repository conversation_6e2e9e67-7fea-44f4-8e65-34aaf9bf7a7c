define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead', 'text!./escalation-profile-add-edit.html','hasher','validator','ui-constants','ui-common','floatThead'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,floatThead) {

	function escalationAddEdit(params){
		var self = this;

		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var configTagLoaded = 0;
  		var levelCounter=0;
  		var unMappedIdsArr = [];
  		//var previousAlertTypeIdSelected = 0;
  		//var previousAlertTypeSelected = "";

  		var prevSelected = "";
  		var currentSelected = "";
		
    	this.errorMsg=ko.observable('');
    	this.firstFieldToShowErr = ko.observable("");

		//params passed from severity list view
		//this.selectedConfigRows = ko.observableArray();

		if(localStorage.selectedConfigRows)
			selectedConfigRows = JSON.parse(localStorage.selectedConfigRows);
		else
			selectedConfigRows = [];

		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.pageSelected = params.pageSelected;
		this.alertProfileTypesArr = params.alertProfileTypesArr;

		//other common variables
		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
		this.selectedConfigNames = ko.observable("");
		this.tags = ko.observable();
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();

		this.profileId = ko.observable(0);
		this.profileName = ko.observable();
		this.profileDescription = ko.observable();
		this.profileStatus = ko.observable(1);
		
		this.getAlertProfileTypeSelected = ko.observable();
		this.escalationDetailArr = ko.observableArray();
		this.escSubDetailsArr = ko.observableArray();
  		this.isModal = ko.observable(false);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		this.showEscalationTable = ko.observable(true);

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*$(".wrapper-scroll-table").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
            });*/

            var $tab = $('#listEscalationSettings');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#escalationStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});
			$('#escalationStatus').bootstrapSwitch('state', self.profileStatus());
			$("#escalationStatus").on('switchChange.bootstrapSwitch', function () {
				self.profileStatus($('#escalationStatus').bootstrapSwitch('state')?1:0);
			});

			if(params.isModal){
				self.isModal(true);
				self.setPreviousVersion();
				$("#divEscalationProfile #alertProfileType").val(params.profileTypeId()).trigger("chosen:updated");
				self.onAlertProfileTypeChange();
			}

			//self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW && selectedConfigRows.length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(selectedConfigRows, ["name"]));
			}

			//tag changes
			$('.panel-body #escalation-tokenfield-typeahead')
				.on('tokenfield:createdtoken', function (e) {
					if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
						tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
					}
					var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
					//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
					if(tagIndex != -1){
						self.configTagAutoCompleteArr.splice(tagIndex, 1);
					}

					$('#escalation-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				})

				.on('tokenfield:edittoken', function(e){
					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
						return false;
					}
				})

				.on('tokenfield:removedtoken', function (e) {
					if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
						tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
					}
					tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
					var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

					if(tagIndex != -1){
						self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
						self.configTagAutoCompleteArr.sort();
					}

					$('#escalation-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
					 	{source: self.configTagAutoCompleteArr()
					});
				});

			requestCall(uiConstants.common.SERVER_IP + "/tag?type=EscalationProfile", "GET", "", "getEscalationTag", successCallback, errorCallback);

			/*$("#alertProfileType").on('change',function(e){
				self.getAlertProfileTypeSelected($("#alertProfileType option:selected").text().toUpperCase());	
			});	*/

			$(".btnDeleteFilter").prop("disabled",true);

			prevSelected = $("#alertProfileType option:selected").text();

		    $("#alertProfileType").on('change',function(e){
				self.onAlertProfileTypeChange();
			});
		}

		this.setPreviousVersion = function(){
	    	previousAlertTypeIdSelected = $("#alertProfileType :selected").val();
		    previousAlertTypeSelected = $("#alertProfileType :selected").text();
	    }

	    this.onAlertProfileTypeChange=function(){
			if($('#alertProfileType option:selected').text() != uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE ){
				self.getConfirmOnTypeChange(previousAlertTypeIdSelected,previousAlertTypeSelected);	
			}
			else{
				/*showMessageBox(uiConstants.escalationProfile.CONFIRM_ALERT_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
					if(r){
						self.escalationDetailArr([]);
						self.addEscalationDetails();
					}
					else{
						$("#alertProfileType").val(previousAlertTypeIdSelected);
						self.getAlertProfileTypeSelected(previousAlertTypeSelected.toUpperCase());
					}
				});*/
			}
	    }

		this.getConfirmOnTypeChange=function(previousTypeId,previousTypeName){	
			var currTypeSelected=$("#alertProfileType option:selected").text();

			if(previousTypeId == 0){
				self.showEscalationTable(false);
				self.showEscalationTable(true);
				//self.addEscalationDetails();
				self.getAlertProfileTypeSelected(currTypeSelected.toUpperCase());

				var $tab = $('#listEscalationSettings');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			}
			else {
				self.getAlertProfileTypeSelected(previousTypeName.toUpperCase());
				showMessageBox(uiConstants.escalationProfile.CONFIRM_ALERT_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
					if(r){
						self.showEscalationTable(false);
						self.showEscalationTable(true);

						self.getAlertProfileTypeSelected(currTypeSelected.toUpperCase());
						self.escalationDetailArr([]);
						self.addEscalationDetails();

						var $tab = $('#listEscalationSettings');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper-scroll-table');
							}
						});
			
					}
					else{
						$('#alertProfileType').val(previousTypeId).trigger('chosen:updated');
						self.getAlertProfileTypeSelected(previousTypeName.toUpperCase());
					}
				});
			}
		}

		/*Get tagid from tagname : START*/
		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(objIndx in self.configTagArr()){
				if(self.configTagArr()[objIndx].tagName.trim() == tagName.trim()){
					return self.configTagArr()[objIndx].tagId;
				}
			}
			return 0;
		}
		/*END*/

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(selectedConfigRows);
					if(!selectedConfigRows[0].status){ //if the component is inactive
						setConfigUneditable(true);
					}
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(selectedConfigRows);
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(selectedConfigRows);
					viewConfig(selectedConfigRows);
				}
			}
		}

		/************Adding/Updating single severity profile************/
		this.addEditEscalation = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			//var levelsObj= {};
			var levelsArr=[];
			var isEscalationErrExists = 0;


			$("#divEscalationProfile #txtName").val($("#divEscalationProfile 	#txtName").val().trim());
			$("#divEscalationProfile #txtDescription").val($("#divEscalationProfile #txtDescription").val().trim());

			if($("#divEscalationProfile #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_REQUIRED);
				showError("#divEscalationProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_REQUIRED);
			    self.errorMsg("#divEscalationProfile #txtName");
			}	
			else if($("#divEscalationProfile #txtName").val().length < 2){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MIN_LENGTH_ERROR);
				showError("#divEscalationProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divEscalationProfile #txtName");
			}
			else if($("#divEscalationProfile #txtName").val().length > 45){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MAX_LENGTH_ERROR);
				showError("#divEscalationProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divEscalationProfile #txtName");
			}
			else if(!nameValidation($("#divEscalationProfile #txtName").val())){
				//self.errorMsg(uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_INVALID_ERROR);
				showError("#divEscalationProfile #txtName", uiConstants.commonAlertProfile.PROFILE_CONTENT_NAME_INVALID_ERROR);
			    self.errorMsg("#divEscalationProfile #txtName");
			}
			if($("#divEscalationProfile #txtDescription").val().trim() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divEscalationProfile #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divEscalationProfile #txtDescription");
			}
			else if($("#divEscalationProfile #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divEscalationProfile #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divEscalationProfile #txtDescription");
			}
			else if($("#divEscalationProfile #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divEscalationProfile #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divEscalationProfile #txtDescription");
			}
			if($("#divEscalationProfile #alertProfileType").chosen().val() == "0"){
				//self.errorMsg(uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
				showError("#divEscalationProfile #alertProfileType_chosen", uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
				showError("#divEscalationProfile #alertProfileType_chosen span", uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE);
			    self.errorMsg("#divEscalationProfile #alertProfileType_chosen");
			}
			else{
				removeError("#divEscalationProfile #alertProfileType_chosen");
				removeError("#divEscalationProfile #alertProfileType_chosen span");
			}

			removeError("#divEscalationProfile .tokenfield");
			removeError("#divEscalationProfile #escalation-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divEscalationProfile #escalation-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divEscalationProfile .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divEscalationProfile #escalation-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divEscalationProfile .tokenfield");
			}
			else{
				

				//if(self.errorMsg() == ""){
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divEscalationProfile .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divEscalationProfile #escalation-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divEscalationProfile .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divEscalationProfile .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divEscalationProfile #escalation-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divEscalationProfile .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divEscalationProfile .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divEscalationProfile #escalation-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divEscalationProfile .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}

				}
			}
					
			var fromIndex = 0;
			//escalation validation

    		/*$(".wrapper-scroll-table").animate({
			    scrollTop: $("#levelId0").offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
			});*/


			$(".wrapper-scroll-table").animate({
			    scrollTop: 0
			});


			setTimeout(function () {
				for (var i = 0; i < self.escalationDetailArr().length; i++) {

					if( self.escalationDetailArr()[i]['levelDetails'].levelName ==""){
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#levelId"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError(".wrapper-scroll-table #levelId"+i, uiConstants.escalationProfile.LEVELNAME_REQUIRED_ERROR);
	   					//self.errorMsg(".wrapper-scroll-table #levelId"+i);
					}
					if( self.escalationDetailArr()[i]['levelDetails'].emailTo ==""){
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #emailTo"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #emailTo"+i, uiConstants.escalationProfile.EMAILTO_REQUIRED_ERROR);
	   					//self.errorMsg("#divEscalationProfile #emailTo"+i);
					}
					else if( self.escalationDetailArr()[i]['levelDetails'].emailTo !="" && !multipleEmailValidation(self.escalationDetailArr()[i]['levelDetails'].emailTo)){
						//case 4 : self.errorMsg(uiConstants.escalationProfile.ERROR_EMAIL_REG_EXP+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #emailTo"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #emailTo"+i, uiConstants.escalationProfile.ERROR_EMAIL_REG_EXP);
	   					//self.errorMsg("#divEscalationProfile #emailTo"+i);
					}
					if(self.escalationDetailArr()[i]['levelDetails'].emailCc != "" && !multipleEmailValidation(self.escalationDetailArr()[i]['levelDetails'].emailCc)){
						//case 5 : self.errorMsg(uiConstants.escalationProfile.ERROR_CC_REG_EXP+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #emailCc"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #emailCc"+i, uiConstants.escalationProfile.ERROR_CC_REG_EXP);
	   					//self.errorMsg("#divEscalationProfile #emailCc"+i);
					}
					if(self.escalationDetailArr()[i]['levelDetails'].emailBcc != "" && !multipleEmailValidation(self.escalationDetailArr()[i]['levelDetails'].emailBcc)){
						//case 6 : self.errorMsg(uiConstants.escalationProfile.ERROR_BCC_REG_EXP+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #emailBcc"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #emailBcc"+i, uiConstants.escalationProfile.ERROR_BCC_REG_EXP);
	   					//self.errorMsg("#divEscalationProfile #emailBcc"+i);
					}
					if( self.escalationDetailArr()[i]['levelDetails'].mobileNo ==""){
						//case 3:  self.errorMsg(uiConstants.escalationProfile.PHONENO_REQUIRED_ERROR+" for Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #mobileNum"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #mobileNum"+i, uiConstants.escalationProfile.PHONENO_REQUIRED_ERROR);
	   					//self.errorMsg("#divEscalationProfile #mobileNum"+i);
					}
					else if(self.escalationDetailArr()[i]['levelDetails'].mobileNo != "" && !multiplePhoneValidation(self.escalationDetailArr()[i]['levelDetails'].mobileNo)){
						//case 7 : self.errorMsg(uiConstants.escalationProfile.ERROR_PHONE_REG_EXP+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #mobileNum"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #mobileNum"+i, uiConstants.escalationProfile.ERROR_PHONE_REG_EXP);
	   					//self.errorMsg("#divEscalationProfile #mobileNum"+i);
					}
					if(self.escalationDetailArr()[i].highSuppression() != "" && self.escalationDetailArr()[i].highSuppression() <= 0){
						//case 8 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_HIGH_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #mobileNum"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #mobileNum"+i, uiConstants.escalationProfile.ERROR_PHONE_REG_EXP);
	   					//self.errorMsg("#divEscalationProfile #mobileNum"+i);
					}
					else if(self.escalationDetailArr()[i].highSuppression() != "" && self.escalationDetailArr()[i].highSuppression() > 99){
						//case 9 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_HIGH_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escHighSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escHighSeverity"+i, uiConstants.escalationProfile.ERROR_MAX_HIGH_PERSISTENCE);
	   					//self.errorMsg("#divEscalationProfile #escHighSeverity"+i);
					}
					if((self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') && (self.escalationDetailArr()[i].mediumSuppression() != "" && self.escalationDetailArr()[i].mediumSuppression() <= 0)){
						//case 10 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_MEDIUM_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escMediumSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escMediumSeverity"+i, uiConstants.escalationProfile.ERROR_MIN_MEDIUM_PERSISTENCE);
	   					//self.errorMsg("#divEscalationProfile #escMediumSeverity"+i);
					}
					else if((self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') && (self.escalationDetailArr()[i].mediumSuppression() != "" && self.escalationDetailArr()[i].mediumSuppression() > 99)){
						//case 11 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_MEDIUM_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escMediumSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escMediumSeverity"+i, uiConstants.escalationProfile.ERROR_MAX_MEDIUM_PERSISTENCE);
	   					//self.errorMsg("#divEscalationProfile #escMediumSeverity"+i);
					} 
					if((self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') && (self.escalationDetailArr()[i].lowSuppression() != "" && self.escalationDetailArr()[i].lowSuppression() <= 0)){
						//case 12 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_LOW_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escLowSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escLowSeverity"+i, uiConstants.escalationProfile.ERROR_MIN_LOW_PERSISTENCE);
	   					//self.errorMsg("#divEscalationProfile #escLowSeverity"+i);
					}
					else if((self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') && (self.escalationDetailArr()[i].lowSuppression() != "" && self.escalationDetailArr()[i].lowSuppression() > 99)){
						//case 13 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_LOW_PERSISTENCE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escLowSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escLowSeverity"+i, uiConstants.escalationProfile.ERROR_MAX_LOW_PERSISTENCE);
	   					//self.errorMsg("#divEscalationProfile #escLowSeverity"+i);
					}
					if((self.getAlertProfileTypeSelected() != 'CORE' && self.getAlertProfileTypeSelected() != 'TRANSACTION') && (self.escalationDetailArr()[0].highSuppression() == undefined || self.escalationDetailArr()[0].highSuppression() == "")){
						//case 14 : self.errorMsg(uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE_EMPTY+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escHighSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escHighSeverity"+i, uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE_EMPTY);
	   					//self.errorMsg("#divEscalationProfile #escHighSeverity"+i);
					}
					else if((self.getAlertProfileTypeSelected() != 'CORE' && self.getAlertProfileTypeSelected() != 'TRANSACTION') && (self.escalationDetailArr()[0].highSuppression() != "" && self.escalationDetailArr()[0].highSuppression() < 0)){
						//case 15 : self.errorMsg(uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escHighSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escHighSeverity"+i, uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE);
	   					//self.errorMsg("#divEscalationProfile #escHighSeverity"+i);
					}
					if((self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION') &&
							((self.escalationDetailArr()[0].highSuppression() == undefined || self.escalationDetailArr()[0].highSuppression() == "" ) &&
							 (self.escalationDetailArr()[0].mediumSuppression() == undefined || self.escalationDetailArr()[0].mediumSuppression() == "") &&	
							 (self.escalationDetailArr()[0].lowSuppression() == undefined || self.escalationDetailArr()[0].lowSuppression() == "" ))){
						//case 16 : self.errorMsg(uiConstants.escalationProfile.ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #escHighSeverity"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #escHighSeverity"+i, uiConstants.escalationProfile.ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK);
						showError("#divEscalationProfile #escMediumSeverity"+i, uiConstants.escalationProfile.ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK);
						showError("#divEscalationProfile #escLowSeverity"+i, uiConstants.escalationProfile.ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK);
	   					//self.errorMsg("#divEscalationProfile #escHighSeverity"+i);
					}
					if( self.escalationDetailArr()[i]['levelDetails'].emailTo !="" && self.escalationDetailArr()[i]['levelDetails'].emailTo.length > 511){
						//case 17 : self.errorMsg(uiConstants.escalationProfile.ERROR_IN_EMAIL_LENGTH+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #emailTo"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #emailTo"+i, uiConstants.escalationProfile.ERROR_IN_EMAIL_LENGTH);
	   					//self.errorMsg("#divEscalationProfile #emailTo"+i);
					}
					if( self.escalationDetailArr()[i]['levelDetails'].mobileNo !="" && self.escalationDetailArr()[i]['levelDetails'].mobileNo.length > 511){
						//case 18 : self.errorMsg(uiConstants.escalationProfile.ERROR_IN_MOBILENO_LENGTH+" at Level "+levelNum);
						if(isEscalationErrExists == 0){
							$(".wrapper-scroll-table").animate({
							    scrollTop: $("#divEscalationProfile #mobileNum"+i).offset().top - $(".wrapper-scroll-table").offset().top + $(".wrapper-scroll-table").scrollTop() - 100
							});

							isEscalationErrExists = 1;
						}

						showError("#divEscalationProfile #mobileNum"+i, uiConstants.escalationProfile.ERROR_IN_MOBILENO_LENGTH);
	   					//self.errorMsg("#divEscalationProfile #mobileNum"+i);
					}
					
				}


				/*switch(flagEsc){
					case 1:  self.errorMsg(uiConstants.escalationProfile.LEVELNAME_REQUIRED_ERROR+" for Level "+levelNum);
							 break;
					case 2:  self.errorMsg(uiConstants.escalationProfile.EMAILTO_REQUIRED_ERROR+" for Level "+levelNum);
					         break;
					case 3:  self.errorMsg(uiConstants.escalationProfile.PHONENO_REQUIRED_ERROR+" for Level "+levelNum);
							 break;
					case 4 : self.errorMsg(uiConstants.escalationProfile.ERROR_EMAIL_REG_EXP+" at Level "+levelNum);
							 break;
					case 5 : self.errorMsg(uiConstants.escalationProfile.ERROR_CC_REG_EXP+" at Level "+levelNum);
							 break;
					case 6 : self.errorMsg(uiConstants.escalationProfile.ERROR_BCC_REG_EXP+" at Level "+levelNum);
							 break;
					case 7 : self.errorMsg(uiConstants.escalationProfile.ERROR_PHONE_REG_EXP+" at Level "+levelNum);
							 break;
					case 8 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_HIGH_PERSISTENCE+" at Level "+levelNum);
							 break;
					case 9 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_HIGH_PERSISTENCE+" at Level "+levelNum);
					 		 break;		 
					case 10 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_MEDIUM_PERSISTENCE+" at Level "+levelNum);
					 		 break;
					case 11 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_MEDIUM_PERSISTENCE+" at Level "+levelNum);
					 		 break;
					case 12 : self.errorMsg(uiConstants.escalationProfile.ERROR_MIN_LOW_PERSISTENCE+" at Level "+levelNum);
					 		 break;
					case 13 : self.errorMsg(uiConstants.escalationProfile.ERROR_MAX_LOW_PERSISTENCE+" at Level "+levelNum);
					 		 break;
					case 14 : self.errorMsg(uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE_EMPTY+" at Level "+levelNum);
					 		 break;
					case 15 : self.errorMsg(uiConstants.escalationProfile.ERROR_HIGHSUPPRESSION_VALUE+" at Level "+levelNum);
					 		 break;
					case 16 : self.errorMsg(uiConstants.escalationProfile.ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK+" at Level "+levelNum);
					 		 break;
					case 17 : self.errorMsg(uiConstants.escalationProfile.ERROR_IN_EMAIL_LENGTH+" at Level "+levelNum);
					 		 break;
					case 18 : self.errorMsg(uiConstants.escalationProfile.ERROR_IN_MOBILENO_LENGTH+" at Level "+levelNum);
					 		 break;
				}*/


				/*if(flagEsc == 16){
					debugger;
					self.madeOtherLevelsEmpty(fromIndex,self.escalationDetailArr(),"#escHighSeverity");
				}
*/
				
				if(self.errorMsg() == "" && isEscalationErrExists == 0){
					//escalation settings object to save
					for(levelIndx in self.escalationDetailArr()){
						var levelsObj = {   
											"id": self.escalationDetailArr()[levelIndx].id,
											"levelNumber": self.escalationDetailArr()[levelIndx].levelNumber,
											"highSuppression": parseInt(self.escalationDetailArr()[levelIndx].highSuppression()),
											"mediumSuppression": parseInt(self.escalationDetailArr()[levelIndx].mediumSuppression()),
											"lowSuppression": parseInt(self.escalationDetailArr()[levelIndx].lowSuppression()),
											"levelDetails": {
												"levelName": self.escalationDetailArr()[levelIndx]['levelDetails'].levelName,
												"emailTo":   self.escalationDetailArr()[levelIndx]['levelDetails'].emailTo,
												"emailCc":   self.escalationDetailArr()[levelIndx]['levelDetails'].emailCc,
												"emailBcc":  self.escalationDetailArr()[levelIndx]['levelDetails'].emailBcc,
												"mobileNo":  self.escalationDetailArr()[levelIndx]['levelDetails'].mobileNo
											}
										}

						levelsArr.push(levelsObj);
					}
						
					var escalationObj = {
						"index":1,
						"id":self.profileId(),
						"name": self.profileName().trim(),
						"description": self.profileDescription(),
						"alertProfileTypeId":($("#divEscalationProfile #alertProfileType").chosen().val() == "Select" || $("#divEscalationProfile #alertProfileType").chosen().val() == null) ? 0 : parseInt($("#divEscalationProfile #alertProfileType").chosen().val()),
						"levels":levelsArr,
						"tags": tagsObjArr,
						"unMappedIds": unMappedIdsArr,
						"status" : ($('#divEscalationProfile #escalationStatus').bootstrapSwitch('state') == true)?1:0};

					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(escalationObj));

					if(self.profileId() == 0)
						requestCall(uiConstants.common.SERVER_IP + "/escalationProfile", "POST", JSON.stringify(escalationObj), "addSingleConfig", successCallback, errorCallback);
					else
						requestCall(uiConstants.common.SERVER_IP + "/escalationProfile/" + self.profileId(), "PUT", JSON.stringify(escalationObj), "editSingleConfig", successCallback, errorCallback);	
				}
		 	}, 1000);
					//}
				//}
			//}
		}

		/************Editing single severity profile************/
		function editSingleConfig(configObj){
			setConfigValues(configObj);
		}

		function setConfigValues(escalationObj){
			if(uiConstants.common.DEBUG_MODE)console.log(escalationObj[0]);
			for(var tagObj in escalationObj[0].tags){
				tagsNameArr.push(escalationObj[0].tags[tagObj].tagName);
			}

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.profileName("");
				self.profileDescription("");
			}
			else{
				self.profileName(escalationObj[0].name);
				self.profileId(escalationObj[0].id);
				self.profileDescription(escalationObj[0].description);
			}
			
			self.profileStatus(escalationObj[0].status);
			$('#escalationStatus').bootstrapSwitch('state',self.profileStatus());
			$("#alertProfileType").val(escalationObj[0].alertProfileTypeId).prop('disabled', true).trigger('chosen:updated');

			self.showEscalationTable(false);
			self.showEscalationTable(true);
			self.getAlertProfileTypeSelected($("#alertProfileType option:selected").text().toUpperCase());	
				
			var $tab = $('#listEscalationSettings');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			$('#escalation-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			for(indx in escalationObj[0].levels){
				var currHighVal = escalationObj[0].levels[indx].highSuppression;
				var currMediumVal = escalationObj[0].levels[indx].mediumSuppression;
				var currLowVal = escalationObj[0].levels[indx].lowSuppression;
				escalationObj[0].levels[indx].highSuppression=ko.observable(currHighVal);
				escalationObj[0].levels[indx].mediumSuppression=ko.observable(currMediumVal);
				escalationObj[0].levels[indx].lowSuppression=ko.observable(currLowVal);
			}

			self.escalationDetailArr(escalationObj[0].levels);

			//self.disableSuppression();
			//self.enableDisableSuppression();
			self.enableHighSuppression();
			self.enableMediumSuppression();
			self.enableLowSuppression();
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		/************View single severity profile************/
		function viewConfig(configObj){
			//self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$("#alertProfileType").prop('disabled', true).trigger('chosen:updated');
			$('#escalation-tokenfield-typeahead').tokenfield('readonly');
			$("#divEscalationSettings").find("input,select").prop("disabled", true);
			
			$(".btnAddFilter").css("display","none");
			$(".btnDeleteFilter").css("display","none");

			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("[name='escalationStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divEscalationAddEdit .chosen-container b").css("display", "none");
			}	
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
		}

		this.cancelAddScreen = function(){
			if(params.isModal){
				$("#idModalAlertProfile").modal("hide");
				//$(".modal-header button").click();
			}
			else{
				selectedConfigRows=[];
				localStorage.selectedConfigRows = JSON.stringify(selectedConfigRows);
				self.pageSelected("Escalation Profile");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
				self.errorMsg("");
			}
		}

		/**************Escalation related changes****************/
		/*this.handleEscalationSelAll = function(index){
			$(".escalationSelAll"+index).prop("checked",$("#escalationSelAll"+index).prop('checked'));
			return true;
		}*/

		this.addEscalationDetails = function(){
			
			var levelNum = self.escalationDetailArr().length;

			if(levelNum != 0 && $("#emailTo"+(levelNum-1)).val().trim() !="" && !multipleEmailValidation($("#emailTo"+(levelNum-1)).val())){
				showMessageBox("Please enter valid Email To address(es) in level "+ (levelNum-1) +" before adding next level", "error");
   			}
   			else{
				self.escalationDetailArr.push({"id":0,
											   "tempLevelId":levelCounter++,
											   "isSelected":0,
											   "levelNumber":levelNum,
											   "highSuppression":ko.observable(""),
											   "lowSuppression":ko.observable(""),
											   "mediumSuppression":ko.observable(""),
											   "levelDetails": {
													"levelName": "",
													"emailTo": "",
													"emailCc": levelNum == 0 ? "" : $("#emailTo"+(levelNum-1)).val(),
													"emailBcc": "",
													"mobileNo": ""
													}
												});
				levelNum++;
				self.disableSuppression();
				if(self.escalationDetailArr().length > 1){
					//self.enableSuppression();
					self.enableHighSuppression();
					self.enableMediumSuppression();
					self.enableLowSuppression();
				}

				self.handleEscalationSelCol(0);
   			}
		}

		/*this.enableDisableSuppression = function(){
			//alert("hgsdgjgas");
			self.enableSuppression();
		}*/

		this.disableSuppression = function(){
			for(indx in self.escalationDetailArr()){
				if(indx > 0){
					$("#escHighSeverity"+indx).prop('disabled',true);
					$("#escMediumSeverity"+indx).prop('disabled',true);
					$("#escLowSeverity"+indx).prop('disabled',true);
				}
			}
		}
/*
		this.enableSuppression = function(){
			debugger;
			for(indx in self.escalationDetailArr()){
				if(indx > 0){

					//debugger;
					if(self.escalationDetailArr()[indx-1].highSuppression()!= null &&  self.escalationDetailArr()[indx-1].highSuppression() != ""){
						$("#escHighSeverity"+indx).prop('disabled',false);
					}
					else{
						//$("#escHighSeverity"+indx).prop('disabled',true);
						self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escHighSeverity");
						return false;
					}
					
					if(self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION'){
						if(self.escalationDetailArr()[indx-1].mediumSuppression()!= null && self.escalationDetailArr()[indx-1].mediumSuppression() != ""){
							$("#escMediumSeverity"+indx).prop('disabled',false);
						}
						else
						{
							//$("#escMediumSeverity"+indx).prop('disabled',true);
							self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escMediumSeverity");
							return false;
						
						} 

						if(self.escalationDetailArr()[indx-1].lowSuppression()!= null && self.escalationDetailArr()[indx-1].lowSuppression() != ""){
							$("#escLowSeverity"+indx).prop('disabled',false);
						}
						else
						{
							//$("#escLowSeverity"+indx).prop('disabled',true);
							self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escLowSeverity");
							return false;
							
						}
					}
				}	
			}
		}*/


		this.enableHighSuppression = function(){
			for(indx in self.escalationDetailArr()){
				if(indx > 0){

					//debugger;
					if(self.escalationDetailArr()[indx-1].highSuppression()!= null &&  self.escalationDetailArr()[indx-1].highSuppression() != ""){
						$("#escHighSeverity"+indx).prop('disabled',false);
					}
					else{
						//$("#escHighSeverity"+indx).prop('disabled',true);
						self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escHighSeverity");
						return false;
					}
					
					
				}	
			}
		}

		this.enableMediumSuppression = function(){
			for(indx in self.escalationDetailArr()){
				if(indx > 0){

					if(self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION'){
						if(self.escalationDetailArr()[indx-1].mediumSuppression()!= null && self.escalationDetailArr()[indx-1].mediumSuppression() != ""){
							$("#escMediumSeverity"+indx).prop('disabled',false);
						}
						else
						{
							//$("#escMediumSeverity"+indx).prop('disabled',true);
							self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escMediumSeverity");
							return false;
						
						} 
					}
				}	
			}
		}

		this.enableLowSuppression = function(){
			for(indx in self.escalationDetailArr()){
				if(indx > 0){

					if(self.getAlertProfileTypeSelected() === 'CORE' || self.getAlertProfileTypeSelected() === 'TRANSACTION'){

						if(self.escalationDetailArr()[indx-1].lowSuppression()!= null && self.escalationDetailArr()[indx-1].lowSuppression() != ""){
							$("#escLowSeverity"+indx).prop('disabled',false);
						}
						else
						{
							//$("#escLowSeverity"+indx).prop('disabled',true);
							self.madeOtherLevelsEmpty(indx,self.escalationDetailArr(),"#escLowSeverity");
							return false;
							
						}
					
					}
				}	
			}
		}

		this.madeOtherLevelsEmpty = function(indx,escArr,id){
			for(var i=indx;i<escArr.length;i++){
				$(id+i).val("");
				$(id+i).prop('disabled',true);
			}
		}

		//on click of check box header column
		this.handleEscalationSelAll = function(index){
			for(objData in self.escalationDetailArr()){
				$(".chkboxColEscalation").prop("checked",$("#escalationSelAll").prop('checked'));
			}
			self.handleDeleteState();
			return true;
		}

		this.handleEscalationSelCol = function(index){
			var length = $(".chkboxColEscalation:checked").length;

			if(length == self.escalationDetailArr().length){
				$("#escalationSelAll").prop("checked",true);
			}
			else{
				$("#escalationSelAll").prop("checked",false);
			}
			self.handleDeleteState();
			return true;
		}

		this.handleDeleteState = function(){
			if($(".chkboxColEscalation:checked").length == 0){
				$("#btnEscalationDelete").prop('disabled', true);
			}
			else{
				if($(".chkboxColEscalation:checked").length == 1 && $("#escLevelChkBox0").prop("checked")){
					$("#btnEscalationDelete").prop('disabled', true);
				}
				else{
					$("#btnEscalationDelete").prop('disabled', false);
				}
			}
		}

		this.deleteEscalationDetails = function(){
			//self.rowsTodeleteArr([]);
			showMessageBox(uiConstants.escalationProfile.CONFIRM_DELETE_ESCALATIONS, "question", "confirm", function confirmCallback(confirmDeleteFilter){
				if(confirmDeleteFilter){
					var rowIndx = 0;
					var flag = false;
				
					$(".chkboxColEscalation").parent().find('input:checked').each(function(i) {
		                rowIndx = $(this).parent().parent().index();
		                if(rowIndx != 0){
							if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
								if($("#filterId"+rowIndx).val() != "0"){
									unMappedIdsArr.push(parseInt($("#filterId"+rowIndx).val()));
								}
							}
							self.escalationDetailArr.splice(rowIndx,1);
						}
						else{
							flag = true;
						} 
		            });
					
					if(flag){
						showMessageBox(uiConstants.escalationProfile.VALIDATE_ONE_ESCALATION_LEVEL, "error");
					}

					//clear chekcbox and set to uncheck if there are no levels
					if(self.escalationDetailArr().length == 0){
						$("#escalationSelAll").prop("checked",false);
					}

					self.handleDeleteState();
				}
			});
		}

		/**************Result and Error handlers ****************/
		function successCallback(data, reqType) {
			 if(reqType === "getEscalationTag"){
			 	self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('.panel-body #escalation-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#escalation-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('.panel-body #escalation-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#escalation-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			 }
			 else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_ESCALATION_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.escalationProfile.ERROR_ADD_ESCALATION, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.profileName());
					}
					else{
						params.curPage(1);
					}

					self.cancelAddScreen();
					showMessageBox(uiConstants.escalationProfile.SUCCESS_ADD_ESCALATION);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_ESCALATION_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.escalationProfile.ERROR_UPDATE_ESCALATION, "error");
					}
				}
				else{
					showMessageBox(uiConstants.escalationProfile.SUCCESS_UPDATE_ESCALATION);
					self.cancelAddScreen();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.escalationProfile.ERROR_ADD_ESCALATION, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.escalationProfile.ERROR_UPDATE_ESCALATION, "error");
			}
		}


	}
	escalationAddEdit.prototype.dispose = function() { };
	return { viewModel: escalationAddEdit, template: templateMarkup };
});