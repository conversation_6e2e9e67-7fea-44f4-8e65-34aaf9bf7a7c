<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divEscalationAddEdit">
	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body" id="divEscalationProfile">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>

			<div class="form-group form-required" >
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: profileName,attr:{'title':uiConstants.escalationProfile.ESCALATION_MULTIPLE_NAME_LENGTH_ERROR}" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Description<span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: profileDescription,attr:{'title':uiConstants.escalationProfile.ESCALATION_DESC_INFO}" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

 			<div class="form-group" id="divKpiDataType">
			   <label class="control-label col-sm-2" >Alert Profile Type <span class="mandatoryField">*</span></label>
			   <div class="col-sm-4">
					<select class="chosen form-control" id="alertProfileType" 
							data-bind="event:{'chosen:showing_dropdown': setPreviousVersion},foreach : alertProfileTypesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE"></option>
						<!-- /ko-->
						<option data-bind="value: $data.masterId, text: $data.name"></option>
					</select>
			   </div>
			</div>
			
			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="escalation-tokenfield-typeahead" data-bind="value: tags" >
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<input type="checkbox" id="escalationStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="escalationStatus">
				</div>
			</div>

			<div id="divEscalationSettings" class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Escalation Settings</div>

					<div style="float: left;width: 100%"  class="col-xs-2">	
						<span> ***Note: Atleast one suppression value is mandatory for level 0</span>
					</div>
					<br>
					<br>
					<div class="panel-body" style="padding:14px" >	
						<div class="panel-body">
							<div class="multi-value-row">
								<div style="float: left;" id="divAddEscLevel">
									<button type="button" class="btn btnAddFilter" style="display: inline-block;margin-bottom: 6px;" data-bind="event:{click:addEscalationDetails()}">Add Escalation Levels</button>
								</div>
								<div style="float: right;">
									<button type="button" class="btn btnDeleteFilter" style="margin-bottom: 6px;" data-bind="attr: {id: 'btnEscalationDelete'}, event:{click: deleteEscalationDetails}" >Delete</button>
								</div>
							</div>


							<!-- ko if: showEscalationTable() -->
								<div class="wrapper-scroll-table" style="height: 500px;">
									<table class="table table-bordered table-hover table-striped" style="width:100%;" id="listEscalationSettings">
										<thead>
											<tr class="a1-inner-table-thead">
												<th style="display: none;" rowspan="2">

												</th>
												<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW -->
												<th class="actionControl" rowspan="2">
													<input type="checkbox" id="escalationSelAll" title="Select All"  data-bind="click: handleEscalationSelAll"/>
												</th> <!-- , click:  $parent.handleEscalationSelAll.bind($data) -->
												<!-- /ko-->
											
												<th class="tableHeaderOverflowOmmiter col-xs-1" rowspan="2">Levels</th>

												<th class="tableHeaderOverflowOmmiter col-xs-6" rowspan="2">Escalation Details</th>

												<!-- ko ifnot: (getAlertProfileTypeSelected() ==='CORE' || getAlertProfileTypeSelected() ==='TRANSACTION') -->
													<th class="tableHeaderOverflowOmmiter col-xs-1">
														<span>High Supression</span>
														<br>
														<span>(Count)</span>
													</th>
												<!-- /ko -->

												<!-- ko if: (getAlertProfileTypeSelected() ==='CORE' || getAlertProfileTypeSelected() ==='TRANSACTION') -->
													<th class="tableHeaderOverflowOmmiter col-xs-3" colspan="3">
													Supression (Count)
													</th>
												<!-- /ko -->
											</tr>

											<!-- ko if: getAlertProfileTypeSelected() ==='CORE' || getAlertProfileTypeSelected() ==='TRANSACTION'-->
												<tr class="a1-inner-table-thead"">
													<th class="tableHeaderOverflowOmmiter col-xs-1">High</th>

													<th class="tableHeaderOverflowOmmiter col-xs-1">Medium</th>

													<th class="tableHeaderOverflowOmmiter col-xs-1">Low</th>
												</tr>
											<!-- /ko-->
										</thead>

										<tbody data-bind="foreach: escalationDetailArr">
											<tr>
													<td style="display: none">
														<span data-bind="value: $data.id,attr: {id: 'filterId'+$index()}"></span>	
													</td>
													<td style="display: none">
														<span data-bind="value: $data.tempLevelId"></span>	
													</td>
												<!-- ko if: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW -->
													<td  style="text-align:center">
														<input type="checkbox" title="Select"
														data-bind="value: $index(),click:$parent.handleEscalationSelCol.bind($data,$index()),attr: {id: 'escLevelChkBox'+$index(), class:'chkboxColEscalation'}" />
													</td>
												<!-- /ko-->
													<td  style="text-align:center">
														<span data-bind="text: 'Level '+$index(),value:$data.levelNumber"></span>	
													</td>

													<td>
														<form class="form-horizontal" role="form" >
															<div  class="form-group">
																<label class="control-label table-control-label">Level Name<span class="mandatoryField">*</span></label>

																<input class="form-control table-input" type="text" placeholder="Enter Level Name" 
																	data-bind="value:$data['levelDetails'].levelName, attr: {id: 'levelId'+$index()}">
															</div>

															<div  class="form-group">
																<label class="control-label table-control-label">Email To<span class="mandatoryField">*</span></label>

																<input class="form-control table-input" type="text" placeholder="Enter Email Address" data-bind="value:$data['levelDetails'].emailTo, attr: {id: 'emailTo'+$index()}">
															</div>

															<div class="form-group">
																<label class="control-label table-control-label">Cc</label>

																<input class="form-control table-input" type="text" placeholder="Enter Email Address" data-bind="value:$data['levelDetails'].emailCc, attr: {id: 'emailCc'+$index()}">
															</div>

															<div class="form-group">
																<label class="control-label table-control-label">Bcc</label>
																	
																<input class="form-control table-input" type="text" placeholder="Enter Email Address" data-bind="value:$data['levelDetails'].emailBcc, attr: {id: 'emailBcc'+$index()}">
															</div>

															<div class="form-group">
																<label class="control-label table-control-label">Mobile Number<span class="mandatoryField">*</span></label>

																<input class="form-control table-input" type="text" placeholder="Enter Mobile Number" data-bind="value:$data['levelDetails'].mobileNo, attr: {id: 'mobileNum'+$index()}">
															</div>
														</form>
													</td>
													<td style="text-align:center">
														<input class="form-control" type="number" data-bind="attr: {id: 'escHighSeverity'+$index()}, value: $data.highSuppression,valueUpdate:'afterkeydown',event:{keyup:$parent.enableHighSuppression}" min="1" max="99"  />
													</td>
													<!-- ko if: $parent.getAlertProfileTypeSelected() ==='CORE' || $parent.getAlertProfileTypeSelected() ==='TRANSACTION'-->
														<td data-bind="" style="text-align:center">
															<input class="form-control" type="number" data-bind="attr: {id: 'escMediumSeverity'+$index()},value: $data.mediumSuppression,valueUpdate:'afterkeydown',event:{keyup:$parent.enableMediumSuppression}"  min="1" max="99"/>
														</td>
														<td data-bind="" style="text-align:center">
															<input class="form-control" type="number"  data-bind="attr: {id: 'escLowSeverity'+$index()},value: $data.lowSuppression,valueUpdate:'afterkeydown',event:{keyup:$parent.enableLowSuppression}" min="1" max="99"/>
														</td>
													<!-- /ko-->
											</tr>
										</tbody>
									</table>
								</div> 
							<!-- /ko -->
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5, event:{click: addEditEscalation}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelAddScreen}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>
		</form>
	</div>
</div>
