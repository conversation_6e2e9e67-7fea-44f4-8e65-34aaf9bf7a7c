define(['jquery','text!./change-password.html','hasher','ui-constants','ui-common'], function($,templateMarkup,hasher,uiConstants,uicommon) {

  function ChangePasswordPage(params) {
	var self = this;

    this.renderHandler=function(){
    }
  }

  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  ChangePasswordPage.prototype.dispose = function() { };

  return { viewModel: ChangePasswordPage, template: templateMarkup };

});