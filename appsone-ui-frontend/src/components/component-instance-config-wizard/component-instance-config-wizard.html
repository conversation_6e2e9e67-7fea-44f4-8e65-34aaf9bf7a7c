<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div data-bind="template:{ afterRender: renderHandler}"></div>
<!-- 	<button type="button" class="btn btn-primary" data-bind="event: {click: addServerDetails}">Add Server Details <span class="glyphicon glyphicon-plus a1-glyphicon-action-btn"></span></button>
 -->
 	<!-- <span class="errorMessageField" data-bind="text: errorMsg"></span>	 -->
 	<button type="button" title="Reset" class="btn btn-primary" data-bind="click: onResetClick" style="float: right; margin-right: 20px;">Reset</button>

	<table id="compVersion" class="table" style="width:100%">
	         <thead>
	            <tr >
	               <th>Component Type</th>   
	               <th>Component<span class="mandatoryField">*</span></th>   
	               <th>Version<span class="mandatoryField">*</span></th>  
	               <th>No. of Instances<span class="mandatoryField">*</span></th>  
	               <th>Host<span class="mandatoryField">*</span></th>  
	               <th>Host Version<span class="mandatoryField">*</span></th>   
	               <th class="col-xs-1"></th>
	            </tr>
	         </thead> 
	         <tbody data-bind="foreach : serverDetailsArr">
	         	<tr>
	                <td>
	                	<select  class="form-control chosen" 
	                	         data-bind="options: $parent.componentTypeArr(),
                                 optionsText: 'componentType',
                                 optionsValue: 'componentTypeId',
                                 optionsCaption: 'Select',attr:{ id:'compTypeRowId'+$data.id}, 
                                 event: {'chosen:showing_dropdown': function(){$parent.onCompVersionFocus($data.id)}, change: function(){$parent.onComponentTypeChange($data.id, $index(), 1)}}">
                        </select>
	                </td>
	                <td>

	                	<select  class="form-control chosen"
	                			 data-bind="options: $parent.componentNamesArr()[$data.id],
                                 optionsText: 'componentName',
                                 optionsValue: 'componentId',
                                 optionsCaption: 'Select',attr:{ id:'compNameRowId'+$data.id},
                             	 event: {'chosen:showing_dropdown': function(){$parent.onCompVersionFocus($data.id)}, change: function(){$parent.onComponentNameChange($data.id, $index(), 1)}}">
                        </select> 
	                </td>
	                <td>
	                	<select  class="form-control chosen"
	                			 data-bind="options: $parent.componentVersionsArr()[$data.id],
                                 optionsText: 'version',
                                 optionsValue: 'versionId',
                                 optionsCaption: 'Select',attr:{ id:'compVersionRowId'+$data.id},
                                 event: {'chosen:showing_dropdown': function(){$parent.onCompVersionFocus($data.id)}, change: function(){$parent.onCompVersionChange($data.id, $index(), 0, '')}}"> 
                        </select>
	                </td>
	                <td>
	                	<input type="number" class="form-control num-instances positive-integer" data-bind="value: $parent.noInstancesArr()[$index()], attr:{ id:'noInstanceRowId'+$data.id}" min="1" max="1000" />
	                </td>
	                 <td>
	                	<select  class="form-control chosen"  
	                			 data-bind="options: $parent.componentNameHostsArr(),
                                 optionsText: 'componentName',
                                 optionsValue: 'componentId',
                                 optionsCaption: 'Select',attr:{ id:'compHostRowId'+$data.id}, 
                                 event: {'chosen:showing_dropdown': function(){$parent.onCompVersionFocus($data.id)}, change: function(){$parent.onHostComponentNameChange($data.id)}}">
                        </select>
	                </td>
	                <td>
	                	<select  class="form-control chosen"  
	                			 data-bind="options: $parent.componentHostVersionArr()[$data.id],
                                 optionsText: 'version',
                                 optionsValue: 'versionId',
                                 optionsCaption: 'Select',attr:{ id:'compHostVersionRowId'+$data.id},
                                 event: {'chosen:showing_dropdown': function(){$parent.onCompVersionFocus($data.id)}, change: function(){$parent.onCompVersionChange($data.id, $index(), 1, '')}}"></select>
	                </td>
	                <td>
	                	<button type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete" data-bind="event: {click: function(){$parent.deleteServerDetails($index())}},visible:(isMasterData && $index() != 0)?1:0 "></button> <!--  -->
	                	<span>&nbsp;&nbsp;</span>
	                	<button type="button" class="glyphicon glyphicon-plus"  title="Add" data-bind="event: {click: function(){$parent.addServerDetails($index())}},visible:($index() == $parent.serverDetailsArr().length-1)"></button>
	                </td>
	            </tr>
	         </tbody>
	</table>

	<button type="button" class="btn btn-primary" data-bind="attr: {enabled:enableConfigServerDetailsBtn},event: {click:  function(){configureServerDetails(true)}}">Configure Server Details <span class="glyphicon a1-glyphicon-action-btn"></span></button>
	

	<div id="divInstDet" data-bind="foreach: instanceDetailArr">
		 <add-multiple-component-instance-general data-bind="attr:{id: 'ele'+$data.eleId}" params="{componentsArr: $data.componentsArr,currentViewIndex:'0', pageSelected: $data.componentType,selcomponentTypeId: $data.componentTypeId,selComponentId:$data.componentId,selVersionId:$data.versionId,numInst:$data.numberInst,selHostId:$data.hostId,selHostVersion:$data.hostVersionId,mode:'componentWizard',applicationId: $parents[0].applicationId,applicationsArr: $parents[0].applicationsArr, noInstancesArr: $parents[0].noInstancesArr, instanceDetailArr: $parents[0].instanceDetailArr, instanceDeleted: $parents[0].instanceDeleted, rowIndex: $index, panelTitle: $data.panelTitle, allElementsLoaded: $parents[0].allElementsLoaded, newCompTypeInstPanelsCount: $parents[0].newCompTypeInstPanelsCount, deletedCompInstArr: $parents[0].deletedCompInstArr, compTypeInstArr: $parents[0].compInstDataForApp(), compInstanceMode: $parents[0].compInstanceMode, instTypeIndex: $index()}"> 	
		</add-multiple-component-instance-general> 
	</div>

	<button type="button" class="btn btn-primary" data-bind="visible: instanceDetailArr().length != 0, event: {click: getAllIdentifiedHosts}">Configure Host Details <span class="glyphicon a1-glyphicon-action-btn"></span></button>
	

	<div class="form-group" style="margin-bottom: 0px;" data-bind="visible:showHosts">
		<div class="panel panel-default inner-panel">
			<div class="configPanel panel-heading"><a id="imgCompAgentShowHide" class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="event: {click: expandCollapsePanel1.bind($data, hostInstPanel, imgCompAgentShowHide)}" title="Expand"><span class="panel-label-expand-collapse"> Hosts</span></a> 
				<!--  <span type="button"  title="Refresh" class="glyphicon glyphicon-refresh glyphicon-refresh-white" data-bind="click: refreshHostDetails" style="float:right"></span> --></div>
			 <div id="hostInstPanel" class="panel-body inner-div-container" style="height: auto; padding-bottom: 0px; display: none;"> 
			 	<table id="compHosts" class="table" style="width:100%">
		         <thead>
		            <tr >
		               <th class="col-xs-2">Name</th>   
		               <th class="col-xs-2">Host<span class="mandatoryField">*</span></th>   
		               <th class="col-xs-1">Host Version<span class="mandatoryField">*</span></th>  
		               <th class="col-xs-1">Host Address<span class="mandatoryField">*</span></th> 
		               <th class="col-xs-4">Host Attributes</th> 
		               <th class="col-xs-1"> </th> 
		               <th data-bind="visible: false"></th>
		            </tr>
		         </thead> 
		         <tbody data-bind="foreach : hostDetailsArr">
		         	<tr class="host-details">
		         		<td>
		                	<input type="text" class="form-control" data-bind="attr:{ id:'HostName'+$data.id},value:$data.hostInstanceName,style:{'opacity': $data.hostsIdentified != 1?'' :0.6}" /> <!--,css:{'disabled':$data.hostsIdentified == 0}  -->
		                </td>
		                <td>
		                	<select  class="form-control"  
		                			 data-bind="options: $parent.componentNameHostsArr(),
	                                 optionsText: 'componentName',
	                                 optionsValue: 'componentId',
	                                 optionsCaption: 'Select',attr:{ id:'compHostDetailsRowId'+$data.id}, 
	                                 value:$data.hostComponentId,
	                                 event: {change: function(){$parent.onHostComponentNameChangeForHostDetails($data.id)}},
	                             	style:{'opacity': $data.hostsIdentified != 1?'' :0.6},css:{'disabled':$data.hostsIdentified == 1}"> 
	                        </select>
		                </td>
		                <td>
		                	<select  class="form-control"  
		                			 data-bind="options: $parent.componentHostDetailsVersionArr()[$data.id],
	                                 optionsText: 'version',
	                                 optionsValue: 'versionId',
	                                 optionsCaption: 'Select',
	                                 value:$data.hostsVersionId,attr:{ id:'compHostInstVersionRowId'+$data.id},css:{'disabled':$data.hostsIdentified == 1},style:{'opacity': $data.hostsIdentified != 1?'':0.6},event:{change : $parent.onHostVersionChange($data.id, $index())}"></select> 
		                </td>
		                <td>
		                	<input type="text" class="form-control" 
		                	 data-bind="attr:{ id:'hostAddress'+$data.id},
		                	 textInput:$data.hostAddress,
		                	 css:{'disabled':$data.hostsIdentified == 1},
		                	 style:{'opacity': $data.hostsIdentified != 1?'' :0.6},
		                	 event:{'keyup':$parent.onHostAddressChange($data.id)}"/> 
		                </td>
		                <td>
		                	<div class="form-group">
								<div class="panel panel-default inner-panel" style="margin-bottom: 0px;">
									<div class="configPanel panel-heading"><a class="glyphicon glyphicon-triangle-right block-glyphicon" data-bind="attr: {id: 'imgAttribShowHide'+$data.id}, event: {click: $parent.expandCollapsePanel.bind($data, $('#attribsPanel'+$data.id), '#imgAttribShowHide'+$data.id, $data.id)}" title="Expand"><span class="panel-label-expand-collapse">Attributes</span></a></div>
									
				                	<div class="panel-body inner-div-container" style="display: none;" data-bind="attr:{ id:'attribsPanel'+$data.id}">

				                		
										<div>
											<div class="form-group form-required Finner-panel-form">
												<table data-bind="attr: {id: 'compHostAttributes'+$index()}">
													<tbody data-bind="foreach : $parent.compAttributesArr()[$index()]">
														<tr>
															<td class="col-sm-5">
																<span data-bind="text: $data.attributeName"></span> <span data-bind="visible : $data.isMandatory" class="mandatoryField">*</span>
																<!-- <span data-bind="text:$component.observableHostAddress"></span> -->
															<td>

															<td  class="col-xs-7 hostAttribvalues">
																<!-- ko if: $data.attributeType.toUpperCase() == 'TEXTBOX' -->
																	<input type="text"  data-bind="textInput: ($data.attributeName == 'HostAddress')?$parent.hostAddress:$data.attributeDefaultValue,attr:{'id': trimSpacesReplaceUnderscore($data.attributeName)+$parentContext.$index(), 'class': 'form-control txtbox'+$index(), 'placeholder': 'Enter ' + $data.attributeName}, 'disable': ($data.attributeName == 'HostAddress')" max="45" pattern="[a-zA-Z0-9_ ]{2,45}"/>
																<!-- /ko-->

																<!-- ko if: $data.attributeType.toUpperCase() == 'PASSWORD' -->
																	<input type="password" class="form-control" data-bind="attr:{'id': trimSpacesReplaceUnderscore($data.attributeName)+$parentContext.$index(), 'placeholder': 'Enter ' + $data.attributeName}" max="45">
																<!-- /ko-->

																<!-- ko if: $data.attributeType.toUpperCase() == 'DROPDOWN' -->
																	<select class="form-control" data-bind="foreach : $data.attributeOptions, attr:{'id': trimSpacesReplaceUnderscore($data.attributeName)+$parentContext.$index(), 'placeholder': 'Select ' + $data.attributeName}" max="45">

																	<!-- ko if: $index() == 0 -->
																		<option data-bind="value: '', text: uiConstants.common.SELECT"></option>
																	<!-- /ko-->

																	<option data-bind="value: $data.attributeOptionId, text: $data.attributeOptionName"></option>
																	</select>
																<!-- /ko-->

																<!-- ko if: $data.attributeType.toUpperCase() == 'CHECKBOX' -->
																	<input type="checkbox" data-bind="attr:{'id': trimSpacesReplaceUnderscore($data.attributeName)+$parentContext.$index(), }">
																<!-- /ko-->
															</td>
														</tr>
												    </tbody>
												</table>

												
											</div>
									    </div>
									   
									</div>
								</div>
							</div>
		                </td>
		                <td>
		                	
		                	<button type="button" class="glyphicon glyphicon-remove buttondelete"  title="Delete" data-bind="event: {click: function(){$parent.deleteHostDetails($index(), $data.id)}},visible:($data.hostsIdentified == 0)?1:0"></button>
		                	<span>&nbsp;&nbsp;</span>
		                	<button type="button" class="glyphicon glyphicon-plus"  title="Add" data-bind="event: {click: function(){$parent.addHosts($index())}},visible:($index() == $parent.hostDetailsArr().length-1)"></button>
		                </td>

		                <td data-bind="visible: false">
		                	<!-- <span data-bind="attr: {id: $data.compInstanceId}"> -->
		                	<span data-bind="attr: {id: 'hostId'+$data.id}">
		                </td>
		         	</tr>
		         </tbody>
		        </table> 
			 </div>	
		</div>
	</div>
<br>
<!-- <div style="float:left">
	 <button  type='button' class='btn button-next' id="btnCompInstWizardSave" data-bind="click: onMultipleSaveClick">Save</button> 
</div> -->