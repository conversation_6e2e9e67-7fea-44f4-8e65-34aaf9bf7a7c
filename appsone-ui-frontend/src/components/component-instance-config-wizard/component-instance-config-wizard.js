define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./component-instance-config-wizard.html','hasher','validator','ui-constants','ui-common','jQuery-plugins'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,jQueryPlugins) {

function Componentinstanceconfigwizard(params) {
	var self = this;
	var indexCount = 0;
	var resHostDetailsArr=[];
	var hostsTableRowIndex = -1;

	this.pageSelected=ko.observable(0);//required for componentinstance multiple add
	this.panelTitle=ko.observable();//required for componentinstance multiple add
	this.currentViewIndex=ko.observable(0);//required for componentinstance multiple add
	this.componentsArray = params.componentsArr; //required for both componentinstance and component insatancewizard multiple add
	//this.instanceDetailArr=params.instanceDetailArr;
	this.applicationsArr=params.applicationsArr;
	this.applicationId = params.applicationId;
    this.applicationTypeId=params.applicationTypeId;


	//copied variables from instance
	this.errorStack = ko.observableArray();
	this.reqRecordCounter = ko.observableArray();
    this.multiRowAddLimit = ko.observable(1001);
    this.errorStackLimitOnRows = ko.observable(10);
    this.requestDataArray = [];
    this.tableHeaders=ko.observableArray();
    this.widgetName = ko.observable();
    this.errorMsg=ko.observable('');
    this.firstFieldToShowErr = ko.observable("");
    self.compAttributesArr=ko.observableArray();
  
    //add server param details- START
    this.instanceDetailArr = ko.observableArray();
    this.instanceDetailAddArr = ko.observableArray();
    this.serverDetailsArr = ko.observableArray();
    this.componentsArr = ko.observableArray();
    this.componentTypeArr= ko.observableArray();
    this.componentNamesArr=ko.observableArray();
    this.componentVersionsArr=ko.observableArray();
    this.componentTypeHostsArr=ko.observableArray();
    this.componentNameHostsArr=ko.observableArray();
    this.componentHostVersionArr=ko.observableArray();
    this.componentHostDetailsVersionArr=ko.observableArray();

    this.hostDetailsArr=ko.observableArray();
    this.observableHostAddress=ko.observable();
    this.showHosts=ko.observable(false);
    this.addedCompInstanceData=ko.observable();
    this.kpiProducerMapArr = ko.observableArray();
    this.compInstDataForApp = ko.observable();
    this.compInstanceMode = ko.observable("Add");

    this.applicationTypeOnEdit = ko.observable();	
    this.isServerDetailHasData = ko.observable();
    this.noInstancesArr = ko.observableArray();
    this.instanceDeleted = ko.observable("");
    this.allElementsLoaded = ko.observable(false);
    this.isCompInstToLoad = params.isCompInstToLoad;
    this.curActiveTab = params.curActiveTab;
    this.hostsArr = ko.observableArray();

    //Get all the tags from DS & store in compInstIdAllTags in the format: 
    //self.compInstIdAllTags([{"tagName": <tagName>, "tagId": <tagId>}, {"tagName": <tagName>, "tagId": <tagId>}])
    this.compInstIdAllTags = ko.observableArray([{
                "tagId": 6,
                "tagName": "tagtest123"
              }]);

    this.compInstDetails = ko.observableArray([]);

   
    var componentNamesTempArr = [];
	var componentVersionsTempArr = [];
	var componentHostNamesTempArr = [];
	var componentHostsVersionsTempArr = [];
	var componentHostsDetailsVersionsTempArr = [];
	var arrHost=[];

	var counterValue=0;
	var hostCounterValue=0;
	var prevCompDetId = "";
	var prevCompText = "";
	var prevHostCompText = "";
	var prevCompHostDetId = "";
	var identifiedHostAddressArr = [];
	var hostNameVersionId = "";
	this.hostNameVersionIdObj = ko.observable({});
	this.appId = ko.observable();
	this.newCompTypeInstPanelsCount = ko.observable(0);
	var isEditFirstLoad = false;
	var isConfigServerBtnClicked = false;
	this.deletedCompInstArr = ko.observableArray([]);
	var hostVersionIdsArr = [];
	var allInstanceHostPort = [];
	var allInstanceHostVersion = [];

	//this.existingInstancesArr = ko.observableArray();
	//this.deletedInstancesArr = ko.observableArray();

    //add server param details- END

	this.renderHandler = function(){
		/*jQuery(".chosen").chosen({
			search_contains: true	
		});*/

	 	$('#btnNext').prop('disabled', false);

	 	$('#compInstancesTab').on('click', function(){
	 		if(self.curActiveTab() != ""){
		 		window.compInstDetailsClicked(true);
	 		}
	 	});	

	 	$('#compInstancesTab').on('change', function(){
	 		if(window.compInstDetailsClicked() && self.curActiveTab() != ""){
	 			window.compInstDetailsChaged(true);
	 		}
	 	});

	 	

	 	/*$('#compInstancesTab').on('change', function(){
	 	});*/
	 	//self.applicationId(2);//8
	 	//self.getServerDetailsByType(90);//90:finacleReplica

	 	/*$("tbody").on('keyup', '.num-instances', function(e){

	 		if ($(this).data('val')!=this.value) {
			    delay(function(){
			    	var eleId=e.target.id.substr(15);
			    	self.noInstancesArr.splice(eleId, 1, $('#noInstanceRowId'+eleId).val());
			    	console.log(self.noInstancesArr());
			    	self.configureServerDetails(eleId);
			    }, 1000 );
			}

			$(this).data('val', this.value);
	      });*/
	}

	this.onResetClick = function(){

		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_RESET_COMP_INST, "question", "confirm", function confirmCallback(confirmReset){
			if(confirmReset){
				self.isCompInstToLoad(false);
				self.isCompInstToLoad(true);
				self.curActiveTab("");

				$('.nav-tabs a[href=#dummyTab]').tab('show') ;
				setTimeout(function(){$('.nav-tabs a[href=#compInstancesTab]').tab('show') ; }, 500);
			}
		});
	}

	self.allElementsLoaded.subscribe(function(allEleLoaded) {
		if(allEleLoaded && !isConfigServerBtnClicked){
    		if(self.compInstanceMode() == "Add"){
    			self.procesInstanceDetails();
    		}
    		else if(self.compInstanceMode() == "Edit"){
    			if(isEditFirstLoad){
					/*var compTypeInstArr = self.compInstDataForApp()['componentTypeInstances'];
					var compTypeIdStrPart = "";
					var compInstArr = [];
					var type = "";*/
					self.deletedCompInstArr([]);


					self.compInstDataForApp()['componentTypeInstances'] = [];




					/*for(var compTypeInst in compTypeInstArr){
						compTypeIdStrPart = trimSpacesReplaceUnderscore(compTypeInstArr[compTypeInst].componentType)+compTypeInst;
						compInstArr = compTypeInstArr[compTypeInst].componentInstances;

						console.log(compInstArr);
						for(var compInst in compInstArr){
							$("#"+compTypeIdStrPart + "Name" + compInst).val(compInstArr[compInst].componentInstanceName);
							$("#"+compTypeIdStrPart + "compInst" + compInst).text(compInstArr[compInst].componentInstanceId);
							//self.existingInstancesArr.push({self.serverDetailsArr()[compTypeInst].id+"_"+compInst : compInstArr[compInst].componentInstanceId});

							for(var compAttrib in compInstArr[compInst].attributes){
								type = compInstArr[compInst].attributes[compAttrib].attributeType;

							//	alert(("#"+compTypeIdStrPart + compInstArr[compInst].attributes[compAttrib].attributeName + compInst));

								if(type.toUpperCase() == 'PASSWORD' || type.toUpperCase() == 'TEXTBOX'){
									$("#"+compTypeIdStrPart + compInstArr[compInst].attributes[compAttrib].attributeName + compInst).val(compInstArr[compInst].attributes[compAttrib].attributeValue);
								}
								else if(type.toUpperCase() == 'DROPDOWN'){
									$("#"+compTypeIdStrPart + compInstArr[compInst].attributes[compAttrib].attributeName + compInst).val(compInstArr[compInst].attributes[compAttrib].attributeValue);
								}
								else if(type.toUpperCase() == 'CHECKBOX'){
									$("#"+compTypeIdStrPart + compInstArr[compInst].attributes[compAttrib].attributeName + compInst).prop('checked',compInstArr[compInst].attributes[compAttrib].attributeValue);				
								}
							}
						}
					}*/



					//requestCall(uiConstants.common.SERVER_IP + "/host", "GET", "", "getHosts", successCallback, errorCallback);

					self.getAllIdentifiedHosts();
					//debugger;

					var hostVersionIdStr = "";
					for(var hostVersionId in hostVersionIdsArr){
						hostVersionIdStr+="&versionId="+hostVersionIdsArr[hostVersionId];
					}

					if(hostVersionIdStr){
						requestCall(uiConstants.common.SERVER_IP + "/componentAttribute?"+hostVersionIdStr.substring(1), "GET", "", "getMultipleComponentAttributes", successCallback, errorCallback);
					}


				}
				else{
    				self.procesInstanceDetails();
				}
			}


		 	
        }
    	
    	//self.allElementsLoaded(false);
	});

	self.instanceDeleted.subscribe(function(valArr) {
		if(self.instanceDeleted() != ""){

			var rowIndexArr = valArr[0].split("delete");
			
	    	if(allInstanceHostVersion[valArr[1]] && allInstanceHostVersion[valArr[1]][rowIndexArr[1]]){
	    		performHostDelete(valArr[1], rowIndexArr[1], false);
				allInstanceHostPort[valArr[1]].splice(rowIndexArr[1], 1);
				allInstanceHostVersion[valArr[1]].splice(rowIndexArr[1], 1);
	    	}
		    self.instanceDeleted("");
		}
	});

	/*this.onKeyUpEventForNumInstance=function(rowId){
		self.configureServerDetails(rowId);
	}*/

	/****Adding Server Parameters : START *****/
	this.getServerDetailsByType=function(selectedApplicationType){
		self.compInstanceMode("Add");
		isEditFirstLoad = false;
		console.log("server details type id : "+selectedApplicationType);	
		if(selectedApplicationType != undefined){
			requestCall(uiConstants.common.SERVER_IP + "/component/"+selectedApplicationType, "GET", "", "getCompTypeVersionForApplicationType", successCallback, errorCallback);
			//requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getHostTypeVersion", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/host", "GET", "", "getHosts", successCallback, errorCallback);
		}
		/*else{
			requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
		}*/
	}

	uicommon.postbox.subscribe(function(value) {
		self.getServerDetailsByType(value);
	},"triggerGetServerDetails");

	this.getServerDetailsForApplication=function(appIdWithTypeId){
		self.compInstanceMode("Edit");
		isEditFirstLoad = true;

		var appWithTypeId=appIdWithTypeId.split(":",2);

		self.applicationTypeOnEdit(appWithTypeId[1]);
		console.log("Application id on edit of wizard: "+appIdWithTypeId +" : "+appWithTypeId[0]+" : "+ appWithTypeId[1]);	

		//if(appWithTypeId[0] != undefined && appWithTypeId[1] != undefined){
			

		//}

		self.appId(appWithTypeId[0]);
	}

	uicommon.postbox.subscribe(function(value) {
		if(!isEditFirstLoad){
			//self.compInstanceMode("Edit");
			//requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getHostTypeVersion", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/host", "GET", "", "getHosts", successCallback, errorCallback);

			self.getServerDetailsForApplication(value);
		}

	},"triggerGetServerDetailsForApplication");

    this.addServerDetails = function(){
    	self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":ko.observable(''), "hostdetails":0, "hostVersion": 0 ,"isMasterData":1});
    	self.noInstancesArr.push(1);
	    $(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });

    	componentNamesTempArr.push([]);
    	componentVersionsTempArr.push([]);
    	componentHostsVersionsTempArr.push([]);

		$('#compTypeRowId'+(counterValue-1)).trigger('chosen:updated');
		jQuery(".chosen").chosen({
			search_contains: true	
		});
		
    }



    //this.configureServerDetails = function(versionEleId){
    this.configureServerDetails = function(val){
    	debugger;
    	if(self.hostDetailsArr().length){
    		self.showHosts(true);
		}
		else{
    		self.showHosts(false);
		}

		/*for(var serverDert in self.serverDetailsArr()){
		}*/
		
    	isConfigServerBtnClicked = val;
		self.errorMsg("");
		self.firstFieldToShowErr("");
		//$("body").find("div, input, textarea, select").removeClass("error-highlighter");

		//compInstValidationMsg="";

    	//var compVersion = versionEleId != null ? $("#compVersionRowId"+versionEleId+" option:selected").text() : null;
		//var hostVersion = versionEleId != null ? $("#compHostVersionRowId"+versionEleId+" option:selected").text() : null;

		//if(versionEleId == null || (compVersion != "Select" && compVersion != "" && hostVersion != "Select" && hostVersion != "")){

	    	var compInstValidationMsg=self.jsonFormatForCompInstValidation();
			if(compInstValidationMsg == ""){
		       /* self.errorMsg(compInstValidationMsg);
		    }
		    else{*/
		    	//self.instanceDetailArr.removeAll();
	    		self.jsonFormatForCompInstPopulation();
	    	}
	    //}
    }

    this.jsonFormatForCompInstPopulation=function(){
      console.log("------------------Json server details population---------------------------");

      var newAddIndex = self.instanceDetailArr().length;

      self.newCompTypeInstPanelsCount(self.serverDetailsArr().length - self.instanceDetailArr().length);
		//self.currentCompTypesCount(newAddIndex);
			//self.panelTitlesObj({});


      	$('#compVersion tbody tr').each(function(i){  
      		//panelTitlesObj[self.serverDetailsArr()[i].id] = 
      		if(i>=newAddIndex){
      			self.allElementsLoaded(false);
	       		var cobj = {};
	        	//self.noInstancesArr([]);
	            $(this).children('td').each(function(j){
	            	 // self.noInstancesArr.push($('#noInstanceRowId'+i).val());

		              if(j==0){
		                cobj['componentType'] = $('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text()+self.serverDetailsArr()[i].id;
		                cobj['eleId'] = self.serverDetailsArr()[i].id;
		                cobj['componentTypeId'] = parseInt($(this).children('select').val());

		               	cobj['panelTitle'] =  $('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text() + " " + ($.grep(self.instanceDetailArr(), function(e){ return e.panelTitle.startsWith($('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text()); }).length+1);
		              }
		              else if(j==1){
		                cobj['componentId'] = parseInt($(this).children('select').val());
		                //cobj['componentName'] = $('#compNameRowId'+i+' option:selected').text();
		              }
		              else if(j==2){
		                cobj['versionId'] = parseInt($(this).children('select').val());
		              }
		              else if(j==3){
		                cobj['numberInst'] =  parseInt($('#noInstanceRowId'+self.serverDetailsArr()[i].id).val());
		              }
		              else if(j==4){
		                cobj['hostId'] =  parseInt($(this).children('select').val());
		                cobj['hostComponentName'] = $('#compHostRowId'+self.serverDetailsArr()[i].id+' option:selected').text();
		              }
		              else if(j==5){
		                cobj['hostVersionId'] =  parseInt($(this).children('select').val());
		                cobj['hostVersionName'] = $('#compHostVersionRowId'+self.serverDetailsArr()[i].id+' option:selected').text();
		              }

	            });
	            cobj['componentsArr'] = self.componentsArr();
	            self.instanceDetailArr.push(cobj);
	            console.log("Wizard : instanece detail array");
	            console.log(self.instanceDetailArr());
        	}

        	else{

        		var instCount = self.noInstancesArr()[i] - self.instanceDetailArr()[i].numberInst;//parseInt($('#noInstanceRowId'+self.serverDetailsArr()[i].id).val());
        		self.instanceDetailArr()[i].numberInst = self.noInstancesArr()[i];

        		for(var inst=0; inst<instCount; inst++){
        			$("#ele"+self.serverDetailsArr()[i].id+" #btnCompInstMultipleAdd").trigger("click");
        		}
        	}
        });
    }

    this.jsonFormatForCompInstValidation=function(){
        console.log("------------------Json componentinstance validation---------------------------");
        var validatedMessage="";
        $('#compVersion tbody tr').each(function(i){    
              $(this).children('td').each(function(j){
              	if(j==0){
              	  	if($(this).children('select').val() == 0){
              	  		showError($(this).children('select'), "Select Component Type");
			            self.errorMsg($(this).children('select'));
                        //validatedMessage="Please select component type at line "+(i+1);
                        //return false;
                        validatedMessage = "error";
                  	}
	                else{
	                	removeError($(this).children('select'));
	                }
	           	}
                if(j==1){
					if($(this).children('select').val() == 0){
                  		showError($(this).children('select'), "Select Component Name");
			            self.errorMsg($(this).children('select'));
                        //validatedMessage="Please select component name at Line "+(i+1);
                        //return false;
                        validatedMessage = "error";
                	}
                 	else{
                  		removeError($(this).children('select'));
                  	}    
                }
                if(j==2){
                	if($(this).children('select').val() == 0){
                  		showError($(this).children('select'), "Select Component Version");
			            self.errorMsg($(this).children('select'));
                        //validatedMessage="Please select version for selected component name at line "+(i+1);
                        //return false;
                        validatedMessage = "error";
					}
					else{
						removeError($(this).children('select'));
					}
	            }
				if(j==3){
					if($(this).children('input').val() == ""){
						showError($(this).children('input'), "Please provide number of instances to create");
					    self.errorMsg($(this).children('input'));
                        validatedMessage = "error";
					    //validatedMessage="Please provide number of instances to create at line "+(i+1);
					    //return false;
					}
					else if($(this).children('input').val() == "0"){
                  		showError($(this).children('input'), "Instance(s) count cannot be zero");
			            self.errorMsg($(this).children('input'));
                        validatedMessage = "error";
                        //validatedMessage="Instance(s) count at line "+(i+1)+" cannot be zero";
                        //return false;
					}
					else if(self.instanceDetailArr()[i] && $(this).children('input').val() < self.instanceDetailArr()[i].numberInst){
					   //alert(self.instanceDetailArr()[i].numberInst);
					   	showError($(this).children('input'), "Instance(s) count cannot be less than instances configured");
					    self.errorMsg($(this).children('input'));
                        validatedMessage = "error";
					    //validatedMessage="Instance(s) count at line "+(i+1)+" cannot be less than instances configured";
					    //return false;
					}
					else if(parseInt($(this).children('input').val()) > 1000){
					   //alert(self.instanceDetailArr()[i].numberInst);
					   	showError($(this).children('input'), "Instance(s) count cannot be greater than 1000");
					    self.errorMsg($(this).children('input'));
                        validatedMessage = "error";
					    //validatedMessage="Instance(s) count at line "+(i+1)+" cannot be greater than 1000";
					    //return false;
					}
					else{
						removeError($(this).children('input'));
					}
				}
                  
				if(j==4){
					if($(this).children('select').val() == ""){
						showError($(this).children('select'), "Please select Host component name");
				    	self.errorMsg($(this).children('select'));
                        validatedMessage = "error";
				    //validatedMessage="Please select Host component name at line "+(i+1);
				    //return false;
					}
					else{
						removeError($(this).children('select'));
					}
				}
				
				if(j==5){
					if($(this).children('select').val() == ""){						
						showError($(this).children('select'), "Please select host version");
				    	self.errorMsg($(this).children('select'));
                        validatedMessage = "error";
				    //validatedMessage="Please select version for selected host name type at line "+(i+1);
				    //return false;
					}
					else{
						removeError($(this).children('select'));
					}
				}
              });
         }); 
        return validatedMessage;
    }

    function performHostDelete(rowIndex, inst, isServerDetDelete){
		var widgetNameStr = trimSpacesReplaceUnderscore(self.instanceDetailArr()[rowIndex].componentType);
		var hostAddrVerArr = [];
		//for(var inst=0; inst<initialNoInst; inst++){
			if(allInstanceHostVersion[rowIndex] && allInstanceHostVersion[rowIndex][inst]){
				hostAddrVerArr = allInstanceHostVersion[rowIndex][inst].split("~");

				if(removeHostDetails(widgetNameStr, rowIndex, inst, isServerDetDelete)){
					var hostToDeleteObj = $.grep(self.hostDetailsArr(), function(e){ return e.hostAddress() == hostAddrVerArr[0] && e.hostsVersionId == hostAddrVerArr[1]; });

					if(hostToDeleteObj.length){
						var hostToDeleteIndex = self.hostDetailsArr.indexOf(hostToDeleteObj[0]);
						self.hostDetailsArr.splice(hostToDeleteIndex, 1);
					}	
				}
			}
			
		//}
    }

    this.deleteServerDetails = function(rowToDelete){
    	showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_DELETE_SERVER_DETAILS, "question", "confirm", function confirmCallback(confirmDeleteServerDetails){
			if(confirmDeleteServerDetails){
		    	//removeHostDetails(self.serverDetailsArr()[rowToDelete].id);
    			var initialNoInst = $("#ele"+self.serverDetailsArr()[rowToDelete].id + " #tableCompInstAddMultiple tbody tr").length;
    			for(var indx=0; indx<initialNoInst; indx++){
	    			performHostDelete(rowToDelete, indx, true);
    			}
	    		allInstanceHostPort.splice(rowToDelete, 1);
				allInstanceHostVersion.splice(rowToDelete, 1);

		    	/*var tableRowId = $("#ele"+self.serverDetailsArr()[rowToDelete].id + " #tableCompInstAddMultiple tbody tr").attr("id");
				var hostIndexPart = tableRowId.substring(0, tableRowId.lastIndexOf("row_"));
				var initialNoInst = $("#ele"+self.serverDetailsArr()[rowToDelete].id + " #tableCompInstAddMultiple tbody tr").length;
				for(var hostInst=0; hostInst<initialNoInst; hostInst++){
            		removeHostDetails(hostIndexPart+"_0");
				}*/

		    	self.serverDetailsArr.splice(rowToDelete,1);
		    	self.instanceDetailArr.splice(rowToDelete,1);
		    	self.noInstancesArr.splice(rowToDelete,1);

		    	var panelTilesArr = [];
      			$('#compVersion tbody tr').each(function(i){
      				$("#panel"+i).text($('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text() + " " + ($.grep(panelTilesArr, function(e){ return e.startsWith($('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text()); }).length+1));
      				panelTilesArr.push($('#compTypeRowId'+self.serverDetailsArr()[i].id+' option:selected').text());
			    });

		    }
		});
    }

    this.onComponentTypeChange = function(eleId, rowIndex, isChangeEvent){

    	/*if(confirmRequired){

    	}*/		
    	if(isChangeEvent){
			self.onCompVersionChange(eleId, rowIndex, false, "compType");
    	}

    	//self.errorMsg("");
		if($("#compTypeRowId"+eleId+" option:selected").text() != "Select"){
	    	var componentsTypeObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#compTypeRowId"+eleId).val(); });	
			componentNamesTempArr.splice(eleId, 1, componentsTypeObj[0].components);
		}
		else{
			componentNamesTempArr.splice(eleId, 1, []);
		}
		self.componentNamesArr(componentNamesTempArr);
		$('#compNameRowId'+rowIndex).trigger('chosen:updated');
			


		//self.onCompVersionChange(eleId, rowIndex);
		//self.serverDetailsArr()[eleId].component=componentsTypeObj[0].components;

	}

	this.onComponentNameChange = function(eleId, rowIndex, isChangeEvent){
    	if(isChangeEvent){
			self.onCompVersionChange(eleId, rowIndex, false, "comp");
    	}

    	//self.errorMsg("");
		//console.log(eleId);
		//console.log(self.componentNamesArr());
		//console.log( $("#compNameRowId"+eleId).val());
		if($("#compNameRowId"+eleId+" option:selected").text() != "Select"){
			var componentsObj = $.grep(self.componentNamesArr()[eleId], function(e){ if(e.componentId == $("#compNameRowId"+eleId).val()) return e; });
			componentVersionsTempArr.splice(eleId, 1, componentsObj[0].versions);
		}
		else{
			componentVersionsTempArr.splice(eleId, 1, []);
		}
		self.componentVersionsArr(componentVersionsTempArr);
		$('#compVersionRowId'+rowIndex).trigger('chosen:updated');


		//self.onCompVersionChange(eleId, rowIndex);
	}

	this.onHostComponentNameChange = function(rowId){
    	//self.errorMsg("");
    	var componentsHostVerObj = $.grep(self.componentNameHostsArr(), function(e){ 
    	if(e.componentId == $("#compHostRowId"+rowId).val()) return e; });
		componentHostsVersionsTempArr.splice(rowId, 1, componentsHostVerObj[0].versions);
		self.componentHostVersionArr(componentHostsVersionsTempArr);
		$('#compHostVersionRowId'+rowId).trigger('chosen:updated');
	}

	this.onHostVersionChange = function(eleId, rowIndex){
		if($("#compHostInstVersionRowId"+eleId+" option:selected").text() != "Select" && $("#compHostDetailsRowId"+eleId).val() && $("#compHostInstVersionRowId"+eleId).val()){
			hostNameVersionId = $("#compHostDetailsRowId"+eleId).val() + "_" + $("#compHostInstVersionRowId"+eleId).val();
			hostsTableRowIndex = rowIndex;
			self.setCompAttributes(hostNameVersionId);
		}
	}

	/******************** host component nd version event ********************/
	this.onHostComponentNameChangeForHostDetails = function(eleId){
	
	 	var componentsHostVerObj = $.grep(self.componentNameHostsArr(), function(e){ 
    	if(e.componentId == $("#compHostDetailsRowId"+eleId).val()) return e; });
		componentHostsDetailsVersionsTempArr.splice(eleId, 1, componentsHostVerObj[0].versions);
		self.componentHostDetailsVersionArr(componentHostsDetailsVersionsTempArr);

	}

	this.enableConfigServerDetailsBtn = function(param){
		 var res=false;
		 res=param;
		 return res;
	}


	this.validateHostAttribDetails = function(rowIndx){
		 var validatedMessage="";
		 $('#compHostAttributes'+rowIndx+' tbody tr').each(function(i){ 
		  	  $(this).children('td').each(function(j){
		  	  		//this.
		  	  });
		 });
		 return validatedMessage;
	}

	this.validateHostDetails = function(){
		//console.log("------------------Host details validation---------------------------");
        var validatedMessage="";
        $('tr.host-details').each(function(i){  //tr.host-details
        
              $(this).children('td').each(function(j){
              	  if(j==1){
	               	  if($(this).children('select').val() == 0){
	                        //validatedMessage="Hosts : Please select host for row "+(i+1);
		                  	showError($(this).children('select'), "Please select host");
					    	self.errorMsg($(this).children('select'));
	                        validatedMessage = "error";
					    //validatedMessage="Please select version for selected host name type at line "+(i+1);
					    //return false;
						}
						else{
							removeError($(this).children('select'));
						}
	              }
	              if(j==2){
	                  if($(this).children('select').val() == 0){
	                        //validatedMessage="Hosts : Please select version for host for row "+(i+1);
	                        showError($(this).children('select'), "Please select host version");
					    	self.errorMsg($(this).children('select'));
	                        validatedMessage = "error";
	                  }
	                  else{
							removeError($(this).children('select'));
	                  }
	              }

	              if(j==3){
	                  if($(this).children('input').val() == ""){
	                        //validatedMessage="Hosts : Please provide hostaddress for row "+(i+1);
	                        showError($(this).children('input'), "Please enter host address");
					    	self.errorMsg($(this).children('input'));
	                        validatedMessage = "error";
	                  }
	                  else{
							removeError($(this).children('input'));
	                  }
	              }
                  else if(j==4){

                  		$('#compHostAttributes'+i+' tbody tr').each(function(k){
			 			 	var l=0;
							$(this).children('td.hostAttribvalues').each(function(l){
								console.log(self.compAttributesArr());
								console.log(k + " "+l);
                  				if(self.compAttributesArr()[i][k].isMandatory){
							 		var type = self.compAttributesArr()[i][k]['attributeType'];

                  					if(type.toUpperCase() == 'PASSWORD' || type.toUpperCase() == 'TEXTBOX'){		
										if($(this).children('input').val() == ""){
											//validatedMessage="Hosts, Line "+ (i+1) +": "+self.compAttributesArr()[i][k].attributeName+" value is required";
											showError($(this).children('input'), self.compAttributesArr()[i][k].attributeName+" value is required");
									    	self.errorMsg($(this).children('input'));
					                        validatedMessage = "error";
										}
										else{
											removeError($(this).children('select'));
										}
									}
									else if(type.toUpperCase() == 'DROPDOWN'){
										if($(this).children('select').val() == ""){
											//validatedMessage="Hosts, Line "+ (i+1) +": "+self.compAttributesArr()[i][k].attributeName+" value is required";
											showError($(this).children('select'), self.compAttributesArr()[i][k].attributeName+" value is required");
									    	self.errorMsg($(this).children('select'));
					                        validatedMessage = "error";
										}
										else{
											removeError($(this).children('select'));
										}
									}
                  				}
							});

							/*if(validatedMessage != "")
								return false;*/
						});
                  }

                  /*if(validatedMessage != "")
					return false;*/
              });
			/*if(validatedMessage != "")
				return false;*/
         }); 
        return validatedMessage;
	}
	/****Adding Server Parameters : END *****/

 
	/*****Identified host block : ADD*******/
	this.expandCollapsePanel1 = function(panelBody, expandCollpaseImg){
    	$(panelBody).toggle();
    	if($(panelBody).is(":hidden")){
    		$(expandCollpaseImg).removeClass("glyphicon-triangle-bottom").addClass("glyphicon-triangle-right");
    		$(expandCollpaseImg).attr("title", "Expand");
    		/*if(panelBody.id == "hostInstPanel"){
	    		self.hostDetailsArr.removeAll();
	    	}*/
    	}
    	else{
    		$(expandCollpaseImg).removeClass("glyphicon-triangle-right").addClass("glyphicon-triangle-bottom");
    		$(expandCollpaseImg).attr("title", "Collapse");
    	
    		if(panelBody.id == "hostInstPanel"){
    			/*self.validateElementsValue();
    			if(self.errorStack().length){	
					self.manageErrorMessage('print');
				}else{
			    	self.getAllIdentifiedHosts();
			    }*/
	    	}
    	}
    }

    this.expandCollapsePanel = function(panelBody, expandCollpaseImg,indx){
		/*hostsTableRowIndex = indx;

		if($("#compHostRowId"+indx).val() == 0){
			showMessageBox("Please select Host");
		}
		else if($("#compHostVersionRowId"+indx).val() == 0){
			showMessageBox("Please select Version for host");
		}
		else{*/
	    	$(panelBody).toggle();
	    	if($(panelBody[0]).is(":hidden")){
	    		$(expandCollpaseImg).removeClass("glyphicon-triangle-bottom").addClass("glyphicon-triangle-right");
	    		$(expandCollpaseImg).attr("title", "Expand");
	    	}
	    	else{

	    		$(expandCollpaseImg).removeClass("glyphicon-triangle-right").addClass("glyphicon-triangle-bottom");
	    		$(expandCollpaseImg).attr("title", "Collapse");
	    		/*if(panelBody[0].id == "attribsPanel"+indx){
	    			self.setCompAttributes(indx);
	    		}*/
	    	}
	   // }
    }

    this.onClickofShowAttributes=function(){
    	//onClickofShowAttributes
    }

    /*this.refreshHostDetails = function(){
    	self.hostDetailsArr.removeAll();
    	self.getAllIdentifiedHosts();
    	//self.expandCollapsePanel('#hostInstPanel','#imgCompAgentShowHide',0);	
    }*/



	this.getAllIdentifiedHosts=function(){
		var hostsAddedObjArr = [];
		identifiedHostAddressArr = [];
		var isHostWithVerExists = false;
		console.log(componentHostsDetailsVersionsTempArr);
		self.validateElementsValue();
		if(self.errorStack().length){	
			self.manageErrorMessage('print');
			return "error";
		}

		else{

			console.log("++++++++++++++++++List of identified hosts++++++++++++++++++++++++");
			console.log(self.instanceDetailArr());
			var duplicateHost = false;
			var hostPortCombinationArr = [];
	    	for(obj in self.instanceDetailArr()){
	    		self.widgetName( trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType));
	    		console.log(self.widgetName()+" : "+obj);

	    		var typePrefix = self.widgetName().substr(0,4);
	    		var hostAddress = "";
	    		var hostPort = "";
	    		var attributeArray = [];
				var attributeHeaderArray = [];
	    		console.log("host deatils");

	    		//get header list
	    		$('#'+self.widgetName()+' #tableCompInstAddMultiple thead tr').each(function(i){
					var cobj = {};
					 $(this).children('th').each(function(j){
					 	var panelName= self.widgetName();
					 	var obj = {};

					 	var splitRes = $(this).children('span')[1].id.toString().split("|",2);
					 	if(self.widgetName() == panelName){
						 	obj['attributeName']=$(this).children('span')[0].innerHTML;
						 	obj['attributeId']=splitRes[0];
						 	obj['attributeType']=splitRes[1];
						 	attributeHeaderArray.push(obj);
						}

					 });

					 cobj[self.widgetName()]=attributeHeaderArray;
					 self.tableHeaders(cobj);
					 attributeHeaderArray=[];

				});

	    		var instHostPortArr = [];
	    		var instHostVersionArr = [];
	    		if(self.instanceDetailArr().length > allInstanceHostPort.length){
		    		allInstanceHostPort.push(instHostPortArr);
		    		allInstanceHostVersion.push(instHostVersionArr);
	    		}
		    	$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody tr').each(function(i){
		    		 	cobj={};
		    		 	hostAddress = "";
				        hostPort = "";
				        isHostWithVerExists = false;
				        	console.log(obj+" "+ i);

						
				        $(this).children('td').each(function(j){
				        	console.log("*************************");
							if(self.tableHeaders()[self.widgetName()][j]['attributeName'] == "HostAddress"){
					    		hostAddress = $(this).children('input').val().trim();

					    	}
					    	else if(self.tableHeaders()[self.widgetName()][j]['attributeName'] == "MonitorPort"){
					    		hostPort = $(this).children('input').val().trim();

					    	}

					    	
							if(hostAddress != "" && hostPort != ""){
				        		console.log(j);
				        		console.log(hostAddress);
				        		console.log(hostPort);

								if($("#"+self.widgetName()+"hostAddrPort"+i).text() == ""){
				    		 		$("#"+self.widgetName()+"hostAddrPort"+i).text(hostAddress+"~"+hostPort);

				    		 		allInstanceHostPort[obj].push(hostAddress+"~"+hostPort);
				    		 		allInstanceHostVersion[obj].push(hostAddress+"~"+self.instanceDetailArr()[obj].hostVersionId);
				    		 	}
								
								if(hostPortCombinationArr.indexOf(hostAddress+"~"+hostPort) == -1){
									hostPortCombinationArr.push(hostAddress+"~"+hostPort);
								}
								else{
									//debugger;
									duplicateHost = true;

									showError("#"+self.widgetName()+"HostAddress"+i, "Host address '"+hostAddress+"' and port '" + hostPort +"' combination is repeated");
									showError("#"+self.widgetName()+"MonitorPort"+i, "Host address '"+hostAddress+"' and port '" + hostPort +"' combination is repeated");
		            				self.errorMsg("#"+self.widgetName()+"HostAddress"+i);

									//showMessageBox($("#panel"+obj).text()+", "+"Line "+(i+1)+": Host address '"+hostAddress+"' and port '" + hostPort +"' combination is repeated","error");
					    			return;
								}

								if($("#"+self.widgetName()+"hostAddrPort"+i).text() != "" && $("#"+self.widgetName()+"hostAddrPort"+i).text().split("~")[0] != (hostAddress)){
									//debugger;
									isHostWithVerExists = isHostWithVersionExists(obj, $("#"+self.widgetName()+"hostAddrPort"+i).text(), hostAddress+"~"+hostPort, self.instanceDetailArr()[obj].hostVersionId);
									$("#"+self.widgetName()+"hostAddrPort"+i).text(hostAddress+"~"+hostPort);

								}

								allInstanceHostPort[obj][i] = hostAddress+"~"+hostPort;
								allInstanceHostVersion[obj][i] = hostAddress+"~"+self.instanceDetailArr()[obj].hostVersionId;
								

								if(identifiedHostAddressArr.indexOf(hostAddress+"~"+self.instanceDetailArr()[obj].hostVersionId) == -1){
									identifiedHostAddressArr.push(hostAddress+"~"+self.instanceDetailArr()[obj].hostVersionId);
					    			cobj['hostAddress'] = ko.observable(hostAddress);

									var hostsMasterObj = $.grep(self.hostsArr(), function(e){ return e.hostAddress == hostAddress; });

					    			if(hostsMasterObj.length && hostsMasterObj[0].hostName){
					    				cobj['hostInstanceName']=hostsMasterObj[0].hostName;
									}
									else{
					    				cobj['hostInstanceName']="CI_"+self.applicationId()+"_"+typePrefix+"_"+cobj['id'];
									}
					    		}
					    		/*else{
					    			duplicateHost = true;
					    			return false;
					    		}*/
					    		hostAddress = "";
				        		hostPort = "";
					       
					    	}

					    	if(isHostWithVerExists){
								return;
							}
							

				    			//return false;
				    		//}
							//console.log("indexObj : "+obj+" version Id"+cobj['hostsVersionId']);
				        });

				        if(duplicateHost){
		    		 		return false;
		    		 	}

				        /*if(duplicateHost){
							showMessageBox(self.widgetName()+", "+"Line "+(i+1)+": Host address '"+hostAddress+"' and port '" + hostPort +"' combination is repeated","error");
				        	return false;
				        }*/

				        


				        if(cobj.hasOwnProperty("hostAddress")){
				        	//debugger;
				        	hostsAddedObjArr = $.grep(self.hostDetailsArr(), function(e){ return e.hostsVersionId == self.instanceDetailArr()[obj].hostVersionId && e.hostAddress() == cobj['hostAddress'](); });

					        if(hostsAddedObjArr.length == 0){
						        	//componentHostsDetailsVersionsTempArr.push(self.componentHostVersionArr()[obj]);
						        	cobj['id']=hostCounterValue++;
					    			cobj['serverDetailsId'] = self.instanceDetailArr()[obj].eleId;
					    			cobj['hostsVersionId']=parseInt(self.instanceDetailArr()[obj].hostVersionId);
					    			
									var hostsMasterObj = $.grep(self.hostsArr(), function(e){ return e.hostAddress == cobj['hostAddress'](); });

									if(hostsMasterObj.length && hostsMasterObj[0].hostName){
					    				cobj['hostInstanceName']=hostsMasterObj[0].hostName;
									}
									else{
					    				cobj['hostInstanceName']="CI_"+self.applicationId()+"_"+typePrefix+"_"+cobj['id'];
									}
									
									cobj['hostComponentId']=self.instanceDetailArr()[obj].hostId;
									cobj['hostsIdentified']=1;
							    	//cobj['hostIdInitial'] = self.widgetName()+"_"+i

									var hostVerObj = $.grep(self.componentNameHostsArr(), function(e){ 
		    							if(e.componentId == self.instanceDetailArr()[obj].hostId) return e; });
						        	componentHostsDetailsVersionsTempArr.push([]);
									componentHostsDetailsVersionsTempArr.splice(cobj['id'], 1, hostVerObj[0].versions);

						        	self.componentHostDetailsVersionArr(componentHostsDetailsVersionsTempArr);
						       		self.hostDetailsArr.push(cobj);

						       		console.log(self.hostNameVersionIdObj());

						       		self.compAttributesArr.push([]);
						       		self.compAttributesArr.splice(cobj['id'], 1, self.hostNameVersionIdObj()[cobj['hostComponentId']+"_"+cobj['hostsVersionId']]);
						       		
						       		console.log(self.compAttributesArr());
						       	/*else{
					    		 	var hostIndex = self.hostDetailsArr.indexOf(hostsAddedObjArr[0]);
					    		 	hostsAddedObjArr[0].hostAddress = hostAddress;
					    		 	hostsAddedObjArr[0].compInstanceId = self.widgetName() + "_" + i;
					    		 	self.hostDetailsArr.splice(hostIndex, 1, hostsAddedObjArr[0]);
					    		}*/
				    		}
				    		/*else{
				    		 	var hostIndex = self.hostDetailsArr.indexOf(hostsAddedObjArr[0]);
					    		hostsAddedObjArr[0].hostAddress(hostAddress);
				    		 	hostsAddedObjArr[0].compInstanceId = self.widgetName() + "_" + i;
				    		 	self.hostDetailsArr.splice(hostIndex, 1, hostsAddedObjArr[0]);
				    		 	//$("#hostAddress"+hostsAddedObjArr[0].id).val(hostAddress);
				    		}*/
				        }
		    	});

		    	if(duplicateHost){
		    		return "error";
		    	}
			}

			/*for(var hostDet in self.hostDetailsArr()){
				if(identifiedHostAddressArr.indexOf($("#hostAddress"+self.hostDetailsArr()[hostDet].id).val()) == -1){
					self.deleteHostDetails(hostDet);
				}
			}*/


			

			// self.hostDetailsArr(arrHost);
			console.log(self.hostDetailsArr());
		}

		if(self.hostDetailsArr().length){
    		self.showHosts(true);
		}
		else{
    		self.showHosts(false);
		}
	}

	function isHostWithVersionExists(indx, prevHostPort, curHostPort, hostVersionId){

		var prevHostCount = 0;
		for(var i in allInstanceHostVersion){
			prevHostCount = prevHostCount + $.grep(allInstanceHostVersion[i], function(e){ return e == (prevHostPort.split("~")[0]+"~"+hostVersionId); }).length;
		}

		if(prevHostCount>1 && allInstanceHostVersion[indx].indexOf((curHostPort.split("~")[0]+"~"+hostVersionId)) == -1){
			return false;
		}
		else if(prevHostCount == 1){

			if(prevHostPort.split("~")[0] == curHostPort.split("~")[0]){
				return true; 	
			}
			else{
				//hostsIdentified
				var hostToDeleteObj = $.grep(self.hostDetailsArr(), function(e){console.log(e.hostAddress()); return e.hostAddress() == prevHostPort.split("~")[0] && e.hostsVersionId == hostVersionId; });

				if(hostToDeleteObj.length){
					var hostToDeleteIndex = self.hostDetailsArr.indexOf(hostToDeleteObj[0]);
					self.hostDetailsArr.splice(hostToDeleteIndex, 1);
				}
			}
		}

		return allInstanceHostVersion[indx].indexOf((curHostPort.split("~")[0]+"~"+hostVersionId)) == -1;
	}

	this.checkForSameIpExists=function(arr){

	}

	this.addHosts = function(indx){
		componentHostsDetailsVersionsTempArr.push([]);
		self.compAttributesArr.push([]);
		self.hostDetailsArr.splice(indx+1,0,{'id':hostCounterValue++,'hostInstanceName': "CI_"+self.applicationId()+"_Host_"+hostCounterValue,'hostComponentId':0,'hostsVersionId':0, 'hostAddress':ko.observable(""),'hostsIdentified':0});
	}
                    
	this.deleteHostDetails = function(rowIndex, dataId){
		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_DELETE_HOST_DETAILS, "question", "confirm", function confirmCallback(confirmDeleteHostDetails){
			if(confirmDeleteHostDetails){
				if($("#hostId"+dataId).text()){
					self.deletedCompInstArr.push(parseInt($("#hostId"+dataId).text()));
				}
		    	self.compAttributesArr.splice(rowIndex, 1);
				self.hostDetailsArr.splice(rowIndex, 1);
		    }
		});
	}

	this.setCompAttributes = function(hostNameVerId){
		var componentVersionId = hostNameVerId.split("_")[1];//Send this to REST API for getting component attributes
		requestCall(uiConstants.common.SERVER_IP + "/componentAttribute/"+componentVersionId, "GET", "", "getComponentAttributes", successCallback, errorCallback);
	}

	this.onHostAddressChange = function(indx){
		self.observableHostAddress($("#hostAddress"+indx).val());

		var hostsMasterObj = $.grep(self.hostsArr(), function(e){ return e.hostAddress == $("#hostAddress"+indx).val(); });
		if(hostsMasterObj.length && hostsMasterObj[0].hostName){
			$("#HostName"+indx).val(hostsMasterObj[0].hostName);
		}
		else{
			if($.grep(self.hostsArr(), function(e){ return e.hostName && e.hostName == $("#HostName"+indx).val(); }).length){
				//cobj['hostInstanceName']="CI_"+self.applicationId()+"_"+typePrefix+"_"+cobj['id'];
				$("#HostName"+indx).val("CI_"+self.applicationId()+"_Host_"+(indx+1));

			}
		}
		console.log(self.observableHostAddress());
	}

	/******Identified host block : END********/

	function get_numbers(input) {
	    return input.match(/[0-9]+/g);
	}

	function get_words(input) {
	    return input.match(/[A-z,a-z]+/g);
	}

	//managing all validation error message on pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount,widgetName){	
		debugger;
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg,'widgetName':widgetName});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			if(addRecordCount>1)
				var messageStr = addRecordCount+uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCE+" and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			else
				var messageStr = addRecordCount+" ComponentInstance(s) is successfully added and "+unsavecnt +" have failed. \n";
				
				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					 if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						var serverDetailsNum=parseInt(get_numbers(item.widgetName))+1;
						messageStr +=" Panel "+serverDetailsNum+" : "+get_words(item.widgetName)+",\n Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";

			self.errorStack().forEach(function(item,index){
			//if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					var serverDetailsNum=parseInt(get_numbers(item.widgetName))+1;
					messageStr += " Panel "+serverDetailsNum+" : "+get_words(item.widgetName)+",\n Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}

	self.errorMsg.subscribe(function(errorField) {
    	if(self.firstFieldToShowErr() == ""){
        	//scrollToPos(0, 300);
        	self.firstFieldToShowErr(errorField);
        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
        }
	});
	
	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(){
		if(uiConstants.common.DEBUG_MODE)console.log("==================== Wizard Save for different componentinstance values ===================");
		self.errorStack.removeAll();
		self.requestDataArray = [];
		var attributeArray = [];
		var attributeHeaderArray = [];
		var copyIndxCount=1;
		$("body").find("div, input, textarea, select").removeClass("error-highlighter");
		self.errorMsg("");
		self.firstFieldToShowErr("");

		//console.log(self.instanceDetailArr());
		
		for(obj in self.instanceDetailArr()){
			self.widgetName( trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType));
			
			$('#'+self.widgetName()+' #tableCompInstAddMultiple thead tr').each(function(i){
				var cobj = {};
				 $(this).children('th').each(function(j){
				 	var panelName= self.widgetName();
				 	var obj = {};

				 	var splitRes = $(this).children('span')[1].id.toString().split("|~",7);
				 	if(self.widgetName() == panelName){
					 	obj['attributeName']=$(this).children('span')[0].innerHTML;
					 	obj['attributeId']=splitRes[0];
					 	obj['attributeType']=splitRes[1];
					 	obj['isMandatory']=parseInt(splitRes[2]);
					 	obj['attributeMinLength']=splitRes[3];
					 	obj['attributeMaxLength']=splitRes[4];
					 	obj['attributeRegEx']=splitRes[5];
					 	obj['componentAttributeMappingId']=splitRes[6];
					 	attributeHeaderArray.push(obj);
					}
				 });
				 cobj[self.widgetName()]=attributeHeaderArray;
				 self.tableHeaders(cobj);
				 attributeHeaderArray=[];
			});

			indexCount=copyIndxCount;
			var instRowIndex = 0;
			$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody tr').each(function(i){
				var cobj = {'index': indexCount++};
				instRowIndex = i;
					 $(this).children('td').each(function(j){
			        	var obj = {};
			        	if((self.tableHeaders()[self.widgetName()][j].attributeType == 'TextBox' || self.tableHeaders()[self.widgetName()][j].attributeType == 'Password') && trimSpacesReplaceUnderscore(self.tableHeaders()[self.widgetName()][j].attributeName) != 'Tags'){
		 					var cellValue = $(this).children('input').val();

			            	if(self.tableHeaders()[self.widgetName()][j].isMandatory && cellValue == ""){

			            		showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, self.tableHeaders()[self.widgetName()][j].attributeName+" value is required");
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);

				    			//self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[self.widgetName()][j].attributeName+" value is required",0,self.widgetName());
				    		}

				    		else if(cellValue && self.tableHeaders()[self.widgetName()][j].attributeMinLength && self.tableHeaders()[self.widgetName()][j].attributeMinLength!="undefined" && cellValue.length < parseInt(self.tableHeaders()[self.widgetName()][j].attributeMinLength)){
								//self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" should be of minimum " + self.tableHeaders()[self.widgetName()][j].attributeMinLength + " characters length",0,self.widgetName());	
								showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" should be of minimum " + self.tableHeaders()[self.widgetName()][j].attributeMinLength + " characters length");
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);

							}
							else if(cellValue && self.tableHeaders()[self.widgetName()][j].attributeMaxLength && self.tableHeaders()[self.widgetName()][j].attributeMaxLength!="undefined" && cellValue.length > parseInt(self.tableHeaders()[self.widgetName()][j].attributeMaxLength)){
								//self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" should be of maximum " + self.tableHeaders()[self.widgetName()][j].attributeMaxLength + " characters length",0,self.widgetName());
								showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" should be of maximum " + self.tableHeaders()[self.widgetName()][j].attributeMaxLength + " characters length");
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);

							}
							else if(!isRegexValid(self.tableHeaders()[self.widgetName()][j].attributeRegEx, cellValue)){
								//self.manageErrorMessage('push',cobj.index-1, "Regex " + self.tableHeaders()[self.widgetName()][j].attributeRegEx + " for "+self.tableHeaders()[self.widgetName()][j].attributeName+" is invalid",0,self.widgetName());
								showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, "Regex " + self.tableHeaders()[self.widgetName()][j].attributeRegEx + " for "+self.tableHeaders()[self.widgetName()][j].attributeName+" is invalid",0,self.widgetName());
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);

							}
							else if(cellValue && self.tableHeaders()[self.widgetName()][j].attributeRegEx && self.tableHeaders()[self.widgetName()][j].attributeRegEx!="undefined" && cellValue != (cellValue.match(new RegExp(self.tableHeaders()[self.widgetName()][j].attributeRegEx, "g")).join("") ? cellValue.match(self.tableHeaders()[self.widgetName()][j].attributeRegEx)[0] : null)){
								//self.manageErrorMessage('push',cobj.index-1, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" does not match the Regex pattern " + self.tableHeaders()[self.widgetName()][j].attributeRegEx,0,self.widgetName());	
								showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, "Value for "+self.tableHeaders()[self.widgetName()][j].attributeName+" does not match the Regex pattern " + self.tableHeaders()[self.widgetName()][j].attributeRegEx);
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);
							}

			    			/*else if(isNaN(parseInt(cellValue))){// if string
								if((cellValue != "" && self.tableHeaders()[self.widgetName()][j].attributeMinLength != "") && cellValue.length  < self.tableHeaders()[self.widgetName()][j].attributeMinLength){
									self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[self.widgetName()][j].attributeName+" should not be less than minimun character length "+ self.tableHeaders()[self.widgetName()][j].attributeMinLength,0,self.widgetName());	
					    		}
					    		else if((cellValue != "" && self.tableHeaders()[self.widgetName()][j].attributeMaxLength != "") && cellValue.length > self.tableHeaders()[self.widgetName()][j].attributeMaxLength){ 
					    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[self.widgetName()][j].attributeName+" should not be greater than maximum character length "+ self.tableHeaders()[self.widgetName()][j].attributeMaxLength,0,self.widgetName());
					    		}
					    		
							}
							else if(!isNaN(parseInt(cellValue))){// if number
								if((cellValue != "" && self.tableHeaders()[self.widgetName()][j].attributeMinLength != "") && parseInt(cellValue)  < self.tableHeaders()[self.widgetName()][j].attributeMinLength){
					    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[self.widgetName()][j].attributeName+" should not be less than minimun value "+ self.tableHeaders()[self.widgetName()][j].attributeMinLength,0,self.widgetName());
					    		}
					    		else if((cellValue != "" && self.tableHeaders()[self.widgetName()][j].attributeMaxLength != "") && parseInt(cellValue) > self.tableHeaders()[self.widgetName()][j].attributeMaxLength){ 
					    			self.manageErrorMessage('push',cobj.index-1, self.tableHeaders()[self.widgetName()][j].attributeName+" should not be greater than maximum value "+ self.tableHeaders()[self.widgetName()][j].attributeMaxLength,0,self.widgetName());	
					    		}
					    		
							}
				    		else if(cellValue != "" && self.tableHeaders()[self.widgetName()][j].attributeRegEx != undefined && cellValue.match(self.tableHeaders()[self.widgetName()][j].attributeRegEx) == null){
				    			self.manageErrorMessage('push',cobj.index-1,"Invalid "+ self.tableHeaders()[self.widgetName()][j].attributeName+" value",0,self.widgetName());
				    		}*/
			            	if(self.tableHeaders()[self.widgetName()][j].attributeName == "Name" && j == 1){
				    			cobj['componentInstanceName'] = trimSpacesReplaceSingleBlankspace($(this).children('input').val());
				    			cobj['componentInstanceId'] = parseInt($("#"+self.widgetName()+"compInst"+instRowIndex).text()) || 0;
				    		}
				    		else if(self.tableHeaders()[self.widgetName()][j].attributeName == "Description" && j == 2){
				    			cobj['description'] = $(this).children('input').val().trim();
				    		}
				    		else{
			            		obj['attributeId'] = self.tableHeaders()[self.widgetName()][j].attributeId;
			            		obj['attributeValue'] = isNaN($(this).children('input').val()) ? $(this).children('input').val() : $(this).children('input').val();
			            		obj['attributeType'] = self.tableHeaders()[self.widgetName()][j]['attributeType'];
			            	 	obj['componentAttributeMappingId'] = self.tableHeaders()[self.widgetName()][j]['componentAttributeMappingId'];
			            		attributeArray.push(obj);  
			            	}

		 				}else if(self.tableHeaders()[self.widgetName()][j].attributeType == 'DropDown'){	  
		 					//var aTypeLen = self.tableHeaders()[j].attributeOptions.length;     no use  	 
			            	 if(self.tableHeaders()[self.widgetName()][j].isMandatory == 1 && $(this).children('select').val() == ""){
			            	 	//self.manageErrorMessage('push',cobj.index-1, "Please select value for "+self.tableHeaders()[self.widgetName()][j]['attributeName'],0,self.widgetName());

			            	 	showError("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex, "Please select value for "+self.tableHeaders()[self.widgetName()][j]['attributeName']);
			            		self.errorMsg("#"+self.widgetName()+self.tableHeaders()[self.widgetName()][j].attributeName+instRowIndex);
							
			            	 }else{
			            	 	obj['attributeId'] = self.tableHeaders()[self.widgetName()][j].attributeId;
			            		obj['attributeValue'] = isNaN($(this).children('select').val()) ? $(this).children('select').val() : $(this).children('select').val();
			            	 	obj['attributeType'] = self.tableHeaders()[self.widgetName()][j]['attributeType'];
			            	 	obj['componentAttributeMappingId'] = self.tableHeaders()[self.widgetName()][j]['componentAttributeMappingId'];
			            	 	attributeArray.push(obj);
			            	 }
		 				}else if(self.tableHeaders()[self.widgetName()][j].attributeType == 'CheckBox'){	 
		            	 	obj['attributeId'] = self.tableHeaders()[self.widgetName()][j].attributeId;
		            		obj['attributeValue'] =parseInt($(this).children('input').prop('checked') == true?1:0);
		            	 	obj['attributeType'] = self.tableHeaders()[self.widgetName()][j]['attributeType'];
		            	 	obj['componentAttributeMappingId'] = self.tableHeaders()[self.widgetName()][j]['componentAttributeMappingId'];
		            	 	attributeArray.push(obj);
		 				}
		 				else if(self.tableHeaders()[self.widgetName()][j].attributeType == 'TextBox' && self.tableHeaders()[self.widgetName()][j].attributeName == 'Tags'){
			            	/*var ctag = $(this).children('input').val();
			            	if(ctag != ""){
					 		}*/
			            	var taglist = [];
		            		var tagObj = [];
		            		var compTypeObj = $.grep(self.compInstDetails(), function(e){ return e.componentTypeId == (parseInt($('#'+self.widgetName()+' #compTypeList').val()) || 0); });
		            		var compInstTagObj = [];
		            		var tagIdNameObjArr = [];

		            		if(compTypeObj.length){
		            			compInstTagObj = $.grep(compTypeObj[0].componentInstances, function(e){ return e.componentInstanceId == (parseInt($("#"+self.widgetName()+"compInst"+instRowIndex).text()) || 0); });
		            		}

			            	if($(this).children('input').val() != ""){
			            		taglist = $(this).children('input').val().split(", ");
			            		
			            		for (var i = 0; i < taglist.length; i++) {
			            			if(tagValidation(taglist[i].trim()) == 0){
			            				showError("#"+self.widgetName()+"Tags"+instRowIndex, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		            					self.errorMsg("#"+self.widgetName()+"Tags"+instRowIndex);
				    					//self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR,0,self.widgetName());
			            			}

			            			else{

			            				tagIdNameObjArr = $.grep(self.compInstIdAllTags(), function(e){ return e.tagName && e.tagName == taglist[i].trim(); });

				            			if(tagIdNameObjArr.length == 0){
				            				tagObj.push({"tagId":0, "tagName":taglist[i].trim(), "tagOperation":"add"});
				            			}
				            			else{
				            				tagObj.push({"tagId":tagIdNameObjArr[0].tagId, "tagName":taglist[i].trim(), "tagOperation":"none"});
				            			}
			            			}
				    		
			            		};

			            		
			            	}

			            	if(compInstTagObj.length != 0){
			            		for (var tag in compInstTagObj[0].tags) {
			            			if(taglist.indexOf(compInstTagObj[0].tags[tag].tagName) == -1){
				            			tagObj.push({"tagId":compInstTagObj[0].tags[tag].tagId, "tagName":compInstTagObj[0].tags[tag].tagName, "tagOperation":"delete"});
			            			}
			            		}
			            	}

			            	cobj['tags'] = tagObj;
			           }	
			           else if(self.tableHeaders()[self.widgetName()][j].attributeType == 'SuggesterBox'){//only for application
			           		 var aTypeLen = $(this).children('select').val().length;	            	 
			            	 if(aTypeLen > 0){
			            	 	cobj['applicationIds'] = [parseInt($(this).children('select').val())];
			            	 }else{
			            	 	//self.manageErrorMessage('push',cobj.index-1, uiConstants.componentInstanceConfig.APPLICATION_NAME_SELECTION_REQUIED,0,self.widgetName());
			            	 	showError($(this).children('select'), uiConstants.componentInstanceConfig.APPLICATION_NAME_SELECTION_REQUIED);
		            			self.errorMsg($(this).children('select'));
			            	 }
			           }	
			        }); 
				
					//cobj['index'],cobj['componentInstanceName'] and cobj['tags'] will be assigned value in foreach loop only

					cobj['componentId'] = parseInt($('#'+self.widgetName()+' #compNamesList').val());
					cobj['componentTypeId'] = parseInt($('#'+self.widgetName()+' #compTypeList').val());
					cobj['componentVersionId'] =  parseInt($('#'+self.widgetName()+' #compVersionsList').val());
					cobj['attributes'] = attributeArray;
					cobj['clusterId'] = 0;
					cobj['applicationIds'] = [parseInt(self.applicationId())];
					cobj['monitor'] = 0;
					cobj['kpiDetails'] = [];
					cobj['kpiGroups'] = [];
					cobj['status'] = 1;

					self.requestDataArray.push(cobj);
					attributeArray = [];

					copyIndxCount=indexCount;
		    });
		}

		console.log(self.requestDataArray);

		/*if(self.currentCompTypesCount() != self.instanceDetailArr().length){
			return "error";
		}*/
	}

	this.getHostDetails=function(){
		var attributeArray = [];
		resHostDetailsArr = [];
		//read host attribute details
		$('tr.host-details').each(function(i){
				var cobj = {};
				attributeArray = [];
				 $(this).children('td').each(function(j){
				 	console.log($(this).children('input').attr('id'));
				 	var inputCellId = $(this).children('input').attr('id');
				 	var selectCellId = $(this).children('select').attr('id');
				 	//var spanCellId = $(this).children('span').attr('id');
				 	if(inputCellId != undefined){
				 		if( inputCellId.indexOf("HostName") != -1 ){//if string matches
				 			cobj['hostName']= $(this).children('input').val();
				 		}
				 		else if( inputCellId.indexOf("hostAddress") != -1 ){
				 			cobj['hostAddress']= $(this).children('input').val();
				 		}
				 	}

				 	if( selectCellId != undefined){
				 		if(selectCellId.indexOf("compHostDetailsRowId") != -1 ){
				 			cobj['hostComponentId']= isNaN($(this).children('select').val()) ? parseInt($(this).children('select').val()) : parseInt($(this).children('select').val());
				 		}
				 		else if(selectCellId.indexOf("compHostInstVersionRowId") != -1 ){
				 			cobj['hostVersionId']= isNaN($(this).children('select').val()) ? parseInt($(this).children('select').val()) : parseInt($(this).children('select').val());
				 		}
				 	}

				 	/*if(spanCellId != undefined){
				 		if(spanCellId.indexOf("compHostDetailsRowId") != -1 ){

				 		}
				 	}*/

				 	cobj['applicationIds'] = [self.applicationId()];
				 });//host foreach td
				
				 //read host instance attribute values

					$('#compHostAttributes'+i+' tbody tr').each(function(k){
			 			 var obj = {};
			 			 var l=0;
						 $(this).children('td.hostAttribvalues').each(function(l){
						 	if( self.compAttributesArr()[i][k]!= undefined){
							 	var type = self.compAttributesArr()[i][k]['attributeType'];
								if(type.toUpperCase() == 'PASSWORD' || type.toUpperCase() == 'TEXTBOX'){		
									obj['attributeValue'] = isNaN($(this).children('input').val()) ? $(this).children('input').val() : $(this).children('input').val();	
									obj['attributeId'] = self.compAttributesArr()[i][k]['attributeId'];
						 			obj['attributeType'] = type;
								}
								else if(type.toUpperCase() == 'DROPDOWN'){
									obj['attributeValue'] = isNaN($(this).children('select').val()) ? $(this).children('select').val() : $(this).children('select').val();
									obj['attributeId'] = self.compAttributesArr()[i][k]['attributeId'];
						 			obj['attributeType'] = type;
								}
								else if(type.toUpperCase() == 'CHECKBOX'){
									obj['attributeValue'] = parseInt($(this).children('input').prop('checked') == true?1:0);
									obj['attributeId'] = self.compAttributesArr()[i][k]['attributeId'];
						 			obj['attributeType'] = type;					
								}
							}
						 });//host attribute foreach td
						  attributeArray.push(obj);
					});//host attribute foreach tr
			 		
					cobj['attributes'] = attributeArray;
					resHostDetailsArr.push(cobj);
		});//host foreach tr

		if(uiConstants.common.DEBUG_MODE){
			console.log("Wizard Componnet Instance : List of Hosts");
			console.log(resHostDetailsArr);
		}
	}

	/*this.passwordChangeHandler = function(index){
    	if($('#password'+index).val() != ''){
		    showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_PASSWORD_RESET, "question", "confirm", function confirmCallback(confirmReset){
				if (confirmReset) {
					$('#password'+index).val("");
				}
				else{
					$('#password'+index).blur();
				}
			});
    	}
    }*/

    this.procesInstanceDetails = function(){
    	if(self.errorMsg() == ""){
    		/*self.validateElementsValue();
			if(self.errorStack().length){	
				self.manageErrorMessage('print');
			}
    		else{*/
    			//delay(function(){

			    	if(self.getAllIdentifiedHosts() != "error"){

	    				var hostValidationMsg=self.validateHostDetails();
				    	if(hostValidationMsg == ""){
					       /*showMessageBox(hostValidationMsg, "error");
					    }
					    else{*/
							self.getHostDetails(); 
							self.reqRecordCounter(self.requestDataArray.length);
							var finalObj = {'componentInstances':self.requestDataArray,'unMappedInstanceIds':self.deletedCompInstArr(),'hostDetails':resHostDetailsArr};
							if(uiConstants.common.DEBUG_MODE)console.log("==================== Add Multiple ComponentInstance===================");
							if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
							var url = uiConstants.common.SERVER_IP+"/wizard/componentInstances";

    						if(self.compInstanceMode() == "Add"){
								requestCall(url, 'POST', JSON.stringify(finalObj), 'addComponentInstances', successCallback, errorCallback);
							}
							else if(self.compInstanceMode() == "Edit"){
								requestCall(url, 'PUT', JSON.stringify(finalObj), 'editComponentInstances', successCallback, errorCallback);
							}
						}
	    			}
			   // }, 1000 );
		    	
			//}
    	}
    }

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
    	//for(indx in self.serverDetailsArr()){
    		self.configureServerDetails(false);

    	//}


    	if(self.allElementsLoaded()){
	    	self.procesInstanceDetails();
	    }

    	
    }	

    uicommon.postbox.subscribe(function(value) {
	     //uicommon.postbox.publish(self.onMultipleSaveClick(),"addComponentInstancesDetails");
	     self.onMultipleSaveClick();
	},"saveComponentInstanceDetailsClickEvent");

	//Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
		for(obj in self.instanceDetailArr()){
			self.widgetName = trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType);
	    	var succnt = 0;
	    	var failcnt = 0;
	    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
	    	if(self.reqRecordCounter() == res.length){
	    		for (var i = 0; i < res.length; i++) {
	    			if(uiConstants.common.DEBUG_MODE)console.log(res[i]);
	    			if(uiConstants.common.DEBUG_MODE)console.log(res[i].index);
	    			var rid = res[i].index-1;
	    			if(res[i].responseStatus == "success"){
	    				$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody #row_'+rid).remove();
	    				succnt++;	
	    			}else{
	    				self.manageErrorMessage('push',failcnt, res[i].message,0,self.widgetName());
	    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+res[i].message);
	    				failcnt++;
	    			}
	    		};

	    		//changing row index and number
	    		self.manageErrorMessage('print',"","",succnt,self.widgetName());
	    		self.chnageRowAndCellId();
	    		
	    	}else{
	    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
	    	}
	    }
    }

    //Changing row and col elements ID's when any delete or append/prepend operation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	for(obj in self.instanceDetailArr()){
    		self.widgetName = trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType);
	    	var rowCnt = 0;
	    	$('#'+self.widgetName+' #tableCompInstAddMultiple tbody tr').each(function(i){
		        $(this).children('td').each(function(j){

		        	if(self.tableHeaders()[self.widgetName][j]['attributeType'] == 'label'){
		        		$(this).children('label').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[self.widgetName][j]['attributeName'])+rowCnt);
		            	$(this).children('label').text(rowCnt+1);
		        	}else if(self.tableHeaders()[self.widgetName][j]['attributeType'] == 'TextBox' || self.tableHeaders()[self.widgetName][j]['attributeType'] == 'Password'){
		        		$(this).children('input').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[self.widgetName][j]['attributeName'])+rowCnt);
		        	}else if(self.tableHeaders()[self.widgetName][j]['attributeType'] == 'DropDown'){
		        		$(this).children('select').attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[self.widgetName][j]['attributeName'])+rowCnt);
		        	}else if(self.tableHeaders()[self.widgetName][j]['attributeType'] == 'delete'){
	 					$(this).attr('id',self.widgetName+trimSpacesReplaceUnderscore(self.tableHeaders()[self.widgetName][j]['attributeType'])+rowCnt);
	 				}
		        });
		        $(this).attr('id',self.widgetName+'row_'+rowCnt);
		        rowCnt++;
		    });
		}

    }

    this.onCompVersionFocus = function(versionEleId){
    	
    	prevCompText = $("#compVersionRowId"+versionEleId+" option:selected").text();
    	if(prevCompDetId == "" && $("#compTypeRowId"+versionEleId).val() && $("#compNameRowId"+versionEleId).val() && $("#compVersionRowId"+versionEleId).val()){
    		prevCompDetId = $("#compTypeRowId"+versionEleId).val() + "_" + $("#compNameRowId"+versionEleId).val() + "_" + $("#compVersionRowId"+versionEleId).val();
    	}

    	prevHostCompText = $("#compHostVersionRowId"+versionEleId+" option:selected").text();
    	if(prevCompHostDetId == "" && $("#compHostRowId"+versionEleId).val() && $("#compHostVersionRowId"+versionEleId).val()){
    		prevCompHostDetId = $("#compHostRowId"+versionEleId).val() + "_" + $("#compHostVersionRowId"+versionEleId).val();
    	}
    };

    function removeHostDetails(widgetNameStr, rowIndex, instIndex, isServerDetDelete){
    	var compWidgetName = "";
    	
    	var deleteHost = true;
		var hostAddrVersion = allInstanceHostVersion[rowIndex][instIndex];

    	for(obj in self.instanceDetailArr()){
    		compWidgetName = trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType);
    		if(compWidgetName != widgetNameStr){
    			console.log($('#'+compWidgetName+' #tableCompInstAddMultiple thead tr'));
	    		$('#'+compWidgetName+' #tableCompInstAddMultiple thead tr').each(function(i){
					if($.grep(allInstanceHostVersion[obj], function(e, i){ return e == hostAddrVersion; }).length > 0){

						deleteHost = false;
						return;
					}

	    			
	    		});
    		}

    		if(!deleteHost){
    			break;
    		}
    	}

    	console.log("#################");
    	console.log(allInstanceHostVersion);


		if(deleteHost && !isServerDetDelete){
			if(!isServerDetDelete && $.grep(allInstanceHostVersion[rowIndex], function(e, i){ return e == hostAddrVersion; }).length > 1){
				deleteHost = false;
			}
		}

    	return deleteHost;

	    		//get header list
					


    	
    	/*var hostsObjArr = $.grep(self.hostDetailsArr(), function(e){ return e.compInstanceId == hostIndex; });

        if(hostsObjArr.length){
        	self.hostDetailsArr.splice(self.hostDetailsArr.indexOf(hostsObjArr), 1);
        }*/
    }

    function onConfirmCompInstChange(versionEleId, rowIndex, isHostVersion, eleType){
    	prevCompDetId = "";
    	prevCompText = "";
    	prevHostCompText = "";
		prevCompHostDetId = "";
		var cobj = {};

		if(eleType == ""){


			/*var initialNoInst = $("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").length;
			//var tableRowId = $("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").attr("id");
			//var hostIndexPart = tableRowId.substring(0, tableRowId.lastIndexOf("row_"));
		
			//for(var hostInst=0; hostInst<initialNoInst; hostInst++){
        		//removeHostDetails(hostIndexPart+"_0");
			//}

			var widgetNameStr = trimSpacesReplaceUnderscore(self.instanceDetailArr()[rowIndex].componentType);

			alert(widgetNameStr);
			var hostAddrVerArr = [];
			for(var inst=0; inst<initialNoInst; inst++){
    			hostAddrVerArr = allInstanceHostVersion[rowIndex][inst].split("~");

				if(removeHostDetails(widgetNameStr, rowIndex, inst)){
					var hostToDeleteObj = $.grep(self.hostDetailsArr(), function(e){ return e.hostAddress() == hostAddrVerArr[0] && e.hostVersionId == hostAddrVerArr[1]; });

					if(hostToDeleteObj.length){
						var hostToDeleteIndex = self.hostDetailsArr.indexOf(hostToDeleteObj[0]);
						self.hostDetailsArr.splice(hostToDeleteIndex, 1);
					}	
				}
			}*/

			var initialNoInst = $("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").length;
			for(var indx=0; indx<initialNoInst; indx++){
    			performHostDelete(rowIndex, indx, true);
			}

			allInstanceHostPort[rowIndex] = [];
			allInstanceHostVersion[rowIndex] = [];

			cobj['componentType'] = $('#compTypeRowId'+versionEleId+' option:selected').text()+versionEleId;
            cobj['eleId'] = versionEleId;
            cobj['componentTypeId'] = $('#compTypeRowId'+versionEleId).val();
           	cobj['panelTitle'] = $('#compTypeRowId'+versionEleId+' option:selected').text();
            cobj['componentId'] = $('#compNameRowId'+versionEleId).val();
            //cobj['componentName'] = $('#compNameRowId'+i+' option:selected').text();
            cobj['versionId'] = $('#compVersionRowId'+versionEleId).val();
            cobj['numberInst'] =  parseInt($('#noInstanceRowId'+versionEleId).val());
            cobj['hostId'] =  $('#compHostRowId'+versionEleId).val();
            cobj['hostComponentName'] = $('#compHostRowId'+versionEleId+' option:selected').text();
            cobj['hostVersionId'] =  $('#compHostVersionRowId'+versionEleId).val();
            cobj['hostVersionName'] = $('#compHostVersionRowId'+versionEleId+' option:selected').text();

            cobj['componentsArr'] = self.componentsArr();
            self.instanceDetailArr.splice(rowIndex, 1, cobj);

            //self.instanceDetailArr.splice(rowIndex,1);
  		  	//self.noInstancesArr.splice(rowIndex,1);

            //if(isHostVersion){

            	

		        /*var hostsObjArr = $.grep(self.hostDetailsArr(), function(e){ return e.serverDetailsId == versionEleId; });

		        for(var host in hostsObjArr){
		        	self.hostDetailsArr.splice(self.hostDetailsArr.indexOf(hostsObjArr[host]), 1);
		        }*/
            //}

            self.configureServerDetails(false);


        }
        else{
        	var initialNoInst = $("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").length;
			for(var indx=0; indx<initialNoInst; indx++){
    			performHostDelete(rowIndex, indx, true);
			}

			allInstanceHostPort[rowIndex] = [];
			allInstanceHostVersion[rowIndex] = [];


	/*var tableRowId = $("#ele"+self.serverDetailsArr()[rowToDelete].id + " #tableCompInstAddMultiple tbody tr").attr("id");
	var hostIndexPart = tableRowId.substring(0, tableRowId.lastIndexOf("row_"));
	var initialNoInst = $("#ele"+self.serverDetailsArr()[rowToDelete].id + " #tableCompInstAddMultiple tbody tr").length;
	for(var hostInst=0; hostInst<initialNoInst; hostInst++){
		removeHostDetails(hostIndexPart+"_0");
	}*/

	    	//self.serverDetailsArr.splice(rowIndex,1);
	    	self.instanceDetailArr.splice(rowIndex,1);
	    	//self.noInstancesArr.splice(rowIndex,1);
        }
    }

    this.onCompVersionChange = function(versionEleId, rowIndex, isHostVersion, eleType){ 
    	if(isHostVersion == 0 && prevCompText!="Select" && $("#compVersionRowId"+versionEleId+" option:selected").text() != "Select" && $("#compHostVersionRowId"+versionEleId+" option:selected").text() != "Select" && rowIndex < self.instanceDetailArr().length){
    	//if((compDetIdArr[0] || compDetIdArr[1] || compDetIdArr[2]) && rowIndex < self.instanceDetailArr().length){
    		
    		//if(!previouslyPrompted){
    			showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP, "question", "confirm", function confirmCallback(confirmReset){
					if (confirmReset) {
				 		window.compInstDetailsClicked(true);
			 			window.compInstDetailsChaged(true);
						onConfirmCompInstChange(versionEleId, rowIndex, isHostVersion, eleType);
					}
			            // - self.instanceDetailArr()[i].numberInst;//parseInt($('#noInstanceRowId'+self.serverDetailsArr()[i].id).val());
	        			//self.instanceDetailArr()[i].numberInst = self.noInstancesArr()[i];
					else{
						var compDetIdArr = prevCompDetId.split("_");
						var compHostDetIdArr = prevCompHostDetId.split("_");

						$("#compTypeRowId"+versionEleId).val(compDetIdArr[0]);
					$('#compTypeRowId'+versionEleId).trigger('chosen:updated');

						self.onComponentTypeChange(versionEleId, rowIndex, 0);
						$("#compNameRowId"+versionEleId).val(compDetIdArr[1]);
						$("#compNameRowId"+versionEleId).trigger('chosen:updated');


						self.onComponentNameChange(versionEleId, rowIndex, 0);
						$("#compVersionRowId"+versionEleId).val(compDetIdArr[2]);
						$("#compVersionRowId"+versionEleId).trigger('chosen:updated');	

						$('#noInstanceRowId'+versionEleId).val($("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").length);
						$("#compHostRowId"+versionEleId).val(compHostDetIdArr[0]);
					$('#compHostRowId'+versionEleId).trigger('chosen:updated');
						self.onHostComponentNameChange(versionEleId, rowIndex);
						$("#compHostVersionRowId"+versionEleId).val(compHostDetIdArr[1]);
					$('#compHostVersionRowId'+versionEleId).trigger('chosen:updated');

						prevCompDetId = "";
						prevCompText = "";
						prevHostCompText = "";
						prevCompHostDetId = "";
					}
				});
    		//}
    		/*else{
    			previouslyPrompted = false;
    		}

    		if(eleType != ""){
    			previouslyPrompted = true;
    		}
    		/*else
    		{
				//onConfirmCompInstChange(versionEleId, rowIndex, isHostVersion, eleType);
    		}*/
    	}
    	
    	
    	if(isHostVersion && prevHostCompText !="Select" && $("#compHostVersionRowId"+versionEleId+" option:selected").text() != "Select" && rowIndex < self.instanceDetailArr().length){
    		showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_HOST_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP, "question", "confirm", function confirmCallback(confirmReset){
				if (confirmReset) {
					prevCompHostDetId = "";

					var initialNoInst = $("#ele"+self.serverDetailsArr()[rowIndex].id + " #tableCompInstAddMultiple tbody tr").length;
					for(var indx=0; indx<initialNoInst; indx++){
		    			performHostDelete(rowIndex, indx, true);
					}

					//allInstanceHostPort[rowIndex] = [];

						for(var inst in allInstanceHostVersion[rowIndex]){
							allInstanceHostVersion[rowIndex][inst] = allInstanceHostVersion[rowIndex][inst].split("~")[0]+"~"+$('#compHostVersionRowId'+versionEleId).val();
						}


							/* cobj['hostId'] =  $('#compHostRowId'+versionEleId).val();
			                cobj['hostComponentName'] = $('#compHostRowId'+versionEleId+' option:selected').text();
			                cobj['hostVersionId'] =  $('#compHostVersionRowId'+versionEleId).val();
			                cobj['hostVersionName'] = $('#compHostVersionRowId'+versionEleId+' option:selected').text();
		*/
				            self.instanceDetailArr()[rowIndex].hostId = $('#compHostRowId'+versionEleId).val();
				            self.instanceDetailArr()[rowIndex].hostComponentName = $('#compHostRowId'+versionEleId+' option:selected').text();
				            self.instanceDetailArr()[rowIndex].hostVersionId = $('#compHostVersionRowId'+versionEleId).val();
				            self.instanceDetailArr()[rowIndex].hostVersionName = $('#compHostVersionRowId'+versionEleId+' option:selected').text();


		    		//hostsTableRowIndex = self.hostDetailsArr()[hostDet].id;
		    		//hostNameVersionId = $('#compHostRowId'+versionEleId).val()+"_"+$('#compHostVersionRowId'+versionEleId).val();
			    	////self.setCompAttributes(hostNameVersionId);
//					
					hostNameVersionId = $('#compHostRowId'+versionEleId).val()+"_"+$('#compHostVersionRowId'+versionEleId).val();
		    		self.setCompAttributes(hostNameVersionId);
			    	//self.getAllIdentifiedHosts();
				}
				else{
					var compHostDetIdArr = prevCompHostDetId.split("_");
					$("#compHostRowId"+versionEleId).val(compHostDetIdArr[0]);
					self.onHostComponentNameChange(versionEleId, rowIndex);
					$("#compHostVersionRowId"+versionEleId).val(compHostDetIdArr[1]);
				}




			});
    	}
    	else{
    		if(isHostVersion && $("#compHostVersionRowId"+versionEleId+" option:selected").text() != "Select"){
					hostNameVersionId = $('#compHostRowId'+versionEleId).val()+"_"+$('#compHostVersionRowId'+versionEleId).val();
		    		self.setCompAttributes(hostNameVersionId);
		    	}
    	}
    }

    this.setServerDetailsForAppType=function(){
    	console.log(self.serverDetailsArr());
    	//alert(self.compInstanceMode());
    	if(self.compInstanceMode() == "Add"){
	    	for(indx in self.componentTypeArr()){
				self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":'', "hostdetails":0, "hostVersion": 0,"isMasterData":0});
				$('#compTypeRowId'+indx).val(self.componentTypeArr()[indx].componentTypeId);
					$('#compTypeRowId'+indx).trigger('chosen:updated');

				$('#compTypeRowId'+indx).prop('disabled', true).trigger('change');
    			self.noInstancesArr.push(1);
	    		$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });
			} 
		}
		else{
			if(self.isServerDetailHasData() == '1'){
				console.log(self.compInstDataForApp());
				hostVersionIdsArr = [];
				for(indx in self.compInstDataForApp()['componentTemplates']){
					self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":'', "hostdetails":0, "hostVersion": 0,"isMasterData":0});
	    			$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });
					
					$('#compTypeRowId'+indx).val(self.compInstDataForApp()['componentTemplates'][indx].componentTypeId);
					$('#compTypeRowId'+indx).trigger('chosen:updated');
					$('#compTypeRowId'+indx).trigger('change');

					$('#compNameRowId'+indx).val(self.compInstDataForApp()['componentTemplates'][indx].componentId);
					$('#compNameRowId'+indx).trigger('change');

					$('#compVersionRowId'+indx).val(self.compInstDataForApp()['componentTemplates'][indx].componentVersionId);
					$('#compVersionRowId'+indx).trigger('change');

					//$('#noInstanceRowId'+indx).val();
					self.noInstancesArr.push(self.compInstDataForApp()['componentTemplates'][indx].noOfInstances);
					$('#compHostRowId'+indx).val(self.compInstDataForApp()['componentTemplates'][indx].hostComponentId);
					$('#compHostRowId'+indx).trigger('change');

					$('#compHostVersionRowId'+indx).val(self.compInstDataForApp()['componentTemplates'][indx].hostVersionId);
					$('#compHostVersionRowId'+indx).trigger('change');
				}

				for(indx in self.compInstDataForApp()['hostTypeInstances']){
					if(hostVersionIdsArr.indexOf(self.compInstDataForApp()['hostTypeInstances'][indx].componentVersionId) == -1){
						hostVersionIdsArr.push(self.compInstDataForApp()['hostTypeInstances'][indx].componentVersionId);
					}
				}

				self.configureServerDetails(false);
			}
			else{
				//for(indx in self.componentTypeArr()){
					/*self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":'', "hostdetails":0, "hostVersion": 0 ,"isMasterData":1});
	    			self.noInstancesArr.push(1);
	    			$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });*/

				//}

				for(indx in self.componentTypeArr()){
					self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":'', "hostdetails":0, "hostVersion": 0,"isMasterData":0});
					$('#compTypeRowId'+indx).val(self.componentTypeArr()[indx].componentTypeId);
					$('#compTypeRowId'+indx).trigger('chosen:updated');

					$('#compTypeRowId'+indx).prop('disabled', true).trigger('change');
	    			self.noInstancesArr.push(1);
		    		$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });
				} 
			}
		}
    }

    function successCallback(data, reqType) {
		if(reqType === "getCompTypeVersion"){//this call is commneted as this was getting used when no data will be there
			//debugger;
			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
			self.componentsArr(data.result);
			//read all component types
			self.componentTypeArr( $.grep(self.componentsArr(), function(e){ return e.componentType != "Host"; }));
			
			if(self.compInstanceMode() == "Edit"){
				self.setServerDetailsForAppType();
			}
			else{
				var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentType == "Host"; });
				self.componentNameHostsArr(componentsObj[0].components);

				self.serverDetailsArr.push({"id":counterValue++,"componentType":0, "component":0 ,"componentVersion":0, "noOfInstances":'', "hostdetails":0, "hostVersion": 0 ,"isMasterData":1});
	    		self.noInstancesArr.push(1);
	    		$(".positive-integer").numeric({ decimal: false, negative: false }, function() { this.value = ""; this.focus(); });
			}
		}
		else if(reqType === "getCompInstDetailsForApp"){
			//debugger;

			console.log("============================Wizard component instance detail data on edit=============================");
			self.compInstDataForApp(data.result);
			self.compInstDetails(data.result.componentTypeInstances);
			
			//console.log(JSON.stringify(self.compInstDataForApp()));
			if(self.compInstDataForApp() && data.result.componentTemplates.length){
				//requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getHostTypeVersion", successCallback, errorCallback);
				self.isServerDetailHasData('1');
			}
			else{
				self.isServerDetailHasData('0');
			}

			requestCall(uiConstants.common.SERVER_IP + "/component/"+self.applicationTypeOnEdit(), "GET", "", "getCompTypeVersionForApplicationType", successCallback, errorCallback);

		}
		else if(reqType === "getCompTypeVersionForApplicationType"){
			//debugger;

			console.log("===========================Inside get comptype for applicationstion type==================================");
			self.componentsArr(data.result);

			if(self.componentsArr().length != 0){
				self.componentTypeArr( $.grep(self.componentsArr(), function(e){ return e.componentType != "Host"; }));
				self.setServerDetailsForAppType();
				
			}
			else{
				requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			}	
		}
		else if(reqType === "getHostTypeVersion"){
			//debugger;

			self.componentsArr(data.result);
			var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentType == "Host"; });
			self.componentNameHostsArr(componentsObj[0].components);

			if(self.compInstanceMode() == "Edit"){
				//uncomment this later
				requestCall(uiConstants.common.SERVER_IP + "/wizard/componentInstances/"+self.appId(), "GET", "", "getCompInstDetailsForApp", successCallback, errorCallback);

				//comment this later
				//requestCall("http://www.mocky.io/v2/5821e3d712000056276f47ff?callback=?", "GET", "", "getCompInstDetailsForApp", successCallback, errorCallback);

			}

		}
		else if(reqType === "addComponentInstances"){
			if(uiConstants.common.DEBUG_MODE){
				console.log("=======================Add ComponentInstance Result Handler =====================================");
				console.log(data);
			}
			var res = data.result;
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCES, "error");
			}
			else{
				showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_ADD_COMP_INSTANCES);
				//$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody').empty();
				$('#btnNext').prop('disabled', true);
				console.log(data.result.ids);
				debugger;
				self.addedCompInstanceData(data.result.ids);

				//$("#"+self.widgetName+"compInst"+compInst).text(compInstArr[compInst].componentInstanceId);


				var compInstName="";
				//To handle comp instance IDs returned
				var compInstNameIdsObj = data.result.instanceMap;
				for(obj in self.instanceDetailArr()){
					self.widgetName( trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType));
					$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody tr').each(function(i){
						compInstName = $('#'+self.widgetName()+'Name'+i).val();

						$("#"+self.widgetName()+"compInst"+i).text(compInstNameIdsObj[compInstName]);
				    });
				}

				self.compInstanceMode("Edit");

				uicommon.postbox.publish(self.addedCompInstanceData(),"addedComponentInstanceDetailResultData");
			}
		}
		else if(reqType === "editComponentInstances"){
			if(uiConstants.common.DEBUG_MODE){
				console.log("=======================Edit ComponentInstance Result Handler =====================================");
				console.log(data);
			}
			var res = data.result;
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(uiConstants.componentInstanceConfig.ERROR_ADD_COMP_INSTANCES, "error");
			}
			else{
				showMessageBox(uiConstants.componentInstanceConfig.SUCCESS_UPDATE_COMP_INSTANCES);
				//$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody').empty();
				$('#btnNext').prop('disabled', true);

				self.addedCompInstanceData(data.result.ids);

				var compInstName="";
				//To handle comp instance IDs returned
				var compInstNameIdsObj = data.result.instanceMap;
				for(obj in self.instanceDetailArr()){
					self.widgetName( trimSpacesReplaceUnderscore(self.instanceDetailArr()[obj].componentType));
					$('#'+self.widgetName()+' #tableCompInstAddMultiple tbody tr').each(function(i){
						compInstName = $('#'+self.widgetName()+'Name'+i).val();

						$("#"+self.widgetName()+"compInst"+i).text(compInstNameIdsObj[compInstName]);
				    });
				}

				uicommon.postbox.publish(self.addedCompInstanceData(),"addedComponentInstanceDetailResultData");
			}
		}
		else if(reqType == "addHostDetails"){
			if(uiConstants.common.DEBUG_MODE){
				console.log("=======================Add host details Result Handler =====================================");
				console.log(data);
			}
		}
		else if(reqType === "getComponentAttributes"){
			//self.compAttributesArr.splice(hostsTableRowIndex,1,data.result);
			if(!self.hostNameVersionIdObj().hasOwnProperty(hostNameVersionId)){
				self.hostNameVersionIdObj()[hostNameVersionId] = data.result;
			}

			if(hostsTableRowIndex != -1){
				self.compAttributesArr.splice(hostsTableRowIndex,1,data.result);
				hostsTableRowIndex = -1;
			}
			
		}
		else if(reqType === "getMultipleComponentAttributes"){
			self.hostNameVersionIdObj(data.result);

			var compTypeInstArr = self.compInstDataForApp()['hostTypeInstances'];
					//var compTypeIdStrPart = "";
			var compInstArr = [];
			var type = "";
			var hostAttribArr = [];

			console.log(compTypeInstArr);
			console.log(self.hostDetailsArr());
			console.log(self.hostNameVersionIdObj());

			self.compAttributesArr([]);

			/*for(var host=self.hostDetailsArr().length-1; host<(compTypeInstArr.length-1); host++){
			}*/

			//self.addHosts()

			var hostCounter = 0;

			for(var host in compTypeInstArr){
    			//var compTypeInst = $.grep(compTypeInstArr, function(e){ return e.componentId == self.hostDetailsArr()[host].hostComponentId && e.componentVersionId == self.hostDetailsArr()[host].hostsVersionId; });	

				compInstArr = compTypeInstArr[host].componentInstances;

				//console.log(compInstArr);

				for(var compInst in compInstArr){
					//alert(compInstArr[compInst].componentVersionId);
					if(compInst != "0"){
						hostCounter++;
					}
					if((self.hostDetailsArr().length-1)<hostCounter){
						self.addHosts(self.hostDetailsArr().length-1);
					}

					$("#HostName" + hostCounter).val(compInstArr[compInst].componentInstanceName);
					$("#compHostDetailsRowId"+hostCounter).val(compInstArr[compInst].componentId);
					self.onHostComponentNameChangeForHostDetails(hostCounter);
					$("#compHostInstVersionRowId"+hostCounter).val(compInstArr[compInst].componentVersionId);
					$("#hostId"+hostCounter).text(compInstArr[compInst].componentInstanceId);
					//alert(compInstArr[compInst].componentId+"_"+compInstArr[compInst].componentVersionId);
					hostAttribArr = self.hostNameVersionIdObj()[compInstArr[compInst].componentId+"_"+compInstArr[compInst].componentVersionId];
    				self.compAttributesArr.push([]);
    				self.compAttributesArr.splice(hostCounter, 1, hostAttribArr);
					
					console.log("#####################");
					console.log(hostAttribArr);

					for(var hostAttrib in compInstArr[compInst].attributes){
							

						type = compInstArr[compInst].attributes[hostAttrib].attributeType;

						if(type.toUpperCase() == 'PASSWORD' || type.toUpperCase() == 'TEXTBOX'){

							$("#"+trimSpacesReplaceUnderscore(compInstArr[compInst].attributes[hostAttrib].attributeName) + hostCounter).val(compInstArr[compInst].attributes[hostAttrib].attributeValue);
						}
						else if(type.toUpperCase() == 'DROPDOWN'){
							$("#"+trimSpacesReplaceUnderscore(compInstArr[compInst].attributes[hostAttrib].attributeName) + hostCounter).val(compInstArr[compInst].attributes[hostAttrib].attributeValue);
						}
						else if(type.toUpperCase() == 'CHECKBOX'){
							$("#"+trimSpacesReplaceUnderscore(compInstArr[compInst].attributes[hostAttrib].attributeName) + hostCounter).prop('checked',compInstArr[compInst].attributes[hostAttrib].attributeValue);
						}
					}

					$("#hostAddress"+hostCounter).val($("#HostAddress"+hostCounter).val());
				}

				hostCounter++;
			}
			console.log(self.compAttributesArr());
		}

		else if(reqType === "getHosts"){
				if(data.result.length == 0){
					self.hostsArr([]);
				}
				else{
					self.hostsArr(data.result);
				}

				requestCall(uiConstants.common.SERVER_IP + "/component", "GET", "", "getHostTypeVersion", successCallback, errorCallback);


				/*if(self.compInstanceMode() == "Edit"){
					
				}*/

				/*$("#hostsList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});*/
			}
	}

	function errorCallback(reqType) {
		if(reqType === "getCompTypeVersion"){
  			showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  		}
	}
    
}

Componentinstanceconfigwizard.prototype.dispose = function() { };
return { viewModel: Componentinstanceconfigwizard, template: templateMarkup };

});