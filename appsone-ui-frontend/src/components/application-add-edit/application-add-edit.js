define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead','text!./application-add-edit.html','hasher','validator','ui-constants','ui-common','scrollable-tooltip'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,scrollabletooltip) {

	function ApplicationAddEdit(params) {	
		var self = this;
		var maintProfileLoaded = 0;

		this.timezoneArr = params.timezoneArr;
		this.currentViewIndex = params.currentViewIndex;
		this.selectedConfigRows = params.selectedConfigRows;
		this.pageSelected = params.pageSelected;

		this.maintProfileArr = ko.observableArray();
		this.appId = ko.observable(0);
		this.appName = ko.observable();
		this.appDescription = ko.observable();
		this.maintProfileId = ko.observable();
		this.appStatus = ko.observable(true);
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
  		this.selectedConfigNames = ko.observable("");
  		this.modalTitle = ko.observable();
  		this.appType = ko.observable();

  		//this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.kpiStatus = ko.observable(0);
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteCopyArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();

		this.panelTitle = params.panelTitle;
		this.mode = params.mode;
		this.addedAppData= ko.observable();

		//show model window
		this.displayComponent = ko.observable(false);
		this.typeArr = ko.observableArray(getAppTypesArr());

		var configTagLoaded = 0;
		var tagsNameArr = [];
		var commonTagsArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
		var listImgEle = "<i class='tip glyphicon glyphicon-info-sign' style='float: right; color: #363698;'></i>";

		function getAppTypesArr(){
			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				return getMasterList(params.typeArr(), "masterId", [self.selectedConfigRows()[0].applicationTypeId], true);
			}
			else{
				return getMasterList(params.typeArr(), "masterId", null, false);
			}
		}

		this.onAddMultipleClick = function(){
			  hasher.setHash('#addMultiple');    
		}

		this.getFilterListData = function(){
			//alert("clicked");
			//self.requestCall(uiConstants.common.SERVER_IP + "/applications?limit=" + self.totalRecordsPerPage() + "&offset=" + self.currentPage() +"&filterfield=name&filter=" +self.fAppname , "GET", "", "getListData");			
		}

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			$('#applicationsTab').on('click', function(){
		 		window.appDetailsClicked(true);
		 	});

		 	$('#applicationsTab').on('change', function(){
		 		if(window.appDetailsClicked()){
		 			window.appDetailsChaged(true);
		 		}
		 	});

		 	$('#txtName').on('change', function(){
		 		window.appDetailsChaged(true);
		 	});

			var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_APPLICATION_LINK; });
      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
				$("#modalAppType").css("visibility","hidden");
      		}
			maintProfileLoaded = 0;

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true/*,
				allow_single_deselect: true*/
			});

			$("#txtName").focus();

			$("#idModal").on('show.bs.modal', function () {
				self.modalTitle("Add Application Type");
				self.displayComponent(true);
			});

			$("#idModal").on('hidden.bs.modal', function () {
				self.displayComponent(false);
		        if(self.appType() && self.appType() != ""){
	 				requestCall(uiConstants.common.SERVER_IP + "/masterTypes/application?status=2&markInactive=1", "GET", "", "getApplicationType", successCallback, errorCallback);
		        }
		    });

			$("#appStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#appStatus').bootstrapSwitch('state', self.appStatus());

			$("#appStatus").on('switchChange.bootstrapSwitch', function () {
				self.appStatus($('#appStatus').bootstrapSwitch('state')?1:0);
			});

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0 && self.currentViewIndex() != uiConstants.common.START_WIZARD){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["applicationName"]));
			}

			$('#application-tokenfield-typeahead').on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			$('#application-tokenfield-typeahead')
				  /*.on('tokenfield:createtoken', function (e) {
				    var data = e.attrs.value.split('|')
				    e.attrs.value = data[1] || data[0]
				    e.attrs.label = data[1] ? data[0] + ' (' + data[1] + ')' : data[0]
				  })*/

				  .on('tokenfield:createdtoken', function (e) {
				  		if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
							tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label

						}
						var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
						//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
						if(tagIndex != -1){
							self.configTagAutoCompleteArr.splice(tagIndex, 1);
						}

						$('#application-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
						 	{source: self.configTagAutoCompleteArr()
						});
				  })

				  .on('tokenfield:edittoken', function(e){
				  		if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
							return false;
						}
				  })

				  .on('tokenfield:editedtoken', function (e) {
					  		if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
							return false;
						}
				  })

				  .on('tokenfield:removedtoken', function (e) {
						if((commonTagsArr.indexOf(e.attrs.label.trim()) !=-1 || tagsNameArr.indexOf(e.attrs.label.trim()) !=-1) && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
							tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
						}
						tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
						var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

						if(tagIndex != -1){
							self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
							self.configTagAutoCompleteArr.sort();
						}

						$('#application-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
						 	{source: self.configTagAutoCompleteArr()
						});
				  })

				  //.tokenfield()

			debugger;
			if(params.selectedConfigRows().length>1){
				$("#divAppName").hide();
				$("#divAppDescription").hide();
				$("#divAppType").hide();


				//For setting the background color of common fields back to white
				$("#appTypeList").on('input', function () {
				    //$("#appTypeList").css("background-color", uiConstants.common.COMMON_VALUE_BGCOLOR);
					$("#appTypeList").next(".chosen-container").css("background-color", uiConstants.common.COMMON_VALUE_BGCOLOR);
				});

				$("#timezoneList").on('change', function () {
					$("#timezoneList").chosen().next(".chosen-container").css("background-color", uiConstants.common.COMMON_VALUE_BGCOLOR);
				});

				/*$("#maintProfileList").on('change', function () {
					$("#maintProfileList").chosen().next(".chosen-container").css("background-color", uiConstants.common.COMMON_VALUE_BGCOLOR);
				});*/

				$("#chkImediateMaintainence").on('switchChange.bootstrapSwitch', function () {
					$("#chkImediateMaintainence").bootstrapSwitch('onColor', "success");
					$("#chkImediateMaintainence").bootstrapSwitch('offColor', "danger");
				});
			}
			else{
			}

			requestCall(uiConstants.common.SERVER_IP + "/coverageWindowProfiles/mstSubType/0", "GET", "", "getMaintenanceProfile", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/tag?type=application", "GET", "", "getApplicationTag", successCallback, errorCallback);

			//switch init
			$("#chkImediateMaintainence").bootstrapSwitch();

			//$("#timezoneList_chosen").prop("title","test tooltip");
			/*$("#maintProfileList_chosen .chosen-search").find("input").on("keyup", function (evt) {
				self.onMaintProfileListOpen();
			});*/
		}

		function onMastersLoad(){
			if(uiConstants.common.DEBUG_MODE)console.log(self.typeArr());
			if(uiConstants.common.DEBUG_MODE)console.log(self.timezoneArr());
			if(uiConstants.common.DEBUG_MODE)console.log(self.maintProfileArr());

			if(self.typeArr().length>0 && self.timezoneArr().length>0 && maintProfileLoaded == 1){
				//Check if the current view is application edit
				if(self.currentViewIndex() == 3 || self.currentViewIndex() == 8){
					if(params.selectedConfigRows().length == 1){ // for single application edit
						editSingleApplication(params.selectedConfigRows());
						if(!params.selectedConfigRows()[0].status){ //if the application is inactive
							setApplicationUneditable(true);
						}
					}
					else{
						editMultipleApplications(params.selectedConfigRows());
					}
				}

				else if(self.currentViewIndex() == 4){
					cloneApplication(params.selectedConfigRows());
				}

				else if(self.currentViewIndex() == 5){
					if(uiConstants.common.DEBUG_MODE)console.log(params.selectedConfigRows());
					viewApplication(params.selectedConfigRows());
				}
			}
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		
		uicommon.postbox.subscribe(function(value) {
		     uicommon.postbox.publish(self.addEditApplication(),"addApplication");
		},"saveClickEvent");

		/*this.onMaintProfileListOpen = function(){
			$("#maintProfileList_chosen")
		    .find("li.active-result").each(function() {
	    	 	if ($(this).attr("data-option-array-index") == 0) //1 is added since the first item in the list will be the text indicating the user to select the option
		        	return;
		    	var value = self.maintProfileArr()[$(this).attr("data-option-array-index")-1];
				var htmlText = "<table>";
				for(var day in value.coverageWindowlist){
					htmlText += "<tr><td>"+value.coverageWindowlist[day].day+"&nbsp;&nbsp;</td><td>";
					for(var timeSlot in value.coverageWindowlist[day].timeSlots){
						htmlText += ('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startMinute).slice(-2)+" to "+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endMinute).slice(-2)+"<br>";
					}

					htmlText += "</td></tr>";
				}

				htmlText+="</table>";

				if($(this).find("img").length == 0){
					//$("#maintProfileList_chosen span").append(listImgEle);

				    $(this).append(listImgEle);

				    $('.tip').tooltipster({
				    	plugins: ['sideTip', 'scrollableTip'],
					    content: $(htmlText)
					});
				}
			});
		}*/

		/*function setListImg(){
			$("#maintProfileList_chosen")
		    .find("li.active-result").each(function() {
		    	if ($(this).attr("data-option-array-index") == 0) //1 is added since the first item in the list will be the text indicating the user to select the option
		        	return;
		    	var value = self.maintProfileArr()[$(this).attr("data-option-array-index")-1];
				var htmlText = "<table>";
				for(var day in value.coverageWindowlist){
					htmlText += "<tr><td>"+value.coverageWindowlist[day].day+"&nbsp;&nbsp;</td><td>";
					for(var timeSlot in value.coverageWindowlist[day].timeSlots){
						htmlText += ('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startMinute).slice(-2)+" to "+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endMinute).slice(-2)+"<br>";
					}

					htmlText += "</td></tr>";
				}

				htmlText+="</table>";

		    	if($(this).find("img").length == 0){
					//$("#maintProfileList_chosen span").append(listImgEle);

				    $(this).append(listImgEle);

				    $('.tip').tooltipster({
				    	plugins: ['sideTip', 'scrollableTip'],
					    content: $(htmlText)
					});
				}
			});
		}*/

		//Adding/Updating single application
		this.addEditApplication = function(){
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows().length);
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var appObjArr = [];
			var appData;

			$("#txtName").val($("#txtName").val().trim());
			$("#txtDescription").val($("#txtDescription").val().trim());
			window.appDetailsChaged(false);

			//var appTimezone=$("#timezoneList").chosen().val();//getTimezoneIdByName($('#timezoneId').val());
			//var appMaintainenceProfile=getMaintProfileIdByName($('#maintProfileId').val());

			if(self.selectedConfigRows().length > 1){
				self.updateMultipleApplications();
			}
			else{
				if($("#divAppAddEdit #txtName").val() == ""){
					//self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
					showError("#divAppAddEdit #txtName", uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
			    	self.errorMsg("#divAppAddEdit #txtName");
				}
				else if($("#divAppAddEdit #txtName").val().length < 2){
					//self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MIN_LENGTH_ERROR);
					showError("#divAppAddEdit #txtName", uiConstants.applicationConfig.APPLICATION_NAME_MIN_LENGTH_ERROR);
			    	self.errorMsg("#divAppAddEdit #txtName");
				}
				else if($("#divAppAddEdit #txtName").val().length > 45){
					//self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_MAX_LENGTH_ERROR);
					showError("#divAppAddEdit #txtName", uiConstants.applicationConfig.APPLICATION_NAME_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divAppAddEdit #txtName");
				}
				else if(!nameValidation($("#divAppAddEdit #txtName").val())){
					//self.errorMsg(uiConstants.applicationConfig.APPLICATION_NAME_INVALID_ERROR);
					showError("#divAppAddEdit #txtName", uiConstants.applicationConfig.APPLICATION_NAME_INVALID_ERROR);
			    	self.errorMsg("#divAppAddEdit #txtName");
				}
				if($("#divAppAddEdit #txtDescription").val() == ""){
					//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
					showError("#divAppAddEdit #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    	self.errorMsg("#divAppAddEdit #txtDescription");
				}
				else if($("#divAppAddEdit #txtDescription").val().length < 25){
					//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
					showError("#divAppAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    	self.errorMsg("#divAppAddEdit #txtDescription");
				}
				else if($("#divAppAddEdit #txtDescription").val().length > 256){
					//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
					showError("#divAppAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divAppAddEdit #txtDescription");
				}
				if($('#divAppAddEdit #appTypeList').val() == 0 ){
					//self.errorMsg(uiConstants.common.APPLICATION_TYPE_REQUIRED);
					showError("#divAppAddEdit #appTypeList_chosen", uiConstants.common.APPLICATION_TYPE_REQUIRED);
					showError("#divAppAddEdit #appTypeList_chosen span", uiConstants.common.APPLICATION_TYPE_REQUIRED);
			    	self.errorMsg("#divAppAddEdit #appTypeList_chosen");
				}
				else{
					removeError("#divAppAddEdit #appTypeList_chosen");
					removeError("#divAppAddEdit #appTypeList_chosen span");
				}
				
				removeError("#divAppAddEdit .tokenfield");
				removeError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield");
				if(containsDuplicate($("#divAppAddEdit #application-tokenfield-typeahead").val())){
					//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
					showError("#divAppAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
					showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
				    self.errorMsg("#divAppAddEdit .tokenfield");
				}
				//else if($('#maintProfileId').val() != "" && appMaintainenceProfile == null){
				/*else if($("#maintProfileList").chosen().val() == ""){
					self.errorMsg(uiConstants.common.INVALID_MAINTENANCE_PROFILE);
				}*/
				else{
					

					if(self.tags() && self.tags().trim().length == 1)
						tagsArr.push(self.tags());

					else if(self.tags() && self.tags().trim().length > 1)
						tagsArr = self.tags().split(",");
					
						for(var t in tagsArr){
							if(tagsArr[t].trim().length < 2){
								//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
								showError("#divAppAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
								showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
							    self.errorMsg("#divAppAddEdit .tokenfield");
								break;
							}
							else if(tagsArr[t].trim().length > 45){
								//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
								showError("#divAppAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
								showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
							    self.errorMsg("#divAppAddEdit .tokenfield");
								break;
							}
							else if(!tagValidation(tagsArr[t].trim())){
								//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
								showError("#divAppAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
								showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
							    self.errorMsg("#divAppAddEdit .tokenfield");
								break;
							}
						}

					if(self.errorMsg() == ""){
						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":0, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
						}

						var appObj = {
							"index":1,
							"applicationId": self.appId(),
							"applicationName": self.appName(),
							"description": self.appDescription(),
							"applicationType": $("#appTypeList option:selected").text(),
							"applicationTypeId": parseInt($("#appTypeList").val()),
							"tags": tagsObjArr,
							"timezoneId": ($("#timezoneList").chosen().val() == "Select" || $("#timezoneList").chosen().val() == null) ? 0 : parseInt($("#timezoneList").chosen().val()),
							//"maintenanceWindowProfileId": parseInt($("#maintProfileList").chosen().val()),
							"maintenanceFlag" : $('#chkImediateMaintainence').bootstrapSwitch('state')?1:0,
							"status" : $('#appStatus').bootstrapSwitch('state')};

						appObjArr.push(appObj);
						appData = {"applications":appObjArr};

						if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(appData));

						if(self.appId() == 0)
							requestCall(uiConstants.common.SERVER_IP + "/applications", "POST", JSON.stringify(appData), "addSingleApp", successCallback, errorCallback);
						else
							requestCall(uiConstants.common.SERVER_IP + "/applications", "PUT", JSON.stringify(appData), "editSingleApp", successCallback, errorCallback);
					}
				}

			}
			if(self.errorMsg() !== "")
			{
				return true;
			}
			else
			{
				return false;
			}
		}
		
	
		//Updating multiple applications
		this.updateMultipleApplications = function(){
			var appObjArr = [];
			var appData;
			var appObj;
			var tagsObjArr = [];
			var tagsArr = [];
			self.errorMsg("");

			if(uiConstants.common.DEBUG_MODE)console.log("self.selectedConfigRows()"+self.selectedConfigRows().length);
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());

			//var appTimezone=getTimezoneIdByName($('#timezoneId').val());

			removeError("#divAppAddEdit .tokenfield");
			removeError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divAppAddEdit #application-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divAppAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divAppAddEdit .tokenfield");
			}
			/*else if($('#appTypeList').val() == "Select Type" ){
				self.errorMsg(uiConstants.common.APPLICATION_TYPE_REQUIRED);
			}*/
			else{
				tagsArr = self.tags() ? self.tags().split(",") : [];

				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divAppAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divAppAddEdit .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divAppAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divAppAddEdit .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divAppAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divAppAddEdit #application-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divAppAddEdit .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(selConfig in self.selectedConfigRows()){
						tagsObjArr = [];
						tagsArr = [];
						if(self.tags() && self.tags().trim().length == 1){
							tagsArr.push(self.tags());
						}

						else if(self.tags() && self.tags().trim().length > 1){
							tagsArr = self.tags().split(",");
						}

						for(var tag in tagsArr){
							if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
								tagsObjArr.push({"tagId":null, 
									"tagName":tagsArr[tag].trim(), 
									"tagOperation":"add"});
							}
							else{
								tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), 
									"tagName":tagsArr[tag].trim(),
									"tagOperation":"none"});
							}
						}

						for(tag in tagsToDeleteArr){
							tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), 
								"tagName":tagsToDeleteArr[tag].trim(), 
								"tagOperation":"delete"});
						}



						for(var tag in self.selectedConfigRows()[selConfig].tags){
							if(tagsToDeleteArr.indexOf(self.selectedConfigRows()[selConfig].tags[tag].tagName.trim()) == -1 && tagsArr.indexOf(self.selectedConfigRows()[selConfig].tags[tag].tagName.trim()) == -1){
								tagsObjArr.push({
									"tagId":self.selectedConfigRows()[selConfig].tags[tag].tagId,
									"tagName":self.selectedConfigRows()[selConfig].tags[tag].tagName.trim(), 
									"tagOperation":"none"});
							}
						}
						appObj = {
						"index": parseInt(selConfig) + 1,
						"applicationId": self.selectedConfigRows()[selConfig].applicationId,
						"applicationName":self.selectedConfigRows()[selConfig].applicationName,
						"applicationType": $("#appTypeList option:selected").val(),
						"description": self.selectedConfigRows()[selConfig].description,
						"tags": tagsObjArr,
						"timezoneId": rgbTohex($("#timezoneList").next(".chosen-container").css("background-color")).toUpperCase() === uiConstants.common.UNCOMMON_VALUE_BGCOLOR ? 
						(self.selectedConfigRows()[selConfig].timezone.timeZoneId != undefined ? self.selectedConfigRows()[selConfig].timezone.timeZoneId : 0) :
						($("#timezoneList").chosen().val() == 0 ? 0 : parseInt($("#timezoneList").chosen().val())),

						//"maintenanceWindowProfileId": parseInt(rgbTohex($("#maintProfileList").next(".chosen-container").css("background-color")).toUpperCase() === uiConstants.common.UNCOMMON_VALUE_BGCOLOR ? 
						//(self.selectedConfigRows()[selConfig].maintenanceWindowProfile != undefined ? (self.selectedConfigRows()[selConfig].maintenanceWindowProfile.profileId || "0"): "0") :
						//($("#maintProfileList").chosen().val() || "0")),


						"maintenanceFlag" : $("#chkImediateMaintainence").bootstrapSwitch('onColor') == uiConstants.common.BTSWITCH_UNCOMMON_COLOR ?
						self.selectedConfigRows()[selConfig].maintenanceFlag : $('#chkImediateMaintainence').bootstrapSwitch('state') ? 1 : 0,

						"status" : $('#appStatus').bootstrapSwitch('indeterminate') ? self.selectedConfigRows()[selConfig].status : $("#appStatus").prop("checked")};
						
						appObjArr.push(appObj);
					}
				}

				if(self.errorMsg() == ""){
					appData = {"applications":appObjArr};			
					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(appData));
					requestCall(uiConstants.common.SERVER_IP + "/applications", "PUT", JSON.stringify(appData), "editMultipleApp", successCallback, errorCallback);
				}				
			}
		}

		function editSingleApplication(appObj){
			if(uiConstants.common.DEBUG_MODE)console.log(appObj[0]);
			for(var tagObj in appObj[0].tags){
				tagsNameArr.push(appObj[0].tags[tagObj].tagName);
			}

			self.appId(appObj[0].applicationId);
			self.appName(appObj[0].applicationName);
			self.appDescription(appObj[0].description);
			self.appStatus(appObj[0].status);
			$('#appStatus').bootstrapSwitch('state',self.appStatus());

			if(!self.typeArr().find( function( ele ) {return ele.masterId && ele.masterId === appObj[0].applicationTypeId;} )){
				self.typeArr.push({masterId: appObj[0].applicationTypeId, name: appObj[0].applicationType});
				$("#appTypeList_chosen span").first().addClass("inactiveOptionClass");
			}



			$("#appTypeList").val(appObj[0].applicationTypeId).prop('disabled', true).trigger('chosen:updated');

			if(appObj[0].timezone.timeZoneId != undefined){
				$("#timezoneList").val(appObj[0].timezone.timeZoneId).trigger('chosen:updated');
			}
			else{
				$("#timezoneList").val("");
			}

			/*if(appObj[0].maintenanceProfileId != undefined){
				$("#maintProfileList").val(appObj[0].maintenanceProfileId).trigger('chosen:updated');
			}
			else{
				$("#maintProfileList").val("");
			}*/

			$('#application-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);
			$('#chkImediateMaintainence').bootstrapSwitch('state',appObj[0].maintenanceFlag);
			//$("#maintProfileList").val((appObj[0].maintenanceWindowProfile != undefined && appObj[0].maintenanceWindowProfile.profileId != undefined) ? appObj[0].maintenanceWindowProfile.profileId : "0").trigger('chosen:updated');

			uicommon.postbox.publish([appObj[0].applicationId, appObj[0].applicationTypeId], "appDetWithTypeId");
		}

		function viewApplication(appObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleApplication(appObj);
			setApplicationUneditable(false);			
		}

		function setApplicationUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$("#appTypeList").prop('disabled', true).trigger('chosen:updated');
			$('#timezoneList').prop('disabled', true).trigger("chosen:updated");
			$('#application-tokenfield-typeahead').tokenfield('readonly');
			$('#application-tokenfield-typeahead').tokenfield('readonly');
			$("[name='chkImediateMaintainence']").bootstrapSwitch('disabled',true);
			//$('#maintProfileList').prop('disabled', true).trigger("chosen:updated");

			//$(".chosen-disabled").addClass("readonly-element");

			if(!isInactiveEdit){
				//document.getElementById("appStatus").disabled = true;
				$("[name='appStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divAppAddEdit .chosen-container b").css("display", "none");
		}

		function editMultipleApplications(appObj){
			var appCommonTypeArr = [];
			var appCommonTimezoneArr = [];
			var appCommonProfileArr = [];
			var appCommonStatusArr = [];
			var appCommonMaintFlagArr = [];
			appCommonTypeArr.push(appObj[0].applicationType);			
			var timezoneStr = getFormattedTimezone(appObj[0]);
			commonTagsArr = [];
			
			if(timezoneStr	!= "")
				appCommonTimezoneArr.push(timezoneStr);
			
			if(appObj[0].maintenanceWindowProfile != undefined && appObj[0].maintenanceWindowProfile.profileName)
				appCommonProfileArr.push(appObj[0].maintenanceWindowProfile.profileName);
			else
				appCommonProfileArr.push("");
			
			appCommonStatusArr.push(appObj[0].status);
			appCommonMaintFlagArr.push(appObj[0].maintenanceFlag);

			//Common tokenfield for multiple edit
			/*var commonEngine = new Bloodhound({	
				local: self.configTagAutoCompleteArr(),
				datumTokenizer: function(d) {
					return Bloodhound.tokenizers.whitespace(d.value);
				},
				queryTokenizer: Bloodhound.tokenizers.whitespace
			});

			commonEngine.initialize();

			$('#application-tokenfield-typeahead').tokenfield({
				typeahead: [null, { source: commonEngine.ttAdapter() }],
				createTokensOnBlur: true
			});

			$('#application-tokenfield-typeahead-tokenfield').on("keyup", function(e){
				if(e.which == 13 && $(this).val().trim() != ""){
					$("#application-tokenfield-typeahead-tokenfield").blur();		
				}
			});*/

			$('#application-tokenfield-typeahead').tokenfield({
			  autocomplete: {
			    source: self.configTagAutoCompleteArr(),
			    delay: 100
			  },
			  createTokensOnBlur: true
			});

			$('#application-tokenfield-typeahead-tokenfield').on("keyup", function(e){
				if(e.which == 13 && $(this).val().trim() != ""){
					$("#application-tokenfield-typeahead-tokenfield").blur();		
				}
			});

			var tagsArr = [];
			var allTagsArr = [];
			var tagsObjArr = [];

			for(obj in appObj){
				tagsObjArr = [];
				for(tag in appObj[obj].tags){

					if(uiConstants.common.DEBUG_MODE)console.log(appObj[obj].tags[tag].tagName);
					tagsObjArr.push(appObj[obj].tags[tag].tagName);
					//if (allTagsArr.indexOf(appObj[obj].tags[tab].tagName)==-1) allTagsArr.push(appObj[obj].tags[tab].tagName);
				}
				tagsArr.push(tagsObjArr);
			}

			commonTagsArr = tagsArr.shift().filter(function(v) {
			    return tagsArr.every(function(a) {
			        return a.indexOf(v) !== -1;
			    });
			});

			$('#application-tokenfield-typeahead').tokenfield('setTokens', commonTagsArr);

			for(obj in appObj){
				if(uiConstants.common.DEBUG_MODE)console.log("appObj[0].maintenanceFlag:"+appObj[obj].maintenanceFlag);

				/*if(appCommonTypeArr.length<=1){
					if(appCommonTypeArr.indexOf(appObj[obj].applicationType) == -1){
						appCommonTypeArr.push(appObj[obj].applicationType);	
					}
				}*/

				if(appCommonTimezoneArr.length<=1){
					timezoneStr = getFormattedTimezone(appObj[obj]);

					if(uiConstants.common.DEBUG_MODE)console.log(timezoneStr);
					
					if(appCommonTimezoneArr.indexOf(timezoneStr) == -1){
						appCommonTimezoneArr.push(timezoneStr);	
					}
				}

				if(appCommonProfileArr.length<=1){
					if(appObj[obj].maintenanceWindowProfile != undefined && appObj[obj].maintenanceWindowProfile.profileName != undefined){
						if(appCommonProfileArr.indexOf(appObj[obj].maintenanceWindowProfile.profileName) == -1){
							appCommonProfileArr.push(appObj[obj].maintenanceWindowProfile.profileName);
						}
					}
					else if(appCommonProfileArr.indexOf("") == -1 ){
						appCommonProfileArr.push("");
					}
				}

				if(uiConstants.common.DEBUG_MODE)console.log(appCommonProfileArr);
				
				if(appCommonStatusArr.length<=1){
					if(appCommonStatusArr.indexOf(appObj[obj].status) == -1){
						appCommonStatusArr.push(appObj[obj].status);	
					}
				}

				if(appCommonMaintFlagArr.length<=1){
					if(appCommonMaintFlagArr.indexOf(appObj[obj].maintenanceFlag) == -1){
						appCommonMaintFlagArr.push(appObj[obj].maintenanceFlag);	
					}
				}
			}
			
			/*if(appCommonTypeArr.length == 1){
				$("#appTypeList").val(appObj[0].applicationType);
			}
			else{
				$("#appTypeList").val("");
				$("#appTypeList").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);

			}*/

			if(appCommonTimezoneArr.length == 1){
				//$("#timezoneId").val(getFormattedTimezone(appObj[0]));
				$("#timezoneList").val(appObj[0].timezone.timeZoneId).trigger('chosen:updated');
			}
			else{

				//$("#timezoneId").val("");
				$("#timezoneList").val("").trigger('chosen:updated');
				$("#timezoneList").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);

				if(uiConstants.common.DEBUG_MODE)console.log($("#timezoneList").chosen().css("background-color"));
			}

			/*if(appCommonProfileArr.length == 1){
				$("#maintProfileList").val((appObj[0].maintenanceWindowProfile != undefined && appObj[0].maintenanceWindowProfile.profileId != undefined) ? appObj[0].maintenanceWindowProfile.profileId : "").trigger('chosen:updated');
			}
			else{
				$("#maintProfileList").val("").trigger('chosen:updated');
				$("#maintProfileList").next(".chosen-container").css("background-color", uiConstants.common.UNCOMMON_VALUE_BGCOLOR);
			}*/

			if(appCommonStatusArr.length == 1){
				//$('#appStatus').prop('checked', appObj[0].status);
				$('#appStatus').bootstrapSwitch('state', appObj[0].status);

				if(!appObj[0].status){ //is status is inactive
					setApplicationUneditable(true);
				}
			}
			else{
				//$("#appStatus").prop("indeterminate", true);
				$('#appStatus').bootstrapSwitch('indeterminate', true);

				setApplicationUneditable(true);
			}

			if(uiConstants.common.DEBUG_MODE)console.log("appCommonMaintFlagArr.length:"+appCommonMaintFlagArr.length);
			if(appCommonMaintFlagArr.length == 1){
				$('#chkImediateMaintainence').bootstrapSwitch('state', appObj[0].maintenanceFlag)
			}
			else{
				$("#chkImediateMaintainence").prop("checked", false);
				$("#chkImediateMaintainence").bootstrapSwitch('onColor', uiConstants.common.BTSWITCH_UNCOMMON_COLOR);
				$("#chkImediateMaintainence").bootstrapSwitch('offColor', uiConstants.common.BTSWITCH_UNCOMMON_COLOR);
			}		
		}

		function cloneApplication(appObj){
			if(uiConstants.common.DEBUG_MODE)console.log(appObj[0]);
			var tagsObjArr = [];

			for(var tagObj in appObj[0].tags){
				tagsObjArr.push(appObj[0].tags[tagObj].tagName);
			}

			//self.appStatus(appObj[0].status);



			if($.grep(self.typeArr(), function(e){ return  e.masterId == appObj[0].applicationTypeId; }).length){
				$("#appTypeList").val(appObj[0].applicationTypeId).trigger('chosen:updated');
			}
			else{
				$("#appTypeList").val("0").trigger('chosen:updated');
			}




			/*if(!self.typeArr().find( function( ele ) {return ele.masterId && ele.masterId === appObj[0].applicationTypeId;} )){
				$("#appTypeList").val(0).trigger('chosen:updated');
			}
			else{
				$("#appTypeList").val(appObj[0].applicationTypeId).trigger('chosen:updated');
			}*/
			$("#timezoneList").val(appObj[0].timezone.timeZoneId).trigger('chosen:updated');
			$('#application-tokenfield-typeahead').tokenfield('setTokens', tagsObjArr);
			$('#chkImediateMaintainence').bootstrapSwitch('state',appObj[0].maintenanceFlag);
			//$("#maintProfileList").val((appObj[0].maintenanceWindowProfile != undefined && appObj[0].maintenanceWindowProfile.profileId != undefined) ? appObj[0].maintenanceWindowProfile.profileId : "").trigger('chosen:updated');
			if(appObj[0].status == 0){
				self.appStatus(!appObj[0].status);
			}
			else{
				self.appStatus(appObj[0].status);
			}
			$('#appStatus').bootstrapSwitch('state',self.appStatus());
		}

		this.cancelApplication = function(){
			params.selectedConfigRows([]);
			self.pageSelected("Application Configuration");
			params.currentViewIndex(0);
		}

		function getFormattedTimezone(timezoneObj){
			if(timezoneObj.timezone.timeZoneName == undefined)
				return "";
			else
				return timezoneObj.timezone.timeZoneName;
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function getTimezoneIdByName(timezone){
			if(timezone == "" || timezone == null)
				return null;
			for(tzone in self.timezoneArr()){
				if((self.timezoneArr()[tzone].timeZoneName.trim()) == timezone.trim()){
					return self.timezoneArr()[tzone].timeZoneId;
				}
			}
			return null;
		}

		/*function getMaintProfileIdByName(maintProfile){
			if(maintProfile == "" || maintProfile == null)
				return null;
			if(uiConstants.common.DEBUG_MODE)console.log("maintProfile:"+maintProfile);
			for(maintProf in self.maintProfileArr()){
				if(uiConstants.common.DEBUG_MODE)console.log(self.maintProfileArr()[maintProf]);
				if(self.maintProfileArr()[maintProf].maintenanceProfileName.trim() == maintProfile.trim()){
					return self.maintProfileArr()[maintProf].maintenanceProfileId;
				}
			}
			return null;
		}*/

		function successCallback(data, reqType) {
			if(reqType === "getApplicationType"){
				self.typeArr.removeAll();
				//self.typeArr.push({'masterId': '','name': "Select Type"});

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					data.result = getMasterList(data.result, "masterId", [self.selectedConfigRows()[0].applicationTypeId], true);
				}
				else{
					data.result = getMasterList(data.result, "masterId", null, false);
				}

				self.typeArr.push.apply(self.typeArr,data.result);

				$("#appTypeList").trigger('chosen:updated');
				var addedAppTypeArr = $.grep(self.typeArr(), function(evt){ return evt.name == self.appType(); });
				if(addedAppTypeArr.length){
					$("#appTypeList").val(addedAppTypeArr[0].masterId).trigger('chosen:updated');
				}
				self.appType("");
			}
			else if(reqType === "getMaintenanceProfile"){
				self.maintProfileArr(data.result);
				/*for(var maintProfile in self.maintProfileArr()){
					$("#maintProfileList_chosen span").append(listImgEle);
				}*/

				//$('#maintProfileList').trigger('chosen:updated');

				maintProfileLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "getApplicationTag"){
				console.log("------------------------------Application tags---------------");

				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);
				}

				console.log(self.configTagAutoCompleteArr());

				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#application-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#application-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});

				$('#application-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#application-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "addSingleApp"){
				var res = data.result;
				if(data.responseStatus === uiConstants.common.CONST_FAILURE){
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_APPLICATION,res[0].errorCode,res[0].message), "error");
					}
					else{
						showMessageBox(uiConstants.applicationConfig.ERROR_ADD_APPLICATION, "error");
					}
				}
				else{
					if(self.mode != "wizard"){
						showMessageBox(uiConstants.applicationConfig.SUCCESS_ADD_APPLICATION);		
						self.cancelApplication();
						params.curPage(1);
					}
				}

				self.addedAppData(data.result);

				uicommon.postbox.publish(self.addedAppData(),"addedApplicationData");
			}
			else if(reqType === "editSingleApp"){
				var res = data.result;
				if(data.responseStatus === uiConstants.common.CONST_FAILURE){
					if(res != undefined && res[0] != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_APPLICATION,res[0].errorCode,res[0].message), "error");
					}else{
						showMessageBox(uiConstants.applicationConfig.ERROR_UPDATE_APPLICATION, "error");
					}
				}
				else{
					if(self.mode != "wizard")showMessageBox(uiConstants.applicationConfig.SUCCESS_UPDATE_APPLICATION);
					if(self.mode != "wizard")self.cancelApplication();
					if(self.mode != "wizard")params.curPage(1);
				}
				console.log("edit application result");
				self.addedAppData(data.result);
				console.log(self.addedAppData());
				uicommon.postbox.publish(self.addedAppData(),"editedApplicationData");
			}
			else if(reqType === "editMultipleApp"){
				var res = data.result;
				if(data.responseStatus === uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(res);
					if(res != undefined && res[0] != undefined)
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_APPLICATION,res[0].errorCode,res[0].message), "error");
					else
						showMessageBox(uiConstants.applicationConfig.ERROR_MULTIPLE_UPDATE_APPLICATION, "error");
				}
				else{
					showMessageBox(uiConstants.applicationConfig.SUCCESS_MULTIPLE_UPDATE_APPLICATION);
					self.cancelApplication();
					params.curPage(1);
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getApplicationType"){
				self.appType("");
				showMessageBox(uiConstants.applicationType.ERROR_GET_APPLICATION_TYPES, "error");
			}
			else if(reqType === "getTimezone"){
				showMessageBox(uiConstants.common.ERROR_GET_TIMEZONES, "error");
			}
			else if(reqType === "getMaintenanceProfile"){						
				showMessageBox(uiConstants.common.ERROR_GET_MAINTENANCE_PROFILES, "error");
			}
			else if(reqType === "getApplicationTag"){
				showMessageBox(uiConstants.common.ERROR_GET_APPLICATION_TAGS, "error");
			}
			else if(reqType === "addSingleApp"){
				showMessageBox(uiConstants.applicationConfig.ERROR_ADD_APPLICATION, "error");
			}
			else if(reqType === "editSingleApp"){
				showMessageBox(uiConstants.applicationConfig.ERROR_UPDATE_APPLICATION, "error");
			}
			else if(reqType === "editMultipleApp"){
				showMessageBox(uiConstants.applicationConfig.ERROR_MULTIPLE_UPDATE_APPLICATION, "error");
			}
		}
	}

	ApplicationAddEdit.prototype.dispose = function() { };
	return { viewModel: ApplicationAddEdit, template: templateMarkup };
});
