<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divAppAddEdit">
	<div id="panelHeader" class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>

	<div class="panel-body">

<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
	<div id="divAppName" class="form-group form-required">
		<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
		<div class="col-sm-4">
			<input type="text" class="form-control" id="txtName" data-bind="value: appName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
		</div>
	</div>

	<div id="divAppDescription" class="form-group form-required">
		<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
		<div class="col-sm-4">
			<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: appDescription" placeholder="Enter Description" style="resize: none"></textarea>
		</div>
	</div>

	<div id="divAppType" class="form-group">
		<label class="control-label col-sm-2">Type <span class="mandatoryField">*</span></label>
		<div class="col-sm-4">
			<!-- <select class="form-control" id="appTypeList" data-bind="foreach : typeArr" required="true">
				<option style="background-color: white" data-bind="text: name,value : " ></option>
			</select> -->

	<!-- <select class="form-control" id="appTypeList" data-bind="options: typeArr(),  	optionsText: 'name', optionsValue: 'masterId', optionsCaption: 'Select'">
			</select> -->

			<!-- <select class="chosen form-control" id="appTypeList" data-bind="foreach : typeArr" data-placeholder="Select"> -->
			<select class="chosen form-control" id="appTypeList" data-bind="foreach : typeArr" data-placeholder="Select">
				<!-- to show placeholder, remove text form first option -->
				
				<!-- ko if: $index() == 0 -->
					<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
				<!-- /ko-->

				<option data-bind="value: $data.masterId, text: $data.name"></option>
			</select>
			

		</div>

		<!-- ko if: currentViewIndex() == 1 ||  currentViewIndex() == 4 || currentViewIndex() == 7 -->
			<button type="button" id="modalAppType" class="glyphicon glyphicon-plus modal-invoker-btn" data-toggle="modal" data-target="#idModal" title="Add Application Type"></button>
		<!-- /ko-->
	</div>

	

	<div class="form-group">
		<label class="control-label col-sm-2">Time Zone </label>
		<div class="col-sm-4">
			<!-- <input type="text" class="form-control" list="timezoneList" id="timezoneId" autocomplete="off">
			<datalist id="timezoneList" data-bind="foreach : timezoneArr">
				<option data-bind="name: timeZoneId, value: timeZoneName"></option>
			</datalist> -->
			<select class="chosen form-control" id="timezoneList" data-bind="foreach : timezoneArr" data-placeholder=" ">
				<!-- ko if: $index() == 0 -->
					<option data-bind="text: 'Select'"></option>
				<!-- /ko-->
				<option data-bind="value: timeZoneId, text: timeZoneName"></option>				
			</select>
		</div>
	</div>

	<div class="form-group">
		<label class="control-label col-sm-2">Tags</label>
		<div class="col-sm-4"">
			<input type="text" class="form-control tokenfield" id="application-tokenfield-typeahead" data-bind="value: tags">
		</div>
	</div>

	<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4 && currentViewIndex() != 7">
		<label class="control-label col-sm-2" >Immediate Maintenance </label>
		<div class="col-sm-4">
			<input type="checkbox" id="chkImediateMaintainence" data-on-color="success" data-off-color="danger" data-size="mini" name="chkImediateMaintainence">
		</div>
	</div>

	<!-- <div class="form-group">
		<label class="control-label col-sm-2" >Maintenance Profile </label>
	   <div class="col-sm-4">
			<select class="chosen form-control" id="maintProfileList" data-bind="event: {'chosen:showing_dropdown': onMaintProfileListOpen.bind($data)}, foreach : maintProfileArr" data-placeholder=" ">
				<option data-bind="value: $data.id, text: $data.name"></option>
			</select>
	   </div>
	</div> -->

	<div id="divAppStatus" class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4 && currentViewIndex() != 7">
		<label class="control-label col-sm-2" >Status</label>
		<div class="col-sm-4">
			<!-- <input type="checkbox" id="appStatus" data-bind="checked: appStatus"> -->
			<input type="checkbox" id="appStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="appStatus">
		</div>
	</div>

	<div class="form-group">
		<div class="col-sm-offset-2 col-sm-4 divActionPanel">
			<button id="appConfigSaveBtn" type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != 5 && currentViewIndex() != 7 && currentViewIndex() != 8, event:{click: addEditApplication}">Save</button>
			<button type="button" class="btn" data-bind="visible : currentViewIndex() != 7 && currentViewIndex() != 8,event:{click: cancelApplication}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
		</div>
	</div>
	</div>
</form>
</div>
</div>

<div class="modal fade" id="idModal" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
			<div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                	<h4><span data-bind="text: modalTitle"></span></h4>
            </div>
            <div data-bind="if : displayComponent">
           		<app-type-add-edit params="{isModal:true, appType: appType, currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, pageSelected: pageSelected}"></app-type-add-edit>
        	</div>
        </div>
     </div> 
</div> 