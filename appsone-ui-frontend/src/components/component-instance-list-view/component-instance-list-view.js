define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./component-instance-list-view.html','hasher','validator','ui-constants','ui-common','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {

	function ComponentInstanceListGridView(params) {
		var self = this;
		var configTableHeaders = ["Name","Type","Component","Version","Application","Cluster","Created Time","Modified Time","Modified By","Tags","Status"];
		var listData = {};
		var filterForFirstPage = false;
		var fConfigName=null;
		var fConfigActiveInactive="1";
		var fTags=null;
		var fComponentTypeId=0;
		var fComponentId=0;
		var fClusterId=0;
		var fApplicationId=0;
		var fComponentVersionId=0;
		var fCreatedTime="";
		var fUpdatedTime="";
		var fUpdatedBy="";
		var colSortOrder = 0;
		var colToSort = "componentInstanceName";
		var filterTxtAfterDelete;

		this.gridHeader = ko.observableArray();
		this.filterGridHeader = ko.observableArray(["Select","Name","Type","Component","Version","Application","Cluster","Created Time","Modified Time","Modified By","Tags","Status"]);
		this.gridData = ko.observableArray();
		this.configTableHeaderObjKeys = ko.observableArray(["componentInstanceName","componentType","componentName","componentVersion","applications","clusters","createdTime","updatedTime","updatedBy","tags","status"]);
		this.noSortColKeys = ko.observableArray(["applications", "clusters", "tags"]);
		this.currentPage = ko.observable(0);
		this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
		this.totalRecordsPerPage = ko.observable(this.numOfPagesOption()[0]);
		this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
		this.enableAdd = ko.observable(true);
		this.enableEdit = ko.observable(false);
		this.enableClone = ko.observable(false);
		this.currentViewIndex = ko.observable(uiConstants.common.LIST_VIEW);
		this.selectedConfigRows = ko.observableArray();
		this.gridHeader(configTableHeaders);
		this.errorMsg = ko.observable("");
		this.isFilterOrList = ko.observable("list");
		this.recordsCountLabel = ko.observable("");
		this.pageSelected = ko.observable("Component Instance Configuration");
		//this.configType = ko.observable("");
		this.componentsArr = ko.observableArray();
		this.applicationsArr = ko.observableArray();
		this.componentNamesArr = ko.observableArray();
		this.clusterNamesArr = ko.observableArray();
		this.componentVersionsArr = ko.observableArray();
		this.tempComponentsArr = ko.observable();
		this.enableFilter = ko.observable(true);
		this.showListAvailable = ko.observable("");
		this.modifiedCols = ko.observableArray([true,true,true]);
		this.selFilterCategory = ko.observable();
		this.filtersAdded = ko.observable("");
		this.filterValueArr = ko.observableArray([]);

		this.renderHandler=function(){
			/*$(".wrapper").scroll(function(){
				var translate = "translate(0,"+this.scrollTop+"px)";
				this.querySelector("thead").style.transform = translate;
				this.querySelector("#filterRow").style.transform = translate;
            });*/

			$(window).resize(function(){
			    self.refreshPageLayout();
			});

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$(".addOptionsContainer").offset({ top: $("#divAddOptionsList").position().top + $("#divAddOptionsList").outerHeight() });

			$('.columnsList').checklistbox({
			    data: [
			    	{"name": "Created Time", "id": 1},
			    	{"name": "Modified Time", "id": 2},
			    	{"name": "Modified By", "id": 3}
			    ]
			});
			$('.columnsList .checkList').prop("checked", true);

			$("div").on("click", "#selAllCols", function(e){
				$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
			});

			$("div").on("change", ".columnsList .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
				}
	        });

			//$("#fActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fActiveInactive_chosen").trigger('chosen:updated');

			localStorage.currentViewIndex = uiConstants.common.LIST_VIEW;
			configType = localStorage.configType = "compInstConfig";
			
		    $("#fCompTypeList").on('change', function () {
		    	setCompNames();
		    	if(!$("#fCompTypeList option:selected").text().toLowerCase().startsWith("select")){
			    	self.onFilterAdd(null, ['Component:', 'Version:']);
			    	self.onFilterAdd('Type');
		    	}
			});

			$("#fCompNamesList").on('change', function () {
		    	setCompVersions();
		    	if(!$("#fCompNamesList option:selected").text().toLowerCase().startsWith("select")){
			    	self.onFilterAdd(null, ['Version:']);
			    	self.onFilterAdd('Component');
			    }
		    	//setClusters();
			});

			$("#fCompVersionsList").on('change', function () {
		    	if(!$("#fCompVersionsList option:selected").text().toLowerCase().startsWith("select")){
			    	self.onFilterAdd('Version');
			    }
			});

			$("#fApplicationsList").on('change', function () {
		    	//setClusters();
			});

			$("#fCompTypeList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fCompNamesList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fCompVersionsList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fClusterNamesList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$("#fApplicationsList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
			$('#fCompNamesList').prop('disabled', true).trigger("chosen:updated");
			$('#fCompVersionsList').prop('disabled', true).trigger("chosen:updated");
			//$('#fClusterNamesList').prop('disabled', true).trigger("chosen:updated");
			
			requestCall(uiConstants.common.SERVER_IP + "/component?status=2&markInactive=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/574294d30f00004812a576a8?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getApplications", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/57304295120000ee253cc845", "GET", "", "getApplications", successCallback, errorCallback);
			

			//DS has to change to give list of all the clusters irrespective off component ID & application ID.
			requestCall(uiConstants.common.SERVER_IP + "/cluster?sortBy=clusterName&orderBy=asc", "GET", "", "getClusters", successCallback, errorCallback);

			$('#fConfigName').keypress(function(event) {
    			if (event.which == 13) {
					self.getFilterListData(true);
				}
			});

			

			/*$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/
			$("#fActiveInactive").val("1").trigger('chosen:updated');

			self.curPage(1);
		}

		self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

		this.onHeaderClick = function(columnNum, columnName){
			if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
				$(".listViewCol").not(".noSort").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

				colSortOrder = colSortOrder ? 0 : 1;
				colToSort = columnName;
				//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
				$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

				self.getListData();
			}
		}

		this.msgShowListData=function(){
			if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
				self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
			}
			else{
				self.showListAvailable(uiConstants.componentInstanceConfig.COMPINST_LISTS_NOT_CONFIGURED);
			}
		}

		this.toggleAddOptions = function(){
			$(".addOptionsContainer").toggle();
			document.addEventListener('click', window.outsideClickListener);
		}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.convertToLocalTime = function(getDateTime){
			return window.gmtToLocalDateTime(getDateTime);
		}

		this.totalPages = ko.computed(function() {
			return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
		}, this);

		this.recordsCountLabel = ko.computed(function() {
			var recordsStartCount = 0;
			var recordsEndCount = 0;

			if(self.currentPage() != 0){
				recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
				recordsEndCount = recordsStartCount + (self.gridData().length - 1);
			}
			return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
		}, this);

		this.enableDisableAdd = function(length){
			self.enableAdd(length>0?false:true)
		}

		this.enableDisableUpdate = function(length){
			if(length > 0)
				self.enableEdit(true);
			else
				self.enableEdit(false);
		};

		this.enableDisableClone = function(length){
			if(length == 1)
				self.enableClone(true);
			else
				self.enableClone(false);
		};

		this.getListOrFilterData = function(){
			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.prevPage = function(){
			if(self.currentPage()>1)
				self.currentPage(self.currentPage()-1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}
	
		this.nextPage = function(){
			if(self.currentPage()<self.totalPages())
				self.currentPage(self.currentPage()+1);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.curPage = function(curPage){
			resetButtonStates();
			self.currentPage(curPage);

			if(self.isFilterOrList() == "list")
				self.getListData();
			else{
				self.getFilterListData(false);
			}
		}

		this.showFilterBox = function(){
			if(!self.filterValueArr().length){
	            for(var headerTxt in self.gridHeader()){
	            	self.filterValueArr.splice(headerTxt, 0, "");
	            }
			}
			$("#filterCriteria").trigger("chosen:updated");
			$("#filterCriteria_chosen").addClass("filterFieldWidth");
			$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
			$("#filterBox").css("display", "block");

			self.onFilterCatChange();

			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.onFilterCatChange = function(){
			if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
				$('#filterCreateModTime').datetimepicker({
					format: "YYYY-MM-DD HH:00",          
					stepping: 1,
					useCurrent: true, 
					//defaultDate: null,
					showTodayButton: false,          
					collapse: true,
					sideBySide: false
				})
				.on('dp.show', function(e){
					if($('#filterCreateModTime input').val() == ""){
						$(this).data("DateTimePicker").date(moment());
					}
				})
				.on('dp.change', function(e){
					if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
						$(this).data("DateTimePicker").maxDate(moment());
						var maxHour = parseInt(moment().format("HH"));
						//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
						//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
					}
					else{
						$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
						//$(this).data("DateTimePicker").disabledHours([]);
					}

					self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();
				})
				.on('dp.hide', function(e){
					self.onFilterAdd();
				});
				$('#filterCreateModTime input').val("");
			}
		}

		this.onFilterAdd = function(parentCategory, removeSubFilterArr){
			if(removeSubFilterArr && removeSubFilterArr.length){
				var filtersAddedArr = self.filtersAdded().split("|");
				var filterFormatted = "";
				for(var i in filtersAddedArr){
					if(removeSubFilterArr.indexOf(filtersAddedArr[i].trim().split(":")[0] + ":") == -1){
						filterFormatted = filterFormatted + "|" + filtersAddedArr[i].trim();
					}
				}

				self.filtersAdded(filterFormatted.substr(1));
			}
			else{
				var filterText = self.getFiltersToAdd(parentCategory || self.selFilterCategory());

				if(!filterText.toLowerCase().startsWith("select")){
					var filterIndex = self.filtersAdded().indexOf((parentCategory || self.selFilterCategory()) + ":");
					var filterExisting = "";
					if(filterIndex != -1){
						var filterExistingEndIndex = self.filtersAdded().indexOf("|", filterIndex);
						if(filterExistingEndIndex != -1){
							filterExisting = self.filtersAdded().substr(filterIndex, (filterExistingEndIndex-filterIndex));
						}
						else{
							filterExisting = self.filtersAdded().substr(filterIndex);
						}

						self.filtersAdded(self.filtersAdded().replace(filterExisting, (parentCategory || self.selFilterCategory()) + ":" + filterText));
					}
					else{
						self.filtersAdded((self.filtersAdded() ? (self.filtersAdded() + "|") : "") + (parentCategory || self.selFilterCategory()) + ":" + filterText);
					}
				}
			}

			/*var filterCat = self.selFilterCategory();

			if(filterCat == "Component"){
				filterCat = "Type";
			}

			var filterPartIndexFrom = self.filtersAdded().indexOf("|"+filterCat+":");

		    if(filterPartIndexFrom == -1){
				filterPartIndexFrom = self.filtersAdded().indexOf("| "+filterCat+":");
		    }

		    if(filterPartIndexFrom == -1){
				filterPartIndexFrom = self.filtersAdded().indexOf(filterCat+":");

				if(filterPartIndexFrom != 0){
					filterPartIndexFrom = -1;
				}
		    }

		    var filterPartIndexTo = self.filtersAdded().indexOf("|", filterPartIndexFrom+1);
		    var filterExtended = false;
		    var componentName = "";

		    if(self.selFilterCategory() == "Component"){
		    	var filterPartTwoIndexFrom = self.filtersAdded().indexOf("|"+self.selFilterCategory()+":");

			    if(filterPartTwoIndexFrom == -1){
			    	filterPartTwoIndexFrom = self.filtersAdded().indexOf("| "+self.selFilterCategory()+":");
			    }

			    if(filterPartTwoIndexFrom != -1){
			    	filterPartIndexTo = self.filtersAdded().indexOf("|", filterPartTwoIndexFrom+1);
			    }
			    filterExtended = true;
			}
			if(self.selFilterCategory() == "Type" || slugify(self.getFiltersToAdd("Component")).startsWith("select")){
			    filterExtended = false;
				var filterPartTwoIndexFrom = self.filtersAdded().indexOf("|Component:");

			    if(filterPartTwoIndexFrom == -1){
			    	filterPartTwoIndexFrom = self.filtersAdded().indexOf("| Component:");
			    }

			    if(filterPartTwoIndexFrom == -1){
					filterPartTwoIndexFrom = self.filtersAdded().indexOf("Component:");

					if(filterPartTwoIndexFrom != 0){
						filterPartTwoIndexFrom = -1;
					}
			    }

			    var filterPartTwoIndexTo = -1;

			    if(filterPartTwoIndexFrom != -1){
				    filterPartTwoIndexTo = self.filtersAdded().indexOf("|", filterPartTwoIndexFrom+1);
				    componentName = filterPartTwoIndexTo != -1 ? self.filtersAdded().substring(filterPartTwoIndexFrom+1, filterPartTwoIndexTo).split(":")[1] : self.filtersAdded().substring(filterPartTwoIndexFrom+1).split(":")[1];
				}

				if(filterPartTwoIndexFrom != -1){
					if($.grep(self.componentNamesArr(), function(e){ return e.componentName == componentName; }).length == 0){
					    self.filtersAdded(self.filtersAdded().substring(0,filterPartTwoIndexFrom) + (filterPartTwoIndexTo !=-1 ? self.filtersAdded().substring(filterPartTwoIndexTo) : ""));
					}
				}
			}

		    if(filterPartIndexFrom == 0){
			    if(filterExtended){

			    	self.filtersAdded(filterCat + ":" + self.getFiltersToAdd(filterCat) + "|" + self.selFilterCategory() + ":" + self.getFiltersToAdd(self.selFilterCategory()) + (filterPartIndexTo !=-1 ? self.filtersAdded().substring(filterPartIndexTo) : ""));
			    }
			    else{
			    	self.filtersAdded(filterCat + ":" + self.getFiltersToAdd(filterCat) + (filterPartIndexTo !=-1 ? self.filtersAdded().substring(filterPartIndexTo) : ""));
			    }
			}
			else if(filterPartIndexFrom == -1){
				if(filterExtended){
					self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCat + ":" + self.getFiltersToAdd(filterCat) + "|" + self.selFilterCategory() + ":" + self.getFiltersToAdd(self.selFilterCategory()));
			    }
			    else{
					self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCat + ":" + self.getFiltersToAdd(filterCat));
				}
			}
			else{
				if(filterExtended){
				    self.filtersAdded(self.filtersAdded().substring(0,filterPartIndexFrom) + "|" + filterCat + ":" + self.getFiltersToAdd(filterCat) + "|" + self.selFilterCategory() + ":" + self.getFiltersToAdd(self.selFilterCategory()) + (filterPartIndexTo !=-1 ? self.filtersAdded().substring(filterPartIndexTo) : ""));
				}
				else{
				    self.filtersAdded(self.filtersAdded().substring(0,filterPartIndexFrom) + "|" + filterCat + ":" + self.getFiltersToAdd(filterCat) + (filterPartIndexTo !=-1 ? self.filtersAdded().substring(filterPartIndexTo) : ""));
				}
			}*/

			$('#filters-tokenfield-typeahead').tokenfield({
				delimiter: ['|']
			});

			var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fConfigName=null;
						fConfigActiveInactive="1";
						fTags=null;
						fComponentTypeId=0;
						fComponentId=0;
						fClusterId=0;
						fApplicationId=0;
						fComponentVersionId=0;
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

		this.getFiltersToAdd = function(category){
			if(category == "Type"){
				return $("#fCompTypeList option:selected").text();
			}
			else if(category == "Component"){
				return $("#fCompNamesList option:selected").text();
			}
			else if(category == "Version"){
				return $("#fCompVersionsList option:selected").text();
			}
			else if(category == "Application"){
				return $("#fApplicationsList option:selected").text();
			}
			else if(category == "Cluster"){
				return $("#fClusterNamesList option:selected").text();
			}
			else if(category == "Status"){
				//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
				return $("#fActiveInactive option:selected").text();
			}
			else{
				return self.filterValueArr()[self.gridHeader.indexOf(category)];
			}
		}

		this.resetFilter = function(){
			showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
				if(confirm){
					self.resetFilterConfirmed();
				}
			});
		}

		this.resetFilterConfirmed = function(){
			self.filtersAdded("");
			for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }

			$('#fCompTypeList').val("0").trigger('chosen:updated');
			self.componentNamesArr({});
			$('#fCompNamesList').trigger('chosen:updated');
			self.componentVersionsArr({});
			$('#fApplicationsList').val("0").trigger('chosen:updated');
			self.clusterNamesArr({});
			$('#fClusterNamesList').trigger('chosen:updated');
			$("#fActiveInactive").val("1").trigger('chosen:updated');
			self.errorMsg("");
			self.currentPage(1);
			self.isFilterOrList("list");

			fConfigName=null;
			fConfigActiveInactive="1";
			fTags=null;
			fComponentTypeId=0;
			fComponentId=0;
			fClusterId=0;
			fApplicationId=0;
			fComponentVersionId=0;
			fCreatedTime="";
			fUpdatedTime="";
			fUpdatedBy="";

			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
			self.selFilterCategory("Select");

			requestCall(uiConstants.common.SERVER_IP + "/componentInstance?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&componentInstanceName=" +null+"&status="+fConfigActiveInactive+"&tagNames="+fTags+"&componentTypeId="+fComponentTypeId+"&componentId="+fComponentId+"&applicationId="+fApplicationId+"&componentVersionId="+fComponentVersionId+"&clusterId="+fClusterId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
		}

		/*this.getClusterNames = function(clustersData){
			var clusterNames = "";
			for(cluster in clustersData){
				clusterNames += ", " + clustersData[cluster].clusterName;
			}

			return clusterNames.substring(2);
		}*/

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += "," + tagsData[tag].tagName;
			}

			return tagNames.substring(1);
		}

		this.getApplicationNames = function(applicationsData){
			var applicationNames = "";
			for(app in applicationsData){
				applicationNames += "," + applicationsData[app].applicationName;
			}

			return applicationNames.substring(1);
		}

		this.getClusterNames = function(clustersData){
			var clusterNames = "";
			for(cluster in clustersData){
				clusterNames += ", " + clustersData[cluster].clusterName;
			}

			return clusterNames.substring(2);
		}

		this.getTagNames = function(tagsData){
			var tagNames = "";
			for(tag in tagsData){
				tagNames += ", " + tagsData[tag].tagName;
			}

			return tagNames.substring(2);
		}

		self.errorMsg.subscribe(function(errorMessage) {
        	if(errorMessage != ""){
	        	scrollToPos(0, 300);
	        }
		});

		this.getFilterListData = function(filterApplied){
			self.isFilterOrList("filter");
			filterForFirstPage = filterApplied;
			setFiltersToVariables();
			var resValue = true;
			var pageOffset = self.currentPage();

			this.errorMsg("");

			/*if($("#fConfigName").val() != "" && $("#fConfigName").val().length < 2){
				self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MIN_LENGTH_ERROR);
				resValue = false;
			}
			else if($("#fConfigName").val() != "" &&  $("#fConfigName").val().length > 45){
				self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_MAX_LENGTH_ERROR);
				resValue = false;
			}*/
			/*else if(fConfigName != "" && fConfigName != null){
				resValue=nameValidation(fConfigName);
				if(resValue == 0){
					self.errorMsg(uiConstants.componentInstanceConfig.COMP_INSTANCE_NAME_INVALID_ERROR);
					
					resValue = false;
				}
			}*/

			resetPagination();
	
			if(resValue){
				if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterApplied);
				if(filterApplied){
					pageOffset = 1;
				}

				$("#btnApplyFilter").css("display", "none");
				$("#filterOptionsBox").css("display", "none");
				$("#filterOptionsDispBtn").css("display", "");
				requestCall(uiConstants.common.SERVER_IP + "/componentInstance?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ pageOffset +"&componentInstanceName=" +fConfigName+"&status="+fConfigActiveInactive+"&tagNames="+fTags+"&componentTypeId="+fComponentTypeId+"&componentId="+fComponentId+"&applicationId="+fApplicationId+"&componentVersionId="+fComponentVersionId+"&clusterId="+fClusterId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
			}
		}

		this.onFilterOptionsDispClick = function(){
			$("#filterOptionsBox").css("display", "");
			$("#btnApplyFilter").css("display", "");
			$("#filterOptionsDispBtn").css("display", "none");
			
			self.refreshPageLayout();
		}

		this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

		this.getListData = function(){
			self.isFilterOrList("list");
			resetPagination();
			if(window.globalSearchTxt){
				fConfigName = window.globalSearchTxt;
			}
			$('#fConfigName').val(fConfigName);
			//window.globalSearchTxt = "";
			requestCall(uiConstants.common.SERVER_IP + "/componentInstance?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&componentInstanceName=" +fConfigName+"&status="+fConfigActiveInactive+"&tagNames="+fTags+"&componentTypeId="+fComponentTypeId+"&componentId="+fComponentId+"&applicationId="+fApplicationId+"&componentVersionId="+fComponentVersionId+"&clusterId="+fClusterId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
			//requestCall("http://www.mocky.io/v2/5784c1b4100000dc3167702f?callback=?", "GET", "", "getListData", successCallback, errorCallback);
		}

		this.switchView = function (viewIndex){
			localStorage.currentViewIndex = viewIndex;
			self.currentViewIndex(viewIndex);

			if(self.currentViewIndex() == uiConstants.common.LIST_VIEW){
				self.pageSelected("Component Instance Configuration");
			}

			else if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				self.pageSelected("Add Component Instance");
			}

			else if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
				if(self.selectedConfigRows().length>1){
					self.pageSelected("Edit Component Instances");
				}
				else{
					self.pageSelected("Edit Component Instance");
				}
			}

			else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.pageSelected("Clone Component Instance");
			}

			else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				self.pageSelected("Component Instance");
			}

			else if(self.currentViewIndex() == uiConstants.common.ADD_MULTIPLE_VIEW){
				self.pageSelected("Add Component Instances");
			}
		}

		this.editConfig = function(){
			getSelectedConfigRows(null);

			var compInstanceVersionObj = $.grep(self.selectedConfigRows(), function(e){ return e.componentVersionId != self.selectedConfigRows()[0].componentVersionId; });
			
			if(uiConstants.common.DEBUG_MODE)console.log(compInstanceVersionObj);
			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());

			if(compInstanceVersionObj.length == 0){
				self.switchView(uiConstants.common.EDIT_VIEW);
			}
			else{
				showMessageBox(uiConstants.componentInstanceConfig.ERROR_DIFFERENT_VERSIONS_MULTI_EDIT, "error");
			}
		}

		this.cloneConfig = function(){
			getSelectedConfigRows(null);
			if(self.selectedConfigRows()[0].status == 0){
				showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "Component Instance"), "error");
			}
			else{
				self.switchView(uiConstants.common.CLONE_VIEW);
			}
		}

		this.viewConfig = function(viewObj){
			getSelectedConfigRows(viewObj);
			self.switchView(uiConstants.common.READ_VIEW);
		}

		this.addMultiple = function(){
			getSelectedConfigRows(null);
			self.switchView(uiConstants.common.ADD_MULTIPLE_VIEW);
		}

		function setCompNames(){
			if($("#fCompTypeList").val() == 0){
				self.componentNamesArr({});
				self.componentVersionsArr({});
				//self.clusterNamesArr({});

				$('#fCompNamesList').prop('disabled', true).trigger("chosen:updated");
				$('#fCompVersionsList').prop('disabled', true).trigger("chosen:updated");
				//$('#fClusterNamesList').prop('disabled', true).trigger("chosen:updated");
			}
			else{
				var componentsObj = $.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#fCompTypeList").val(); });

				if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
				self.componentNamesArr(componentsObj[0].components);
				$('#fCompNamesList').prop('disabled', false).trigger("chosen:updated");
			}
		}

		function setCompVersions(){
			if($("#fCompNamesList").val() == 0){
				self.componentVersionsArr({});
				$('#fCompVersionsList').prop('disabled', true).trigger("chosen:updated");

			}
			else{
				var componentsObj = $.grep(self.componentNamesArr(), function(e){ return e.componentId == $("#fCompNamesList").val(); });

				if(uiConstants.common.DEBUG_MODE)console.log(componentsObj[0]);
				self.componentVersionsArr(componentsObj[0].versions);
				$('#fCompVersionsList').prop('disabled', false).trigger("chosen:updated");
			}
		}

		function getSelectedConfigRows(viewObj){
			self.selectedConfigRows([]);

			if(viewObj != null){
				if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
				self.selectedConfigRows.push(viewObj);
			}
			else{
				for(objData in self.gridData()){
					if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(self.gridData()[objData]));

					if(self.gridData()[objData].isSelected){
						self.selectedConfigRows.push(self.gridData()[objData]);
					}
				}			
			}

			localStorage.selectedConfigRows = JSON.stringify(self.selectedConfigRows());
		}

		$('#listConfigDetailsPage table').on('click', '.chkboxCol', function(e){
			self.handleChkClick();
		});



	    $('#listgrid tbody').on('dblclick', 'tr', function(e){
	    	if(e.target.parentNode.rowIndex != undefined)
	    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
		});

		self.onNameClick = function(){
			self.viewConfig($(this)[0]);
		}

		self.handleChkClick = function(){
			var length = $('.chkboxCol:checked').length;
			self.enableDisableAdd(length);
			self.enableDisableUpdate(length);
			self.enableDisableClone(length);
			if (length == self.gridData().length) {
				$("#chkboxHeader").prop("checked",true);
			}
			else {
				$("#chkboxHeader").prop("checked",false);
			}
		}

	    function resetButtonStates(){
			self.enableDisableAdd(0);
			self.enableDisableUpdate(0);
			self.enableDisableClone(0);
		}

		function setFiltersToVariables(){
			var filtersArr = self.filtersAdded().split("|");
			var filterCategoryArr = [];

			for(var filters in filtersArr){
				filterCategoryArr = filtersArr[filters].trim().split(":");

				if(filterCategoryArr[0] == "Name"){
					fConfigName = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Type"){
					fComponentTypeId = $.grep(self.componentsArr(), function(e){
						return e.componentType == filterCategoryArr[1];
					})[0].componentTypeId;
				}
				else if(filterCategoryArr[0] == "Component"){
					fComponentId = $.grep(self.componentNamesArr(), function(e){
						return e.componentName == filterCategoryArr[1];
					})[0].componentId;
				}
				else if(filterCategoryArr[0] == "Version"){
					fComponentVersionId = $.grep(self.componentVersionsArr(), function(e){
						return e.version == filterCategoryArr[1];
					})[0].versionId;
				}
				else if(filterCategoryArr[0] == "Application"){
					fApplicationId = $.grep(self.applicationsArr(), function(e){
						return e.applicationName == filterCategoryArr[1];
					})[0].applicationId;
				}
				else if(filterCategoryArr[0] == "Created Time"){
					fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
				}
				else if(filterCategoryArr[0] == "Modified Time"){
					fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
				}
				else if(filterCategoryArr[0] == "Modified By"){
					fUpdatedBy = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Tags"){
					fTags = filterCategoryArr[1];
				}
				else if(filterCategoryArr[0] == "Status"){
					fConfigActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
				}
			}
		}

		function resetPagination(){
			if(self.totalPages() != 0){
				if(typeof self.currentPage() == "string"){
					self.currentPage(parseInt(self.currentPage()));
				}

				if(self.currentPage() == "" || isNaN(self.currentPage()))
					self.currentPage(1);
				else if(self.currentPage()>self.totalPages())
					self.currentPage(self.totalPages());
				else if(self.currentPage()<1)
					self.currentPage(1);
			}
		}

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);

			if(reqType === "getListData"){
				$("#chkboxHeader").prop("checked",false);
				resetButtonStates();

				if(data.responseStatus == "success"){
					listData = data;
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					self.totalRecords(listData.totalRecords);
					
					//self.gridData(listData.result);

					$("#listgrid #gridDataBody").empty();
			 		$("#listgrid").trigger("update");
					self.gridData(listData.result);

					var initialSortColumn = 1;
					var sortOrder = 0; //0=asc; 1=desc
					if(!$("#listgrid").hasClass("tablesorter")){
						if(!self.gridData().length){
							self.enableFilter(false);
						}
						else{

							if (!$("#pageNum").hasClass("select2-hidden-accessible")){
								debugger;
								$("#pageNum").select2();

								$("#pageNum").select2("open");
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
								$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
								$("#select2-pageNum-container").parent().css({
									"border": "none",
									"outline": "none"
								});

								$("#pageNum").parent().children("span").css("width", "36px");
								//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
								$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
								$("#select2-pageNum-container").css({
										"font-weight": "bold",
										"color": "#218DC0",
										"padding-left": "4px"
									});

								$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
								$(".pageNumDropDown .select2-search").css("display", "none");
								$("#select2-pageNum-results").css("overflow-x", "hidden");
								$("#pageNum").select2("close");
							}
						}

						$("#listgrid").addClass("tablesorter")
						/*$("#listgrid").tablesorter({
							//ignoreCase : false,
							cancelSelection: false,
							headers: { 0: { sorter: false}, 5: { sorter: false}, 6: { sorter: false}, 10: { sorter: false} },

							widgetOptions: {
						      sortTbody_primaryRow : '.main',
						      sortTbody_sortRows   : false,
						      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
						    }
							//sortList: [[initialSortColumn, sortOrder]]
						});

						$("#listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

			            var $tab = $('#listgrid');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper');
							}
						});

						$('#listConfigDetailsPage table').on('click', '#chkboxHeader', function(e){
							if (this.checked == false) {
								$(".chkboxCol").prop("checked",false);
								for(objData in self.gridData()){
									self.gridData()[objData].isSelected=false;
								}
							}
							else {
								$(".chkboxCol").prop("checked",true);
								
								for(objData in self.gridData()){
									self.gridData()[objData].isSelected=true;
								}
							}
							var length = $('.chkboxCol:checked').length;
							self.enableDisableAdd(length);
							self.enableDisableUpdate(length);
							self.enableDisableClone(length);
						});
					}
					/*else{
						$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
						
					}*/

					/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
				 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
*/

					$("#listgrid").trigger("update");
					self.refreshPageLayout();

					if((self.currentPage() == 0 || filterForFirstPage) && self.gridData().length>0){
						self.currentPage(1);
						filterForFirstPage = false;
					}
					self.msgShowListData();

				}else{
					showMessageBox(data.message, "error");
					self.showListAvailable("");
				}

				var $tab = $('#listgrid');
				$tab.floatThead('reflow');
			}
			else if(reqType === "getCompTypeVersion"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.componentsArr(data.result);

				self.tempComponentsArr(data.result);
				$("#fCompTypeList").trigger('chosen:updated');

				self.componentNamesArr.push({});
				self.componentVersionsArr.push({});
				//self.clusterNamesArr.push({});
				$("#fCompNamesList").trigger('chosen:updated');
				$("#fCompVersionsList").trigger('chosen:updated');
				$("#fClusterNamesList").trigger('chosen:updated');
			}
			else if(reqType === "getClusters"){
				if(data.result.length > 0){
					self.clusterNamesArr(data.result);
				}
				else{
					self.clusterNamesArr({});
				}
				//$('#fClusterNamesList').prop('disabled', false).trigger("chosen:updated");
			}
			else if(reqType === "getApplications"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.applicationsArr(data.result);
				$("#fApplicationsList").trigger('chosen:updated');
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getListData"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_COMP_INSTANCES, "error");
  			}
  			else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}
			else if(reqType === "getClusters"){
				showMessageBox(uiConstants.common.ERROR_GET_CLUSTERS, "error");
			}
  			else if(reqType === "getApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
		}
	}

	ComponentInstanceListGridView.prototype.dispose = function() { };
	return { viewModel: ComponentInstanceListGridView, template: templateMarkup };
});