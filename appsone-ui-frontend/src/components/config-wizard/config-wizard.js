define(['jquery','knockout','jquery-chosen','typeahead', 'text!./config-wizard.html','hasher','validator','ui-constants','ui-common','bootstrap-wizard'], function($,ko,jc,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,bwizard){
function ConfigWizard(params) {
    	var self = this;

    	self.componentInstanceloaded = ko.observable(0);
      this.componentsArr = ko.observableArray();
      this.instanceDetailArr=ko.observableArray();
      this.errorMsg = ko.observable("");
      this.applicationId = ko.observable(0);
      this.addedApplicationId = ko.computed(function(){
        return self.applicationId();
      });
      this.applicationTypeId = ko.observable();
      this.applicationsArr = ko.observableArray();

      //application config related variables
      this.panelTitle = params.panelTitle;
      this.mode = params.mode;
      this.currentViewIndex = params.currentViewIndex;
      this.selectedConfigRows =params.selectedConfigRows;
      this.typeArr = params.typeArr;
      this.timezoneArr = params.timezoneArr;
      this.pageSelected = params.pageSelected;

      this.applicationAddedData=ko.observable();//application global variable
      this.applicationAddDataResult=ko.observable();
      this.componentInstAdddedData=ko.observable();
      this.componentInstAddedDataResult=ko.observable('');


      this.checkToDisplaySkip = ko.observable(false);
      this.toShowAgentWizardPopup = ko.observable(false);
      this.checkToShowSaveContinue = ko.observable(true);
      this.appDetWithTypeId = ko.observable();
      this.saveAndContinueButtonLabel = ko.observable('Save and Continue');
      this.skipButtonLabel = ko.observable('Skip');
      this.applicationsGridData = params.applicationsGridData;

      this.txnListViewModel = ko.observable();
      this.agentListViewModel = ko.observable();
      this.compInstAddDataLoaded = ko.observable(false);
      this.compInstEditDataLoaded = ko.observable(false);

      this.isApplicationAdded = ko.observable(false);
      this.curActiveTab = ko.observable();
      this.isCompInstToLoad = ko.observable(true);
      this.nextTabToShow = ko.observable("");
      this.appCurrentView = ko.observable("Add");
  
      this.onSaveContinueBtnClick =  function(){  
        var activeTabId= $(".nav.nav-tabs li.active").find("a").attr('href');
         switch(activeTabId){
              case '#applicationsTab' :   uicommon.postbox.publish("applicationSaveClickEvent","saveClickEvent");//call function
                                          window.compInstDetailsChaged(false);
                                          window.compInstDetailsClicked(false);
                                          window.txnDetailsChaged(false);
                                          window.txnDetailsClicked(false);
                                          window.agentDetailsChaged(false);
                                          window.agentDetailsClicked(false);
                                          break;
              case '#compInstancesTab' :  uicommon.postbox.publish("componentInstanceDetailsClickEvent","saveComponentInstanceDetailsClickEvent");
                                          window.appDetailsChaged(false);
                                          window.appDetailsClicked(false);
                                          window.txnDetailsChaged(false);
                                          window.txnDetailsClicked(false);
                                          window.agentDetailsChaged(false);
                                          window.agentDetailsClicked(false);
                                          break;
              case '#transactionsTab' :   uicommon.postbox.publish("TransactionAddSaveClickEvent","saveTransactionDetailsClickEvent");
                                          window.compInstDetailsChaged(false);
                                          window.compInstDetailsClicked(false);
                                          window.appDetailsChaged(false);
                                          window.appDetailsClicked(false);
                                          window.agentDetailsChaged(false);
                                          window.agentDetailsClicked(false);
                                          break;
              case '#agentsTab' :         uicommon.postbox.publish("AgentAddDetailsSaveClickEvent","saveAgentDetailsClickEvent");
                                          window.compInstDetailsChaged(false);
                                          window.compInstDetailsClicked(false);
                                          window.appDetailsChaged(false);
                                          window.appDetailsClicked(false);
                                          window.txnDetailsChaged(false);
                                          window.txnDetailsClicked(false);
                                          break;
         }
      };

      // subscribe for application save click
      uicommon.postbox.subscribe(function(value) {
         self.applicationAddedData(value);
      },"addApplication");

      // get application added resultant data
      uicommon.postbox.subscribe(function(value) {
        debugger;
           self.applicationAddDataResult(value);
           if(!self.applicationAddedData()){
               if(self.applicationAddDataResult()!= undefined && self.applicationAddDataResult()[0].responseStatus == uiConstants.common.CONST_FAILURE){
                    showMessageBox(self.applicationAddDataResult()[0].message);
                      $('#rootwizard').bootstrapWizard('show',0);
               }
               else{
                debugger;
                    self.applicationId(self.applicationAddDataResult()[0].result.applicationId);//23,142 :self.applicationAddDataResult()[0].applicationId
                    self.applicationTypeId(self.applicationAddDataResult()[0].result.applicationTypeId);//92,89 :self.applicationAddDataResult()[0].applicationTypeId
                    
                    self.appDetWithTypeId(self.applicationId()+":"+self.applicationTypeId());
                    debugger;
                    //self.selectedConfigRows([{"applicationId":7,"applicationName":"aaaaaaaa","description":"qweqewqewqewq","maintenanceWindowProfile":{},"timezone":{"timeZoneId":26,"timeZoneName":"(GMT) Greenwich Mean Time: Dublin, Edinburgh, Lisbon, London","timeZoneOffset":0},"tags":[{"tagId":10,"tagName":"testapptag1"},{"tagId":11,"tagName":"testapptag2"}],"applicationType":"DotNet","applicationTypeId":6,"databaseTimeStamp":"0","maintenanceFlag":0,"status":1,"selected":false}]);
                    self.selectedConfigRows([self.applicationAddDataResult()[0].result]);

                    if(self.nextTabToShow() == "" || self.nextTabToShow() == "compInstancesTab"){
                      self.masterDataReqCallComponentWizard();
                      console.log("Application Id : "+self.applicationId()+"Application Type Id : "+self.applicationTypeId());
                      
                      self.isApplicationAdded(true);
                      $("#divAppAddEdit #appTypeList").prop('disabled', true).trigger('chosen:updated');
                      $('#rootwizard').bootstrapWizard('show',1);
                      uicommon.postbox.publish( self.applicationTypeId(),"triggerGetServerDetails");
                    }
                    else{
                      $('.nav-tabs a[href=#'+self.nextTabToShow()+']').tab('show') ;
                    }


               }
           }
           else{
              $('#rootwizard').bootstrapWizard('show',0);
           }        
     },"addedApplicationData");

      uicommon.postbox.subscribe(function(value) {
           self.applicationAddDataResult(value);
           if(!self.applicationAddedData()){
               if(self.applicationAddDataResult()!= undefined && self.applicationAddDataResult()[0].responseStatus == uiConstants.common.CONST_FAILURE){
                    showMessageBox(self.applicationAddDataResult()[0].message);
               }
               else{
                    self.applicationId(self.applicationAddDataResult()[0].result.applicationId);//23,142 :self.applicationAddDataResult()[0].applicationId
                    self.applicationTypeId(self.applicationAddDataResult()[0].result.applicationTypeId);//92,89 :self.applicationAddDataResult()[0].applicationTypeId
                    
                    debugger;
                    //self.selectedConfigRows([{"applicationId":7,"applicationName":"aaaaaaaa","description":"qweqewqewqewq","maintenanceWindowProfile":{},"timezone":{"timeZoneId":26,"timeZoneName":"(GMT) Greenwich Mean Time: Dublin, Edinburgh, Lisbon, London","timeZoneOffset":0},"tags":[{"tagId":10,"tagName":"testapptag1"},{"tagId":11,"tagName":"testapptag2"}],"applicationType":"DotNet","applicationTypeId":6,"databaseTimeStamp":"0","maintenanceFlag":0,"status":1,"selected":false}]);
                    self.selectedConfigRows([self.applicationAddDataResult()[0].result]);

                    console.log("Application Id : "+self.applicationId()+"Application Type Id : "+self.applicationTypeId());
                    if(self.nextTabToShow() == "" || self.nextTabToShow() == "compInstancesTab"){
                      self.masterDataReqCallComponentWizard();
                      $('#rootwizard').bootstrapWizard('show',1);
                      var appIdWithTypeId=self.applicationId()+":"+self.applicationTypeId();
                      uicommon.postbox.publish( appIdWithTypeId,"triggerGetServerDetailsForApplication");
                    }
                    else{
                      $('.nav-tabs a[href=#'+self.nextTabToShow()+']').tab('show') ;
                    }
              }
           }   
     },"editedApplicationData");

    uicommon.postbox.subscribe(function(appDetArr) {
      self.applicationId(appDetArr[0]);
      self.applicationTypeId(appDetArr[1]);
      self.masterDataReqCallComponentWizard();

      self.appDetWithTypeId(appDetArr[0]+":"+appDetArr[1]);
    },"appDetWithTypeId");


     // subscribe for component instance multiple save click
     uicommon.postbox.subscribe(function(value){ 
        self.componentInstAdddedData(value);
     },"addComponentInstancesDetails");

     // get component instance added resultant data
     uicommon.postbox.subscribe(function(value){ 
        self.componentInstAddedDataResult(value);
        $('#rootwizard').bootstrapWizard('next');
     },"addedComponentInstanceDetailResultData");


    this.renderHandler=function(){
  		  $('#rootwizard').bootstrapWizard({
  		  		'tabClass': 'nav nav-tabs',
            'nextSelector': '.next',
            'onTabShow':  self.onTabShow,
            'onPrevious':self.onPrevious
  		  });  

        /*Disable Click on Application Tab*/
        $("#rootwizard a[data-toggle='tab']").on('click', function (e) {
            var targetTab = e.currentTarget.hash && e.currentTarget.hash.split('#')[1] || '';
            self.nextTabToShow(targetTab);
            if(targetTab === "applicationsTab"){
              self.nextTabToShow("compInstancesTab");
            }
            if(!self.applicationId()){
              showMessageBox(uiConstants.common.CREATE_APPLICATION_MSG);
              return false;
            }
            else{
              if(window.appDetailsChaged()){
                e.preventDefault();


                showMessageBox(uiConstants.applicationConfig.CONFIRM_SAVE_APP_DETAILS, "question", "confirm", function confirmCallback(confirmAppDetailsSave){
                  if(confirmAppDetailsSave){
                    setTimeout(function(){ self.onSaveContinueBtnClick(); }, 500);
                  }
                  else{
                    window.appDetailsChaged(false);
                    window.appDetailsClicked(false);
                    $('.nav-tabs a[href=#'+targetTab+']').tab('show') ;
                    //return true;

                  }
                });

                return false;
              }

              else if(window.compInstDetailsChaged()){
                e.preventDefault();


                showMessageBox(uiConstants.componentInstanceConfig.CONFIRM_SAVE_COMP_INST_DETAILS, "question", "confirm", function confirmCallback(confirmCompInstDetailsSave){
                  if(confirmCompInstDetailsSave){
                    setTimeout(function(){ self.onSaveContinueBtnClick(); }, 500);
                  }
                  else{
                    window.compInstDetailsChaged(false);
                    window.compInstDetailsClicked(false);
                    $('.nav-tabs a[href=#'+targetTab+']').tab('show') ;
                    //return true;

                  }
                });

                return false;
              }

              else if(window.txnDetailsChaged()){
                e.preventDefault();


                showMessageBox(uiConstants.transactionConfig.CONFIRM_SAVE_TXN_DETAILS, "question", "confirm", function confirmCallback(confirmTxnDetailsSave){
                  if(confirmTxnDetailsSave){
                    setTimeout(function(){ self.onSaveContinueBtnClick(); }, 500);
                  }
                  else{
                    window.txnDetailsChaged(false);
                    window.txnDetailsClicked(false);
                    $('.nav-tabs a[href=#'+targetTab+']').tab('show') ;
                    //return true;

                  }
                });

                return false;
              }

              else if(window.agentDetailsChaged()){
                e.preventDefault();


                showMessageBox(uiConstants.agentConfig.CONFIRM_SAVE_AGENT_DETAILS, "question", "confirm", function confirmCallback(confirmAgentDetailsSave){
                  if(confirmAgentDetailsSave){
                    setTimeout(function(){ self.onSaveContinueBtnClick(); }, 500);
                  }
                  else{
                    window.agentDetailsChaged(false);
                    window.agentDetailsClicked(false);
                    $('.nav-tabs a[href=#'+targetTab+']').tab('show') ;
                    //return true;

                  }
                });

                return false;
              }

              else{
                return true;
              }
            }
        });

        $('#btnNext').prop('disabled', true);

        /*$('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {

          alert(self.compInstEditLoadData());
          if(self.compInstEditLoadData()){
            self.compInstEditLoadData(false);
            self.toShowAgentWizardPopup(false);
            uicommon.postbox.publish(self.appDetWithTypeId(), "triggerGetServerDetailsForApplication");
          }
         
        });*/
      }


    this.onExitBtnClick = function(){
       window.location.reload(false);   
    }

    this.masterDataReqCallComponentWizard=function(){
      requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=1", "GET", "", "getApplications", successCallback, errorCallback);
    }

    //application page save functionality :End
  	function successCallback(data, reqType) {
    		if(reqType === "getCompTypeVersion"){
      			//console.log("result Inside config wizrad ");
      			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
      			self.componentsArr(data.result);
            self.jsonFormatForCompInstPopulation();
      			self.componentInstanceloaded(1);
    		}
        else if(reqType === "getApplications"){
            console.log("get all applications");
            if(uiConstants.common.DEBUG_MODE)console.log(data.result);
            self.applicationsArr(data.result);
        }
  	}

  	function errorCallback(reqType) {
    		if(reqType === "getCompTypeVersion"){
      			showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
      	}
        else if(reqType === "getApplications"){
            showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
        }
  	}

    self.onTabShow = function(tab, navigation, index){
      if(tab.context.hash === "#applicationsTab"){
        if(self.isApplicationAdded()){
          self.currentViewIndex(8);
        }

        self.curActiveTab("applicationsTab");
        window.compInstDetailsChaged(false);
        window.compInstDetailsClicked(false);
        window.txnDetailsChaged(false);
        window.txnDetailsClicked(false);
        window.agentDetailsChaged(false);
        window.agentDetailsClicked(false);

        self.toggleButtonLabel(self.saveAndContinueButtonLabel,"Save and Continue");
        self.isCompInstToLoad(false);
        self.isCompInstToLoad(true);
        if(self.panelTitle === "Edit" || self.appCurrentView() == "Edit"){
          self.checkToDisplaySkip(true);
        }else if(self.panelTitle === "Add"){
            if(self.isApplicationAdded()){ // if application is added in add mode and skip is required hen this flag is set,only when navigation from compInst tab 
               self.checkToDisplaySkip(true);
               self.checkToShowSaveContinue(false);
               self.appCurrentView("Edit");
            }
            else{
              self.checkToDisplaySkip(false);
            }
        }
        //self.hidePreviousButton(true);
        self.hidePreviousButton();
      }
      else if(tab.context.hash === "#compInstancesTab"){
        self.curActiveTab("compInstancesTab");
        window.appDetailsChaged(false);
        window.appDetailsClicked(false);
        window.txnDetailsChaged(false);
        window.txnDetailsClicked(false);
        window.agentDetailsChaged(false);
        window.agentDetailsClicked(false);

        self.checkToShowSaveContinue(true);
        self.checkToDisplaySkip(true);
        self.hidePreviousButton();
        self.toggleButtonLabel(self.saveAndContinueButtonLabel,"Save and Continue"); 

        if(self.panelTitle === "Edit" || self.compInstAddDataLoaded()){
          uicommon.postbox.publish(self.appDetWithTypeId(), "triggerGetServerDetailsForApplication");
        }

        self.compInstAddDataLoaded(true);

        if(self.panelTitle === "Edit"){//} && !self.compInstEditDataLoaded()){
          self.compInstEditDataLoaded(true);
        }

         //self.compInstDataForApp('');
      }
      else if(tab.context.hash === "#transactionsTab"){
        self.isCompInstToLoad(false);
        self.isCompInstToLoad(true);
        self.curActiveTab("transactionsTab");
        window.compInstDetailsChaged(false);
        window.compInstDetailsClicked(false);
        window.appDetailsChaged(false);
        window.appDetailsClicked(false);
        window.agentDetailsChaged(false);
        window.agentDetailsClicked(false);

        uicommon.postbox.publish("checking the current View","checkCurrentView");
        self.checkToDisplaySkip(true);
        self.hidePreviousButton();
        self.toggleButtonLabel(self.saveAndContinueButtonLabel,"Save");
      }
      else if(tab.context.hash === "#agentsTab"){
        self.isCompInstToLoad(false);
        self.isCompInstToLoad(true);
        self.curActiveTab("agentsTab");
        window.compInstDetailsChaged(false);
        window.compInstDetailsClicked(false);
        window.appDetailsChaged(false);
        window.appDetailsClicked(false);
        window.txnDetailsChaged(false);
        window.txnDetailsClicked(false);
                                          
        uicommon.postbox.publish("checking the current View","checkCurrentView");
        self.checkToDisplaySkip(false);
        self.hidePreviousButton();
        self.toggleButtonLabel(self.saveAndContinueButtonLabel,"Save");

        if(self.panelTitle === "Edit"){
            self.agentListViewModel().getUnmappedInstanceDetails();
        }
        else{
            self.displayAgentWizardPopup(self.componentInstAddedDataResult(),"Add");
        }
      }
      else if(tab.context.hash === "#dummyTab"){
        window.appDetailsChaged(false);
        window.appDetailsClicked(false);
        window.compInstDetailsChaged(false);
        window.compInstDetailsClicked(false);
        window.txnDetailsChaged(false);
        window.txnDetailsClicked(false);
        window.agentDetailsChaged(false);
        window.agentDetailsClicked(false);
      }
      else{
        if(self.panelTitle === "Edit"){
          uicommon.postbox.publish("displaySkip","ViewIsChangedToAddEdit");
        }else{
          self.checkToDisplaySkip(false);
        }
      }
    };

    self.displayAgentWizardPopup= function(value,operation){
         if(value.length>0){
            self.toShowAgentWizardPopup(true);          
         }else{
            self.toShowAgentWizardPopup(false);          
         }
       
      /*Display agent wizard popup only when toShowAgentWizardPopup is true*/
        if(self.toShowAgentWizardPopup()){
          $("#messageDialogBoxComponent").modal("show");
        }
    }



    /*Skip and jump to next Tab*/
    self.onSkip = function(){
      var activeTabId= $(".nav.nav-tabs li.active").find("a").attr('href');

      window.compInstDetailsChaged(false);
      window.compInstDetailsClicked(false);
      window.appDetailsChaged(false);
      window.appDetailsClicked(false);
      window.txnDetailsChaged(false);
      window.txnDetailsClicked(false);
      window.agentDetailsChaged(false);
      window.agentDetailsClicked(false);

      /*If the current active tab is component instances tab, and Skip is clicked. Do not display Agent wizard popup*/
      if(activeTabId == "#compInstancesTab"){
        self.toShowAgentWizardPopup(false);
      }
      else if(activeTabId === "#transactionsTab"){
        uicommon.postbox.publish("checking the current View","checkCurrentView");
        if(currentTabView && (currentTabView === uiConstants.common.ADD_SINGLE_VIEW || currentTabView === uiConstants.common.EDIT_VIEW || currentTabView === uiConstants.common.CLONE_VIEW)){
          uicommon.postbox.publish(uiConstants.common.LIST_VIEW,"changeViewToList");
        }
      }
      else if(activeTabId === "#agentsTab"){
        uicommon.postbox.publish("checking the current View","checkCurrentView");
        if(currentTabView && (currentTabView === uiConstants.common.ADD_SINGLE_VIEW || currentTabView === uiConstants.common.EDIT_VIEW || currentTabView === uiConstants.common.CLONE_VIEW)){
          uicommon.postbox.publish(uiConstants.common.LIST_VIEW,"changeViewToList");
        }
      }

      $('#rootwizard').bootstrapWizard('next');

      
       

     /* activeTabId= $(".nav.nav-tabs li.active").find("a").attr('href');
      if(activeTabId ==="#compInstancesTab"){
        uicommon.postbox.publish(self.appDetWithTypeId(), "triggerGetServerDetailsForApplication");
        
      }*/
    };

    self.hidePreviousButton = function(hide){
      if(hide){
        $('.previous').addClass('hidden');
        $('.customPrevious').removeClass('hidden');
      }
      else{
        $('.previous').removeClass('hidden');
        $('.customPrevious').addClass('hidden');
      }
    };

    /**
     * When Previous button is clicked in the Wizard, and the tabs are in following orders:
     * 1. Application Tab - 
     * 2. Component Instances - Previous will bring to Application Tab only
     * 3. a) Transaction List View - Previous will bring to CI Tab
     *    b) Transaction Add/Edit View - Previous will bring to Transaction List View
     * 4. a) Agent List View - Previous will bring to Transaction Tab   
     *    b) Agent Add/Edit View - Previous will bring to Agent List View
     *    
     * @param {tab} - Current Tab
     * @param {navigation} - Navigated Tab
     * @param {index} - Navigated Tab Index
     * @return {boolean} - true to go to previous tab, false will stop the navigation
     */
    self.onPrevious = function(tab, navigation, index){
      if(tab.context.hash === "#applicationsTab"){
        return true;
      }
      else if(tab.context.hash === "#compInstancesTab"){
          return true;
      }
      else if(tab.context.hash === "#transactionsTab"){
        var currentTabView = '';
        var $txnComponentVM = self.txnListViewModel();
        currentTabView = $txnComponentVM.currentViewIndex();

        if((currentTabView === uiConstants.common.ADD_SINGLE_VIEW || currentTabView === uiConstants.common.EDIT_VIEW || currentTabView === uiConstants.common.CLONE_VIEW || currentTabView === uiConstants.common.READ_VIEW)){
          $txnComponentVM.currentViewIndex(uiConstants.common.LIST_VIEW);
          uicommon.postbox.publish("View is now a List","ViewIsChangedToList");  
          return false;
        }
        return true;
      }
      else if(tab.context.hash === "#agentsTab"){
        var currentTabView = '';
        var $agentComponentVM = self.agentListViewModel();
        currentTabView = $agentComponentVM.currentViewIndex();

        if((currentTabView === uiConstants.common.ADD_SINGLE_VIEW || currentTabView === uiConstants.common.EDIT_VIEW || currentTabView === uiConstants.common.CLONE_VIEW || currentTabView === uiConstants.common.READ_VIEW)){
          $agentComponentVM.currentViewIndex(uiConstants.common.LIST_VIEW);
          uicommon.postbox.publish("View is now a List","ViewIsChangedToList");  
          return false;
        }
        return true;
      }
      else{
        return true; // reset the skip visibility to invisible
      }
    };

    /*Subscribed to topic 'ViewIsChangedToList'. When View is changed to List, hide Save and Continue button for list View*/
    uicommon.postbox.subscribe(function(value){
      self.checkToShowSaveContinue(false);
      
      var activeTabId= $(".nav.nav-tabs li.active").find("a").attr('href');
      if(activeTabId === "#agentsTab"){
        self.checkToDisplaySkip(false); // do not display skip button in list view
      }else{
        self.checkToDisplaySkip(true); // display skip button in list view
      }
    },"ViewIsChangedToList");

    /*Subscribed to topic 'ViewIsChangedToAddEdit'. When View is changed to List, hide Save and Continue button for list View*/
    uicommon.postbox.subscribe(function(value){
      self.checkToShowSaveContinue(true);
      if(value === "displaySkip"){
        self.checkToDisplaySkip(true);
      }
      else{
        self.checkToDisplaySkip(false);// hide skip button in add/edit view
      }
    },"ViewIsChangedToAddEdit");

  uicommon.postbox.subscribe(function(value){
    currentTabView = value;
    if(value === uiConstants.common.LIST_VIEW){
      self.checkToShowSaveContinue(false);
    }else{
      self.checkToShowSaveContinue(true);
    }
  },"currentViewIsChecked");

  /**
   * This method will change the observable value to given new Label
   * @param  {observable} observable Observable variable which will be assigned a new label value
   * @param  {string} newLabel   New Label value
   * @return {null}
   */
  self.toggleButtonLabel = function(observable, newLabel){

    observable(newLabel);
  }

}

ConfigWizard.prototype.dispose = function() { };
return { viewModel: ConfigWizard, template: templateMarkup };

});
