 <div id="rootwizard" class="tabbable tabs-left" data-bind="template: {afterRender: renderHandler}" >
	<div class="navbar">
	  <div class="navbar-inner">
			<ul>
			  	<li><a href="#applicationsTab" data-toggle="tab" title="Configure Application details"><span style="font-size:20px;">Step 1 :</span> Application</a></li>
				<li><a href="#compInstancesTab" data-toggle="tab"  title="Configure Component Instance details"><span style="font-size:20px;">Step 2 :</span>  Component Instances</a></li>
				<li><a href="#transactionsTab" data-toggle="tab"  title="Configure Transaction details"><span style="font-size:20px;">Step 3 :</span>  Transactions</a></li>
				<li><a href="#agentsTab" data-toggle="tab"  title="Configure Agent details"><span style="font-size:20px;">Step 4 :</span>  Agents</a></li>
				<li style="display: none;"><a href="#dummyTab" data-toggle="tab"></a></li>
				
			</ul>
	  </div>
	</div>
	<div class="tab-content">
	    <div class="tab-pane" id="applicationsTab">
			<!-- ko if:  curActiveTab() == 'applicationsTab' -->
		      <application-add-edit id="appConfigWizard" params="{'panelTitle':panelTitle,'mode':mode,'typeArr':typeArr, 'timezoneArr': timezoneArr,currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, pageSelected: pageSelected}"></application-add-edit>
		    <!-- /ko -->

	    </div>
	    <div class="tab-pane" id="compInstancesTab">


		       	<div >
			<!-- ko if:  isCompInstToLoad() -->

					 <component-instance-config-wizard params="applicationId: applicationId,applicationTypeId:applicationTypeId,applicationsArr: applicationsArr, isCompInstToLoad: isCompInstToLoad, curActiveTab: curActiveTab" ></component-instance-config-wizard> 
			<!-- /ko -->

				</div> 
	    </div>
	    <div class="tab-pane" id="transactionsTab">
			<!-- ko if:  curActiveTab() == 'transactionsTab' -->
		 		<transaction-list-view params=" txnListViewModel: txnListViewModel, applicationId: addedApplicationId, applicationsArr: applicationsArr,'mode':mode"> </transaction-list-view>
			<!-- /ko -->
	    </div>
		<div class="tab-pane" id="agentsTab">
			<!-- ko if:  curActiveTab() == 'agentsTab' -->
			<!-- /ko -->
			 	<agent-list-view params="{ 'panelTitle':panelTitle,agentListViewModel: agentListViewModel, componentInstanceIDs: componentInstAddedDataResult,'mode':mode,applicationId: applicationId,callMethod:displayAgentWizardPopup}">  </agent-list-view> 
	    </div>
	    <div class="tab-pane" id="dummyTab">

	    </div>
	    
		<ul class="pager wizard">
			<li class="customPrevious" style="float:left;" data-bind=" click: onExitBtnClick">
		  		<a>Previous</a>
	  		</li>
			<li class="previous"><a>Previous</a></li>
			<li  id="next last" style="float:right" data-bind="click: onExitBtnClick"><a >Exit</a></li>
			<li  id="btnNext" data-bind="click:onSaveContinueBtnClick,visible:checkToShowSaveContinue"><a data-bind="text:saveAndContinueButtonLabel"></a></li>
			<li class="skipNext" data-bind="click: onSkip, visible: checkToDisplaySkip"><a data-bind="text: skipButtonLabel"></a></li>
		</ul>
	</div>	
</div> 



