<div >
		  <h4>
			  Email/SMS Configuration
		  </h4>
	
		  <ul class="nav nav-tabs">
		    <li class="active">
		    	<a data-toggle="tab" href="#email">Email Server Settings</a>
		    </li>
		    <li>
		    	<a data-toggle="tab" href="#sms">SMS Gateway Settings</a>
		    </li>
		  </ul>

		  <div class="tab-content">
		    <div id="email" class="tab-pane fade in active" >
			    	<!-- ko if: emailErrorMsg() != '' -->
						<div data-bind="attr:{class: 'error-container'}">
							<div class="errorMessageField error-span">
								<span class="glyphicon glyphicon-exclamation-sign"></span>
								<span>Please correct the errors highlighted below!</span>
							</div>
						</div>
					<!-- /ko-->
					</br>
					</br>

					<form class="form-horizontal" role="form" data-bind='template: {afterRender: renderHandlerEmail }'>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMTP Sever IP Address <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtIpAddress" placeholder="Enter IP address" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMTP Port <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="number" class="form-control" id="txtPort" placeholder="Enter port" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group ">
							<label class="control-label col-sm-2">SMTP Security </label>
							<!-- ko foreach: smtpSecurityArr() -->
							    <label class="config-option-label" style="margin-left: 20px;">
      								<input type="radio" name="smtpSecurity" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'none' ? 'checked' : false},event:{click: $parent.onSecuritySelection}"> <span data-bind="text: $data.name"></span>
   								</label>
							<!-- /ko-->
						</div>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMTP Username <span data-bind="visible:selectedSecurity() === 'SSL' || selectedSecurity() === 'TLS'" class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtUsername" placeholder="Enter username" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMTP Password<span data-bind="visible:selectedSecurity() == 'SSL' || selectedSecurity() === 'TLS'" class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="password" class="form-control" id="txtPwd" placeholder="Enter password" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">From Recepient <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtFromRecepient" placeholder="Enter recepient email-id" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group">
							<div class="col-sm-offset-2 col-sm-4 divActionPanel">
								<button type ="submit" class="btn btn-primary" data-bind="event:{click: onClickSubmitEmailSettings}">Submit</button>
							</div>
						</div>
					</form> 
		    </div>
		    <div id="sms" class="tab-pane fade">
		      		<!-- ko if: errorMsg() != '' -->
						<div data-bind="attr:{class: 'error-container'}">
							<div class="errorMessageField error-span">
								<span class="glyphicon glyphicon-exclamation-sign"></span>
								<span>Please correct the errors highlighted below!</span>
							</div>
						</div>
					<!-- /ko-->

					<br>
					<br>
					<form class="form-horizontal" role="form" data-bind='template: {afterRender: renderHandlerSMS }'>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMS Gateway IP Address <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtSmsIpAddress" placeholder="Enter IP address" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" required autofocus >
							</div>
						</div>

						<div class="form-group form-required" >
							<label class="control-label col-sm-2">SMS Port <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="number" class="form-control" id="txtSmsPort" placeholder="Enter port" required  >
							</div>
						</div>

						<!-- <div class="form-group" >
							<label class="control-label col-sm-2">Country Code</label>
							<div class="col-sm-4">
								<input type="number" class="form-control" id="txtCountryCode" placeholder="Enter country code"  >
							</div>
						</div> -->

						<div class="form-group ">
							<label class="control-label col-sm-2">Protocol </label>
							<!-- ko foreach: protocolArr() -->
							    <label class="config-option-label" style="margin-left: 20px;">
      								<input type="radio" name="protocol" data-bind="value: $data.masterId, attr: {'checked': slugify($data.name) == 'http' ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
   								</label>
							<!-- /ko-->
						</div>

						<div class="form-group form-required" data-bind="visible:selectedProtocol() === 'HTTP'">
							<label class="control-label col-sm-2">Request Method</label>
							<div class="col-sm-4">
								<select id="requestMethodList" class="chosen form-control" data-bind="options: requestMethodArr, optionsText: 'name', optionsValue: 'masterId', event: {change: onSelectionOfReqMethod}"></select>
							</div>
						</div>
					
				
						<div class="form-group " data-bind="visible:selectedProtocol() === 'HTTP' &&selectedReqMethod() === 'POST'">
							<label class="control-label col-sm-2">POST Content</label>
							<div class="col-sm-4">
								<textarea type="textarea" class="form-control" rows="3" id="txtPostContent" placeholder="Enter post content" style="resize: none"></textarea>
							</div>
						</div>
					
					
						<div class="form-group form-required" data-bind="visible:selectedProtocol() === 'HTTP'">
							<label class="control-label col-sm-2">Relative URL <span class="mandatoryField">*</span></label>
							<div class="col-sm-4">
								<input type="text" class="form-control" id="txtRelativeURL" placeholder="Enter relative url" max="45" required >
							</div>
						</div>
						
						<div id="divTxnThresholds" class="form-group form-required" >
							<div class="panel panel-default inner-panel">
								<div class="configPanel panel-heading">User defined parameters</div>
									 <div class="panel-body" style="padding:15px" >
									 	<div class="col-sm-4" style="width:100%;float:left;" >
											
											<button id="btnParametersAdd" class="glyphicon glyphicon-plus" type ="button" data-bind="event: {click: onClickParamAdd}" title="Add more parameters"></button>
											
											<table id="userParametersList" class="table table-hover table-striped" data-bind="visible: userParamsArr().length > 0">
													<thead>
														<tr>
															<th class="col-xs-2">Parameter Type</th>
															<th class="col-xs-3">Name</th>
															<th class="col-xs-7">Value</th>
															<th style="width: 20px;""></th>
														</tr>
													</thead>
													<tbody data-bind="foreach : userParamsArr">
														<tr >
															<td>
																<select class="chosen form-control paramTypeListChosen" data-bind="options: $parent.paramTypesArr,  optionsText: 'name', 'optionsValue': 'masterId', attr: {'id': 'paramTypeList_'+$index()}"></select>
															</td>
															<td>
																<input type="text" data-bind="attr: {id: 'paramName'+$index()}" class="form-control" id="txtParamName" placeholder="Enter name" data-bind="value: $data.parameterName">
															</td>
															<td>
																<div class="input-group">
																	<textarea type="text" class="form-control" placeholder="Enter value" data-bind="attr : {id: 'txtParamValue'+$index()}, value: $data.parameterValue" rows="2" style="resize: none"></textarea>
																	<span type="button" class="modalPlaceholder input-group-addon input-group-btndollar" data-toggle="modal" data-target="#idModalPlaceholder" data-backdrop="static" data-keyboard="false" title="Add Placeholder" style="vertical-align: middle" data-bind="event:{click: $parents[0].onPlaceholderClick.bind($data, 'SMS Settings Value', 'smsSettingsValue', 'txtParamValue'+$index())}">
																		<img src="images/placeholder-16.png"/>
																	</span>
																</div>
															</td>
												
															<td style="text-align:center; padding-left: 0px;" class="col-xs-1">
																<span type="button" class="glyphicon glyphicon-remove buttondelete" title="Delete" data-bind="event: {click: $parents[0].onSmsParamsDelete.bind($data, $index())}" style="float: left;"></span>
															</td>
														
														</tr>
													</tbody>
											</table>
										</div>	
									 </div>
							</div>
						</div>		 

						<div class="form-group">
							<div class="col-sm-offset-2 col-sm-4 divActionPanel">
								<button type ="submit" class="btn btn-primary" data-bind="event:{click: onClickSubmitSMSSettings}">Submit</button>
							</div>
						</div>
					</form> 
		    </div>
	</div>

	<div class="modal fade" id="idModalPlaceholder" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
		<div class="modal-dialog">
	        <div class="modal-content">
				<div class="modal-header">
	                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
	                	<h4><span data-bind="text: modalTitle"></span></h4>
	            </div>	
	            <div>
					<!-- ko if: modalTitle() != '' -->
	        			<placeholder-options params="{selectedPlaceholdersArr: selectedPlaceholdersArr,screenName:'emailSMS'}"></placeholder-options>
					<!-- /ko-->
	        	</div>
	        </div>
    	</div> <!-- /.modal-content -->
	</div>


