define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead', 'text!./email-sms-settings.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon) {

function emailSmsSettings(params){
	var self = this;

	var previousProtocolSelected="";
	var currentProtocolSelected="";

	this.configId = ko.observable(0);
	this.configSMSId = ko.observable(0);
	this.emailErrorMsg = ko.observable("");
	this.errorMsg = ko.observable("");
    this.firstFieldToShowErr = ko.observable("");

	this.smtpSecurityArr = ko.observableArray();
	this.mailServerSettings = ko.observable();
	this.smsServerSettings = ko.observable();

	this.protocolArr = ko.observableArray();
	this.requestMethodArr = ko.observableArray();
	this.placeholdersArr = ko.observableArray();
	this.paramTypesArr = ko.observableArray();
	this.userParamsArr = ko.observableArray();

	this.selectedPlaceholdersArr = ko.observableArray();
	this.modalTitle = ko.observable();
	this.contentType = ko.observable();
	this.palceholderContEleId = ko.observable("");

	this.selectedReqMethod = ko.observable();
	this.selectedSecurity = ko.observable();
	this.selectedProtocol = ko.observable();

	this.renderHandlerEmail = function(){
		window.currentPageMode = "AddUpdate";

		$("#idModalPlaceholder").on('hidden.bs.modal', function () {
			var placeholderLength = 0;

			if(self.selectedPlaceholdersArr().length != 0){
				var placeholderLength = document.getElementById(self.palceholderContEleId()).selectionStart;

				for(var placeholder in self.selectedPlaceholdersArr()){
					placeholderLength += self.selectedPlaceholdersArr()[placeholder].length;
					insertAtCursor(document.getElementById(self.palceholderContEleId()), self.selectedPlaceholdersArr()[placeholder]);
				}

				document.getElementById(self.palceholderContEleId()).selectionStart = placeholderLength;
				document.getElementById(self.palceholderContEleId()).selectionEnd = placeholderLength;

			    $("#"+self.palceholderContEleId()).focus();

				self.modalTitle("");
				self.selectedPlaceholdersArr([]);
			}
		});
		requestCall(uiConstants.common.SERVER_IP + "/gateway/security/smtp", "GET", "", "getSecurityTypes", successCallback, errorCallback);
		requestCall(uiConstants.common.SERVER_IP + "/gateway/smtp", "GET", "", "getMailServerSettings", successCallback, errorCallback);
		//requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/methods", "GET", "", "getRequestMethods", successCallback, errorCallback);

		self.emailErrorMsg.subscribe(function(errorField) {
	        if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		self.errorMsg.subscribe(function(errorField) {
	        if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});
	}

	this.onSecuritySelection = function(){//radio click function
		self.selectedSecurity(self.getSecurityName(parseInt($("input[name='smtpSecurity']:checked").val())));
		return true;
	}

	this.onProtocolSelection =function(){
		if($("input[name='protocol']:checked").val() != previousProtocolSelected){
			showMessageBox(uiConstants.emailSMSSettings.CONFIRM_PROTOCOL_CHANGE, "question", "confirm", function confirmCallback(r){
				if(r){
					handleProtocolSelection();
					previousProtocolSelected = "";

				}
				else{
					$("input[name='protocol'][value='"+previousProtocolSelected+"']").prop('checked', true);
					self.selectedProtocol(self.getProtocolName($('input[name=protocol]:checked').val()));
					previousProtocolSelected = "";
				}
			});
		}



		return true;
	}

	function handleProtocolSelection(){
		self.userParamsArr([]);
		self.selectedProtocol(self.getProtocolName($('input[name=protocol]:checked').val()));
		self.getParameterOnProtocolChange();
	}

	this.getParameterOnProtocolChange=function(){
		if(self.selectedProtocol() == "TCP"){
			requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/parameterTypes?type="+self.selectedProtocol(), "GET", "", "getParamTypes", successCallback, errorCallback);
		}
		else{
			requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/parameterTypes?type="+self.selectedReqMethod(), "GET", "", "getParamTypes", successCallback, errorCallback);
		}
	}

	this.getSecurityName=function(securityId){
		console.log(securityId);
		var name="";
		var securityObj=($.grep(self.smtpSecurityArr(), function(evt){ return evt.masterId == securityId; }));
		if(securityObj.length){
			name=securityObj[0].name;
		}
		return name;
	}

	this.getProtocolName=function(protocolId){
		var name="";
		var protocolObj=($.grep(self.protocolArr(), function(evt){ return evt.masterId == protocolId; }));
		if(protocolObj.length){
			name=protocolObj[0].name;
		}
		return name;
	}

	this.onGetMailSeverSettings = function(){
		if(self.mailServerSettings() != null){
			self.configId(self.mailServerSettings()['id']);
			$("#txtIpAddress").val(self.mailServerSettings()['ipAddress']);
			$("#txtPort").val(self.mailServerSettings()['port']);
			self.selectedSecurity(self.getSecurityName(self.mailServerSettings()['security']));
			$("input[name='smtpSecurity'][value='"+self.mailServerSettings()['security']+"']").prop('checked', true);
			$("#txtUsername").val(self.mailServerSettings()['username']);
			$("#txtPwd").val(self.mailServerSettings()['password']);
			$("#txtFromRecepient").val(self.mailServerSettings()['fromRecipient']);
		}
	}

	this.renderHandlerSMS = function(){
		/*$("#requestMethodList").on('change',function(e){
		 	//self.onSelectionOfReqMethod();
		});*/

		/*Jquery chosen start*/
		jQuery(".chosen").chosen({
			search_contains: true	
		});

		requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/methods", "GET", "", "getRequestMethods", successCallback, errorCallback);
		requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/protocols", "GET", "", "getProtocols", successCallback, errorCallback);

		/*requestCall(uiConstants.common.SERVER_IP + "/gateway/sms", "GET", "", "getSMSServerSettings", successCallback, errorCallback);

		$("#userParametersList tbody").on('click', '.buttondelete', function(e){
			var tableObj = this;
		    showMessageBox(uiConstants.emailSMSSettings.DELETE_CONFIRM_USERPARAMS, "question", "confirm", function confirmCallback(confirmDelete){
          		if (confirmDelete) {
				 	var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
				 	if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);
				 	self.userParamsArr.splice(rowToDelete,1);
				}
			});
		});*/
	}

	this.onSmsParamsDelete = function(indx){
		showMessageBox(uiConstants.emailSMSSettings.DELETE_CONFIRM_USERPARAMS, "question", "confirm", function confirmCallback(confirmDelete){
      		if (confirmDelete) {
			 	self.userParamsArr.splice(indx,1);

			 	$(".paramTypeListChosen").chosen('destroy')
			 	$(".paramTypeListChosen").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
		});
	}

	this.onSelectionOfReqMethod=function(){
		self.selectedReqMethod($('#requestMethodList option:selected').text());
		self.userParamsArr([]);

		self.getParameterOnProtocolChange();
		//requestCall(uiConstants.common.SERVER_IP + "/gateway/sms/parameterTypes?type="+self.selectedReqMethod(), "GET", "", "getParamTypes", successCallback, errorCallback);
	}

	this.onPlaceholderClick = function(placeholderType, contentType, placeHolderId){
		self.palceholderContEleId(placeHolderId);
		self.modalTitle(placeholderType + " Placeholder Text");
		self.contentType(contentType);
	}

	this.onClickSubmitEmailSettings = function(){
		self.emailErrorMsg("");
		self.firstFieldToShowErr("");
		removeError();

		if($("#email #txtIpAddress").val() == ""){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.REQUIRED_SMTP_IP_ADDRESS);
			showError("#email #txtIpAddress", uiConstants.emailSMSSettings.REQUIRED_SMTP_IP_ADDRESS);
		    self.emailErrorMsg("#email #txtIpAddress");
		}
		else if($("#email #txtIpAddress").val() != "" && (!hostIpValidation($("#email #txtIpAddress").val()))){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.INVALID_SMTP_IP_ADDRESS);
			showError("#email #txtIpAddress", uiConstants.emailSMSSettings.INVALID_SMTP_IP_ADDRESS);
		    self.emailErrorMsg("#email #txtIpAddress");
		}
		
		if($("#email #txtPort").val() == ""){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.REQUIRED_SMTP_PORT);
			showError("#email #txtPort", uiConstants.emailSMSSettings.REQUIRED_SMTP_PORT);
		    self.emailErrorMsg("#email #txtPort");
		}
		else if($("#email #txtPort").val() != "" && ($("#email #txtPort").val() < 0 || $("#email #txtPort").val() > uiConstants.common.MAX_PORT_RANGE) ){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.INVALID_SMTP_PORT);
			showError("#email #txtPort", uiConstants.emailSMSSettings.INVALID_SMTP_PORT);
		    self.emailErrorMsg("#email #txtPort");
		}

		if((self.selectedSecurity() === 'SSL' || self.selectedSecurity() === 'TLS') && $("#email #txtUsername").val() == ""){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.REQUIRED_USERNAME);
			showError("#email #txtUsername", uiConstants.emailSMSSettings.REQUIRED_USERNAME);
		    self.emailErrorMsg("#email #txtUsername");
		}
		else if((self.selectedSecurity() === 'SSL' || self.selectedSecurity() === 'TLS') && $("#email #txtUsername").val() != "" && $("#email #txtUsername").val().length > 45){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.ERROR_USERNAME_SSL);
			showError("#email #txtUsername", uiConstants.emailSMSSettings.ERROR_USERNAME_SSL);
		    self.emailErrorMsg("#email #txtUsername");
		}
		
		if((self.selectedSecurity() === 'SSL' || self.selectedSecurity() === 'TLS') && $("#email #txtPwd").val() == ""){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.REQUIRED_PASSWORD);
			showError("#email #txtPwd", uiConstants.emailSMSSettings.REQUIRED_PASSWORD);
		    self.emailErrorMsg("#email #txtPwd");
		}
		else if((self.selectedSecurity() === 'SSL' || self.selectedSecurity() === 'TLS') && $("#email #txtPwd").val() != "" && $("#email #txtPwd").val().length > 60){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.ERROR_PASSWORD_SSL);
			showError("#email #txtPwd", uiConstants.emailSMSSettings.ERROR_PASSWORD_SSL);
		    self.emailErrorMsg("#email #txtPwd");
		}
		
		if($("#email #txtFromRecepient").val() == ""){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.REQUIRED_FROM_EMAILID);
			showError("#email #txtFromRecepient", uiConstants.emailSMSSettings.REQUIRED_FROM_EMAILID);
		    self.emailErrorMsg("#email #txtFromRecepient");
		}
		else if($("#email #txtFromRecepient").val() != "" && !emailValidation($("#email #txtFromRecepient").val())){
			//self.emailErrorMsg(uiConstants.emailSMSSettings.INVALID_FROM_RECEPIENT_EMAILID);
			showError("#email #txtFromRecepient", uiConstants.emailSMSSettings.INVALID_FROM_RECEPIENT_EMAILID);
		    self.emailErrorMsg("#email #txtFromRecepient");
		}

		if(self.emailErrorMsg() == ""){
			var emailSettingsObj = {
				"index":1,
				"id":self.configId(),
				"ipAddress": $("#email #txtIpAddress").val(),
				"port": $("#email #txtPort").val(),
				"security":parseInt($("input[name='smtpSecurity']:checked").val()),
				"username":$("#email #txtUsername").val(),
				"password": $("#email #txtPwd").val(),
				"fromRecipient": $("#email #txtFromRecepient").val()
			};

			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(emailSettingsObj));

			if(self.configId() == 0)
				requestCall(uiConstants.common.SERVER_IP + "/gateway/smtp", "POST", JSON.stringify(emailSettingsObj), "addSingleConfig", successCallback, errorCallback);
			else
				requestCall(uiConstants.common.SERVER_IP + "/gateway/smtp" , "PUT", JSON.stringify(emailSettingsObj), "editSingleConfig", successCallback, errorCallback);
		}
	}

	this.onGetSmsSeverSettings = function(){
		if(self.smsServerSettings() != null){
			self.configSMSId(self.smsServerSettings()['id']);
			$("#txtSmsIpAddress").val(self.smsServerSettings()['address']);
			$("#txtSmsPort").val(self.smsServerSettings()['port']);

			self.selectedProtocol(self.getProtocolName(parseInt(self.smsServerSettings()['protocolId'])));
			$("input[name='protocol'][value='"+self.smsServerSettings()['protocolId']+"']").prop('checked', true);
			//$("#txtCountryCode").val(self.smsServerSettings()['countryCode']);

			self.selectedReqMethod(self.smsServerSettings()['httpMethod']);
			var reqMethodId=($.grep(self.requestMethodArr(), function(evt){ return evt.name == self.smsServerSettings()['httpMethod']; }));
			$("#requestMethodList").val(reqMethodId[0].masterId).trigger('chosen:updated');
			$("#txtRelativeURL").val(self.smsServerSettings()['httpRelativeUrl']);
			$("#txtPostContent").val(self.smsServerSettings()['postData']);

			self.userParamsArr(self.smsServerSettings()['parameters']);
		}
	}

	this.onClickParamAdd = function(){
		self.userParamsArr.push({ "id":0,
								  "parameterId":0,
								  "parameterTypeId":0,
								  "parameterName":"",
								  "parameterValue":""});
		$(".paramTypeListChosen").trigger('chosen:updated');
		jQuery(".chosen").chosen({
			search_contains: true	
		});
	}

	this.onClickSubmitSMSSettings = function(){
		self.errorMsg("");
		self.firstFieldToShowErr("");
		removeError();

		if($("#sms #txtSmsIpAddress").val() == ""){
			//self.errorMsg(uiConstants.emailSMSSettings.REQUIRED_SMTP_IP_ADDRESS);
			showError("#sms #txtSmsIpAddress", uiConstants.emailSMSSettings.REQUIRED_SMTP_IP_ADDRESS);
		    self.errorMsg("#sms #txtSmsIpAddress");
		}
		else if($("#sms #txtSmsIpAddress").val() != "" && (!hostIpValidation($("#sms #txtSmsIpAddress").val()))){
			//self.errorMsg(uiConstants.emailSMSSettings.INVALID_SMTP_IP_ADDRESS);
			showError("#sms #txtSmsIpAddress", uiConstants.emailSMSSettings.INVALID_SMTP_IP_ADDRESS);
		    self.errorMsg("#sms #txtSmsIpAddress");
		}
		
		if($("#sms #txtSmsPort").val() == ""){
			//self.errorMsg(uiConstants.emailSMSSettings.REQUIRED_SMTP_PORT);
			showError("#sms #txtSmsPort", uiConstants.emailSMSSettings.REQUIRED_SMTP_PORT);
		    self.errorMsg("#sms #txtSmsPort");
		}
		else if($("#sms #txtSmsPort").val() != "" && ($("#sms #txtSmsPort").val() < 0 || $("#sms #txtSmsPort").val() > uiConstants.common.MAX_PORT_RANGE) ){
			//self.errorMsg(uiConstants.emailSMSSettings.INVALID_SMTP_PORT);
			showError("#sms #txtSmsPort", uiConstants.emailSMSSettings.INVALID_SMTP_PORT);
		    self.errorMsg("#sms #txtSmsPort");
		}
		/*else if($("#txtCountryCode").val() != "" && ($("#txtCountryCode").val() < 1 || $("#txtCountryCode").val() > 99999999)){
			self.errorMsg(uiConstants.emailSMSSettings.ERROR_COUNTRY_CODE);
		}*/
		if(self.selectedProtocol() != "TCP" && $("#txtRelativeURL").val().trim() == ""){
			//self.errorMsg(uiConstants.emailSMSSettings.REQUIRED_RELATIVEURL);
			showError("#sms #txtRelativeURL", uiConstants.emailSMSSettings.REQUIRED_RELATIVEURL);
		    self.errorMsg("#sms #txtRelativeURL");
		}

		//debugger;
		//var flagEsc = 0;
		//var levelNum = 0;
		var paramsArr=[];

		for (var i = 0; i < self.userParamsArr().length; i++) {
			if( $("#paramName"+i).val() !="" && $("#paramName"+i).val().length > 127){
				//flagEsc = 1;
				//levelNum = (i+1);
				showError("#sms #paramName"+i, uiConstants.emailSMSSettings.ERROR_IN_PARAM_NAME_LENGTH);
			    self.errorMsg("#sms #paramName"+i);
				//break;
			}
			
			if( $("#txtParamValue"+i).val() !="" && $("#txtParamValue"+i).val().length > 127){
				//flagEsc = 2;
				//levelNum = (i+1);
				showError("#sms #txtParamValue"+i, uiConstants.emailSMSSettings.ERROR_IN_PARAM_VALUE_LENGTH);
			    self.errorMsg("#sms #txtParamValue"+i);
				//break;
			}
		}

		/*switch(flagEsc){
			case 1:  self.errorMsg(uiConstants.emailSMSSettings.ERROR_IN_PARAM_NAME_LENGTH+" for row "+levelNum);
					 break;
			case 2:  self.errorMsg(uiConstants.emailSMSSettings.ERROR_IN_PARAM_VALUE_LENGTH+" for row "+levelNum);
			         break;
		}*/
		
		if(self.errorMsg() == ""){

			//smsuser params object to save
			for(indx in self.userParamsArr()){
				var paramsObj = {
									"parameterId": self.userParamsArr()[indx].parameterId,
									"parameterName": $("#paramName"+indx).val(),
									"parameterValue": ($("#txtParamValue"+indx).val()),//self.userParamsArr()[indx].parameterValue,
									"parameterTypeId": parseInt($("#paramTypeList_"+indx).val())
								}

				paramsArr.push(paramsObj);
			}


			var protocolName=($.grep(self.protocolArr(), function(evt){ return evt.masterId == parseInt($("input[name='protocol']:checked").val()); }));
			var smsSettingsObj = {
				"id":self.configSMSId(),
				"address":$("#txtSmsIpAddress").val(),
				"port":$("#txtSmsPort").val(),
				"countryCode":0,
				"protocolId":parseInt($("input[name='protocol']:checked").val()),
				"protocolName":protocolName[0].name,
				"httpMethod":$("#requestMethodList option:selected").text(),
				"httpRelativeUrl":$("#txtRelativeURL").val(),
				"postData":$("#txtPostContent").val(),
				"parameters":paramsArr
			};

			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(smsSettingsObj));

			if(self.configSMSId() == 0)
				requestCall(uiConstants.common.SERVER_IP + "/gateway/sms", "POST", JSON.stringify(smsSettingsObj), "addSingleConfigforSMS", successCallback, errorCallback);
			else
				requestCall(uiConstants.common.SERVER_IP + "/gateway/sms" , "PUT", JSON.stringify(smsSettingsObj), "editSingleConfigforSMS", successCallback, errorCallback);
		}
	}

	function onDataLoad(){
		if(self.smtpSecurityArr().length && self.mailServerSettings()){
			self.onGetMailSeverSettings();
		}
	}

	function onMasterLoad(){
		if(self.protocolArr().length && self.requestMethodArr().length){
			requestCall(uiConstants.common.SERVER_IP + "/gateway/sms", "GET", "", "getSMSServerSettings", successCallback, errorCallback);
		}
	}

	function successCallback(data, reqType) {
		if(reqType === "getSecurityTypes"){
			if(data.responseStatus == "success"){
				if(data.result.length == 0){
					self.smtpSecurityArr([{}]);
				}
				else{
					self.smtpSecurityArr(data.result);
					self.selectedSecurity(self.getSecurityName(parseInt($("input[name='smtpSecurity']:checked").val())));

				}
					onDataLoad();

			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		else if(reqType === "getMailServerSettings"){
			if(data.responseStatus == "success"){
				self.mailServerSettings(data.result);
				//self.onGetMailSeverSettings();
				onDataLoad();
			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		else if(reqType === "getProtocols"){
			if(data.responseStatus == "success"){
				if(data.result.length == 0){
					self.protocolArr([{}]);
				}
				else{
					self.protocolArr(data.result);
					currentProtocolSelected = $('input[name=protocol]:checked').val();
					self.selectedProtocol(self.getProtocolName(parseInt($("input[name='protocol']:checked").val())));


					//self.getParameterOnProtocolChange();

					$('input[name=protocol]').on('focus',function(){
						if(previousProtocolSelected == ""){
						     previousProtocolSelected = $('input[name=protocol]:checked').val();

						}
					}).change(function(){
						currentProtocolSelected = $('input[name=protocol]:checked').val();
						self.onProtocolSelection();

					});




				}
				onMasterLoad();
			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		/*else if(reqType === "getPlaceholders"){
			if(data.responseStatus == "success"){
				if(data.result.length == 0){
					self.placeholdersArr([{}]);
				}
				else{
					self.placeholdersArr(data.result);
				}
				self.onSelectionOfReqMethod();
				onMasterLoad();
			}
			else{
				showMessageBox(data.message, "error");
			}
		}*/
		else if(reqType === "getRequestMethods"){
			if(data.responseStatus == "success"){
				if(data.result.length == 0){
					self.requestMethodArr([{}]);
				}
				else{
					self.requestMethodArr(data.result);
				}
				
				$("#requestMethodList").trigger('chosen:updated');
				onMasterLoad();
			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		else if(reqType === "getParamTypes"){
			debugger;
			if(data.responseStatus == "success"){
				if(data.result.length != 0){
					self.paramTypesArr(data.result);

				}
				//onMasterLoad();




				/*if(self.smsServerSettings().hasOwnProperty("httpMethod")){
						self.selectedReqMethod(self.smsServerSettings()["httpMethod"]);

					}*/




				if(self.smsServerSettings()){
					for(var sms in self.smsServerSettings()["parameters"]){
						self.onClickParamAdd();
						$("#paramTypeList_"+sms).val(self.smsServerSettings()["parameters"][sms].parameterTypeId).trigger('chosen:updated');
						$("#paramName"+sms).val(self.smsServerSettings()["parameters"][sms].parameterName);
						$("#txtParamValue"+sms).val(self.smsServerSettings()["parameters"][sms].parameterValue);
					}
				}


				if(self.smsServerSettings() && self.smsServerSettings()["parameters"] && self.smsServerSettings()["parameters"].length){
					self.smsServerSettings()["parameters"] = [];
				}
				else{
					if(!self.userParamsArr().length){
						self.onClickParamAdd();
					}
				}
			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		else if(reqType === "getSMSServerSettings"){
			if(data.responseStatus == "success"){
				self.smsServerSettings(data.result);

				if(self.smsServerSettings().hasOwnProperty("httpMethod")){
					self.selectedReqMethod(self.smsServerSettings()["httpMethod"]);

				}

				$("#txtSmsIpAddress").val(self.smsServerSettings()["address"]);
				$("#txtSmsPort").val(self.smsServerSettings()["port"]);
				//$("#txtCountryCode").val(self.smsServerSettings()["countryCode"]);


				/*self.configSMSId(self.smsServerSettings()['id']);
			$("#txtSmsIpAddress").val(self.smsServerSettings()['address']);
			$("#txtSmsPort").val(self.smsServerSettings()['port']);

			self.selectedProtocol(self.getProtocolName(parseInt(self.smsServerSettings()['protocolId'])));
			$("input[name='protocol'][value='"+self.smsServerSettings()['protocolId']+"']").prop('checked', true);
			$("#txtCountryCode").val(self.smsServerSettings()['countryCode']);

			self.selectedReqMethod(self.smsServerSettings()['httpMethod']);
			var reqMethodId=($.grep(self.requestMethodArr(), function(evt){ return evt.name == self.smsServerSettings()['httpMethod']; }));
			$("#requestMethodList").val(reqMethodId[0].masterId);
			$("#txtRelativeURL").val(self.smsServerSettings()['httpRelativeUrl']);
			$("#txtPostContent").val(self.smsServerSettings()['postData']);*/




				$("input[name='protocol'][value='"+self.smsServerSettings()['protocolId']+"']").prop('checked', true);
				var reqMethodId=($.grep(self.requestMethodArr(), function(evt){ return evt.name == self.smsServerSettings()['httpMethod']; }));
				if(reqMethodId.length){
					$("#requestMethodList").val(reqMethodId[0].masterId).trigger('chosen:updated');;
				}

				if((self.getProtocolName(self.smsServerSettings()['protocolId'])) == "TCP"){

				}
				else{
					if(self.smsServerSettings().hasOwnProperty("postData")){
						$("#txtPostContent").val(self.smsServerSettings()['postData']);

					}
					$("#txtRelativeURL").val(self.smsServerSettings()['httpRelativeUrl']);
				}

				handleProtocolSelection();

				//self.onGetSmsSeverSettings();
				//onMasterLoad();
			}
			else{
				showMessageBox(data.message, "error");
			}
		}
		else if(reqType === "addSingleConfig"){
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				showMessageBox(uiConstants.emailSMSSettings.ERROR_ADD_MAIL_SERVER_SETTINGS, "error");
			}
			else{
				showMessageBox(uiConstants.emailSMSSettings.SUCCESS_ADD_MAIL_SERVER_SETTINGS);
			}
		}
		else if(reqType === "editSingleConfig"){
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				showMessageBox(uiConstants.emailSMSSettings.ERROR_UPDATE_MAIL_SERVER_SETTINGS, "error");
			}
			else{
				showMessageBox(uiConstants.emailSMSSettings.SUCCESS_UPDATE_MAIL_SERVER_SETTINGS);
			}
		}
		else if(reqType === "addSingleConfigforSMS"){
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				showMessageBox(uiConstants.emailSMSSettings.ERROR_ADD_SMS_SERVER_SETTINGS, "error");
			}
			else{
				showMessageBox(uiConstants.emailSMSSettings.SUCCESS_ADD_SMS_SERVER_SETTINGS);
			}
		}
		else if(reqType === "editSingleConfigforSMS"){
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				showMessageBox(uiConstants.emailSMSSettings.ERROR_UPDATE_SMS_SERVER_SETTINGS, "error");
			}
			else{
				showMessageBox(uiConstants.emailSMSSettings.SUCCESS_UPDATE_SMS_SERVER_SETTINGS);
			}
		}
	}

	function errorCallback(reqType) {
		if(reqType === "getSecurityTypes"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_SECURITY_TYPES, "error");
		}
		else if(reqType === "getMailServerSettings"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_MAIL_SERVER_SETTINGS, "error");
		}
		else if(reqType === "getProtocols"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_PROTOCOLS, "error");
		}
		else if(reqType === "getPlaceholders"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_SMS_PLACE_HOLDERS, "error");
		}
		else if(reqType === "getRequestMethods"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_REQUEST_METHODS, "error");
		}
		else if(reqType === "getParamTypes"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_PARAM_TYPES, "error");
		}
		else if(reqType === "getSmsServerSettings"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_GET_SMS_SERVER_SETTINGS, "error");
		}
		else if(reqType === "addSingleConfig"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_ADD_MAIL_SERVER_SETTINGS, "error");
		}
		else if(reqType === "editSingleConfig"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_UPDATE_MAIL_SERVER_SETTINGS, "error");
		}
		else if(reqType === "addSingleConfigforSMS"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_ADD_SMS_SERVER_SETTINGS, "error");
		}
		else if(reqType === "editSingleConfigforSMS"){
			showMessageBox(uiConstants.emailSMSSettings.ERROR_UPDATE_SMS_SERVER_SETTINGS, "error");
		}
	}
}

emailSmsSettings.prototype.dispose = function() { };
	return { viewModel: emailSmsSettings, template: templateMarkup };
});
