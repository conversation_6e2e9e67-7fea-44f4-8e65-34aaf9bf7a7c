define(['jquery','knockout','d3','c3','fusionCharts','text!./horizontal-bar-chart.html','hasher','ui-constants','ui-common', 'knockout-es5'], function($,ko,d3,c3,fusioncharts,templateMarkup,hasher,uiConstants,uicommon, koES5) {

  function HorizontalBarChart(params) {
    var self = this;
    self.chartDataObj = params.chartDataObj;
    self.podId = params.podId;

    koES5.track(this);

    self.chartContId = "chartContId_" + self.podId;

    this.renderHandler = function(){  
      var cdObj = self.chartDataObj;
      self.initChart(self.chartContId, cdObj.chartHeight, cdObj.chartWidth, cdObj.legends, cdObj.yAxisDataSet);
    }

    this.initChart = function(chartCont, chartHeight, chartWidth,xAxisDataSet,yAxisDataSet){
      var chart = c3.generate({
          bindto: document.getElementById(chartCont),
          size: {
            width: parseInt(chartWidth),
            //height : 180,
            height: parseInt(chartHeight),
          },
          grid: {
            focus: {
              show: false
            }
          },
          area: {
            zerobased: true
          },
          data: {
            columns: [
                yAxisDataSet,
            ],
            type: 'bar',
            labels: true,
            colors: {
              data1: function (d) { 
                  if(typeof d.x !== "undefined"){                       
                    if(xAxisDataSet[d.x].toLowerCase() == 'good'){
                        return '#6B8E23';
                    }else if(xAxisDataSet[d.x].toLowerCase() == 'slow'){
                        return '#269abc';
                    }else if(xAxisDataSet[d.x].toLowerCase() == 'failed'){
                        return '#E62424';
                    }else if(xAxisDataSet[d.x].toLowerCase() == 'timedout'){
                        return '#d58512';
                    }else if(xAxisDataSet[d.x].toLowerCase() == 'unknown'){
                        return '#858080';
                    }
                  }else{

                  }

                }
            },
          },
          zoom:{
            enabled: false,
          },
          padding: {
              bottom: 10,
              //top: 5,
              //right: 0,
              //left : 0,
          },
          legend: {
            show: false
          },
          axis: {
            rotated:true,
            x: {
                type: 'category',
                categories: xAxisDataSet,
            },
            y:{
                show:false
            }
          },
          bar: {
            width: {
              ratio: 0.5,
              //max: 30
            }
          },
          grid: {
            focus: {
              show: false
            }
          }
        });
      }
    }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
    HorizontalBarChart.prototype.dispose = function() { };
  
    return { viewModel: HorizontalBarChart, template: templateMarkup };

});
