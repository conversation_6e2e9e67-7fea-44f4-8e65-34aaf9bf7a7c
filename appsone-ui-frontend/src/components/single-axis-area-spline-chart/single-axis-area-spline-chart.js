define(['jquery','knockout', 'text!./single-axis-area-spline-chart.html', 'knockout-es5','d3','c3'], function($, ko, templateMarkup, koES5, d3, c3){

	function SingleAxisAreaSplineChart(params) {
    	var self = this;
    	this.podId = params && params.podId || '';
    	this.chartDataObj = params.chartDataObj;
    	this.chartContId = params.chartContId;
        koES5.track(this);

        //this.chartContId = "chartContId_" + self.podId;
        
    	this.renderHandler = function(){
    		var cdObj = self.chartDataObj;
    		var chartGridContId = self.chartContId;//koES5.getObservable(self,'chartContId')();
    		console.log(cdObj.yaxisDataSet);
    		self.initChart(chartGridContId,cdObj.chartHeight, cdObj.chartWidth, cdObj.xAxisLabel, cdObj.yAxisLabel, cdObj.chartPaletteColors, cdObj.timeWindowSet, cdObj.yaxisDataSet, cdObj.podName);
    	}
        
        this.initChart = function(chartCont, chartHeight, chartWidth,xAxisLabel,yAxisLabel,chartPaletteColors,xAxisDataSet,yAxisDataSet){
        /*Parameters:-
            chartCont :- container Id where Chart will render
            chartHeight :- chart canvas height
            chartWidth :- chart canvas width
            xAxisLabel  :- chart X Axis Label
            yAxisLabel :- chart Primary Y AXis Label
            xAxisDataSet :- chart x AXis Data set Array(for Ex - Time window)
            yAxisDataSet :- chart actual entity data set(Array of object)
        */
        
            
            var chart = c3.generate({
                bindto: document.getElementById(chartCont),
                size: {
                    width: parseInt(chartWidth),
                    height: parseInt(chartHeight),
                },
                grid: {
                    focus: {
                    show: false
                    }
                },
                area: {
                    zerobased: true
                },
                legend:{
                    show :true,
                },
                zoom:{
                   enabled: false,
                },
                padding: {
                    //bottom: 10,
                    //top: 5,
                    //right: 0,
                    //left : 0,
                },
                data: {
                    columns: yAxisDataSet,
                    type: 'area-spline',
                    order: null,
                    groups: [
                        ['Good', 'Slow','Failed','Timeout','Unknown']                        
                    ],
                    colors:{
                        'Good': '#6B8E23',
                        'Slow': '#269abc',
                        'Failed': '#E62424',
                        'Timeout': '#d58512',
                        'Unknown': '#484B4B',
                       
                    }
                },
                axis: {
                    rotated: false,
                    y: {
                      show:true,
                      type: 'category',
                      label: {
                        text: yAxisLabel,
                        position: 'inner-top',   // inner-top,inner-middle,inner-bottom,outer-top,outer-middle,outer-bottom

                      },
                      //min: 0,
                      //max: Math.max.apply(Math,tickYValues),
                      tick: {
                          //min: 0,
                          //max: Math.max.apply(Math,tickYValues),
                          format: d3.format('d'),
                          //values: tickYValues,
                          /*format: function(d){
                               if(d%2==0) return d;
                          },*/
                        },

                    },
                    x: {
                      show:true,
                      type: 'category',
                      categories: xAxisDataSet,
                      tick: {
                        rotate: 0,
                        multiline: false,
                        culling: {
                                //max: 7,// the number of tick texts will be adjusted to less than this value
                                //count: 7,

                        }
                      },
                      label: {
                        text: xAxisLabel,
                        position: 'outer-center',// inner-right,inner-center,inner-left,outer-right,outer-center,outer-left
                      }
                    },
              },

            });

    	}

    }
    SingleAxisAreaSplineChart.prototype.dispose = function() {}

	return { viewModel: SingleAxisAreaSplineChart, template: templateMarkup };
});

