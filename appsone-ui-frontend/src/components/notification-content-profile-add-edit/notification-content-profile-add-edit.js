define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./notification-content-profile-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox) {

	function NotificationContentProfileAddEdit(params) {
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = ko.observableArray();
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex || ko.observable(uiConstants.common.ADD_SINGLE_VIEW);
		this.configStatus = ko.observable(true);
		this.sendEmail = ko.observable(false);
		this.emailSubject = ko.observable("");
		this.emailMessage = ko.observable("");
		this.sendSms = ko.observable(false);
		this.smsMessage = ko.observable("");
		this.pageSelected = params.pageSelected;
		this.alertProfileTypesArr = params.alertProfileTypesArr;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableConfig = ko.observable(true);
  		this.selectedPlaceholdersArr = ko.observableArray();
  		this.modalTitle = ko.observable("");
  		this.contentType = ko.observable();
  		this.isEmailMandatory = ko.observable(false);
  		this.isSmsMandatory = ko.observable(false);
  		this.isModal = ko.observable(false);
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		this.notificationTypeId = ko.observable(0);

		var configTagLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*$("input[type=select]").on("mouseover", function(){

				//var txt = $(this).val();
				$(this).prop("title", "sfdsfsdsd");

			});*/


			/*$("#notificationAddEdit").on("change", function(){
				//handle change event here
			});*/

			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			if(params.isModal){
				self.isModal(true);
				$("#notificationAddEdit #alertProfileTypeList").val(params.profileTypeId()).trigger("chosen:updated");
				self.onAlertProfileTypeChange();
			}

			$("#idModalPlaceholder").on('hidden.bs.modal', function () {
				var palceholderContEleId = "";
				var placeholderLength = 0;
				if(self.selectedPlaceholdersArr().length != 0){
					if(self.contentType() == "emailsubject"){
						palceholderContEleId = "txtEmailSubject";
					}
					else if(self.contentType() == "emailmessage"){
						palceholderContEleId = "txtEmailMessage";
					}
					else if(self.contentType() == "smsmessage"){
						palceholderContEleId = "txtSmsMessage";
					}
					var placeholderLength = document.getElementById(palceholderContEleId).selectionStart;

					for(var placeholder in self.selectedPlaceholdersArr()){
						placeholderLength += self.selectedPlaceholdersArr()[placeholder].length;
						insertAtCursor(document.getElementById(palceholderContEleId), self.selectedPlaceholdersArr()[placeholder]);
					}
					
					document.getElementById(palceholderContEleId).selectionStart = placeholderLength;
					document.getElementById(palceholderContEleId).selectionEnd = placeholderLength;

					//if (!$("#txtEmailSubject").is(":focus")) {
				     	$("#"+palceholderContEleId).focus();
				    //}
					
				}

				self.modalTitle("");
				self.selectedPlaceholdersArr([]);

				
			});

			
			self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			$('#notification-tokenfield-typeahead')
			.on('tokenfield:createdtoken', function (e) {

				if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
					tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
				}
				var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
				//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
				if(tagIndex != -1){
					self.configTagAutoCompleteArr.splice(tagIndex, 1);
				}

				$('#notification-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			})

			.on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			.on('tokenfield:removedtoken', function (e) {
				if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
					tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
				}
				tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
				var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

				if(tagIndex != -1){
					self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
					self.configTagAutoCompleteArr.sort();
				}

				$('#notification-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			});

	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=NotificationContentProfile", "GET", "", "getNotificationContentTag", successCallback, errorCallback);
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
	        	if(params.isModal){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});

		this.handleEmailSmsChk = function(chkType){
			if(chkType == "email"){
				if($("#chkEmail").prop("checked")){

				}
				self.isEmailMandatory($("#chkEmail").prop("checked"));
			}
			else if(chkType == "sms"){
				if($("#chkSms").prop("checked")){

				}
				self.isSmsMandatory($("#chkSms").prop("checked"));
			}
			return true;
		}

		this.onPlaceholderClick = function(placeholderType, contentType){
			self.modalTitle(placeholderType + " Placeholder Text");
			self.contentType(contentType);


		}

		this.onAlertProfileTypeChange = function(){
			self.notificationTypeId($("#alertProfileTypeList").val());
		}

       	//Adding/Updating single notification content
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var configData;

			$("#divNotificationProfile #txtName").val($("#divNotificationProfile #txtName").val().trim());
			$("#divNotificationProfile #txtDescription").val($("#divNotificationProfile #txtDescription").val().trim());
			$("#txtEmailSubject").val($("#txtEmailSubject").val().trim());
			$("#txtEmailMessage").val($("#txtEmailMessage").val().trim());
			$("#txtSmsMessage").val($("#txtSmsMessage").val().trim());
			
			if($("#divNotificationProfile #txtName").val().trim() == ""){
				//self.errorMsg(uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_REQUIRED);
				showError("#divNotificationProfile #txtName", uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_REQUIRED);
			    self.errorMsg("#divNotificationProfile #txtName");
			}
			else if($("#divNotificationProfile #txtName").val().length < 2){
				//self.errorMsg(uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_MIN_LENGTH_ERROR);
				showError("#divNotificationProfile #txtName", uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divNotificationProfile #txtName");
			}
			else if($("#divNotificationProfile #txtName").val().length > 45){
				//self.errorMsg(uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_MAX_LENGTH_ERROR);
				showError("#divNotificationProfile #txtName", uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divNotificationProfile #txtName");
			}
			else if(!nameValidation($("#divNotificationProfile #txtName").val())){
				//self.errorMsg(uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_INVALID_ERROR);
				showError("#divNotificationProfile #txtName", uiConstants.notificationContentProfile.NOTIFICATION_CONTENT_NAME_INVALID_ERROR);
			    self.errorMsg("#divNotificationProfile #txtName");
			}
			if($("#divNotificationProfile #txtDescription").val().trim() == ""){
				//self.errorMsg(uiConstants.common.DESCRIPTION_REQUIRED);
				showError("#divNotificationProfile #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divNotificationProfile #txtDescription");
			}
			else if($("#divNotificationProfile #txtDescription").val().length < 25){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
				showError("#divNotificationProfile #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divNotificationProfile #txtDescription");
			}
			else if($("#divNotificationProfile #txtDescription").val().length > 256){
				//self.errorMsg(uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
				showError("#divNotificationProfile #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divNotificationProfile #txtDescription");
			}
			if($("#divNotificationProfile #alertProfileTypeList").val() == 0){
				//self.errorMsg(uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
				showError("#divNotificationProfile #alertProfileTypeList_chosen", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
				showError("#divNotificationProfile #alertProfileTypeList_chosen span", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
			    self.errorMsg("#divNotificationProfile #alertProfileTypeList_chosen");
			}
			else{
				removeError("#divNotificationProfile #alertProfileTypeList_chosen");
				removeError("#divNotificationProfile #alertProfileTypeList_chosen span");
			}
			
			removeError("#divNotificationProfile .tokenfield");
			removeError("#divNotificationProfile #notification-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#divNotificationProfile #notification-tokenfield-typeahead").val())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError("#divNotificationProfile .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divNotificationProfile #notification-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divNotificationProfile .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						//self.errorMsg(uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divNotificationProfile .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divNotificationProfile #notification-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divNotificationProfile .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						//self.errorMsg(uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divNotificationProfile .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divNotificationProfile #notification-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divNotificationProfile .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						//self.errorMsg(uiConstants.common.INVALID_TAG_NAME);
						showError("#divNotificationProfile .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divNotificationProfile #notification-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divNotificationProfile .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}
				}
			}

			self.emailSubject($("#txtEmailSubject").val());
			self.emailMessage($("#txtEmailMessage").val());
			self.smsMessage($("#txtSmsMessage").val());

			if(self.isEmailMandatory()){
				
				if(self.emailSubject().trim() == ""){
					//self.errorMsg(uiConstants.notificationContentProfile.EMAIL_SUBJECT_REQUIRED);
					showError("#divNotificationProfile #txtEmailSubject", uiConstants.notificationContentProfile.EMAIL_SUBJECT_REQUIRED);
			    	self.errorMsg("#divNotificationProfile #txtEmailSubject");
				}
				else if(self.emailSubject().trim().length > 255){
					//self.errorMsg(uiConstants.notificationContentProfile.EMAIL_SUBJECT_MAX_LENGTH_ERROR);
					showError("#divNotificationProfile #txtEmailSubject", uiConstants.notificationContentProfile.EMAIL_SUBJECT_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divNotificationProfile #txtEmailSubject");
				}
				if(self.emailMessage().trim() == ""){
					//self.errorMsg(uiConstants.notificationContentProfile.EMAIL_MESSAGE_REQUIRED);
					showError("#divNotificationProfile #txtEmailMessage", uiConstants.notificationContentProfile.EMAIL_MESSAGE_REQUIRED);
			    	self.errorMsg("#divNotificationProfile #txtEmailMessage");
				}
				else if(self.emailMessage().trim().length > 65535){
					//self.errorMsg(uiConstants.notificationContentProfile.EMAIL_MESSAGE_MAX_LENGTH_ERROR);
					showError("#divNotificationProfile #txtEmailMessage", uiConstants.notificationContentProfile.EMAIL_MESSAGE_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divNotificationProfile #txtEmailMessage");
				}
			}

			if(self.isSmsMandatory()){
				if(self.smsMessage().trim() == ""){
					//self.errorMsg(uiConstants.notificationContentProfile.SMS_MESSAGE_REQUIRED);
					showError("#divNotificationProfile #txtSmsMessage", uiConstants.notificationContentProfile.SMS_MESSAGE_REQUIRED);
			    	self.errorMsg("#divNotificationProfile #txtSmsMessage");
				}
				else if(self.smsMessage().trim().length > 512){
					//self.errorMsg(uiConstants.notificationContentProfile.SMS_MESSAGE_MAX_LENGTH_ERROR);
					showError("#divNotificationProfile #txtSmsMessage", uiConstants.notificationContentProfile.SMS_MESSAGE_MAX_LENGTH_ERROR);
			    	self.errorMsg("#divNotificationProfile #txtSmsMessage");
				}
			}

			if(self.errorMsg() == ""){
				var configData = {"index":1,
					"name": self.configName(),
					"description":  self.configDescription().trim(),
					"alertProfileTypeId": parseInt($("#divNotificationProfile #alertProfileTypeList").val()),
					"contentSettings": {
						"emailSettings": {
							"sendEmail": self.isEmailMandatory() ? 1 : 0,
							"subject": self.emailSubject(),
							"body": self.emailMessage()
						},
						"smsSettings": {
							"sendSms": self.isSmsMandatory() ? 1 : 0,
							"body": self.smsMessage()
						}
					},
					"tags": tagsObjArr,
					"status" : self.configStatus()?1:0};

				if(self.configId() == 0)
					requestCall(uiConstants.common.SERVER_IP + "/notificationProfile", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
				else
					requestCall(uiConstants.common.SERVER_IP + "/notificationProfile/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
			}
			//}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();			
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			setConfigUneditable();			
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(){
			if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#alertProfileTypeList').prop('disabled', true).trigger("chosen:updated");
				document.getElementById("chkEmail").disabled = true;
				document.getElementById("chkSms").disabled = true;
				$('#notification-tokenfield-typeahead').tokenfield('readonly');
				$('#txtEmailSubject').prop('readonly', true);
				$('#txtEmailMessage').prop('readonly', true);
				$('#txtSmsMessage').prop('readonly', true);
				$("#emailSubPlaceholder").css("visibility","hidden");
				$("#emailMsgPlaceholder").css("visibility","hidden");
				$("#smsMsgPlaceholder").css("visibility","hidden");
			}
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divNotificationProfile .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
			$('#alertProfileTypeList').prop('disabled', true).trigger("chosen:updated");
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].name);
				self.configId(configObj[0].id);
				self.configDescription(configObj[0].description);
			}

			$("#alertProfileTypeList").val(configObj[0].alertProfileTypeId).prop('disabled', true);
			$("#alertProfileTypeList").val(configObj[0].alertProfileTypeId).trigger('chosen:updated');
			self.notificationTypeId($("#alertProfileTypeList").val());
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#notification-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			self.isEmailMandatory(configObj[0].contentSettings.emailSettings.sendEmail == 1);
			$("#chkEmail").prop("checked", self.isEmailMandatory());
			self.emailSubject(configObj[0].contentSettings.emailSettings.subject);
			self.emailMessage(configObj[0].contentSettings.emailSettings.body);

			self.isSmsMandatory(configObj[0].contentSettings.smsSettings.sendSms == 1);
			$("#chkSms").prop("checked", self.isSmsMandatory());
			self.smsMessage(configObj[0].contentSettings.smsSettings.body);

			self.errorMsg("");
		}

		this.cancelConfig = function(){
			if(params.isModal){
				$("#idModalAlertProfile").modal("hide");
				//$(".modal-header button").click();
			}
			else{
				self.selectedConfigRows([]);
				self.pageSelected("Notification Content Profile Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onMastersLoad(){
			if(configTagLoaded == 1){

			//$(".chosen-container > a > span").prop("title", "test");

				$("#alertProfileTypeList").trigger("chosen:updated");
			//$(".chosen-container > a > span").prop("title", "test");

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getNotificationContentTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);


				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#notification-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#notification-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#notification-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#notification-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_NOTIFICATION_CONTENT,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.notificationContentProfile.ERROR_ADD_NOTIFICATION_CONTENT, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.configName());
					}
					//  
					else{
						params.curPage(1);
					}
					self.cancelConfig();

					showMessageBox(uiConstants.notificationContentProfile.SUCCESS_ADD_NOTIFICATION_CONTENT);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_NOTIFICATION_CONTENT,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.notificationContentProfile.ERROR_UPDATE_NOTIFICATION_CONTENT, "error");
					}
				}
				else{
					showMessageBox(uiConstants.notificationContentProfile.SUCCESS_UPDATE_NOTIFICATION_CONTENT);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getNotificationContentTag"){
				showMessageBox(uiConstants.notificationContentProfile.ERROR_GET_NOTIFICATION_CONTENT_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.notificationContentProfile.ERROR_ADD_NOTIFICATION_CONTENT, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.notificationContentProfile.ERROR_UPDATE_NOTIFICATION_CONTENT, "error");
			}
		}
	}

	NotificationContentProfileAddEdit.prototype.dispose = function() { };
	return { viewModel: NotificationContentProfileAddEdit, template: templateMarkup };
});