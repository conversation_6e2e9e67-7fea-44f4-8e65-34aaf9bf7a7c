<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="notificationAddEdit">
 	<div class="configPanel panel-heading" data-bind="visible: !isModal()"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body" id="divNotificationProfile">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Alert Profile Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="alertProfileTypeList" data-bind="event: {change: onAlertProfileTypeChange}, foreach : alertProfileTypesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE"></option>
						<!-- /ko-->

						<option data-bind="value: $data.masterId, text: $data.name"></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="notification-tokenfield-typeahead" data-bind="enable: enableConfig(), value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>
			
			<div id="divContentSettings" class="form-group">
				<div class="panel panel-default inner-panel">
					<div class="configPanel panel-heading">Content Settings</div>
					<div class="panel-body" style="padding-bottom: 0px">
						<div class="form-group">
							<label class="control-label col-sm-4" >
							<div style="text-align: left;">
								<label><input type="checkbox" id="chkEmail" data-bind="checked: sendEmail, click: handleEmailSmsChk.bind($data,'email')"> Send Email</label>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-sm-2">Subject<span class="mandatoryField" data-bind="if : isEmailMandatory"> *</span></label>
							<div class="input-group col-sm-8">
								<input type="text" class="form-control" id="txtEmailSubject" data-bind="value: emailSubject" placeholder="Enter Subject">
								<!-- <span class="input-group-addon glyphicon glyphicon-usd input-group-btndollar" title="Add Placeholder" style="vertical-align: top"></span> -->

								<!-- ko if: notificationTypeId() != 0 -->
									<span id="emailSubPlaceholder" type="button" class="modalPlaceholder input-group-addon input-group-btndollar" data-toggle="modal" data-target="#idModalPlaceholder" data-backdrop="static" data-keyboard="false" title="Add Placeholder" style="vertical-align: top" data-bind="event:{click: onPlaceholderClick.bind($data, 'Email', 'emailsubject')}">
										<img src="images/placeholder-16.png"/>
									</span>
								<!-- /ko -->
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-sm-2">Message<span class="mandatoryField" data-bind="if : isEmailMandatory"> *</span></label>
							<div class="input-group col-sm-8">
								<textarea class="form-control" id="txtEmailMessage" rows="5" data-bind="value: emailMessage" placeholder="Enter Message" style="resize: none"></textarea>
								
								<!-- ko if: notificationTypeId() != 0 -->
									<span id="emailMsgPlaceholder" type="button" class="modalPlaceholder input-group-addon input-group-btndollar" data-toggle="modal" data-target="#idModalPlaceholder" data-backdrop="static" data-keyboard="false" title="Add Placeholder" style="vertical-align: top" data-bind="event:{click: onPlaceholderClick.bind($data, 'Email', 'emailmessage')}">
										<img src="images/placeholder-16.png"/>
									</span>
								<!-- /ko -->
							</div>
						</div>

						<hr class="form-group-seperator">

						<div class="form-group">
							<label class="control-label col-sm-4" >
							<div style="text-align: left;">
								<label><input type="checkbox" id="chkSms" data-bind="checked: sendSms, click: handleEmailSmsChk.bind($data,'sms')"> Send SMS</label>
							</div>
						</div>

						<div class="form-group">
							<label class="control-label col-sm-2">Message<span class="mandatoryField" data-bind="if : isSmsMandatory"> *</span></label>
							<div class="input-group col-sm-8">
								<span class="field-note-msg">Note: Actual SMS content while sending alert notification is limited to 160 characters only.</span>
								<textarea class="form-control" id="txtSmsMessage" rows="5" data-bind="value: smsMessage" placeholder="Enter Message" style="resize: none"></textarea>

								<!-- ko if: notificationTypeId() != 0 -->
									<span id="smsMsgPlaceholder" type="button" class="modalPlaceholder input-group-addon input-group-btndollar" data-toggle="modal" data-target="#idModalPlaceholder" data-backdrop="static" data-keyboard="false" title="Add Placeholder" style="vertical-align: top; padding-top: 28px;" data-bind="event:{click: onPlaceholderClick.bind($data, 'SMS', 'smsmessage')}">
										<img src="images/placeholder-16.png"/>
									</span>
								<!-- /ko -->
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>

			
		</form>
	</div>
</div>

<div class="modal fade" id="idModalPlaceholder" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true">
		<div class="modal-dialog">
        <div class="modal-content">
			<div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                	<h4><span data-bind="text: modalTitle"></span></h4>
            </div>	
            <div>
				<!-- ko if: modalTitle() != '' -->
        			<placeholder-options params="{selectedPlaceholdersArr: selectedPlaceholdersArr,screenName:'notification', notificationTypeId: notificationTypeId}"></placeholder-options>
				<!-- /ko-->
        	</div>
        </div>
    </div> <!-- /.modal-content -->
</div>