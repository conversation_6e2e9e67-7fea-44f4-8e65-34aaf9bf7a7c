define(['jquery','knockout', 'text!./tiles-page.html','hasher'], function($,ko, templateMarkup,hasher) {

  function TilesPage(params) {
    var self = this;

    //declare variables here
    this.linkAddress = ko.observable();

    self.openLinkOnTileClick = function() {  
    	 //hasher.setHash("#configWizard");
    }
  }

  TilesPage.prototype.dispose = function() { };
  return { viewModel: TilesPage, template: templateMarkup };

});