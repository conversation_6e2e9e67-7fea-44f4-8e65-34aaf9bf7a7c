<div class="login_container col-lg-12 col-md-12 col-sm-12 col-xs-12" data-bind="template: {afterRender: renderHandler}">
	<div class="row">
		<div class="col-lg-4 col-md-3 col-sm-2 col-xs-1"></div>
		<div class="login_sub_container col-lg-4 col-md-6 col-sm-8 col-xs-10">
			<div class="logo_cont col-lg-12 col-md-12 col-sm-12 col-xs-12">
				<div>
					<!-- <span>AppsOne Logo</span> -->
					<img src="images/logo.png" />				
				</div>			
			</div>
			<div class="ctrl_cont col-lg-12 col-md-12 col-sm-12 col-xs-12">
				<div class="header_cont">
					<span class="glyphicon glyphicon-user"></span>
					<span data-bind="text: actionTitle"></span>
					<!-- ko if: actionTitle() == uiConstants.login.CHANGE_PASSWORD_TITLE -->
						<span data-bind="text: userNameDisplay"></span>
					<!-- /ko -->

				</div>

				<!-- ko if: actionTitle() != uiConstants.login.CHANGE_PASSWORD_TITLE -->
					<div class="input_cont">
						<input type="text" id="textUsername" data-bind="value: userName" placeholder="User Name" type="text" autofocus/>
						<!-- ko if: actionTitle() == uiConstants.login.LOGIN_TITLE -->
							<input type="password" id="textPassword" data-bind="value: userPassword"  placeholder="Password"/>
						<!-- /ko -->

					</div>
				<!-- /ko -->

				<!-- ko if: actionTitle() == uiConstants.login.CHANGE_PASSWORD_TITLE -->
					<div class="input_cont">
						<input type="password" id="textOldPassword" data-bind="value: userOldPassword"  placeholder="Old Password"/>
						<div>
							<input type="password" id="textNewPassword" data-bind="value: userNewPassword"  placeholder="New Password">

							<span class="glyphicon glyphicon-question-sign glyphicon-question-style" style="float: right; margin-right: -17px; top: -21px;" title="Password should contain minimum 8 & maximum 15 characters with atleast one of each – special character, numeral, lower case and upper case alphabets"></span>
						</div>
						<input type="password" id="textConfirmPassword" data-bind="value: userConfirmPassword"  placeholder="Re-enter Password"/>
					</div>
				<!-- /ko -->

				<!-- ko if: actionTitle() == uiConstants.login.LOGIN_TITLE -->
					<div class="link_cont">
						<div><a href="" id="linkForgotPassword" data-bind="click: onForgotPasswordClick">Forgot Password?</a></div>
					</div>				
					<div class="btn_cont">
						<button type="button" class="btn btn-default" id="btnLogin" onclick="validateLogin(textUsername, textPassword, errorMessage)">Login</button>
					</div>
				<!-- /ko -->

				<!-- ko if: actionTitle() == uiConstants.login.FORGOT_PASSWORD_TITLE || actionTitle() == uiConstants.login.CHANGE_PASSWORD_TITLE -->
					<div class="reset-password-cont">
						<button id="btnOk" type="button" class="btn btn-default" data-bind="click: onSubmit">OK</button>
						<button type="button" class="btn btn-default" data-bind="click: onCancel">Cancel</button>
					</div>
				<!-- /ko -->
				<div class="small" style="color:rgb(174, 93, 93);margin-top:2%;">
					<label id="errorMessage"></label>
				</div>
			</div>
		</div>
		<div class="col-lg-4 col-md-3 col-sm-2 col-xs-1"></div>
	</div>
</div>