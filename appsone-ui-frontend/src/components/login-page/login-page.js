define(['jquery','knockout', 'cryptojs-core','cryptojs-aes','text!./login-page.html','hasher','validator','ui-constants','ui-common'], function($,ko,CryptoJSCore,CryptoJSAES,templateMarkup,hasher,validator,uiConstants,uicommon) {
 

  function LoginPage(params) {
    var self = this;
    this.userName = ko.observable();
    this.userPassword = ko.observable();
    this.userOldPassword = ko.observable();
    this.userNewPassword = ko.observable();
    this.userConfirmPassword = ko.observable();
    this.actionTitle = ko.observable(params.actionTitle || uiConstants.login.LOGIN_TITLE);
    this.navigateToFirstPage = ko.observable(0);
    this.userNameDisplay = ko.observable();

    //Hiding top and side bar

    /*if(localStorage.authToken){
      $("#slide-nav").css("visibility","visible"); //top-bar
    }
    else{
      $("#slide-nav").css("visibility","hidden"); //top-bar
    }*/
    $("#slide-nav").css("visibility","hidden"); //top-bar
    //$("#side-nav").css("visibility","hidden"); //side-bar

    this.renderHandler=function(){
      if(localStorage.apptoken){
        self.userNameDisplay("(User Name: " + JSON.parse(atob(localStorage.apptoken.split(".")[1])).userName+")");
      }

      uiConstants.common.FIRST_PAGE_AFTER_LOGIN = "#home";

      if(uiConstants.common.FORCE_PASSWORD_CHANGE == 0 && localStorage.apptoken && nonTokenBasedPages.indexOf(hasherVar.getHash()) == -1){
        hasher.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
        $("#slide-nav").css("visibility","visible");

        //validateSession("session3");
      }

      window.koLoginData.subscribe(function(actionTitleVal) {
          self.actionTitle(uiConstants.login.CHANGE_PASSWORD_TITLE);
          self.navigateToFirstPage(1);
      });

      $(this).keypress(function(e){
        if(e.keyCode==13){
          if(self.actionTitle() == uiConstants.login.LOGIN_TITLE){
            $('#btnLogin').click();
          }
          else{
            $('#btnOk').focus();
            $('#btnOk').click();
          }
        }
      });

      if(isSessionTimeout){
        $(".login_container #errorMessage").text("You have been logged out due to inactive session.");
      }
      else{
        $(".login_container #errorMessage").text("");
      }

      if(window.forgotPasswordClick){
        window.forgotPasswordClick = false;
        self.onForgotPasswordClick();
      }
    }

    this.onForgotPasswordClick = function(){
      self.actionTitle(uiConstants.login.FORGOT_PASSWORD_TITLE);
      $("#textUsername").focus();
    }

    this.onSubmit = function(){
      $("#errorMessage").text("");
      if(self.actionTitle() == uiConstants.login.FORGOT_PASSWORD_TITLE){
        if(self.userName() == undefined || self.userName() == ""){
          $("#errorMessage").text(uiConstants.login.USER_NAME_REQUIRED);
        }
        else{
        //  alert("send request to server");
          //requestCall("http://www.mocky.io/v2/580de5741200009c0b07871f?callback=?", "GET", "", "forgotPassword", successCallback, errorCallback);
          requestCall(uiConstants.common.SERVER_IP + "/password/forgot/reset", "PUT", "{userName: "+self.userName()+"}", "forgotPassword", successCallback, errorCallback);
        }
      }
      else if(self.actionTitle() == uiConstants.login.CHANGE_PASSWORD_TITLE){
        if(self.userOldPassword() == undefined || self.userOldPassword() == ""){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_OLD_PASSWORD_REQUIRED);
        }
        else if(self.userNewPassword() == undefined || self.userNewPassword() == ""){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_NEW_PASSWORD_REQUIRED);
        }
        else if(self.userOldPassword() == self.userNewPassword()){
          self.errorMsg(uiConstants.userProfileConfig.USER_PROFILE_OLD_NEW_PASSWORD_SAME_ERROR);
        }
        else if(self.userNewPassword().length < 8){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MIN_LENGTH_ERROR);
        }
        else if(self.userNewPassword().length > 15){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_PASSWORD_MAX_LENGTH_ERROR);
        }
        else if(!passwordValidation(self.userNewPassword())){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_INVALID_PASSWORD);
        }
        else if(self.userConfirmPassword() == undefined || self.userConfirmPassword() == ""){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_CONFIRM_PASSWORD_REQUIRED);
        }
        else if(self.userNewPassword() != self.userConfirmPassword()){
          $("#errorMessage").text(uiConstants.userProfileConfig.USER_PROFILE_PASSWORDS_MISMATCH);
        }
        else{
          //This is same as individual user profile edit page
          var ivStr = getRandomString();
          var oldPasswd = getEncodedText(ivStr, self.userOldPassword());
          var newPasswd = getEncodedText(ivStr, self.userNewPassword());
          //var userProfileId = uiConstants.common.USER_NAME;

          var userProfileObj = {
            "oldPassword": oldPasswd,
            "newPassword": newPasswd,
            "initializationVector": ivStr};

          if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(userProfileObj));
          requestCall(uiConstants.common.SERVER_IP + "/password/reset", "PUT", JSON.stringify(userProfileObj), "changeUserPassword", successCallback, errorCallback);
        }
      }
    }

    this.onCancel = function(){

      self.actionTitle(uiConstants.login.LOGIN_TITLE);
      if(self.navigateToFirstPage() == 1){
        self.navigateToFirstPage(0);
        loadPageAfterLogin(window.koLoginData());
        //hasher.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
      }
      else{
        hasher.setHash("#");
      }
    }

    function successCallback(data, reqType) {
      if(reqType === "forgotPassword"){
        if(data.responseStatus == uiConstants.common.CONST_FAILURE){
            showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_USER_PROFILE,data.errorCode,data.message), "error");
        }
        else{
          //var appUrl = location.protocol + "//" + location.hostname + (location.port ? ":"+location.port: "")+"/#/changePassword/"+data.result.userProfileId;
          showMessageBox(uiConstants.login.PASSWORD_RESET_EMAIL_SENT);

          self.actionTitle(uiConstants.login.LOGIN_TITLE);
        }
      }
      else if(reqType === "changeUserPassword"){
        if(data.responseStatus == uiConstants.common.CONST_FAILURE){
          if(data != undefined){
            showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_CHANGE_PASSWORD,data.errorCode,data.message), "error");
          }else{
            showMessageBox(uiConstants.login.ERROR_CHANGE_USER_PASSWORD, "error");
          }
        }
        else{
          showMessageBox(uiConstants.login.SUCCESS_CHANGE_USER_PASSWORD);
          uiConstants.common.FORCE_PASSWORD_CHANGE = 0;
          koLoginData(data);
          localStorage.apptoken = data.authToken;
          self.onCancel();
        }
      }
    }

    function errorCallback(reqType) {
      if(reqType === "forgotPassword"){
        showMessageBox(uiConstants.login.ERROR_SENDING_PASSWD_RESET_EMAIL, "error");
      }
      else if(reqType === "changeUserPassword"){
        showMessageBox(uiConstants.login.ERROR_CHANGE_USER_PASSWORD, "error");
         // self.onCancel();

      }
    }  
  }

  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  LoginPage.prototype.dispose = function() { };

  return { viewModel: LoginPage, template: templateMarkup };

});