<!-- ko if: errorMsg() != '' -->
	<div data-bind="attr:{class: 'error-container'}">
		<div class="errorMessageField error-span">
			<span class="glyphicon glyphicon-exclamation-sign"></span>
			<span>Please correct the errors highlighted below!</span>
		</div>
	</div>
<!-- /ko-->

<div class="panel panel-default" id="divAlertProfileAddEdit">
 	<div class="configPanel panel-heading"><div><h4><span data-bind="style: {float: (selectedConfigNames()!='' ? 'left' : '')}, text: pageSelected"></span></h4><label class="panel-heading-sel-item" data-bind="text: selectedConfigNames, attr: {title: selectedConfigNames}"></label></div></div>
 	
	<div class="panel-body">

		<form class="form-horizontal" role="form" id="formAddEdit" data-bind='template: {afterRender: renderHandler }'>
			<div class="form-group form-required">
				<label class="control-label col-sm-2">Name <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<input type="text" class="form-control" id="txtName" data-bind="value: configName" placeholder="Enter Name" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
				</div>
			</div>

			<div id="divConfigDescription" class="form-group form-required">
				<label class="control-label col-sm-2">Description <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<textarea class="form-control" id="txtDescription" rows="3" data-bind="value: configDescription" placeholder="Enter Description" style="resize: none"></textarea>
				</div>
			</div>

			<div class="form-group form-required">
				<label class="control-label col-sm-2">Alert Profile Type <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" id="alertProfileTypeList" data-bind="foreach : alertProfileTypesArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.commonAlertProfile.SELECT_ALERT_PROFILE_TYPE"></option>
						<!-- /ko-->

						<option data-bind="value: $data.masterId, text: $data.name"></option>
					</select>
				</div>
			</div>

			<div class="form-group">
				<label class="control-label col-sm-2">Notification Profile <span class="mandatoryField">*</span></label>
				<div class="col-sm-4">
					<select class="chosen form-control" data-bind="event: {'chosen:showing_dropdown': onNotificationProfileListOpen.bind($data,  'notificationProfileList')}, attr: {id: 'notificationProfileList'}, foreach : notificationProfileListsArr" data-placeholder=" ">
						<!-- ko if: $index() == 0 -->
							<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_NOTIFICATION_PROFILE"></option>
						<!-- /ko-->

						<option data-bind="value: $data.id, text: $data.name"></option>
					</select>
				</div>

				<!-- ko if: (currentViewIndex() == uiConstants.common.EDIT_VIEW && $parents[0].selectedConfigRows && $parents[0].selectedConfigRows()[0] && $parents[0].selectedConfigRows()[0].status == 1) || currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || currentViewIndex() == uiConstants.common.CLONE_VIEW -->
					<button id="modalNotificationProfile" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: switchModal.bind($data, 'notificationProfile')}" data-toggle="modal" data-target="#idModalAlertProfile" title="Add Notification Profile"></button>
				<!-- /ko-->
			</div>

			<!-- ko if: slugify(alertProfileTypeStr()) == 'transaction' -->
				<div class="form-group form-required">
					<label class="control-label col-sm-2">Application <span class="mandatoryField">*</span></label>
					<div class="col-sm-4">
						<select class="chosen form-control" id="txnApplicationsList" data-bind="foreach : txnApplicationsArr" data-placeholder=" ">
							<!-- ko if: $index() == 0 -->
								<option data-bind="value: 0, text: uiConstants.common.SELECT_APPLICATION"></option>
							<!-- /ko-->

							<option data-bind="value: $data.applicationId, text: $data.applicationName"></option>
						</select>
					</div>
				</div>
			<!-- /ko -->

			<div class="form-group">
				<label class="control-label col-sm-2">Tags</label>
				<div class="col-sm-4">
					<input type="text" class="form-control tokenfield" id="alert-profile-tokenfield-typeahead" data-bind="enable: enableConfig(), value: tags">
				</div>
			</div>

			<div class="form-group" data-bind="visible : currentViewIndex() != 1 && currentViewIndex() != 4">
				<label class="control-label col-sm-2" >Status</label>
				<div class="col-sm-4">
					<!-- <input type="checkbox" id="configStatus" data-bind="checked: configStatus"> -->

					<input type="checkbox" id="configStatus" data-on-color="success" data-off-color="danger" data-size="mini" name="configStatus">
				</div>
			</div>

			<!-- ko if: currentViewIndex() != uiConstants.common.READ_VIEW && displayAlertProfileBlocks() -->
				<button id="btnCoverageWindow" type="button" class="btn" style="margin-bottom: 6px;" data-bind="event:{click: addCoverageWindow.bind($data, true)}">Add Coverage Window</button>
			<!-- /ko -->
			
			<!-- ko foreach: coverageWindowDetArr() -->
				<!-- ko if: !$data.hidden -->
					<div id="divCoverageWindowDet" class="form-group">
						<div class="panel panel-default inner-panel">
							<div class="configPanel panel-heading"><input style="width: 90%; border-color: #4D6E75; background-color: #4D6E75 !important; border-style: solid;" type="text" data-bind="attr: {id: 'coverageWindowTitle'+$index()}">
								<span class="glyphicon glyphicon-remove" data-bind="attr: {id: 'coverageRemove'+$index()}, event:{click: function(){$parent.deleteCoverageWindow($index())}}" title="Delete" style="top: 2px; float: right;"></span>
							</div>
							<div class="panel-body" style="padding-bottom: 0px">
								<div class="form-group">
									<label class="control-label col-sm-2">Time Profile <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<select class="chosen form-control" data-bind="event: {'chosen:showing_dropdown': $parents[0].onTimeProfileListOpen.bind($data, $index(), 'timeProfileList')}, attr: {id: 'timeProfileList' + $index()}, foreach : $parents[0].timeProfileListsArr()[$index()]" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_TIME_PROFILE"></option>
											<!-- /ko-->

											<option data-bind="value: $data.id, text: $data.name"></option>
										</select>
									</div>

									<!-- ko if: ($parents[0].currentViewIndex() == uiConstants.common.EDIT_VIEW && $parents[0].selectedConfigRows && $parents[0].selectedConfigRows()[0] && $parents[0].selectedConfigRows()[0].status == 1) || $parents[0].currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || $parents[0].currentViewIndex() == uiConstants.common.CLONE_VIEW -->
										<button id="modalTimeProfile" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parents[0].switchModal.bind($data, 'timeProfile')}" data-toggle="modal" data-target="#idModalAlertProfile" title="Add Time Profile"></button>
									<!-- /ko-->
								</div>

								<div class="form-group">
									<label class="control-label col-sm-2">Severity Profile <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<select class="chosen form-control" data-bind="event: {'chosen:showing_dropdown': $parents[0].onSeverityProfileListOpen.bind($data, $index(), 'severityProfileList')}, attr: {id: 'severityProfileList' + $index()}, foreach : $parents[0].severityProfileListsArr()[$index()]" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_SEVERITY_PROFILE"></option>
											<!-- /ko-->

											<option data-bind="value: $data.id, text: $data.name"></option>
										</select>
									</div>

									<!-- ko if: ($parents[0].currentViewIndex() == uiConstants.common.EDIT_VIEW && $parents[0].selectedConfigRows && $parents[0].selectedConfigRows()[0] && $parents[0].selectedConfigRows()[0].status == 1) || $parents[0].currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || $parents[0].currentViewIndex() == uiConstants.common.CLONE_VIEW -->
										<button id="modalSeverityProfile" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parents[0].switchModal.bind($data, 'severityProfile')}" data-toggle="modal" data-target="#idModalAlertProfile" title="Add Severity Profile"></button>
									<!-- /ko-->
								</div>

								<div class="form-group">
									<label class="control-label col-sm-2">Escalation Profile <span class="mandatoryField">*</span></label>
									<div class="col-sm-4">
										<select class="chosen form-control" data-bind="event: {'chosen:showing_dropdown': $parents[0].onEscalationProfileListOpen.bind($data, $index(), 'escalationProfileList')}, attr: {id: 'escalationProfileList' + $index()}, foreach : $parents[0].escalationProfileListsArr()[$index()]" data-placeholder=" ">
											<!-- ko if: $index() == 0 -->
												<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_ESCALATION_PROFILE"></option>
											<!-- /ko-->

											<option data-bind="value: $data.id, text: $data.name"></option>
										</select>
									</div>


									<!-- ko if: ($parents[0].currentViewIndex() == uiConstants.common.EDIT_VIEW && $parents[0].selectedConfigRows && $parents[0].selectedConfigRows()[0] && $parents[0].selectedConfigRows()[0].status == 1) || $parents[0].currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || $parents[0].currentViewIndex() == uiConstants.common.CLONE_VIEW -->


										<button id="modalEscalationProfile" type="button" class="glyphicon glyphicon-plus modal-invoker-btn" data-bind="event:{click: $parents[0].switchModal.bind($data, 'escalationProfile')}" data-toggle="modal" data-target="#idModalAlertProfile" title="Add Escalation Profile"></button>
									<!-- /ko-->
								</div>

								<!-- ko if: slugify($parents[0].alertProfileTypeStr()) != 'transaction' -->
									<label>Threshold Details</label>

									<div class="form-group">
										<div class="panel panel-default inner-panel" style="margin-top: 10px;">
											<div class="panel-body">
												<!-- ko if: $parents[0].displayAlertProfileBlocks -->
												<div class="form-group">
													<div class="col-sm-12" style="display: inline-flex;">
														<div class="col-sm-3">
															<label style="white-space: nowrap; margin-right: 5px;">Search by</label>
															<select class="chosen form-control" data-bind="attr: {id: 'compInstSearchByList' + $index()}, options: $parents[0].compInstSearchByArr, optionsCaption: uiConstants.common.SELECT, event:{ change: function(){$parents[0].onCompInstSearchByChange($index())}}"></select>
														</div>

														<div class="col-sm-4" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] == 1">
															<label style="white-space: nowrap;">Application Type</label>
															<select class="chosen form-control" data-bind="event:{change: function(){$parents[0].onApplicationTypeChange($index())}}, attr: {id: 'applicationTypesList' + $index()}, foreach : $parents[0].applicationTypesArr()[$index()]" data-placeholder=" ">
																<!-- ko if: $index() == 0 -->
																	<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																<!-- /ko-->
																<option data-bind="value: $data.masterId, text: $data.name"></option>
															</select>
														</div>

														<div class="col-sm-5" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] == 1">
															<label style="white-space: nowrap;">Application</label>
															<select class="chosen form-control" data-bind="event:{change: function(){$parents[0].onSearchNameChange($index())}}, attr: {id: 'applicationNamesList' + $index()}, foreach : $parents[0].applicationNamesArr()[$index()]" data-placeholder=" ">
																<!-- ko if: $index() == 0 -->
																	<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																<!-- /ko-->
																<option data-bind="value: $data.applicationId, text: $data.applicationName"></option>
															</select>
														</div>

														<div class="col-sm-4" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] == 2">
															<label style="white-space: nowrap;">Component</label>
															<select class="chosen form-control" data-bind="event:{change: function(){$parents[0].onSearchNameChange($index())}}, attr: {id: 'compNamesList' + $index()}, foreach : $parents[0].componentNamesArr()[$index()]" data-placeholder=" ">
																<!-- ko if: $index() == 0 -->
																	<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																<!-- /ko-->
																<option data-bind="value: $data.componentId, text: $data.componentName"></option>
															</select>
														</div>

														<div class="col-sm-4" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] == 3">
															<label style="white-space: nowrap;"></label>
															<input type="text" class="form-control" data-bind="attr: {id: 'txtTag' + $index()}" placeholder="Enter Tag" max="45" pattern="[a-zA-Z0-9_ ]{2,45}" autofocus required>
														</div>

														<div class="col-sm-4" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] == 4">
															<label style="white-space: nowrap;">Cluster</label>
															<select class="chosen form-control" data-bind="event:{change: function(){$parents[0].onSearchNameChange($index())}}, attr: {id: 'clusterNamesList' + $index()}, foreach : $parents[0].clusterNamesArr()[$index()]" data-placeholder=" ">
																<!-- ko if: $index() == 0 -->
																	<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																<!-- /ko-->
																<option data-bind="value: $data.clusterId, text: $data.clusterName"></option>
															</select>
														</div>

														<button type="button" class="glyphicon glyphicon-search" style="margin: auto 5px; top: 8px;" data-bind="visible: $parents[0].searchCriteriaFlag()[$index()] != 0, attr: {id: 'btnSrch'+$index(), title: uiConstants.common.SEARCH}, event:{click: function(){$parents[0].searchComponentInstances($index())}}"></button>
													</div>
												</div>


												<div class="form-group">
													<label class="control-label col-sm-2">Component Instances/Clusters <span class="mandatoryField">*</span></label>
													<div class="col-sm-4" data-bind="visible: currentViewIndex() != uiConstants.common.READ_VIEW" >
														<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {id: 'selAllAvailCompInst'+$index(), disabled: $parent.availableCompInstArr()[$index()].length == 0}, click:  $parent.handleCompInstSelAll.bind($data,$index())"> Select All</input></label>

														<div class="inner-div-container" data-bind="attr: {id : 'availableCompInstList'+$index()}" style="height: 150px;"></div>

													</div> 
												</div>

												<div>
								    				<button type="button" class="btn" data-bind="event:{click: function(){$parent.getKpis($index(), null, null)}}" style="margin-bottom: 5px; margin-left: 5px;">Show KPIs</button>
												</div>

												<div style="display: none">
													<span data-bind="attr: {id: 'compInstId'+$index()}"></span>
													<span data-bind="attr: {id: 'compInstName'+$index()}"></span>
													<span data-bind="attr: {id: 'coverageId'+$index()}"></span>
												</div>
												
												<div class="form-group" style="margin-left: 5px; margin-right: 5px;">
													<div class="configPanel panel-heading">Threshold Settings - KPIs
													</div>

													<div data-bind="attr: {id: 'compKpiThresholdSettings'+$index()}" class="panel-body inner-div-container-fixed" style="padding-bottom: 0px; margin-bottom: 15px;">
														<div class="col-sm-12 wrapper-scroll-table" style="height: 200px;">
															<table data-bind="attr:{class:'alertProfileKpiCoverageDet'+$index()+' table table-hover table-striped table-sm table-bordered'}" style="width:100%">
																<thead>
																	<tr class="a1-inner-table-thead">
																		<th class="actionControl"><input type="checkbox" data-bind="attr: {id: 'chkboxHeaderKpi'+$index()}, click:  $parent.handleKpiSelAll.bind($data,$index())" title="Select All"/></th>
																		<th class="col-sm-4">KPI</th>
																		<th class="col-sm-2">Threshold Operator <span class="mandatoryField">*</span></th>
																		<th class="col-sm-2">High</th>
																		<!-- ko if: slugify($parents[0].alertProfileTypeStr()) == 'core' -->
																			<th class="col-sm-2">Medium</th>
																			<th class="col-sm-2">Low</th>
																		<!-- /ko -->
																		<th style="display: none"></th>
																	</tr>
																</thead>

																<tbody data-bind="foreach: $parents[0].kpisArr()[$index()] ">

																	<!-- ko if : $data.kpiName-->
																	<tr>
																		<td style="text-align:center">	<input type="checkbox" data-bind="attr: {class: 'chkboxCol'+$parentContext.$index(), id: 'chkboxCol'+$parentContext.$index()+$index()}, click: $parents[1].handleKpiSelCol.bind($data,$parentContext.$index())" title="Select"/>
																		</td>
																		<td data-bind="attr:{id: 'kpiCol'+$parentContext.$index()+$index()}, text: $data.kpiName"></td>
																		<td>
																			<select class="chosen form-control kpiChosen" data-bind="attr: {id: 'thresholdOperationsList' + $parentContext.$index() + $index()},foreach : $parents[1].thresholdOperationsArr">
																				<!-- ko if: $index() == 0 -->
																					<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_THRESHOLD_OPERATION"></option>
																				<!-- /ko-->

																				<option data-bind="value: $data.masterId, text: $data.name"></option>
																			</select>
																		</td>
																		<td>
																			<input type="number" class="form-control" data-bind="attr: {id: 'highThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">
																		</td>
																		<!-- ko if: slugify($parents[1].alertProfileTypeStr()) == 'core' -->
																			<td>
																				<input type="number" class="form-control" data-bind="attr: {id: 'mediumThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">
																			</td>
																			<td>
																				<input type="number" class="form-control" data-bind="attr: {id: 'lowThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">

																			</td>
																		<!-- /ko -->
																		<td style="display: none"><span data-bind="attr:{id: 'kpiId' + $parentContext.$index() + $index()}, text: 'kpiId'+$data.kpiId+'txt'"></span>
																		<span data-bind="attr:{id: 'defaultThresholdId' + $parentContext.$index() + $index()}, text: $data.thresholdOperationId"></span>
																		<span data-bind="attr:{id: 'highThresholdVal' + $parentContext.$index() + $index()}, text: $data.highThreshold"></span>
																		<span data-bind="attr:{id: 'mediumThresholdVal' + $parentContext.$index() + $index()}, text: $data.mediumThreshold"></span>
																		<span data-bind="attr:{id: 'lowThresholdVal' + $parentContext.$index() + $index()}, text: $data.lowThreshold"></span></td>
																	</tr>
																	<!-- /ko -->
																</tbody>
															</table>
														</div>
													</div>


													<div class="configPanel panel-heading">Threshold Settings - KPI Groups
													</div>

													<div data-bind="attr: {id: 'compKpiGroupThresholdSettings'+$index()}" class="panel-body inner-div-container-fixed" style="padding-bottom: 0px">
														<div class="col-sm-12 wrapper-scroll-table" style="height: 200px;">
															<table data-bind="attr:{class:'alertProfileKpiCoverageDet'+$index()+' table table-hover table-striped table-sm table-bordered'}"   style="width:100%">
																<thead>
																	<tr class="a1-inner-table-thead">
																		<th class="actionControl"><input type="checkbox" data-bind="attr: {id: 'chkboxHeaderKpiGrp'+$index()}, click:  $parent.handleKpiGrpSelAll.bind($data,$index())" title="Select All"/></th>
																		<th class="col-sm-4">KPI</th>
																		<th class="col-sm-2">Threshold Operator <span class="mandatoryField">*</span></th>
																		<th class="col-sm-2">High</th>
																		<!-- ko if: slugify($parents[0].alertProfileTypeStr()) == 'core' -->
																			<th class="col-sm-2">Medium</th>
																			<th class="col-sm-2">Low</th>
																		<!-- /ko -->
																		<th style="display: none"></th>
																	</tr>
																</thead>


																<tbody data-bind="foreach: $parents[0].kpiGroupsArr()[$index()]">
																	<!-- ko if : $data.kpiGroupName-->

																	<tr>
																		<td data-bind="attr: {colspan: slugify($parents[1].alertProfileTypeStr()) == 'core' ? '7' : '4'}">
																			<label>KPI Group: </label>
																			<span data-bind="text: $data.kpiGroupName"></span>
																		</td>
																	</tr>
																	<!-- ko foreach: $data.kpiGroupDetails -->
																	<tr>
																		<td style="text-align:center">	<input type="checkbox" data-bind="attr: {class: 'chkboxColGrp'+$parentContext.$parentContext.$index(), id: 'chkboxColGrp'+$parentContext.$parentContext.$index()+$parentContext.$index()+$index()}, click: $parents[2].handleKpiGrpSelCol.bind($data,$parentContext.$parentContext.$index())" title="Select"/>
																		</td>
																		<td data-bind="attr:{id: 'kpiGrpCol'+$parentContext.$parentContext.$index()+$parentContext.$index()+$index()}, text: $data.kpiName"></td>
																		<td>
																			<select class="chosen form-control kpiGrpChosen" data-bind="attr: {id: 'thresholdOperationsListKpiGrp' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, foreach : $parents[2].thresholdOperationsArr">
																				<!-- ko if: $index() == 0 -->
																					<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_THRESHOLD_OPERATION"></option>
																				<!-- /ko-->

																				<option data-bind="value: $data.masterId, text: $data.name"></option>
																			</select>
																		</td>
																		<td>
																			<input type="number" class="form-control" data-bind="attr: {id: 'highThresholdKpiGrp' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">
																		</td>

																		<!-- ko if: slugify($parents[2].alertProfileTypeStr()) == 'core' -->
																			<td>
																				<input type="number" class="form-control" data-bind="attr: {id: 'mediumThresholdKpiGrp' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">
																			</td>
																			<td>
																				<input type="number" class="form-control" data-bind="attr: {id: 'lowThresholdKpiGrp' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">
																			</td>
																		<!-- /ko -->
																		<td style="display: none">	<span data-bind="attr:{id: 'kpiGroupId' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: 'kpiId'+$data.kpiId+'txt'"></span>
																		<span data-bind="attr:{id: 'kpiGroupMainId' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: 'kpiGrpId'+$parentContext.$data.kpiGroupId+'txt'"></span>


																		<span data-bind="attr:{id: 'defaultThresholdGrpId' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: $data.thresholdOperationId"></span>
																		<span data-bind="attr:{id: 'highGrpThresholdVal' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: $data.highThreshold"></span>
																		<span data-bind="attr:{id: 'mediumGrpThresholdVal' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: $data.mediumThreshold"></span>
																		<span data-bind="attr:{id: 'lowGrpThresholdVal' + $parentContext.$parentContext.$index() + $parentContext.$index() + $index()}, text: $data.lowThreshold"></span>


																		</td>
																	</tr>
																	<!-- /ko -->
																	<!-- /ko -->
																</tbody>
															</table>
														</div>
													</div>
												</div>

												<button class="btn-small" data-bind="attr: {id: 'btnAdd'+$index()}, event:{click: function(){$parents[0].addCompInstKpiMap($index())}}" type ="button">Add</button>
												<button class="btn-small" data-bind="attr: {id: 'btnUpdate'+$index()}, event:{click: function(){$parents[0].updateCompInstKpiMap($index())}}, visible: false" type ="button">Update</button>
												<button class="btn-small" data-bind="attr: {id: 'btnReset'+$index()}, event:{click: function(){$parents[0].resetCompInstKpiMap($index())}}" type ="button">Reset</button>
												<button class="btn-small" data-bind="attr: {id: 'btnCancel'+$index()}, event:{click: function(){$parents[0].cancelCompInstKpiMap($index())}}, visible: false" type ="button">Cancel</button>

												<button style="visibility: hidden" class="btn-small" data-bind="attr: {id: 'btnDefault'+$index()}, event:{click: function(){$parents[0].setDefaultThreshold($index())}}" type ="button">Set Default Threshold</button>
												<!-- /ko -->
												<table class="table table-hover table-striped table-sm table-bordered" style="width:100%">
													<thead>
														<tr>
															<th col-sm-5>Component Instance</th>
															<th col-sm-5>KPI with Thresholds</th>

															<!-- ko if: $parents[0].currentViewIndex() != uiConstants.common.READ_VIEW && $parents[0].displayAlertProfileBlocks() -->
																<th class="col-xs-1"></th>
															<!-- /ko -->
														</tr>
													</thead>

													<tbody data-bind="foreach: $parents[0].compInstKpiMapArr()[$index()]">
														<tr>
															<td class="textOverflowOmmiter" data-bind="text: $data.componentInstanceName"></td>

															<td class="textOverflowOmmiter" data-bind="text: $parents[1].getKpiNames($data), attr:{title: $parents[1].getKpiNames($data)}" data-toggle="tooltip"></td>

															<!-- ko if: $parents[1].currentViewIndex() != uiConstants.common.READ_VIEW && $parents[1].displayAlertProfileBlocks()-->
																<td style="text-align:center">
																	<button type="button" class="glyphicon glyphicon-edit buttonedit" title="Edit" data-bind="event:{click: function(){$parents[1].onKpiEdit($parentContext.$index(), $index(), $data)}}"></button>
																	<button type="button" class="glyphicon glyphicon-remove buttondelete" title="Delete" data-bind="event:{click: function(){$parents[1].onKpiDelete($parentContext.$index(), $index(), $data)}}"></button>
																</td>
															<!-- /ko -->
														</tr>
													</tbody>
												</table>
											</div>
										</div>
									</div>
								<!-- /ko -->

								<!-- ko if: slugify($parents[0].alertProfileTypeStr()) == 'transaction' -->
									<div class="form-group form-required" data-bind="attr: {'id': 'divTxn'+$index()">

										<div style="display: none">
											<span data-bind="attr: {id: 'coverageId'+$index()}"></span>
										</div>
												
										<label class="control-label col-sm-2">Transactions <span class="mandatoryField">*</span></label>
										<div data-bind="attr: {'id': 'txnDiv'+$index()}" class="col-sm-6">
											<table class="checklist-div-table">
												<tr>
													<td style="width: 270px">
														<label style="margin-left: 5px;">Available:</label>
														<div data-bind="visible: $parents[0].currentViewIndex() != uiConstants.common.READ_VIEW || $parents[0].displayAlertProfileBlocks()">
															<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': 'selAllAvailTxn'+$index(), disabled: $parents[0].availableTxnArr().length == 0}">Select All</input></label>
														</div>
													</td>

													<td style="width: 40px"></td>

													<td style="width: 270px">
														<label style="margin-left: 5px;">Selected:</label>
														<div data-bind="visible: $parents[0].currentViewIndex() != uiConstants.common.READ_VIEW || $parents[0].displayAlertProfileBlocks()">
															<label style="font-weight: normal;"><input type="checkbox" style="margin-left: 5px;" data-bind="attr: {'id': 'selAllSelTxn'+$index(), disabled: $parents[0].selectedTxnArr().length == 0}">Select All</input></label>
														</div>
													</td>
												</tr>

												<tr>
													<td>
														<div class="inner-div-container" data-bind="attr: {'id': 'availableTxnList'+$index()}"></div>
													</td>

													<td style="padding: 5px;">
														<button type="button" class="glyphicon glyphicon-triangle-right" style="margin-bottom: 5px" data-bind="attr: {disabled: !$parents[0].displayAlertProfileBlocks(), title: uiConstants.common.ADD_TO_SELECTED_LIST}, css: {confButtonDisabled: !$parents[0].enableAddTxnBtn()[$index()]}, event:{click: $parents[0].addToSelectedTxn.bind($data, $index())}"></button>
														<button type="button" class="glyphicon glyphicon-triangle-left" style="margin-top: 5px" data-bind="attr: {disabled: !$parents[0].displayAlertProfileBlocks(), title: uiConstants.common.ADD_TO_AVAILABLE_LIST}, css: {confButtonDisabled: !$parents[0].enableRemoveTxnBtn()[$index()]}, event:{click: $parents[0].addToAvailableTxn.bind($data, $index())}"></button>
													</td>

													<td>
														<div class="inner-div-container" data-bind="attr: {'id': 'selectedTxnList'+$index()}"></div>
													</td>
												</tr>
											</table>
										</div>
									</div>




									<div class="form-group form-required">
										<label class="control-label col-sm-2">Alert on</label>
									    <!-- ko foreach: $parents[0].txnThresholdTypesArr() -->
										    <label class="config-option-label" style="margin-left: 20px;">
			      								<input type="radio" data-bind="value: $data.masterId, attr: {name: 'txnThresholdType'+$parentContext.$index(), 'checked': $index() == 0 ? 'checked' : false}"> <span data-bind="text: $data.name"></span>
			   								</label>
		   								<!-- /ko-->
									</div>




									<div class="form-group" style="margin-left: 5px; margin-right: 5px;">
										<div class="configPanel panel-heading">Threshold Settings
										</div>

										<div data-bind="attr: {id: 'coverageThreshold'+$index()}" class="panel-body inner-div-container-fixed" style="padding-bottom: 0px; margin-bottom: 15px;">

											<!-- ko if: $parents[0].txnType() && $parents[0].txnType()[$index()] == 'volume' -->
												<div class="form-group form-required">
													<label class="control-label col-sm-2">Operation <span class="mandatoryField">*</span></label>
													<div class="col-sm-4">
														<select class="chosen form-control txnThresholdOperationsChosen" data-bind="attr: {id: 'txnThresholdOperationsList' + $index()}, foreach : $parents[0].thresholdOperationsArr">
															<!-- ko if: $index() == 0 -->
																<option data-bind="value: 0, text: uiConstants.alertProfile.SELECT_THRESHOLD_OPERATION"></option>
															<!-- /ko-->

															<!-- ko if: slugify($data.name).startsWith('greater') -->
																<option data-bind="value: $data.masterId, text: $data.name"></option>
															<!-- /ko-->
														</select>
													</div>
												</div>

												<div class="col-sm-12 wrapper-scroll-table" style="height: 350px;">
													<table data-bind="attr:{class:'alertProfileKpiCoverageDet'+$index()+' table table-hover table-striped table-sm table-bordered'}" style="width:100%">
														<thead>
															<tr class="a1-inner-table-thead">
																<th class="col-sm-6">Transactions <span class="mandatoryField">*</span></th>
																<th class="col-sm-6">Thresholds (High) <span class="mandatoryField">*</span></th>
															</tr>
														</thead>


														<tbody data-bind="foreach: $parents[0].txnVolumeDetailsArr()[$index()] ">

															<tr>
																<td data-bind="attr:{id: 'txnCol'+$parentContext.$index()+$index()}, text: $data.transactionName"></td>
																<td>
																	<div data-bind="attr: {class: $parents[1].displayAlertProfileBlocks() ? 'input-group' : ''}">
																		<input type="number" class="form-control" data-bind="value: $data.highThreshold, attr: {id: 'highThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0">

																		<!-- ko if: $parents[1].displayAlertProfileBlocks() -->
			      															<span class="input-group-btn" style="padding-left: 3px;">
																				<img src="/images/calculator_16x16.png" style="cursor: pointer;" title="Calculate Threshold" data-toggle="modal" data-target="#idModalAlertProfile" data-bind="event: {click: $parents[1].onThresholdCalcClick.bind($data, $parentContext.$index(), $index())}">
																			</span>
																		<!-- /ko -->
																	</div>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											<!-- /ko -->

											<!-- ko if: $parents[0].txnType() && $parents[0].txnType()[$index()] == 'status' -->
												<div class="col-sm-12">
													<!-- ko if: $parents[0].currentViewIndex() != uiConstants.common.READ_VIEW && $parents[0].displayAlertProfileBlocks() -->
														<button type="button" class="btn" style="margin-bottom: 6px;" data-bind="event:{click: $parents[0].addTxnStatus.bind($data, $index())}, attr: {id: 'btnStatus'+$index()}">Add Status</button>

														<button style="float: right" type="button" class="btn" style="margin-bottom: 6px;" data-bind="attr: {id: 'deleteStatus'+$index()},event:{click: $parents[0].deleteTxnStatus.bind($data, $index())}" disabled>Delete</button>
													<!-- /ko -->
												</div>

												<div class="col-sm-12 wrapper-scroll-table" style="height: 300px;">
													<table data-bind="attr:{class:'alertProfileKpiCoverageDet'+$index()+' table table-hover table-striped table-sm table-bordered'}" style="width:100%">
														<thead>
															<tr class="a1-inner-table-thead">
																<th class="actionControl" rowspan="2"><input type="checkbox" data-bind="attr: {id: 'chkboxHeaderTxn'+$index(), disabled: !($parent.txnStatusDetailsArr()[$index()] && $parent.txnStatusDetailsArr()[$index()].length)}, click:  $parent.handleTxnSelAll.bind($data,$index())" title="Select All"/></th>
																<th class="col-sm-2" rowspan="2">Status Type <span class="mandatoryField">*</span></th>
																<th class="col-sm-2" rowspan="2">Operation <span class="mandatoryField">*</span></th>
																<th class="col-sm-2" rowspan="2">Response Time Type <span class="mandatoryField">*</span></th>
																<th class="col-sm-6" colspan="3">Thresholds <span class="mandatoryField">*</span></th>
															</tr>
															<tr class="a1-inner-table-thead">
																<th class="col-sm-4">High(%)</th>
																<th class="col-sm-4">Medium(%)</th>
																<th class="col-sm-4">Low(%)</th>
															</tr>
														</thead>

														<tbody data-bind="foreach: $parents[0].txnStatusDetailsArr()[$index()] ">

															<tr>
																<td style="text-align:center">	<input type="checkbox" data-bind="value: $index(), attr: {class: 'chkboxCol'+$parentContext.$index(), id: 'chkboxCol'+$parentContext.$index()+$index()}, click: $parents[1].handleTxnSelCol.bind($data,$parentContext.$index())" title="Select"/>
																</td>
																<td>
																	<select class="chosen form-control txnStatusChosen" data-bind="attr: {id: 'txnStatus' + $parentContext.$index() + $index()},foreach : $parents[1].txnStatusArr, event: {change: $parents[1].onTxnStatusChange.bind($data, $parentContext.$index(), $index(), true)}">
																		<!-- ko if: $index() == 0 -->
																			<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																		<!-- /ko-->

																			<option data-bind="value: $data.masterId, text: $data.name"></option>
																	</select>
																</td>

																<td>
																	<select class="chosen form-control thresholdOperationsListChosen" data-bind="attr: {id: 'thresholdOperationsList' + $parentContext.$index() + $index()},foreach : $parents[1].thresholdOperationsArr">
																		<!-- ko if: $index() == 0 -->
																			<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																		<!-- /ko-->

																			<option data-bind="value: $data.masterId, text: $data.name"></option>
																	</select>
																</td>

																<td style="text-align: center;">
																	<span data-bind="attr: {id: 'txnResponseTypeNotApp' + $parentContext.$index() + $index()}, text: uiConstants.common.NOT_APPLICABLE"></span>
																	<select style="text-align: left; display: none" class="chosen form-control txnResponseTypeChosen" data-bind="attr: {id: 'txnResponseType' + $parentContext.$index() + $index()},foreach : $parents[1].txnResponseTypesArr, event: {change: $parents[1].onResponseTypeChange.bind($data, $parentContext.$index(), $index())}">
																		<!-- ko if: $index() == 0 -->
																			<option data-bind="value: 0, text: uiConstants.common.SELECT"></option>
																		<!-- /ko-->

																			<option data-bind="value: $data.responseTypeId, text: $data.responseTypeName"></option>
																	</select>
																</td>
																<td>
																	<input type="text" class="form-control threshold-val" data-bind="value: $data.highThreshold, attr: {id: 'highThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0" max="100">
																</td>
																<td>
																	<input type="text" class="form-control threshold-val" data-bind="value: $data.mediumThreshold, attr: {id: 'mediumThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0" max="100">
																</td>
																<td>
																	<input type="text" class="form-control threshold-val" data-bind="value: $data.lowThreshold, attr: {id: 'lowThreshold' + $parentContext.$index() + $index()}" placeholder="Enter Value" min="0" max="100">
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											<!-- /ko -->
										</div>
									</div>
								<!-- /ko -->
							</div>
						</div>
					</div>
				<!-- /ko -->
			<!-- /ko -->

			<div class="form-group">
				<div class="col-sm-offset-2 col-sm-4 divActionPanel">
					<button type ="button" class="btn btn-primary" data-bind="visible : currentViewIndex() != uiConstants.common.READ_VIEW, event:{click: addEditConfig}">Save</button>
					<button type="button" class="btn" data-bind="event:{click: cancelConfig}"><span data-bind="text:toggleCancelCloseBtn"></span></button>
				</div>
			</div>

			<div class="modal fade" id="idModalAlertProfile" tabindex="-1" role="dialog" aria-labelledby="modalLabel" aria-hidden="true" style="overflow: scroll;" data-backdrop="static" data-keyboard="false">
					<div class="modal-dialog modal-lg">
			        <div class="modal-content">
						<div class="modal-header">
			                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
			                	<h4><span data-bind="text: modalTitle"></span></h4>
							<div data-bind="if : displayComponent() == 1">
				        		<time-profile-add-edit params="{isModal:true, modalConfigName: modalConfigName}"></time-profile-add-edit>
				        	</div>

				        	<div data-bind="if : displayComponent() == 2">
				        		<severity-profile-add-edit params="{isModal:true, modalConfigName: modalConfigName, alertProfileTypesArr: alertProfileTypesArr, profileTypeId: profileTypeId}"></severity-profile-add-edit>
				        	</div>

				        	<div data-bind="if : displayComponent() == 3">
				        		<notification-content-profile-add-edit params="{isModal:true, modalConfigName: modalConfigName, alertProfileTypesArr: alertProfileTypesArr, profileTypeId: profileTypeId}"></notification-content-profile-add-edit>
				        	</div>

				        	<div data-bind="if : displayComponent() == 4">
				        		<escalation-profile-add-edit params="{isModal:true, modalConfigName: modalConfigName, alertProfileTypesArr: alertProfileTypesArr, profileTypeId: profileTypeId}"></escalation-profile-add-edit>
				        	</div>

			        		<div data-bind="if : displayComponent() == 5">

			        		</div>

			        		<div data-bind="if : displayComponent() == 6">
			        			<div><h4>Calculate Threshold</h4></div>
 	
								<div class="panel-body">
				        			<div class="form-group col-sm-6">
						          		<label class="col-sm-2" style="text-align: right; margin-top: 7px;">From </label>
							     	 	<div class='input-group date' id='calcThresholdFromDate'>    	
								            <input type='text' class="form-control" placeholder="Select date" value=""/>
								            <span id="calendarIcon" class="input-group-addon" title="Select Date/Time">
								                <span class="glyphicon glyphicon-calendar"></span>
								            </span>
								        </div>
								    </div>

								    <div class="form-group col-sm-6">
						          		<label class="col-sm-2" style="text-align: right; margin-top: 7px;">To </label>
							     	 	<div class='input-group date' id='calcThresholdToDate'>    	
								            <input type='text' class="form-control" placeholder="Select date" value=""/>
								            <span id="calendarIcon" class="input-group-addon" title="Select Date/Time">
								                <span class="glyphicon glyphicon-calendar"></span>
								            </span>
								        </div>
								    </div>

								    <button type="button" class="btn" data-bind="event:{click: calculateThreshold}">Calculate</button>

								    <span style="color: red; padding-left: 15px;" data-bind="text: calcThresholdErrMsg"></span>
							    </div>
			        		</div>
			            </div>	

			        </div>
			    </div> <!-- /.modal-content -->
			</div>
		</form>
	</div>
</div>