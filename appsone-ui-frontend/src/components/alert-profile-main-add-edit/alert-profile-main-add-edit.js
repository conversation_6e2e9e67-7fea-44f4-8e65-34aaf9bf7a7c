define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','bootstrap-switch','typeahead','text!./alert-profile-main-add-edit.html','hasher','validator','ui-constants','ui-common','checklistbox','scrollable-tooltip','bootstrap-datepicker','jQuery-plugins','fsstepper','floatThead'], function($,ko,jc,bttokenfield,bts,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,checklistbox,scrollabletooltip,btdatetimepicker,jQueryPlugins,fsstepper,floatThead) {

	function AlertProfileMainAddEdit(params) {
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.selectedConfigRows = params.selectedConfigRows;
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.configTagArr = ko.observableArray();
		this.configTagAutoCompleteArr = ko.observableArray();
		this.tags = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(true);
		this.pageSelected = params.pageSelected;
		this.alertProfileTypesArr = params.alertProfileTypesArr;
  		this.selectedConfigNames = ko.observable("");
  		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);
  		this.enableConfig = ko.observable(true);
  		this.modalTitle = ko.observable();
  		this.contentType = ko.observable();
  		this.coverageWindowDetArr = ko.observableArray();
  		this.timeProfileListsArr = ko.observableArray();
  		this.timeProfileListsCopyArr = ko.observableArray();
  		this.severityProfileListsArr = ko.observableArray();
  		this.severityProfileListsCopyArr = ko.observableArray();
  		this.notificationProfileListsArr = ko.observableArray([{}]);
  		this.escalationProfileListsArr = ko.observableArray();
  		this.escalationProfileListsCopyArr = ko.observableArray();
  		this.displayComponent = ko.observable(0);
  		this.modalConfigName = ko.observableArray("");
  		this.compInstSearchByArr = ko.observableArray(["Application","Component Name", "Tag", "Cluster"]);
  		this.applicationTypesArr = ko.observableArray();
		this.applicationNamesArr = ko.observableArray();
		this.txnApplicationsArr = ko.observableArray();
		this.txnThresholdTypesArr = ko.observableArray();
		this.txnStatusArr = ko.observableArray();
		this.txnResponseTypesArr = ko.observableArray();
		this.componentNamesArr = ko.observableArray();
		this.clusterNamesArr = ko.observableArray();
		//this.compInstanceNamesArr = ko.observableArray([{}]);
  		this.tagNameSearch = ko.observable();
  		this.searchCriteriaFlag = ko.observableArray();
  		this.kpisArr = ko.observableArray();
  		this.kpiGroupsArr = ko.observableArray();
  		this.compInstKpiMapArr = ko.observableArray();
  		this.thresholdOperationsArr = ko.observableArray();
  		this.kpiToEditData = ko.observable();
  		this.coverageWindowDetailsForId = ko.observableArray();
  		this.alertProfileTypeStr = ko.observable("");
  		this.kpiMapId = ko.observable();
  		this.unMappedCoverageIds = ko.observableArray();
  		this.thresholdMapIds = ko.observable();
  		this.configTagAutoCompleteCopyArr = ko.observableArray();
  		this.displayAlertProfileBlocks = ko.observable(!(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)));
  		this.availableTxnArr = ko.observableArray();
  		this.selectedTxnArr = ko.observableArray();
  		this.transactionsArr = ko.observableArray();
  		this.enableAddTxnBtn = ko.observableArray();
  		this.enableRemoveTxnBtn = ko.observableArray();
  		this.txnVolumeDetailsArr = ko.observableArray();
  		this.txnStatusDetailsArr = ko.observableArray();
  		this.txnType = ko.observableArray();
  		this.calcThresholdErrMsg = ko.observable("");
		this.availableCompInstArr = ko.observableArray([]);
		this.profileTypeId = ko.observable(0);
  		var coverageIndex = 0;
  		var previousTxnTypeId = "";
  		var calculateThresholdIndex = "";

		var configTagLoaded = 0;
		var timeProfilesLoaded = 0;
		var severityProfilesLoaded = 0;
		var escalationProfilesLoaded = 0;
		var notificationProfilesLoaded = 0;
		var thresholdOpsLoaded = 0;
		var txnsLoaded = 0;
		var txnStatusLoaded = 0;
		var txnRespTimeTypeLoaded = 0;
		var txnApplicationsLoaded = 0;
		//var coverageWindowDetailsLoaded = 0;
		var tagsNameArr = [];
  		var tagsToDeleteArr = [];
  		var tagsNewToAddArr = [];
  		var compInstSearchByChangeIndex = -1;
  		var operationIndex = -1;
		var listImgEle = "<i class='tip glyphicon glyphicon-info-sign' style='float: right; color: #363698;'></i>";
		var previousProfileTypeId = 0;
		var previousAppId = 0;
		var slowStatusIdArr = [];
		var slowStatusId = -1;
		var coverageAutoTitlesArr = [];
		var availTxnArr = [];
		var selKpiAllIdsArr = [];
		var selKpiGroupAllIdsArr = [];

		//var previousMasterId = 0;

		this.renderHandler = function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});




			$("#idModalAlertProfile").on('hidden.bs.modal', function () {
				//previousMasterId = $("#notificationProfileList").val();

				if(self.displayComponent() == 1){
				 	requestCall(uiConstants.common.SERVER_IP + "/coverageWindowProfiles/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getTimeProfiles", successCallback, errorCallback);
				}
				else if(self.displayComponent() == 2){
				 	requestCall(uiConstants.common.SERVER_IP + "/severityProfile/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getSeverityProfiles", successCallback, errorCallback);
				}
				else if(self.displayComponent() == 3){
					requestCall(uiConstants.common.SERVER_IP + "/notificationProfiles/mstSubType/"+previousProfileTypeId+"?status=2&markInactive=1", "GET", "", "getNotificationProfiles", successCallback, errorCallback);
				}
				else if(self.displayComponent() == 4){
				 	requestCall(uiConstants.common.SERVER_IP + "/escalationProfiles/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getEscalationProfiles", successCallback, errorCallback);
				}
				else if(self.displayComponent() == 5){
	        		requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getTxnApplications", successCallback, errorCallback);
				}

			});

			$("#idModalAlertProfile").on('shown.bs.modal', function () {
				if(self.displayComponent() == 4){
					$('#listEscalationSettings').floatThead('reflow');
				}
			});
			
			//self.selectedConfigRows = params.selectedConfigRows;

			if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}

			if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
				$('#notificationProfileList').prop('disabled', true).trigger("chosen:updated");
				$('#modalNotificationProfile').prop('disabled', true);
				$('#modalNotificationProfile').removeClass("modalInvokerDisabled").addClass("modalInvokerDisabled");
			}

			$('#alert-profile-tokenfield-typeahead')
			.on('tokenfield:createdtoken', function (e) {

				if(!($.grep(self.configTagAutoCompleteArr(), function(evt){ return evt == e.attrs.label.trim(); })[0])){
					tagsNewToAddArr.push(e.attrs.label.trim()); //Push tag label
				}
				var tagIndex = self.configTagAutoCompleteArr.indexOf(e.attrs.label.trim());
				//if(self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim()) != -1){
				if(tagIndex != -1){
					self.configTagAutoCompleteArr.splice(tagIndex, 1);
				}

				$('#alert-profile-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			})

			.on('tokenfield:edittoken', function(e){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					return false;
				}
			})

			.on('tokenfield:removedtoken', function (e) {
				if(tagsNameArr.indexOf(e.attrs.label.trim()) !=-1 && tagsNewToAddArr.indexOf(e.attrs.label.trim()) == -1){
					tagsToDeleteArr.push(e.attrs.label.trim()); //Push tag label
				}
				tagsNewToAddArr.splice(tagsNewToAddArr.indexOf(e.attrs.label.trim()), 1);
				var tagIndex = self.configTagAutoCompleteCopyArr.indexOf(e.attrs.label.trim());

				if(tagIndex != -1){
					self.configTagAutoCompleteArr.push(tagIndex, e.attrs.label.trim());
					self.configTagAutoCompleteArr.sort();
				}

				$('#alert-profile-tokenfield-typeahead').data('bs.tokenfield').$input.autocomplete(
				 	{source: self.configTagAutoCompleteArr()
				});
			});

			$(".panel-body #alertProfileTypeList").on('change', function () {
		    	if(previousProfileTypeId == 0){
			    	self.onAlertProfileTypeChange(false);
					previousProfileTypeId = $("#alertProfileTypeList").val();
			    	self.profileTypeId(previousProfileTypeId);
			    }

			    else{
		    		showMessageBox(uiConstants.alertProfile.CONFIRM_ALERT_PROFILE_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClearThresholdDetails){
						if(confirmClearThresholdDetails){
							self.onAlertProfileTypeChange(true);
							previousProfileTypeId = $("#alertProfileTypeList").val();
			    		}
			    		else{
			    			$("#alertProfileTypeList").val(previousProfileTypeId);
							$("#alertProfileTypeList").trigger("chosen:updated");

			    		}
			    		self.profileTypeId(previousProfileTypeId);
			    	});
		    	}
			});

	        requestCall(uiConstants.common.SERVER_IP + "/tag?type=AlertProfile", "GET", "", "getAlertProfileTag", successCallback, errorCallback);

			//requestCall("http://www.mocky.io/v2/5857c03d120000bb07c8ae3f?callback=?", "GET", "", "getNotificationProfiles", successCallback, errorCallback);
			

	    }

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
		        $('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        }
		});

		function clearKpiThresholdEntryContainer(indx){
			self.kpisArr.splice(indx, 1, [{}]);
			self.kpiGroupsArr.splice(indx, 1, [{}]);
			$("#btnDefault"+indx).css("visibility","hidden");
			$("#compInstId"+indx).text("");
			$("#compInstName"+indx).text("");
		}

		this.onTxnAppChange = function(){
			self.coverageWindowDetArr([]);
  			self.txnVolumeDetailsArr([]);
  			self.txnStatusDetailsArr([]);
  			self.transactionsArr([]);
  			self.availableTxnArr([]);

			//self.severityProfileListsArr([]);
				//self.timeProfileListsArr([]);
				//self.escalationProfileListsArr([]);
		}

		this.onThresholdCalcClick = function(parentIndex, rowIndex){
			calculateThresholdIndex = parentIndex+"_"+rowIndex;
			self.displayComponent(6);
			$('#calcThresholdFromDate').datetimepicker({          
				format: "YYYY-MM-DD",          
				maxDate: new Date(),
				stepping: 1,
				useCurrent: true, 
				defaultDate: new Date(),
				showTodayButton: false,          
				sideBySide: true
			});

			$('#calcThresholdToDate').datetimepicker({          
				format: "YYYY-MM-DD",          
				maxDate: new Date(),
				stepping: 1,
				useCurrent: true, 
				defaultDate: new Date(),
				showTodayButton: false,          
				sideBySide: true
			});

			$("#calcThresholdFromDate").on("dp.change", function (e) {
	            $('#calcThresholdToDate').data("DateTimePicker").minDate(e.date);
	        });

	        $("#calcThresholdToDate").on("dp.change", function (e) {
	            $('#calcThresholdFromDate').data("DateTimePicker").maxDate(e.date);
	        });
		}

		this.calculateThreshold = function(){
			self.calcThresholdErrMsg("");

			if($('#calcThresholdFromDate input').val().trim() == ""){
				self.calcThresholdErrMsg("Please select From date");
			}
			else if($('#calcThresholdToDate input').val().trim() == ""){
				self.calcThresholdErrMsg("Please select To date");
			}
			else{

				//Move this to result handler
				$("#highThreshold"+calculateThresholdIndex.split("_")[0]+calculateThresholdIndex.split("_")[1]).val(123);
				$("#idModalAlertProfile").modal("hide");
				self.displayComponent(0);
			}
		}

		this.addToSelectedTxn = function(rowIndex){
			var availArr = $('#availableTxnList'+rowIndex).getSelectedValues();

			var txnObj;
			if(uiConstants.common.DEBUG_MODE)console.log(self.availableTxnArr());

			for(arr in availArr){
				txnObj = $.grep(self.availableTxnArr()[rowIndex], function(e){ return e.id == availArr[arr]; });
				self.availableTxnArr()[rowIndex].splice(self.availableTxnArr()[rowIndex].indexOf(txnObj[0]), 1);
				self.selectedTxnArr()[rowIndex].push(txnObj[0]);
			}

			$('#selectedTxnList'+rowIndex).checklistbox({
			    data: self.selectedTxnArr()[rowIndex]
			});

			$('#availableTxnList'+rowIndex).checklistbox({
			    data: self.availableTxnArr()[rowIndex]
			});

			addTxnVolumeThresholdData(rowIndex);

			uncheckTxn(rowIndex);
			self.enableAddTxnBtn.splice(rowIndex, 1, false);
		}

		function addTxnVolumeThresholdData(rowIndex){
			var txnVolData = [];

			if(self.txnVolumeDetailsArr()[rowIndex]){
				txnVolData = self.txnVolumeDetailsArr()[rowIndex];
			}
			for(var txn in self.selectedTxnArr()[rowIndex]){
				if(!$.grep(txnVolData, function(e){ return e.transactionId == self.selectedTxnArr()[rowIndex][txn].id; }).length){
					txnVolData.push({
						"transactionId": self.selectedTxnArr()[rowIndex][txn].id,
						"transactionName": self.selectedTxnArr()[rowIndex][txn].name,
						"highThreshold": ko.observable("")
					});
				}
			}
			self.txnVolumeDetailsArr.splice(rowIndex, 1, txnVolData);
			
			console.log(self.txnVolumeDetailsArr());
		}

		this.addToAvailableTxn = function(rowIndex){
			showMessageBox("The threshold details of the transaction(s) to be unmapped will be removed. Do you want to continue?", "question", "confirm", function confirmCallback(confirmUnmap){
				if(confirmUnmap){
					var selArr = $('#selectedTxnList'+rowIndex).getSelectedValues();
					var txnObj;
					for(arr in selArr){
						txnObj = $.grep(self.selectedTxnArr()[rowIndex], function(e){ return e.id == selArr[arr]; });
						self.selectedTxnArr()[rowIndex].splice(self.selectedTxnArr()[rowIndex].indexOf(txnObj[0]), 1);
						self.availableTxnArr()[rowIndex].push(txnObj[0]);
					}

					$('#selectedTxnList'+rowIndex).checklistbox({
					    data: self.selectedTxnArr()[rowIndex]
					});

					$('#availableTxnList'+rowIndex).checklistbox({
					    data: self.availableTxnArr()[rowIndex]
					});

					var txnVolData = [];

					if(self.txnVolumeDetailsArr()[rowIndex]){
						txnVolData = self.txnVolumeDetailsArr()[rowIndex];
					}
					var addedThresholdObjArr = [];
					var addedThresholdObjIndex = -1;

					for(var txn in selArr){
						addedThresholdObjArr = $.grep(txnVolData, function(e){ return e.transactionId == selArr[txn]; });
						if(addedThresholdObjArr.length){
							addedThresholdObjIndex = txnVolData.indexOf(addedThresholdObjArr[0]);

							

							txnVolData.splice(addedThresholdObjIndex, 1);
						}
					}
					self.txnVolumeDetailsArr.splice(rowIndex, 1, txnVolData);

					uncheckTxn(rowIndex);
					self.enableRemoveTxnBtn.splice(rowIndex, 1, false);
				}
			});
		}

		function uncheckTxn(rowIndex){
			$("#selectedTxnList"+rowIndex+" .checkList").prop("checked",false);
			$("#availableTxnList"+rowIndex+" .checkList").prop("checked",false);
			$("#selAllAvailTxn"+rowIndex).prop("checked",false);
			$("#selAllSelTxn"+rowIndex).prop("checked",false);
		}

		this.onAlertProfileTypeChange = function(clearData){
			if(clearData){
				self.coverageWindowDetArr([]);
				self.severityProfileListsArr([]);
  				self.timeProfileListsArr([]);
  				self.escalationProfileListsArr([]);
  				self.txnApplicationsArr([]);
			}
			self.alertProfileTypeStr($("#alertProfileTypeList option:selected").text());
			if(slugify(self.alertProfileTypeStr()) == "transaction" && !self.txnApplicationsArr().length){
	        	requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=2&markInactive=1", "GET", "", "getTxnApplications", successCallback, errorCallback);
			}
			if(slugify(self.alertProfileTypeStr()) == "transaction" && !self.txnApplicationsArr().length){
	        	requestCall(uiConstants.common.SERVER_IP + "/transactionThreshold/type", "GET", "", "getTxnThresholdTypes", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5864e8521100009e08cf8d5c?callback=?", "GET", "", "getTxnThresholdTypes", successCallback, errorCallback);
	        	requestCall(uiConstants.common.SERVER_IP + "/transactionThreshold/status", "GET", "", "getTxnStatus", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/586647651200000207b7cc59?callback=?", "GET", "", "getTxnStatus", successCallback, errorCallback);
				requestCall(uiConstants.common.SERVER_IP + "/transactionResponseTypes?alertProfile=1", "GET", "", "getResponseTypes", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/586a1e5d1100001c08261e1f?callback=?", "GET", "", "getResponseTypes", successCallback, errorCallback);

			}
			if($("#alertProfileTypeList").val() != 0){
				requestCall(uiConstants.common.SERVER_IP + "/notificationProfiles/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getNotificationProfiles", successCallback, errorCallback);
				$('#notificationProfileList').prop('disabled', false).trigger("chosen:updated");
				$('#modalNotificationProfile').prop('disabled', false);
				$('#modalNotificationProfile').removeClass("modalInvokerDisabled");
			}
			else{

				self.notificationProfileListsArr([{}]);
				$("#notificationProfileList").trigger('chosen:updated');
				$('#notificationProfileList').prop('disabled', true).trigger("chosen:updated");
				$('#modalNotificationProfile').prop('disabled', true);
				$('#modalNotificationProfile').removeClass("modalInvokerDisabled").addClass("modalInvokerDisabled");
			}
		}

		this.getKpis = function(indx, compInstanceId, compInstName){
			clearKpiThresholdEntryContainer(indx);

			if(compInstanceId == null){
				compInstanceId = "";
				compInstName = "";
				for(var compInst in $("#availableCompInstList"+indx).getSelectedItems()){
					compInstanceId += "&id="+$("#availableCompInstList"+indx).getSelectedItems()[compInst].value;
					compInstName += ","+$("#availableCompInstList"+indx).getSelectedItems()[compInst].label;
				}
				//compInstanceId = getSelectedItems() $("#compInstanceNamesList"+indx).val();
				//compInstName = $("#compInstanceNamesList"+indx+" option:selected").text();
			}
			else{
				compInstanceId = "&id="+compInstanceId;
				compInstName = ","+compInstName;
			}
			$("#compInstId"+indx).text(compInstanceId.split("&id=").toString().substring(1));
			$("#compInstName"+indx).text(compInstName.substring(1));

			//$("#compInstanceNamesList" + indx + "_chosen span").text(compInstName);

			compInstSearchByChangeIndex = indx;
			if(compInstanceId != 0){
				requestCall(uiConstants.common.SERVER_IP + "/threshold/kpiListForInstance?"+compInstanceId.substring(1)+"&typeId=" + $("#alertProfileTypeList").val() + "&status=2&markInactive=1", "GET", "", "getKpiList", successCallback, errorCallback);					
				//requestCall("http://www.mocky.io/v2/5858f6262400003b047c5a22?callback=?", "GET", "", "getKpiList", successCallback, errorCallback);
			}
		}

		this.onApplicationTypeChange = function(indx){
			self.availableCompInstArr.splice(indx, 1, []);
			self.applicationNamesArr.splice(indx, 1, [{}]);
			$('#availableCompInstList'+indx).checklistbox({
			    data: self.availableCompInstArr()[indx]
			});
	        requestCall(uiConstants.common.SERVER_IP + "/applications/applicationTypeId/"+$("#applicationTypesList"+indx).val()+"?status=1", "GET", "", "getApplications", successCallback, errorCallback);
		}

		this.onSearchNameChange = function(indx){
			self.availableCompInstArr.splice(indx, 1, []);
			$('#availableCompInstList'+compInstSearchByChangeIndex).checklistbox({
			    data: self.availableCompInstArr()[compInstSearchByChangeIndex]
			});
			//$("#compInstanceNamesList"+indx).val("0").trigger("chosen:updated");

			self.searchComponentInstances(indx);
		}

		/*this.onSetKpiThresholds = function(indx){
			clearKpiThresholdEntryContainer(indx);
			//self.searchComponentInstances();
		}*/

		this.onCompInstSearchByChange = function(indx){
			debugger;
			$("#btnSrch"+indx).css("visibility", "hidden");

			$('#availableCompInstList'+indx).checklistbox({
			    data: self.availableCompInstArr()[indx]
			});
			compInstSearchByChangeIndex = indx;
        	$("#compNamesList"+indx).val("0").trigger("chosen:updated");
        	$("#applicationNamesList"+indx).val("0").trigger("chosen:updated");
        	$("#clusterNamesList"+indx).val("0").trigger("chosen:updated");
        	$("#txtTag"+indx).val("");
        	if($("#compInstSearchByList"+indx+" option:selected").text() == self.compInstSearchByArr()[0]){
				self.searchCriteriaFlag.splice(indx, 1, 1);
				if(self.applicationTypesArr()[indx].length == 1){
			 		requestCall(uiConstants.common.SERVER_IP + "/masterTypes/application?status=1", "GET", "", "getApplicationTypes", successCallback, errorCallback);
	        		//requestCall(uiConstants.common.SERVER_IP + "/applicationsName?status=1", "GET", "", "getApplications", successCallback, errorCallback);
				}
			}
			else if($("#compInstSearchByList"+indx+" option:selected").text() == self.compInstSearchByArr()[1]){
				self.searchCriteriaFlag.splice(indx, 1, 2);
				if(self.componentNamesArr()[indx].length == 1){
					requestCall(uiConstants.common.SERVER_IP + "/component?status=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/572b335d1300003b0ae2b7f9?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
				}
			}
			else if($("#compInstSearchByList"+indx+" option:selected").text() == self.compInstSearchByArr()[2]){
				$("#btnSrch"+indx).css("visibility", "visible");
				self.searchCriteriaFlag.splice(indx, 1, 3);
			}
			else if($("#compInstSearchByList"+indx+" option:selected").text() == self.compInstSearchByArr()[3]){
				self.searchCriteriaFlag.splice(indx, 1, 4);
				if(self.clusterNamesArr()[indx].length == 1){
					requestCall(uiConstants.common.SERVER_IP + "/threshold/clusterListView?status=1", "GET", "", "getClusters", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/572b335d1300003b0ae2b7f9?callback=?", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
				}
			}
			else{
				self.searchCriteriaFlag.splice(indx, 1, 0);
			}
        }

        this.searchComponentInstances = function(indx){
			compInstSearchByChangeIndex = indx;
			self.kpisArr.splice(indx, 1, [{}]);
			self.kpiGroupsArr.splice(indx, 1, [{}]);
			$("#btnDefault"+indx).css("visibility","hidden");

        	if(self.searchCriteriaFlag()[indx] == 1){
        		var applicationId = $("#applicationNamesList"+indx).val(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?applicationId=" + applicationId + "&isCluster=2", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/57308b551200000a0786387a?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        	else if(self.searchCriteriaFlag()[indx] == 2){
        		var componentId = $("#compNamesList"+indx).val(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?componentId=" + componentId + "&isCluster=2", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/57308b551200000a0786387a?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        	else if(self.searchCriteriaFlag()[indx] == 3){
        		if($("#txtTag"+indx).val().trim() == ""){
        			showMessageBox("Please enter tag to search");
        		}
        		else{
	        		var tagName = $("#txtTag"+indx).val(); //Send this to REST API
	        		requestCall(uiConstants.common.SERVER_IP + "/componentInstances?tagName=" + tagName + "&isCluster=2", "GET", "", "getCompInstanceList", successCallback, errorCallback);
	        		//requestCall("http://www.mocky.io/v2/575656150f0000090d2eff75?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
	        	}
        	}
        	else if(self.searchCriteriaFlag()[indx] == 4){
        		var clusterId = $("#clusterNamesList"+indx).val(); //Send this to REST API
        		requestCall(uiConstants.common.SERVER_IP + "/threshold/compInstancesListView/" + clusterId, "GET", "", "getCompInstanceList", successCallback, errorCallback);
        		//requestCall("http://www.mocky.io/v2/575656500f0000170d2eff76?callback=?", "GET", "", "getCompInstanceList", successCallback, errorCallback);
        	}
        }

		this.switchModal = function(modalScreen){
			self.displayComponent(0);
			if(modalScreen == "timeProfile"){
				self.displayComponent(1);
				self.modalTitle("Add Time Profile");
			}
			else if(modalScreen == "severityProfile"){
				self.displayComponent(2);
				self.modalTitle("Add Severity Profile");
			}
			else if(modalScreen == "notificationProfile"){
				self.displayComponent(3);
				self.modalTitle("Add Notification Profile");
				//self.modalCompType("hostModal");
			}
			else if(modalScreen == "escalationProfile"){
				self.displayComponent(4);
				self.modalTitle("Add Escalation Profile");
				//self.modalCompType("hostModal");
			}
		}

		this.getKpiNames = function(kpiData){
			var kpiNames = "";
			var thresholdValues = "";
			for(var kpi in kpiData.kpiThresholdDetails){
				thresholdValues="";
				kpiNames += ", " + kpiData.kpiThresholdDetails[kpi].kpiName;
				if(kpiData.kpiThresholdDetails[kpi].hasOwnProperty("highThreshold") && kpiData.kpiThresholdDetails[kpi].highThreshold != null && kpiData.kpiThresholdDetails[kpi].highThreshold != ""){
					thresholdValues += ", High:" + kpiData.kpiThresholdDetails[kpi].highThreshold+", ";
				}
				if(kpiData.kpiThresholdDetails[kpi].hasOwnProperty("mediumThreshold") && kpiData.kpiThresholdDetails[kpi].mediumThreshold != null && kpiData.kpiThresholdDetails[kpi].mediumThreshold != ""){
					if(thresholdValues!=""){
						thresholdValues += "Medium:"
					}
					else{
						thresholdValues += ", Medium:"
					}

					thresholdValues += kpiData.kpiThresholdDetails[kpi].mediumThreshold+", ";
				}
				if(kpiData.kpiThresholdDetails[kpi].hasOwnProperty("lowThreshold") && kpiData.kpiThresholdDetails[kpi].lowThreshold != null && kpiData.kpiThresholdDetails[kpi].lowThreshold != ""){
					if(thresholdValues!=""){
						thresholdValues += "Low:"
					}
					else{
						thresholdValues += ", Low:"
					}

					thresholdValues += kpiData.kpiThresholdDetails[kpi].lowThreshold+", ";
				}

				kpiNames=kpiNames+"["+thresholdValues.substring(2, thresholdValues.length-2)+"]";
			}

			for(var kpiGrp in kpiData.kpiGroupThresholdDetails){
				thresholdValues="";
				kpiNames += ", " + kpiData.kpiGroupThresholdDetails[kpiGrp].kpiName;
				if(kpiData.kpiGroupThresholdDetails[kpiGrp].highThreshold){
					thresholdValues += ", High:" + kpiData.kpiGroupThresholdDetails[kpiGrp].highThreshold+", ";
				}
				if(kpiData.kpiGroupThresholdDetails[kpiGrp].mediumThreshold){
					if(thresholdValues!=""){
						thresholdValues += "Medium:"
					}
					else{
						thresholdValues += ", Medium:"
					}

					thresholdValues += kpiData.kpiGroupThresholdDetails[kpiGrp].mediumThreshold+", ";
				}
				if(kpiData.kpiGroupThresholdDetails[kpiGrp].lowThreshold){
					if(thresholdValues!=""){
						thresholdValues += "Low:"
					}
					else{
						thresholdValues += ", Low:"
					}

					thresholdValues += kpiData.kpiGroupThresholdDetails[kpiGrp].lowThreshold+", ";
				}
				kpiNames=kpiNames+"["+thresholdValues.substring(2, thresholdValues.length-2)+"]";
			}

			return kpiNames.substring(2);
		}

		this.onKpiEdit = function(indx, curIndex, kpiData){
			resetToUpdateState(indx);
			compInstSearchByChangeIndex = indx;
			operationIndex = curIndex;
			self.kpiToEditData(kpiData);
			self.getKpis(indx, kpiData.componentInstanceId, kpiData.componentInstanceName);
		}

		this.onKpiDelete = function(indx, curIndex, kpiData){
			showMessageBox("Are you sure you want to delete threshold details?", "question", "confirm", function confirmCallback(confirmDelete){
				if(confirmDelete){
					var compInstKpiObj = JSON.parse(JSON.stringify(self.compInstKpiMapArr()[indx]));
					compInstKpiObj.splice(curIndex, 1);

					self.compInstKpiMapArr.splice(indx, 1, compInstKpiObj);
				}
			});
		}

		this.addCompInstKpiMap = function(indx){
			self.errorMsg("");
			var compInstKpiMap = [];
			var kpiThresholdDet = [];
			var kpiGroupThresholdDet = [];
			var existingCompInstKpiData = [];

			var kpisSelected = $("input.chkboxCol"+indx+":checkbox:checked");
			var kpiRowId = -1;
			self.firstFieldToShowErr("");
			removeError("#compKpiThresholdSettings"+indx+" select");
			removeError("#compKpiThresholdSettings"+indx+" input");
			removeError("#compKpiGroupThresholdSettings"+indx+" select");
			removeError("#compKpiGroupThresholdSettings"+indx+" input");



			for(var kpi=0; kpi<kpisSelected.length; kpi++){
				kpiRowId = $(kpisSelected[kpi])[0].id.split("chkboxCol")[1];


				if($("#thresholdOperationsList"+kpiRowId).val() == "0"){
					showError("#compKpiThresholdSettings"+indx+" #thresholdOperationsList"+kpiRowId+"_chosen", "Please select Threshold Operator");
					showError("#compKpiThresholdSettings"+indx+" #thresholdOperationsList"+kpiRowId+"_chosen span", "Please select Threshold Operator");
				    self.errorMsg("#compKpiThresholdSettings"+indx);

				    $("#compKpiThresholdSettings"+indx).animate({
					    scrollTop: $("#thresholdOperationsList"+kpiRowId+"_chosen").offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
					});
				}
				if($("#highThreshold"+kpiRowId).val() == "" && $("#mediumThreshold"+kpiRowId).val() == ""  && $("#lowThreshold"+kpiRowId).val() == ""){
					showError("#compKpiThresholdSettings"+indx+" #highThreshold"+kpiRowId, "Please enter threshold value for any severity");
					showError("#compKpiThresholdSettings"+indx+" #mediumThreshold"+kpiRowId, "Please enter threshold value for any severity");
					showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Please enter threshold value for any severity");
				    self.errorMsg("#compKpiThresholdSettings"+indx);

				    $("#compKpiThresholdSettings"+indx).animate({
					    scrollTop: $("#highThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
					});
				}
				else if(slugify($("#thresholdOperationsList"+kpiRowId+" option:selected").text()).startsWith("greater")){
					if($("#highThreshold"+kpiRowId).val() != ""){
						if($("#mediumThreshold"+kpiRowId).val() != "" && $("#mediumThreshold"+kpiRowId).val() >= $("#highThreshold"+kpiRowId).val()){
							showError("#compKpiThresholdSettings"+indx+" #mediumThreshold"+kpiRowId, "Medium threshold value should be less than high threshold");
						    self.errorMsg("#compKpiThresholdSettings"+indx);

						    $("#compKpiThresholdSettings"+indx).animate({
							    scrollTop: $("#mediumThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
							});
						}
						if($("#lowThreshold"+kpiRowId).val() != ""){
							if($("#mediumThreshold"+kpiRowId).val() != "" && parseInt($("#lowThreshold"+kpiRowId).val()) >= $("#mediumThreshold"+kpiRowId).val()){
								showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be less than medium threshold");
							    self.errorMsg("#compKpiThresholdSettings"+indx);

							    $("#compKpiThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
								});
							}
							if(parseInt($("#lowThreshold"+kpiRowId).val()) >= parseInt($("#highThreshold"+kpiRowId).val())){
								showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be less than high threshold");
							    self.errorMsg("#compKpiThresholdSettings"+indx);

							    $("#compKpiThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
								});
							}
						}
					}
					if($("#mediumThreshold"+kpiRowId).val() != ""){
						if($("#lowThreshold"+kpiRowId).val() != "" && parseInt($("#lowThreshold"+kpiRowId).val()) >= parseInt($("#mediumThreshold"+kpiRowId).val())){
							showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be less than medium threshold");
						    self.errorMsg("#compKpiThresholdSettings"+indx);

						    $("#compKpiThresholdSettings"+indx).animate({
							    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
							});
						}
					}
				}
				if(slugify($("#thresholdOperationsList"+kpiRowId+" option:selected").text()).startsWith("less")){
					if($("#highThreshold"+kpiRowId).val() != ""){
						if($("#mediumThreshold"+kpiRowId).val() != "" && parseInt($("#mediumThreshold"+kpiRowId).val()) <= parseInt($("#highThreshold"+kpiRowId).val())){
							showError("#compKpiThresholdSettings"+indx+" #mediumThreshold"+kpiRowId, "Medium threshold value should be greater than high threshold");
						    self.errorMsg("#compKpiThresholdSettings"+indx);

						    $("#compKpiThresholdSettings"+indx).animate({
							    scrollTop: $("#mediumThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
							});
						}
						if($("#lowThreshold"+kpiRowId).val() != ""){
							if($("#mediumThreshold"+kpiRowId).val() != "" && parseInt($("#lowThreshold"+kpiRowId).val()) <= parseInt($("#mediumThreshold"+kpiRowId).val())){
								showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be greater than medium threshold");
							    self.errorMsg("#compKpiThresholdSettings"+indx);

							    $("#compKpiThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
								})
							}
							if(parseInt($("#lowThreshold"+kpiRowId).val()) <= parseInt($("#highThreshold"+kpiRowId).val())){
								showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be greater than high threshold");
							    self.errorMsg("#compKpiThresholdSettings"+indx);

							    $("#compKpiThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
								})
							}
						}
					}
					if($("#mediumThreshold"+kpiRowId).val() != ""){
						if($("#lowThreshold"+kpiRowId).val() != "" && parseInt($("#lowThreshold"+kpiRowId).val()) <= parseInt($("#mediumThreshold"+kpiRowId).val())){
							showError("#compKpiThresholdSettings"+indx+" #lowThreshold"+kpiRowId, "Low threshold value should be greater than medium threshold");
						    self.errorMsg("#compKpiThresholdSettings"+indx);

						    $("#compKpiThresholdSettings"+indx).animate({
							    scrollTop: $("#lowThreshold"+kpiRowId).offset().top - $("#compKpiThresholdSettings"+indx).offset().top + $("#compKpiThresholdSettings"+indx).scrollTop() - 10
							})
						}
					}
				}
				if(self.errorMsg()==""){
					kpiThresholdDet.push({
						"kpiId": $("#kpiId"+kpiRowId).text().split("kpiId")[1].split("txt")[0],
						"kpiName": $("#kpiCol"+kpiRowId).html(),
						"thresholdOperationId": $("#thresholdOperationsList"+kpiRowId).val(),
						"highThreshold": $("#highThreshold"+kpiRowId).val(),
						"mediumThreshold": $("#mediumThreshold"+kpiRowId).val(),
						"lowThreshold": $("#lowThreshold"+kpiRowId).val()
					});
				}
			}

			var groupKpisSelected = $("input.chkboxColGrp"+indx+":checkbox:checked");

			for(var kpi=0; kpi<groupKpisSelected.length; kpi++){
				kpiRowId = $(groupKpisSelected[kpi])[0].id.split("chkboxColGrp")[1];

				if($("#thresholdOperationsListKpiGrp"+kpiRowId).val() == "0"){
					showError("#compKpiGroupThresholdSettings"+indx+" #thresholdOperationsListKpiGrp"+kpiRowId+"_chosen", "Please select Threshold Operator");
					showError("#compKpiGroupThresholdSettings"+indx+" #thresholdOperationsListKpiGrp"+kpiRowId+"_chosen span", "Please select Threshold Operator");
				    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

				    //alert($("#thresholdOperationsList"+kpiRowId).offset().top - $("#compKpiThresholdSettings").offset().top + $("#compKpiThresholdSettings").scrollTop());

				    $("#compKpiGroupThresholdSettings"+indx).animate({
					    scrollTop: $("#thresholdOperationsListKpiGrp"+kpiRowId+"_chosen").offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
					});
				}
				if($("#highThresholdKpiGrp"+kpiRowId).val() == "" && $("#mediumThresholdKpiGrp"+kpiRowId).val() == ""  && $("#lowThresholdKpiGrp"+kpiRowId).val() == ""){
					showError("#compKpiGroupThresholdSettings"+indx+" #highThresholdKpiGrp"+kpiRowId, "Please enter threshold value for any severity");
					showError("#compKpiThresholdSettings #mediumThresholdKpiGrp"+kpiRowId, "Please enter threshold value for any severity");
					showError("#compKpiThresholdSettings #lowThresholdKpiGrp"+kpiRowId, "Please enter threshold value for any severity");

				    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

				    //alert($("#thresholdOperationsList"+kpiRowId).offset().top - $("#compKpiThresholdSettings").offset().top + $("#compKpiThresholdSettings").scrollTop());

				    $("#compKpiGroupThresholdSettings"+indx).animate({
					    scrollTop: $("#highThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSetting"+indx).scrollTop() - 10
					});
				}

				else if(slugify($("#thresholdOperationsListKpiGrp"+kpiRowId+" option:selected").text()).startsWith("greater")){
					if($("#highThresholdKpiGrp"+kpiRowId).val() != ""){
						if($("#mediumThresholdKpiGrp"+kpiRowId).val() != "" && $("#mediumThresholdKpiGrp"+kpiRowId).val() >= $("#highThresholdKpiGrp"+kpiRowId).val()){
							showError("#compKpiGroupThresholdSettings"+indx+" #mediumThresholdKpiGrp"+kpiRowId, "Medium threshold value should be less than high threshold");
						    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

						    $("#compKpiGroupThresholdSettings"+indx).animate({
							    scrollTop: $("#mediumThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
							});
						}
						if($("#lowThresholdKpiGrp"+kpiRowId).val() != ""){
							if($("#mediumThresholdKpiGrp"+kpiRowId).val() != "" && parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) >= $("#mediumThresholdKpiGrp"+kpiRowId).val()){
								showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be less than medium threshold");
							    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

							    $("#compKpiGroupThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
								});
							}
							if(parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) >= parseInt($("#highThresholdKpiGrp"+kpiRowId).val())){
								showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be less than high threshold");
							    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

							    $("#compKpiGroupThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
								});
							}
						}
					}
					if($("#mediumThresholdKpiGrp"+kpiRowId).val() != ""){
						if($("#lowThresholdKpiGrp"+kpiRowId).val() != "" && parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) >= parseInt($("#mediumThresholdKpiGrp"+kpiRowId).val())){
							showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be less than medium threshold");
						    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

						    $("#compKpiGroupThresholdSettings"+indx).animate({
							    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
							});
						}
					}
				}
				if(slugify($("#thresholdOperationsListKpiGrp"+kpiRowId+" option:selected").text()).startsWith("less")){
					if($("#highThresholdKpiGrp"+kpiRowId).val() != ""){
						if($("#mediumThresholdKpiGrp"+kpiRowId).val() != "" && parseInt($("#mediumThresholdKpiGrp"+kpiRowId).val()) <= parseInt($("#highThresholdKpiGrp"+kpiRowId).val())){
							showError("#compKpiGroupThresholdSettings"+indx+" #mediumThresholdKpiGrp"+kpiRowId, "Medium threshold value should be greater than high threshold");
						    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

						    $("#compKpiGroupThresholdSettings"+indx).animate({
							    scrollTop: $("#mediumThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
							});
						}
						if($("#lowThresholdKpiGrp"+kpiRowId).val() != ""){
							if($("#mediumThresholdKpiGrp"+kpiRowId).val() != "" && parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) <= parseInt($("#mediumThresholdKpiGrp"+kpiRowId).val())){
								showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be greater than medium threshold");
							    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

							    $("#compKpiGroupThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
								})
							}
							if(parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) <= parseInt($("#highThresholdKpiGrp"+kpiRowId).val())){
								showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be greater than high threshold");
							    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

							    $("#compKpiGroupThresholdSettings"+indx).animate({
								    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
								})
							}
						}
					}
					if($("#mediumThresholdKpiGrp"+kpiRowId).val() != ""){
						if($("#lowThresholdKpiGrp"+kpiRowId).val() != "" && parseInt($("#lowThresholdKpiGrp"+kpiRowId).val()) <= parseInt($("#mediumThresholdKpiGrp"+kpiRowId).val())){
							showError("#compKpiGroupThresholdSettings"+indx+" #lowThresholdKpiGrp"+kpiRowId, "Low threshold value should be greater than medium threshold");
						    self.errorMsg("#compKpiGroupThresholdSettings"+indx);

						    $("#compKpiGroupThresholdSettings"+indx).animate({
							    scrollTop: $("#lowThresholdKpiGrp"+kpiRowId).offset().top - $("#compKpiGroupThresholdSettings"+indx).offset().top + $("#compKpiGroupThresholdSettings"+indx).scrollTop() - 10
							})
						}
					}
				}

				else{
					kpiGroupThresholdDet.push({
						"kpiId": $("#kpiGroupId"+kpiRowId).text().split("kpiId")[1].split("txt")[0],
						"kpiName": $("#kpiGrpCol"+kpiRowId).html(),
						"kpiGroupId": $("#kpiGroupMainId"+kpiRowId).text().split("kpiGrpId")[1].split("txt")[0],
						"thresholdOperationId": $("#thresholdOperationsListKpiGrp"+kpiRowId).val(),
						"highThreshold": $("#highThresholdKpiGrp"+kpiRowId).val(),
						"mediumThreshold": $("#mediumThresholdKpiGrp"+kpiRowId).val(),
						"lowThreshold": $("#lowThresholdKpiGrp"+kpiRowId).val()
					});
				}
			}

			if(self.errorMsg() == ""){
				if(kpiThresholdDet.length == 0 && kpiGroupThresholdDet.length == 0){
					showMessageBox("Please select KPI(s) in Coverage Window: "+ $("#coverageWindowTitle"+indx).val() +" to map with thresholds", "error");
					self.errorMsg("#divAlertProfileAddEdit #compKpiThresholdSettings"+indx);
					self.errorMsg("");
				}
			}

			if(self.errorMsg() == ""){
				var compInstIdsArr = $("#compInstId"+indx).text().split(",");
				var compInstINamesArr = $("#compInstName"+indx).text().split(",");

				for(var compInst in compInstIdsArr){
					existingCompInstKpiData = [];
					compInstKpiMap = self.compInstKpiMapArr()[indx];
					if(compInstKpiMap.length){
						existingCompInstKpiData = $.grep(self.compInstKpiMapArr()[indx], function(evt){ return evt.componentInstanceId == compInstIdsArr[compInst]; });

						
					}

					var compInstKpiObj = {
						'componentInstanceId': compInstIdsArr[compInst],
						'componentInstanceName': compInstINamesArr[compInst],
						'kpiThresholdDetails': kpiThresholdDet,
						'kpiGroupThresholdDetails': kpiGroupThresholdDet
					};

					if(existingCompInstKpiData.length){
						var existingCompInstKpiDataIndex = self.compInstKpiMapArr()[indx].indexOf(existingCompInstKpiData[0]);
						compInstKpiMap.splice(existingCompInstKpiDataIndex, 1, compInstKpiObj);
					}
					else{
						compInstKpiMap.push(compInstKpiObj);
					}
				}
				
				

				self.compInstKpiMapArr.splice(indx, 1, compInstKpiMap);



				self.onCompInstSearchByChange(indx);
			}
		}

		this.updateCompInstKpiMap = function(indx){
			self.addCompInstKpiMap(indx);
			if(self.errorMsg() == ""){
				resetToAddState(indx);
			}
		}

		this.cancelCompInstKpiMap = function(indx){
			self.kpisArr.splice(indx, 1, [{}]);
			self.kpiGroupsArr.splice(indx, 1, [{}]);
			$("#btnDefault"+indx).css("visibility","hidden");
			resetToAddState(indx);
		}

		this.setDefaultThreshold = function(indx){

			for(var kpi in self.kpisArr()[indx]){
				$("#thresholdOperationsList"+indx+kpi).val($("#defaultThresholdId"+indx+kpi).text()).trigger('chosen:updated');
				$("#highThreshold"+indx+kpi).val($("#highThresholdVal"+indx+kpi).text());
				$("#mediumThreshold"+indx+kpi).val($("#mediumThresholdVal"+indx+kpi).text());
				$("#lowThreshold"+indx+kpi).val($("#lowThresholdVal"+indx+kpi).text());
			}

			for(var kpi in self.kpiGroupsArr()[indx]){
				for(var group in self.kpiGroupsArr()[indx][kpi].kpiGroupDetails){
					$("#thresholdOperationsListKpiGrp"+indx+kpi+group).val($("#defaultThresholdGrpId"+indx+kpi+group).text()).trigger('chosen:updated');
					$("#highThresholdKpiGrp"+indx+kpi+group).val($("#highGrpThresholdVal"+indx+kpi+group).text());
					$("#mediumThresholdKpiGrp"+indx+kpi+group).val($("#mediumThresholdVal"+indx+kpi+group).text());
					$("#lowThresholdKpiGrp"+indx+kpi+group).val($("#lowThresholdVal"+indx+kpi+group).text());
				}
			}

			//self.kpisArr()[indx]
		}

		this.handleKpiGrpSelAll = function(index){
			$(".chkboxColGrp"+index).prop("checked",$("#chkboxHeaderKpiGrp"+index).prop('checked'));

			return true;
		}

		this.handleKpiGrpSelCol = function(index){
			var length = $(".chkboxColGrp" + index + ":checked").length;

			var kpiGrpLength = 0;
			for(var grp in self.kpiGroupsArr()){
				for(var kpi in self.kpiGroupsArr()[grp]){
					kpiGrpLength++;
				}
			}
			if(length == kpiGrpLength){
				$("#chkboxHeaderKpiGrp"+index).prop("checked",true);
			}
			else{
				$("#chkboxHeaderKpiGrp"+index).prop("checked",false);
			}

			return true;
		}

		this.handleKpiSelAll = function(index){
			$(".chkboxCol"+index).prop("checked",$("#chkboxHeaderKpi"+index).prop('checked'));

			return true;
		}

		this.handleCompInstSelAll = function(index){
			$("#availableCompInstList"+index+" .checkList").prop("checked", $("#selAllAvailCompInst"+index).prop("checked"));

			return true;
		}

		this.handleCompInstChkClick = function(index){
			alert("Dsfsfsdfsdfsdfsd");
			return true;
		}

		this.handleKpiSelCol = function(index){
			var length = $(".chkboxCol" + index + ":checked").length;

			if(length == self.kpisArr()[index].length){
				$("#chkboxHeaderKpi"+index).prop("checked",true);
			}
			else{
				$("#chkboxHeaderKpi"+index).prop("checked",false);
			}

			return true;
		}

		this.handleTxnSelAll = function(index){
			$(".chkboxCol"+index).prop("checked",$("#chkboxHeaderTxn"+index).prop('checked'));
			$("#deleteStatus"+index).prop("disabled", !$(".chkboxCol" + index + ":checked").length);
			return true;
		}

		this.handleTxnSelCol = function(index){
			var length = $(".chkboxCol" + index + ":checked").length;
			$("#deleteStatus"+index).prop("disabled", !length);

			if(length == self.txnStatusDetailsArr()[index].length){
				$("#chkboxHeaderTxn"+index).prop("checked",true);
			}
			else{
				$("#chkboxHeaderTxn"+index).prop("checked",false);
			}

			return true;
		}

		this.resetCompInstKpiMap = function(indx){
			var kpiData = self.kpisArr()[indx];
			var kpiGroupData = self.kpiGroupsArr()[indx];
			self.kpisArr.splice(compInstSearchByChangeIndex, 1, [{}]);
			self.kpiGroupsArr.splice(compInstSearchByChangeIndex, 1, [{}]);
			self.kpisArr.splice(compInstSearchByChangeIndex, 1, kpiData);
			self.kpiGroupsArr.splice(compInstSearchByChangeIndex, 1, kpiGroupData);

			var thresholdObj = $.grep(self.compInstKpiMapArr()[indx], function(evt){ return evt.componentInstanceId == $("#compInstanceNamesList"+indx).val(); });

			if(thresholdObj.length){
				setExistingThresholdValues(thresholdObj[0]);
			}
		}

		function resetToAddState(indx){
			$("#btnAdd"+indx).css("display","");
			$("#btnReset"+indx).css("display","");
			$("#btnUpdate"+indx).css("display","none");
			$("#btnCancel"+indx).css("display","none");
		}

		function resetToUpdateState(indx){
			$("#btnAdd"+indx).css("display","none");
			$("#btnReset"+indx).css("display","none");
			$("#btnUpdate"+indx).css("display","");
			$("#btnCancel"+indx).css("display","");
		}

		function initTxnChecklistSelection(rowIndex){
			self.enableAddTxnBtn.splice(rowIndex, 1, false);
			self.enableRemoveTxnBtn.splice(rowIndex, 1, false);

			$("div").on("click", "#selAllAvailTxn"+rowIndex, function(e){
				$("#availableTxnList"+rowIndex+" .checkList").prop("checked", $("#selAllAvailTxn"+rowIndex).prop("checked"));
				self.enableAddTxnBtn.splice(rowIndex, 1, $("#availableTxnList"+rowIndex+" .checkList:checked").length);
			});

			$("div").on("change", "#availableTxnList"+rowIndex+" .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllAvailTxn"+rowIndex).prop("checked", self.availableTxnArr()[rowIndex].length == $("#availableTxnList"+rowIndex+" .checkList:checked").length);
					self.enableAddTxnBtn.splice(rowIndex, 1, $("#availableTxnList"+rowIndex+" .checkList:checked").length);
				}
			});

			$("div").on("click", "#selAllSelTxn"+rowIndex, function(e){
				$("#selectedTxnList"+rowIndex+" .checkList").prop("checked", $("#selAllSelTxn"+rowIndex).prop("checked"));
				self.enableRemoveTxnBtn.splice(rowIndex, 1, $("#selectedTxnList"+rowIndex+" .checkList:checked").length);
			});

			$("div").on("click", "#selectedTxnList"+rowIndex+" .checkList", function(e){
				if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
					$("#selAllSelTxn"+rowIndex).prop("checked", self.selectedTxnArr()[rowIndex].length == $("#selectedTxnList"+rowIndex+" .checkList:checked").length);
					self.enableRemoveTxnBtn.splice(rowIndex, 1, $("#selectedTxnList"+rowIndex+" .checkList:checked").length);
				}
			});
		}

		this.addCoverageWindow = function(userAction){
			console.log(availTxnArr);
			//self.errorMsg("");
			var alertProfileSelected = 0;
			var applicationSelected = 0;

			self.errorMsg("");
			self.firstFieldToShowErr("");

			/*if(userAction){
				self.addEditConfig();
			}*/

			if($("#alertProfileTypeList").val() == 0){
				showError("#divAlertProfileAddEdit #alertProfileTypeList_chosen", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
				showError("#divAlertProfileAddEdit #alertProfileTypeList_chosen span", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
			    self.errorMsg("#divAlertProfileAddEdit #alertProfileTypeList_chosen");
			}
			else{
				alertProfileSelected = 1;
				removeError("#divAlertProfileAddEdit #alertProfileTypeList_chosen");
				removeError("#divAlertProfileAddEdit #alertProfileTypeList_chosen span");
			}

			if(slugify(self.alertProfileTypeStr()) == "transaction" && $("#txnApplicationsList").val() == 0){
				showError("#divAlertProfileAddEdit #txnApplicationsList_chosen", uiConstants.common.SELECT_APPLICATION_MSG);
				showError("#divAlertProfileAddEdit #txnApplicationsList_chosen span", uiConstants.common.SELECT_APPLICATION_MSG);
			    self.errorMsg("#divAlertProfileAddEdit #txnApplicationsList_chosen");
			}
			else{
				applicationSelected = 1;
				removeError("#divAlertProfileAddEdit #txnApplicationsList_chosen");
				removeError("#divAlertProfileAddEdit #txnApplicationsList_chosen span");
			}

			if(alertProfileSelected && applicationSelected){
				self.applicationTypesArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.applicationNamesArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.componentNamesArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.clusterNamesArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.availableCompInstArr.splice(self.coverageWindowDetArr().length, 1, []);
				self.kpisArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.kpiGroupsArr.splice(self.coverageWindowDetArr().length, 1, [{}]);
				self.compInstKpiMapArr.splice(self.coverageWindowDetArr().length, 1, []);
				self.searchCriteriaFlag.splice(self.coverageWindowDetArr().length, 1, 0);
				self.availableTxnArr.splice(self.coverageWindowDetArr().length, 1, JSON.parse(JSON.stringify(availTxnArr)));
				self.selectedTxnArr.splice(self.coverageWindowDetArr().length, 1, []);
				coverageIndex = $.grep(self.coverageWindowDetArr(), function(evt){ return evt.hidden == false; }).length + 1;

				$("div").on("change", "#availableCompInstList"+self.coverageWindowDetArr().length+" .checkList", function(e){
					var divIndex = $(e.target).parent().parent().parent().attr("id").split("availableCompInstList")[1];
					$("#selAllAvailCompInst"+divIndex).prop("checked", $("#availableCompInstList"+divIndex).getAllValues().length == $("#availableCompInstList"+divIndex+" .checkList:checked").length);
		        });

				coverageAutoTitlesArr = [];
				for(var cw in self.coverageWindowDetArr()){
					if(!self.coverageWindowDetArr()[cw]["hidden"] && $("#coverageWindowTitle"+cw).val().startsWith("Coverage Window ")){
						coverageAutoTitlesArr.push(parseInt($("#coverageWindowTitle"+cw).val().split("Coverage Window ")[1] || "0"));
					}
				}
				self.coverageWindowDetArr.push({"hidden":false, "coverageIndex": ko.observable(coverageIndex)});

				if(slugify(self.alertProfileTypeStr()) == "transaction" && userAction){
					self.addTxnStatus(self.coverageWindowDetArr().length-1);
				}

				$("#coverageWindowTitle"+(self.coverageWindowDetArr().length-1)).val("Coverage Window "+ (coverageAutoTitlesArr.length!=0 ? Math.max.apply(null, coverageAutoTitlesArr)+1 : "1"));

				jQuery(".chosen").chosen({
					search_contains: true	
				});

				var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_TIME_PROFILE_LINK; });
	      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("#modalTimeProfile").css("visibility","hidden");
	      		}

	      		optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_SEVERITY_PROFILE_LINK; });
	      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("#modalSeverityProfile").css("visibility","hidden");
	      		}

	      		optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_NOTIFICATION_CONTENT_LINK; });
	      		if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("#modalNotificationProfile").css("visibility","hidden");
	      		}

	      		optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == uiConstants.common.CONST_ESCALATION_PROFILE_LINK; });
	      		/*if(optionPermissionsObj.length == 0 || !optionPermissionsObj[0].createEnabled){
					$("#modalEscalationProfile").css("visibility","hidden");
	      		}*/
			
	      		var profilesArr = [];
				if(self.timeProfileListsArr().length == 0){
				 	requestCall(uiConstants.common.SERVER_IP + "/coverageWindowProfiles/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getTimeProfiles", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5857a81c120000ec04c8ade9?callback=?", "GET", "", "getTimeProfiles", successCallback, errorCallback);
				}
				else{
					profilesArr = $.grep(self.timeProfileListsCopyArr(), function(evt){ return slugify(evt.status) == 1; });
					self.timeProfileListsArr.splice(self.coverageWindowDetArr().length-1, 1, profilesArr.length ? profilesArr : [{}]);

					$("#timeProfileList"+(self.coverageWindowDetArr().length-1)).trigger('chosen:updated');
					//$("#timeProfileList"+self.coverageWindowDetArr.length+"_chosen span").append(listImgEle);
					$("#timeProfileList"+(self.coverageWindowDetArr().length-1)+"_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onTimeProfileListOpen(self.coverageWindowDetArr().length-1, "timeProfileList");
					});
				}

				if(self.severityProfileListsArr().length == 0){
				 	requestCall(uiConstants.common.SERVER_IP + "/severityProfile/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getSeverityProfiles", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5857ba95120000f506c8ae23?callback=?", "GET", "", "getSeverityProfiles", successCallback, errorCallback);
				}
				else{
					profilesArr = $.grep(self.severityProfileListsCopyArr(), function(evt){ return slugify(evt.status) == 1; });
					self.severityProfileListsArr.splice(self.coverageWindowDetArr().length-1, 1, profilesArr.length ? profilesArr : [{}]);

					$("#severityProfileList"+(self.coverageWindowDetArr().length-1)).trigger('chosen:updated');
					//$("#severityProfileList"+self.coverageWindowDetArr.length+"_chosen span").append(listImgEle);
					$("#severityProfileList"+(self.coverageWindowDetArr().length-1)+"_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onSeverityProfileListOpen(self.coverageWindowDetArr().length-1, "severityProfileList");
					});
				}

				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					if(self.transactionsArr().length == 0){
					 	requestCall(uiConstants.common.SERVER_IP + "/alertProfile/transactionThreshold/transactionList/"+($("#txnApplicationsList").val() || self.selectedConfigRows()[0].applicationId)+"?status=1", "GET", "", "getTxnList", successCallback, errorCallback);
						//requestCall("http://www.mocky.io/v2/58639f591000005102b48908?callback=?", "GET", "", "getTxnList", successCallback, errorCallback);
					}
					else{
						self.availableTxnArr.splice(self.coverageWindowDetArr().length-1, 1, JSON.parse(JSON.stringify(availTxnArr)));
						$("#availableTxnList"+(self.coverageWindowDetArr().length-1)).checklistbox({
				            data: self.availableTxnArr()[self.coverageWindowDetArr().length-1]
				        });

						initTxnChecklistSelection(self.coverageWindowDetArr().length-1);
					}

					setTxnType(self.coverageWindowDetArr().length-1);
					debugger;
					self.txnType.splice(self.coverageWindowDetArr().length-1, 1, slugify($.grep(self.txnThresholdTypesArr(), function(evt){return evt.masterId == $('input[name=txnThresholdType'+(self.coverageWindowDetArr().length-1)+']:checked').val(); })[0].name).indexOf("volume") !=-1 ? "volume" : "status");
					self.txnStatusDetailsArr.splice(self.coverageWindowDetArr().length-1, 1, []);
				}

				/*if(self.notificationProfileListsArr().length == 0){
				 	//requestCall(uiConstants.common.SERVER_IP + "/notificationProfiles/mstSubType/"+$("#alertProfileTypeList").val(), "GET", "", "getNotificationProfiles", successCallback, errorCallback);
				}
				else{
					$("#notificationProfileList"+self.coverageWindowDetArr.length).trigger('chosen:updated');
					$("#notificationProfileList"+self.coverageWindowDetArr.length+"_chosen span").append(listImgEle);
				}*/

				if(self.escalationProfileListsArr().length == 0){
				 	requestCall(uiConstants.common.SERVER_IP + "/escalationProfiles/mstSubType/"+$("#alertProfileTypeList").val()+"?status=2&markInactive=1", "GET", "", "getEscalationProfiles", successCallback, errorCallback);
					//requestCall("http://www.mocky.io/v2/5850dfb00f00005204046bb3?callback=?", "GET", "", "getEscalationProfiles", successCallback, errorCallback);
				}
				else{
					profilesArr = $.grep(self.escalationProfileListsCopyArr(), function(evt){ return slugify(evt.status) == 1; });
					self.escalationProfileListsArr.splice(self.coverageWindowDetArr().length-1, 1, profilesArr.length ? profilesArr : [{}]);

					$("#escalationProfileList"+(self.coverageWindowDetArr().length-1)).trigger('chosen:updated');
					//$("#escalationProfileList"+self.coverageWindowDetArr.length+"_chosen span").append(listImgEle);
					$("#escalationProfileList"+(self.coverageWindowDetArr().length-1)+"_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onEscalationProfileListOpen(self.coverageWindowDetArr().length-1, "escalationProfileList");
					});
				}
				if(self.thresholdOperationsArr().length == 0){
				 	requestCall(uiConstants.common.SERVER_IP + "/thresholdOperations", "GET", "", "getOperationsList", successCallback, errorCallback);
	        		//requestCall("http://www.mocky.io/v2/5853a2380f0000aa07c73190?callback=?", "GET", "", "getOperationsList", successCallback, errorCallback);
				}



			}

			var $tab = $('#divAlertProfileAddEdit table');
			$tab.floatThead({
				scrollContainer: function($table){
					return $table.closest('.wrapper-scroll-table');
				}
			});

			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("#divCoverageWindowDet .chosen-container b").css("display", "none");
			}
		}

		this.deleteCoverageWindow = function(deleteIndex){
			if($.grep(self.coverageWindowDetArr(), function(evt){return evt.hasOwnProperty("hidden") && !evt["hidden"]; }).length == 1){
				showMessageBox(uiConstants.alertProfile.DENY_LAST_COVERAGE_WINDOW)
			}
			else{
				showMessageBox(uiConstants.alertProfile.CONFIRM_COVERAGE_WINDOW_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
					if(confirmDelete){
						if($("#coverageId"+deleteIndex).text() != ""){
							self.unMappedCoverageIds.push(parseInt($("#coverageId"+deleteIndex).text()));
						}

						self.coverageWindowDetArr.splice(deleteIndex, 1, {"hidden":true});
						coverageIndex = 0;
						for(var coverage in self.coverageWindowDetArr()){
							if(self.coverageWindowDetArr()[coverage].hasOwnProperty("coverageIndex")){
								coverageIndex++;
								self.coverageWindowDetArr()[coverage]["coverageIndex"](coverageIndex);
							}
						}

						//self.coverageWindowDetArr.splice(deleteIndex, 1);

					}
				});
			}
		}

		function getTimeProfileTooltipContent(value){
			var htmlText = "<table>";
			for(var day in value.coverageWindowlist){
				htmlText += "<tr><td>"+value.coverageWindowlist[day].day+"&nbsp;&nbsp;</td><td>";
				for(var timeSlot in value.coverageWindowlist[day].timeSlots){
					htmlText += ('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].startMinute).slice(-2)+" to "+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endHour).slice(-2)+":"+('0'+value.coverageWindowlist[day].timeSlots[timeSlot].endMinute).slice(-2)+"<br>";
				}

				htmlText += "</td></tr>";
			}

			return htmlText+="</table>";
		}

		function getSeverityProfileTooltipContent(value){
			var htmlText = "<label>Profile Type: "+value.alertProfileType+"</label><br>" +
				"<div class='profile-tooltip-content'><label>Severity: HIGH</label><br>"+
				"<label>Status: " + (value.settings.high.status ? "Active" : "Inactive") + "</label><br>" +
				"<label>Persistece: " + value.settings.high.persistence + "</label><br>";
			if(slugify(value.alertProfileType) == "core" || slugify(value.alertProfileType) == "transaction"){
				htmlText += "<hr class='profile-tooltip-hr'><label>Severity: MEDIUM</label><br>"+
					"<label>Status: " + (value.settings.medium.status ? "Active" : "Inactive") + "</label><br>" +
					"<label>Persistece: " + value.settings.medium.persistence + "</label><br>" +
					"<hr class='profile-tooltip-hr'><label>Severity: LOW</label><br>"+
					"<label>Status: " + (value.settings.low.status ? "Active" : "Inactive") + "</label><br>" +
					"<label>Persistece: " + value.settings.low.persistence + "</label>";
			}

			console.log(htmlText);

			return htmlText+"</div>";
		}

		function getEscalationProfileTooltipContent(value){
			var htmlText = "";
			for(var level in value.levels){
				htmlText += (htmlText == "" ? "" : "<hr class='profile-tooltip-hr'>")+"<label>Level: "+value.levels[level].levelNumber+"</label><br>" +
					"<p style='max-width: 400px; word-wrap: break-word; margin-bottom: 0px	'>Email (To): "+value.levels[level].levelDetails.emailTo+"</p><br>";
			}
			return htmlText;
		}

		function setListImg(rowIndex, listIdPart, tooltipParentObj, profileMasterType){
			$("#"+listIdPart+rowIndex+"_chosen")
		    .find("li.active-result").each(function() {

			    if ($(this).attr("data-option-array-index") == 0) //1 is added since the first item in the list will be the text indicating the user to select the option
			        return;

			    var tooltipContent = "";
			    if(profileMasterType == "timeprofile"){
			    	tooltipContent = getTimeProfileTooltipContent(tooltipParentObj[$(this).attr("data-option-array-index")-1]);
			    }
			    else if(profileMasterType == "severityprofile"){
			    	tooltipContent = getSeverityProfileTooltipContent(tooltipParentObj[$(this).attr("data-option-array-index")-1]);
			    }
			    else if(profileMasterType == "notificationprofile"){
			    	tooltipContent = "<div class='profile-tooltip-content'><p style='max-width: 350px; word-wrap: break-word;'>"+tooltipParentObj[$(this).attr("data-option-array-index")-1].description+"</p></div>";
			    }
			    else if(profileMasterType == "escalationprofile"){
			    	tooltipContent = getEscalationProfileTooltipContent(tooltipParentObj[$(this).attr("data-option-array-index")-1]);
			    }

			    console.log($(this));
			  	if($(this).find("img").length == 0){
				    $(this).append(listImgEle);

				    $('.tip').tooltipster({
				    	plugins: ['sideTip', 'scrollableTip'],
					    content: $(tooltipContent)
					});
				}
			});
		}

		this.onTimeProfileListOpen = function(rowIndex, listIdPart){
			debugger;
			setListImg(rowIndex, listIdPart, self.timeProfileListsArr()[rowIndex], "timeprofile");
		}
		
		this.onSeverityProfileListOpen = function(rowIndex, listIdPart){
			setListImg(rowIndex, listIdPart, self.severityProfileListsArr()[rowIndex], "severityprofile");
		}

		this.onNotificationProfileListOpen = function(listIdPart){
			setListImg('', listIdPart, self.notificationProfileListsArr(), "notificationprofile");
		}

		this.onEscalationProfileListOpen = function(rowIndex, listIdPart){
			setListImg(rowIndex, listIdPart, self.escalationProfileListsArr()[rowIndex], "escalationprofile");
		}

       	//Adding/Updating single alert profile
		this.addEditConfig = function(){
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			var tagsArr = [];
			var tagsObjArr = [];
			var configData;
			var coverageDetails = [];

			$("#divAlertProfileAddEdit #txtName").val($("#divAlertProfileAddEdit #txtName").val().trim());
			$("#divAlertProfileAddEdit #txtDescription").val($("#divAlertProfileAddEdit #txtDescription").val().trim());

			if($("#divAlertProfileAddEdit #txtName").val().trim() == ""){
				showError("#divAlertProfileAddEdit #txtName", uiConstants.alertProfile.ALERT_PROFILE_NAME_REQUIRED);
			    self.errorMsg("#divAlertProfileAddEdit #txtName");
			}
			else if($("#divAlertProfileAddEdit #txtName").val().length < 2){
				showError("#divAlertProfileAddEdit #txtName", uiConstants.alertProfile.ALERT_PROFILE_NAME_MIN_LENGTH_ERROR);
			    self.errorMsg("#divAlertProfileAddEdit #txtName");
			}
			else if($("#divAlertProfileAddEdit #txtName").val().length > 45){
				showError("#divAlertProfileAddEdit #txtName", uiConstants.alertProfile.ALERT_PROFILE_NAME_MAX_LENGTH_ERROR);
			    self.errorMsg("#divAlertProfileAddEdit #txtName");
			}
			if($("#divAlertProfileAddEdit #txtDescription").val().trim() == ""){
				showError("#divAlertProfileAddEdit #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
			    self.errorMsg("#divAlertProfileAddEdit #txtDescription");
			}
			else if($("#divAlertProfileAddEdit #txtDescription").val().length < 25){
				showError("#divAlertProfileAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			    self.errorMsg("#divAlertProfileAddEdit #txtDescription");
			}
			else if($("#divAlertProfileAddEdit #txtDescription").val().length > 256){
				showError("#divAlertProfileAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			    self.errorMsg("#divAlertProfileAddEdit #txtDescription");
			}
			if($("#alertProfileTypeList").val() == 0){
				showError("#divAlertProfileAddEdit #alertProfileTypeList_chosen", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
				showError("#divAlertProfileAddEdit #alertProfileTypeList_chosen span", uiConstants.notificationContentProfile.SELECT_ALERT_PROFILE_TYPE_MSG);
			    self.errorMsg("#divAlertProfileAddEdit #alertProfileTypeList_chosen");
			}
			else{
				removeError("#divAlertProfileAddEdit #alertProfileTypeList_chosen");
				removeError("#divAlertProfileAddEdit #alertProfileTypeList_chosen span");
			}
			if($("#alertProfileTypeList").val() != 0 && $("#notificationProfileList").val() == "0"){
				showError("#divAlertProfileAddEdit #notificationProfileList_chosen", "Please select Notification Profile");
				showError("#divAlertProfileAddEdit #notificationProfileList_chosen span", "Please select Notification Profile");
			    self.errorMsg("#divAlertProfileAddEdit #notificationProfileList_chosen");
			}
			else{
				removeError("#divAlertProfileAddEdit #notificationProfileList_chosen");
				removeError("#divAlertProfileAddEdit #notificationProfileList_chosen span");
			}
			if(slugify(self.alertProfileTypeStr()) == "transaction" && $("#txnApplicationsList").val() == 0){
				showError("#divAlertProfileAddEdit #txnApplicationsList_chosen", uiConstants.common.SELECT_APPLICATION_MSG);
				showError("#divAlertProfileAddEdit #txnApplicationsList_chosen span", uiConstants.common.SELECT_APPLICATION_MSG);
			    self.errorMsg("#divAlertProfileAddEdit #txnApplicationsList_chosen");
			}
			else{
				removeError("#divAlertProfileAddEdit #txnApplicationsList_chosen");
				removeError("#divAlertProfileAddEdit #txnApplicationsList_chosen span");
			}

			removeError("#divAlertProfileAddEdit .tokenfield");
			removeError("#divAlertProfileAddEdit #alert-profile-tokenfield-typeahead-tokenfield");
			if(containsDuplicate($("#alert-profile-tokenfield-typeahead").val())){
				showError("#divAlertProfileAddEdit .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError("#divAlertProfileAddEdit #alert-profile-tokenfield-typeahead-tokenfield", uiConstants.common.DUPLICATE_TAGS);
			    self.errorMsg("#divAlertProfileAddEdit .tokenfield");
			}
			else{
				if(self.tags() && self.tags().trim().length == 1)
					tagsArr.push(self.tags());

				else if(self.tags() && self.tags().trim().length > 1)
					tagsArr = self.tags().split(",");
				
				for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						showError("#divAlertProfileAddEdit .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError("#divAlertProfileAddEdit #alert-profile-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
					    self.errorMsg("#divAlertProfileAddEdit .tokenfield");
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						showError("#divAlertProfileAddEdit .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError("#divAlertProfileAddEdit #alert-profile-tokenfield-typeahead-tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
					    self.errorMsg("#divAlertProfileAddEdit .tokenfield");
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						showError("#divAlertProfileAddEdit .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError("#divAlertProfileAddEdit #alert-profile-tokenfield-typeahead-tokenfield", uiConstants.common.INVALID_TAG_NAME);
					    self.errorMsg("#divAlertProfileAddEdit .tokenfield");
						break;
					}
				}

				if(self.errorMsg() == ""){
					for(var tag in tagsArr){
						if(tagsNewToAddArr.indexOf(tagsArr[tag].trim()) != -1){
							tagsObjArr.push({"tagId":null, "tagName":tagsArr[tag].trim(), "tagOperation":"add"});
						}
						else{
							tagsObjArr.push({"tagId":getTagIdByName(tagsArr[tag].trim()), "tagName":tagsArr[tag].trim(), "tagOperation":"none"});
						}
					}

					for(tag in tagsToDeleteArr){
						tagsObjArr.push({"tagId":getTagIdByName(tagsToDeleteArr[tag].trim()), "tagName":tagsToDeleteArr[tag].trim(), "tagOperation":"delete"});
					}
				}


				if(self.errorMsg() == "" && !self.coverageWindowDetArr().length){
					showMessageBox("Please add atleast one Coverage Window", "error");
				}

				else{
					var compInstanceKpiMapDetails = [];
					var transactionVolDetails = [];
					var transactionStatusDetails = [];
					var kpiDetailsArr = [];
					var thresholdValueObj = {};
					var selectedThresholdIds = [];
					var isThresholdSet = 0;

					for(var coverageDet in self.coverageWindowDetArr()){
						selectedThresholdIds = [];
						if(self.coverageWindowDetArr()[coverageDet].hasOwnProperty("hidden") && !self.coverageWindowDetArr()[coverageDet]["hidden"]){
							$("#coverageWindowTitle"+coverageDet).val($("#coverageWindowTitle"+coverageDet).val().trim());
							if($("#coverageWindowTitle"+coverageDet).val() == ""){
								showError("#divAlertProfileAddEdit #coverageWindowTitle"+coverageDet, "Please enter coverage window title");
							    self.errorMsg("#divAlertProfileAddEdit #coverageWindowTitle"+coverageDet);
								//break;
							}
							if($("#timeProfileList"+coverageDet).val() == "0"){
								showError("#divAlertProfileAddEdit #timeProfileList"+coverageDet+"_chosen", "Please select Time Profile");
								showError("#divAlertProfileAddEdit #timeProfileList"+coverageDet+"_chosen span", "Please select Time Profile");
							    self.errorMsg("#divAlertProfileAddEdit #timeProfileList"+coverageDet+"_chosen");
							}
							else{
								removeError("#divAlertProfileAddEdit #timeProfileList"+coverageDet+"_chosen");
								removeError("#divAlertProfileAddEdit #timeProfileList"+coverageDet+"_chosen span");
							}

							if($("#severityProfileList"+coverageDet).val() == "0"){
								showError("#divAlertProfileAddEdit #severityProfileList"+coverageDet+"_chosen", "Please select Severity Profile");
								showError("#divAlertProfileAddEdit #severityProfileList"+coverageDet+"_chosen span", "Please select Severity Profile");
							    self.errorMsg("#divAlertProfileAddEdit #severityProfileList"+coverageDet+"_chosen");
							}
							else{
								removeError("#divAlertProfileAddEdit #severityProfileList"+coverageDet+"_chosen");
								removeError("#divAlertProfileAddEdit #severityProfileList"+coverageDet+"_chosen span");
							}

							if($("#escalationProfileList"+coverageDet).val() == "0"){
								showError("#divAlertProfileAddEdit #escalationProfileList"+coverageDet+"_chosen", "Please select Escalation Profile");
								showError("#divAlertProfileAddEdit #escalationProfileList"+coverageDet+"_chosen span", "Please select Escalation Profile");
							    self.errorMsg("#divAlertProfileAddEdit #escalationProfileList"+coverageDet+"_chosen");
							}
							else{
								removeError("#divAlertProfileAddEdit #escalationProfileList"+coverageDet+"_chosen");
								removeError("#divAlertProfileAddEdit #escalationProfileList"+coverageDet+"_chosen span");
							}

							debugger;
							if(slugify(self.alertProfileTypeStr()) != "transaction"){
								if(self.errorMsg() == ""){
									if(self.compInstKpiMapArr()[coverageDet].length == 0){
										showMessageBox("Please add threshold details for atleast one KPI for Coverage Window: "+$("#coverageWindowTitle"+coverageDet).val(), "error");
								    	self.errorMsg("#divAlertProfileAddEdit #coverageWindowTitle"+coverageDet);
								    	self.errorMsg("");
								    }
								    else{
								    	isThresholdSet = 1;
								    }
								}
								compInstanceKpiMapDetails = [];

								for(var compInstKpi in self.compInstKpiMapArr()[coverageDet]){
									kpiDetailsArr = [];
									for(var kpi in self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails){
										
										thresholdValueObj = self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails[kpi];
										kpiDetailsArr.push({

											"kpiId": parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails[kpi].kpiId),
											"thresholdOperationId": parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails[kpi].thresholdOperationId),
											"highThreshold": thresholdValueObj.highThreshold ? parseInt(thresholdValueObj.highThreshold) : null,
											"mediumThreshold": thresholdValueObj.mediumThreshold ? parseInt(thresholdValueObj.mediumThreshold) : null,
											"lowThreshold": thresholdValueObj.lowThreshold ? parseInt(thresholdValueObj.lowThreshold) : null,
											"id": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? self.kpiMapId()[$("#coverageId"+coverageDet).text()+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails[kpi].kpiId+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].componentInstanceId] : 0
										});

										if($("#coverageId"+coverageDet).text() != ""){
											selectedThresholdIds.push(self.kpiMapId()[$("#coverageId"+coverageDet).text()+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiThresholdDetails[kpi].kpiId+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].componentInstanceId]);
										}
									}

									for(var kpi in self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails){
										thresholdValueObj = self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi];
										kpiDetailsArr.push({
											"kpiId": parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi].kpiId),
											"kpiGroupId": parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi].kpiGroupId),
											"thresholdOperationId":parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi].thresholdOperationId),
											"highThreshold": thresholdValueObj.highThreshold ? parseInt(thresholdValueObj.highThreshold) : null,
											"mediumThreshold": thresholdValueObj.mediumThreshold ? parseInt(thresholdValueObj.mediumThreshold) : null,
											"lowThreshold": thresholdValueObj.lowThreshold ? parseInt(thresholdValueObj.lowThreshold) : null,
											"id": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? self.kpiMapId()[$("#coverageId"+coverageDet).text()+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi].kpiId+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].componentInstanceId] : 0
										});

										if($("#coverageId"+coverageDet).text() != ""){
											selectedThresholdIds.push(self.kpiMapId()[$("#coverageId"+coverageDet).text()+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].kpiGroupThresholdDetails[kpi].kpiId+"_"+self.compInstKpiMapArr()[coverageDet][compInstKpi].componentInstanceId]);
										}
									}

									
									compInstanceKpiMapDetails.push({
										"componentInstanceId": parseInt(self.compInstKpiMapArr()[coverageDet][compInstKpi].componentInstanceId),
										"kpiThresholdMapping": kpiDetailsArr
									})
								}

								console.log("==================")

								console.log($("#coverageId"+coverageDet).text());
								console.log(selectedThresholdIds);

								coverageDetails.push({
									"coverageWindowName": $("#coverageWindowTitle"+coverageDet).val(),
									"id": $("#coverageId"+coverageDet).text() ? parseInt($("#coverageId"+coverageDet).text()) : 0,
									"timeProfileId": parseInt($("#timeProfileList"+coverageDet).val()),
									"severityProfileId": parseInt($("#severityProfileList"+coverageDet).val()),
									"escalationProfileId": parseInt($("#escalationProfileList"+coverageDet).val()),
									"unMappedThresholdMapIds": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? ($("#coverageId"+coverageDet).text() ? diffArray(self.thresholdMapIds()[$("#coverageId"+coverageDet).text()], selectedThresholdIds) : []) : [],
									"componentInstanceDetails": compInstanceKpiMapDetails,
								});
							}
							else if(slugify(self.alertProfileTypeStr()) == "transaction"){
								if(self.txnType()[coverageDet] == "volume"){
									if(!self.selectedTxnArr()[coverageDet].length){
										showError("#divAlertProfileAddEdit #availableTxnList"+coverageDet, "Please select atleast one transaction");
									    self.errorMsg("#divAlertProfileAddEdit #availableTxnList"+coverageDet);
									}
									if($("#txnThresholdOperationsList"+coverageDet).val() == "0"){
										showError("#divAlertProfileAddEdit #txnThresholdOperationsList"+coverageDet+"_chosen", "Please select Threshold Operator");
										showError("#divAlertProfileAddEdit #txnThresholdOperationsList"+coverageDet+"_chosen span", "Please select Threshold Operator");
									    self.errorMsg("#divAlertProfileAddEdit #txnThresholdOperationsList"+coverageDet+"_chosen");
									}
									else{
										transactionVolDetails = [];

										for(var txnVolDet in self.txnVolumeDetailsArr()[coverageDet]){
											if(self.txnVolumeDetailsArr()[coverageDet][txnVolDet].highThreshold() == ""){
												//self.errorMsg("Please enter high threshold value for transaction '"+self.txnVolumeDetailsArr()[coverageDet][txnVolDet].transactionName+"' in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												showError("#coverageThreshold"+coverageDet+" #highThreshold"+coverageDet+txnVolDet, "Please enter high threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#highThreshold"+coverageDet+txnVolDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											else{

												transactionVolDetails.push({
													"transactionId": parseInt(self.txnVolumeDetailsArr()[coverageDet][txnVolDet].transactionId),
													"highThreshold": parseInt(self.txnVolumeDetailsArr()[coverageDet][txnVolDet].highThreshold()),
													"thresholdOperationId": parseInt($("#txnThresholdOperationsList"+coverageDet).val())
												});
											}
											
										}

										if(self.errorMsg() == ""){
								    		isThresholdSet = 1;

											coverageDetails.push({
												"coverageWindowName": $("#coverageWindowTitle"+coverageDet).val(),
												"id": $("#coverageId"+coverageDet).text() ? parseInt($("#coverageId"+coverageDet).text()) : 0,
												"timeProfileId": parseInt($("#timeProfileList"+coverageDet).val()),
												"severityProfileId": parseInt($("#severityProfileList"+coverageDet).val()),
												"escalationProfileId": parseInt($("#escalationProfileList"+coverageDet).val()),
												//"unMappedThresholdMapIds": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? ($("#coverageId"+coverageDet).text() ? diffArray(self.thresholdMapIds()[$("#coverageId"+coverageDet).text()], selectedThresholdIds) : []) : [],
												"transactions": $("#selectedTxnList"+coverageDet).getAllValues().map(function (x){return {"transactionId" : parseInt(x)};}),
												"transactionVolume": transactionVolDetails
											});
										}
									}
								}

								if(self.txnType()[coverageDet] == "status"){
									if(!self.selectedTxnArr()[coverageDet].length){
										//self.errorMsg("Please select atleast one transaction for Coverage Window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
										//break;

										showError("#divAlertProfileAddEdit #availableTxnList"+coverageDet, "Please select atleast one transaction");
									    self.errorMsg("#divAlertProfileAddEdit #availableTxnList"+coverageDet);
									}
									if(!self.txnStatusDetailsArr()[coverageDet] || !self.txnStatusDetailsArr()[coverageDet].length){
										//self.errorMsg("Please add atleast one threshold details in Coverage Window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());

										showMessageBox("Please add atleast one threshold details", "error");
								    	self.errorMsg("#divAlertProfileAddEdit #coverageWindowTitle"+coverageDet);
								    	self.errorMsg("");
									}
									else{
										transactionStatusDetails = [];

										for(var txnStatusDet in self.txnStatusDetailsArr()[coverageDet]){
											if($("#txnStatus"+coverageDet+txnStatusDet).val() == "0"){
												//self.errorMsg("Please select transaction status type in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												//break;

												showError("#coverageThreshold"+coverageDet+" #txnStatus"+coverageDet+txnStatusDet+"_chosen", "Please select transaction status type");
												showError("#coverageThreshold"+coverageDet+" #txnStatus"+coverageDet+txnStatusDet+"_chosen span", "Please select transaction status type");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#txnStatus"+coverageDet+txnStatusDet+"_chosen").offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											if($("#thresholdOperationsList"+coverageDet+txnStatusDet).val() == "0"){
												/*self.errorMsg("Please select Threshold Operator in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #thresholdOperationsList"+coverageDet+txnStatusDet+"_chosen", "Please select Threshold Operator");
												showError("#coverageThreshold"+coverageDet+" #thresholdOperationsList"+coverageDet+txnStatusDet+"_chosen span", "Please select Threshold Operator");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#thresholdOperationsList"+coverageDet+txnStatusDet+"_chosen").offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											else if($("#txnStatus"+coverageDet+txnStatusDet).val() == slowStatusId && $("#txnResponseType"+coverageDet+txnStatusDet).val() == "0"){
												/*self.errorMsg("Please select Response Time Type in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #txnResponseType"+coverageDet+txnStatusDet, "Please select Response Time Type");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#txnResponseType"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											if(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold() == ""){
												/*self.errorMsg("Please enter high threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #highThreshold"+coverageDet+txnStatusDet, "Please enter high threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#highThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											else if(isNaN(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold()) || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold()<0 || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold()>100){
												/*self.errorMsg("Invalid high threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #highThreshold"+coverageDet+txnStatusDet, "Invalid high threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#highThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											if(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold() == ""){
												/*self.errorMsg("Please enter medium threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #mediumThreshold"+coverageDet+txnStatusDet, "Please enter medium threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#mediumThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											else if(isNaN(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()) || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()<0 || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()>100){
												/*self.errorMsg("Invalid medium threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #mediumThreshold"+coverageDet+txnStatusDet, "Invalid medium threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#mediumThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											if(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold() == ""){
												/*self.errorMsg("Please enter low threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #lowThreshold"+coverageDet+txnStatusDet, "Please enter low threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#lowThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											else if(isNaN(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold()) || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold()<0 || self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold()>100){
												/*self.errorMsg("Invalid low threshold value in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
												break;*/

												showError("#coverageThreshold"+coverageDet+" #lowThreshold"+coverageDet+txnStatusDet, "Invalid low threshold value");
												self.errorMsg("#coverageThreshold"+coverageDet);

											    $("#coverageThreshold"+coverageDet).animate({
												    scrollTop: $("#lowThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
												});
											}
											
											if(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold() != "" && self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold() != "" && self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold() != ""){
												if(slugify($("#thresholdOperationsList"+coverageDet+txnStatusDet+" option:selected").text()).startsWith("greater") && parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()) >= parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold())){
													/*self.errorMsg("Medium threshold value should be less than high threshold in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
													break;*/

													showError("#coverageThreshold"+coverageDet+" #mediumThreshold"+coverageDet+txnStatusDet, "Medium threshold value should be less than high threshold");
													self.errorMsg("#coverageThreshold"+coverageDet);

												    $("#coverageThreshold"+coverageDet).animate({
													    scrollTop: $("#mediumThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
													});
												}
												else if(slugify($("#thresholdOperationsList"+coverageDet+txnStatusDet+" option:selected").text()).startsWith("greater") && parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold()) >= parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold())){
													/*self.errorMsg("Low threshold value should be less than medium threshold in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
													break;*/

													showError("#coverageThreshold"+coverageDet+" #lowThreshold"+coverageDet+txnStatusDet, "Low threshold value should be less than medium threshold");
													self.errorMsg("#coverageThreshold"+coverageDet);

												    $("#coverageThreshold"+coverageDet).animate({
													    scrollTop: $("#lowThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
													});
												}
												else if(slugify($("#thresholdOperationsList"+coverageDet+txnStatusDet+" option:selected").text()).startsWith("less") && parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()) <= parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold())){
													/*self.errorMsg("Medium threshold value should be greater than high threshold in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
													break;*/

													showError("#coverageThreshold"+coverageDet+" #mediumThreshold"+coverageDet+txnStatusDet, "edium threshold value should be greater than high threshold");
													self.errorMsg("#coverageThreshold"+coverageDet);

												    $("#coverageThreshold"+coverageDet).animate({
													    scrollTop: $("#mediumThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
													});
												}
												else if(slugify($("#thresholdOperationsList"+coverageDet+txnStatusDet+" option:selected").text()).startsWith("less") && parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold()) <= parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold())){
													/*self.errorMsg("Low threshold value should be greater than medium threshold in coverage window: "+self.coverageWindowDetArr()[coverageDet].coverageIndex());
													break;*/

													showError("#coverageThreshold"+coverageDet+" #lowThreshold"+coverageDet+txnStatusDet, "Low threshold value should be greater than medium threshold");
													self.errorMsg("#coverageThreshold"+coverageDet);

												    $("#coverageThreshold"+coverageDet).animate({
													    scrollTop: $("#lowThreshold"+coverageDet+txnStatusDet).offset().top - $("#coverageThreshold"+coverageDet).offset().top + $("#coverageThreshold"+coverageDet).scrollTop() - 10
													});
												}
											}
											if(self.errorMsg() == ""){
												transactionStatusDetails.push({
													"transactionStatusId": parseInt($("#txnStatus"+coverageDet+txnStatusDet).val()),
													"thresholdOperationId": parseInt($("#thresholdOperationsList"+coverageDet+txnStatusDet).val()),
													"responseTimeTypeId": parseInt($("#txnStatus"+coverageDet+txnStatusDet).val() == slowStatusId ? $("#txnResponseType"+coverageDet+txnStatusDet).val() : null),
													"highThreshold": parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].highThreshold()),
													"mediumThreshold": parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].mediumThreshold()),
													"lowThreshold": parseFloat(self.txnStatusDetailsArr()[coverageDet][txnStatusDet].lowThreshold())
												});
											}
											
										}

										if(self.errorMsg() == ""){
								    		isThresholdSet = 1;
											
											coverageDetails.push({
												"coverageWindowName": $("#coverageWindowTitle"+coverageDet).val(),
												"id": $("#coverageId"+coverageDet).text() ? parseInt($("#coverageId"+coverageDet).text()) : 0,
												"timeProfileId": parseInt($("#timeProfileList"+coverageDet).val()),
												"severityProfileId": parseInt($("#severityProfileList"+coverageDet).val()),
												"escalationProfileId": parseInt($("#escalationProfileList"+coverageDet).val()),
												//"thresholdOperationId": $("#txnThresholdOperationsList"+coverageDet).val(),
												//"unMappedThresholdMapIds": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? ($("#coverageId"+coverageDet).text() ? diffArray(self.thresholdMapIds()[$("#coverageId"+coverageDet).text()], selectedThresholdIds) : []) : [],
												"transactions": $("#selectedTxnList"+coverageDet).getAllValues().map(function (x){return {"transactionId" : parseInt(x)};}),
												"transactionStatus": transactionStatusDetails
											});
										}
									}
								}

								if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
									$(".alertProfileKpiCoverageDet" + coverageDet + " .chosen-container b").css("display", "none");
								}
							}
						}
					}

					if(self.errorMsg() == "" && isThresholdSet){
						if(slugify(self.alertProfileTypeStr()) != "transaction"){
							var configData = {"index":1,
								"name": self.configName(),
								"description":  self.configDescription().trim(),
								"alertProfileTypeId": parseInt($("#alertProfileTypeList").val()),
								"notificationProfileId": parseInt($("#notificationProfileList").val()),
								"coverageWindows": coverageDetails,
								"unMappedCoverageWindowIds": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? self.unMappedCoverageIds() : [],
								"tags": tagsObjArr,
								"status" : self.configStatus()?1:0};

							if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || self.currentViewIndex() == uiConstants.common.CLONE_VIEW)
								requestCall(uiConstants.common.SERVER_IP + "/alertProfile", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
							else
								requestCall(uiConstants.common.SERVER_IP + "/alertProfile/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
						}
						else if(slugify(self.alertProfileTypeStr()) == "transaction"){
							var configData = {"index":1,
								"name": self.configName(),
								"description":  self.configDescription().trim(),
								"alertProfileTypeId": parseInt($("#alertProfileTypeList").val()),
								"notificationProfileId": parseInt($("#notificationProfileList").val()),
								"coverageWindows": coverageDetails,
								"unMappedCoverageWindowIds": self.currentViewIndex() == uiConstants.common.EDIT_VIEW ? self.unMappedCoverageIds() : [],
								"tags": tagsObjArr,
								"status" : self.configStatus()?1:0};

							if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || self.currentViewIndex() == uiConstants.common.CLONE_VIEW)
								requestCall(uiConstants.common.SERVER_IP + "/alertProfile/transactionThreshold", "POST", JSON.stringify(configData), "addSingleConfig", successCallback, errorCallback);
							else
								requestCall(uiConstants.common.SERVER_IP + "/alertProfile/transactionThreshold/" + self.configId(), "PUT", JSON.stringify(configData), "editSingleConfig", successCallback, errorCallback);
						}
					}
				}
			}
		}

		function editSingleConfig(configObj){
			setConfigValues(configObj);
			setConfigUneditable();			
		}

		function viewConfig(configObj){
			editSingleConfig(configObj);
			setConfigUneditable();			
		}

		this.checkInactive = function(dataObj, configElement, configId){
        	optionsCheckInactive(dataObj, configElement, configId);
        }

		function setConfigUneditable(){
			if(self.selectedConfigRows()[0].status == 0 || self.currentViewIndex() != uiConstants.common.EDIT_VIEW){
				$('#txtName').prop('readonly', true);
				$('#txtDescription').prop('readonly', true);
				$('#alertProfileTypeList').prop('disabled', true).trigger("chosen:updated");
				$('#notificationProfileList').prop('disabled', true).trigger("chosen:updated");
				$('#alert-profile-tokenfield-typeahead').tokenfield('readonly');

				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					$('#txnApplicationsList').prop('disabled', true).trigger("chosen:updated");
				}
			}
			if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);

				$("#divAlertProfileAddEdit .chosen-container b").css("display", "none");
			}
		}

		function cloneConfig(configObj){
			setConfigValues(configObj);
			$('#alertProfileTypeList').prop('disabled', true).trigger("chosen:updated");
		}

		function setConfigValues(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);
			for(var tagObj in configObj[0].tags){
				tagsNameArr.push(configObj[0].tags[tagObj].tagName);
			}

			if(uiConstants.common.DEBUG_MODE)console.log(tagsNameArr);

			if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
				self.configName("");
				self.configDescription("");
			}
			else{
				self.configName(configObj[0].name);
				self.configDescription(configObj[0].description);
			}
			self.configId(configObj[0].id);

			$("#alertProfileTypeList").val(configObj[0].alertProfileTypeId).prop('disabled', true);
			$("#alertProfileTypeList").val(configObj[0].alertProfileTypeId).trigger('chosen:updated');
			previousProfileTypeId = configObj[0].alertProfileTypeId;
			self.profileTypeId(previousProfileTypeId);

			previousAppId = configObj[0].applicationId;
			self.onAlertProfileTypeChange(false);
			
			self.configStatus(self.currentViewIndex() == uiConstants.common.CLONE_VIEW ? 1 : configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());
			$('#alert-profile-tokenfield-typeahead').tokenfield('setTokens', tagsNameArr);

			if(slugify(self.alertProfileTypeStr()) != "transaction"){
				requestCall(uiConstants.common.SERVER_IP + "/alertProfile/coverageWindows/"+self.configId(), "GET", "", "getCoverageWindowDetails", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/5857779e1200007f00c8ad8e?callback=?", "GET", "", "getCoverageWindowDetails", successCallback, errorCallback);
			}
			else if(slugify(self.alertProfileTypeStr()) == "transaction"){
				/*if(self.txnType() == "volume"){

				}
				else if(self.txnType() == "status"){

				}*/

				requestCall(uiConstants.common.SERVER_IP + "/alertProfile/transactionThreshold/coverageWindows/"+self.configId(), "GET", "", "getTxnCoverageWindowDetails", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/586a02881100005904261e0e?callback=?", "GET", "", "getTxnCoverageWindowDetails", successCallback, errorCallback);
				//requestCall("http://www.mocky.io/v2/586b3de911000046012e0d27?callback=?", "GET", "", "getTxnCoverageWindowDetails", successCallback, errorCallback);
			}

			self.errorMsg("");
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			self.pageSelected("Alert Profile with Threshold");
			self.currentViewIndex(uiConstants.common.LIST_VIEW);
		}

		function getTagIdByName(tagName){
			if(tagName == "" || tagName == null)
				return 0;
			if(uiConstants.common.DEBUG_MODE)console.log(tagName);
			for(configTag in self.configTagArr()){
				if(self.configTagArr()[configTag].tagName.trim() == tagName.trim()){
					return parseInt(self.configTagArr()[configTag].tagId);
				}
			}
			return 0;
		}

		function onMastersLoad(){
			if(configTagLoaded == 1){
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW){
					editSingleConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
					cloneConfig(self.selectedConfigRows());
				}

				else if(self.currentViewIndex() == uiConstants.common.READ_VIEW){
					if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
					viewConfig(self.selectedConfigRows());
				}
			}
		}

		function onTxnCoverageWindowMastersLoad(){
				console.log("*********************");

				console.log(self.alertProfileTypeStr());

			//console.log(coverageWindowDetailsLoaded);
			console.log(timeProfilesLoaded);
			console.log(severityProfilesLoaded);
			console.log(escalationProfilesLoaded);
			console.log(thresholdOpsLoaded);
			console.log(txnsLoaded);
			console.log(txnStatusLoaded);
			console.log(txnRespTimeTypeLoaded);

			if(txnApplicationsLoaded == 1 && timeProfilesLoaded == 1 && severityProfilesLoaded == 1 && escalationProfilesLoaded == 1 && thresholdOpsLoaded == 1 && txnsLoaded == 1 && txnStatusLoaded == 1 && txnRespTimeTypeLoaded == 1){
				//var kpiThresholdDet = [];
				//var kpiGroupThresholdDet = [];
				//var compInstKpiMap = [];
				//var compInstKpiDet = [];
				//var kpiMapKey = "";
				//var kpiMapObj = {};
				//var eachThresholdMapIds = [];
				var txnVolumeDet = [];
				var txnStatusDet = [];
				var txnVolData = {};
				//var txnStatusData = {};
				var txnVolObjArr = [];
				var txnsList = [];

				for(var coverage=0; coverage<self.coverageWindowDetailsForId().length; coverage++){
					
					//compInstKpiMap = [];
					//eachThresholdMapIds = [];
					//compInstKpiDet = self.coverageWindowDetailsForId()[coverage].componentInstanceDetails;
					txnVolObjArr = [];
					if(coverage != 0){
						self.addCoverageWindow(false);
					}

					$("#coverageId"+coverage).text(self.coverageWindowDetailsForId()[coverage].id);

					txnsList = $("#availableTxnList"+coverage).getIdsNames();

					for(txn in self.coverageWindowDetailsForId()[coverage].transactions){
						if($.grep(txnsList, function(evt){return evt.id == self.coverageWindowDetailsForId()[coverage].transactions[txn].transactionId; }).length == 0){
							txnsList.push({
								"id": self.coverageWindowDetailsForId()[coverage].transactions[txn].transactionId,
								"name": self.coverageWindowDetailsForId()[coverage].transactions[txn].transactionName
							})
						}
					}

					sortArrayObjByValue(txnsList, "name");
					self.availableTxnArr.splice(coverage, 1, JSON.parse(JSON.stringify(txnsList)));

					$("#availableTxnList"+coverage).checklistbox({
			            data: self.availableTxnArr()[coverage]
			        });

			        debugger;

					for(txn in self.coverageWindowDetailsForId()[coverage].transactions){
						$("#availableTxnList"+coverage+" .checkList[value=" + self.coverageWindowDetailsForId()[coverage].transactions[txn].transactionId + "]").prop("checked",true);
					}

					self.addToSelectedTxn(coverage);

					$("#coverageWindowTitle"+coverage).val(self.coverageWindowDetailsForId()[coverage].coverageWindowName);
					$("#timeProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].timeProfileId);
					$("#severityProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].severityProfileId);
					$("#escalationProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].escalationProfileId);

					if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)){
						$("#coverageRemove"+coverage).css("visibility", "hidden");
						$("#timeProfileList"+coverage).prop('disabled', true);
						$("#severityProfileList"+coverage).prop('disabled', true);
						$("#escalationProfileList"+coverage).prop('disabled', true);
						$("#coverageWindowTitle"+coverage).prop('readonly', true);
						$("#txnDiv"+coverage).find("input,button,select").attr("disabled", "disabled");
							$("#availableTxnList"+coverage).addClass("checklist-disabled");
						$("#selectedTxnList"+coverage).addClass("checklist-disabled");
						$("input[name='txnThresholdType"+coverage+"']").prop("disabled", true);

						//self.displayAlertProfileBlocks(false);
					}

					$("#timeProfileList"+coverage).trigger('chosen:updated');
					$("#severityProfileList"+coverage).trigger('chosen:updated');
					$("#escalationProfileList"+coverage).trigger('chosen:updated');

					if(self.coverageWindowDetailsForId()[coverage].hasOwnProperty("transactionVolume") && self.coverageWindowDetailsForId()[coverage]["transactionVolume"].length){
						txnVolumeDet = self.coverageWindowDetailsForId()[coverage]["transactionVolume"];
						$("input:radio[name='txnThresholdType"+coverage+"'][value='"+txnVolumeDet[0].transactionTresholdTypeId+"']").attr("checked",true);
						self.txnType.splice(coverage, 1, "volume");
						addTxnVolumeThresholdData(coverage);
						$("#txnThresholdOperationsList"+coverage).val(txnVolumeDet[0].thresholdOperationId).trigger('chosen:updated');

						for(var txnVol in txnVolumeDet){
							txnVolData = $.grep(self.txnVolumeDetailsArr()[coverage], function(evt){return evt.transactionId == txnVolumeDet[txnVol].transactionId; });
							
							if(txnVolData.length){
								//txnVolObjIndex = self.txnVolumeDetailsArr.indexOf(txnVolData[0]);
								txnVolData[0].highThreshold(txnVolumeDet[txnVol].highThreshold);
							}
							txnVolObjArr.push(txnVolData[0]);
						}

						self.txnVolumeDetailsArr.splice(coverage, 1, txnVolObjArr);

						if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)){
							//$("#txnThresholdOperationsList"+coverage).

							$("#coverageThreshold"+coverage).find("input,button,select").attr("disabled", "disabled");

						}

						self.txnType.splice(coverage, 1, "volume");
					}

					else if(self.coverageWindowDetailsForId()[coverage].hasOwnProperty("transactionStatus") && self.coverageWindowDetailsForId()[coverage]["transactionStatus"].length){
						txnStatusDet = self.coverageWindowDetailsForId()[coverage]["transactionStatus"];
						$("input:radio[name='txnThresholdType"+coverage+"'][value='"+txnStatusDet[0].transactionTresholdTypeId+"']").attr("checked",true);
						self.txnType.splice(coverage, 1, "status");
						for(var txnStatus in txnStatusDet){
							self.addTxnStatus(coverage);
							//txnStatusData = $.grep(self.txnStatusDetailsArr()[coverage], function(evt){return evt.transactionId == txnStatusDet[txnStatus].transactionId; });

							$("#txnStatus"+coverage+txnStatus).val(txnStatusDet[txnStatus].transactionStatusId).trigger('chosen:updated');
							$("#thresholdOperationsList"+coverage+txnStatus).val(txnStatusDet[txnStatus].thresholdOperationId).trigger('chosen:updated');
							if(txnStatusDet[txnStatus].responseTimeTypeId){
								$("#txnResponseType"+coverage+txnStatus).val(txnStatusDet[txnStatus].responseTimeTypeId);
							}
							//$("#highThreshold"+coverage+txnStatus).val(txnStatusDet[txnStatus].highThreshold);
							self.txnStatusDetailsArr()[coverage][txnStatus]["highThreshold"](txnStatusDet[txnStatus].highThreshold);
							self.txnStatusDetailsArr()[coverage][txnStatus]["mediumThreshold"](txnStatusDet[txnStatus].mediumThreshold);
							self.txnStatusDetailsArr()[coverage][txnStatus]["lowThreshold"](txnStatusDet[txnStatus].lowThreshold);
							//$("#mediumThreshold"+coverage+txnStatus).val(txnStatusDet[txnStatus].mediumThreshold);
							//$("#lowThreshold"+coverage+txnStatus).val(txnStatusDet[txnStatus].lowThreshold);
						self.onTxnStatusChange(coverage, txnStatus, false);

							

						}

						console.log($("#txnStatus"+coverage+txnStatus).val());

						if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)){
							//$("#txnThresholdOperationsList"+coverage).

							$("#coverageThreshold"+coverage).find("input,button,select").attr("disabled", "disabled");

						}

						self.txnType.splice(coverage, 1, "status");

					}
				}
				if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)){
					$(".txnStatusChosen").prop("disabled", true).trigger('chosen:updated');
					$(".thresholdOperationsListChosen").prop("disabled", true).trigger('chosen:updated');
					$(".txnResponseTypeChosen").prop("disabled", true).trigger('chosen:updated');
				}

				$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			}
		}

		function onCoverageWindowMastersLoad(){
			if(timeProfilesLoaded == 1 && severityProfilesLoaded == 1 && escalationProfilesLoaded == 1){
				var kpiThresholdDet = [];
				var kpiGroupThresholdDet = [];
				var compInstKpiMap = [];
				var compInstKpiDet = [];
				var kpiMapKey = "";
				var kpiMapObj = {};
				var eachThresholdMapIds = [];
				self.thresholdMapIds({});
				var profilesObjArr = [];
				var selKpiIdsArr = [];
				var selKpiGroupIdsArr = [];

				for(var coverage=0; coverage<self.coverageWindowDetailsForId().length; coverage++){
					selKpiIdsArr = [];
					selKpiGroupIdsArr = [];
					compInstKpiMap = [];
					eachThresholdMapIds = [];
					compInstKpiDet = self.coverageWindowDetailsForId()[coverage].componentInstanceDetails;

					if(coverage != 0){
						self.addCoverageWindow(false);
					}

					$("#coverageWindowTitle"+coverage).val(self.coverageWindowDetailsForId()[coverage].coverageWindowName);

					if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
						profilesObjArr = [];
						profilesObjArr = self.timeProfileListsArr()[coverage];
						if($.grep(profilesObjArr, function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].timeProfileId; }).length == 0){

							profilesObjArr.push($.grep(self.timeProfileListsCopyArr(), function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].timeProfileId; })[0]);
						}
						sortArrayObjByValue(profilesObjArr, "name");
						self.timeProfileListsArr.splice(coverage, 1, profilesObjArr);

						profilesObjArr = [];
						profilesObjArr = self.severityProfileListsArr()[coverage];
						if($.grep(profilesObjArr, function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].severityProfileId; }).length == 0){

							profilesObjArr.push($.grep(self.severityProfileListsCopyArr(), function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].severityProfileId; })[0]);
						}
						sortArrayObjByValue(profilesObjArr, "name");
						self.severityProfileListsArr.splice(coverage, 1, profilesObjArr);

						profilesObjArr = [];
						profilesObjArr = self.escalationProfileListsArr()[coverage];
						if($.grep(profilesObjArr, function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].escalationProfileId; }).length == 0){

							profilesObjArr.push($.grep(self.escalationProfileListsCopyArr(), function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].escalationProfileId; })[0]);
						}
						sortArrayObjByValue(profilesObjArr, "name");
						self.escalationProfileListsArr.splice(coverage, 1, profilesObjArr);
					}
					
					if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
						$("#severityProfileList"+coverage).val($.grep(self.severityProfileListsArr()[coverage], function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].severityProfileId; }).length == 0 ? "0" : self.coverageWindowDetailsForId()[coverage].severityProfileId);
						$("#timeProfileList"+coverage).val($.grep(self.timeProfileListsArr()[coverage], function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].timeProfileId; }).length == 0 ? "0" : self.coverageWindowDetailsForId()[coverage].timeProfileId);
						$("#escalationProfileList"+coverage).val($.grep(self.escalationProfileListsArr()[coverage], function(e){ return e.id == self.coverageWindowDetailsForId()[coverage].escalationProfileId; }).length == 0 ? "0" : self.coverageWindowDetailsForId()[coverage].escalationProfileId);
					}
					else{
						$("#severityProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].severityProfileId);
						$("#timeProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].timeProfileId);
						$("#escalationProfileList"+coverage).val(self.coverageWindowDetailsForId()[coverage].escalationProfileId);
					}

					for(var thresholdDet in compInstKpiDet){
						kpiThresholdDet = [];
						kpiGroupThresholdDet = [];

						
						if(self.currentViewIndex() == uiConstants.common.READ_VIEW || (self.selectedConfigRows().length && self.selectedConfigRows()[0].status == 0)){
							$("#coverageRemove"+coverage).css("visibility", "hidden");
							$("#timeProfileList"+coverage).prop('disabled', true);
							$("#severityProfileList"+coverage).prop('disabled', true);
							$("#escalationProfileList"+coverage).prop('disabled', true);
							$("#coverageWindowTitle"+coverage).prop('readonly', true);
							//self.displayAlertProfileBlocks(false);
						}
						$("#timeProfileList"+coverage).trigger('chosen:updated');
						$("#severityProfileList"+coverage).trigger('chosen:updated');
						$("#escalationProfileList"+coverage).trigger('chosen:updated');



						for(var kpiDet in compInstKpiDet[thresholdDet].kpiThresholdMapping){
							//$("#coverageId"+coverage).text(self.coverageWindowDetailsForId()[coverage]);

							if(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiGroupId == null){
								kpiThresholdDet.push({
									"kpiId": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiId,
									"kpiName": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiName,
									"thresholdOperationId": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].thresholdOperationId,
									"highThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].highThreshold,
									"mediumThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].mediumThreshold,
									"lowThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].lowThreshold
								});
							}
							else{
								kpiGroupThresholdDet.push({
									"kpiGroupId": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiGroupId,
									"kpiId": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiId,
									"kpiName": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiName,
									"thresholdOperationId": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].thresholdOperationId,
									"highThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].highThreshold,
									"mediumThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].mediumThreshold,
									"lowThreshold": compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].lowThreshold
								});
							}
							eachThresholdMapIds.push(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].id);

							kpiMapKey = self.coverageWindowDetailsForId()[coverage].id+"_"+compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiId+"_"+compInstKpiDet[thresholdDet].componentInstanceId;
							kpiMapObj[kpiMapKey] = compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].id;
							if(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiId){
								selKpiIdsArr.push(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiId);
							}

							if(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiGroupId){
								selKpiGroupIdsArr.push(compInstKpiDet[thresholdDet].kpiThresholdMapping[kpiDet].kpiGroupId);
							}
						}

						compInstKpiMap.push({
							'componentInstanceId': compInstKpiDet[thresholdDet].componentInstanceId,
							'componentInstanceName': compInstKpiDet[thresholdDet].componentInstanceName,
							'kpiThresholdDetails': kpiThresholdDet,
							'kpiGroupThresholdDetails': kpiGroupThresholdDet
						});

					}
					selKpiAllIdsArr.splice(coverage, 1, selKpiIdsArr);
					selKpiGroupAllIdsArr.splice(coverage, 1, selKpiGroupIdsArr);


					self.compInstKpiMapArr.splice(coverage, 1, compInstKpiMap);
					$("#coverageId"+coverage).text(self.coverageWindowDetailsForId()[coverage].id);


					self.thresholdMapIds()[self.coverageWindowDetailsForId()[coverage].id] = eachThresholdMapIds;

					
				}
				$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
				self.kpiMapId(kpiMapObj);
			}
		}

		function setTxnType(rowIndex){
			$('input[name=txnThresholdType'+rowIndex+']').on('focus',function(){

				previousTxnTypeId = $('input[name=txnThresholdType'+rowIndex+']:checked').val();
				console.log(previousTxnTypeId);
				
				var $tab = $('#divAlertProfileAddEdit table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			}).on('change',function(){

				showMessageBox(uiConstants.alertProfile.CONFIRM_ALERT_ON_TYPE_CHANGE, "question", "confirm", function confirmCallback(confirmClearThresholdDetails){
					if(confirmClearThresholdDetails){
						self.txnType.splice(rowIndex, 1, slugify($.grep(self.txnThresholdTypesArr(), function(evt){return evt.masterId == $('input[name=txnThresholdType'+rowIndex+']:checked').val(); })[0].name).indexOf("volume") !=-1 ? "volume" : "status");

				//	previousTxnTypeId = "";
						/*self.txnVolumeDetailsArr([]);
						self.txnStatusDetailsArr([]);*/
						self.txnVolumeDetailsArr.splice(rowIndex, 1, []);
						self.txnStatusDetailsArr.splice(rowIndex, 1, []);
						if(self.txnType()[rowIndex]=="volume"){
							addTxnVolumeThresholdData(rowIndex);
							$(".txnThresholdOperationsChosen").trigger('chosen:updated');

							jQuery(".chosen").chosen({
								search_contains: true	
							});
						}
						else if(self.txnType()[rowIndex]=="status"){
							self.addTxnStatus(rowIndex);
						}

						var $tab = $('#divAlertProfileAddEdit table');
						$tab.floatThead({
							scrollContainer: function($table){
								return $table.closest('.wrapper-scroll-table');
							}
						});
					}
					else{
						$('input[name=txnThresholdType'+rowIndex+'][value='+previousTxnTypeId+']').prop("checked", true);
						self.txnType.splice(rowIndex, 1, slugify($.grep(self.txnThresholdTypesArr(), function(evt){return evt.masterId == $('input[name=txnThresholdType'+rowIndex+']:checked').val(); })[0].name).indexOf("volume") !=-1 ? "volume" : "status");
					}

					previousTxnTypeId = "";
				});
			});
		}

		this.addTxnStatus = function(rowIndex){
			//alert(rowIndex);

			var txnStatusData = [];

			if(self.txnStatusDetailsArr()[rowIndex]){
				txnStatusData = self.txnStatusDetailsArr()[rowIndex];
			}
					
			txnStatusData.push({
				"highThreshold": ko.observable(""),
				"mediumThreshold": ko.observable(""),
				"lowThreshold": ko.observable("")
			});

			self.txnStatusDetailsArr.splice(rowIndex, 1, txnStatusData);

			$("#chkboxHeaderTxn"+rowIndex).prop("checked", false);

			$(".threshold-val").on("keypress", function(e){
				var character = String.fromCharCode(e.keyCode)
		        var newValue;

	        	newValue = this.value.substring(0, this.selectionStart)+character+this.value.substring(this.selectionEnd);

	        	if(newValue.indexOf(".") != -1 && newValue.split(".")[1] &&  newValue.split(".")[1].length>3){
	        		e.preventDefault();
		            return false;
	        	}

				if(!(this.selectionStart == 0 && this.selectionEnd == this.value.length)){
					if (isNaN(newValue) || hasDuplicateDecimalPoint(newValue, 3) || newValue<0 || newValue>100) {
			            e.preventDefault();
			            return false;
			        }
				}
			});

			if(self.txnStatusDetailsArr().length){
				self.onTxnStatusChange(rowIndex, 0, false);
			}

			$("#txnStatusChosen").trigger('chosen:updated');
			$("#thresholdOperationsListChosen").trigger('chosen:updated');
			$("#txnResponseTypeChosen").trigger('chosen:updated');

			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txnResponseType"+rowIndex+(self.txnStatusDetailsArr()[rowIndex].length-1)+"_chosen").css("display", "none");

			//if(self.txnStatusDetailsArr().length == self.txnStatusArr().length+self.txnResponseTypesArr().length){
				$("#btnStatus"+rowIndex).attr("disabled", self.txnStatusDetailsArr()[rowIndex].length == (self.txnStatusArr().length-1)+self.txnResponseTypesArr().length);
			//}



		}

		this.deleteTxnStatus = function(rowIndex){
			if($(".chkboxCol"+rowIndex).parent().find('input:checked').length == self.txnStatusDetailsArr()[rowIndex].length){
				showMessageBox(uiConstants.alertProfile.DENY_LAST_TXN_STATUS_DELETE)
			}
			else{
				showMessageBox(uiConstants.alertProfile.CONFIRM_DELETE_STATUS, "question", "confirm", function confirmCallback(confirmDeleteStatus){
					if(confirmDeleteStatus){
						$(".chkboxCol"+rowIndex).parent().find('input:checked').each(function(i) {
			                var txnStatusDetailsArrItem = self.txnStatusDetailsArr()[rowIndex];
			                txnStatusDetailsArrItem.splice(this.value, 1);

							self.txnStatusDetailsArr.splice(rowIndex,1, txnStatusDetailsArrItem);
			            });

						if($("#chkboxHeaderTxn"+rowIndex).prop("checked")){
							$("#chkboxHeaderTxn"+rowIndex).prop("checked", self.txnStatusDetailsArr()[rowIndex].length);
						}

					$("#btnStatus"+rowIndex).attr("disabled", self.txnStatusDetailsArr()[rowIndex].length == (self.txnStatusArr().length-1)+self.txnResponseTypesArr().length);
		            }
				});
			}
		}

		function setExistingThresholdValues(obj){
			var thresholdDet;
			var compInstKpiMapIdPart="";
			thresholdDet = obj.kpiThresholdDetails;

			for(var kpi in thresholdDet){
				console.log("$$$$$$$$$$$$$$$$$$$");
				console.log(thresholdDet[kpi].kpiName);
				console.log(thresholdDet[kpi].kpiId);
				compInstKpiMapIdPart = $(".alertProfileKpiCoverageDet"+compInstSearchByChangeIndex+" span:contains("+'kpiId'+thresholdDet[kpi].kpiId+'txt'+")")[0].id.split("kpiId")[1];
				console.log(compInstKpiMapIdPart);
				console.log(thresholdDet[kpi].thresholdOperationId);
				$("#chkboxCol"+compInstKpiMapIdPart).prop('checked', true);
				$("#thresholdOperationsList"+compInstKpiMapIdPart).val(thresholdDet[kpi].thresholdOperationId).trigger('chosen:updated');
				$("#highThreshold"+compInstKpiMapIdPart).val(thresholdDet[kpi].highThreshold);
				$("#mediumThreshold"+compInstKpiMapIdPart).val(thresholdDet[kpi].mediumThreshold);
				$("#lowThreshold"+compInstKpiMapIdPart).val(thresholdDet[kpi].lowThreshold);
				$("#kpiId"+compInstKpiMapIdPart).text(thresholdDet[kpi].kpiId.toString().startsWith("kpiId") ? thresholdDet[kpi].kpiId : 'kpiId'+thresholdDet[kpi].kpiId+'txt');
			}

			thresholdDet = obj.kpiGroupThresholdDetails;

			for(var kpi in thresholdDet){
				compInstKpiMapIdPart = $(".alertProfileKpiCoverageDet"+compInstSearchByChangeIndex+" span:contains("+'kpiId'+thresholdDet[kpi].kpiId+'txt'+")")[1].id.split("kpiGroupId")[1];
				$("#chkboxColGrp"+compInstKpiMapIdPart).prop('checked', true);
				$("#thresholdOperationsListKpiGrp"+compInstKpiMapIdPart).val(thresholdDet[kpi].thresholdOperationId).trigger('chosen:updated');
				$("#highThresholdKpiGrp"+compInstKpiMapIdPart).val(thresholdDet[kpi].highThreshold);
				$("#mediumThresholdKpiGrp"+compInstKpiMapIdPart).val(thresholdDet[kpi].mediumThreshold);
				$("#lowThresholdKpiGrp"+compInstKpiMapIdPart).val(thresholdDet[kpi].lowThreshold);
				$("#kpiGroupId"+compInstKpiMapIdPart).text(thresholdDet[kpi].kpiId.toString().startsWith("kpiGroupId") ? thresholdDet[kpi].kpiId : 'kpiId'+thresholdDet[kpi].kpiId+'txt');
				$("#kpiGroupMainId"+compInstKpiMapIdPart).text(thresholdDet[kpi].kpiGroupId.toString().startsWith("kpiGroupMainId") ? thresholdDet[kpi].kpiGroupId : 'kpiGrpId'+thresholdDet[kpi].kpiGroupId+'txt');
			}
		}

		this.onTxnStatusChange = function(parentIndex, rowIndex, resetResponseTimeType){
			var slowStatusCount = 0;

			for(var status in self.txnStatusDetailsArr()[parentIndex]){
				$("#txnStatus"+parentIndex+status).children("option").show();

				if($("#txnStatus"+parentIndex+status).val() == slowStatusId){
					slowStatusCount++;
				}
			}

			for(var curStatus in self.txnStatusDetailsArr()[parentIndex]){
				for(var destStatus in self.txnStatusDetailsArr()[parentIndex]){
					if((curStatus != destStatus && $("#txnStatus"+parentIndex+curStatus).val() != "0")){

						//$("#txnStatus"+parentIndex+curStatus).val() == slowStatusId && slowStatusCount == 2
						if($("#txnStatus"+parentIndex+curStatus).val() != slowStatusId){
							$("#txnStatus"+parentIndex+destStatus).children("option[value='"+$("#txnStatus"+parentIndex+curStatus).val()+"']").hide();
						}
						else{
							if(slowStatusCount == self.txnResponseTypesArr().length){
								$("#txnStatus"+parentIndex+destStatus).children("option[value='"+$("#txnStatus"+parentIndex+curStatus).val()+"']").hide();
							}
						}
					}
				}
			}

			if(resetResponseTimeType){
				$("#txnResponseType"+parentIndex+rowIndex).val("0");
			}
			if(slugify($("#txnStatus"+parentIndex+rowIndex+" option:selected").text()) == "slow"){
				$("#txnResponseTypeNotApp"+parentIndex+rowIndex).css("display", "none");
				$("#txnResponseType"+parentIndex+rowIndex+"_chosen").css("display", "");
			}
			else{
				$("#txnResponseTypeNotApp"+parentIndex+rowIndex).css("display", "");
				$("#txnResponseType"+parentIndex+rowIndex+"_chosen").css("display", "none");
			}

			self.onResponseTypeChange(parentIndex, rowIndex);
//			jQuery('#destinations').children('option[value="1"]').hide();
		}

		this.onResponseTypeChange = function(parentIndex, rowIndex){
			for(var reponse in self.txnStatusDetailsArr()[parentIndex]){
				$("#txnResponseType"+parentIndex+reponse).children("option").show();
			}

			for(var curRessponse in self.txnStatusDetailsArr()[parentIndex]){
				for(var destResponse in self.txnStatusDetailsArr()[parentIndex]){
					if((curRessponse != destResponse && $("#txnResponseType"+parentIndex+curRessponse).val() != "0")){
						$("#txnResponseType"+parentIndex+destResponse).children("option[value='"+$("#txnResponseType"+parentIndex+curRessponse).val()+"']").hide();
					}
				}
			}
		}

		function successCallback(data, reqType) {
			if(reqType === "getAlertProfileTag"){
				self.configTagArr(data.result);

				for(var tagData in self.configTagArr()){
					if(uiConstants.common.DEBUG_MODE)console.log(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteArr.push(self.configTagArr()[tagData].tagName);
					self.configTagAutoCompleteCopyArr.push(self.configTagArr()[tagData].tagName);

				}
				/*var engine = new Bloodhound({	
					local: self.configTagAutoCompleteArr(),
					datumTokenizer: function(d) {
						return Bloodhound.tokenizers.whitespace(d.value);
					},
					queryTokenizer: Bloodhound.tokenizers.whitespace
				});

				engine.initialize();

				$('#alert-profile-tokenfield-typeahead').tokenfield({
					typeahead: [null, { source: engine.ttAdapter() }],
					createTokensOnBlur: true
				});*/

				$('#alert-profile-tokenfield-typeahead').tokenfield({
				  autocomplete: {
				    source: self.configTagAutoCompleteArr(),
				    delay: 100
				  },
				  createTokensOnBlur: true
				});
				
				$('#alert-profile-tokenfield-typeahead-tokenfield').on("keyup", function(e){
					if(e.which == 13 && $(this).val().trim() != ""){
						$("#alert-profile-tokenfield-typeahead-tokenfield").blur();		
					}
				});
				
				configTagLoaded = 1;
				onMastersLoad();

				
			}
			else if(reqType === "getTimeProfiles"){
				var masterId = [];

				for(var coverageDet in self.coverageWindowDetArr()){
					masterId[coverageDet] = $("#timeProfileList"+coverageDet).val();
				}

				self.timeProfileListsCopyArr(data.result);
				var profilesArr = $.grep(data.result, function(evt){ return slugify(evt.status) == 1; });
				
				for(var coverage=0; coverage < self.coverageWindowDetArr().length; coverage++){
					if(profilesArr.length){
						self.timeProfileListsArr.splice(coverage, 1, profilesArr);
					}
					else{
						self.timeProfileListsArr.splice(coverage, 1, [{}]);

					}
				}

				if(self.modalConfigName() != ""){
					for(var coverageDet in self.coverageWindowDetArr()){
						if(self.coverageWindowDetArr()[coverageDet].hasOwnProperty("hidden") && !self.coverageWindowDetArr()[coverageDet]["hidden"]){
							$("#timeProfileList"+coverageDet).val(masterId[coverageDet]).trigger('chosen:updated');
						}
					}
				}
				else{
					$("#timeProfileList0").trigger('chosen:updated');
					$("#timeProfileList0_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onTimeProfileListOpen(0, "timeProfileList");
					});
				}

				self.modalConfigName("");

				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					timeProfilesLoaded = 1;
					onTxnCoverageWindowMastersLoad();
				}
				else{
					timeProfilesLoaded = 1;
					onCoverageWindowMastersLoad();
				}
			}
			else if(reqType === "getSeverityProfiles"){
				var masterId = [];

				for(var coverageDet in self.coverageWindowDetArr()){
					masterId[coverageDet] = $("#severityProfileList"+coverageDet).val();
				}

				self.severityProfileListsCopyArr(data.result);
				var profilesArr = $.grep(data.result, function(evt){ return slugify(evt.status) == 1; });

				for(var coverage=0; coverage < self.coverageWindowDetArr().length; coverage++){
					if(profilesArr.length){
						self.severityProfileListsArr.splice(coverage, 1, profilesArr);
					}
					else{
						self.severityProfileListsArr.splice(coverage, 1, [{}]);

					}
				}

				if(self.modalConfigName() != ""){

					for(var coverageDet in self.coverageWindowDetArr()){
						if(self.coverageWindowDetArr()[coverageDet].hasOwnProperty("hidden") && !self.coverageWindowDetArr()[coverageDet]["hidden"]){
							$("#severityProfileList"+coverageDet).val(masterId[coverageDet]).trigger('chosen:updated');
						}
					}
				}
				else{
					$("#severityProfileList0").trigger('chosen:updated');
					$("#severityProfileList0_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onSeverityProfileListOpen(0, "severityProfileList");
					});
				}

				self.modalConfigName("");

				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					severityProfilesLoaded = 1;
					onTxnCoverageWindowMastersLoad();
				}
				else{
					severityProfilesLoaded = 1;
					onCoverageWindowMastersLoad();
				}
			}
			else if(reqType === "getNotificationProfiles"){
				var masterId = $("#notificationProfileList").val();

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					data.result = getMasterList(data.result, "id", [self.selectedConfigRows()[0].notificationProfileId], true);
				}
				else{
					data.result = getMasterList(data.result, "id", null, false);
				}

				if(data.result.length){
					self.notificationProfileListsArr(data.result);
				}
				else{
					self.notificationProfileListsArr([{}]);
				}

				if(self.modalConfigName() != ""){
					$("#notificationProfileList").val(masterId).trigger('chosen:updated');
				}
				else{
					if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
						if(self.currentViewIndex() == uiConstants.common.CLONE_VIEW){
							debugger;
							$("#notificationProfileList").val($.grep(self.notificationProfileListsArr(), function(e){ return e.id == self.selectedConfigRows()[0].notificationProfileId; }).length == 0 ? "0" : self.selectedConfigRows()[0].notificationProfileId).trigger('chosen:updated');
						}
						else{
							$("#notificationProfileList").val(self.selectedConfigRows()[0].notificationProfileId).trigger('chosen:updated');
						}
					}
					else{
						$("#notificationProfileList").trigger('chosen:updated');
						$("#notificationProfileList_chosen .chosen-search").find("input").on("keyup", function (evt) {
							self.onNotificationProfileListOpen("notificationProfileList");
						});
					}
				}

				self.modalConfigName("");

				//notificationProfilesLoaded = 1;
				//onMastersLoad();
			}
			else if(reqType === "getEscalationProfiles"){
				var masterId = [];

				for(var coverageDet in self.coverageWindowDetArr()){
					masterId[coverageDet] = $("#escalationProfileList"+coverageDet).val();
				}

				self.escalationProfileListsCopyArr(data.result);
				var profilesArr = $.grep(data.result, function(evt){ return slugify(evt.status) == 1; });
				
				for(var coverage=0; coverage < self.coverageWindowDetArr().length; coverage++){
					if(profilesArr.length){
						self.escalationProfileListsArr.splice(coverage, 1, profilesArr);
					}
					else{
						self.escalationProfileListsArr.splice(coverage, 1, [{}]);

					}
				}

				if(self.modalConfigName() != ""){
					for(var coverageDet in self.coverageWindowDetArr()){
						if(self.coverageWindowDetArr()[coverageDet].hasOwnProperty("hidden") && !self.coverageWindowDetArr()[coverageDet]["hidden"]){
							$("#escalationProfileList"+coverageDet).val(masterId[coverageDet]).trigger('chosen:updated');
						}
					}
				}
				else{
					$("#escalationProfileList0").trigger('chosen:updated');
					$("#escalationProfileList0_chosen .chosen-search").find("input").on("keyup", function (evt) {
						self.onEscalationProfileListOpen(0, "escalationProfileList");
					});
				}

				self.modalConfigName("");

				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					escalationProfilesLoaded = 1;
					onTxnCoverageWindowMastersLoad();
				}
				else{
					escalationProfilesLoaded = 1;
					onCoverageWindowMastersLoad();
				}
			}
			else if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_ALERT_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.alertProfile.ERROR_ADD_ALERT_PROFILE, "error");
					}
				}
				else{
					if(params.isModal){
						params.modalConfigName(self.configName());
					}
					//  
					else{
						params.curPage(1);
					}
					self.cancelConfig();

					showMessageBox(uiConstants.alertProfile.SUCCESS_ADD_ALERT_PROFILE);
					
				}
			}
			else if(reqType === "editSingleConfig"){
				var res = data.result;

				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(uiConstants.common.DEBUG_MODE)console.log(data);
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_ALERT_PROFILE,data.errorCode,data.message), "error");
					}else{
						showMessageBox(uiConstants.alertProfile.ERROR_UPDATE_ALERT_PROFILE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.alertProfile.SUCCESS_UPDATE_ALERT_PROFILE);
					self.cancelConfig();
					if(params){
						params.curPage(1);
					}
				}
			}
			else if(reqType === "getApplicationTypes"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.applicationTypesArr.splice(compInstSearchByChangeIndex, 1, data.result.length ? data.result : [{}]);

				$("#applicationTypesList"+compInstSearchByChangeIndex).val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getApplications"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				self.applicationNamesArr.splice(compInstSearchByChangeIndex, 1, data.result.length ? data.result : [{}]);

				$("#applicationNamesList"+compInstSearchByChangeIndex).val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getTxnApplications"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);

				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					data.result = getMasterList(data.result, "applicationId", [self.selectedConfigRows()[0].applicationId], true);
				}
				else{
					data.result = getMasterList(data.result, "applicationId", null, false);
				}

				if(data.result.length){
					self.txnApplicationsArr(data.result);
				}
				else{
					self.txnApplicationsArr([]);
				}

				$("#txnApplicationsList").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW){
					$("#txnApplicationsList").val(self.selectedConfigRows()[0].applicationId).trigger("chosen:updated");
				}

				$(".panel-body #txnApplicationsList").on('change', function () {
			    	if(previousAppId == 0){
				    	self.onTxnAppChange(false);
						previousAppId = $("#txnApplicationsList").val();
				    }

				    else{
			    		showMessageBox(uiConstants.alertProfile.CONFIRM_ALERT_PROFILE_APPLICATION_CHANGE, "question", "confirm", function confirmCallback(confirmClearThresholdDetails){
							if(confirmClearThresholdDetails){
								self.onTxnAppChange(true);
								previousAppId = $("#txnApplicationsList").val();
				    		}
				    		else{
				    			$("#txnApplicationsList").val(previousAppId);
								$("#txnApplicationsList").trigger("chosen:updated");

				    		}
				    	});
			    	}
				});

				txnApplicationsLoaded = 1;
				onTxnCoverageWindowMastersLoad();
			}
			else if(reqType === "getTxnThresholdTypes"){
				self.txnThresholdTypesArr(data.result);

				self.txnType.splice(0, 1, slugify(self.txnThresholdTypesArr()[0].name).indexOf("volume") !=-1 ? "volume" : "status");
			}
			else if(reqType === "getTxnStatus"){
				self.txnStatusArr(data.result);

				$("#txnStatusChosen").trigger('chosen:updated');
				$("#thresholdOperationsListChosen").trigger('chosen:updated');
				$("#txnResponseTypeChosen").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});

				slowStatusIdArr = $.grep(self.txnStatusArr(), function(evt){ return slugify(evt.name) == 'slow'; });
				slowStatusId = slowStatusIdArr.length ? slowStatusIdArr[0].masterId : -1;

				txnStatusLoaded = 1;
				onTxnCoverageWindowMastersLoad();
			}
			else if(reqType === "getResponseTypes"){
				self.txnResponseTypesArr(data.result);

				txnRespTimeTypeLoaded = 1;
				onTxnCoverageWindowMastersLoad();
			}
			else if(reqType === "getCompTypeVersion"){
				debugger;
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				//self.componentNamesArr([{}]);
				var compNamesArr = [];
				for(var comp in data.result){
					//compNamesArr.push(data.result[comp].components);
					compNamesArr = compNamesArr.concat(data.result[comp].components);

				}

				console.log(compNamesArr);

				console.log(self.componentNamesArr());

				//self.componentNamesArr()[compInstSearchByChangeIndex] = compNamesArr;
				if(compNamesArr.length){
					self.componentNamesArr.splice(compInstSearchByChangeIndex, 1, compNamesArr);
					sortArrayObjByValue(self.componentNamesArr()[compInstSearchByChangeIndex], "componentName");
				}
				else{
					self.componentNamesArr.splice(compInstSearchByChangeIndex, 1, [{}]);
				}

				console.log(self.componentNamesArr());

				$("#compNamesList"+compInstSearchByChangeIndex).val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getClusters"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				if(data.result.length){
					self.clusterNamesArr.splice(compInstSearchByChangeIndex, 1, data.result);
				}
				else{
					self.clusterNamesArr.splice(compInstSearchByChangeIndex, 1, [{}]);
				}

				$("#clusterNamesList"+compInstSearchByChangeIndex).val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});
			}
			else if(reqType === "getTxnList"){
				self.transactionsArr(data.result);



				/*if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					var selTxnIdsArr = [];

					for(var app in self.selectedConfigRows()[0].applications){
						selAppIdsArr.push(self.selectedConfigRows()[0].applications[app].applicationId);
					}

					self.transactionsArr(getMasterList(data.result, "applicationId", selAppIdsArr, true));
				}
				else{
					self.transactionsArr(getMasterList(data.result, "applicationId", null, false));
				}*/


				//var availTxnArr = [];

				for(txn in self.transactionsArr()){
					availTxnArr.push({
						"id": self.transactionsArr()[txn].id,
						"name": self.transactionsArr()[txn].name
					});
				}

				self.availableTxnArr.splice(0, 1, JSON.parse(JSON.stringify(availTxnArr)));
				self.selectedTxnArr.splice(0, 1, []);

				$("#availableTxnList0").checklistbox({
		            data: self.availableTxnArr()[0]
		        });

		        initTxnChecklistSelection(0);

		        txnsLoaded = 1;
		        onTxnCoverageWindowMastersLoad();
			}
			else if(reqType === "getCompInstanceList"){
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				if(data.result.length){
					for(compInst in data.result){
						self.availableCompInstArr()[compInstSearchByChangeIndex].push({
							"id": data.result[compInst].componentInstanceId,
							"name": data.result[compInst].componentInstanceName,
							"imageSrc": data.result[compInst].isCluster ? "/images/cluster.png" : '',
							"tooltipText": data.result[compInst].isCluster ? "Cluster" : ''
						});
					}
					self.availableCompInstArr.splice(compInstSearchByChangeIndex, 1, self.availableCompInstArr()[compInstSearchByChangeIndex]);
				}
				else{
					self.availableCompInstArr.splice(compInstSearchByChangeIndex, 1, []);
				}

				$('#availableCompInstList'+compInstSearchByChangeIndex).checklistbox({
				    data: self.availableCompInstArr()[compInstSearchByChangeIndex]
				});

				/*$("#compInstanceNamesList"+compInstSearchByChangeIndex).val("0").trigger("chosen:updated");
				//$("#compNamesList").trigger('chosen:updated');
				jQuery(".chosen").chosen({
					search_contains: true	
				});*/
			}
			else if(reqType === "getKpiList"){
				debugger;
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					data.result.kpiDetails = getMasterList(data.result.kpiDetails, "kpiId", selKpiAllIdsArr[compInstSearchByChangeIndex] || [], true);
					data.result.kpiGroups = getMasterList(data.result.kpiGroups, "kpiGroupId", selKpiGroupAllIdsArr[compInstSearchByChangeIndex] || [], true);
				}
				else{
					data.result.kpiDetails = getMasterList(data.result.kpiDetails, "kpiId", null, false);
					data.result.kpiGroups = getMasterList(data.result.kpiGroups, "kpiGroupId", null, false);
				}

				self.kpisArr.splice(compInstSearchByChangeIndex, 1, data.result.kpiDetails);
				self.kpiGroupsArr.splice(compInstSearchByChangeIndex, 1, data.result.kpiGroups);

				$(".kpiChosen").trigger('chosen:updated');
				$(".kpiGrpChosen").trigger('chosen:updated');

				jQuery(".chosen").chosen({
					search_contains: true	
				});

				if(self.kpisArr.length || self.kpiGroupsArr.length){
					$("#btnDefault"+compInstSearchByChangeIndex).css("visibility","hidden");
				}
				else{
					$("#btnDefault"+compInstSearchByChangeIndex).css("visibility","visible");
				}

				//if($("#btnUpdate"+compInstSearchByChangeIndex).css("display") != "none"){
				if(self.kpiToEditData() && self.kpiToEditData().hasOwnProperty("componentInstanceId")){
					setExistingThresholdValues(self.kpiToEditData());
					//kpiThresholdDetails
				}
				/*mk else{
					var compInstKpiObjArr = $.grep(self.compInstKpiMapArr()[compInstSearchByChangeIndex], function(evt){console.log($("#compInstId"+compInstSearchByChangeIndex).text()); console.log(evt.componentInstanceId); return evt.componentInstanceId == $("#compInstId"+compInstSearchByChangeIndex).text(); });
						if(compInstKpiObjArr.length){
							setExistingThresholdValues(compInstKpiObjArr[0]);
						}
				}*/
				//}

				self.kpiToEditData({});

				var $tab = $('#divAlertProfileAddEdit table');
				$tab.floatThead({
					scrollContainer: function($table){
						return $table.closest('.wrapper-scroll-table');
					}
				});
			}
			else if(reqType === "getOperationsList"){
				self.thresholdOperationsArr(data.result);



				if(slugify(self.alertProfileTypeStr()) == "transaction"){
					thresholdOpsLoaded = 1;
					onTxnCoverageWindowMastersLoad();
				}

			}
			else if(reqType === "getCoverageWindowDetails"){
				self.coverageWindowDetailsForId(data.result);
				if(self.coverageWindowDetailsForId().length){
					self.addCoverageWindow(false);
				}
			}
			else if(reqType === "getTxnCoverageWindowDetails"){
				self.coverageWindowDetailsForId(data.result);
				if(self.coverageWindowDetailsForId().length){
					self.addCoverageWindow(false);
				}
				/*coverageWindowDetailsLoaded = 1;
				onTxnCoverageWindowMastersLoad();*/
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getAlertProfileTag"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_ALERT_PROFILE_TAGS, "error");
			}
			else if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.alertProfile.ERROR_ADD_ALERT_PROFILE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.alertProfile.ERROR_UPDATE_ALERT_PROFILE, "error");
			}
			else if(reqType === "getTimeProfiles"){
				showMessageBox(uiConstants.timeProfile.ERROR_GET_TIME_PROFILES, "error");
			}
			else if(reqType === "getSeverityProfiles"){
				showMessageBox(uiConstants.severityProfile.ERROR_GET_SEVERITY_PROFILES, "error");
			}
			else if(reqType === "getNotificationProfiles"){
				showMessageBox(uiConstants.notificationContentProfile.ERROR_GET_NOTIFICATION_CONTENT_PROFILES, "error");
			}
			else if(reqType === "getEscalationProfiles"){
				showMessageBox(uiConstants.escalationProfile.ERROR_GET_ESCALATION_PROFILES, "error");
			}
  			else if(reqType === "getApplicationTypes"){
  				showMessageBox(uiConstants.applicationType.ERROR_GET_APPLICATION_TYPES, "error");
  			}
  			else if(reqType === "getApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
  			else if(reqType === "getTxnApplications"){
  				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
  			}
  			else if(reqType === "getTxnThresholdTypes"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_TXN_THRESHOLD_TYPES, "error");
			}
			else if(reqType === "getTxnStatus"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_TXN_STATUS, "error");
			}
			else if(reqType === "getResponseTypes"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_TXN_RESPONSE_TYPES, "error");
			}
  			else if(reqType === "getCompTypeVersion"){
  				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
  			}
			else if(reqType === "getClusters"){
				showMessageBox(uiConstants.common.ERROR_GET_CLUSTERS, "error");
			}
			else if(reqType === "getTxnList"){
				showMessageBox(uiConstants.common.ERROR_GET_TXNS, "error");
			}
			else if(reqType === "getCompInstanceList"){
				showMessageBox(uiConstants.clusterConfig.ERROR_GET_COMP_INSTANCES, "error");
			}
			else if(reqType === "getKpiList"){
				showMessageBox(uiConstants.common.ERROR_GET_KPIS, "error");
			}
			else if(reqType === "getOperationsList"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_THRESHOLD_OPERATIONS, "error");
			}
			else if(reqType === "getCoverageWindowDetails"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_COVERAGE_WINDOW_DETAILS, "error");
			}
			else if(reqType === "getTxnCoverageWindowDetails"){
				showMessageBox(uiConstants.alertProfile.ERROR_GET_COVERAGE_WINDOW_DETAILS, "error");
			}
		}
	}

	AlertProfileMainAddEdit.prototype.dispose = function() { };
	return { viewModel: AlertProfileMainAddEdit, template: templateMarkup };
});