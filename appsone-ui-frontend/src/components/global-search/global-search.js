define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./global-search.html','hasher','validator','ui-constants','ui-common'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon) {

	function GlobalSearch(params) {
		var self = this;
		this.searchResultArr = ko.observableArray([]);
		this.accessLink = ko.observable("");
		this.txnApplicationId = ko.observable(0);
		this.loadingComplete = ko.observable(false);

		this.renderHandler=function(){
			//requestCall("http://www.mocky.io/v2/5860dbbf1000002f01f3943b?callback=?", "GET", "", "getSearchResultData", successCallback, errorCallback);
			if(window.globalSearchTxt == ""){
				hasher.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
			}

			self.loadingComplete(false);
			requestCall(uiConstants.common.SERVER_IP + "/search?name="+window.globalSearchTxt, "GET", "", "getSearchResultData", successCallback, errorCallback);
		}

		this.onSearchResClick = function(accessLink){
			var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == accessLink; });
	     	if(optionPermissionsObj.length){
		    	window.commonAddEnabled(optionPermissionsObj[0].createEnabled);
		        window.commonUpdateEnabled(optionPermissionsObj[0].updateEnabled);
		        window.commonDeleteEnabled(optionPermissionsObj[0].deleteEnabled);
		    }

			self.accessLink(accessLink);
			$("#srchTxt").val("");
		}

		this.onSearchResGroupClick = function(accessLink, groupItemId){
			self.accessLink(accessLink);
			self.txnApplicationId(groupItemId);
		}

		this.backToSearchRes = function(){
			self.accessLink("");
		}

		function successCallback(data, reqType) {
			if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
			if(uiConstants.common.DEBUG_MODE)console.log(data);

			if(reqType === "getSearchResultData"){
				self.loadingComplete(true);
				self.searchResultArr(data.result);
			}
		}

		function errorCallback(reqType) {
			if(reqType === "getSearchResultData"){
 				showMessageBox(uiConstants.globalSearch.ERROR_GET_SEARCH_RESULTS, "error");
  			}
		}
	}

	GlobalSearch.prototype.dispose = function() { };
	return { viewModel: GlobalSearch, template: templateMarkup };
});