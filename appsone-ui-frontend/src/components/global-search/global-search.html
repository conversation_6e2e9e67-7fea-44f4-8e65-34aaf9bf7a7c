<div data-bind="template: {afterRender: renderHandler}"></div>

<div data-bind="visible: !accessLink()" class="col-sm-12" id="searchResultsPage">
	<!-- ko if: searchResultArr().length -->
		<label data-bind="text: 'Search results for the text: \'' + window.globalSearchTxt+'\''" style="font-size: 20px; font-weight: normal; margin-bottom: 5px;"></label>
		<!-- ko foreach : searchResultArr() -->
				<!-- ko if: $data.group && $data.count -->
					<div style="padding-bottom: 5px; margin-left: 20px;">
						<span data-bind="text: $data.count + ' ' + $data.name+' found'"></span><br>
						<!-- ko foreach : $data.group -->
							<span class="glyphicon glyphicon-asterisk" style="font-size: 10px; padding-left: 20px;"></span> <span data-bind="text: $data.count"></span> in <a data-bind="attr: {href: $parentContext.$data.accessLink}, event: {click: $parents[1].onSearchResGroupClick.bind($data, $parentContext.$data.accessLink, $data.id)}"><span data-bind="text: $data.name"></span></a><br>

						<!-- /ko -->

					</div>

				<!-- /ko -->

				<!-- ko if: !$data.group  && $data.count -->
					<div style="padding-bottom: 5px; margin-left: 20px;">
						<span data-bind="text: $data.count"></span> <a data-bind="attr: {href: $data.accessLink}, event: {click: $parent.onSearchResClick.bind($data, $data.accessLink)}"><span data-bind="text: $data.name"></span></a> found
					</div>

				<!-- /ko -->
		<!-- /ko -->
	<!-- /ko -->

	<!-- ko if: searchResultArr().length == 0 && loadingComplete() -->
		<label data-bind="text: 'No matches found for the search text: \'' + window.globalSearchTxt+'\''" style="font-size: 20px; font-weight: normal; margin-bottom: 5px;"></label>
		
	<!-- /ko -->
</div>

<!-- ko if: accessLink() -->
	<!-- <button type="button" class="btn btn-primary" data-bind="event: {click: backToSearchRes}" title="Back to Search Results" style="float: right;"> --><label style="float: right; color: #337ab7; text-decoration: underline; cursor: pointer;     margin-top: -10px;" data-bind="event: {click: backToSearchRes}"><span class="glyphicon glyphicon-backward" style="padding-right: 3px;"></span>Back to Search Results</label><!-- </button> -->
<!-- /ko -->

<!-- ko if: accessLink() == '#listGridView'-->
	<list-grid-view></list-grid-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#appTypeListView'-->
	<app-type-list-view></app-type-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#compTypeListView'-->
	<comp-type-list-view></comp-type-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#componentListView'-->
	<component-list-view></component-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#clusterListView'-->
	<cluster-list-view></cluster-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#componentInstanceListView'-->
	<component-instance-list-view></component-instance-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#kpiListView'-->
	<kpi-list-view></kpi-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#kpiGroupListView'-->
	<kpi-group-list-view></kpi-group-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#producerListView'-->
	<producer-list-view></producer-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#agentListView'-->
	<agent-list-view></agent-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#transactionListView'-->
	<transaction-list-view params="{txnApplicationId: txnApplicationId}"></transaction-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#userRoleListView'-->
	<user-role-list-view></user-role-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#userProfileListView'-->
	<user-profile-list-view></user-profile-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#notificationContentListView'-->
	<notification-content-profile-list-view></notification-content-profile-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#alertProfileMainListView'-->
	<alert-profile-main-list-view></alert-profile-main-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#timeProfileListView'-->
	<time-profile-list-view></time-profile-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#severityProfileListView'-->
	<severity-profile-list-view></severity-profile-list-view>
<!-- /ko -->

<!-- ko if: accessLink() == '#escalationProfileListView'-->
	<escalation-profile-list-view></escalation-profile-list-view>
<!-- /ko -->