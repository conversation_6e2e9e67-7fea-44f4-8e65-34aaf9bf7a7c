define(['jquery','knockout','text!./side-bar-collapsible.html','hasher'], function($,ko,templateMarkup,hasher) {

    function SideBarCollapsible(params) {
        var self = this;
        var toggleBtnDivWidth = 2;

        this.menuData = ko.observable({
          "menuDetails": [
            {
              "id": 1,
              "name": "Configuration",
              "parentId": 0,
              "access": null,
              "accessLink": "#tiles",
              "level": "0",
              "submenu": [
                {
                  "id": 5,
                  "name": "Agents",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#agentListView",
                  "level": "1_1"
                },
                {
                  "id": 20,
                  "name": "Alert Profile",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#tiles",
                  "level": "1_1",
                  "submenu": [
                    {
                      "id": 22,
                      "name": "Notification Profile",
                      "parentId": 20,
                      "access": null,
                      "accessLink": "#notificationContentListView",
                      "level": "2_20"
                    },
                    {
                      "id": 21,
                      "name": "Severity Profile",
                      "parentId": 20,
                      "access": null,
                      "accessLink": "#severityProfileListView",
                      "level": "2_20"
                    },
                    {
                      "id": 25,
                      "name": "Threshold Settings",
                      "parentId": 20,
                      "access": null,
                      "accessLink": "#alertProfileMainListView",
                      "level": "2_20"
                    },
                    {
                      "id": 23,
                      "name": "Time Profile",
                      "parentId": 20,
                      "access": null,
                      "accessLink": "#timeProfileListView",
                      "level": "2_20"
                    },
                    {
                      "id": 24,
                      "name": "Escalation Profile",
                      "parentId": 20,
                      "access": null,
                      "accessLink": "#escalationProfileListView",
                      "level": "2_20"
                    }
                  ]
                },
                {
                  "id": 6,
                  "name": "Applications",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#listGridView",
                  "level": "1_1"
                },
                {
                  "id": 12,
                  "name": "Clusters",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#clusterListView",
                  "level": "1_1"
                },
                {
                  "id": 9,
                  "name": "Component Instances",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#componentInstanceListView",
                  "level": "1_1"
                },
                {
                  "id": 8,
                  "name": "Components",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#componentListView",
                  "level": "1_1"
                },
                {
                  "id": 26,
                  "name": "GRPC Settings",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#grpcSettingsListView",
                  "level": "1_1"
                },
                {
                  "id": 27,
                  "name": "KPI Groups",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#kpiGroupListView",
                  "level": "1_1"
                },
                {
                  "id": 11,
                  "name": "KPIs",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#kpiListView",
                  "level": "1_1"
                },
                {
                  "id": 2,
                  "name": "Masters",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#tiles",
                  "level": "1_1",
                  "sidemenu": [
                    {
                      "id": 3,
                      "name": "Application Type",
                      "parentId": 2,
                      "access": null,
                      "accessLink": "#appTypeListView",
                      "level": "2_2"
                    },
                    {
                      "id": 4,
                      "name": "Component Type",
                      "parentId": 2,
                      "access": null,
                      "accessLink": "#compTypeListView",
                      "level": "2_2"
                    }
                  ]
                },
                {
                  "id": 10,
                  "name": "Producers",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#producerListView",
                  "level": "1_1"
                },
                {
                  "id": 7,
                  "name": "Transactions",
                  "parentId": 1,
                  "access": null,
                  "accessLink": "#transactionListView",
                  "level": "1_1"
                }
              ]
            },
            {
              "id": 13,
              "name": "Dashboard",
              "parentId": 0,
              "access": null,
              "accessLink": "#tiles",
              "level": "0",
              "submenu": [
                {
                  "id": 18,
                  "name": "Default Dashboard",
                  "parentId": 13,
                  "access": null,
                  "accessLink": "#home",
                  "level": "1_13"
                }
              ]
            },
            {
              "id": 14,
              "name": "Settings",
              "parentId": 0,
              "access": null,
              "accessLink": "#tiles",
              "level": "0",
              "submenu": [
                {
                  "id": 17,
                  "name": "Email/SMS",
                  "parentId": 14,
                  "access": null,
                  "accessLink": "#emailSmsSettings",
                  "level": "1_14"
                },
                {
                  "id": 19,
                  "name": "GlobalSettings",
                  "parentId": 14,
                  "access": null,
                  "accessLink": "#globalSettings",
                  "level": "1_14"
                },
                {
                  "id": 15,
                  "name": "Roles",
                  "parentId": 14,
                  "access": null,
                  "accessLink": "#userRoleListView",
                  "level": "1_14"
                },
                {
                  "id": 16,
                  "name": "User Profile",
                  "parentId": 14,
                  "access": null,
                  "accessLink": "#userProfileListView",
                  "level": "1_14"
                }
              ]
            }
          ]
        });

        this.renderHandler=function(){
          $("#toggleSideBar").click(function (e) {
            e.preventDefault();
            $("#wrapper").toggleClass("toggled");
            $("#divMenuList").toggleClass("toggledMenuList");
            $("#toggleSideBar").toggleClass("glyphicon-menu-right");
            self.refreshMenuLayout();
          });

//          $("#wrapper").toggleClass("toggled");
          self.refreshMenuLayout();

          $(window).resize(function(){
              self.refreshMenuLayout();
          });
        }

        $(".sidebar-wrapper").css("width", $("#divToggleSideBar").outerWidth()+toggleBtnDivWidth);
        $("#toggleSideBar").css("margin-right", toggleBtnDivWidth/2);

        this.refreshMenuLayout = function(){
          $("#divToggleSideBar").css("top",($(".sidebar-wrapper").outerHeight()) / 2 + "px");
          //$("#divToggleSideBar").css("left", $(".sidebar-wrapper").outerWidth() + "px");
        }
    }

    SideBarCollapsible.prototype.dispose = function() { };

    return { viewModel: SideBarCollapsible, template: templateMarkup };

});