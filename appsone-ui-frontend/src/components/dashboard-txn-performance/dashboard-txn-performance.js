define(['jquery','knockout', 'text!./dashboard-txn-performance.html', 'knockout-es5','d3','c3','ui-constants','ui-common'], function($, ko, templateMarkup, koES5, d3, c3, uiConstants, uiCommon){

	function DashboardTxnPerformance(params) {
    	var self = this;
    	
    	this.timeUnit = params.timeUnit;
    	this.offSet = params.offSet;
    	this.podId = params.podId;
    	this.dualView = params.dualView;
	    this.podTitle = params.podTitle;
	    this.podName = params.podName;
	    this.selectedMonitoringType = params.monitorType;
	    this.isModal = params.isModal;
	    this.isGraphView = true;
	    this.chartComponentName = "single-axis-line-chart";

	    //this.LCPodId = ko.observable(params.podId+'_lines-chart');
	    this.LCPodId = 'txn_performance_'+this.podId;
	    //this.ModalLCPodId = params.podId+'_Modal_lines-chart';
	    this.ModalLCPodId = "txn_performance_modal_"+ this.podId;
	    this.currentPodBodyHeight = '';
	    this.currentPodBodyWidth = '';
	    this.chartPaletteColors = "#0075c2,#1aaf5d,#ff4000,#0000ff,#524e4e"; //RGB color code seprated by comma string
	    
	    this.timeWindow = [];
	    this.transactionVolume = [];
	    this.responseTime = [];
	    this.slowTxnGridHeaders = [];
	    this.slowTxnGridData = [];
	    this.chartDataObj = {};
	    this.chartDataObjForExpandableView = {};
	    this.showChart = false;
	    this.showExpandableView = true;

	    params.podVMContainer = self;

	    koES5.track(this);

	    this.chartContId = ko.observable("chartContId_" + self.podId);
	    this.noDataAvailable = ko.computed(function(){
	    	var $chartContId = $("#"+self.chartContId());
	    	if($chartContId.length){
	    		self.showChart = false;
	    		return true;
	    	}
	    	else{
	    		self.showChart = true;
	    		return false;
	    	}
	    });

	    this.renderHandler = function(){  
    	    self.currentPodBodyHeight = $('#pod_'+params.podId).height()-35;
	        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
	        self.currentPodBodyWidth = $('#pod_'+params.podId).width()-35;

	        self.getGraphData();
	    }

	    this.getGraphData = function(){
	    	if(self.podName === "Slow_Transactions"){
	    		var queryParams = '';

	    		self.selectedMonitoringType.forEach(function(item){
	    			queryParams += item +'&';
	    		});

	    		//var url = uiConstants.common.SERVER_IP+"/application/1/dashboardData?dataType=timeSeries&scenarioType=transactionAggregate&fromTime=1480422840000&toTime=1480424340000&monitoringType=EUE&aggregateLevel=1&pageSize=0&pageNumber=0&aggregateBy=something&orderBy=asc"
	    		var url = "http://www.mocky.io/v2/58411489100000371d358320?callback=?";
	        	requestCall(url,"GET", '',"Slow_Transactions",self.successCallBack, self.errorCallBack);
	    	}
	    	else if(self.podName === "High_Volume_Transactions"){
	    		var url = "http://www.mocky.io/v2/58411489100000371d358320?callback=?";
	    		//var url = "http://www.mocky.io/v2/58400d04100000aa02358192?callback=?";
	        	requestCall(url,"GET", '',"High_Volume_Transactions",self.successCallBack, self.errorCallBack);
	    	}
	    }

    	this.successCallBack = function(data, reqType){
    		
    		if(reqType === "Slow_Transactions"){
		    	if(data.responseStatus === "success"){
		    		var dataResult = data.graphData;
		    		var yAxisDataSet = dataResult.yAxis[0];

        			var xAxisLabel = dataResult.xAxis[0].label;
        			var yAxisLabel = yAxisDataSet.label;
        			var timeWindow = ["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"];//self.getTimeWindow(dataResult.startTime, dataResult.collationInterval, "3600");//dataResult.timeSeries;
        			var responseTime  = [];
        			var txnList    = [];
        			var responseTimeForChart = [];

        			var index = 0;
        			$.each(yAxisDataSet.data, function(item){
        				var self = this;
        				var currentKey = item;//Object.keys(self)[0];

        				txnList.push(currentKey);
        				responseTime.push(self);

        				responseTimeForChart[index] = [];
        				responseTimeForChart[index].push(txnList[index]);
        				responseTimeForChart[index].push.apply(responseTimeForChart[index],responseTime[index]);
        				index++;

        			});

        			self.chartDataObj = {
        				chartHeight: self.currentPodBodyHeight-5,
        				chartWidth: self.currentPodBodyWidth-5,
        				xAxisLabel: xAxisLabel,
        				yAxisLabel: yAxisLabel,
        				chartPaletteColors: self.chartPaletteColors,
        				timeWindowSet: timeWindow,
        				yaxisDataSet: responseTimeForChart,
        				podName: reqType,
        				gridHeader: [
					      {'columnTitle':'txnName', 'displayName':'Transaction Name' },
					      {'columnTitle':'resTime', 'displayName':'Response Time(ms)' },
                          {'columnTitle':'monitorType', 'displayName':'Monitor Type' },
                          {'columnTitle':'volume', 'displayName':'Volume' }
					    ],
        				gridBody: responseTimeForChart
    				};

    				self.chartDataObjForExpandableView = $.extend({},self.chartDataObj);// Clonning object for expandable View Chart/Grid with a different width & Height

    				self.chartDataObjForExpandableView.chartHeight = $('body').height()*0.70;
	        		self.chartDataObjForExpandableView.chartWidth = $('body').width()*0.85;

    				self.showChart = true;
		    	}
	        }
	        else if(reqType === "High_Volume_Transactions"){
        		if(data.responseStatus === "success"){
        			var dataResult = data.graphData;
		    		var yAxisDataSet = dataResult.yAxis[1];

        			var xAxisLabel = dataResult.xAxis[0].label;
        			var yAxisLabel = yAxisDataSet.label;
        			var timeWindow = ["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"];
        			var txnVolume  = [];
        			var txnList    = [];
        			var txnVolumeForChart = [];

        			// $.each(yAxisDataSet.data, function(item){
        			// 	var self = this;
        			// 	var currentKey = Object.keys(self)[0];

        			// 	txnList.push(currentKey);
        			// 	txnVolume.push(self[currentKey]);

        			// 	txnVolumeForChart[item] = [];
        			// 	txnVolumeForChart[item].push(txnList[item]);
        			// 	txnVolumeForChart[item].push.apply(txnVolumeForChart[item],txnVolume[item]);

        			// });
        			 
        			var index = 0;
        			$.each(yAxisDataSet.data, function(item){
        				var self = this;
        				var currentKey = item;//Object.keys(self)[0];

        				txnList.push(currentKey);
        				txnVolume.push(self);

        				txnVolumeForChart[index] = [];
        				txnVolumeForChart[index].push(txnList[index]);
        				txnVolumeForChart[index].push.apply(txnVolumeForChart[index],txnVolume[index]);
        				index++;

        			});
        			
        			self.chartDataObj = {
        				chartHeight: self.currentPodBodyHeight-5,
        				chartWidth: self.currentPodBodyWidth-5,
        				xAxisLabel: xAxisLabel,
        				yAxisLabel: yAxisLabel,
        				chartPaletteColors: self.chartPaletteColors,
        				timeWindowSet: timeWindow,
        				yaxisDataSet: txnVolumeForChart,
        				podName: reqType,
        				gridHeader: [
					      {'columnTitle':'txnName', 'displayName':'Transaction Name' },
					      {'columnTitle':'volume', 'displayName':'Volume' }
					    ],
        				gridBody: txnVolumeForChart
    				};

    				self.chartDataObjForExpandableView = $.extend({},self.chartDataObj); // Clonning object for expandable View Chart/Grid with a different width & Height

    				self.chartDataObjForExpandableView.chartHeight = $('body').height()*0.70;
	        		self.chartDataObjForExpandableView.chartWidth = $('body').width()*0.85;
	        		
	        		self.showChart = true;
				}
	        }
    	};

    	this.errorCallBack = function(reqType){
    		if(reqType === "Slow_Transactions"){
    			console.log("Error in getting Graph Data for POD - "+ "Slow_Transactions");
	        }
	        else if(reqType === "High_Volume_Transactions"){
	            console.log("Error in getting Graph Data for POD - "+ "High_Volume_Transactions");
	        }
    	};
	}

    DashboardTxnPerformance.prototype.dispose = function() {}

    return { viewModel: DashboardTxnPerformance, template: templateMarkup };

});

