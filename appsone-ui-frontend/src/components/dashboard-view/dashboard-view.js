define(['jquery','bootstrap','knockout','text!./dashboard-view.html','knockout-es5', 'bootstrap-toggle', 'application-model', 'enterprise-model', 'jvm-model', 'method-model', 'page-name-model', 'scenario-model', 'time-model'], function($,bootstrap,ko,templateMarkup,koES5, bts, apm, epm,jvms,methods,pagenames,scenarios,times){

	var podObject = (function(){

		if(podObject){
			return podObject;
		}
		else{
			return{				
				activeData: {
					activeDashboard: "",
					podData: "",
					selectedMenuItem: ""					
				},
				selectedApplication: undefined,
				selectedEnterprise: undefined,
				selectedJvm: undefined,
				selectedMethod: undefined,
				selectedTime: undefined,
				selectedScenario: undefined,
				selectedPageName: undefined,
				timeZoneOffset: undefined,
				selectedTransaction: undefined,
				selectedExtractor: undefined
			}
		}		
	})();

	var getDateWithUTCOffset = function (inputTzOffset){
    	var now = new Date(); // get the current time
    	var currentTzOffset = -now.getTimezoneOffset() / 60 // in hours, i.e. -4 in NY
    	var deltaTzOffset = inputTzOffset - currentTzOffset; // timezone diff
    	var nowTimestamp = now.getTime(); // get the number of milliseconds since unix epoch 
    	var deltaTzOffsetMilli = deltaTzOffset * 1000 * 60 * 60; // convert hours to milliseconds (tzOffsetMilli*1000*60*60)
    	var outputDate = new Date(nowTimestamp + deltaTzOffsetMilli) // your new Date object with the timezone offset applied.
    	return outputDate;
	}

	var dataHolder = (function(){

		var applicationlist = {}, globalDashboardList = [], 
			applicationSpecificDashboardList = [], activeApplication = "", activeDashboard="", 
			activeApplicationData = {}, activeDashboardData = {}, podList = [];

		return{
			setApplicationList: function(appList){
				applicationlist = appList;
			},
			getApplicationList: function(){
				return applicationlist;
			},

			setGlobalDashboardList: function(glblDbList){
				globalDashboardList = glblDbList;
			},
			getGlobalDashboardList: function(){
				return globalDashboardList;
			},

			setApplicationSpecificDashboardList: function(applSpDbLst){
				applicationSpecificDashboardList = applSpDbLst;
			},
			getApplicationSpecificDashboardList: function(){
				return applicationSpecificDashboardList;
			},

			setActiveApplication: function(activeApp){
				activeApplication = activeApp;
			},
			getActiveApplication: function(){
				return activeApplication;
			},

			setActiveDashboard: function(activeDb){
				activeDashboard = activeDb;
			},
			getActiveDashboard: function(){
				return activeDashboard;
			},

			setActiveApplicationData: function(actAppData){
				activeApplicationData = actAppData; 
			},
			getActiveApplicationData: function(){
				return activeApplicationData;
			},

			setActiveDashboardData: function(actDbData){
				activeDashboardData = actDbData;			
			},
			getActiveDashboardData: function(){
				return activeDashboardData;
			},

			setPodList: function(pods){
				podList = podList;
			},
			getPodList: function(){
				return podList;
			},
		
			filterApplicationData: function(filterItem){
				var masterDashboardList = applicationSpecificDashboardList.concat(globalDashboardList);
				return masterDashboardList.filter(function(elem){
					return elem.dashboardName === filterItem;
				});
			},

			filterOverViewPerformance: function(filterElem){
				return activeDashboardData.filter(function(elem){
					return elem.tabName === filterElem;
				});
			}
		}
	})();


	var dashboardModel = (function(){

		var urlList = {
			applicationUrl: "http://www.mocky.io/v2/583fd7ab240000411583b528",
			globalDashboardsUrl: "http://www.mocky.io/v2/58411d9b100000ac1e358331",
			applicationSpecificDashboardsUrl: "http://www.mocky.io/v2/5847b6553f00004829fe69e8"
		}

		return{
			populateApplicationList: function(me){
				var dataReturned = [];    
				var successCallback = function(data, reqType){        
					if(data["responseStatus"]==="success"){
						dataReturned = data["result"];
						podObject.timeZoneOffset = data["timeZoneOffset"];

						podObject.toDate = getDateWithUTCOffset(podObject.timeZoneOffset/3600000) || new Date();

						dataReturned.unshift({"applicationId":-1,"applicationName": "Applications"});
						me.applicationlist = dataReturned;    
						me.activateapplications = true;   
						me.activatetime = true;						
						dataHolder.setApplicationList(me.applicationlist);                
					}      
				}
				var errorCallback = function(data, reqType){
					console.log("Error in loading application list");
					return;
				}
				requestCall(urlList.applicationUrl+"?callback=?", "GET", "", "application", successCallback, errorCallback);  
			},

			populateTimeList: function(me){
				me.timelist = [{"id":1,"value":"1 hour"},{"id":2,"value":"1 day"},{"id":3,"value":"1 month"},{"id":4,"value":"1 year"}];
				//Will be active if application list is active
				me.activatetime = false;
			},

			activateApplicationDashboardList: function(me, myApplication){				
				var dataReturned = [], index=0; 
				dataHolder.setActiveApplication(myApplication);				
				var successCallback = function(data, reqType){        
					if(data["responseStatus"]==="success"){
						dataReturned = data["result"];
						dataHolder.setApplicationSpecificDashboardList(dataReturned);

						for(index=0; index<dataReturned.length; index++){
							if(dataReturned[index]["dashboardName"] === "Application Dashboard"){
								me.defaultdeactivate = false;
								menuUpdates.setSideMenuActiveOption(me, "default");
							}
							else if(dataReturned[index]["dashboardName"] === "JIM Dashboard"){
								me.jimdeactivate = false;								
							}
							else if(dataReturned[index]["dashboardName"] === "BVE Dashboard"){
								me.bvedeactivate = false;								
							}
						} 
					}      
				}
				var errorCallback = function(data, reqType){
					console.log("Error in loading application specific dashboard list");
					return;
				}
				requestCall(urlList.applicationSpecificDashboardsUrl+"?callback=?", "GET", "", "application", successCallback, errorCallback);  
			
			},

			activateGlobalDashboardList: function(me){
				var dataReturned = [], index=0;    
				var successCallback = function(data, reqType){        
					if(data["responseStatus"]==="success"){
						dataReturned = data["result"];
						dataHolder.setGlobalDashboardList(dataReturned);

						for(index=0; index<dataReturned.length; index++){
							if(dataReturned[index]["dashboardName"] === "Synthetic Monitoring"){
								me.syntheticdeactivate = false;
							}
							else if(dataReturned[index]["dashboardName"] === "NOC Dashboard"){
								//No Code yet
							}
						}                 
					}      
				}
				var errorCallback = function(data, reqType){
					console.log("Error in loading global dashboard list");
					return;
				}
				requestCall(urlList.globalDashboardsUrl+"?callback=?", "GET", "", "application", successCallback, errorCallback);  
			}
		}
	})();

	var menuPopulation = (function(){
		var captionFilter = function(elemList, cmpValue){
			return	!(elemList.some(function(elem){
				return elem.value === cmpValue;
			}));
		};

		function populate(me, subMenuList){
			var subMenu = null, subMenuName = null, subMenuValues = null;

			var subMenuIndex = 0;
			for(subMenuIndex = 0; subMenuIndex < subMenuList.length; subMenuIndex++){
				subMenu = subMenuList[subMenuIndex];					
				subMenuName = subMenu.parameterName;
				subMenuValues = subMenu.paramValues;

		
				if(subMenuName === "Scenarios"){	
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "Scenarios"});
					}
					
					me.scenariolist = subMenuValues;
					me.selectedScenario = subMenuName;
					me.activatescenario = true;
				}
				

				else if(subMenuName === "Methods"){		
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "Methods"});
					}			
					
					me.methodlist = subMenuValues;
					me.selectedMethod = subMenuName;
					me.activatemethods = true;
				}
				
				else if(subMenuName === "JVMs"){
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "JVMs"});	
					}
									
					me.jvmlist = subMenuValues;
					me.selectedJvm = subMenuName;
					me.activatejvm = true;
				}
				
				else if(subMenuName === "Page Name"){		
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "Page Name"});
					}			
					
					me.pagenamelist = subMenuValues;
					me.selectedPageName = subMenuName;
					me.activatepagename = true;
				}
				else if(subMenuName === "Transactions"){		
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "Transactions"});
					}			
					
					me.transactionlist = subMenuValues;
					me.selectedTransaction = subMenuName;
					me.activatetransaction = true;
				}	
				else if(subMenuName === "Extractors"){		
					if(captionFilter(subMenuValues, subMenuName)){
						subMenuValues.unshift({"id":-1,"value": "Extractor"});
					}			
					
					me.extractorlist = subMenuValues;
					me.selectedExtractor = subMenuName;					
					me.activateextractor = true;
					me.activateextractorifneeded = true;
				}				
			}
		}

		return {
			populateSubMenus: function(me, subMenuList){
				populate(me, subMenuList);
			}
		}

	})();

	

	var menuModel = (function(){
	
		return{
			setMenuModelData: function(me){
				me.acvtivatedefaultdashboard = true;
				me.acvtivatejimdashboard = true;
				me.acvtivatebvedashboard = true;
				me.acvtivatesyntheticdashboard = true;

				
				me.defaultdeactivate = true;
				me.jimdeactivate = true;
				me.syntheticdeactivate = true;
				me.bvedeactivate = true;
				
				dashboardModel.populateApplicationList(me);
				me.selectedApplication = "Applications";

				dashboardModel.populateTimeList(me);	
				me.selectedTime = me.timelist[0]["value"];				

				dashboardModel.activateGlobalDashboardList(me);

				me.selectedMethod = "";
				me.selectedPageName = "";	
				me.selectedScenario = "";
				me.selectedJvm ="";
				me.selectedTransaction ="";
				me.selectedExtractor = "";

        		me.violationcount = 0;
        		me.activatemethods = false;   
        		me.activatescenario = false;	
        		me.activatepagename = false;

			},
			getActiveDashboard: function(){
				return "not applicable";
			}
		}
	})();
	
	var menuUpdates = (function(){
		var resetTopMenu = function(me){	
			me.jvmlist = [];
			me.methodlist = [];
			me.scenariolist = [];
			me.pagenamelist =[];
			me.transactionlist =[];
			me.extractorlist = [];
			menuModel.setMenuModelData(me);
		}

		var toggleMe = function(me, elem, sidemenu, toggleListValue){
			menuUpdates.resetOptionalTopMenu(me);
				var toggleList = toggleListValue || null,
					subMenuList =null;

				if(toggleList === null || toggleList === undefined){
					if(elem === "Overview"){
						toggleList = dataHolder.filterOverViewPerformance("Overview");					
					}
					else if(elem === "Performance"){
						toggleList = dataHolder.filterOverViewPerformance("Performance");
					}
					else if(elem === "Violations"){
						toggleList = dataHolder.filterOverViewPerformance("Violations");
					}
				}				
				
				subMenuList = toggleList[0].tabConfigurationParams;
				
				podObject.activeData.podData = toggleList[0]["pods"];
				me.noPods = podObject.activeData.podData.length === 0;

				menuPopulation.populateSubMenus(me, subMenuList);	

				me.setPodComponentData();

		}
		return{
			resetOptionalTopMenu: function(me){	
				me.activatemethods = false;
        		me.activatescenario = false;
        		me.activateviolations = false; 
        		me.activatejvm = false;
				me.activatepagename = false;
				me.activatetransaction = false;
				me.activateextractor = false;
				me.activateextractorifneeded = true;
				me.jvmlist = [];
				me.methodlist = [];
				me.scenariolist = [];
				me.pagenamelist =[];
				me.transactionlist =[];
				me.extractorlist = [];

			//	menuModel.setMenuModelData(me);
			},

			setBasicValues: function(me){
				me.noPods = false;
				me.activateapplications = false;
   				me.activatetime = false;

   				me.activateoverview = true;
   				me.activateperformance = false;  
   				me.activateviolations = false;

	  			me.accountname = "";

				me.applicationlist = [];			
				me.timelist = [];
				me.jvmlist = [];
				me.methodlist = [];
				me.scenariolist = [];
				me.pagenamelist =[];
				me.transactionlist =[];
				me.extractorlist = [];

				me.highlightdefault = false;
	    		me.highlightjim = false;
	    		me.highlightsynthetic = false;
	    		me.highlightbve = false;
	    		
        		me.activatejvm = false;        	
        		me.activatetime = true;
        		me.activatetransaction = false;
        		me.activateextractor = false;
        		me.activateextractorifneeded = true;
        		me.violationvisible = false;

        		me.activateviolations = false;
        		me.activatetogglebar = false;
        		menuModel.setMenuModelData(me);
			},
			defaultActive: function(me){			
        		me.highlightdefault = true;
	    		me.highlightjim = false;
	    		me.highlightsynthetic = false;	  
	    		me.highlightbve = false;  		
			},

			jimActive: function(me){			
        		me.highlightdefault = false;
	    		me.highlightjim = true;
	    		me.highlightsynthetic = false;
	    		me.highlightbve = false;
			},
			bveActive: function(me){			
        		me.highlightdefault = false;
	    		me.highlightjim = false;
	    		me.highlightsynthetic = false;
	    		me.highlightbve = true;
			},

			syntheticActive: function(me){			
        		me.highlightdefault = false;
	    		me.highlightjim = false;
	    		me.highlightsynthetic = true;
	    		me.highlightbve = false;
			},
			setSideMenuActiveOption: function(me, sidemenu){
				podObject.activeData.activeDashboard = sidemenu;

				var clickedDashboardData = null;
				
				if(me.acvtivatejimdashboard && sidemenu === "jim"){
					clickedDashboardData = dataHolder.filterApplicationData("JIM Dashboard")[0];
        			dataHolder.setActiveDashboard("JIM Dashboard");
        			if(clickedDashboardData["tabs"].length > 1){
        				me.activatetogglebar = true;

        				if(clickedDashboardData["tabs"].length>2){
        					me.violationvisible = true;
        				}        				
        			}
        			dataHolder.setActiveDashboardData(clickedDashboardData["tabs"]); 

        			if(!me.activatetogglebar && clickedDashboardData["tabs"][0].tabConfigurationParams.length>0){
        				toggleMe(me, "Not Applicable", sidemenu, clickedDashboardData["tabs"]);
        			}

	    			this.jimActive(me);
        		}
        		else if(me.acvtivatesyntheticdashboard && sidemenu === "synthetic"){

        			//Resetting all applications since this is a global dashboard
        			dashboardModel.populateApplicationList(me);

        			//Deactivating all optional dashboards since this is a global dashboard
        			me.defaultdeactivate = true;
        			me.jimdeactivate = true;

        			clickedDashboardData = dataHolder.filterApplicationData("Synthetic Monitoring")[0];
        			dataHolder.setActiveDashboard("Synthetic Monitoring");
        			if(clickedDashboardData["tabs"].length > 1){
        				me.activatetogglebar = true;

        				if(clickedDashboardData["tabs"].length>2){
        					me.violationvisible = true;
        				}
        			}
        			dataHolder.setActiveDashboardData(clickedDashboardData["tabs"]);

        			if(!me.activatetogglebar && clickedDashboardData["tabs"][0].tabConfigurationParams.length>0){
        				toggleMe(me, "Not Applicable", sidemenu, clickedDashboardData["tabs"]);
        			}

        			this.syntheticActive(me);
        		}

        		else if(me.acvtivatebvedashboard && sidemenu === "bve"){
        			clickedDashboardData = dataHolder.filterApplicationData("BVE Dashboard")[0];        			
        			dataHolder.setActiveDashboard("BVE Dashboard");        
        			if(clickedDashboardData["tabs"].length > 1){
        				me.activatetogglebar = true;

        				if(clickedDashboardData["tabs"].length>2){
        					me.violationvisible = true;
        				}
        			}
        			
        			dataHolder.setActiveDashboardData(clickedDashboardData["tabs"]); 

        			if(!me.activatetogglebar && clickedDashboardData["tabs"][0].tabConfigurationParams.length>0){
        				toggleMe(me, "Not Applicable", sidemenu, clickedDashboardData["tabs"]);
        			}

        			this.bveActive(me);
        		}

        		else if(me.acvtivatedefaultdashboard && sidemenu === "default"){
        			clickedDashboardData = dataHolder.filterApplicationData("Application Dashboard")[0];        			
        			dataHolder.setActiveDashboard("Application Dashboard");
        			if(clickedDashboardData["tabs"].length > 1){
        				me.activatetogglebar = true;

        				if(clickedDashboardData["tabs"].length>2){
        					me.violationvisible = true;
        				}
        			}
        			dataHolder.setActiveDashboardData(clickedDashboardData["tabs"]);  

        			if(!me.activatetogglebar && clickedDashboardData["tabs"][0].tabConfigurationParams.length>0){
        				toggleMe(me, "Not Applicable", sidemenu, clickedDashboardData["tabs"]);
        			}

        			this.defaultActive(me);
        		}
        		else{
        			return;
        		}
        		podObject.activeData.podData = clickedDashboardData["tabs"][0]["pods"];

        		me.noPods = podObject.activeData.podData.length === 0;


        		me.setPodComponentData();
			},

			toggleMenu: function(me, elem, sidemenu){
				/*menuUpdates.resetOptionalTopMenu(me);
				var toggleList = null,
					subMenuList =null;

				if(elem === "Overview"){
					toggleList = dataHolder.filterOverViewPerformance("Overview");					
				}
				else if(elem === "Performance"){
					toggleList = dataHolder.filterOverViewPerformance("Performance");
				}
				else if(elem === "Violations"){
					toggleList = dataHolder.filterOverViewPerformance("Violations");
				}
				
				subMenuList = toggleList[0].tabConfigurationParams;
				
				podObject.activeData.podData = toggleList[0]["pods"];
				me.noPods = podObject.activeData.podData.length === 0;

				menuPopulation.populateSubMenus(me, subMenuList);	

				me.setPodComponentData();
*/	
				toggleMe(me, elem, sidemenu);
			}
		}
	})();
	
	
	function Dashboardview(params) {
	    var self = this;	
 
	    /* KO ES5 Tracker Start
		 *  All the properties which needs to acvtivatebvedashboardbe tracked as KO Observable, should be grouped together 
         */
	    this.currentDashboard = "default";
    	this.podInstanceArray = [];    
    	this.dashboardObject = podObject;

	  
		var	sidemenu = null;   
		menuUpdates.setBasicValues(self);
	
	    this.sideMenuSelected = function(data, event){
	    	if(event.target.id === "sidemenu"){
	    		return;	    	
	    	}	
	    	sidemenu = event.target.id;
	    	menuUpdates.setSideMenuActiveOption(self, sidemenu);
	    };

	 	//Time default selected
	    podObject.selectedTime = self.selectedTime;


        this.togglechange = function(data, event){
        	var elem = event.target.innerText;
        	menuUpdates.toggleMenu(self, elem, sidemenu);        	       	
        };
        
        this.changedMenu = function(data, event){
       
            var eventId = event.target.id; 
        	podObject.activeData.selectedMenuItem = eventId;     	

        	if(eventId === "application"){

        		//Deactivating all application specific dashboards
        		self.defaultdeactivate = true;
        		self.jimdeactivate = true;
        		self.bvedeactivate = true;

        		//Deactivating all submenus
        		self.activatemethods = false;
        		self.activatescenario = false;
	       		self.activateviolations = false; 
	       		self.activatejvm = false;
	       		
        		//Deactivating togglebar
        		self.activatepagename = false;
        		self.activatetogglebar = false;

        		var myApplication = self.applicationlist[self.selectedApplication];
        		podObject.selectedApplication = myApplication;
        		
        		podObject.timeZoneOffset = myApplication.timeZoneOffset;

     			//Activating Application Specific Dashboards
     			dashboardModel.activateApplicationDashboardList(self, myApplication);      	 
        	}        	
        	else if(eventId ==="jvm"){
        		podObject.selectedJvm = self.jvmlist[self.selectedJvm];
        		self.setPodComponentData();
        	}
        	else if(eventId ==="methods"){
        		podObject.selectedMethod = self.methodlist[self.selectedMethod];
        		self.setPodComponentData();
        	}
        	else if(eventId ==="pagename"){
        		podObject.selectedPageName = self.pagenamelist[self.selectedPageName];
        		self.setPodComponentData();
        	}
        	else if(eventId ==="scenario"){
        		podObject.selectedScenario = self.scenariolist[self.selectedScenario];
        		self.setPodComponentData();
        	}
        	else if(eventId ==="time"){
        		podObject.selectedTime = self.selectedTime;
	       		self.setPodComponentData();
        	}
        	else if(eventId ==="transaction"){
        		podObject.selectedTransaction = self.transactionlist[self.selectedTransaction];
        		self.activateextractorifneeded = false;
        		self.setPodComponentData();
        	}	
        	else if(eventId ==="extractor"){
        		podObject.selectedExtractor = self.extractorlist[self.selectedExtractor];
        		self.setPodComponentData();
        	}	        	
        };

       

      
       /* this.getPods = function(){
	    	requestCall("http://www.mocky.io/v2/581b2df2260000ce064b6be1?callback=?", "GET", "", "PodList", self.successCallback, self.errorCallback);
	    	console.log("ffff")
	    	console.log(self.dashboardObject);
	    }*/
      
       
        koES5.track(this);
       
	    /* KO ES5 Tracker End*/

	    self.setPodComponentData = function(){
	    	self.podInstanceArray.removeAll();			
			self.podInstanceArray.push.apply(self.podInstanceArray, self.dashboardObject.activeData.podData);
		};
 
	   
	   
	    
	    this.renderHandler = function(){ 
  			
  			if(uiConstants.common.DEBUG_MODE){ 
      			console.log("Dashboard Container Loaded, Selected dashboard: "+self.currentDashboard);
      		}

	    	var queryParams = '';
	    	queryParams = queryParams+ "dashboardType=" +self.currentDashboard;

	    	//self.setPodComponentData(); 
	    	//requestCall(uiConstants.common.SERVER_IP +"/podInstances?" + queryParams, "GET", "", "PodList", self.successCallback, self.errorCallback);
	    	

	    	//requestCall("http://www.mocky.io/v2/581b2df2260000ce064b6be1?callback=?", "GET", "", "PodList", self.successCallback, self.errorCallback);
		
		};



	  /*  this.successCallback = function(data, reqType) { 
	      if(reqType === "PodList"){
	        if(uiConstants.common.DEBUG_MODE) console.log("POD Instances");
	        if(uiConstants.common.DEBUG_MODE) console.log(data.result);
	        
	        self.podInstanceArray.removeAll();



	        self.podInstanceArray.push.apply(self.podInstanceArray, data.result);

	        console.log("WORKING");
	        console.log(data.result)
	      }

	    }

	    this.errorCallback = function(reqType) {
	      if(reqType === "PodList"){
	        if(uiConstants.common.DEBUG_MODE){ 
	        	console.log("Error in Getting PODs");
	    	};
	      }
	    }*/
	}

	Dashboardview.prototype.dispose = function() {
        // This runs when the component is torn down. Put here any logic necessary to clean up,
        // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
    }
	
	return { viewModel: Dashboardview, template: templateMarkup };
});


