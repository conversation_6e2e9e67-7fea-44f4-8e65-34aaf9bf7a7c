 var setPageNameData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"pageId":-1,"pageName": "Page Name"});
            me.pagenamelist = dataReturned;            
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading page name list");
    }
    requestCall("http://www.mocky.io/v2/58294e29120000e6068a2666?callback=?", "GET", "", "pagename", successCallback, errorCallback);  
  };

