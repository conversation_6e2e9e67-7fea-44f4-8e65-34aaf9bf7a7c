var setMethodData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"methodId":-1,"methodName": "Methods"});
            me.methodlist = dataReturned;            
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading method list");
    }
    requestCall("http://www.mocky.io/v2/58294d3f120000c2068a2665?callback=?", "GET", "", "method", successCallback, errorCallback);  
  };
