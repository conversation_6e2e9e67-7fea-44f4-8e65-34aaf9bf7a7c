   var setEnterpriseData = function(me){
      var dataReturned = [];    
      var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"enterpriseId":-1,"enterpriseName": "Enterprises"});
            me.enterpriselist = dataReturned;           
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading enterprise list");
    }
    requestCall("http://www.mocky.io/v2/58294a051200001c068a2660?callback=?", "GET", "", "enterprise", successCallback, errorCallback);  
  };


