var setTimeData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
       //     dataReturned.unshift({"timeId":-1,"timeName": "Time"});
            me.timelist = dataReturned;            
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading time list");
    }
    requestCall("http://www.mocky.io/v2/58295001120000e2068a2669?callback=?", "GET", "", "time", successCallback, errorCallback);  
  };
