var setJvmData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"jvmId":-1,"jvmName": "JVM\'s"});
            me.jvmlist = dataReturned;            
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading jvm list");
    }
    requestCall("http://www.mocky.io/v2/58294c4b12000091068a2661?callback=?", "GET", "", "jvm", successCallback, errorCallback);  
  };

