  var setApplicationData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){        
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"applicationId":-1,"applicationName": "Applications"});
            me.applicationlist = dataReturned;                     
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading application list");
    }
    requestCall("http://www.mocky.io/v2/58254b9b0f00006a0334f858?callback=?", "GET", "", "application", successCallback, errorCallback);  
  };