var setScenarioData = function(me){
    var dataReturned = [];    
    var successCallback = function(data, reqType){       
       if(data["responseStatus"]==="success"){
            dataReturned = data["result"];
            dataReturned.unshift({"scenarioId":-1,"scenarioName": "Scenarios"});
            me.scenariolist = dataReturned;            
        }      
    }
    var errorCallback = function(data, reqType){
        console.log("Error in loading scenario list");
    }
    requestCall("http://www.mocky.io/v2/58294f3512000016078a2667?callback=?", "GET", "", "scenario", successCallback, errorCallback);  
  };

