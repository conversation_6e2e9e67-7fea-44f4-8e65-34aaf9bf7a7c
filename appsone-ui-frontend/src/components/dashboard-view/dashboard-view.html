<div data-bind="template: {afterRender: renderHandler}">	
	<div class="row" style="margin-bottom: 5px; margin-left: -70px;">
		<div class="col-lg-1" >
			<span>&nbsp &nbsp &nbsp &nbsp &nbsp &nbsp &nbsp <b data-bind="value: accountname"></b></span>			
		</div>
		<div class="col-lg-11">
			<div class="row" id="mandatorytopmenu">
				<div class="col-lg-2">				
					<span data-bind="visible: activateapplications">           
           <select class="form-control" id="application" data-bind="value: selectedApplication, foreach: applicationlist, 
           event: { change: changedMenu }">                    
           <option data-bind="attr: { disabled: (applicationId=== -1), value: applicationId }, text: applicationName"></option>
         </select>
       </span>           
     </div>
     <div class="col-lg-2">
       <span data-bind="visible: activatetime">
        <select class="form-control" id="time" data-bind="value: selectedTime, foreach: timelist, 
        event: { change: changedMenu }">                    
        <option data-bind="attr: { 
        disabled: (id=== -1), 
        value: value 
      }, 
      text: value">    						
    </option>
  </select>

</span>					    
</div>

<div class="col-lg-3">
 <div class="row" data-bind="checked: togglevalue, event: { mouseup: togglechange }, visible: activatetogglebar">
  <div class="col-log-1 btn-group" data-toggle="buttons">
   <span class="btn btn-primary">
    <input type="radio" name="options" id="overview" value="Overview">Overview
  </span>
</div>

<div class="col-log-1 btn-group" data-toggle="buttons">
 <span class="btn btn-success">
  <input type="radio" name="options" id="performance" value="Performance">Performance
</span>  
</div>

<div class="col-log-1 btn-group" data-toggle="buttons" data-bind="visible: violationvisible">
 <span class="btn btn-danger">
  <input type="radio" name="options" id="violations" value="Violations">Violations
</span>  
</div>

</div>			
</div>
</div>		

<!-- -->
<div class="row" id="optionaltopmenu" style="margin-top: 5px;">
  <div class="col-lg-2" id="methods" data-bind="visible: activatemethods">	
    <select class="form-control" data-bind="value: selectedMethod, foreach: methodlist, 
    event: { change: changedMenu }">                    
    <option data-bind="attr: { 
    disabled: (methodId=== -1), 
    value: methodId 
  }, 
  text: methodName">    						
</option>
</select>

</div>


<div class="col-lg-2" data-bind="visible: activatescenario">	
 <select  id="scenario" class="form-control" data-bind="value: selectedScenario, foreach: scenariolist, 
 event: { change: changedMenu }">                    
 <option data-bind="attr: { 
 disabled: (id === -1), 
 value: id 
}, 
text: value">    						
</option>
</select>

</div>

<div class="col-lg-2" data-bind="visible: activatejvm">  
 <select  id="jvm" class="form-control" data-bind="value: selectedJvm, foreach: jvmlist, 
 event: { change: changedMenu }">                    
 <option data-bind="attr: { 
 disabled: (id === -1), 
 value: id 
}, 
text: value">               
</option>
</select>

</div>


<div>
<div class="col-lg-2" data-bind="visible: activatepagename">
  <select id="pagename" class="form-control" data-bind="value: selectedPageName, foreach: pagenamelist, 
  event: { change: changedMenu }">                    
  <option data-bind=" attr: { 
    disabled: (id=== -1), 
    value: id 
  }, 
  text: value">                
  </option>
  </select> 
</div>


<div class="col-lg-2" data-bind="visible: activatetransaction">
  <select id="transaction" class="form-control" data-bind="value: selectedTransaction, foreach: transactionlist, 
  event: { change: changedMenu }">                    
  <option data-bind=" attr: { 
    disabled: (id=== -1), 
    value: id 
  }, 
  text: value">                
  </option>
  </select> 
</div>

<div class="col-lg-2" data-bind="visible: activateextractor">
  <select id="extractor" class="form-control" data-bind="value: selectedExtractor, foreach: extractorlist, 
  event: { change: changedMenu }, attr: {disabled: activateextractorifneeded}" >                    
  <option data-bind=" attr: { 
    disabled: (id=== -1), 
    value: id 
  }, 
  text: value">                
  </option>
  </select> 
</div>
</div>
<!--
<div class="col-lg-2">
 <div id="violation" data-bind="visible: activateviolations">	<button type="button" class="btn btn-danger violations-style"               data-toggle="modal" data-target="#myModal">			
   Violations
 </button>
</div>

<div class="modal fade" id="myModal" role="dialog">
 <div class="modal-dialog">
  
  
  <div class="modal-content">
    <div class="modal-header">
     <button type="button" class="close" data-dismiss="modal">&times;</button>
     <h4 class="modal-title">
      Scenario Violations for enterprise: <span data-bind="text: violationcount"></span>
    </h4>
  </div>
  <div class="modal-body">
    <div class="row">
     <div class="col-lg-3">
       <select id="time" class="form-control" data-bind="
       value: selectedTime, 
       foreach: timelist, 
       event: { change: changedMenu }">                    
       <option data-bind="
       attr: { disabled: (timeId=== -1), 
       value: timeId }, 
       text: timeName">    						
     </option>
   </select>


 </div>
 <div class="col-lg-3">
   <select id="scenario" class="form-control" data-bind="
   value: selectedScenario, 
   foreach: scenariolist, 
   event: { change: changedMenu }">                    
   <option data-bind="
   attr: { disabled: (id=== -1), 
   value: id }, 
   text: value">    						
 </option>
</select>

</div>
<div class="col-lg-4">
  <select id="pagename" class="form-control" data-bind="
  value: selectedPageName, 
  foreach: pagenamelist, 
  event: { change: changedMenu }">                    
  <option data-bind="
  attr: { disabled: (pageId=== -1), 
  value: pageId }, 
  text: pageName">    						
</option>
</select>	
</div>
</div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
</div>
</div>
 
</div>
</div>
</div>-->
</div>
</div>

</div>
<div class="row" style="margin-left: -25px;">
  <div class="col-sm-1" id="sidemenu" data-bind ="click: sideMenuSelected">
    <div class="row" data-bind="visible: acvtivatedefaultdashboard, css: {deactivate: defaultdeactivate}">
      <img id="default" src = "images/dashboard_types/default.png" class="sp icon-design" alt="Default Dashboard" title="Default Dashboard" data-bind="css: { selected: highlightdefault}">
    </div>  

    <div class="row" data-bind="visible: acvtivatejimdashboard, css: {deactivate: jimdeactivate}" id="jimdashboard">
      <img id="jim" src = "images/dashboard_types/jvm.png" class="sp icon-design" alt="JIM Dashboard" title="JIM Dashboard" data-bind="css: { selected: highlightjim}">
    </div>
    <div class="row" data-bind="visible: acvtivatebvedashboard, css: {deactivate: bvedeactivate}" id="bvedashboard">
      <img id="bve" src = "images/dashboard_types/bve.png" class="sp icon-design" alt="BVE Dashboard" title="BVE Dashboard" data-bind="css: { selected: highlightbve}">
    </div>

    <div class="row" data-bind="visible: acvtivatesyntheticdashboard, css: {deactivate: syntheticdeactivate}">
      <img id="synthetic" src = "images/dashboard_types/synthetic.png" class="sp icon-design" alt="Synthetic Dashboard" title="Synthetic Dashboard" data-bind="css: { selected: highlightsynthetic}">
    </div>            
  </div>


  <div class="col-sm-11" data-bind="if : podInstanceArray.length > 0" style="margin-left: -49px;">
    <pod-cont-view params="{ currentDashboard : currentDashboard, 
                             podList:podInstanceArray, 
                             dashboardObject: dashboardObject}">
    </pod-cont-view>
  </div>  
  <div data-bind="visible : podInstanceArray.length === 0">
  <div class="alert alert-info" style="margin-left: 85px; width: 500px;">Please select an <b>Application</b> from the top <br/> OR <br/> One of the <b>Active Dashboards</b> on the left to continue</div>
</div>

<div data-bind="visible : noPods">
  <div class="alert alert-danger" style="margin-left: 85px; width: 500px;">  
    No data is available for the selected dashboard
  </div> 
</div>

</div>




<!-- <div data-bind="visible : podInstanceArray.length === 0">
  <span style="position: relative; left: 500px, right: 0 ">Please select an application or one of the active dashboards to continue</span>
</div> -->
</div>

<style type="text/css">
	.icon-design{
		height: 70px;
		width: 65px;
		padding: 10px;
		text-align: center;		
		border-radius: 10px;
		margin: 2px;
	}
	
	img.sp:hover, .selected{		
		height: 72px;
		width: 67px;
		-webkit-transition: width 0.5s, height 0.5; /* Safari */
   transition: width 0.5s, height 0.5s;
   outline: none;
   border-color: #9ecaed;
  box-shadow: 1px 1px 1px 1px #333;
  /* box-shadow:0 0 10px rgba(81, 203, 238, 1);*/


 }

 .violations-style{
   margin-top: 1px;
 }
 .deactivate {    	
   -webkit-filter: grayscale(100%); /* Safari 6.0 - 9.0 */
   filter: grayscale(100%);
   pointer-events: none;
   cursor: default;
 }
 .specialbtndefault {
    color: black;
    background-color: #fff;
    border-color: #ccc;
}

.activatebutton, .activatebutton:active{
    color: white;
    background-color: #286090;
    border-color: #204d74;
}
.activatebutton:focus{
  color: white;
}
/*.activatebutton:hover{
  color: white;
}*/
</style>
