<div class="col-sm-12" id="listConfigDetailsPage" data-bind="visible : currentViewIndex() == uiConstants.common.LIST_VIEW">
	<div class="form-inline">
		<div class="form-group" style="padding-bottom: 5px;">
			<!-- <select class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}"></select>
			<label data-bind="text:uiConstants.common.PER_PAGE"></label> -->

			<h4 data-bind="if: currentViewIndex() == uiConstants.common.LIST_VIEW"><span class="a1-page-title" data-bind="text: pageSelected"></span></h4>
			<div data-bind="template: {afterRender: renderHandler}"></div>
		</div>
		
		<div  class="form-group" style="float: right;">
			<span class="mandatoryField" data-bind="text: errorMsg()"></span>
			<button type="button" id="btnFilter" class="btn listViewBtn" data-bind="event: {click: function(){showFilterBox()}}"><span class="fa fa-filter listViewBtnTxt"></span>FILTER</button>

			<!-- ko if: window.commonAddEnabled() -->
				<button type="button" class="btn listViewBtn btn-primary" id="btnAdd" data-bind="attr: {disabled: !enableAdd()}, event: {click: switchView.bind($data,uiConstants.common.ADD_SINGLE_VIEW)}"><span class="fa fa-plus listViewBtnTxt"></span>ADD</button>
			<!-- /ko -->

			<!-- ko if: window.commonUpdateEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnEdit" data-bind="attr: {disabled: !enableEdit()}, event: {click: editConfig}"><span class="fa fa-pencil-square-o listViewBtnTxt"></span>EDIT</button>
			<!-- /ko -->

			<!-- ko if: window.commonAddEnabled() -->
				<button type="button" class="btn listViewBtn" id="btnClone" data-bind="attr: {disabled: !enableClone()}, event: {click: cloneConfig}"><span class="fa fa-clone listViewBtnTxt"></span>CLONE</button>
			<!-- /ko -->

			<span class="glyphicon glyphicon-cog" id="columnsOption" title="Show/Hide Columns" data-bind="event: {click: function(){toggleColumnsList()}}"></span>
			<div class="columnsListContainer">
				<span class="columnsListSpan">Columns:</span>

				<label title="Select All" class="columnsSelectAll"><input type="checkbox" id="selAllCols" style="margin-right: 5px;" checked>Select All</label>
				<hr class="horizRuleColumnsList">
				
				<div class="columnsList"></div>
				<button class="btn-small" type="button" style="margin-left: 5px; margin-bottom: 5px;" data-bind="event: {click : function(){modifyColumnsListBox()}}">Done</button>
				<button class="btn-small" type="button" style="margin-bottom: 5px;" data-bind="event: {click : function(){closeColumnsListBox()}}">Cancel</button>
			</div>
		</div>
	</div>

	<div id="filterBox" class="a1-filter-box">
		<div id="filterOptionsBox">
			<select class="chosen form-control" id="filterCriteria" data-bind="value: selFilterCategory, event: {change: function(){onFilterCatChange()}}, foreach: filterGridHeader" data-placeholder=" " >
				<option  data-bind="text: $data"></option>
			</select>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<input type="text" class="form-control filterFieldWidth" style="display: inline;" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"> 
			<!-- /ko -->

			<!-- ko if: selFilterCategory() == 'Created Time' || selFilterCategory() == 'Modified Time' -->
				<div class='input-group date filterFieldWidth' id='filterCreateModTime' style="display: inline-flex;">
		            <input type='text' class="form-control" placeholder="Pick Date/Hour" data-bind="value: filterValueArr()[gridHeader.indexOf(selFilterCategory())]"/>
		            <span id="calendarIcon" class="input-group-addon" title="Pick Date/Hour" style="width: 40px;">
		                <span class="glyphicon glyphicon-calendar"></span>
		            </span>
		        </div>
			<!-- /ko -->

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Producer Type' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fProducerTypeList" data-bind="foreach : producerTypesArr, value: filterValueArr()[gridHeader.indexOf('Producer Type')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="value: 0, text: uiConstants.common.SELECT_PRODUCER_TYPE"></option>
					<!-- /ko-->
					
					<option data-bind="value: $data.producerTypeId, text: $data.producerType"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Component Type' || selFilterCategory() == 'Component' || selFilterCategory() == 'Version' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fCompTypeList" data-bind="foreach : componentsArr, value: filterValueArr()[gridHeader.indexOf('Component Type')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_TYPE"></option>
					<!-- /ko-->
					
					<option data-bind="value: $data.componentTypeId, text: $data.componentType"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Component'  || selFilterCategory() == 'Version' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fCompNamesList" data-bind="foreach : componentNamesArr, value: filterValueArr()[gridHeader.indexOf('Component')]" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_NAME"></option>
					<!-- /ko-->
					
					<option data-bind="value: $data.componentId, text: $data.componentName"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Version' ? 'inline-block' : 'none'}">
				<select class="chosen form-control" id="fCompVersionsList" data-bind="foreach : componentVersionsArr, value: filterValueArr()[gridHeader.indexOf('Version')]" data-placeholder=" ">
					<!-- ko if: $index() == 0 -->
						<option data-bind="value: 0, text: uiConstants.common.SELECT_COMPONENT_VERSION"></option>
					<!-- /ko-->
					
					<option data-bind="value: $data.versionId, text: $data.version"></option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Category' ? 'inline-block' : 'none'}">
				<select id="fCategory" class="chosen form-control" data-bind="value: filterValueArr()[gridHeader.indexOf('Category')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}">
					<option value="2">All</option>
					<option value="0">Standard</option>
					<option value="1">Custom</option>
				</select>
			</div>

			<div class="filterFieldWidth" data-bind="style: {'display' : selFilterCategory() == 'Status' ? 'inline-block' : 'none'}">
				<select id="fActiveInactive" class="chosen form-control" data-bind="value: filterValueArr()[gridHeader.indexOf('Status')], event: {'chosen:hiding_dropdown':  function(){onFilterAdd()}}">
					<option value="2" >All</option>
					<option value="1">Active</option>
					<option value="0">Inactive</option>
				</select>
			</div>

			<!-- ko if: selFilterCategory() == 'Name' || selFilterCategory() == 'Modified By' || selFilterCategory() == 'Tags' -->
				<button type="button" class="btn a1-action-btn" data-bind="event: {click: function(){onFilterAdd()}}">ADD</button>
			<!-- /ko-->
		</div>

		

		<!-- ko if: filtersAdded() -->
			<table style="margin-top: 5px;">
				<tbody>
					<tr>
						<td class="filter-table-td" style="width: 100%;">
							<input type="text" class="form-control tokenfield" id="filters-tokenfield-typeahead" style="margin-top: 5px;" data-bind="value: filtersAdded">
						</td>

						<td class="filter-table-td">
							<button type="button" class="btn a1-action-link" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL <span class="fa fa-times-circle"></span></button>
						</td>

						<td>
							<button type="button" style="width: 70px; margin-left: 6px;" class="btn btn-primary" id="btnApplyFilter" data-bind="event: {click: function(){getFilterListData(true)}}">APPLY</button>

							<span id="filterOptionsDispBtn" class="fa fa-angle-double-down a1-options-display" data-bind="event: {click: function(){onFilterOptionsDispClick()}}" style="display: none;" title="Show Filter Options"></span>
						</td>
					</tr>
				</tbody>
			</table>
			<!-- <div style="width: 80%; display: inline-block;">
				
			</div>

			<div style="width: 15%; display: inline-block;">
				<button type="button" class="btn listViewBtn" id="btnAdd" data-bind="event: {click: function(){resetFilter()}}">CLEAR ALL</button>

				


			</div> -->
		<!-- /ko -->

	</div>

	<div>
		<div class="wrapper"><!-- style="width:100%;float:left; padding-top: 5px" -->
			<table id="listgrid" class="table table-sm table-hover a1-list-grid" style="width:100%">
				<thead class="a1-list-grid-header">
                    <tr data-bind="foreach: gridHeader">
						<!-- ko if: $index() == 0 -->
						<th class="actionControl">
							<input type="image" src="/images/unselect-16.png" id="unselectAll" title="Deselect All"></input>
						</th>
						<!-- /ko -->
						
						<th class="listViewCol textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="css: $parent.noSortColKeys.indexOf($parent.configTableHeaderObjKeys()[$index()]) == -1 ? 'header' : 'noSort', text: $data, attr:{'width': $data=='Status'?'100px':'auto'}, event: {click: function(){$parent.onHeaderClick($index(), $parent.configTableHeaderObjKeys()[$index()])}}" ></th> 	
					</tr>
				</thead>

				<tbody id="gridDataBody" data-bind="foreach: gridData" class="main" style="background-color: white;">
					<tr>
						<td style="text-align:center"><input type="checkbox" class="chkboxCol" data-bind="checked: $data.isSelected, attr:{rowData: $data}" title="Select"/></td>

						<!-- <td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.producerName, attr:{'title': $data.producerName}"></td> -->

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<a data-bind="text: $data.producerName, attr:{'title': $data.producerName}, click: $parent.onNameClick"></a>
						</td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.producerType, attr:{'title': $data.producerType}"></td>

						<td data-bind="foreach: $data.components" class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" >
							<span data-bind="text: $data.componentType, attr:{title: $data.componentType}"></span>
							<br>
						</td>

						<td data-bind="foreach: $data.components" class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<span data-bind="text: $data.componentName, attr:{title: $data.componentName}"></span>
							<br>
						</td>

						<td data-bind="foreach: $data.components" class="textOverflowOmmiter" 
						data-toggle="tooltip" data-placement="bottom">
						
							<span data-bind="text: getCommaSeparatedVal($data.componentVersions, ['componentVersion']), attr:{title: getCommaSeparatedVal($data.componentVersions, ['componentVersion'])}"></span>
							<br>
						</td>

						<!-- <td class="textOverflowOmmiter" data-bind="text: getCommaSeparatedVal($data.kpis, ['kpiName']), attr:{title: getCommaSeparatedVal($data.kpis, ['kpiName'])}" data-toggle="tooltip"
						></td> -->

						<td style="width: 20" class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.isCustom == 1?'Custom':'Standard'"></td>

						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.createdTime), attr:{'title': $parent.convertToLocalTime($data.createdTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $parent.convertToLocalTime($data.updatedTime), attr:{'title': $parent.convertToLocalTime($data.updatedTime)}"></td>
						<td class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom" data-bind="text: $data.updatedBy, attr:{'title': $data.updatedBy}"></td>

						<td class="col-xs-2">
							<div class="text-two-line-ellipsis" data-bind="text: $parent.getTagNames($data.tags), attr:{title: $parent.getTagNames($data.tags)}" data-toggle="tooltip"
							>
							</div>
						</td>

						<td style="width: 20" class="textOverflowOmmiter" data-toggle="tooltip" data-placement="bottom">
							<!-- ko if: $data.status == 1 -->
								<span>
									<img src="/images/green-circle-12.png">
									Active
								</span>
							<!-- /ko -->

							<!-- ko if: $data.status == 0 -->
								<span>
									<img src="/images/red-circle-12.png">
									Inactive
								</span>
							<!-- /ko -->
						</td>
					</tr>
				</tbody>
			</table>

			<!-- ko if: gridData().length == 0 -->
				<div colspan="11" style="text-align: center;"><h4><span data-bind="text: showListAvailable" ></span></h4></div>
			<!-- /ko -->
		</div>
	</div>

	<!-- ko if : currentViewIndex() == uiConstants.common.LIST_VIEW && gridData().length>0 -->
		<div class="config-pagination-footer">
			<label class="a1LabelFooter"></label>

			<div style="float:right;">
				<label class="a1LabelFooter" data-bind="text:uiConstants.common.PER_PAGE"></label>
				<select id="pageNum" class="form-control" data-bind="options: numOfPagesOption, value: totalRecordsPerPage, event: {change: curPage(1)}" style="display: inline; width: auto;"></select>

				<label class="a1LabelFooter" data-bind="text: recordsCountLabel()"></label>

				<span id="prevButton" class="fa fa-angle-left paginationPrev" data-bind="attr: {title: 'Previous Page', disabled: currentPage()<=1}, click: currentPage()<=1 ? '' : prevPage, css: {paginationArrowDisabled: currentPage()<=1}"></span>

				<!-- <span class="paginationLabel">Page <input type="number" min="1" class="pageNumber" data-bind="value: currentPage, event: {change: getListOrFilterData}"> of&nbsp; </span><strong class="paginationLabel" data-bind="text: totalPages()"></strong> -->
				<span class="fa fa-angle-right paginationNext" data-bind="attr: {title: 'Next Page', disabled: currentPage()==totalPages()}, click: currentPage()==totalPages() ? '' : nextPage, css: {paginationArrowDisabled: currentPage()==totalPages()}"></span>
			</div>
		</div>
	<!-- /ko -->
</div>

<div style="width:100%;float:left;" data-bind="if : currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || currentViewIndex() == uiConstants.common.EDIT_VIEW || currentViewIndex() == uiConstants.common.CLONE_VIEW || currentViewIndex() == uiConstants.common.READ_VIEW">
	<producer-add-edit params="{producerTypesArr: producerTypesArr, componentsArr: componentsArr, currentViewIndex: currentViewIndex, selectedConfigRows: selectedConfigRows, curPage: curPage, pageSelected: pageSelected}"></producer-add-edit>
</div>