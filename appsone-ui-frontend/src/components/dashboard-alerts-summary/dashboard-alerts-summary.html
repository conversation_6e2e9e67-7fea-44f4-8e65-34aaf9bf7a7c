<div data-bind=" template: {afterRender: renderHandler}">
	
	<!-- Alerts Summary Grid Structure start -->
	<div>
		<table id="alertSparkLineGrid" class="table table-fixedheader table-bordered podTable">
            <thead>
                <tr data-bind="foreach: alertPodGridHeaders">
                	<th class="textOverflowOmmiter" 
                    	data-toggle="tooltip" 
                    	data-placement="bottom" 
                    	data-bind=" attr:{ title : $data.displayName}, 
                    				text: $data.displayName,
                    				css: $parents[0].getHeaderCSS($data.columnTitle)
                    			  ">
					</th>
                </tr>
            </thead>
            <tbody data-bind="foreach: alertPodGridData">
                <!-- ko if: $data['type'] != 'Availability' --> 
                    <tr data-bind="foreach : $parents[0].alertPodGridHeaders">
                        <!-- ko if: $data['columnTitle'] != 'sparkline' --> 
                            <td data-toggle="tooltip" 
                            	data-placement="bottom" 
                            	data-bind=" attr:{ title : $parents[0][$data['columnTitle']]}, 
                            				text: $parents[0][$data['columnTitle']]"
                				style="padding: 2px;">
        					</td>
                        <!-- /ko -->

                        <!-- ko if: $data['columnTitle'] == 'sparkline' --> 
                            <td class="col-sm-4">
                            	<div data-bind=" component:{ name: $parents[1].chartComponentName, 
										 				 	 params: { 'podId': $parents[1].podId, 
											 				 		   'chartDataObj' : $parents[1].chartDataObj[$parentContext.$index()]
											 				 		 }
							 				 		   	   }" >
 				 		   	   	</div>
                            </td>
                        <!-- /ko -->
                    </tr>
                <!-- /ko -->

                <!-- ko if: $data['type'] == 'Availability' --> 
                    <tr>                        
                        <td data-toggle="tooltip" 
                        	data-placement="bottom" 
                    		data-bind=" text : $data['type']"
                    		style="padding: 2px;">
                    	</td>
                        <td data-toggle="tooltip" 
                        	data-placement="bottom" 
                        	colspan="3"  
                        	style="text-align:center;" 
                        	data-bind=" text : $data['value']"
                        	style="padding: 2px;">
                        </td>

                        <td class="col-sm-4">
                        	<div data-bind=" component:{ name: $parents[0].chartComponentName, 
									 				 	 params: { 'podId': $parents[0].podId, 
										 				 		   'chartDataObj' : $parents[0].chartDataObj[$index()]
										 				 		 }
						 				 		   	   }" >
			 		   	   	</div>
                        </td>
                    </tr>
                <!-- /ko -->

            </tbody>
        </table>        
  	</div>
    <!-- Alerts Summary Grid Structure end -->

</div>
