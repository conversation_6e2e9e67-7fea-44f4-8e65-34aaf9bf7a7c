define(['jquery','knockout', 'text!./dashboard-alerts-summary.html', 'knockout-es5','d3','c3' ], function($, ko, templateMarkup, koES5, d3, c3){

	function DashboardAlertsSummary(params) {
	    var self= this;

	    self.podId = params && params.podId || '';
	    self.podName = params && params.podName || '';
	    self.alertPodGridHeaders = [];
	    self.alertPodGridData = [];
	    self.chartComponentName = "sparkline-chart";
	    self.chartDataObj = [];

	    self.dashboardMenuObject = params.dashboardMenuObject;
	    
	    koES5.track(this);

	    self.renderHandler = function(){
	    	self.getGraphData();

	    	console.log("dashboard object in alert pod");
	    	console.log();
	    };

	    self.getGraphData = function(){
	    	requestCall("http://www.mocky.io/v2/5825752e0f0000680634f87a?callback=?","GET", '',"getAlertPODData",self.successCallBack, self.errorCallBack);

	 		//Timeseries data
	 		//requestCall("http://www.mocky.io/v2/584566d91100005d04f3c986?callback=?","GET", '',"getAlertPODTimeSeriesData",self.successCallBack, self.errorCallBack);
	 		
	 		//Snapshot Data
	 		//requestCall("http://www.mocky.io/v2/584567401100007404f3c988?callback=?","GET", '',"getAlertPODSnapShotData",self.successCallBack, self.errorCallBack);
	    };

	    /**
	     * Returns the class name to be binded to the header of given Title
	     * @param  {string} columnTitle - Header Title
	     * @return {string} cssClassStr - String of CSS class
	     */
	    self.getHeaderCSS = function(columnTitle){
	    	var cssClassStr = '';

	    		switch(columnTitle){
	    			case 'type': 
	    				cssClassStr = 'col-sm-3';
    					break;
				 	case 'high': 
				 		cssClassStr = 'col-sm-1';
    					break;
					case 'medium': 
						cssClassStr = 'col-sm-1';
    					break;
    				case 'low': 
    					cssClassStr = 'col-sm-1';
    					break;
    				case 'sparkline': 
    					cssClassStr = 'col-sm-6';
    					break;
				 	default:
				 		cssClassStr = '';
    					break;
	    		}

	    	return cssClassStr;
	    }

		self.successCallBack = function(data, reqType){
    		if(reqType === "getAlertPODData"){
    			if(data.responseStatus === "success"){
					self.alertPodGridHeaders = data.result[0].gridData.gridHeaders;
					self.alertPodGridData = data.result[0].gridData.gridBody;

					var alertPODGraphData = data.result[0].graphData;
					
					/*  Iterate through each objects in the alertPODGraphData array,
						which will have ID to which Chart to be binded to and the DataSet alertTrendArray for the chart
					*/
					$.each(alertPODGraphData, function(item){
						var currentArray = this;
						var chartDataObj = {
							chartContId: currentArray.sparkLineType,
        					chartHeight: '',
	        				chartWidth: '100%',
	        				alertTrendArray: currentArray.alertTrendArray
						};

						self.chartDataObj.push(chartDataObj);

						//self.sparkInitChart(this.sparkLineType+"_"+ self.podId,'','100%',this.alertTrendArray);
			        });
					
		            //Reducing top & bottom margin of Alert Grid
		            $("#alertSparkLineGrid tr td").css('padding','0px 8px 12px 8px').css('line-height','1.0');
    			}
    			else if(data.responseStatus === "failure"){
    				/*Handle the failure status  here*/
				}
    		}
    		

    		else if(reqType === "getAlertPODTimeSeriesData"){
    			if(data.responseStatus === "success"){
					var alertPODGraphData = data.graphData.yAxis[0];
					
					/*  Iterate through each objects in the alertPODGraphData array,
						which will have ID to which Chart to be binded to and the DataSet alertTrendArray for the chart
					*/
					// $.each(alertPODGraphData, function(item){
					// 	var currentArray = this;
					// 	var chartDataObj = {
					// 		chartContId: currentArray.sparkLineType,
     //    					chartHeight: '',
	    //     				chartWidth: '100%',
	    //     				alertTrendArray: currentArray.alertTrendArray
					// 	};

					// 	self.chartDataObj.push(chartDataObj);

					// 	//self.sparkInitChart(this.sparkLineType+"_"+ self.podId,'','100%',this.alertTrendArray);
			  //       });

					var alertTypes = [];
					var alertTrendArray = [];

			        //var index = 0;
        			$.each(alertPODGraphData.data, function(item){
        				var currObj = this;
        				var currentKey = item;//Object.keys(self)[0];

        				// alertTypes.push(currentKey);
        				// alertTrendArray.push(self);

        				var chartDataObj = {
							chartContId: currentKey,
        					chartHeight: '',
	        				chartWidth: '100%',
	        				alertTrendArray: currObj
						};

						self.chartDataObj.push(chartDataObj);


        				// responseTimeForChart[index] = [];
        				// responseTimeForChart[index].push(txnList[index]);
        				// responseTimeForChart[index].push.apply(responseTimeForChart[index],responseTime[index]);
        				// index++;

        			});
					
		            //Reducing top & bottom margin of Alert Grid
		            $("#alertSparkLineGrid tr td").css('padding','0px 8px 12px 8px').css('line-height','1.0');
    			}
    			else if(data.responseStatus === "failure"){
    				/*Handle the failure status  here*/
				}
    		}
    		else if(reqType === "getAlertPODSnapShotData"){
    			if(data.responseStatus === "success"){
					var alertPODGridData = data.graphData.yAxis[0];
					self.alertPodGridHeaders = data.result[0].gridData.gridHeaders;
					self.alertPodGridData = data.result[0].gridData.gridBody;

					/*  Iterate through each objects in the alertPODGraphData array,
						which will have ID to which Chart to be binded to and the DataSet alertTrendArray for the chart
					*/
					$.each(alertPODGraphData, function(item){
						var currentArray = this;
						var chartDataObj = {
							chartContId: currentArray.sparkLineType,
        					chartHeight: '',
	        				chartWidth: '100%',
	        				alertTrendArray: currentArray.alertTrendArray
						};

						self.chartDataObj.push(chartDataObj);

						//self.sparkInitChart(this.sparkLineType+"_"+ self.podId,'','100%',this.alertTrendArray);
			        });
					
		            //Reducing top & bottom margin of Alert Grid
		            $("#alertSparkLineGrid tr td").css('padding','0px 8px 12px 8px').css('line-height','1.0');
    			}
    			else if(data.responseStatus === "failure"){
    				/*Handle the failure status  here*/
				}
    		} 		
    	}

    	self.errorCallBack = function(reqType){
    		if(reqType === "getAlertPODData"){
    			showMessageBox("Error while fetching Alert POD Data");
    		}
    	}
	}

	DashboardAlertsSummary.prototype.dispose = function() {}

	return { viewModel: DashboardAlertsSummary, template: templateMarkup };
});

