<div data-bind="template :{ afterRender: renderHandler} ">
  <!-- ko if: showChart-->
  <div data-bind=" component:{ name: chartComponentName, 
                               params: { 'podId': podId, 
                                         'chartDataObj' : chartDataObjForExpandableView,
                                         'chartContId':chartContId
                                       }
                             }" >
  </div>
  <!-- /ko -->
</div>
