define(['jquery','knockout','bootstrap','bootstrap-switch','d3','c3','fusionCharts','text!./dashboard-aggregated-txns.html','hasher','knockout-es5'], function($,ko,bootstrap,bs,d3,c3,fusioncharts,templateMarkup,hasher, koES5) {

  function DashboardAggregatedTxns(params) {
    var self = this;
    self.showChart = false;
    self.chartComponentName = "single-axis-area-spline-chart";
    this.currentPodBodyHeight = '';
    this.currentPodBodyWidth = '';
    this.podId = params.podId;
    this.podTitle = params.podTitle;
    this.podName = params.podName;
    this.chartDataObj = {};
    this.showExpandableView = true;
    this.dualView = params.dualView;
    this.chartDataObjForExpandableView = {};
    this.chartPaletteColors = "#0075c2,#1aaf5d,#ff4000,#0000ff,#524e4e"; //RGB color code seprated by comma string

    //this.xAxisDataApp = ["10:00","10:10","10:20","10:30","10:40","10:50","11:00"];
    
    // this.yAxisData = [
    //         ['Unknown',13, 12, 19, 32, 45, 47],
    //         ['Timeout', 21, 20, 27, 32, 47, 49],
    //         ['Failed', 32, 12,70, 35, 40, 90],
    //         ['Slow', 30, 10, 12, 20, 10, 50],
    //         ['Good', 100, 200, 200, 400, 150, 250],
    // ];
    
    koES5.track(this);

    this.chartContId = ko.observable("chartContId_Modal_" + self.podId);
    
    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podType);

    this.renderHandler = function(){  
        self.currentPodBodyHeight = $('#pod_'+params.podId).height()-35;
        self.currentPodBodyWidth = $('#pod_'+params.podId).width()-35;
        self.getGraphData();
    };

    this.getGraphData = function(){
      //var url = uiConstants.common.SERVER_IP+"/application/1/dashboardData?dataType=timeSeries&scenarioType=transactionAggregate&fromTime=1480422840000&toTime=1480424340000&monitoringType=EUE&aggregateLevel=1&pageSize=0&pageNumber=0&aggregateBy=something&orderBy=asc"
      var url = "http://www.mocky.io/v2/58412465100000a31f358342?callback=?";
      requestCall(url,"GET",null,"Aggregated_Transactions", self.successCallBack, self.errorCallBack);
    };

    this.successCallBack = function(data,reqType){
      if(reqType === "Aggregated_Transactions"){
        if(data.responseStatus.toLowerCase() === "success"){
          var dataResult = data.graphData;
          var yAxisDataSet = dataResult.yAxis[0];

          var xAxisLabel = dataResult.xAxis[0].label;
          var yAxisLabel = yAxisDataSet.label;
          var timeWindow = ["10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00",
                            "10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00",
                            "10:00","10:05","10:10","10:15","10:20","10:25","10:30","10:35","10:40","10:45","10:50","10:55","11:00"
                           ];
          var txnStatusList  = [];//dataResult.transactionVolume;
          var txnStatusCount = [];
          var txnStatusCountForChart = [];

          var index = 0;
          $.each(yAxisDataSet.data, function(item){
            var self = this;
            var currentKey = item;//Object.keys(self)[0];

            txnStatusList.push(currentKey);
            txnStatusCount.push(self);

            txnStatusCountForChart[index] = [];
            txnStatusCountForChart[index].push(txnStatusList[index]);
            txnStatusCountForChart[index].push.apply(txnStatusCountForChart[index],txnStatusCount[index]);
            index++;

          });
          
          self.chartDataObjForExpandableView = {
            chartHeight: self.currentPodBodyHeight-5,
            chartWidth: self.currentPodBodyWidth-5,
            xAxisLabel: xAxisLabel,
            yAxisLabel: yAxisLabel,
            chartPaletteColors: self.chartPaletteColors,
            timeWindowSet: timeWindow,
            yaxisDataSet: txnStatusCountForChart,
            podName: reqType,
            gridHeader: [
              {'columnTitle':'txnName', 'displayName':'Transaction Name' },
              {'columnTitle':'volume', 'displayName':'Volume' }
            ],
            gridBody: txnStatusCountForChart
          };

          self.chartDataObjForExpandableView.chartHeight = $('body').height()*0.70;
          self.chartDataObjForExpandableView.chartWidth = $('body').width()*0.85;

        //self.chartDataObjForExpandableView = $.extend({},self.chartDataObj); // Clonning object for expandable View Chart/Grid with a different width & Height

        // self.chartDataObjForExpandableView.chartHeight = $('body').height()*0.80;
        // self.chartDataObjForExpandableView.chartWidth = $('body').width()*0.85;
          
          self.showChart = true;
        }
      }

    };

    this.errorCallBack = function(reqType){
      if(reqType === "Aggregated_Transactions"){
        console.log("Error in getting Graph Data for POD - "+ "Aggregated_Transactions");
      }
    };
  }

  
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  DashboardAggregatedTxns.prototype.dispose = function() { };
  
  return { viewModel: DashboardAggregatedTxns, template: templateMarkup };

});