define(['knockout', 'text!./message-dialog-box.html','ui-common'], function(ko, templateMarkup, uicommon){
	function Messagedialogbox(params) {
	    var self = this;

	    /*Initialization point for this viewmodel*/
	    self.initialize = function(){
	    	self.displayModalPopup = ko.observable(false);
	    	self.isCloseRequired = params && params.isCloseRequired || ko.observable(false);
	    	self.componentNameToBeBinded = params && params.componentBindingType || 'to be decided what to go by default';
	    	self.modalHeaderTitle = params && params.modalHeaderTitle || '';
	    	self.componentInstanceIDs = params && params.componentInstanceIDs || ko.observableArray();
	    	self.childViewModel = ko.observable();
		};

		self.initialize();

		/*This handler will be called upon rendering of the view*/
		self.renderHandler  = function(){
			$('#messageDialogBoxComponent').on('shown.bs.modal', function () {
				document.activeElement.blur();
				$("#cancelButton").focus();
			});
		};

		/*This handler will be called when closing the modal.
		* On closing of the modal, history should go back to previous tab
		*/
		self.closeModal = function(){
			//$('#rootwizard').bootstrapWizard('previous');
		};

		/*This handler will be called when closing the modal.
		* On click of OK button, Modal will be closed by doing certain operations:
		* 1. Publish the topic "OKButtonClicked" which will trigger its subscriber
		*/
		self.actionOnModal = function(){
			uicommon.postbox.publish("Calling component on submit handler","OKButtonClicked");
		};

		/*
        * Subscribed to topic "CallBack On click of Ok Button". 
        * When that topic is published, this subscriber will get triggered and execute the passed callback.
        */
		uicommon.postbox.subscribe(function(value){
			value();
		},"CallBack On click of Ok Button");
	    
	}

	Messagedialogbox.prototype.dispose = function() { };

	return { viewModel: Messagedialogbox, template: templateMarkup };
});