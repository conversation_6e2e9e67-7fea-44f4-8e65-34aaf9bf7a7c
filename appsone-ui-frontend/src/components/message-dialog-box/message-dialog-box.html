<div data-bind="template: {afterRender: renderHandler}"></div>
<div id="messageDialogBoxComponent" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" data-bind="visible: displayModalPopup">
	<div class="modal-dialog form-inline">
		<div class="modal-content" style="width: 450px;">
			<div class="modal-header" style="padding-top: 10px; padding-bottom: 10px; border-top: 0px;background-color: lightblue;">
				<h4 data-bind="text: modalHeaderTitle"></h4>
				<button type="button" class="close" data-dismiss="modal" style="opacity: 1" data-bind="click: closeModal, visible: isCloseRequired">&times;</button>
			</div>
			<div class="modal-body">
				<div data-bind="component: {name: componentNameToBeBinded, params: {childViewModel: childViewModel, componentInstanceIDs:componentInstanceIDs}}"></div>
			</div>

			<div class="modal-footer form-horizontal" style="padding-top: 5px; padding-bottom: 5px; border-top: 0px;background-color: lightblue;">
				<button id="okButton" type="button" class="btn btn-default" data-dismiss="modal" data-bind="click: actionOnModal ">OK</button>
				<button id="cancelButton" type="button" class="btn btn-default" data-dismiss="modal" data-bind="click: closeModal">Cancel</button>
			</div>
		</div>
	</div>
</div>
