define(['jquery','knockout', 'text!./view-individual-profile.html','ui-constants','ui-common'], function($,ko,templateMarkup,uiConstants,uicommon) {

	function ViewIndividualProfile(params) {
		var self = this;

		this.selectedConfigRows = ko.observableArray();
		this.currentViewIndex = ko.observable(uiConstants.common.EDIT_VIEW);
		this.pageSelected = ko.observable("User Profile");

		this.renderHandler = function(){
			var userProfileId = 1; //Read user profile ID from token

			//requestCall("http://www.mocky.io/v2/580dd562120000930a0786fd?callback=?", "GET", "", "indivUserProfileChange", successCallback, errorCallback);
			requestCall(uiConstants.common.SERVER_IP + "/loggedinUserProfile", "GET", "", "indivUserProfile", successCallback, errorCallback);
		}

		function successCallback(data, reqType) {
			if(reqType === "indivUserProfile"){
				self.selectedConfigRows([data.result]);
			}
		}

		function errorCallback(reqType) {
			if(reqType === "indivUserProfile"){
  				showMessageBox(uiConstants.indivUserProfileConfig.ERROR_GET_INDIVIDUAL_USER_PROFILE, "error");
  			}
  		}
	}

	ViewIndividualProfile.prototype.dispose = function() { };
	return { viewModel: ViewIndividualProfile, template: templateMarkup };
});