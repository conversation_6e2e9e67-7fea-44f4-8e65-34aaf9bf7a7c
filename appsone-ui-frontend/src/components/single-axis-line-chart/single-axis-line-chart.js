define(['jquery','knockout', 'text!./single-axis-line-chart.html', 'knockout-es5','d3','c3'], function($, ko, templateMarkup, koES5, d3, c3){

	function SingleAxisLineChart(params) {
    	var self = this;
    	this.podId = params && params.podId || '';
    	this.chartDataObj = params.chartDataObj;
    	this.chartContId = params.chartContId;
        koES5.track(this);

        //this.chartContId = "chartContId_" + self.podId;
        
    	this.renderHandler = function(){
    		var cdObj = self.chartDataObj;
    		var chartGridContId = self.chartContId;//koES5.getObservable(self,'chartContId')();
    		console.log(cdObj.yaxisDataSet);
    		self.initChart(chartGridContId,cdObj.chartHeight, cdObj.chartWidth, cdObj.xAxisLabel, cdObj.yAxisLabel, cdObj.chartPaletteColors, cdObj.timeWindowSet, cdObj.yaxisDataSet, cdObj.podName);
    	}
        
        this.initChart = function(chartCont, chartHeight, chartWidth,xAxisLabel,yAxisLabel,chartPaletteColors,timeWindowSet,yaxisDataSet, reqType){
        /*Parameters:-
            chartCont :- container Id where Chart will render
            chartHeight :- chart canvas height
            chartWidth :- chart canvas width
            xAxisLabel  :- chart X Axis Label
            yAxisLabel :- chart Y AXis Label
            chartPaletteColors :- chart different line color codes(hex) within string seprated by comma(,)
            timeWindowSet :- chart x AXis Data set Array(for Ex - Time window)
            yaxisDataSet :- chart actual entity data set(Array of object)
            reqType :- Pod Type 
        */
            
	        var chart = c3.generate({
	            bindto: document.getElementById(chartCont),
	            size: {
	              width: parseInt(chartWidth),
	              height: parseInt(chartHeight),
	            },
	            grid: {
	              focus: {
	                show: false
	              }
	            },
	            area: {
	              zerobased: true
	            },
	            point: {
	                  show: true,
	                  
	                }, 
	            data: {
	                  columns: yaxisDataSet,
	                  //groups: [['Txn1','Txn2','Txn3','Txn4','Txn5']],
	                  type: 'spline', // 'line', 'spline', 'step', 'area', 'area-step' are also available to stack
	                  colors: {
	                      'Txn1': 'rgb(31, 119, 180)',
	                      'Txn2': 'rgb(255, 127, 14)',
	                      'Txn3': 'rgb(44, 160, 44)',
	                      'Txn3': 'rgb(148, 103, 189)',
	                      'Txn5':'rgb(140, 86, 75)',                      
	                  },

	              },
	              zoom:{
	                enabled: false,
	              },
	              padding: {
	                //bottom: 40,
	                //top: 5,
	                //right: 0,
	                //left : 0,

	              },
	            legend: {
	                  show: true
	              },
	              axis: {
	                rotated: false,
	                y: {
	                  show:true,
	                  type: 'category',
	                  label: {
	                    text: yAxisLabel,
	                    position: 'inner-top',   // inner-top,inner-middle,inner-bottom,outer-top,outer-middle,outer-bottom

	                  },                  
	                  //min: 0,
	                  //max: Math.max.apply(Math,tickYValues),
	                  //tick: {
	                      //min: 0,
	                      //max: 5,
	                      //format: d3.format('d'),
	                      //values: [tickYValues],
	                      /*format: function(d){
	                           if(d%2==0) return d;
	                      },*/
	                    //},

	                },
	                x: {
	                  show:true,
	                  type: 'category',
	                  categories: timeWindowSet,
	                  tick: {
		                  rotate: -90,
		                  multiline: false,
		                  culling: false,
	                        
                      },
	                  label: {
	                    text: xAxisLabel,
	                    position: 'inner-center',// inner-right,inner-center,inner-left,outer-right,outer-center,outer-left
	                  },
	                  height: 50,
	                },
	              },
	              tooltip: {
	                  grouped:true,

	                  contents: function (d, defaultTitleFormat, defaultValueFormat, color) {
	                      var $$ = this, config = $$.config,
	                          titleFormat = config.tooltip_format_title || defaultTitleFormat,
	                          nameFormat = config.tooltip_format_name || function (name) { return name; },
	                          valueFormat = config.tooltip_format_value || defaultValueFormat,
	                          text, i, title, value, name, bgcolor;
	                          var gridData = yaxisDataSet;
                          // var gridData = reqType === "Slow_Transactions"? 
                          // 				 self.responseTime : 
                          // 				 reqType === "High_Volume_Transactions"? self.transactionVolume : undefined;

      				 		var vol_respTime = reqType === "Slow_Transactions"	?	'ResTime(ms)' : 
                          				 	   reqType === "High_Volume_Transactions"	? 	'Volume' : 
                          				 	   reqType === "BVE"	?	'Count' : "Not Specified";
	                      for (i = 0; i < d.length; i++) {
	                          if (! (d[i] && (d[i].value || d[i].value === 0))) { continue; }

	                          if (! text) {
	                              title = titleFormat ? titleFormat(d[i].x) : d[i].x;
	                              text = "<table class='" + $$.CLASS.tooltip + "'>" + (title || title === 0 ? "<tr><th colspan='3'>Time - " + title + "</th></tr>" : "");
	                          }
	                          
	                          name = nameFormat(d[i].name);
	                          value = valueFormat(d[i].value, d[i].ratio, d[i].id, d[i].index);
	                          bgcolor = $$.levelColor ? $$.levelColor(d[i].value) : color(d[i].id);


	                        if(i == 0){
	                              text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
	                              text += "<td class='name'><b>Name</b> &nbsp&nbsp</td>";
	                              text += "<td class='value'><b>"+ vol_respTime +"</b></td>";
	                              text += "</tr>";
	                         }
	                          text += "<tr class='" + $$.CLASS.tooltipName + "-" + d[i].id + "'>";
	                          text += "<td class='name'><span style='background-color:" + bgcolor + "'></span>" + name + "&nbsp&nbsp</td>";
	                          var vol_respTimeData = gridData ? gridData[i][d[i].index+1] :"NA" ;
	                          text += "<td class='value'>" + vol_respTimeData + "</td>";
	                          //text += "<td class='value'>" + self.responseTime[i][d[i].index+1] + "</td>";
	                          text += "</tr>";
	                        

	                      }
	                      return text + "</table>";
	                  }


	              }
	        });
    	}
    }
    
    SingleAxisLineChart.prototype.dispose = function() {}

	return { viewModel: SingleAxisLineChart, template: templateMarkup };
});

