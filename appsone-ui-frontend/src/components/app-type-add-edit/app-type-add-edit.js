define(['jquery','knockout','jquery-chosen','bootstrap','bootstrap-switch','bootstrap-tokenfield','typeahead','text!./app-type-add-edit.html','hasher','validator','ui-constants','ui-common','highchecktree'], function($,ko,jc,bt,bts,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,highchecktree) {

	function ConfigAddEdit(params) {	
		var self = this;

		this.configId = ko.observable(0);
		this.configName = ko.observable("");
		this.configDescription = ko.observable();
		this.currentViewIndex = params.currentViewIndex;
		this.configStatus = ko.observable(1);
		this.configAny = ko.observable(false);
		this.errorMsg = ko.observable("");
    	this.firstFieldToShowErr = ko.observable("");
		this.pageSelected = ko.observable("");
		configType = localStorage.configType;
		this.componentsArr = ko.observableArray();
		this.componentsEditArr = ko.observableArray();
		this.compNameVersionArr = ko.observableArray();
		this.compNameVersionObjArr = ko.observableArray();
		this.compVersionGridData = ko.observableArray();
		this.isModal = ko.observable(false);
		this.appType = params.appType;
		this.curOper = ko.observable("addcomp");
  		this.selectedConfigNames = ko.observable("");
  		this.disableReset = ko.observable(true);
  		this.selectedConfigRows = params.selectedConfigRows;
		var rowToEdit;
		var compTypeIdsEditArr = [];
		this.toggleCancelCloseBtn = ko.observable(uiConstants.common.CONST_CANCEL);

		/*if(localStorage.selectedConfigRows)
			self.selectedConfigRows(JSON.parse(localStorage.selectedConfigRows));
		else
			self.selectedConfigRows([]);*/

		this.renderHandler=function(){
			$(".mandatoryField").css("display", self.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
			
			/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});

			$("#txtName").focus();

			$("#configStatus").bootstrapSwitch({
				'onColor': "success",
				'offColor': "danger",
				'onText': "Active",
				'offText': "Inactive"
			});

			$('#configStatus').bootstrapSwitch('state', self.configStatus());

			$("#configStatus").on('switchChange.bootstrapSwitch', function () {
				self.configStatus($('#configStatus').bootstrapSwitch('state')?1:0);
			});

			//event handler for checktree checkbox click
			$("#tree-container").bind('click', '.checkbox', function (event) {
        		if(event.target.className.indexOf("checkbox") != -1){
        			self.disableReset(false);
        		}
        	});

        	if(self.currentViewIndex() != uiConstants.common.ADD_SINGLE_VIEW && self.currentViewIndex() != uiConstants.common.READ_VIEW &&  self.selectedConfigRows() && self.selectedConfigRows().length>0){
				self.selectedConfigNames(": "+getCommaSeparatedVal(self.selectedConfigRows(), ["name"]));
			}
			
			if(params.isModal){
				self.isModal(true);
			}

			if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW || self.isModal()){
				debugger;
				requestCall(uiConstants.common.SERVER_IP + "/component?status=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			}

			setPageTitle();
			if(self.currentViewIndex() == 3){
				self.pageSelected("Edit "+self.pageSelected());
				editSingleConfig(self.selectedConfigRows());
				if(!self.selectedConfigRows()[0].status){ //if the config is inactive
					setConfigUneditable(true);
				}
			}

			else if(self.currentViewIndex() == 5){
				if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
				viewConfig(self.selectedConfigRows());
			}
			else{
				self.pageSelected("Add "+self.pageSelected());
			}

			$("#compTypeList").on('change', function () {
				onCompTypeChange(self.componentsArr(), $("#compTypeList").chosen().val(), true);
			});

			$("#compVersionList tbody").on('click', '.buttonedit', function(e){
				self.curOper("editcomp");
			 	rowToEdit = $(this).closest('tr').get(0).rowIndex-1;
			 	if(uiConstants.common.DEBUG_MODE)console.log(rowToEdit);
			 	$("#compTypeList").val(self.compVersionGridData()[rowToEdit].compTypeId).trigger('chosen:updated');
			 	onCompTypeChange(self.componentsArr(), $("#compTypeList").chosen().val(), true);
			 	
			});

			$("#compVersionList tbody").on('click', '.buttondelete', function(e){
			    var tableObj = this;
			    showMessageBox(uiConstants.applicationType.CONFIRM_COMP_ASSOCIATION_DELETE, "question", "confirm", function confirmCallback(confirmDelete){
					if (confirmDelete) {
				 		var rowToDelete = $(tableObj).closest('tr').get(0).rowIndex-1;
				 		if(uiConstants.common.DEBUG_MODE)console.log(rowToDelete);

				 		self.compVersionGridData.splice(rowToDelete,1);
				 	}
				 });
			});
		}

		function onCompTypeChange(componentsArr, compTypeId, loadCheckTree){
			self.compNameVersionObjArr([]);
			self.compNameVersionArr([]);

			if(uiConstants.common.DEBUG_MODE)console.log(componentsArr);
			if(uiConstants.common.DEBUG_MODE)console.log(compTypeId);
			
			debugger;
			var compNameVerArr = [];
			for(compArr in componentsArr){
				if(componentsArr[compArr].componentTypeId == compTypeId){
					compNameVerArr = componentsArr[compArr].components;
					break;
				}
			}

			if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
				var compIds = [];
				var compVersions = [];
				var compIdVerionsEditArr = [];
				var compsArr = $.grep(self.componentsEditArr(), function(e){ return  e.componentTypeId == compTypeId; });

				if(compsArr.length){
					for(var compEdit in compsArr[0].components){
						compIds.push(compsArr[0].components[compEdit].componentId);
					
						for(var verEdit in compsArr[0].components[compEdit].versions){
							compVersions.push(compsArr[0].components[compEdit].versions[verEdit].versionId);
						}
					}
				}

				compIdVerionsEditArr = getMasterList(compNameVerArr, "componentId", [compIds], true);
				var compVersionIds = [];

				for(var compVer in compIdVerionsEditArr){
					compIdVerionsEditArr[compVer].versions = getMasterList(compIdVerionsEditArr[compVer].versions, "versionId", compVersions, true);
				}
				self.compNameVersionArr(compIdVerionsEditArr);
			}
			else{
				self.compNameVersionArr(compNameVerArr);
			}





			
			var compNameVersionObj;
			for(compName in self.compNameVersionArr()){
				if(uiConstants.common.DEBUG_MODE)console.log(self.compNameVersionArr()[compName]);
				compNameVersionObj = {
					"item":{
	                    "id": "id"+self.compNameVersionArr()[compName].componentId.toString(),
	                    "label": self.compNameVersionArr()[compName].componentName,
	                    "checked": false
            		},
            		"children":[]
        		}

        		var compVersionObjArr = [];
        		var compVersionObj;
        		if(uiConstants.common.DEBUG_MODE)console.log(self.compNameVersionArr());
        		for(compVersion in self.compNameVersionArr()[compName].versions){
        			compVersionObj = {
						"item":{
		                    "id": self.compNameVersionArr()[compName].versions[compVersion].versionId.toString(),
		                    "label": self.compNameVersionArr()[compName].versions[compVersion].version,
		                    "checked": false
                		}                		
            		}
					compVersionObjArr.push(compVersionObj);
        			 
        		}

        		if(uiConstants.common.DEBUG_MODE)console.log(compVersionObjArr);        		
        		compNameVersionObj["children"]=compVersionObjArr;
        		if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(compNameVersionObj));
        		self.compNameVersionObjArr.push(compNameVersionObj);
			}

			if(loadCheckTree){
				$('#tree-container').highCheckTree({
	                data: self.compNameVersionObjArr()
	            });
			}

			if(self.curOper() == "addcomp"){
				for(selComp in self.compVersionGridData()){
					checkSelectedComps(selComp);
				}
			}
			else if(self.curOper() == "editcomp"){
				checkSelectedComps(rowToEdit);
			}
            //$("#tree-container .checkbox").pointer-events: none;
		}

		function setPageTitle(){
			self.pageSelected("Application Type");
		}

		function setCompNameVersion(selCompTypeId, selObj){
			var compVersions;
			var versionCounter = 0;

			if(uiConstants.common.DEBUG_MODE)console.log(self.compNameVersionObjArr());
			if(uiConstants.common.DEBUG_MODE)console.log(selObj);
			for(compName in self.compNameVersionObjArr()){
				compVersions = self.compNameVersionObjArr()[compName].children;
				versionCounter = 0;
				for(compVersion in compVersions){
					if(selObj.indexOf(compVersions[compVersion].item.id) != -1 && selCompTypeId == $("#compTypeList").val()){
						if(uiConstants.common.DEBUG_MODE)console.log(self.compNameVersionObjArr());
						compVersions[compVersion].item.checked = true;
						versionCounter++;
					}
				}

				if(uiConstants.common.DEBUG_MODE)console.log(versionCounter);
				if(uiConstants.common.DEBUG_MODE)console.log(compVersions.length);

				if(compVersions.length == versionCounter){
					$("#tree-container .checkbox").parent().children('div .checkbox').get(compName).className = "checkbox checked";
				}
				else if(versionCounter != 0){
					$("#tree-container .checkbox").parent().children('div .checkbox').get(compName).className = "checkbox half_checked";
				}
			}
		}

		self.errorMsg.subscribe(function(errorField) {
        	if(self.firstFieldToShowErr() == ""){
	        	//scrollToPos(0, 300);
	        	self.firstFieldToShowErr(errorField);
        		if(self.isModal()){
		        	$('.modal').animate({scrollTop: $(errorField).offset().top-100});
		        }
	        	else{
		        	$('html,body').animate({scrollTop: $(errorField).offset().top-100});
	        	}
	        }
		});


		//Adding/Updating single config
		this.addEditConfig = function(){
			var compAssocitionExists = 0;

			if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows().length);
			self.errorMsg("");
			self.firstFieldToShowErr("");
			removeError();

			self.configName(self.configName().trim());
			$("#txtDescription").val($("#txtDescription").val().trim());

			if(self.configName() == undefined || self.configName() == ""){
				showError("#divAppTypeAddEdit #txtName", uiConstants.applicationType.APPLICATION_TYPE_NAME_REQUIRED);
		    	self.errorMsg("#divAppTypeAddEdit #txtName");
			}
			else if(self.configName().length < 2){
				showError("#divAppTypeAddEdit #txtName", uiConstants.applicationType.APPLICATION_TYPE_NAME_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divAppTypeAddEdit #txtName");
			}
			else if(self.configName().length > 45){
				showError("#divAppTypeAddEdit #txtName", uiConstants.applicationType.APPLICATION_TYPE_NAME_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divAppTypeAddEdit #txtName");
			}
			else if(!nameValidation(self.configName())){
				showError("#divAppTypeAddEdit #txtName", uiConstants.applicationType.APPLICATION_TYPE_NAME_INVALID_ERROR);
		    	self.errorMsg("#divAppTypeAddEdit #txtName");
			}
			if(self.configDescription() == undefined || self.configDescription() == ""){
				showError("#divAppTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_REQUIRED);
		    	self.errorMsg("#divAppTypeAddEdit #txtDescription");
			}
			else if(self.configDescription().length < 25){
				showError("#divAppTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
		    	self.errorMsg("#divAppTypeAddEdit #txtDescription");
			}
			else if(self.configDescription().length > 256){
				showError("#divAppTypeAddEdit #txtDescription", uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
		    	self.errorMsg("#divAppTypeAddEdit #txtDescription");
			}
			if(self.compVersionGridData().length == 0 && self.errorMsg() == ""){
				showMessageBox(uiConstants.common.ADD_COMPONENT_ASSOCIATION, "error");
			}
			else{
				compAssocitionExists = 1;
			}

			if(compAssocitionExists == 1 && self.errorMsg() == ""){
				var componentDetails = [];
				//var compVersionIdsArr = [];
				var compTypeArr = [];
				var compNameArr = [];
				if(uiConstants.common.DEBUG_MODE)console.log(self.compVersionGridData());

				for(compType in self.compVersionGridData()){
					compNameArr = [];
					for(compNameVersionId in self.compVersionGridData()[compType].compNameVersion){
						//compVersionIdsArr = self.compVersionGridData()[compType].compNameVersion[compNameVersionId].compVersionIds;
					
						compNameArr.push({
							"componentId": parseInt(self.compVersionGridData()[compType].compNameVersion[compNameVersionId].compId.substring(2)),
							"versions": self.compVersionGridData()[compType].compNameVersion[compNameVersionId].compVersionIds.map(function(versionId) {return parseInt(versionId);})
						})
					}
					compTypeArr.push({
						"componentTypeId": parseInt(self.compVersionGridData()[compType].compTypeId),
						"components": compNameArr
						});
				}

				if(uiConstants.common.DEBUG_MODE)console.log(compTypeArr);

				var configObj = {
					"index":1,
					"masterId": self.configId(),
					"name": self.configName(),
					"description": $("#txtDescription").val() != ""? self.configDescription().trim():null,
					"status" : self.configStatus()?1:0,
					"componentDetails": compTypeArr
				};

				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(configObj));

				if(self.configId() == 0){
					requestCall(uiConstants.common.SERVER_IP + "/applicationType", "POST", JSON.stringify(configObj), "addSingleConfig", successCallback, errorCallback);
					
				}
				else{
					requestCall(uiConstants.common.SERVER_IP + "/applicationType", "PUT", JSON.stringify(configObj), "editSingleConfig", successCallback, errorCallback);
				}
			}
	}

		function editSingleConfig(configObj){
			if(uiConstants.common.DEBUG_MODE)console.log(configObj[0]);

			self.configId(configObj[0].masterId);
			self.configName(configObj[0].name);
			self.configDescription(configObj[0].description);
			self.configStatus(configObj[0].status);
			$('#configStatus').bootstrapSwitch('state',self.configStatus());

			requestCall(uiConstants.common.SERVER_IP + "/component/"+self.configId()+"?status=2&markInactive=1", "GET", "", "getAppCompTypeVersion", successCallback, errorCallback);

			if(configObj[0].hasOwnProperty("isCustom") && configObj[0].isCustom == 0)
				$('#txtName').prop('readonly', true);
		}

		function viewConfig(configObj){
			self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			editSingleConfig(configObj);
			setConfigUneditable(false);			
		}

		function setConfigUneditable(isInactiveEdit){
			$('#txtName').prop('readonly', true);
			$('#txtDescription').prop('readonly', true);
			$("#compType").hide();
			$("#compVersion").hide();
			$("#compButtons").hide();

			if(!isInactiveEdit){
				//document.getElementById("configStatus").disabled = true;
				$("[name='configStatus']").bootstrapSwitch('disabled',true);
				self.toggleCancelCloseBtn(uiConstants.common.CONST_CLOSE);
			}

			$("#divAppTypeAddEdit .chosen-container b").css("display", "none");
		}

		this.cancelConfig = function(){
			self.selectedConfigRows([]);
			//localStorage.selectedConfigRows = JSON.stringify(self.selectedConfigRows());
			if(self.isModal()){
				$(".modal-header button").click();
			}
			else{
				self.pageSelected("Application Type Configuration");
				self.currentViewIndex(uiConstants.common.LIST_VIEW);
			}
		}

		this.addCompNameVersion = function(){
			self.errorMsg("");
			removeError("#divAppTypeAddEdit #compTypeList_chosen");
			removeError("#divAppTypeAddEdit #compTypeList_chosen span");

			var selCompType = $("#compTypeList option:selected").text();//$('#compTypeList').val();
			
			if(selCompType == uiConstants.common.SELECT_COMPONENT_TYPE){
				//self.errorMsg(uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
		    	showError("#divAppTypeAddEdit #compTypeList_chosen", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
				showError("#divAppTypeAddEdit #compTypeList_chosen span", uiConstants.common.SELECT_COMPONENT_TYPE_MSG);
		    	self.errorMsg("#divAppTypeAddEdit #compTypeList");

		    	showError("#divCompAssociation .checktree-container", uiConstants.common.SELECT_COMPONENT_VERSION);
			   	self.errorMsg("#divCompAssociation .checktree-container");
			}
			else{
				addComponentMapping(selCompType, $("#compTypeList").val(), self.compNameVersionObjArr(), false);
			}
			/*$("#tree-container .checkbox").parent().children('div .checkbox').each(function() {console.log($(this)); $(this)[0].className = "checkbox"});

			for(compName in self.compNameVersionObjArr()){
				var compVersions = self.compNameVersionObjArr()[compName].children;
				for(compVersion in compVersions){
					compVersions[compVersion].item.checked = false;
				}
			}*/
		}

		this.updateCompNameVersion = function(){
			self.addCompNameVersion();
		}

		function addComponentMapping(selCompType, selCompTypeId, compNameVersionObjArr, isEdit){
			//var compObj = {compType: selCompType};
			var compVersionArr = [];
			var compVersionIdArr = [];
			var selectedVersion;

			removeError("#divCompAssociation .checktree-container");

			if(uiConstants.common.DEBUG_MODE)console.log(compNameVersionObjArr);

			//if(self.curOper() == "editcomp"){
				for(compType in self.compVersionGridData()){
					if(self.compVersionGridData()[compType].compType == selCompType){
						self.compVersionGridData().splice(compType,1);
						break;
					}
				}
			//}

			var compNameVerArr = [];

			for(compName in compNameVersionObjArr){
				if(uiConstants.common.DEBUG_MODE)console.log(compNameVersionObjArr[compName].item.label);
				compVersionArr = [];
				compVersionIdArr = [];

				selectedVersion = compNameVersionObjArr[compName].children;

				if(uiConstants.common.DEBUG_MODE)console.log(selectedVersion);
				for(compVersion in selectedVersion){
            		if(uiConstants.common.DEBUG_MODE)console.log(selectedVersion[compVersion].item);

            		if(!isEdit){
            			if(selectedVersion[compVersion].item.checked){
            				compVersionArr.push(selectedVersion[compVersion].item.label);
            				compVersionIdArr.push(selectedVersion[compVersion].item.id.toString());
            			}
            		}
            		else{
            			compVersionArr.push(selectedVersion[compVersion].item.label);
            			compVersionIdArr.push(selectedVersion[compVersion].item.id.toString());
            		}
        		}
				
        		if(compVersionArr.length > 0){
        			compNameVerArr.push({
        				"compName":compNameVersionObjArr[compName].item.label,
        				"compId": compNameVersionObjArr[compName].item.id,
        				"compVersion": compVersionArr,
        				"compVersionIds": compVersionIdArr}
        			);
        		}					
			}

			if(uiConstants.common.DEBUG_MODE)console.log(compNameVerArr);

			if(compNameVerArr.length > 0){
				self.curOper("addcomp");
				/*if(self.curOper() == "editcomp"){
					self.curOper("addcomp");
					self.compVersionGridData.splice(rowToEdit,1,{"compType": selCompType,"compTypeId": selCompTypeId,"compNameVersion":compNameVerArr});
				}
				else{*/
					self.compVersionGridData.push({"compType": selCompType,"compTypeId": selCompTypeId,"compNameVersion":compNameVerArr});
				//}

				/*if(!isEdit){
					self.clearCompNameVersion();
				}*/
			}
			else{
				//self.errorMsg(uiConstants.common.SELECT_COMPONENT_VERSION);

				showError("#divCompAssociation .checktree-container", uiConstants.common.SELECT_COMPONENT_VERSION);
			   	self.errorMsg("#divCompAssociation .checktree-container");
			}

			if(uiConstants.common.DEBUG_MODE)console.log(self.compVersionGridData());
		}

		this.clearCompNameVersion = function(){
			onCompTypeChange(self.componentsArr(), $("#compTypeList").chosen().val(), true);
			self.disableReset(true);
		}

		this.cancelEditCompNameVersion = function(){
			self.curOper("addcomp");
			self.clearCompNameVersion();
		}

		function successCallback(data, reqType) {
			if(reqType === "addSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_APPTYPE,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.applicationType.ERROR_ADD_APPLICATION_TYPE, "error");
					}
				}
				else{
					if(self.isModal()){
						self.appType(self.configName());
					}
					else{
						params.curPage(1);
					}
					self.cancelConfig();
					showMessageBox(uiConstants.applicationType.SUCCESS_ADD_APPLICATION_TYPE);
				}
			}
			else if(reqType === "editSingleConfig"){
				if(data.responseStatus == uiConstants.common.CONST_FAILURE){
					if(data != undefined){
						showMessageBox(handleServiceErrorMsgs(uiConstants.common.CONST_APPTYPE,data.errorCode,data.message), "error");
					}
					else{
						showMessageBox(uiConstants.applicationType.ERROR_UPDATE_APPLICATION_TYPE, "error");
					}
				}
				else{
					showMessageBox(uiConstants.applicationType.SUCCESS_UPDATE_APPLICATION_TYPE);
					self.cancelConfig();
					params.curPage(1);
				}
			}
			else if(reqType === "getCompTypeVersion"){
				debugger;
				if(uiConstants.common.DEBUG_MODE)console.log(data.result);
				
				if(self.currentViewIndex() == uiConstants.common.EDIT_VIEW || self.currentViewIndex() == uiConstants.common.READ_VIEW){
					self.componentsArr(getMasterList(data.result, "componentTypeId", compTypeIdsEditArr, true));
				}
				else{
					self.componentsArr(data.result);
				}

				$("#compTypeList").trigger('chosen:updated');
			}
			else if(reqType === "getAppCompTypeVersion"){
				debugger;
				self.componentsEditArr(data.result);
				compTypeIdsEditArr = [];

				for(compEdit in self.componentsEditArr()){
					compTypeIdsEditArr.push(self.componentsEditArr()[compEdit].componentTypeId);
					onCompTypeChange(self.componentsEditArr(), self.componentsEditArr()[compEdit].componentTypeId, false);
					addComponentMapping(self.componentsEditArr()[compEdit].componentType, self.componentsEditArr()[compEdit].componentTypeId, self.compNameVersionObjArr(), true);

					if(uiConstants.common.DEBUG_MODE)console.log(self.compVersionGridData());
				}

				requestCall(uiConstants.common.SERVER_IP + "/component?status=2&markInactive=1", "GET", "", "getCompTypeVersion", successCallback, errorCallback);
			}
		}

		function errorCallback(reqType) {
			if(reqType === "addSingleConfig"){
				showMessageBox(uiConstants.applicationType.ERROR_ADD_APPLICATION_TYPE, "error");
			}
			else if(reqType === "editSingleConfig"){
				showMessageBox(uiConstants.applicationType.ERROR_UPDATE_APPLICATION_TYPE, "error");
			}
			else if(reqType === "getCompTypeVersion"){
				showMessageBox(uiConstants.common.ERROR_GET_COMP_TYPE_VERSION, "error");
			}
			else if(reqType === "getAppCompTypeVersion"){
				showMessageBox(uiConstants.common.ERROR_GET_APP_COMP_TYPE_VERSION, "error");
			}
		}

		function checkSelectedComps(rowToEdit){
			//	$("#compTypeList").val(self.compVersionGridData()[rowToEdit].compTypeId).trigger('chosen:updated');
		 	if(uiConstants.common.DEBUG_MODE)console.log(self.compVersionGridData()[rowToEdit]);

		 	var selectedVersionIds = [];
		 	var compVersionsToEdit = self.compVersionGridData()[rowToEdit];
		 	for(selVersionId in compVersionsToEdit.compNameVersion){
		 		selectedVersionIds = selectedVersionIds.concat(compVersionsToEdit.compNameVersion[selVersionId].compVersionIds);
		 	}

		 	if(uiConstants.common.DEBUG_MODE)console.log(selectedVersionIds);
		 	setCompNameVersion(compVersionsToEdit.compTypeId, selectedVersionIds);
		}
	}

	ConfigAddEdit.prototype.dispose = function() { };
	return { viewModel: ConfigAddEdit, template: templateMarkup };
});