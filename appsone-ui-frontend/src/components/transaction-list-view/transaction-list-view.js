define(['jquery','knockout','jquery-chosen','bootstrap-tokenfield','typeahead', 'text!./transaction-list-view.html','hasher','validator','ui-constants','ui-common','moment','bootstrap-datepicker','tablesorter','checklistbox','floatThead','select2'], function($,ko,jc,bttokenfield,typeahead,templateMarkup,hasher,validator,uiConstants,uicommon,moment,btdatetimepicker,tablesorter,checklistbox,floatThead,select2) {

function Transactionlistview(params){
	var self = this;
	var configTableHeaders = ["Name","Transaction Type","Created Time","Modified Time","Modified By","Tags","Status"];
	var listData = {};
	var filterForFirstPage = false;
	var fConfigName=null;
	var fConfigActiveInactive="1";
	var fTags=null;
	var fTxnTypeId=0;
	var fCreatedTime="";
	var fUpdatedTime="";
	var fUpdatedBy="";
	var colSortOrder = 0;
	var colToSort = "txnName";
	var filterTxtAfterDelete;

	this.gridHeader = ko.observableArray();
	this.filterGridHeader = ko.observableArray(["Select","Name","Transaction Type","Created Time","Modified Time","Modified By","Tags","Status"]);
	this.gridData = ko.observableArray();
	this.configTableHeaderObjKeys = ko.observableArray(["txnName","txnType","createdTime","updatedTime","updatedBy","tags","status"]);
	this.noSortColKeys = ko.observableArray(["tags"]);
	this.currentPage = ko.observable(0);
	this.numOfPagesOption = ko.observableArray([10,20,30,40,50]);
	this.totalRecordsPerPage = ko.observable(this.numOfPagesOption()[0]);
	this.totalRecords = ko.observable(this.numOfPagesOption()[0]);
	this.enableAdd = ko.observable(true);
	this.enableEdit = ko.observable(false);
	this.enableClone = ko.observable(false);
	this.currentViewIndex = ko.observable(uiConstants.common.LIST_VIEW);
	this.selectedConfigRows = ko.observableArray();
	this.gridHeader(configTableHeaders);
	this.errorMsg = ko.observable("");
	this.isFilterOrList = ko.observable("list");
	this.recordsCountLabel = ko.observable("");
	this.pageSelected = ko.observable("Transaction Configuration");

	this.transactionTypeArr = ko.observableArray([{"transactionTypeName": "Select", "transactionTypeId": 0}]);
	this.applicationsArr = params && params.applicationsArr || ko.observableArray();
	this.transactionMethodArr = ko.observableArray();
	this.transactionResTypeArr = ko.observableArray();
	this.selectedApplicationId = ko.observable();
	this.selectedApplicationName = ko.observable();
	this.enableFilter = ko.observable(true);
	this.showListAvailable = ko.observable("");
	this.preSelectedApplication = params && params.applicationId || ko.observable(0);
	this.mode = params && params.mode || '';
	this.txnApplicationId = params && params.txnApplicationId || ko.observable();
	this.modifiedCols = ko.observableArray([true,true,true]);
	this.selFilterCategory = ko.observable();
	this.filtersAdded = ko.observable("");
	this.filterValueArr = ko.observableArray([]);
	//this.auditMasterData = ko.observableArray();//
	
	if(self.mode == "wizard"){
		params.txnListViewModel(self);
	}

	this.renderHandler=function(){
		/*$(".wrapper").scroll(function(){
			var translate = "translate(0,"+this.scrollTop+"px)";
			this.querySelector("thead").style.transform = translate;
			this.querySelector("#filterRow").style.transform = translate;
        });*/

		$(window).resize(function(){
		    self.refreshPageLayout();
		});

		/*Jquery chosen start*/
		jQuery(".chosen").chosen({
			search_contains: true	
		});

		$(".addOptionsContainer").offset({ top: $("#divAddOptionsList").position().top + $("#divAddOptionsList").outerHeight() });

		$('.columnsList').checklistbox({
		    data: [
		    	{"name": "Created Time", "id": 1},
		    	{"name": "Modified Time", "id": 2},
		    	{"name": "Modified By", "id": 3}
		    ]
		});
		$('.columnsList .checkList').prop("checked", true);

		$("div").on("click", "#selAllCols", function(e){
			$(".columnsList .checkList").prop("checked", $("#selAllCols").prop("checked"));
		});

		$("div").on("change", ".columnsList .checkList", function(e){
			if(self.currentViewIndex() != uiConstants.common.READ_VIEW){
				$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
			}
        });

		//$("#fActiveInactive_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
		$("#fActiveInactive_chosen").trigger('chosen:updated');

		if(self.mode == "wizard"){
			$("#applicationList").prop('disabled', true).trigger("chosen:updated");
		}

		localStorage.currentViewIndex = uiConstants.common.LIST_VIEW;
		$("#fTxnTypeList_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});

		/* $("#selItemsList").on('change', function () {
			self.curPage(1);
		});*/

		$("#applicationList").on('change', function () {
			self.selectedApplicationId = $("#applicationList").val();
			self.selectedApplicationName = $("#applicationList option:selected").text();
			
			$("#applicationList").chosen("chosen:updated");
			if($("#applicationList").val() != undefined || $("#applicationList").val() != null){
				$("#selItemsList").trigger('change');
				self.getListData();
			}
			else{
				showMessageBox(uiConstants.transactionConfig.MSG_SELECT_APPLICATION, "error");
			}
		});

		var selectedApplicationId = $('#applicationList').val() == null ? '' : $('#applicationList').val();

		requestCall(uiConstants.common.SERVER_IP +"/applicationsName?status=2&markInactive=1", "GET", "", "getApplications", successCallback, errorCallback);
		requestCall(uiConstants.common.SERVER_IP +"/transactionTypes"+selectedApplicationId, "GET", "","getTransactionType", successCallback, errorCallback);
		requestCall(uiConstants.common.SERVER_IP +"/transactionMethods", "GET", "","getTransactionMethods", successCallback, errorCallback);
		requestCall(uiConstants.common.SERVER_IP +"/transactionResponseTypes", "GET", "","getTransactionResponseTypes", successCallback, errorCallback);
		//
		//requestCall("http://www.mocky.io/v2/58296c52120000080b8a2699?callback=?", "GET", "","getTransactionType", successCallback, errorCallback);
  					
		$('#fConfigName').keypress(function(event) {
			if (event.which == 13) {
				self.getFilterListData(true);
			}
		});

		

			/*$('#createdTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#createdTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#createdTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#createdTime input').val("");

			$('#updatedTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#updatedTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#updatedTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));

				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}
			});
			$('#updatedTime input').val("");*/
			$("#fActiveInactive").val("1").trigger('chosen:updated');

			self.curPage(1);
	}

	self.currentViewIndex.subscribe(function(curViewIndex) {
			window.currentViewIndex(curViewIndex);
        	if(curViewIndex == uiConstants.common.LIST_VIEW || curViewIndex == uiConstants.common.READ_VIEW){
				window.currentPageMode = "";
        	}
        	else{
				$("#messageDialogBox").on('hidden.bs.modal', function () {
					var $tab = $('#listgrid');
					$tab.floatThead('reflow');

					$("#messageDialogBox").off('hidden.bs.modal');
				});

				window.currentPageMode = "AddUpdate";
	        }
		});

	this.getTagNames = function(tagsData){
		var tagNames = "";
		for(tag in tagsData){
			tagNames += "," + tagsData[tag].tagName;
		}

		return tagNames.substring(1);
	}

	this.onHeaderClick = function(columnNum, columnName){
		if($(".listViewCol:eq("+columnNum+")").hasClass("header") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortUp") || $(".listViewCol:eq("+columnNum+")").hasClass("headerSortDown")){
			$(".listViewCol").removeClass("headerSortUp").removeClass("headerSortDown").addClass("header");

			colSortOrder = colSortOrder ? 0 : 1;
			colToSort = columnName;
			//$("#listgrid").trigger("sorton", [ [[columnNum,colSortOrder]] ]);
			$(".listViewCol:eq("+columnNum+")").addClass(colSortOrder ? "headerSortUp" : "headerSortDown");

			self.getListData();
		}
	}

		this.toggleColumnsList = function(){
			$(".columnsListContainer").toggle();
		}

		this.closeColumnsListBox = function(){
			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				$(this).prop("checked", self.modifiedCols()[indx]);
			});
			$(".columnsListContainer").hide();
			$("#selAllCols").prop("checked", $(".columnsList .checkList:checked").length == 3);
		}

		this.modifyColumnsListBox = function(){
			debugger;
			self.modifiedCols([]);

			$('.columnsListContainer .columnsList').find('input:checkbox').each(function(indx) {
				var checkBoxObj = $(this);
                
                $('table .a1-list-grid-header th:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')').css({"width": checkBoxObj.prop("checked") ? "auto" : "0",
	        			"white-space": checkBoxObj.prop("checked") ? "normal" : "nowrap"});

	        	$('table td:eq('+$('.a1-list-grid-header tr th').filter(
		            function(){
		                return $(this).text() == checkBoxObj.parent().text();
		            }).index()+
	        	')  > *').css("width", checkBoxObj.prop("checked") ? "auto" : "0");

	        	self.modifiedCols.push(checkBoxObj.prop("checked"));
            });

			$(".columnsListContainer").hide();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

	this.convertToLocalTime = function(getDateTime){
		return window.gmtToLocalDateTime(getDateTime);
	}
	
	/*Subscribed to take care of refreshing the application list with latest populated data. This should have newly added application in it*/
	this.applicationsArr.subscribe(function(){
		jQuery(".chosen").chosen({
			search_contains: true	
		});
		$("#applicationList").trigger('chosen:updated');
	});

	/*Handler to run after select options are completely rendered
	* 
	*/
	this.chooseItemFromList = function(option, item){
		if(item && (item.applicationId === self.preSelectedApplication())){
			ko.applyBindingsToNode(option.parentElement, {value: item.applicationId}, item); // Assign value to select element
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			$("#applicationList").trigger('chosen:updated');
		}
		
	};

	this.msgShowListData=function(){
		if(self.isFilterOrList() == "filter" && self.totalRecords() == 0){
				self.currentPage(0);
			self.showListAvailable(uiConstants.common.NO_RESULTS_FOUND);
		}
		else{
			self.showListAvailable(uiConstants.transactionConfig.TXN_LISTS_NOT_CONFIGURED);
		}
	}

	this.toggleAddOptions = function(){
		$(".addOptionsContainer").toggle();
		document.addEventListener('click', window.outsideClickListener);
	}

	this.totalPages = ko.computed(function() {
		return Math.ceil(self.totalRecords()/self.totalRecordsPerPage());
	}, this);

	this.recordsCountLabel = ko.computed(function() {
		var recordsStartCount = 0;
		var recordsEndCount = 0;

		if(self.currentPage() != 0){
			recordsStartCount = ((self.currentPage() - 1) * self.totalRecordsPerPage()) + 1;
			recordsEndCount = recordsStartCount + (self.gridData().length - 1);
		}
		return recordsStartCount + "-" + recordsEndCount + " of " + self.totalRecords();
	}, this);

	this.downloadJsonTxnDefs= function(){
		var reqUrl="/transactions/0?limit=0&offset=0&txnName=null&status=2&tags=null&txnTypeId=0";
		requestCall(uiConstants.common.SERVER_IP + reqUrl, "GET", "", "getJsonConfig", successCallback, errorCallback);
	}

	this.enableDisableAdd = function(length){
		self.enableAdd(length>0?false:true)
	}

	/*this.disableFilter = ko.computed(function(){
		if(self.gridData().length > 0)
			return false;
		else
			return true;
	});
*/
	this.enableDisableUpdate = function(length){
		if(length == 1)
			self.enableEdit(true);
		else
			self.enableEdit(false);
	};

	this.enableDisableClone = function(length){
		if(length == 1)
			self.enableClone(true);
		else
			self.enableClone(false);
	};

	this.getListOrFilterData = function(){
		if(self.isFilterOrList() == "list")
			self.getListData();
		else{
			self.getFilterListData(false);
		}
	}

	this.prevPage = function(){
		if(self.currentPage()>1)
			self.currentPage(self.currentPage()-1);

		if(self.isFilterOrList() == "list")
			self.getListData();
		else{
			self.getFilterListData(false);
		}
	}

	this.nextPage = function(){
		if(self.currentPage()<self.totalPages())
			self.currentPage(self.currentPage()+1);

		if(self.isFilterOrList() == "list")
			self.getListData();
		else{
			self.getFilterListData(false);
		}
	}

	this.curPage = function(curPage){
		resetButtonStates();
		self.currentPage(curPage);

		if(self.isFilterOrList() == "list")
			self.getListData();
		else{
			self.getFilterListData(false);
		}
	}

	this.showFilterBox = function(){
		if(!self.filterValueArr().length){
            for(var headerTxt in self.gridHeader()){
            	self.filterValueArr.splice(headerTxt, 0, "");
            }
		}
		$("#filterCriteria").trigger("chosen:updated");
		$("#filterCriteria_chosen").addClass("filterFieldWidth");
		$("#btnFilter").removeClass("filterapplied").addClass("filterapplied");
		$("#filterBox").css("display", "block");

		self.onFilterCatChange();

		self.refreshPageLayout();

		var $tab = $('#listgrid');
		$tab.floatThead('reflow');
	}

	this.onFilterCatChange = function(){
		if(self.selFilterCategory() == "Created Time" || self.selFilterCategory() == "Modified Time"){
			$('#filterCreateModTime').datetimepicker({
				format: "YYYY-MM-DD HH:00",          
				stepping: 1,
				useCurrent: true, 
				//defaultDate: null,
				showTodayButton: false,          
				collapse: true,
				sideBySide: false
			})
			.on('dp.show', function(e){
				if($('#filterCreateModTime input').val() == ""){
					$(this).data("DateTimePicker").date(moment());
				}
			})
			.on('dp.change', function(e){
				if(moment().diff($('#filterCreateModTime input').val(), 'days') == 0){
					$(this).data("DateTimePicker").maxDate(moment());
					var maxHour = parseInt(moment().format("HH"));
					//var disabledHoursArr = Array.apply(null, Array(24-maxHour));
					//$(this).data("DateTimePicker").disabledHours(disabledHoursArr.map(function (x, i) { return i+(maxHour+1) }));
				}
				else{
					$(this).data("DateTimePicker").maxDate(moment().format('YYYY-MM-DD 23:00'));
					//$(this).data("DateTimePicker").disabledHours([]);
				}

				self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())] = $('#filterCreateModTime input').val();

			})
			.on('dp.hide', function(e){
				self.onFilterAdd();
			});
			$('#filterCreateModTime input').val("");
		}
	}

	this.onFilterAdd = function(){
		if(self.filtersAdded() == ""){
			self.filtersAdded($("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').tokenfield({
				delimiter: ['|']
			});
		}


		var filtersArr = self.filtersAdded().split("|");
		var filterCategoryArr = [];
		var filterCategoryFound = 0;
		
		self.filtersAdded("");
		for(var filters in filtersArr){
			filterCategoryArr = filtersArr[filters].trim().split(":");
			
			if(filterCategoryArr[0].trim() == self.selFilterCategory()){
				filterCategoryArr[1] = self.getFiltersToAdd(self.selFilterCategory());
				filterCategoryFound = 1;
			}

			self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + filterCategoryArr[0].trim()+":"+filterCategoryArr[1]);

			if(filters == "0"){
				$('#filters-tokenfield-typeahead').tokenfield({
					delimiter: ['|']
				});
			}
		}

		if(filterCategoryFound == 0){
			self.filtersAdded(self.filtersAdded() + (self.filtersAdded() == "" ? "" : "|") + $("#filterCriteria option:selected").text() + ":" + self.getFiltersToAdd(self.selFilterCategory()));
		}
		filterCategoryFound = 0;

		var filtersTxt;
			var deleteFilterTxt;
			var deleteTokenIndx;

			//self.filtersAdded(self.filtersAdded() + "|" + $("#filterCriteria option:selected").text() + ":" + self.filterValueArr()[self.gridHeader.indexOf(self.selFilterCategory())]);
			$('#filters-tokenfield-typeahead').on('tokenfield:edittoken', function (e) {
				e.preventDefault();
			}).on('tokenfield:removetoken', function (e) {
				filtersTxt = $("#filters-tokenfield-typeahead").val();
				deleteTokenTxt = e.attrs.label.trim();
				deleteTokenIndx = self.filtersAdded().indexOf(deleteTokenTxt);
			}).on('tokenfield:removedtoken', function (e) {
				if(filterTxtAfterDelete == undefined){
					filterTxtAfterDelete = $("#filters-tokenfield-typeahead").val();
				}

				if(deleteTokenIndx>0){
					deleteTokenTxt = "|"+deleteTokenTxt;
				}

				self.filtersAdded(filtersTxt);

				$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
				$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
				$('.token, .token a').removeClass("div-token").addClass("div-token");
				$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");

				showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
					if(confirm){
						self.filtersAdded(filterTxtAfterDelete);

						$('#filters-tokenfield-typeahead').tokenfield('setTokens', self.filtersAdded());
						self.formatFilters();
						
						fConfigName=null;
						fConfigActiveInactive="1";
						fTxnTypeId=0;
						fCategory="2";
						fTags=null;
						fCreatedTime="";
						fUpdatedTime="";
						fUpdatedBy="";

						if(self.filtersAdded()){
							self.getFilterListData(true);
						}
						else{
							self.resetFilterConfirmed();
						}
					}

					filterTxtAfterDelete = undefined;
				});
			})
			.tokenfield('setTokens', self.filtersAdded());

			self.formatFilters();
			self.refreshPageLayout();

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}

		this.formatFilters = function(){
			$('#filters-tokenfield-typeahead').parent().removeClass("tokenfield-container").addClass("tokenfield-container");
			$('.token, .token a').removeClass("div-token").addClass("div-token");
			$("#filters-tokenfield-typeahead-tokenfield").css("display", "none");
		}

	this.getFiltersToAdd = function(category){
		if(category == "Transaction Type"){
			return $("#fTxnTypeList_chosen option:selected").text();
		}
		else if(category == "Status"){
			//return self.filterValueArr()[self.gridHeader.indexOf(category)] == 1 ? 'Active' : (self.filterValueArr()[self.gridHeader.indexOf(category)] == 0 ? 'Inactive' : 'All');
			return $("#fActiveInactive option:selected").text();
		}
		else{
			return self.filterValueArr()[self.gridHeader.indexOf(category)];
		}
	}

	this.resetFilter = function(){
		showMessageBox(uiConstants.common.CONFIRM_CLEAR_FILTER, "question", "confirm", function confirmCallback(confirm){
			if(confirm){
				self.resetFilterConfirmed();
			}
		});
	}

	this.resetFilterConfirmed = function(){
		self.filtersAdded("");
		for(var headerTxt in self.gridHeader()){
        	self.filterValueArr.splice(headerTxt, 0, "");
        }

		$("#fActiveInactive").val("1").trigger('chosen:updated');
		$("#fTxnTypeList_chosen").val("0").trigger('chosen:updated');
		$('#filterCreateModTime').val("");
		self.errorMsg("");
		self.currentPage(1);
		self.isFilterOrList("list");

		fConfigName=null;
		fConfigActiveInactive="1";
		fTxnTypeId=0;
		fCategory="2";
		fTags=null;
		fCreatedTime="";
		fUpdatedTime="";
		fUpdatedBy="";

		$("#filterOptionsBox").css("display", "");
		$("#btnApplyFilter").css("display", "");
		$("#filterOptionsDispBtn").css("display", "none");
		$("#filterCriteria option").filter(function () { return $(this).html() == "Select"; }).prop('selected', true).trigger('chosen:updated');
		self.selFilterCategory("Select");

		if($("#applicationList").val() !=""){
			requestCall(uiConstants.common.SERVER_IP + "/transactions/"+$("#applicationList").val()+"?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset=1&txnName="+fConfigName+"&status="+fConfigActiveInactive+"&tags="+fTags+"&txnTypeId="+fTxnTypeId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy, "GET", "", "getListData", successCallback, errorCallback);
		}
		else{
			showMessageBox(uiConstants.transactionConfig.MSG_SELECT_APPLICATION, "error");
		}
	}

	this.getFilterListData = function(filterApplied){
		self.isFilterOrList("filter");
		filterForFirstPage = filterApplied;
		setFiltersToVariables();
		var resValue = true;
		var pageOffset = self.currentPage();

		this.errorMsg("");

		/*if($("#fConfigName").val() != "" && $("#fConfigName").val().length < 2){
			self.errorMsg(uiConstants.transactionConfig.TXN_NAME_MIN_LENGTH_ERROR);
			resValue = false;
		}
		else if($("#fConfigName").val() != "" && $("#fConfigName").val().length > 45){
			self.errorMsg(uiConstants.transactionConfig.TXN_NAME_MAX_LENGTH_ERROR);
			resValue = false;
		}*/
		/*else if(fConfigName != "" && fConfigName != null){
			resValue=nameValidation(fConfigName);
			if(resValue == 0){
				self.errorMsg(uiConstants.componentConfig.COMPONENT_NAME_INVALID_ERROR);
				
				resValue = false;
			}
		}*/

		resetPagination();

		if(resValue){
			if(uiConstants.common.DEBUG_MODE)console.log("filterApplied:"+filterApplied);
			if(filterApplied){
				pageOffset = 1;
				$("#btnApplyFilter").css("display", "none");
				$("#filterOptionsBox").css("display", "none");
				$("#filterOptionsDispBtn").css("display", "");
			}
			var selectedAppId = ($('#applicationList').val() == "") ? 0:parseInt($('#applicationList').val());
			requestCall(uiConstants.common.SERVER_IP + "/transactions/"+selectedAppId+"?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ pageOffset +"&txnName=" +fConfigName+"&status="+fConfigActiveInactive+"&tags="+fTags+"&txnTypeId="+fTxnTypeId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);
		}
	}

	this.onFilterOptionsDispClick = function(){
		$("#filterOptionsBox").css("display", "");
		$("#btnApplyFilter").css("display", "");
		$("#filterOptionsDispBtn").css("display", "none");
			
		self.refreshPageLayout();
	}

	this.refreshPageLayout = function(){
			var wrapperHeight = ($(window).outerHeight(true) - $(".wrapper").offset().top - $(".config-pagination-footer").outerHeight(true));
			$(".wrapper").height("");

			if($(".wrapper").prop("scrollHeight") > wrapperHeight){
				$(".wrapper").height(wrapperHeight + "px");
			}
		}

	this.getListData = function(){
		self.isFilterOrList("list");
		resetPagination();
		//self.currentPage(1);
		var selectedAppId = ($('#applicationList').val() == "") ? 0:parseInt($('#applicationList').val());		
		requestCall(uiConstants.common.SERVER_IP + "/transactions/"+selectedAppId+"?sortBy="+colToSort+"&orderBy="+(colSortOrder == 1 ? 'desc' : 'asc')+"&limit=" + self.totalRecordsPerPage()+"&offset="+ self.currentPage() +"&txnName=" +fConfigName+"&status="+fConfigActiveInactive+"&tags="+fTags+"&txnTypeId="+fTxnTypeId+"&createdTime="+fCreatedTime+"&updatedTime="+fUpdatedTime+"&updatedBy="+fUpdatedBy , "GET", "", "getListData", successCallback, errorCallback);		
	}

	this.switchView = function (viewIndex){
		localStorage.currentViewIndex = viewIndex;
		if($('#applicationList option:selected').text() == "Select Application"){
			showMessageBox("Please select Application", "error");
		}
		else{
			debugger;
			if((viewIndex == uiConstants.common.ADD_SINGLE_VIEW || viewIndex == uiConstants.common.CLONE_VIEW) && $('#applicationList option:selected').text().endsWith("(Inactive)")){
				showMessageBox("Cannot " + (viewIndex == uiConstants.common.ADD_SINGLE_VIEW ? " add " : " clone ") + "transaction for inactive application", "error");
			}
			else{
				self.currentViewIndex(viewIndex);
			}
		}

		if(self.currentViewIndex() == uiConstants.common.LIST_VIEW){
			self.pageSelected("Transaction Configuration");
			uicommon.postbox.publish(uiConstants.common.LIST_VIEW,"changeViewToList");
		}

		else if(self.currentViewIndex() == uiConstants.common.ADD_SINGLE_VIEW){
			uicommon.postbox.publish("checking the current View","checkCurrentView");
			uicommon.postbox.publish("View is now add","ViewIsChangedToAddEdit");
			self.pageSelected("Add Transaction");
		}
	}

	this.editConfig = function(){
		getSelectedConfigRows(null);
		if(uiConstants.common.DEBUG_MODE)console.log(self.selectedConfigRows());
		self.pageSelected("Edit Transaction");
		self.switchView(uiConstants.common.EDIT_VIEW);
		uicommon.postbox.publish("checking the current View","checkCurrentView");
		 uicommon.postbox.publish("View is now edit","ViewIsChangedToAddEdit");
	}

	this.cloneConfig = function(){
		getSelectedConfigRows(null);
		if(self.selectedConfigRows()[0].status == 0){
			showMessageBox(uiConstants.common.DENY_INACTIVE_CONFIG_CLONE.replace("%s", "Transaction"), "error");
		}
		else{
			self.pageSelected("Clone Transaction");
			self.switchView(uiConstants.common.CLONE_VIEW);
			uicommon.postbox.publish("checking the current View","checkCurrentView");
			uicommon.postbox.publish("View is now clone","ViewIsChangedToAddEdit");
		}
	}

	this.addMultipleConfig = function(){
		getSelectedConfigRows(null);
		self.pageSelected("Add Transactions");
		self.switchView(uiConstants.common.ADD_MULTIPLE_VIEW);
	}

	this.viewConfig = function(viewObj){
		getSelectedConfigRows(viewObj);
		self.pageSelected("Transaction");
		self.switchView(uiConstants.common.READ_VIEW);
		uicommon.postbox.publish("checking the current View","checkCurrentView");
		uicommon.postbox.publish("View is now readonly","ViewIsChangedToAddEdit");
	}

	this.addMultipleConfig = function(){
		//getSelectedConfigRows(null);
		self.pageSelected("Add Multiple Transactions for " + self.selectedApplicationName);
		self.switchView(uiConstants.common.ADD_MULTIPLE_VIEW);
	}

	function getSelectedConfigRows(viewObj){
		self.selectedConfigRows([]);

		if(viewObj != null){
			if(uiConstants.common.DEBUG_MODE)console.log(viewObj);
			self.selectedConfigRows.push(viewObj);
		}
		else{
			for(objData in self.gridData()){
				if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(self.gridData()[objData]));

				if(self.gridData()[objData].isSelected){
					self.selectedConfigRows.push(self.gridData()[objData]);
					break; //exiting loop since multiple entry is not allowed to edit
				}
			}			
		}

		localStorage.selectedConfigRows = JSON.stringify(self.selectedConfigRows());
	}

	$('#txnListConfigDetailsPage table').on('click', '.chkboxCol', function(e){
		var rowIndex = $(this).parent().parent().index();
		var chkState = $(this).prop("checked");
		$(".chkboxCol").prop("checked",false);
		$(this).prop("checked",chkState);

		for(objData in self.gridData()){
			self.gridData()[objData].isSelected=false;
		}

		self.gridData()[rowIndex].isSelected = chkState;
		self.handleChkClick();
	});

    $('#txnListConfigDetailsPage #listgrid tbody').on('dblclick', 'tr', function(e){
    	if(e.target.parentNode.rowIndex != undefined)
    		self.viewConfig(self.gridData()[e.target.parentNode.rowIndex - 1]);
	});

	self.onNameClick = function(){
		self.viewConfig($(this)[0]);
	}

	self.handleChkClick = function(){
		var length = $('.chkboxCol:checked').length;
		self.enableDisableAdd(length);
		self.enableDisableUpdate(length);
		self.enableDisableClone(length);
	}

    function resetButtonStates(){
		self.enableDisableAdd(0);
		self.enableDisableUpdate(0);
		self.enableDisableClone(0);
	}

	function setFiltersToVariables(){
		var filtersArr = self.filtersAdded().split("|");
		var filterCategoryArr = [];

		for(var filters in filtersArr){
			filterCategoryArr = filtersArr[filters].trim().split(":");

			if(filterCategoryArr[0] == "Name"){
				fConfigName = filterCategoryArr[1];
			}
			else if(filterCategoryArr[0] == "Transaction Type"){
				fTxnTypeId = $.grep(self.transactionTypeArr(), function(e){ return e.transactionTypeName == filterCategoryArr[1].trim(); })[0]["transactionTypeId"];
			}
			else if(filterCategoryArr[0] == "Created Time"){
				fCreatedTime=localToGmtDateTime(filterCategoryArr[1].trim());
			}
			else if(filterCategoryArr[0] == "Modified Time"){
				fUpdatedTime=localToGmtDateTime(filterCategoryArr[1].trim()); 
			}
			else if(filterCategoryArr[0] == "Modified By"){
				fUpdatedBy = filterCategoryArr[1];
			}
			else if(filterCategoryArr[0] == "Tags"){
				fTags = filterCategoryArr[1];
			}
			else if(filterCategoryArr[0] == "Status"){
				fConfigActiveInactive = filterCategoryArr[1] == 'Active' ? 1 : (filterCategoryArr[1] == 'Inactive' ? 0 : 2);
			}
		}
	}

	function resetPagination(){
		if(self.totalPages() != 0){
			if(typeof self.currentPage() == "string"){
				self.currentPage(parseInt(self.currentPage()));
			}

			if(self.currentPage() == "" || isNaN(self.currentPage()))
				self.currentPage(1);
			else if(self.currentPage()>self.totalPages())
				self.currentPage(self.totalPages());
			else if(self.currentPage()<1)
				self.currentPage(1);
		}
	}

	function successCallback(data, reqType) {
		if(reqType === "getListData"){
			resetButtonStates();

			if(data.responseStatus == "success"){
				listData = data;
				if(uiConstants.common.DEBUG_MODE)console.log("==========================Transaction List data==============================");
				if(uiConstants.common.DEBUG_MODE)console.log(data);
				self.totalRecords(listData.totalRecords);
				
				//self.gridData(listData.result);

				$("#txnListConfigDetailsPage #listgrid #gridDataBody").empty();
		 		$("#txnListConfigDetailsPage #listgrid").trigger("update");
				self.gridData(listData.result);

				var initialSortColumn = 1;
				var sortOrder = 0; //0=asc; 1=desc
				if(!$("#txnListConfigDetailsPage #listgrid").hasClass("tablesorter")){
					if(!self.gridData().length){
						self.enableFilter(false);
					}
					else{

						if (!$("#pageNum").hasClass("select2-hidden-accessible")){
							debugger;
							$("#pageNum").select2();

							$("#pageNum").select2("open");
							$("#select2-pageNum-container").parent().children('.select2-selection__arrow').children("b").hide();
							$("#select2-pageNum-container").parent().children('.select2-selection__arrow').append('<i class="fa fa-angle-down" style="font-weight: bold;"></i>');
							$("#select2-pageNum-container").parent().css({
								"border": "none",
								"outline": "none"
							});

							$("#pageNum").parent().children("span").css("width", "36px");
							//$("#pageNum").parent().children("span select2-selection").css("width", "30px");
							$("#select2-pageNum-container").parent().children(".select2-selection__arrow").css("left", $("#pageNum").parent().children("span").outerWidth() - 12 + "px");
							$("#select2-pageNum-container").css({
									"font-weight": "bold",
									"color": "#218DC0",
									"padding-left": "4px"
								});

							$("#select2-pageNum-results").parent().parent().removeClass("pageNumDropDown").addClass("pageNumDropDown");
							$(".pageNumDropDown .select2-search").css("display", "none");
							$("#select2-pageNum-results").css("overflow-x", "hidden");
							$("#pageNum").select2("close");
						}
					}

					$("#txnListConfigDetailsPage #listgrid").addClass("tablesorter")
					/*$("#txnListConfigDetailsPage #listgrid").tablesorter({
						//ignoreCase : false,
						cancelSelection: false,
						headers: { 0: { sorter: false}, 6: { sorter: false} },

						widgetOptions: {
						      sortTbody_primaryRow : '.main',
					      sortTbody_sortRows   : false,
					      sortTbody_noSort     : 'tablesorter-no-sort-tbody'
					    }
						//sortList: [[initialSortColumn, sortOrder]]
					});

					$("#txnListConfigDetailsPage #listgrid").trigger("sorton", [ [[initialSortColumn,colSortOrder]] ]);*/

			        var $tab = $('#listgrid');
					$tab.floatThead({
						scrollContainer: function($table){
							return $table.closest('.wrapper');
						}
					});

					$('#unselectAll').on('click', function(e){
						window.unselectAllCheck(self.handleChkClick, '.chkboxCol');
					});
				}
				/*else{
					$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
			 		$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
					
				}*/

				/*$("#listgrid th").removeClass('headerSortDown').removeClass('headerSortUp');
			 	$("#listgrid").find("th:eq("+initialSortColumn+")").addClass(sortOrder ? 'headerSortUp' : 'headerSortDown');
*/

				$("#txnListConfigDetailsPage #listgrid").trigger("update");
				self.refreshPageLayout();

				if((self.currentPage() == 0 || filterForFirstPage) && self.gridData().length>0){
					self.currentPage(1);
					filterForFirstPage = false;
				}
				self.msgShowListData();
			}else{
				showMessageBox(data.message, "error");
				self.showListAvailable("");				
			}

			var $tab = $('#listgrid');
			$tab.floatThead('reflow');
		}
		else if(reqType === "getTransactionType"){
			if(uiConstants.common.DEBUG_MODE)console.log("==========================Transaction : transaction type list==============================");
			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
			var res = data.result;
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined){
					showMessageBox(res[0].message, "error");
				}
				else{
					showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_TYPES, "error");
				}
			}
			else{
				data.result.splice(0, 0, {"transactionTypeName": "Select", "transactionTypeId": 0});
				self.transactionTypeArr(data.result);
				$("#fTxnTypeList_chosen").trigger('chosen:updated');
			}
		}
		else if(reqType === "getApplications"){
			if(uiConstants.common.DEBUG_MODE)console.log("==========================Transaction : application list==============================");
			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined){
					showMessageBox(res[0].message, "error");
				}
				else{
					showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
				}
			}
			else{
				self.applicationsArr(data.result);

				$("#applicationList").trigger('chosen:updated');
				if(self.txnApplicationId()){
					$("#applicationList").val(self.txnApplicationId()).trigger('chosen:updated');
					
					if(window.globalSearchTxt){
						fConfigName = window.globalSearchTxt;
					}
					$('#fConfigName').val(fConfigName);
					//window.globalSearchTxt = "";
					self.getListData();
				}
			}
		}
		else if(reqType == "getTransactionMethods"){
			if(uiConstants.common.DEBUG_MODE)console.log("==========================Transaction : transaction method list==============================");
			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined){
					showMessageBox(res[0].message, "error");
				}
				else{
					showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_METHODS, "error");
				}
			}
			else{
				self.transactionMethodArr(data.result);
			}
		}
		else if(reqType == "getTransactionResponseTypes"){
			if(uiConstants.common.DEBUG_MODE)console.log("==========================Transaction : transaction responsetype list==============================");
			if(uiConstants.common.DEBUG_MODE)console.log(data.result);
			if(data.responseStatus == "failure"){
				if(res != undefined && res[0] != undefined){
					showMessageBox(res[0].message, "error");
				}
				else{
					showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_RESPONSE_TYPES, "error");
				}
			}
			else{
				self.transactionResTypeArr(data.result);
			}
		}
		else if(reqType === "getJsonConfig"){
			downloadFile("transactions.json", JSON.stringify(data.result, null, "\t"));
		}
	}

	function errorCallback(reqType) {
			if(reqType === "getListData"){
				showMessageBox(uiConstants.common.ERROR_GET_TRANSACTIONS, "error");
			}
			else if(reqType === "getApplications"){
				showMessageBox(uiConstants.applicationConfig.ERROR_GET_APPLICATIONS, "error");
			}
			else if(reqType === "getTransactionType"){
				showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_TYPES, "error");
			}
			else if(reqType === "getTransactionMethods"){
				showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_METHODS, "error");
			}
			else if(reqType === "getTransactionResponseTypes"){
				showMessageBox(uiConstants.transactionConfig.ERROR_GET_TRANSACTION_RESPONSE_TYPES, "error");
			}
			else if(reqType === "getJsonConfig"){
				showMessageBox(uiConstants.transactionConfig.ERROR_GET_TXN_JSON_CONFIG, "error");
			}
	}

	/*Subscriber lists*/
	uicommon.postbox.subscribe(function(value){
      self.currentViewIndex(uiConstants.common.LIST_VIEW);
      uicommon.postbox.publish("View is now a List","ViewIsChangedToList");
    }, "changeViewToList");

	uicommon.postbox.subscribe(function(value){
		uicommon.postbox.publish(self.currentViewIndex(),"currentViewIsChecked");
	},"checkCurrentView");
}

Transactionlistview.prototype.dispose = function() { };
return { viewModel: Transactionlistview, template: templateMarkup };
});
