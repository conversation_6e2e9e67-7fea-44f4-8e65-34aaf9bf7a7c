define(['jquery','bootstrap','knockout','validator','jquery-chosen','text!./add-multiple-kpi.html','hasher','ui-constants','ui-common','floatThead'], function($,bt,ko,validator,jc,templateMarkup,hasher,uiConstants,uicommon,floatThead) {

function Addmultiplekpi(params) {
	var self = this;

	this.tableHeaders = ko.observableArray([]);

	this.tableHeadersCore = ko.observableArray([
	  	{'attributeName': 'Sl.No.','name': '#','type': 'label','mandate':false},
	  	{'attributeName': 'Name','name':'Name','type':'input','mandate':true},
	  	{'attributeName': 'Description','name':'Description','type':'input','mandate':true},
	  	{'attributeName': 'Data Type','name':'DataType','type':'dataType','mandate':true},
	  	{'attributeName': 'Units','name':'Units','type':'units','mandate':true},
	  	{'attributeName': 'Cluster Operation','name':'ClusterOperation','type':'clusterOperation','mandate':true},
	  	{'attributeName': 'KPI Group','name':'KpiGroup','type':'kpiGroup','mandate':false},	
	  	{'attributeName': 'Tag','name':'Tag','type':'input','mandate':false},
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.tableHeadersConfigWatch = ko.observableArray([
	  	{'attributeName': 'Sl.No.','name': '#','type': 'label','mandate':false},
	  	{'attributeName': 'Name','name':'Name','type':'input','mandate':true},
	  	{'attributeName': 'Description','name':'Description','type':'input','mandate':true},
	  	{'attributeName': 'Data Type','name':'DataType','type':'dataType','mandate':true},
	  	{'attributeName': 'KPI Group','name':'KpiGroup','type':'kpiGroup','mandate':false},	
	  	{'attributeName': 'Tag','name':'Tag','type':'input','mandate':false},  
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.tableHeadersOthers = ko.observableArray([
	  	{'attributeName': 'Sl.No.','name': '#','type': 'label','mandate':false},
	  	{'attributeName': 'Name','name':'Name','type':'input','mandate':true},
	  	{'attributeName': 'Description','name':'Description','type':'input','mandate':true},
	  	//{'attributeName': 'Data Type','name':'DataType','type':'dataType','mandate':true},
	  	{'attributeName': 'KPI Group','name':'KpiGroup','type':'kpiGroup','mandate':false},	
	  	{'attributeName': 'Tag','name':'Tag','type':'input','mandate':false},  
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.tableHeadersSelect = ko.observableArray([
	  	{'attributeName': '','name': '','type': 'label','mandate':false},
	  	{'attributeName': '','name':'','type':'delete','mandate':false}
	]);

	this.kpiTypeArr = ko.observableArray(params.kpiTypeArr());
	this.kpiGroupArr = ko.observableArray(params.kpiGroupArr());
	this.kpiClusterOpernArr = ko.observableArray(params.kpiClusterOpernArr());
	this.kpiUnitsArr = ko.observableArray(params.kpiUnitsArr());
	this.pageSelected = params.pageSelected;
	this.kpiDataTypeArr = ko.observableArray();
	this.currentViewIndex = ko.observable(params.currentViewIndex());

    this.errorStack = ko.observableArray();
    this.typeSelected = ko.observable();
    this.reqRecordCounter = ko.observableArray();
    this.multiRowAddLimit = ko.observable(1001);
    this.errorStackLimitOnRows = ko.observable(10);
    this.requestDataArray = [];
    this.showAddRowBtn = ko.observable(1);
    var previousKpiTypeSelected = "";
    var previousKpiTypeIdSelected = 0;

	//render handler will be call after loading all elements of current SPA component.
	this.renderHandler=function(){
		var $tab = $('#tableMultipleAdd');
		$tab.floatThead({
			scrollContainer: function($table){
				return $table.closest('.wrapper');
			}
		});

		$(window).resize(function(){
		    self.refreshPageLayout();
		});
		/*Jquery chosen start*/
		jQuery(".chosen").chosen({
			search_contains: true	
		});
		$("#kpiTypeList").trigger('chosen:updated');

	    $("#kpiTypeList").on('chosen:showing_dropdown', function () {
	        // Store the current value on focus and on change
	        previousKpiTypeIdSelected = this.value;
	        previousKpiTypeSelected =  $("#kpiTypeList option:selected").text();
	    }).change(function() {
	        // Do something with the previous value after the change
	    });

	    self.refreshPageLayout();
	}

	this.refreshPageLayout = function(){
		$(".wrapper").height(($(window).outerHeight() - $(".wrapper").offset().top - $("#actionBtns").outerHeight() - 50) + "px");
		$('#tableMultipleAdd').floatThead('reflow');
	}

	this.onKpiTypeChange = function(){
		var rowCounter = $('#tableMultipleAdd tbody tr').length;
		self.typeSelected($('#kpiTypeList option:selected').text());
		
		if($('#kpiTypeList option:selected').text() == uiConstants.common.CONST_CORE){
			self.getConfirmOnTypeChange(rowCounter,self.tableHeadersCore(),previousKpiTypeIdSelected);	
		}
		else if($('#kpiTypeList option:selected').text() == uiConstants.common.CONST_CONFIGWATCH){
			self.getConfirmOnTypeChange(rowCounter,self.tableHeadersConfigWatch(),previousKpiTypeIdSelected);
		}
		else if($('#kpiTypeList option:selected').text() == uiConstants.common.STR_AVAILABILITY ||  $('#kpiTypeList option:selected').text() == uiConstants.common.CONST_FILEWATCH) {
			self.getConfirmOnTypeChange(rowCounter,self.tableHeadersOthers(),previousKpiTypeIdSelected);
		}
		else{
			showMessageBox(uiConstants.kpiProducerMap.CONFIRM_KPI_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
				if(r){
					debugger;
					self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", null, false), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
					self.tableHeaders(self.tableHeadersSelect());
					self.tableHeaders.removeAll();
					$('#tableMultipleAdd tbody').empty();
					$("#btnSave").addClass('disabled');	

					console.log("id selected : "+previousKpiTypeIdSelected);
				}
				else{
					console.log("id selected : "+previousKpiTypeIdSelected);
					$("#kpiTypeList").val(previousKpiTypeIdSelected).trigger('chosen:updated');
					self.typeSelected($('#kpiTypeList option:selected').text());
				}
			});
		}

		self.showAddRowBtn($('#kpiTypeList').val() == ""?1:0);
	}

	this.getConfirmOnTypeChange=function(rowCounter,dynmicheaders,previous){
		if(rowCounter == 0){
				self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", null, false), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));

				self.tableHeaders(dynmicheaders);
				self.getKpiDataTypelist($("#kpiTypeList").val());
				self.onAddClick();
		}else {
			showMessageBox(uiConstants.kpiProducerMap.CONFIRM_KPI_TYPE_CHANGE, "question", "confirm", function confirmCallback(r){
				if(r){
					self.kpiGroupArr($.grep(getMasterList(params.kpiGroupArr(), "id", null, false), function(e){ return e.kpiTypeId == $('#kpiTypeList').val(); }));
					
					self.tableHeaders(dynmicheaders);
					$('#tableMultipleAdd tbody').empty();
					$("#btnSave").addClass('disabled');	

					if($('#kpiTypeList option:selected').text() != "Select"){
						self.getKpiDataTypelist($("#kpiTypeList").val());
						self.onAddClick();
					}
				}
				else{
					$("#kpiTypeList").val(previous).trigger('chosen:updated');
					self.typeSelected($('#kpiTypeList option:selected').text());
				}
			});
		}
	}

	this.cancelAddScreen = function(){
		self.pageSelected("KPI Configuration");
		$('#tableMultipleAdd tbody').empty();
		params.currentViewIndex(uiConstants.common.LIST_VIEW);
	}

	/*Filter search Change events : START*/
	this.getKpiDataTypelist = function(selectedVal){
		if($('#kpiTypeList').val() != ""){
			var dataTypeObj = $.grep(self.kpiTypeArr(), function(e){ return e.kpiTypeId == selectedVal; });		
			self.kpiDataTypeArr(dataTypeObj[0].dataType);
		}
	}
	/*END*/

	//Adding blank row into the grid and calling binding listner and assigning ID's of row and col elements.
	this.onAddClick = function(){
		 //add new blank row initially on click of add button
		 if(uiConstants.common.DEBUG_MODE)console.log('%%%%%%%%%');
		 if(uiConstants.common.DEBUG_MODE)console.log(self.tableHeaders());
		 
		 var rowCounter = $('#tableMultipleAdd tbody tr').length;
		 var row = $('<tr class="" id="row_'+rowCounter+'"/>');
		 var tabIndexCounter = rowCounter * self.tableHeaders().length;

		 if(rowCounter <= self.multiRowAddLimit()-2){
	 		for (var i = 0; i < self.tableHeaders().length; i++) {		 	
			 	if(self.tableHeaders()[i].type == 'label'){
			 		row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.tableHeaders()[i].type+rowCounter+"'>"+(rowCounter+1)+"</label></span></td>");
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Name'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Name' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Description'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Enter Description' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'dataType' && self.tableHeaders()[i].name == 'DataType'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}
			 	else if(self.tableHeaders()[i].type == 'units'  && self.tableHeaders()[i].name == 'Units'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}
			 	else if(self.tableHeaders()[i].type == 'clusterOperation' && self.tableHeaders()[i].name == 'ClusterOperation'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}
			 	else if(self.tableHeaders()[i].type == 'kpiGroup' && self.tableHeaders()[i].name == 'KpiGroup'){
			 		row.append("<td class='col-xs-2'><select tabindex="+(tabIndexCounter)+" class='chosen chosen col-xs-10 form-control' id='"+self.tableHeaders()[i].name+rowCounter+"'/></td>");		 		
			 	}
			 	else if(self.tableHeaders()[i].type == 'input' && self.tableHeaders()[i].name == 'Tag'){
			 		row.append("<td class='col-xs-2'><input type='text' tabindex="+(tabIndexCounter)+" placeholder='Tags separated by comma(,)' id='"+self.tableHeaders()[i].name+rowCounter+"' class='col-xs-10 form-control'></td>");	
			 	}
			 	else if(self.tableHeaders()[i].type == 'delete'){
			 		row.append("<td class='col-xs-1 deleteRow' id='"+self.tableHeaders()[i].type+rowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
			 	}

			 	tabIndexCounter++;
			 }		 
			 $("#tableMultipleAdd tbody").append(row);
			 self.addDataListOption('dataType','DataType'+rowCounter, uiConstants.common.SELECT);
	         self.addDataListOption('units','Units'+rowCounter, uiConstants.common.SELECT);
			 self.addDataListOption('clusterOperation','ClusterOperation'+rowCounter, uiConstants.common.SELECT);
			 self.addDataListOption('kpiGroup','KpiGroup'+rowCounter, uiConstants.common.SELECT);
			 
			 $('#DataType'+rowCounter).on('change', function(e){
				self.addDataListOption('units','Units'+rowCounter, uiConstants.common.SELECT);
			 });
			 //delete row 
			 self.deleteRowBind("delete"+rowCounter);
			 
			 //calling table row listner
			 self.bindTableRowListner("row_"+rowCounter);
			 self.enableButton();

		 }else{
			showMessageBox(uiConstants.common.RECORDS_PASTE_LIMIT_EXCEEDED, "error");			
		}
		
		if($('#tableMultipleAdd tbody tr').length == 1){
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
		}
		else{
			$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled");
		}

		self.refreshPageLayout();
	}
	//required later to specifically show the units for related type
	/*this.setKpiUnits= function(dataTypeId,rowCnt){
		
    	for(indx in self.kpiDataTypeArr()){
    		if(self.kpiDataTypeArr()[indx].kpiDataTypeId == dataTypeId){
    			if(self.kpiDataTypeArr()[indx].kpiUnits.length !=0){
	    			self.kpiUnitsArr(self.kpiDataTypeArr()[indx].kpiUnits);
	    		}
    			break;
    		}
		}

		$('<option>', {text: uiConstants.common.SELECT, value: ''}).appendTo('#Units'+rowCnt);
		if(self.kpiUnitsArr().length != 0){
	 		self.kpiUnitsArr().forEach(function(item){
	 			$('<option>', {value: item.kpiUnitId, text: item.kpiUnitName, name: item.kpiUnitName}).appendTo('#Units'+rowCnt);
      		});
	 	}
	}*/


	//Loading all values into Type dropdown and Timezone custom dropdown and selecting current option otherwise default value will be set.
	this.addDataListOption = function(type, _class, _val ){
		_val = (typeof _val == "string" ? _val.trim() : _val);
		/*console.log(_class.slice(-1));
		var rowCnt=_class.slice(-1);*/
		if(type == 'dataType'){
			$('<option>', {text: uiConstants.common.SELECT, value: ''}).appendTo('#'+_class);
	 		self.kpiDataTypeArr().forEach(function(item){
	 			$('<option>', {value: item.kpiDataTypeId, text: item.dataTypeName, name: item.dataTypeName}).appendTo('#'+_class);
      		});

			//$('#'+_class+' option').filter(function () { return $(this).html() == compAttribHostAddrObj.attributeDefaultValue || $(this).html().indexOf("("+compAttribHostAddrObj.attributeDefaultValue+")") != -1 || $(this).html().indexOf(compAttribHostAddrObj.attributeDefaultValue+" ") != -1; }).prop('selected', true);

      		$('#'+_class+' option').filter(function() { 
				if(($(this).text() === _val)) {
				  return true;
				}                       
			}).prop('selected', true);

			//$('#'+_class+" option").filter(function(index) {debugger; return $(this).text() === (typeof _val == "string" ? _val.trim() : _val); }).attr('selected', 'selected');

			//$('#'+_class+'').val(16);

			/*$('#'+_class).on('click', function(e){
				 if($('#'+_class+' option:selected').text() != uiConstants.common.SELECT){
				 	$('#Units'+rowCnt+" option").remove();
				 	self.setKpiUnits($(this).val(),rowCnt);
				 }
			});*/
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid KPI datatype from the list.");}
 			
 		}
 		else if(type == 'units'){

 			console.log($('#DataType'+_class.substring(5)).val());
 			console.log(self.kpiDataTypeArr());
 			console.log(_class);
			$('#'+_class).empty();
			$('<option>', {text: uiConstants.common.SELECT, value: ''}).appendTo('#'+_class);

			if($('#DataType'+_class.substring(5)).val()){
				var kpiUnitsList = [];
				for(indx in self.kpiDataTypeArr()){
		    		if(self.kpiDataTypeArr()[indx].kpiDataTypeId == $('#DataType'+_class.substring(5)).val()){
		    			if(self.kpiDataTypeArr()[indx].kpiUnits.length !=0){
			    			kpiUnitsList = self.kpiDataTypeArr()[indx].kpiUnits;
			    		}
		    			break;
		    		}
				}

				if(kpiUnitsList.length != 0){
			 		kpiUnitsList.forEach(function(item){
			 			$('<option>', {value: item.kpiUnitId, text: item.kpiUnitName, name: item.kpiUnitName}).appendTo('#'+_class);
		      		});
			 	}
			}
			
      		$('#'+_class+' option').filter(function() {
			    if(($(this).text() === _val)) {return true;}			    
			}).prop('selected', true);
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid KPI units from the list.");}

			$('#'+_class).trigger('chosen:updated')
 		}
 		else if(type == 'clusterOperation'){
			$('<option>', {text: uiConstants.common.SELECT, value: ''}).appendTo('#'+_class);
	 		self.kpiClusterOpernArr().forEach(function(item){
	 			$('<option>', {value: item.clusterOperationId, text: item.clusterOperationName, name: item.clusterOperationName}).appendTo('#'+_class);
      		});
      		$('#'+_class+' option').filter(function() { 
			    if(($(this).text() === _val)) {return true;}			    
			}).prop('selected', true);
			
			if($("#"+_class).val() == ""){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid cluster operation from the list.");}
 		}
 		else if(type == 'kpiGroup'){
		if(uiConstants.common.DEBUG_MODE)console.log(type+"-------"+_class+"-----"+_val);
 			var flag = false;
 			$('<option>', {value: 'Select', text: 'Select', name: ''}).appendTo('#'+_class);
 			
 			self.kpiGroupArr().forEach(function(item){
      			if(_val == item.name){
      				$('<option>', {value: item.id, text: item.name, name: item.name, selected:true}).appendTo('#'+_class);
      				flag = true;
      			}else{
      				$('<option>', {value: item.id, text: item.name, name: item.name}).appendTo('#'+_class);	
      			}
      			
      		});

      		/*Jquery chosen start*/
			jQuery(".chosen").chosen({
				search_contains: true	
			});
			/*Jquery chosen start*/
			$("#"+_class+"_chosen .chosen-drop").css({"width": "auto", "white-space": "nowrap"});
      		
      					
			if($("#"+_class).chosen().val() != "" && flag == false){self.manageErrorMessage('push', _class.substr(_class.length-1), "Please select valid KPI group from the list.");}
 		}
	}

	//paste listner on every rows Application name filed and on rest of fileds treat as normal copy&paste.
	this.bindTableRowListner = function(rowid){
		
		 $('#row_0 input ,#row_0 select ').on('change', function(e){
		 	 $("#btnClearAll").removeClass('disabled');	

		 	 if($("#Name0").val() == "" &&  $("#Description0").val() == "" && $("#Tag0").val() == "" && $("#Type0 option:selected").text() == uiConstants.common.SELECT && $("#Data Type0 option:selected").text() == uiConstants.common.SELECT && $("#Units0 option:selected").text() == uiConstants.common.SELECT && $("#Cluster Operation0 option:selected").text() == uiConstants.common.SELECT){
		 	 	 $("#btnClearAll").addClass('disabled');
		 	 }
		 });


		 //on paste listener
		 var indexID = rowid.split("_")[1];
		 //$('#'+rowid+'>td>input').on('paste', function(e){
		 $('#'+rowid+'>td>#Name'+indexID).on('paste', function(e){
		 	var curObj = this;
		 	showLoadingContainer();

		 	e.stopPropagation();
		    e.preventDefault(); 	

		    if (e.originalEvent.clipboardData) { 
				var data = (e.originalEvent || e).clipboardData.getData('text/plain');
				var inputId = e.target.id;
			} else if (window.clipboardData) { 
				var data = window.clipboardData.getData('Text');
				var inputId = window.event.srcElement.id;
			}

		 	setTimeout(function () {
			    $("#btnClearAll").removeClass('disabled');	

				//data = data.slice(1, -1);
				if(uiConstants.common.DEBUG_MODE)console.log(data);

			   if(data != null){
			   		//clearing old vaule
			   		self.errorStack.removeAll();

			   		var crid = $(curObj).parent().parent().attr('id');
			   		$("#"+crid).addClass('empty');
			   		var curRowIndex = $(curObj).parent().parent().index();
					
			   		data.replace(/\n$/, "");
					var rows = data.split("\n");
					var table = $("#tableMultipleAdd tbody");
					var curType = uiConstants.common.SELECT;
					var curTZ = "";				
					
					var rowCounter = $('#tableMultipleAdd tbody tr').length;
					
					
					var limitFlag = (rows.length-1) + rowCounter;				
					if(limitFlag <= self.multiRowAddLimit()){
						if(uiConstants.common.DEBUG_MODE)console.log("Total no of rows----------------->"+limitFlag);
						
							var col = rows[0].split("\t");
							
							if(uiConstants.common.DEBUG_MODE)console.log(col.length +"=="+ (self.tableHeaders().length-2));

							if(col.length <= self.tableHeaders().length-2){

								rowCounter = $('#tableMultipleAdd tbody tr').length;
								

								

								rows.forEach(function (y, yindex) {	

								    var cells = y.split("\t");
								    if(uiConstants.common.DEBUG_MODE)console.log(cells);					    
								    var currentRowCounter = rowCounter + yindex;
								    var row = $("<tr class='' id='row_"+currentRowCounter+"'/>");
								    

								    if(yindex < rows.length-1){					    	
									    if(cells.length != self.tableHeaders().length-2){
									    	self.manageErrorMessage('push',currentRowCounter, "Cell values not be separated by newline('\\n')");
									    }

									    //control creation start
								    	for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {
								    		if(self.tableHeaders()[xindex].type == 'label' && self.tableHeaders()[xindex].name == '#'){
								    			row.append("<td class='col-xs-1 indexCol' scope='row'><label id='"+self.tableHeaders()[xindex].type+currentRowCounter+"'></label></td>");
								    		}
								    		else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Name'){
										 		row.append("<td class='col-xs-2'><input type='text' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Description'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Enter Description' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'dataType'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'units'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'clusterOperation'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'kpiGroup'){							 		
										 		row.append("<td class='col-xs-2'><select class='chosen col-xs-10 form-control' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"'/></td>");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Tag'){					    			
										 		row.append("<td class='col-xs-2'><input type='text' placeholder='Tags separated by comma(,)' id='"+self.tableHeaders()[xindex].name+currentRowCounter+"' class='col-xs-10 form-control'></td>");
										 	}
										 	
								    	};					   
									    row.append("<td class='col-xs-1 deleteRow' id='"+self.tableHeaders()[xindex].type+currentRowCounter+"'><span class='glyphicon glyphicon-remove buttondelete'></span></td>");
									    
									    //table.append(row);
									    $(".empty").after(row);
									    table.find('.empty').removeClass('empty').next().addClass('empty');

									    self.deleteRowBind("delete"+currentRowCounter);
									    self.bindTableRowListner("row_"+currentRowCounter);
									    //control creation end

									    var tarr = [currentRowCounter+1];
									    cells = tarr.concat(cells);
									    if(uiConstants.common.DEBUG_MODE)console.log(cells);

									    //value assign to current row controls start
									    for (var xindex = 0; xindex < self.tableHeaders().length-1; xindex++) {					    	
									    	
									    	//value assign
									    	if(self.tableHeaders()[xindex].type == 'label' && self.tableHeaders()[xindex].name == '#'){
									    		$("#"+self.tableHeaders()[xindex].type+currentRowCounter).text(cells[xindex]||"");
									    	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Name'){
									    		var cname = cells[xindex] || "";
									    		if(cname == ""){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
									    		}else if(nameValidation(cname) == 0){
									    			self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_NAME_LENGTH_ERROR);
									    		}					    			
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Description'){
										 		
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'dataType'){							 		
										 		self.addDataListOption('dataType','DataType'+currentRowCounter, cells[xindex]||"");
										 	}else if(self.tableHeaders()[xindex].type == 'units'){							 		
										 		self.addDataListOption('units','Units'+currentRowCounter, cells[xindex]||"");
										 	}else if(self.tableHeaders()[xindex].type == 'clusterOperation'){							 		
										 		self.addDataListOption('clusterOperation','ClusterOperation'+currentRowCounter, cells[xindex]||"");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'kpiGroup'){							 		
										 		self.addDataListOption('kpiGroup','KpiGroup'+currentRowCounter, cells[xindex]||"");
										 	}
										 	else if(self.tableHeaders()[xindex].type == 'input' && self.tableHeaders()[xindex].name == 'Tag'){
										 		var ctag = cells[xindex] || "";
										 		if(ctag != ""){
										 			if(ctag.trim().endsWith(","))
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
										 			else if(tagValidationWithComma(ctag) == 0)
									    				self.manageErrorMessage('push',currentRowCounter, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
									    		}
									    		if(uiConstants.common.DEBUG_MODE)console.log(ctag);	
										 		$("#"+self.tableHeaders()[xindex].name+currentRowCounter).val(cells[xindex]||"");
										 	}
									    }
									    //value assign to current row controls end
									}
								});
							}else{
								
								showMessageBox(uiConstants.common.COLUMNS_MISMATCH, "error");	
								return false;
							}	

					}else{
						showMessageBox(uiConstants.common.ROW_LIMIT_EXCEEDED, "error");
						self.enableButton();
						//self.onAddClick();
					}

					if(limitFlag <= self.multiRowAddLimit()){
						$("#tableMultipleAdd tbody").find('.empty').removeClass('empty');
						$("#"+crid).remove();

						if($('#tableMultipleAdd tbody tr').length == 1){
							$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
						}

						self.chnageRowAndCellId();
						self.validateElementsValue();
						if(self.errorStack().length){	
							self.manageErrorMessage('print');
							if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());								
						}
					}
				}


				//if(rowid > 200){
		 			removeLoadingContainer();

				//}
		 	}, 1);

		 	
		});

		
	}


	 //Changing row and col elements ID's when any delete or append/prepend operation happen in grid rows. 
    this.chnageRowAndCellId = function(){
    	var rowCnt = 0;
    	$('#tableMultipleAdd tbody tr').each(function(i){    		    					
	        $(this).children('td').each(function(j){
	        	if(j==0){// Application Index 
	            	$(this).children('label').attr('id','label'+rowCnt);
	            	$(this).children('label').text(rowCnt+1);
	            }else if(j==1){// kpi Name 
	            	$(this).children('input').attr('id','Name'+rowCnt);
	            }else if(j==2){// Descripition
	            	$(this).children('input').attr('id','Description'+rowCnt);
	            }else if(j==3){// dsataType
	            	$(this).children('select').attr('id','DataType'+rowCnt);
	            }else if(j==4){// Units
	            	$(this).children('select').attr('id','Units'+rowCnt);
	            }else if(j==5){//cluster operation
	            	$(this).children('select').attr('id','ClusterOperation'+rowCnt);
	            }else if(j==6){//KPI group
	            	$(this).children('select').attr('id','KpiGroup'+rowCnt);
	            }else if(j==7){//kpi Tag
	            	$(this).children('input').attr('id','Tag'+rowCnt);
	            }else if(j==8){
 					$(this).attr('id','delete'+rowCnt);
 				}
	        });
	        $(this).attr('id','row_'+rowCnt);
	        rowCnt++;
	    });

    }

	//after adding or pasting dynamic rows in to grid, delete row listner binding.
	this.deleteRowBind = function(deleteRowId){
		$('#'+deleteRowId).on('click', function(e){

			if(uiConstants.common.DEBUG_MODE)console.log($('#tableMultipleAdd tbody tr').length);	 		
		 		if($('#tableMultipleAdd tbody tr').length == 0){
		 			self.disableButton();
		 		}else if($('#tableMultipleAdd tbody tr').length == 1){
		 			return;
		 			/*var deleteFlag = true;
		 			$("#row_0").children('td').each(function(j){
		 				if(j==1){
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);   	
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }else if(j==2){
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }
		 				else if(j==3){// Kpi Type 
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').val().length);
			            	if($(this).children('select').val().length) deleteFlag=false;
			            }else if(j==4 && $('#kpiTypeList option:selected').text() == uiConstants.common.CONST_CORE){// Kpi Type 
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').val().length);
			            	if($(this).children('select').val().length) deleteFlag=false;
			            }else if(j==5 && $('#kpiTypeList option:selected').text() == uiConstants.common.CONST_CORE){// Kpi Type 
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('select').val().length);
			            	if($(this).children('select').val().length) deleteFlag=false;
			            }else if(j==6){// Tag
			            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val().length);
			            	if($(this).children('input').val().length) deleteFlag=false;
			            }
		 			});	
		            
		            	if(uiConstants.common.DEBUG_MODE)console.log(deleteFlag);
			 			if(deleteFlag){
			 				showMessageBox(uiConstants.common.ERROR_LAST_ROW_DELETE, "error");
			 			}else{
			 				$("#row_0").remove();
			 				self.onAddClick();
			 			} */
			 		
		 		}else{
		 			var curObj = this;
	 				showMessageBox(uiConstants.kpiConfig.CONFIRM_DELETE_KPI, "question", "confirm", function confirmCallback(confirmDelete){
						if(confirmDelete){
		 					$(curObj).parent().remove();

		 					self.chnageRowAndCellId();
					 		if($('#tableMultipleAdd tbody tr').length == 1){
								$("#tableMultipleAdd tr span.buttondelete").removeClass("confButtonDisabled").addClass("confButtonDisabled");
							}
			 			}
					});
		 		}

		 		//changing each row's Id and childrens id
		 		
		 });
	}	
    
	//enabling buttons based on conditions
	this.enableButton = function(){
		if($('#tableMultipleAdd tbody tr').length > 0){
			$("#btnAddMultiple").removeClass('disabled');
			$("#btnSave").removeClass('disabled');
		}
	}

	//clearing all rows with confirmation of user's. 
	this.onClearAll = function(){
		var rowCounter = $('#tableMultipleAdd tbody tr').length;
		if(rowCounter > 1){		
		    showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableMultipleAdd tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
			    }
			});
		}
		else if(rowCounter == 1 ){			
			showMessageBox(uiConstants.applicationMultipleAddEdit.CONFIRM_CLEAR_ROWS_CONTENT, "question", "confirm", function confirmCallback(r){
				if (r == true) {
			        $('#tableMultipleAdd tbody').empty();
					self.onAddClick();
					 $("#btnClearAll").addClass('disabled');	
					
			    }
			});
		}		
	}

	//managing all validation error message at pasting and saving time and pushing all to errorstack container.
	this.manageErrorMessage = function(flag, row, msg, addRecordCount){		
		if(flag == 'push'){
			self.errorStack.push({'rowno': row, 'message':msg});
		}else if(flag == 'print' && row == "" && msg == "" && addRecordCount != undefined){
			if(uiConstants.common.DEBUG_MODE)console.log("post api res"+"----"+self.reqRecordCounter()+"---"+addRecordCount);
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var unsavecnt = self.reqRecordCounter() - addRecordCount;
			//if(addRecordCount>1)
				var messageStr = addRecordCount+" "+uiConstants.kpiConfig.SUCCESS_MULTIPLE_ADD_KPIS+" and "+unsavecnt + (unsavecnt>1? " have" : " has" )+" failed. \n";
			//else
			//	var messageStr = addRecordCount+" application is successfully added and "+unsavecnt +" have failed. \n";

				messageStr += "\nReason:- \n";  
				self.errorStack().forEach(function(item,index){
					if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){
						item.rowno++;
						messageStr += "Line "+item.rowno+": "+item.message+"\n";
					}else{
						return false;
					}
				});
			showMessageBox(messageStr);
		}else if(flag == 'print'){
			if(uiConstants.common.DEBUG_MODE)console.log("print");
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			var messageStr = "";
			self.errorStack().forEach(function(item,index){
			if(uiConstants.common.DEBUG_MODE)console.log(item.rowno +">="+ 0 +"&&"+ index +"<="+ self.errorStackLimitOnRows());				
				/*if(item.rowno == -1){					
					messageStr += item.message +"\n";					
					self.enableButton();					
					if(DEBUG_MODE)console.log("count not match while copy");
				}else */
				if(item.rowno >= 0 && index <= self.errorStackLimitOnRows()){					
					item.rowno++;
					messageStr += "Line "+item.rowno+": "+item.message +"\n\n";
					if(uiConstants.common.DEBUG_MODE)console.log("rowno not ''");
				}else{
					return false;
				}
			});
			if(uiConstants.common.DEBUG_MODE)console.log(self.errorStack());
			showMessageBox(messageStr, "error");
		}
	}

	//Save & Paste time validating all elements values on each rows and pushing message to errorstack.
	this.validateElementsValue = function(startOffSet, endOffSet){
		self.errorStack.removeAll();
		self.requestDataArray = [];
		$('#tableMultipleAdd tbody tr').each(function(i){
			var cobj = {'index': i+1};
	        $(this).children('td').each(function(j){
	            if(j==1){// Kpi Name 
	            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.common.NAME_REQUIRED);
		    		}else if(nameValidation(cname) == 0){
		    			self.manageErrorMessage('push',cobj.index-1, uiConstants.kpiConfig.KPI_MULTIPLE_NAME_LENGTH_ERROR);
		    		}
	            	cobj['kpiName'] = $(this).children('input').val();
	            }
	            else if(j==2){// Kpi Description
	            	var cname = $(this).children('input').val();
	            	if(cname == ""){
	            		self.manageErrorMessage('push',cobj.index-1, uiConstants.login.DESC_REQUIRED);
	            	}
	            	cobj['description'] = $(this).children('input').val();
	            }
	            else if((slugify($("#kpiTypeList option:selected").text()) == "core" || slugify($("#kpiTypeList option:selected").text()) == "configwatch") && j==3){// Kpi data Type
	            	 var aTypeLen = $(this).children('select').val().length;	            	 
	            	 if(aTypeLen > 0){
	            	 	cobj['kpiDataTypeId'] = parseInt($(this).children('select').val());
	            	 }
	            	 else{
	            	 	self.manageErrorMessage('push',cobj.index-1, uiConstants.kpiConfig.KPI_DATATYPE_REQUIRED);
	            	 }
	            }
	            else if(slugify($("#kpiTypeList option:selected").text()) == "core" && j==4 ){// Kpi units
	            	 var aTypeLen = (self.typeSelected() == uiConstants.common.CONST_CORE)?$(this).children('select').val().length:0;	       
	            	 if(self.typeSelected() == uiConstants.common.CONST_CORE){	         	 
		            	 if(aTypeLen > 0){
			            	 cobj['kpiUnitId'] = parseInt($(this).children('select').val());
		            	 }
		            	 else{
		            	 	 self.manageErrorMessage('push',cobj.index-1, uiConstants.kpiConfig.KPI_UNITS_REQUIRED);
		            	 }
		            }else{
		            	cobj['kpiUnitId'] = 0;
		            }
	            }
	            else if(slugify($("#kpiTypeList option:selected").text()) == "core" && j==5 ){// Kpi cluster operation
	            	 var aTypeLen = (self.typeSelected() == uiConstants.common.CONST_CORE)?$(this).children('select').val().length:0;

	            	 if(self.typeSelected() == uiConstants.common.CONST_CORE){	            	 
		            	 if(aTypeLen > 0){
		            	 	cobj['clusterOperationId'] = parseInt($(this).children('select').val());
		            	 }
		            	 else{
		            	 	self.manageErrorMessage('push',cobj.index-1, uiConstants.kpiConfig.KPI_CLUSTEROPERN_REQUIRED);
		            	 }
		            }else{
		            	cobj['clusterOperationId'] = 0;
		            }
	            }
	            else if((slugify($("#kpiTypeList option:selected").text()) == "core" && j==6) || (slugify($("#kpiTypeList option:selected").text()) == "configwatch" && j==4) || (slugify($("#kpiTypeList option:selected").text()) == "availability" && j==3) || (slugify($("#kpiTypeList option:selected").text()) == "filewatch" && j==3)){// KPI Group
				   	cobj['kpiGroupId'] = $(this).children('select').val() || 0;
	            }
	            else if((slugify($("#kpiTypeList option:selected").text()) == "core" && j==7) || (slugify($("#kpiTypeList option:selected").text()) == "configwatch" && j==5) || (slugify($("#kpiTypeList option:selected").text()) == "availability" && j==4) || (slugify($("#kpiTypeList option:selected").text()) == "filewatch" && j==4)){// Kpi Tag
	            	if(uiConstants.common.DEBUG_MODE)console.log($(this).children('input').val());
	            	var ctag = $(this).children('input').val();
	            	if(ctag != ""){
			 			if(ctag.trim().endsWith(","))
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.common.TAGS_LISTS_ENDS_WITH_COMMA_ERROR);
			 			else if(tagValidationWithComma(ctag) == 0)
		    				self.manageErrorMessage('push',cobj.index-1, uiConstants.applicationMultipleAddEdit.APPLICATION_MULTIPLE_TAG_LENGTH_ERROR);
		    		}
	            	var taglist = $(this).children('input').val();
	            	if(taglist != ""){
	            		taglist = taglist.split(",");
	            		if(uiConstants.common.DEBUG_MODE)console.log("*********");
	            		if(uiConstants.common.DEBUG_MODE)console.log(taglist);
	            		var tagObj = [];
	            		for (var i = 0; i < taglist.length; i++) {
	            			if($.grep(tagObj, function(evt){ return evt.tagName == taglist[i].trim(); }).length>0){
		    					self.manageErrorMessage('push',cobj.index-1, uiConstants.common.DUPLICATE_TAGS);
		    					break;
	            			}
	            			//tagObj.push({'tagId':null , 'tagName': taglist[i]});
	            			tagObj.push({"tagId":null, "tagName":taglist[i].trim(), "tagOperation":"add"});
	            		};
	            	}
	            	cobj['tags'] = tagObj;
	            }
	        });

			if(slugify($("#kpiTypeList option:selected").text()) != "core" && slugify($("#kpiTypeList option:selected").text()) != "configwatch"){
            	cobj['kpiDataTypeId'] = self.kpiDataTypeArr()[0].kpiDataTypeId;   
			}

        	cobj['kpiTypeId'] = parseInt($("#kpiTypeList option:selected").val());
			cobj['status'] = 1;
			cobj['isCustom'] = 1;
			self.requestDataArray.push(cobj);
	    });
	}

	//save/create/add all applications to the DB with validation on all fields and sending request to the server. 
	this.onMultipleSaveClick = function(){
		self.requestDataArray = [];
    	self.validateElementsValue();

		if(self.errorStack().length){	
			self.manageErrorMessage('print');
		}else{
			self.reqRecordCounter(self.requestDataArray.length);
			var finalObj = {'kpis':self.requestDataArray};
			if(uiConstants.common.DEBUG_MODE)console.log("==================== Add Multiple Kpi===================");
			if(uiConstants.common.DEBUG_MODE)console.log(JSON.stringify(finalObj));
			var url = uiConstants.common.SERVER_IP+"/kpiDetail";
			requestCall(url, 'POST', JSON.stringify(finalObj), 'AddKpis', successCallback, errorCallback);	
		}
    }

    //Based on Add Applications server response created apps remove from grid and rest are in same grid and pushing all failure message with line index to errorstack.
    this.processFailureResponse = function(res){
    	if(uiConstants.common.DEBUG_MODE)console.log(res);
    	if(uiConstants.common.DEBUG_MODE)console.log(res.length);
    	var succnt = 0;
    	var failcnt = 0;
    	if(uiConstants.common.DEBUG_MODE)console.log(self.reqRecordCounter() +"=="+ res.length);
    	if(self.reqRecordCounter() == res.length){
    		for (var i = 0; i < res.length; i++) {
    			//if(DEBUG_MODE)console.log(res[i]);
    			//if(DEBUG_MODE)console.log(res[i].index);
    			//var rid = res[i].index-1;
    			var rid = parseInt(res[i].objectId)-1;
    			if(res[i].responseStatus == uiConstants.common.CONST_SUCCESS){
    				$("#tableMultipleAdd tbody #row_"+rid).remove();
    				succnt++;	
    			}else{
    				self.manageErrorMessage('push',failcnt,handleServiceErrorMsgs(uiConstants.common.CONST_KPI,res[i].errorCode,res[i].message));//handleServiceErrorMsgs(CONST_KPI,res[i].errorCode,res[i].message
    				if(uiConstants.common.DEBUG_MODE)console.log("push message in errorStack"+res[i].message);
    				failcnt++;
    			}
    		};

    		//changing row index and number
    		self.manageErrorMessage('print',"","",succnt);
    		self.chnageRowAndCellId();
    	}else{
    		showMessageBox(uiConstants.common.REQUEST_RESPONSE_RECORDS_MISMATCH, "error");
    	}
    }

    function successCallback(data, reqType) {
		if(reqType === "AddKpis"){
			if(uiConstants.common.DEBUG_MODE)console.log(data);
			var res = data.result;
			if(data.responseStatus == uiConstants.common.CONST_FAILURE){
				if(res != undefined && res[0] != undefined)
					self.processFailureResponse(data.result);
				else
					showMessageBox(uiConstants.kpiConfig.ERROR_ADD_MULTIPLE_KPIS, "error");
			}
			else{
				showMessageBox(uiConstants.kpiConfig.SUCCESS_MULTIPLE_ADD_KPIS);
				$('#tableMultipleAdd tbody').empty();
				if(self.kpiTypeArr().length){
					$("#btnAddMultiple").trigger('click');
				}
				self.cancelAddScreen();
			}
		}
	}

	function errorCallback(reqType) {
		showMessageBox(uiConstants.kpiConfig.ERROR_ADD_MULTIPLE_KPIS, "error");
	}

}

Addmultiplekpi.prototype.dispose = function() { };
return { viewModel: Addmultiplekpi, template: templateMarkup };

});