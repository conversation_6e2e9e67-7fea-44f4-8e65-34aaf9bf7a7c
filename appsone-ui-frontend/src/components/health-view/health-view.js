define(['jquery','knockout','fusionCharts','text!./health-view.html','hasher','ui-constants','ui-common'], function($,ko,fusioncharts,templateMarkup,hasher,uiConstants,uicommon) {

  function HEALTHVIEW(params) {
    this.message = ko.observable('Hello from the '+params.podTitle+' component!'); 
    var self = this;
    this.HVPodId = ko.observable(params.podId+'_health-view');
    this.currentPodBodyHeight = ko.observable();
    this.currentPodBodyWidth = ko.observable();
    this.podId = ko.observable(params.podId);
    this.podTitle = ko.observable(params.podTitle);
    this.podType = ko.observable(params.podType);
    this.isModal = ko.observable(params.isModal);

    if(uiConstants.common.DEBUG_MODE) console.log(params.podId+"--"+params.podTitle+"--"+params.podTyp);

    this.renderHandler = function(){  
        self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
        //$('#podBody_'+params.podId).css('height',self.currentPodBodyHeight()+'px');
        self.currentPodBodyWidth($('#pod_'+params.podId).width()-35);

        self.initChart();
    }

     this.initChart = function(){
          FusionCharts.ready(function () {
          var cSatScoreChart = new FusionCharts({
              type: 'angulargauge',
              renderAt: self.HVPodId(),
              width: self.currentPodBodyWidth()-5,
              height: self.currentPodBodyHeight()-5,
              dataFormat: 'json',
              dataSource: {
                  "chart": {
                      "caption": "",
                      "subcaption": "",
                      "lowerLimit": "0",
                      "upperLimit": "100",                
                      "showValue": "1",
                      "pivotRadius": "10",
                      "valueBelowPivot": "1",
                      "gaugeFillMix": "{dark-30},{light-60},{dark-10}",
                      "gaugeFillRatio": "15",
                      "theme": "fint",
                      "canvasBgColor" : "#ffffff", 
                      "bgColor":"#ffffff",
                      "showBorder":"0",
                      "canvaspadding":"0",
                      //Modifying chart left margin
                      "chartLeftMargin": "0",
                      //Modifying chart top margin
                      "chartTopMargin": "0",
                      //Modifying chart right margin
                      "chartRightMargin": "0",
                      //Modifying chart bottom margin
                      "chartBottomMargin": "0",
                      // Enable export
                      "exportEnabled": "1",
                      "exportAtClientSide":"1",
                      // Hide export menu item
                      "exportShowMenuItem": "1",
                      "showExportDialog":"1",
                      "exportDialogMessage":"Capturing Data : ",
                      "exportDialogColor":"#333",
                      "exportDialogPBColor":"#0372ab",
                      "toolbarButtonColor":"#999999",
                      "exportFileName":self.podTitle(),
                      //Tooltip customization        
                        "toolTipColor": "#ffffff",
                        "toolTipBorderThickness": "0",
                        "toolTipBgColor": "#000000",
                        "toolTipBgAlpha": "80",
                        "toolTipBorderRadius": "2",
                        "toolTipPadding": "5",
                        //External image url path for logo
                        //"logoURL": "/../../images/logo.png",
                        //Changing logo alpha
                        "logoAlpha": "40",
                        //Scaling logo image
                        "logoScale": "40",
                        //Setting logo position
                        "logoPosition": "BR"


                  },
                  "colorRange": {
                      "color": [
                          {
                              "minValue": "0",
                              "maxValue": "50",
                              "code": "#6B8E23"                              
                          },
                          {
                              "minValue": "50",
                              "maxValue": "75",
                              "code": "#d58512"
                          },
                          {
                              "minValue": "75",
                              "maxValue": "100",
                              "code": "#d43f3a"
                          }
                      ]
                  },
                  "dials": {
                      "dial": [{
                          "value": "67"
                      }]
                  }
              }
          }).render();
      });

        /*$('.grid-stack').on('resizestop', function(event, ui) {
            self.currentPodBodyHeight($('#pod_'+params.podId).height()-35);
            if(DEBUG_MODE) console.log("****Height--"+self.currentPodBodyHeight());
            var grid = this;
            var element = event.target;
            var podId = $(element).attr('id');
            if(DEBUG_MODE) console.log(grid);
            if(DEBUG_MODE) console.log(element);
            if(DEBUG_MODE) console.log($(element).attr('id'));
            if(DEBUG_MODE) console.log($(element).find(".chartCont"));
            //$(element).children("div").attr('height',(self.currentPodBodyHeight()-5)+'!important');
          
            cSatScoreChart.resizeTo({
                'height': parseInt(self.currentPodBodyHeight()-5),
            });
        });*/
     }

  }
  
  // This runs when the component is torn down. Put here any logic necessary to clean up,
  // for example cancelling setTimeouts or disposing Knockout subscriptions/computeds.
  HEALTHVIEW.prototype.dispose = function() { };
  
  return { viewModel: HEALTHVIEW, template: templateMarkup };

});