	define(['cryptojs-core','cryptojs-aes','hasher','ui-constants','knockout','moment'], function(CryptoJSCore,CryptoJSAES,hasher,uiConstants,ko,moment) {
		hasherVar = hasher;
		koLoginData = ko.observable();
		koPreviousHash = ko.observable();
		koGlobalMultipleLogin = ko.observable(1);
		koMenuPermissions = ko.observable();
		commonAddEnabled = ko.observable();
		commonUpdateEnabled = ko.observable();
		commonDeleteEnabled = ko.observable();
    	sideMenuObj = ko.observable({});
  		userName = ko.observable();
  		currentViewIndex = ko.observable();
  		compInstDetailsChaged = ko.observable(false);
		compInstDetailsClicked = ko.observable(false); //set click to true/false to determine the if he change event is valid
  		appDetailsChaged = ko.observable(false);
  		appDetailsClicked = ko.observable(false);
  		txnDetailsChaged = ko.observable(false);
  		txnDetailsClicked = ko.observable(false);
  		agentDetailsChaged = ko.observable(false);
  		agentDetailsClicked = ko.observable(false);

  		window.validateName = function(element){
  			removeError(element);

			if($("#divAppAddEdit #txtName").val() == ""){
				showError(element, uiConstants.applicationConfig.APPLICATION_NAME_REQUIRED);
			}
			else if($("#divAppAddEdit #txtName").val().length < 2){
				showError(element, uiConstants.applicationConfig.APPLICATION_NAME_MIN_LENGTH_ERROR);
			}
			else if($("#divAppAddEdit #txtName").val().length > 45){
				showError(element, uiConstants.applicationConfig.APPLICATION_NAME_MAX_LENGTH_ERROR);
			}
			else if(!nameValidation($("#divAppAddEdit #txtName").val())){
				showError(element, uiConstants.applicationConfig.APPLICATION_NAME_INVALID_ERROR);
			}
  		}

  		window.validateDescription = function(element){
  			removeError(element);

			if($(element).val() == ""){
				showError(element, uiConstants.common.DESCRIPTION_REQUIRED);
			}
			else if($(element).val().length < 25){
				showError(element, uiConstants.common.DESCRIPTION_MIN_LENGTH_ERROR);
			}
			else if($(element).val().length > 256){
				showError(element, uiConstants.common.DESCRIPTION_MAX_LENGTH_ERROR);
			}
  		}

  		window.validateTags = function(parentElement, element, tags){
  			removeError(parentElement + " .tokenfield");
			removeError(parentElement + " "+element);

			if($(parentElement + " "+element).val() != ""){
				if(tags()){
					tags(tags()+","+$(parentElement + " "+element).val());
				}
				else{
					tags($(parentElement + " "+element).val());
				}
			}
				
			if(containsDuplicate(tags())){
				//self.errorMsg(uiConstants.common.DUPLICATE_TAGS);
				showError(parentElement + " .tokenfield", uiConstants.common.DUPLICATE_TAGS);
				showError(parentElement + " "+element, uiConstants.common.DUPLICATE_TAGS);
			}
			else{

	  			var tagsArr = tags() == "" ? [] : tags().split(",");
	  			for(var t in tagsArr){
					if(tagsArr[t].trim().length < 2){
						showError(parentElement + " .tokenfield", uiConstants.common.TAG_MIN_LENGTH_ERROR);
						showError(parentElement + " "+element, uiConstants.common.TAG_MIN_LENGTH_ERROR);
						break;
					}
					else if(tagsArr[t].trim().length > 45){
						showError(parentElement + " .tokenfield", uiConstants.common.TAG_MAX_LENGTH_ERROR);
						showError(parentElement + " "+element, uiConstants.common.TAG_MAX_LENGTH_ERROR);
						break;
					}
					else if(!tagValidation(tagsArr[t].trim())){
						showError(parentElement + " .tokenfield", uiConstants.common.INVALID_TAG_NAME);
						showError(parentElement + " "+element, uiConstants.common.INVALID_TAG_NAME);
						break;
					}
				}
			}
  		}

  		window.validateOption = function(element, errorMsg){
  			if($(element).val() == 0 || $(element).val() == '' || $(element).val() == uiConstants.common.SELECT || $(element).val() == uiConstants.common.ENTER_SELECT){
					//self.errorMsg(uiConstants.common.APPLICATION_TYPE_REQUIRED);
					showError(element+"_chosen", errorMsg);
					showError(element+"_chosen span", errorMsg);
			    	self.errorMsg(element+"_chosen");
				}
				else{
					removeError(element+"_chosen");
					removeError(element+"_chosen span");
				}
  		}

		var subscribersArr = {};
		var inactiveOptionObj;

        /*Knock out PUB-SUB utility*/         
        this.postbox = (function() {
		    var pb = new ko.subscribable();
		    return {    
		        subscribe: function(handler, topic) {
					if(subscribersArr.hasOwnProperty(topic)){
						subscribersArr[topic].dispose();
					}
					subscribersArr[topic] = pb.subscribe(handler, null, topic);
		        },        
		        publish: function(message, topic) {
		            pb.notifySubscribers(message, topic);
		        }
		    };
		}());

		/*CONVERT GMT TO LOCAL BROWSER TIME*/
		gmtToLocalDateTime = function(gmt_time_milliseconds){
			var momentFormatDate = moment(new Date(gmt_time_milliseconds + localOffset )).format("YYYY-MM-DD hh:mm:ss");
			return momentFormatDate;
		}

		/*CONVERT LOCAL BROWSER TIME TO GMT*/
		localToGmtDateTime = function(local_Time){
			var local_Time_milliseconds = moment(local_Time,'YYYY-MM-DD HH:00').valueOf();
			var momentFormatDate = moment(new Date(local_Time_milliseconds - localOffset ));
			return momentFormatDate;
		}




		/**@method - buildURLFromParamObj - Builds URL from given query params object
		 * @param  paramsArr {Array} - Array of query params in key:value pair
		 * @return queryParamsForURL {[String]}
		 */
		this.buildURLFromParamObj = function(paramsArr){
			var queryParamsForURL = "";
			var currentURL = location.href;

			if(currentURL.indexOf('?')==-1){
				queryParamsForURL+='?';
			}
			
			for(var i=0;i<paramsArr.length;i++){
				var currObj = paramsArr[i];
				var currObjKey= Object.keys(currObj)[0];
				queryParamsForURL+=  currObjKey + '=' + currObj[currObjKey];

				if(i!=paramsArr.length-1){
					queryParamsForURL+='&';
				}
			}
			return queryParamsForURL;
		};
		
		return this;
	});

	var hasherVar;
	var koLoginData;
	var koPreviousHash;
	var koGlobalMultipleLogin;
	var koMenuPermissions;
	var sideMenuObj;
	var authToken = "";
	var configType = "";
	var currentViewIndex = 0;
	var selectedConfigRows = [];
	var commonMenuFunc = null;
	var txtUserName;
	var txtPassword;
	var loginError;
	var idleTimeoutMilli = localStorage.timeout ? localStorage.timeout : 0;
	var idleTimer=null;
	var logoutUser = false;
	var isSessionTimeout = false;
	var curHasher = "";
	var userName;
	var nonTokenBasedPages = ["changePassword","forgotPassword"];
	var nonMenuBasedRequests = ["authenticateUser", "changeUserPassword", "sendPasswdChangeEmail", "forgotPassword", "logoutUser"];
	var nonPermisionBasedPages = ["kpiGroupListView","viewIndividualProfile", "changePassword", "globalSearch", 'home', 'applicationConfigWizard', 'addMultipleKpi', 'addMultiple', 'configWizard', 'appConfig', 'appTypeAddEdit', 'compTypeAddEdit'];
	var lastAction = "";
	var commonAddEnabled;
	var commonUpdateEnabled;
	var commonDeleteEnabled;
	var currentViewIndex;
	var globalSearchTxt = "";
	var compInstDetailsChaged;
	var compInstDetailsClicked;
	var appDetailsChaged;
	var appDetailsClicked;
	var txnDetailsChaged;
	var txnDetailsClicked;
	var agentDetailsChaged;
	var agentDetailsClicked;
	var agentConfDownloadData;
	var forgotPasswordClick = false;
	var allowLogin = 0;
	var gmtToLocalDateTime;
	var localToGmtDateTime;
	var currentPageMode;

	/*Dashboard related Objects & Arrays start*/
	var dcEumPodList = [{'type':'Transaction_Health'}, {'type':'Slow_Transactions'}, {'type':'High_Volume_Transactions'}, {'type':'Transaction_Performance'}, {'type':'Aggregated_Transactions'}];
	var fullScreenPodList = [{'type':'Topology'}, {'type':'Slow_Transactions'}, {'type':'High_Volume_Transactions'}, {'type':'Transaction_Performance'}, {'type':'Aggregated_Transactions'}, {'type':'KPI_Performance'}, {'type':'Appserver KPI Performance'}, {'type': 'Transaction_Health'}, {'type': 'BVE'}];
	
	var currDate = new Date();
	var localOffset = currDate.getTimezoneOffset();
	
	/*Dashboard related Objects & Arrays end*/

	$("#loginModal").keypress(function(e){
		if(e.keyCode==13){
		  $('#btnLogin').click();
		}
	});

	$('#loginModal').on('shown.bs.modal', function () {
	    $('#textUsername').focus();
	})


	//idleTimer = window.setTimeout(handleTimeout,idleTimeoutMilli); 

	//var idleInterval = setInterval(timerIncrement, 60000); // 1 minute

	    //Zero the idle timer on mouse movement.
    $(this).click(function (e) {
    	if($("#idleWarningModal").css('display') == 'none' && $("#loginModal").css('display') == 'none' && hasherVar.getHash() != ""){
	    	if(uiConstants.common.DEBUG_MODE)console.log(idleTimeoutMilli);
	  		startIdleHandler();
	 	}
    });

    $(this).keypress(function (e) {
    	if($("#idleWarningModal").css('display') == 'none' && $("#loginModal").css('display') == 'none' && hasherVar.getHash() != ""){
	    	if(uiConstants.common.DEBUG_MODE)console.log(idleTimeoutMilli);
	  		startIdleHandler();
	  	}
    });



    /*$(this).mouseover(function(e){
    	$("div").find("input,button,textarea").on("mouseover", function(){
    		if(!$(this).hasClass("error-highlighter")){
    			if(slugify($(this).attr("type")) == "checkbox"){
	    			if($(this).attr("title")){// || $(this).attr("title") == "Select" || $(this).attr("title") == "Unselect"){
	    				if($(this).attr("title").endsWith("All")){
	    					$(this).attr("title", $(this).is(":checked") ? "Unselect All" : "Select All");
	    				}
	    				else{
	    					$(this).attr("title", $(this).is(":checked") ? "Unselect" : "Select");
	    				}
	    			}
	    			else{
	    				$(this).attr("title", $(this).is(":checked") ? "Unselect" : "Select");
	    			}
	    		}
	    		else if(slugify($(this).attr("type")) != "password" && slugify($(this).attr("type")) != "button"){
					$(this).attr("title", $(this).val());
	    		}
    		}
		});

		$("div").find("select").on("mouseover", function(){
    		if(!$(this).hasClass("error-highlighter")){
				$(this).attr("title", $(this).find('option:selected').text());
			}
		});

		$("div").find("label").on("mouseover", function(){
    		if(!$(this).hasClass("error-highlighter")){
				$(this).attr("title", $(this).text());
			}
		});

		$(".chosen-container").find("span").on("mouseover", function(){
    		if(!$(this).hasClass("error-highlighter")){
				$(this).attr("title", $(this).text());
			}
		});

		$("table").find("td").on("mouseover", function(){
			if(!$(this).attr("title") && !$(this).hasClass("error-highlighter")){
				if(!$(this).children().length){
					$(this).attr("title", $(this).html());
				}
			}
			
		});


		$("table").find("th").on("mouseover", function(){
			if(!$(this).attr("title") && !$(this).hasClass("error-highlighter")){
				if(!$(this).children().length){
					$(this).attr("title", $(this).html());
				}
			}
			
		});
	});*/

    $(this).ajaxStop(function() {
		removeLoadingContainer();
	});

	/*$("input[type=number]").on("keypress", function (evt) {
	    if (evt.which < 48 || evt.which > 57)
	    {
	        evt.preventDefault();
	    }
	});*/


	function scrollToPos(position, duration, elementSelector){
		if(elementSelector == undefined){
			elementSelector = 'html, body';
		}
		$(elementSelector).animate({
	    	scrollTop: position
	    }, duration);
	}

  	function startIdleHandler(){
  		logoutUser=false;
	  	if(idleTimeoutMilli != 0){
			window.clearTimeout(idleTimer);
			idleTimer = window.setTimeout(handleTimeout,idleTimeoutMilli);
		}
	}

	 function handleTimeout(){

	 	if(!logoutUser){
	 		$("#idleWarningModal").modal("show");
	 		window.clearTimeout(idleTimer);
			idleTimer = window.setTimeout(handleTimeout,uiConstants.common.IDLE_WARNING_TIME_MILLI);
			logoutUser=true;
		}

	  	else{

	  		if(localStorage.apptoken){
	            localStorage.removeItem("apptoken");
				window.koMenuPermissions(null);
	        }

	        $("#idleWarningModal .modal-header button").click();
	        logoutUser=false;
	        isSessionTimeout = true;
	        logout();
	       
	        requestCall(uiConstants.common.SERVER_IP + "/user/tokenLastUsedTime", "PUT", "", null, successLastUsedCallback, errorLastUsedCallback);
	        removeLoadingContainer();
	  	}
	 }

	function isRegexValid(pattern, val){
		try{
			val.match(pattern);
			return true;
		}
		catch(error){
			return false;
		}
	}

	function showError(elementSelector, errorMsg){
		//$('html,body').animate({scrollTop: $(elementSelector).offset().top-100});
		$(elementSelector).addClass("error-highlighter");
		$(elementSelector).attr("title", errorMsg);
	}

	function removeError(elementSelector){
		if(elementSelector){
			$(elementSelector).removeClass("error-highlighter");
			$(elementSelector).removeAttr("title");
		}
		else{
			$("body").find("div, input, textarea, select").removeClass("error-highlighter");
		}
	}

	function diffArray(a, b) {
		var seen = [], diff = [];
		for ( var i = 0; i < b.length; i++)
		  seen[b[i]] = true;
		for ( var i = 0; i < a.length; i++)
		  if (!seen[a[i]])
		      diff.push(a[i]);
		return diff;
	}

	function sortArrayObjByValue(arr, objKey) {
		arr.sort(function(a, b){
				if (a[objKey] < b[objKey]){
				return -1 ;
			}
			else if (a[objKey] > b[objKey]){
				return 1;
			}
			else{
				return 0;
			}
		});
	}

	function sortNumbersArray(arr){
		arr.sort(function(a,b){
			return a - b
		})
	}

	function hasDuplicateDecimalPoint(val, x) {
        var decimalIndex = (val.match(/\./g) || []).length;
        return  decimalIndex > 1;
    }

	function getRandomString(){
		return CryptoJS.lib.WordArray.random(8).toString();
	}

	function getEncodedText(ivStr, plainText){
		var iv = CryptoJS.enc.Utf8.parse(ivStr);
		var key = CryptoJS.enc.Base64.parse(uiConstants.common.SECRETE_KEY);
		var options = { mode: CryptoJS.mode.CBC, padding: CryptoJS.pad.Pkcs7 };  

		/*** encrypt */  
		var json = CryptoJS.AES.encrypt(plainText, key, {
		iv:iv,
		padding: CryptoJS.pad.Pkcs7
		});

		return json.ciphertext.toString(CryptoJS.enc.Base64);
	}

	function onForgotPasswordClick(){
		window.forgotPasswordClick = true;
		hasherVar.setHash("#");
	}

	function onChosenDropdownOpen(elementId, parentDiv){
		var elementTop = $("#" + elementId + "_chosen").position().top;
		var elementWidth = $("#" + elementId + "_chosen").outerWidth();
		var parentDivFullWidth = $("#"+parentDiv).width() - $("#"+parentDiv)[0]['clientWidth'];
		//alert($("#"+parentDiv).outerWidth());
		$("#" + elementId + "_chosen").css('position', 'absolute');
		$("#" + elementId + "_chosen").css('top', elementTop+'px');
		//$("#" + elementId + "_chosen").css('width', '');
		//$("#" + elementId + "_chosen").attr('style', 'width: ' + elementWidth + 'px !important');
		$("#" + elementId + "_chosen").attr('style', function(i, existingStyle) { return existingStyle + 'width: ' + elementWidth + 'px !important;' });
		$("#"+parentDiv).css('overflow', 'hidden');
		$("#"+parentDiv).css('margin-right', parentDivFullWidth+'px');
	}

	function onChosenDropdownClose(elementId, parentDiv){
		$("#" + elementId + "_chosen").css('position', 'relative');
		$("#" + elementId + "_chosen").css('top', '0px');
		$("#" + elementId + "_chosen").css('width', '0px');
		$("#"+parentDiv).css('overflow-y', 'scroll');
		$("#"+parentDiv).css('margin-right', '0px');
	}

	function rgbTohex(rgb) {
	    rgb = rgb.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
	    function hex(x) {
	        return ("0" + parseInt(x).toString(16)).slice(-2);
	    }
	    return "#" + hex(rgb[1]) + hex(rgb[2]) + hex(rgb[3]);
	}

	function handleInvalidToken(reqMethod){
		authToken = "";
		window.clearTimeout(idleTimer);

		if(reqMethod == "GET"){
			uiConstants.common.FIRST_PAGE_AFTER_LOGIN = "#"+hasherVar.getHash();
		}
		else if(reqMethod == "POST" || reqMethod == "PUT"){
			uiConstants.common.FIRST_PAGE_AFTER_LOGIN = "";
		}

	  	$(loginError).text("");
	 	$("#loginModal").modal("show");
	}

	function containsDuplicate(arr){
		var unsortedTagsArr = arr.split(",");
		var sortedTagsArr = unsortedTagsArr.slice().sort();
		var limit=unsortedTagsArr.length-1;
		for (var i = 0; i < unsortedTagsArr.length-1; i++) {
		    if (sortedTagsArr[i + 1].trim() == sortedTagsArr[i].trim()) {
		        return true;
		    }
		    else if(sortedTagsArr[limit].trim() == sortedTagsArr[0].trim()){
		    	return true;
		    }
		}

		return false;
	}

	function removeDuplicates(arr){
		var uniqueValues = [];
		$.each(arr, function(i, ele){
		    if($.inArray(ele, uniqueValues) === -1){
		    	uniqueValues.push(ele);
		    }
		});
		return uniqueValues;
	}

	function removeArrayObjElements(soruceArr, sourceKey, eleToDeleteArr, eleToDeleteKey, removeCommon){
		soruceArr = soruceArr.filter( function( el ) {
		  	return $.grep(eleToDeleteArr, function(e){ return e[eleToDeleteKey] == el[sourceKey]; })[0] ? !removeCommon : removeCommon;
		});

		return soruceArr;
	}

	function removeArrayElements(soruceArr, eleToDeleteArr, removeCommon){
		soruceArr = soruceArr.filter( function( el ) {
		  	return $.grep(eleToDeleteArr, function(e){ return e == el; })[0] ? !removeCommon : removeCommon;
		});

		return soruceArr;
	}

	function slugify(txt){
		if(txt){
		  	return txt.toString().toLowerCase()
			    //.replace(/\s+/g, '-')           //Replace spaces with -
			    .replace(/[^\w\-]+/g, '')       //Remove all non-word chars
			    .replace(/\-\-+/g, '-')         //Replace multiple - with single -
			    .replace(/^-+/, '')             //Trim start of text
			    .replace(/-+$/, '');            //Trim end of text
		}
		return txt;
	}

	function getCommaSeparatedVal(valObj, keyNameArr){
		var vals = "";
		for(val in valObj){
			vals += ", " + valObj[val][keyNameArr[0]];

			if(keyNameArr.length>1){
				for(i=1; i<keyNameArr.length; i++){
					vals += " " + valObj[val][keyNameArr[i]];
				}
			}
		}

		return vals.substring(2);
	}

	function getCommaSeparatedMultiLineVal(valObj, keyNameArr, isTooltip){
		var vals = "";

		if(isTooltip){
			for(val in valObj){
				vals += "\n" + valObj[val][keyNameArr[0]];
			}

			return vals.substring(2);
		}
		else{
			for(val in valObj){
				vals += "<br>" + valObj[val][keyNameArr[0]];
			}

			return vals.substring(4);
		}
		
	}

	function logout(){
		requestCall(uiConstants.common.SERVER_IP + "/logout", "PUT", "", "logoutUser", successCallbackLogout, errorCallbackLogout);
	}

	function downloadFile(filename, text) {
	    var pom = document.createElement('a');
	    document.body.appendChild(pom);
	    pom.setAttribute('href', 'data:text/plain;charset=utf-8,' + encodeURIComponent(text));
	    pom.setAttribute('download', filename);

	    if (document.createEvent) {
	        var event = document.createEvent('MouseEvents');
	        event.initEvent('click', true, true);
	        pom.dispatchEvent(event);
	    }
	    else {
	        pom.click();
	    }
	}

	window.unselectAllCheck = function(handleChkClick, chkboxCol, chkboxHeader){
		if(chkboxCol){
			$(chkboxCol).prop("checked",false);
		}

		if(chkboxHeader){
			$(chkboxHeader).prop("checked",false);
		}

		handleChkClick();
	}

	window.onerror = function(message, source, lineno, colno, error) {
	    console.log(source+":"+lineno+":"+colno+": "+message+": "+error);
	    removeLoadingContainer();
	};

	function getMasterList(objArr, objIdKey, selOptionArr, checkSelOptionId){
		if(checkSelOptionId){
			return $.grep(objArr, function(e){ return  e.status == 1 || selOptionArr.indexOf(e[objIdKey]) != -1; });
		}
		else{
			return $.grep(objArr, function(e){ return  e.status == 1; });
		}
	}

	function markInactiveOption(objArr, objIdKey, objNameKey, selOptionId){
		/*if(objArr.find( function( ele ) {return ele[objKey] && ele[objKey] == selOption && ele["status"] == 0;} )){
			
		}*/

		//$.grep(self.componentsArr(), function(e){ return e.componentTypeId == $("#"+self.modalCompType()+"compTypeList").val(); });
		inactiveOptionObj = $.grep(objArr, function(e){ return e[objIdKey] == selOptionId && e["status"] == 0; });

		if(inactiveOptionObj.length){
			inactiveOptionObj[0][objNameKey] = inactiveOptionObj[0][objNameKey] + " (Inactive)";
			return true;
		}
		return false;
	}

	function optionsCheckInactive(dataObj, configElement, configId, configLabel){
		//uncomment below block if the lable has to be shown in different style for inactive

		/*if(dataObj.find( function( ele ) {return ele[configId] == $(configElement).val() && ele.isActive != undefined;} )){
			$(configElement + "_chosen span").first().addClass("inactiveOptionClass");
		}
		else{
			$(configElement + "_chosen span").first().removeClass("inactiveOptionClass");
		}*/
	}

	/*function showErrorMessage(control, errorMessage){
		alert(control);
		$(control).addClass("aoneErrorMessage");
	}
*/
	var delay = (function(){
	  var timer = 0;
	  return function(callback, ms){
	    clearTimeout (timer);
	    timer = setTimeout(callback, ms);
	  };
	})();

	function requestCall(reqUrl, reqMethod, reqData, reqType, successCallBack, errorCallBack){
		if(uiConstants.common.DEBUG_MODE)console.log(reqUrl);
		if(uiConstants.common.DEBUG_MODE)console.log(reqData);
		if(uiConstants.common.DEBUG_MODE)console.log(reqType);
		if(uiConstants.common.DEBUG_MODE)console.log(localStorage.apptoken);

		if(reqMethod == "GET"){
			//lastAction = "";
		}
		else if(reqType != "authenticateUser" && (reqMethod == "POST" || reqMethod == "PUT")){
			window.lastAction = "addUpdate";
		}

		authToken = localStorage.apptoken;

		if(authToken == undefined)
			authToken = "";

		if(uiConstants.common.DEBUG_MODE)console.log("Active requests count:" + $.active);
		if(reqType != "logoutUser"){
			if(authToken == "" && hasherVar.getHash() != "" && nonTokenBasedPages.indexOf(hasherVar.getHash()) == -1){
				/*if(!($.active > 1)){
					removeLoadingContainer();
				}*/

				/*if(hasherVar.getHash() == "changePassword"){
					removeLoadingContainer();
					hasherVar.setHash("#");
				}

				else{*/
					uiConstants.common.FIRST_PAGE_AFTER_LOGIN = "#"+hasherVar.getHash();
					
					if($("#loginModal").css('display') == 'none'){
						handleInvalidToken(reqMethod);
					}
				//}
			}
			else{
				if(!window.koMenuPermissions() && nonMenuBasedRequests.indexOf(reqType) == -1){
					/*var urlHasher = "#"+hasherVar.getHash();

					//hasherVar.setHash("");
					hasherVar.setHash(urlHasher);
					return;*/

					if(reqType != null && reqType != "getMenuPermission"){
						showLoadingContainer();
						return;
					}

				}
				else{
					if(window.koMenuPermissions() && nonMenuBasedRequests.indexOf(reqType) == -1){
						if(hasherVar.getHash() != "globalSearch"){
							window.globalSearchTxt = "";
						}
						if(window.userName() != JSON.parse(atob(localStorage.apptoken.split(".")[1])).userName){
							//hasherVar.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
							location.reload();
						}

						var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == ("#"+hasherVar.getHash()); });
				     	if(optionPermissionsObj.length){
					    	window.commonAddEnabled(optionPermissionsObj[0].createEnabled);
					        window.commonUpdateEnabled(optionPermissionsObj[0].updateEnabled);
					        window.commonDeleteEnabled(optionPermissionsObj[0].deleteEnabled);
					    }
					   else if(nonPermisionBasedPages.indexOf(hasherVar.getHash()) == -1){
				        	showMessageBox("You do not have permission to access this page!", "error");
				        	hasherVar.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
					    }
					}

					console.log("###########");
					console.log(window.koMenuPermissions());

					if(nonMenuBasedRequests.indexOf(reqType) == -1){
						//var optionPermissionsObj = $.grep(window.koMenuPermissions(), function(evt){ return evt.accessLink == ("#"+hasherVar.getHash()); });
				  		//if(optionPermissionsObj && optionPermissionsObj.length){
							if(reqUrl.indexOf("?") == -1){
								reqUrl = reqUrl + "?page=" + hasherVar.getHash();
							}
							else{
								reqUrl = reqUrl + "&page=" + hasherVar.getHash();
							}


				  		//}
					}
				}
			}
		}
		

		if(authToken != "" || reqType == "authenticateUser" || reqType == "sendPasswdChangeEmail" || reqType == "changeUserPassword" || reqType == "forgotPassword" || reqType == "logoutUser"){ //check if the hash is for login page
			showLoadingContainer();
			$.ajax({              
				url: reqUrl,
				type: reqMethod,
				crossDomain: true,
				dataType: "json",
				data: reqData,
				headers: {
					"Authorization": authToken
				},
				success:
					function(data){
						try{
							//window.lastAction = "";
							/*if(!($.active > 1)){
								removeLoadingContainer();
							}*/

							if(uiConstants.common.DEBUG_MODE)console.log(data);
							if(uiConstants.common.DEBUG_MODE)console.log(reqType+":: "+JSON.stringify(data));
							if(reqType == "logoutUser"){
								logoutHandler();
							}
							else if(data.result == "InvalidToken"){
								localStorage.removeItem("apptoken");
							    window.koMenuPermissions(null);
								handleInvalidToken(reqMethod);
								$("#loginModal #errorMessage").text(uiConstants.common.INVALID_TOKEN_MSG);
			          		}
							else{

					 			successCallBack(data, reqType);
								uiConstants.common.USER_ROLE = JSON.parse(atob(localStorage.apptoken.split(".")[1])).userRole;
					 			
								//hasherVar.setHash("#changePassword");
					 		}
				 		}
				 		catch(err){
				 			console.log("Error:");
				 			console.log(err)
				 		}
					},
				error:
					function(x, t, m) {
						try{
							//window.lastAction = "";
							/*if(!($.active > 1)){
								removeLoadingContainer();
							}*/
			          		if(uiConstants.common.DEBUG_MODE)console.log(x);
			          		if(uiConstants.common.DEBUG_MODE)console.log(m);
			          		if(uiConstants.common.DEBUG_MODE)console.log(t);

			          		if(x.status == 401){
			          			localStorage.removeItem("apptoken");
							    window.koMenuPermissions(null);

								if(reqType == "logoutUser"){
									logoutHandler();
								}
								else if(nonTokenBasedPages.indexOf(hasherVar.getHash()) == -1){
									handleInvalidToken(reqMethod);
									console.log($("#loginModal #errorMessage"));

									$("#loginModal #errorMessage").text(uiConstants.common.INVALID_TOKEN_MSG);
								}
			          		}
			          		else{
			          			errorCallBack(reqType);
			          		}
			          	}
				 		catch(err){
				 			console.log("Error:");
				 			console.log(err)
				 		}
		          	}
			});
		}
		$(".mandatoryField").css("display", window.currentViewIndex() == uiConstants.common.READ_VIEW ? 'none' : '');
	};

	function showLoadingContainer(){
		$(".loadingContainer").css('display','block');
		$("#page").addClass('disabled');
	}

	
	function removeLoadingContainer(){
		$(".loadingContainer").css('display','none');
		$("#page").removeClass('disabled');
	}

	function downloadAgentPropertiesConfig(agentObj){

		if(agentObj == null){
			agentObj = window.agentConfDownloadData;
		}
		if(slugify(agentObj["agentType"]) == "psagent"){
				var confPropertiesContent = "# PSAgent defaults to using JSON as the config protocol sent from appsone. Uncomment the"
				+"\n# following line if it is using XML, \"properties\" is also supported"
				+"\n#configserver.output.protocol = xml"
				+"\n\n\nagent.uniqueid = " + agentObj.identifier

				+"\n\nconfigserver.protocol = " + location.protocol.split(":")[0]
				+"\nconfigserver.ip = " + location.hostname
				+"\nconfigserver.port = " + (location.port || "")
				+"\nconfigserver.url = /txn/AppsOnePSConfigService"
				 
				+"\n\n\nhealthserver.url = /txn/AppsOneAgentHealthService"
				 
				+"\n\nhttp.proxy.status = " + (agentObj.httpProxy == 1 ? "true" : "false")
				+"\nhttp.proxy.ip = " + agentObj.psAgentParam.httpProxyHost
				+"\nhttp.proxy.port = " + agentObj.psAgentParam.httpProxyPort
				+"\nhttp.connection.timeout = 86400";

				downloadFile("basic.properties", confPropertiesContent);
		}
		else{
			var configServerUrl = location.protocol + "//" + location.hostname + (location.port ? ":"+location.port: "");
			var dataServerUrl = "";

			if(agentObj.collectionAgentParam && agentObj.collectionAgentParam.dataProtocol){
				if(slugify(agentObj.collectionAgentParam.dataProtocol) == "grpc"){
					dataServerUrl = "grpc:" + agentObj.collectionAgentParam.dataHost + ":" + agentObj.collectionAgentParam.dataPort;
				}
				else if(slugify(agentObj.collectionAgentParam.dataProtocol) == "http"){
					dataServerUrl = agentObj.collectionAgentParam.dataUrl;
				}

				var confPropertiesContent = "#########################################################\n" +
				"#### Configuration Parameters of Component Agent ###\n" +
				"#########################################################\n\n" +
				"# Set the below property value to unique ID which is generated by dashboard for the data collector agent.\n" +
				"component.agent.uniqueid=" + agentObj.identifier + "\n\n" +
				"# Configuration server host URL\n" +
				"component.agent.configserver.url=" + configServerUrl;

				downloadFile("basic.properties", confPropertiesContent);
			}
		}
	}

	function downloadAgentDetailsJson(agentUniqueId){
		requestCall(uiConstants.common.SERVER_IP + "/agents/identifier/" + agentUniqueId, "GET", "", "", agentDetSuccessCallBack, errorCallback);
	}

	function validateLogin(textUsername, textPassword, errorMessage) {
		txtUserName = textUsername;
		txtPassword = textPassword;
		loginError = errorMessage;
		isSessionTimeout = false;

		$(loginError).text("");

		if($(txtUserName).val() == ""){
			$(loginError).text(uiConstants.login.USER_NAME_REQUIRED);
			$(txtUserName).focus();
		}
		else if($(txtPassword).val() == ""){
			$(loginError).text(uiConstants.login.PASSWORD_REQUIRED);
			$(txtPassword).focus();
		}
		else{
			$(txtUserName).focus();

			if(localStorage.apptoken){
				localStorage.removeItem("apptoken");
				window.koMenuPermissions(null);
			}
			// generate 128 bit IV
			var ivStr = getRandomString();
			var loginData = {
				'userName': $(txtUserName).val(),
				'password': getEncodedText(ivStr, $(txtPassword).val()),
				'initializationVector':ivStr,
				'allowLogin': allowLogin
			};

			if($("#loginModal").css('display') != 'none'){
				zIndexloginModal = $(".modal").css("z-index");
				$("#loginModal").css("z-index",0);
			}

			requestCall(uiConstants.common.SERVER_IP + "/user/authenticate", "POST", JSON.stringify(loginData), "authenticateUser", successCallback, errorCallback);
		}
	}

	/*function validateSession(val){
		setTimeout(function(){alert(val);}, 3000);
	}*/

	/*$(document).ready(function(){
		validateSession("validate2");
	});*/

	function onSideMenuClick(indx){
		$(".sideMenuClass").removeClass("active");
      	$("#sideMenu"+indx).removeClass("active").addClass("active");
    }

	function loadPageAfterLogin(data){
		allowLogin = 0;
		idleTimeoutMilli = data.idleTime * 60000;

	    localStorage.timeout = idleTimeoutMilli;
	  	//idleInterval = setTimeout(handleTimeout, idleTimeoutMilli);

	  	if((idleTimeoutMilli - uiConstants.common.IDLE_WARNING_TIME_MILLI) > 0){
	  		idleTimeoutMilli = idleTimeoutMilli - uiConstants.common.IDLE_WARNING_TIME_MILLI;
	  	}
	  	//idleTimeoutMilli = idleTimeoutMilli
	  	startIdleHandler();

	    localStorage.apptoken = data.authToken;
	    var authTokenPayload = JSON.parse(atob(localStorage.apptoken.split(".")[1]));
	    uiConstants.common.USER_ROLE = authTokenPayload.userRole;
		uiConstants.common.USER_NAME = authTokenPayload.userName;
		uiConstants.common.USER_ID = authTokenPayload.userId;
		uiConstants.common.FORCE_PASSWORD_CHANGE = authTokenPayload.forcePasswordChange;

		if(uiConstants.common.DEBUG_MODE)console.log(authTokenPayload);
	    if(uiConstants.common.DEBUG_MODE)console.log(uiConstants.common.USER_ROLE);
	    if(uiConstants.common.DEBUG_MODE)console.log(idleTimeoutMilli);

	    var isModal = false;
	    if($("#loginModal").css('display') != 'none'){
	    	isModal = true;
			$("#loginModal").modal("hide");
		}

		//if(data.forcePasswordChange){
			//hasherVar.setHash("#changePassword");
			//debugger;
			/*tempAuthToken = localStorage.apptoken;
			if(localStorage.apptoken){
	        	localStorage.removeItem("apptoken");
			}*/

			//return;
		//}



		if(uiConstants.common.FORCE_PASSWORD_CHANGE == 0){
			//Showing top and side bar
		    $("#slide-nav").css("visibility","visible"); //top-bar
		    //$("#side-nav").css("visibility","visible"); //side-bar	

		    console.log("********************");

		    if(uiConstants.common.FIRST_PAGE_AFTER_LOGIN != ""){
		    	if(!isModal || window.lastAction!="addUpdate"){
		   			//alert(lastAction);

					hasherVar.setHash('');
			    	hasherVar.setHash(uiConstants.common.FIRST_PAGE_AFTER_LOGIN);
			    }
			    /*else{
			    	lastAction = "";
			    }*/
		    	isModal = false;

		    	//To remove modal backdrop fade in case of login modal is shown on top of another modal
		    	$("div.modal-backdrop.fade.in").removeClass("modal-backdrop fade in");
		    }

		    uiConstants.common.FIRST_PAGE_AFTER_LOGIN = "#home";

		    //validateSession("validate1");
		    commonMenuFunc();
		}
		else{
            hasherVar.setHash("#changePassword");
		}
	}

	window.outsideClickListener = function(event) {
		debugger;
		if (!$(event.target).closest("#addOptionsList").length) {
		  if ($(".addOptionsContainer").is(':visible')) {
		    $(".addOptionsContainer").hide();
		    removeClickListener();
		  }
		}
	};

	var removeClickListener = function removeClickListener() {
		document.removeEventListener('click', window.outsideClickListener);
	};


	function loginHandler(data){
	  if(uiConstants.common.DEBUG_MODE)console.log(data);
	  allowLogin = 0;
	  $(loginError).text("");

	  if(data.result){

	  	if(!data.authToken){
	  		showMessageBox(uiConstants.login.CONFIRM_LOGIN_WITH_OTHER_SESSION_ACTIVE, "question", "confirm", function confirmCallback(confirmLogin){
		     	if(confirmLogin){
		     		allowLogin = 1;
		          	$("#btnLogin").trigger("click");
		     	}
		    });
	  	}
	  	else{
	  		$(txtUserName).val("");
	  		$(textPassword).val("");

		  	//koLoginData(null);
		  	if(data.promptPasswordExpiry){
		  		showMessageBox(uiConstants.login.CONFIRM_PASSWORD_CHANGE.replace('%s', data.remainingDays), "question", "confirm", function confirmCallback(confirmPasswdChange){
		          if(confirmPasswdChange){
		            //check this later
		            //koLoginData(data);
		            hasherVar.setHash("#changePassword");
					location.reload();

		          }
		          else{
		          	loadPageAfterLogin(data);
		          }
		        });
		  	}
		  	else{
		        loadPageAfterLogin(data);
		  	}
	  	}
	  }
	  else{
	  	$(txtUserName).val("");
	  	$(textPassword).val("");

	  	if(!data.status){
	  		if(data.remainingAttempts){
	   			$(loginError).text(uiConstants.login.INVALID_USERNAME_PASSWORD + (data.remainingAttempts ? " (" + uiConstants.login.LOGIN_ATTEMPTS_LEFT + data.remainingAttempts + ")" : ""));
	  		}
	  		else{
	  			$(loginError).text(uiConstants.login.INVALID_USERNAME_PASSWORD);
	  		}
	  	}
	  	else{
	  		$(loginError).text(data.message);
	  	}

	  	/*if(data.failureType){
	  		if(slugify(data.failureType) == "credentials"){
	   			$(loginError).text(uiConstants.login.INVALID_USERNAME_PASSWORD + (data.loginAttemptsLeft ? " (" + uiConstants.login.LOGIN_ATTEMPTS_LEFT + data.loginAttemptsLeft + ")" : ""));
	  		}
	  		else if(slugify(data.failureType) == "inactive"){
	   			$(loginError).text(uiConstants.login.USER_ACCOUNT_INACTIVE);
	  		}
	  		else if(slugify(data.failureType) == "accountlocked"){
	   			$(loginError).text(uiConstants.login.USER_ACCOUNT_LOCKED);
	  		}
	  		//else if(slugify(data.failureType) == "passwordvalidity"){
	   		//	$(loginError).text(uiConstants.login.USER_PASSWORD_EXPIRED);
	  		//}
	  	}
	  	else{
	  		$(loginError).text(data.message);
	  	}*/
	  	
	  }
	}

	function handleForgotPassword(){
		$("#forgotPasswordModal").modal("show");
	}

	function showMessageBox(msg, msgType, msgBoxType, confirmCallback){ //default message sign is info
		if(msgType == undefined){
			msgType = "info";
		}

		if(msgBoxType == undefined){
			msgBoxType = "alert";
		}

		window.confirmCallback = confirmCallback;
		$("#messageDialogBox").modal("show");
		$("#messageDialogBox #messageCont").html(msg.replace(/\n/g,'<br>'));
		$("#okBtn").css("display", "none");
		$("#yesBtn").css("display", "none");
		$("#noBtn").css("display", "none");

		if(msgBoxType == "alert"){
			$("#okBtn").css("display", "inline-block");
		}
		else if(msgBoxType == "confirm"){
			$("#yesBtn").css("display", "inline-block");
			$("#noBtn").css("display", "inline-block");
		}

		if(msgType.toLowerCase() === "info"){
			$("#msgSign").removeClass().addClass("glyphicon glyphicon-info-sign");
			$("#msgSign").css("color", "#2E5A80");
		}
		else if(msgType.toLowerCase() === "error"){
			$("#msgSign").removeClass().addClass("glyphicon glyphicon-exclamation-sign");
			$("#msgSign").css("color", "#AB0000");
		}
		else if(msgType.toLowerCase() === "question"){
			$("#msgSign").removeClass().addClass("glyphicon glyphicon-question-sign");
			$("#msgSign").css("color", "#ADAD00");
		}


	}



	$('#messageDialogBox').on('shown.bs.modal', function () {
			if($("#okBtn").css("display").toLowerCase() != "none"){
				$("#okBtn").focus();
			}
			else{
				$("#noBtn").focus();
			}
		});

	function onConfirmYes(){
		window.confirmCallback(true);
	}

	function onConfirmNo(){
		window.confirmCallback(false);
	}

	function agentDetSuccessCallBack(data, reqType) {
		downloadFile("agent-details.json", JSON.stringify(data.result, null, "\t"));
	}

	function successCallback(data, reqType) {
	  if(uiConstants.common.DEBUG_MODE)console.log("Response---->");
	  if(uiConstants.common.DEBUG_MODE)console.log(reqType);
	  if(uiConstants.common.DEBUG_MODE)console.log(data);
	  if($("#loginModal").css('display') != 'none'){
		$("#loginModal").css("z-index",zIndexloginModal);
	  }
	  loginHandler(data);
	}

	function errorCallback(data, reqType) {
	  if($("#loginModal").css('display') != 'none'){
		$("#loginModal").css("z-index",zIndexloginModal);
	  }
	  alert(uiConstants.login.ERROR_AUTHENTICATE_USER);
	}

	function logoutHandler(){
		if(localStorage.apptoken){
	        localStorage.removeItem("apptoken");
	    }
	    window.clearTimeout(idleTimer);
	    window.koMenuPermissions(null);
	    idleTimeoutMilli = 0;
	    localStorage.removeItem("timeout");
	    hasherVar.setHash("#");
	    //console.log("here it has come");
	    $("#sideMenu").css("visibility","hidden");
	}

	function successCallbackLogout(data, reqType) {
	  console.log("user logged out");
	  logoutHandler();
	}

	function errorCallbackLogout(data, reqType) {
	  console.log("error in user log out");
	  logoutHandler();
	}

	function successLastUsedCallback(data, reqType) {
	  if(uiConstants.common.DEBUG_MODE)console.log(uiConstants.common.SUCCESS_TOKEN_LAST_USED_UPDATE);
	}

	function errorLastUsedCallback(data, reqType) {
	  if(uiConstants.common.DEBUG_MODE)console.log(uiConstants.common.ERROR_TOKEN_LAST_USED_UPDATE);
	}

	/*Dashboard related common methods Start*/
	function podIconValidation(podType, iconType){
		/*Vaidation for pod is support for fullscreen, Grid-Graph switch view, Dc-Eum switch features
			parameters:-
			podType :- pod type defined by server
			iconType :- type of icon
		*/

		var flag = false;
		var searchArray = [];
		if(iconType === "fullScreen") searchArray = fullScreenPodList;
		else if(iconType === "DcEum") searchArray = dcEumPodList;

		$.each(searchArray, function( index, item ) {
		   if(item.type === podType){flag = true;	return; }
		});
		if(uiConstants.common.DEBUG_MODE) console.log(flag);
		return flag;
	}


	function JSONToCSVConvertor(JSONData, ReportTitle, FileName, ShowLabel) {
	/*Parameters:-
	    JSONData :- Dataset convert to csv file
	    ReportTitle :- User Defined ReportTitle
	    ShowLabel :- Boolean value True/False (Need to check)
	*/


	//If JSONData is not an object then JSON.parse will parse the JSON string in an Object
	    var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
	    
	    var CSV = '';    
	    //Set Report title in first row or line
	    
	    CSV += ReportTitle + '\r\n\n';

	    //This condition will generate the Label/Header
	    if (ShowLabel) {
	        var row = "";
	        
	        //This loop will extract the label from 1st index of on array
	        for (var index in arrData[0]) {
	            
	            //Now convert each value to string and comma-seprated
	            row += index + ',';
	        }

	        row = row.slice(0, -1);
	        
	        //append Label row with line break
	        CSV += row + '\r\n';
	    }
	    
	    //1st loop is to extract each row
	    for (var i = 0; i < arrData.length; i++) {
	        var row = "";
	        
	        //2nd loop will extract each column and convert it in string comma-seprated
	        for (var index in arrData[i]) {
	            row += '"' + arrData[i][index] + '",';
	            
	        }

	        row.slice(0, row.length - 1);
	        
	        //add a line break after each row
	        CSV += row + '\r\n';
	    }

	    if (CSV == '') {        
	        alert("Invalid data");
	        return;
	    }   
	    
	    //Generate a file name
	    var fileName = FileName;
	    //this will remove the blank-spaces from the title and replace it with an underscore
	    fileName += ReportTitle.replace(/ /g,"_");   
	    
	    //Initialize file format you want csv or xls
	    var uri = 'data:text/csv;charset=utf-8,' + escape(CSV);
	    
	    // Now the little tricky part.
	    // you can use either>> window.open(uri);
	    // but this will not work in some browsers
	    // or you will not get the correct file extension    
	    
	    //this trick will generate a temp <a /> tag
	    var link = document.createElement("a");    
	    link.href = uri;
	    
	    //set the visibility hidden so it will not effect on your web-layout
	    link.style = "visibility:hidden";
	    link.download = fileName + ".csv";
	    
	    //this part will append the anchor tag and remove it after automatic click
	    document.body.appendChild(link);
	    link.click();
	    document.body.removeChild(link);
	}

	/*Dashboard related common methods end*/

	/*Error code handling*/
	function handleServiceErrorMsgs(pageName,errorCode,errorMsg){
		console.log(pageName);
		var customMsg="";
		switch(errorCode){
			case "E2009":customMsg = pageName+uiConstants.common.NAME_EXISTS;
						 break;
			default:customMsg=errorMsg;
		}
		return customMsg;
	}

	function insertAtCursor(ele, val) {
	    //IE support
	    if (document.selection) {
	        ele.focus();
	        sel = document.selection.createRange();
	        sel.text = val;
	    }
	    //MOZILLA and others
	    else if (ele.selectionStart || ele.selectionStart == '0') {
	        var startPos = ele.selectionStart;
	        var endPos = ele.selectionEnd;
	        ele.value = ele.value.substring(0, startPos)
	            + val
	            + ele.value.substring(endPos, ele.value.length);
	        ele.selectionStart = startPos + val.length;
	        ele.selectionEnd = startPos + val.length;
	    } else {
	        ele.value += val;
	    }
	}