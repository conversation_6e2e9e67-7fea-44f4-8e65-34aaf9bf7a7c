//name field validation using REGEX
nameValidation = function(name){
	if(name.match("^.*[a-zA-Z].*$")){ //First check if aleast one alphabet exists
		if (name.match("^(?!.*  )(?!.*__)(?!.*--)[a-zA-Z0-9\-_\ ]{2,45}$")) {
	    	return 1;
		}else{
			return 0;
		}
	}
	else{
		return 0;
	}
	
}

//tag field validation using REGEX
tagValidation = function(tag){
	if(tag.match("^.*[a-zA-Z].*$")){ //First check if aleast one alphabet exists
		if (tag.match("^(?!.*  )(?!.*__)(?!.*--)[a-zA-Z0-9\-_\ ]{2,45}$")) {
		    return 1;
		}else{
			return 0;
		}
	}
	else{
		return 0;
	}
}

//tag field validation for add multiple entities using REGEX
tagValidationWithComma = function(tag){
	if(tag.match("^.*[a-zA-Z].*$")){ //First check if aleast one alphabet exists
		if (tag.match("^(?!.*  )(?!.*__)(?!.*--)[a-zA-Z0-9\-,_\ ]{2,45}$")) {
		    return 1;
		}else{
			return 0;
		}
	}
	else{
		return 0;
	}
}


//trim spaces at begining and end of the string and replaces underscore for between spaces
trimSpacesReplaceUnderscore = function(str) {
    str = str.trim();
    str = str.replace(/\s+/g,'_');
    return str;
}

//trim spaces at begining and end of the string and replaces single space for name validation
trimSpacesReplaceSingleBlankspace= function(str) {
    str = str.trim();
    str = str.replace(/\s+/g,' ');
    return str;
}

//email validation using REGEX
emailValidation = function(emailStr){
	var filter = /^[\w\-\.\+]+\@[a-zA-Z0-9\.\-]+\.[a-zA-z0-9]{2,3}$/;
 	return filter.test(emailStr);
}

//multiple emailValidation using REGEX
multipleEmailValidation = function(emailsStr){
	var filter = /^((\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)|(['\&quot;][^\<\>'\&quot;]*['\&quot;]\s*\<\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*\>))(,\s*((\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*)|(['\&quot;][^\<\>'\&quot;]*['\&quot;]\s*\<\w+([-+.']\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*\>)))*$/;
	return filter.test(emailsStr);
}

//multiple phone number validation
multiplePhoneValidation = function(phoneNumber){
	var filter = /^\d{10}(,\d{10})*$/;
	return filter.test(phoneNumber);
}

userNameValidation = function(name){
	var filter = /^(?!.*\.\.)(?!.*__)(?!.*--)[a-zA-Z0-9\-_\.]{5,10}$/;
 	return filter.test(name);
}

passwordValidation = function(password){
	var filter = /^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])(?=.*[^a-zA-Z0-9])(?!.*\s).{8,15}$/;
	return filter.test(password);
}

hostIpValidation = function(val){
	if(!isNaN(val.split(".").join(""))){ //check if val contains only numbers delimited by dot; if yes, then user has entered an IP address
		return ipAddressValidation(val);
	}
	else{
		return hostAddressValidation(val);
	}
}

ipAddressValidation = function(val, hostValidationRequired){
	var filter = /^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])$/;
	return filter.test(val);
}

hostAddressValidation = function(val){
	var filter = /^(?=.{1,253}$)[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?(?:\.[0-9A-Za-z](?:(?:[0-9A-Za-z]|-){0,61}[0-9A-Za-z])?)*\.?$/;
	return filter.test(val);
}