/*	Object contains application specific constants in nested level
*
*/
var uiConstants = {

		common:{
			SERVER_IP : "/v1.0/ui",
			//SERVER_IP : "https://*************:9191/v1.0/ui",

			COMMON_VALUE_BGCOLOR : "white",
			UNCOMMON_VALUE_BGCOLOR : "#7AC6EF", //lightgrey
			BTSWITCH_UNCOMMON_COLOR : "default",
			FIRST_PAGE_AFTER_LOGIN : "#home",
			SECRETE_KEY : "ErmLbkvWzYyKnJYZcX1Rra1dgE2Ud+ligErT8B4KH2A=",
			DEBUG_MODE : true,
			LIST_VIEW : 0,
			ADD_SINGLE_VIEW : 1,
			ADD_MULTIPLE_VIEW : 2,
			EDIT_VIEW : 3,
			CLONE_VIEW : 4,
			READ_VIEW : 5,
			KPI_PRODUCER_MAP : 6,
			START_WIZARD : 7,
			EDIT_WIZARD_WITH_APPLICATION : 8,
			USER_NAME : "",
			USER_ID : "",
			USER_ROLE : "",
			FORCE_PASSWORD_CHANGE : 0,
			IDLE_WARNING_TIME_MILLI : 120000, //Show warning message before loggin out in 2 minutes

			MAX_PORT_RANGE : 65535,
			CONST_APPLICATION : "Application",
			CONST_APPLICATION_LINK : "#listGridView",
			CONST_AGENT : "Agent",
			CONST_AGENT_LINK : "#agentListView",
			CONST_TRANSACTION : "Transaction",
			CONST_TRANSACTION_LINK : "#transactionListView",
			CONST_COMPTYPE : "Component Type",
			CONST_COMPTYPE_LINK : "#compTypeListView",
			CONST_APPTYPE : "Application Type",
			CONST_APPTYPE_LINK : "#appTypeAddEdit",
			CONST_COMPONENT : "Component",
			CONST_COMPONENT_LINK : "#componentListView",
			CONST_COMPINST : "Component Instance",
			CONST_COMPINST_LINK : "#componentInstanceListView",
			CONST_KPI : "KPI",
			CONST_PRODUCER : "Producer",
			CONST_CLUSTER : "Cluster",
			CONST_CORE : "Core",
			STR_AVAILABILITY : "Availability",
			CONST_CONFIGWATCH : "ConfigWatch",
			CONST_FILEWATCH : "FileWatch",
			CONST_TEXT : "Text",
			CONST_NONE : "None",
			CONST_USER_ROLE : "User Role",
			CONST_USER_PROFILE : "User Profile",
			CONST_CHANGE_PASSWORD : "User Profile",
			CONST_GLOBAL_SETTINGS : "Global Settings",
			WIZARD_POPUP_TITLE : "Agent Mapping",
			CONST_NOTIFICATION_CONTENT : "Notification Content Profile",
			CONST_NOTIFICATION_CONTENT_LINK : "#notificationContentListView",
			CONST_ALERT_PROFILE : "Alert Profile with Threshold",
			CONST_ALERT_PROFILE_LINK : "#alertProfileMainListView",
			CONST_SEVERITY_PROFILE : "Severity Profile",
			CONST_SEVERITY_PROFILE_LINK : "#severityProfileListView",
			CONST_TIME_PROFILE : "Time Profile",
			CONST_TIME_PROFILE_LINK : "#timeProfileListView",
			CONST_KPI_GROUP : "KPI Group",
			CONST_KPI_GROUP_LINK : "#kpiGroupListView",
			CONST_ESCALATION_PROFILE : "Escalation Profile",
			CONST_ESCALATION_PROFILE_LINK : "#escalationProfileListView",
			CONST_GRPC_SETTINGS : "GRPC Settings",

			//Common messages
			//ERROR_GET_APPLICATION_TYPES : "Error in getting application types!", // Commented as this is already there under applicationType
			ERROR_GET_COMPONENT_TYPES : "Error in getting component types!",
			ERROR_GET_TIMEZONES : "Error in getting timezones!",
			ERROR_GET_MAINTENANCE_PROFILES : "Error in getting maintenance profiles!",
			ERROR_GET_APPLICATION_TAGS : "Error in getting application tags!",
			INVALID_TIMEZONE : "Please select a valid Time Zone from the list.",
			INVALID_MAINTENANCE_PROFILE : "Please select a valid Maintenance Profile from the list",
			DUPLICATE_TAGS : "Duplicate Tags not allowed",
			TAG_MIN_LENGTH_ERROR : "Tag should at least contain 2 characters",
			TAG_MAX_LENGTH_ERROR : "Tag should not exceed 45 characters",
			INVALID_TAG_NAME : "Tags should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '_' or space",
			APPLICATION_TYPE_REQUIRED : "Application Type is required",
			COLUMNS_MISMATCH : "The number of columns in the source and destination do not match.",
			ROW_LIMIT_EXCEEDED : "You have exceeded the maximum permitted limit of 1000 rows.",
			RECORDS_PASTE_LIMIT_EXCEEDED : "Maximum records(1000) paste limit exceeded.",
			ERROR_LAST_ROW_DELETE : "You cannot delete last row.",
			REQUEST_RESPONSE_RECORDS_MISMATCH : "Request and Response record counter are not matched.",
			DESCRIPTION_MIN_LENGTH_ERROR : "Description should contain atleast 25 characters",
			DESCRIPTION_MAX_LENGTH_ERROR : "Description should not exceed 256 characters",
			SELECT_COMPONENT_TYPE : "Select Component Type",
			SELECT_COMPONENT_AGENT : "Select Component Agent",
			SELECT_COMPONENT_NAME : "Select Component",
			SELECT_CLUSTER_NAME : "Select Cluster",
			SELECT_APPLICATION : "Select Application",
			ENTER_SELECT_COMPONENT_NAME : "Enter/Select Component Name",
			ENTER_SELECT_VERSION : "Enter/Select Component Version",
			SELECT_COMPONENT_TYPE_MSG : "Please select Component Type",
			ENTER_SELECT_COMPONENT_NAME_MSG : "Please enter/select Component Name",
			ENTER_COMPONENT_NAME_MSG : "Please enter Component Name",
			ENTER_SELECT_VERSION_MSG : "Please enter/select Component Version",
			ADD_COMPONENT_ASSOCIATION : "Please select atleast one component/version",
			IDLE_LOGOUT_MSG : "You have been logged out of session due to idle state!",
			SUCCESS_TOKEN_LAST_USED_UPDATE : "Updated last token accessed time successfully",
			ERROR_TOKEN_LAST_USED_UPDATE : "Error in updating last token accessed time",
			ENTER_SELECT_ATTRIB_LIST : "Enter/Select Attribute Name",
			ENTER_SELECT : "Enter/Select",
			ENTER_SELECT_ATTRIB_LIST_MSG : "Please enter/select Attribute Name",
			SELECT_KPI : "Select KPI",
			SELECT_KPI_TYPE : "Select KPI Type",
			SELECT_KPI_TYPE_MSG : "Please select KPI Type",
			SELECT : "Select",
			SELECT_PRODUCER_TYPE_MSG : "Please select Producer Type",
			ERROR_GET_COMPONENTS : "Error in getting components!",
			ERROR_GET_COMP_TYPE_VERSION : "Error in getting component type/name/version",
			ERROR_GET_APP_COMP_TYPE_VERSION : "Error in getting component type/name/version for application type",
			//ERROR_GET_APPLICATIONS : "Error in getting applications", // commented as this constant already exists in applicationConfig
			SELECT_COMPONENT_MSG : "Please select Component",
			SELECT_COMPONENT_VERSION_MSG : "Please select Component Version",
			SELECT_APPLICATION_MSG : "Please select Application",
			SELECT_APPLICATION_MULT_OPTION_MSG : "Please select Application(s)",
			ADD_TO_AVAILABLE_LIST : "Add to available list",
			ADD_TO_SELECTED_LIST : "Add to selected list",
			VIEW_CLUSTER_OPERTATION : "View Cluster Operation",
			ERROR_GET_CLUSTERS : "Error in getting clusters!",
			ERROR_GET_TXNS : "Error in getting transactions!",
			SELECT_COMPONENT_VERSION : "Select Component Version",
			NAME_REQUIRED : "Name is required",
			NAME_MIN_LENGTH_ERROR : "Name should at least contain 2 characters",
			NAME_MAX_LENGTH_ERROR : "Name should not exceed 45 characters",
			NAME_INVALID_ERROR : "Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			DESCRIPTION_REQUIRED : "Description is required",
			SELECT_PRODUCER_TYPE : "Select Producer Type",
			ERROR_GET_PRODUCER_TYPES : "Error in getting producer types",
			ERROR_GET_KPIS : "Error in getting KPI list",
			ERROR_GET_KPI_TYPES : "Error in getting KPI Types",
			ERROR_GET_PARAMETER_TYPES : "Error in getting Parameter Types",
			ERROR_GET_QUERY_RESULTS : "Error in getting Query Results",
			ERROR_GET_ATTRIB_TYPES : "Error in getting Attribute Types",
			ERROR_GET_SERVER_TYPES : "Error in getting Server Types",
			SELECT_VERSIONS : "Please select version(s)",
			SELECT_KPIS : "Please select KPI(s)",
			CELL_VALUES_SEPERATION : "Cell values should not be separated by newline('\\n')",
			SEARCH : "Search",
			NOT_APPLICABLE : "--NA--",
			SELECT_COMPONENT_INSTANCES_MSG : "Please select Component Instance(s)",
			SELECT_APP_COMP_INT_MSG : "Please add Application(s) & Component Instance(s)",
			ERROR_FIELD_REQUIRED : "Field value required",
			INVALID_TOKEN_MSG : "You have been logged out due to invalid session. Please re-login to continue.",
			PER_PAGE : "Rows Per Page",
			NO_RESULTS_FOUND : "No results found...",
			CONST_FAILURE : "failure",
			CONST_SUCCESS : "success",
			NAME_EXISTS : " Name already exists",
			CONST_CANCEL : "Cancel",
			CONST_CLOSE : "Close",
			ERROR_GET_APPLICATIONS: "Error in getting applications",
			SELECT_USER_ROLE : "Select User Role",
			PHONE_COUNTRY_CODE_REQUIRED : "Please enter country code for Phone number",
			PHONE_NUMBER_REQUIRED : "Please enter Phone number",
			INVALID_EMAIL_ID : "Please enter valid Email ID",
			EMAIL_ID_REQUIRED : "Email ID is required",

			ERROR_GET_HOSTS : "Error in getting hosts!",
			SELECT_HOST : "Select Host",
			SELECT_HOST_MSG : "Please select Host",
			ENTER_HOST_ADDRESS_MSG : "Please enter Host Address",

			CREATE_APPLICATION_MSG : "Please create application before navigating to other tabs",

			CONFIRM_PAGE_EXIT : "Navigating away from current page may result in loss of unsaved data. Do you want to continue?",
			TAGS_LISTS_ENDS_WITH_COMMA_ERROR: "Tag(s) specified should not end with comma",
			DENY_INACTIVE_CONFIG_CLONE : "Inactive %s cannot be clonned",
			CONFIRM_PAGE_NAVIGATION : "Any unsaved data will be lost if you navigate away from current configuration page. Do you want to continue?",
			CONFIRM_CLEAR_FILTER : "Are you sure you want to clear the filter?"
		},

		//Application Type configuration messages
		applicationType:{
			APPLICATION_TYPE_NAME_INVALID_ERROR : "Application Type Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ERROR_GET_APPLICATION_TYPES : "Error in getting application type details!",
			ERROR_APPLICATION_TYPE_STATUS_CHANGE : "Error in changing application type status!",
			APPLICATION_TYPE_NAME_REQUIRED : "Application Type Name is required",
			APPLICATION_TYPE_NAME_MIN_LENGTH_ERROR : "Application Type Name should at least contain 2 characters",
			APPLICATION_TYPE_NAME_MAX_LENGTH_ERROR : "Application Type Name should not exceed 45 characters",
			ERROR_ADD_APPLICATION_TYPE : "Error in adding application type!",
			SUCCESS_ADD_APPLICATION_TYPE : "Application Type added successfully.",
			SUCCESS_UPDATE_APPLICATION_TYPE : "Application Type updated successfully.",
			ERROR_UPDATE_APPLICATION_TYPE : "Error in updating application type!",
			CONFIRM_COMP_ASSOCIATION_DELETE : "Are you sure you want to delete a component association?",
			APPLICATION_TYPE_LISTS_NOT_CONFIGURED : "Application types not configured",
			DENY_STANDARD_APP_TYPE_EDIT : "Standard application types cannot be edited",
			ERROR_GET_APPLICATION_TYPES : "Error in getting application types!"
		},

		//Login messages
		login:{
			INVALID_USERNAME_PASSWORD : "Invalid User Name or Password!",
			ERROR_AUTHENTICATE_USER : "Error in authenticating user!",
			USER_NAME_REQUIRED : "User name is required",
			PASSWORD_REQUIRED : "Password is required",
			DESC_REQUIRED : "Description is required",
			LOGIN_TITLE: "Member Login",
			FORGOT_PASSWORD_TITLE: "Reset Password",
			CHANGE_PASSWORD_TITLE: "Change Password",
			ERROR_SENDING_PASSWD_RESET_EMAIL: "Error in sending email for resetting password",
			PASSWORD_RESET_EMAIL_SENT: "A temporary password has been sent to your email address",
			ERROR_CHANGE_USER_PASSWORD: "Error in changing user password!",
			SUCCESS_CHANGE_USER_PASSWORD: "User Password changed successfully.",
			CONFIRM_LOGIN_WITH_OTHER_SESSION_ACTIVE: "Another session with same user already exists. Do you want to continue & terminate existing session?",
			LOGIN_ATTEMPTS_LEFT: "Remaining Attempts: ",
			CONFIRM_PASSWORD_CHANGE: "Your password is about to expire in %s day(s). Do you want to change it now?"
		},


		//Application configuration messages
		applicationConfig:{
			ERROR_GET_APPLICATIONS : "Error in getting application details!",
			SUCCESS_APPLICATION_STATUS_CHANGE : "Application status changed successfully.",
			ERROR_APPLICATION_STATUS_CHANGE : "Error in changing application status!",
			ERROR_ADD_APPLICATION : "Error in adding application!",
			SUCCESS_ADD_APPLICATION : "Application added successfully.",
			SUCCESS_UPDATE_APPLICATION : "Application updated successfully.",
			SUCCESS_MULTIPLE_UPDATE_APPLICATION : "Applications updated successfully.",
			ERROR_UPDATE_APPLICATION : "Error in updating application!",
			ERROR_MULTIPLE_UPDATE_APPLICATION : "Error in updating applications!",
			SUCCESS_MULTIPLE_ADD_APPLICATION : "Application(s) added successfully.",
			APPLICATION_NAME_REQUIRED : "Application Name is required",
			APPLICATION_NAME_MIN_LENGTH_ERROR : "Application Name should at least contain 2 characters",
			APPLICATION_NAME_MAX_LENGTH_ERROR : "Application Name should not exceed 45 characters",
			APPLICATION_NAME_INVALID_ERROR : "Application Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			APPLICATION_LISTS_NOT_CONFIGURED  : "Applications not configured",
			CONFIRM_SAVE_APP_DETAILS : "You have unsaved Application details. Do you want to save?",
			CONFIRM_DELETE_APPLICATION : "Are you sure you want to delete?"
		},

		//Application Add/Edit Multiple messages
		applicationMultipleAddEdit:{
			APPLICATION_MULTIPLE_NAME_LENGTH_ERROR : "Application Name should be between 2 and 45 characters in length and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space.",
			APPLICATION_MULTIPLE_TAG_LENGTH_ERROR : "Tags should be between 2 and 45 characters in length, comma separated and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '_' or space.",
			CONFIRM_CLEAR_ROWS : "Do you want to reset the rows?",
			CONFIRM_CLEAR_ROWS_CONTENT : "Do you want to clear content of the row?",
			ERROR_ADD_MULTIPLE_APPLICATIONS : "Error in adding application(s)!"
		},

		//Component Type configuration messages
		componentTypeConfig:{
			COMPONENT_TYPE_NAME_REQUIRED : "Component Type Name is required",
			COMPONENT_TYPE_NAME_MIN_LENGTH_ERROR : "Component Type Name should at least contain 2 characters",
			COMPONENT_TYPE_NAME_MAX_LENGTH_ERROR : "Component Type Name should not exceed 45 characters",
			COMPONENT_TYPE_NAME_INVALID_ERROR : "Component Type Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ERROR_ADD_COMPONENT_TYPE : "Error in adding component type!",
			SUCCESS_ADD_COMPONENT_TYPE : "Component Type added successfully.",
			SUCCESS_UPDATE_COMPONENT_TYPE : "Component Type updated successfully.",
			ERROR_UPDATE_COMPONENT_TYPE : "Error in updating component type!",
			COMPONENT_TYPE_LISTS_NOT_CONFIGURED : "Component types not configured",
			DENY_STANDARD_COMP_TYPE_EDIT : "Standard component types cannot be edited"
		},

		//Menu details messages
		menuDetails:{
			ERROR_GET_MENU_DETAILS : "Error in getting menu details!",
			ERROR_GET_MENU_PERMISSION : "Error in getting menu permisson!"
		},

		//Component configuration
		componentConfig:{
			COMPONENT_NAME_INVALID_ERROR : "Component Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			COMPONENT_NAME_MIN_LENGTH_ERROR : "Component Name should at least contain 2 characters",
			COMPONENT_NAME_MAX_LENGTH_ERROR : "Component Name should not exceed 45 characters",
			ERROR_ADD_COMPONENT : "Error in adding component!",
			SUCCESS_ADD_COMPONENT : "Component added successfully.",
			SUCCESS_UPDATE_COMPONENT : "Component updated successfully.",
			ERROR_GET_COMPONENT_TAGS : "Error in getting component tags!",
			ERROR_GET_COMP_ATTRIB_LIST : "Error in getting component attribute list!",
			ERROR_GET_COMP_ATTRIB_TYPES : "Error in getting component attribute types!",
			ERROR_UPDATE_COMPONENT : "Error in updating component!",
			CONFIRM_ATTRIBUTE_DELETE : "Are you sure you want to delete an attribute?",
			ATTRIBUTE_NAME_INVALID_ERROR : "Attribute Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ATTRIBUTE_NAME_MIN_LENGTH_ERROR : "Attribute Name should at least contain 2 characters",
			ATTRIBUTE_NAME_MAX_LENGTH_ERROR : "Attribute Name should not exceed 45 characters",
			ATTRIBUTE_TYPE_REQUIRED_ERROR : "Please select Attribute Type",
			DENY_STANDARD_COMPONENT_EDIT : "Standard components cannot be edited",
			DENY_STANDARD_COMPONENT_CLONE : "Standard components cannot be cloned",
			DUPLICATE_ATTRIBUTE_NAME_ERROR : "Duplicate attribute names not allowed",
			COMP_LISTS_NOT_CONFIGURED : "Components not configured",
			CONFIRM_LOGOUT : "Are you sure you want to logout?",
			DUPLICATE_OPTIONS : "Duplicate Options not allowed",
			OPTION_MIN_LENGTH_ERROR : "Options should at least contain 2 characters",
			INVALID_OPTION_NAME : "Options should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '_' or space",
			DENY_INACTIVE_COMPONENT_MAPPING : "Inactive components cannot be mapped to KPIs"
		},

		//KPI/Producer Mapping
		kpiProducerMap:{
			ERROR_GET_PRODUCERS_FOR_KPI : "Error in producers mapping for KPI!",
			ERROR_GET_KPI_UNITS : "Error in getting KPI units list!",
			//ERROR_GET_PRODUCERS : "Error in getting producer list!", // commented as this already exists under producerConfig
			ERROR_KPI_PRODUCER_MAP : "Error in getting KPI/Producer Mapping!",
			ERROR_KPI_REQUIRED : "Please select KPI",
			ERROR_KPI_UNIT_REQUIRED : "Please select unit",
			ERROR_PRODUCER_REQUIRED : "Please select producer",
			ERROR_DEFAULT_PRODUCER_REQUIRED : "Please select default producer",
			ERROR_COLLECTION_INTERVAL_REQUIRED : "Please enter collection interval",
			ERROR_COLLECTION_INTERVAL_UNIT_REQUIRED : "Please enter collection interval unit",
			ERROR_INVALID_COLL_INTERVAL_SECONDS : "Collection interval should be 15 or 30 seconds",
			ERROR_INVALID_COLL_INTERVAL_MINUTES : "Collection interval should be between 1 to 1440 minutes",
			ERROR_SAME_KPI_DEFAULT_PRODUCER : "Cannot set more than one default producer for same KPI",
			ERROR_STANDARD_COMPONENT_DELETE : "You cannot delete standard KPI/producer mapping belonging to standard components",
			SUCCESS_UPDATE_KPI_PRODUCER_MAP : "KPI Producer mapping updated successfully.",
			ERROR_UPDATE_KPI_PRODUCER_MAP : "Error in updating KPI Producer mapping!",
			CONFIRM_KPI_PROD_MAPPING_DELETE : "Deleting Component/KPI mapping will remove producer mapping from component & its instances. Are you sure you want to delete?",
			CONFIRM_KPI_TYPE_CHANGE : "KPI list will be cleared due to change in selection.Are you sure you want to proceed with the change?",
			KPI_NAME_INVALID_ERROR : "KPI Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ERROR_DEFAULT_THRESHOLD_OPERATOR_REQUIRED : "Please select default threshold operator",
			ERROR_SEVERITY_VALUE_REQUIRED : "Please enter severity value"
		},

		//Cluster Configuration
		clusterConfig:{
			ERROR_ADD_CLUSTER : "Error in adding cluster!",
			SUCCESS_ADD_CLUSTER : "Cluster added successfully.",
			SUCCESS_UPDATE_CLUSTER : "Cluster updated successfully.",
			ERROR_GET_CLUSTER_TAGS : "Error in getting cluster tags!",
			ERROR_UPDATE_CLUSTER : "Error in updating cluster!",
			CLUSTER_NAME_REQUIRED : "Cluster Name is required",
			CLUSTER_NAME_INVALID_ERROR : "Cluster Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			CLUSTER_NAME_MIN_LENGTH_ERROR : "Cluster Name should at least contain 2 characters",
			CLUSTER_NAME_MAX_LENGTH_ERROR : "Cluster Name should not exceed 45 characters",
			ERROR_GET_COMP_INSTANCES : "Error in getting component instances!",
			ERROR_GET_CLUSTER_OPERATIONS : "Error in getting cluster operations!",
			CONFIRM_COMPTYPE_CLEAR_COMPINST : "Changing component type will clear component instance(s) selection. Do you want to continue?",
			CONFIRM_COMPNAME_CLEAR_COMPINST : "Changing component will clear component instance(s) selection. Do you want to continue?",
			DENY_INACTIVE_CLUSTER_MAPPING : "Inactive clusters cannot be mapped to KPIs",
			DENY_NO_INSTANCES_CLUSTER_MAPPING : "Clusters without any component instance(s) cannot be mapped to KPIs",
			CLUSTER_LISTS_NOT_CONFIGURED : "Clusters not configured"
		},

		//Component Instance Configuration
		componentInstanceConfig:{
			ERROR_ADD_COMP_INSTANCE : "Error in adding component instance!",
			ERROR_ADD_HOST_INSTANCE : "Error in adding host instance!",
			SUCCESS_ADD_COMP_INSTANCE : "Component instance added successfully.",
			SUCCESS_ADD_HOST_INSTANCE : "Host instance added successfully.",
			SUCCESS_EDIT_HOST_INSTANCE : "Host instance updated successfully.",
			SUCCESS_UPDATE_COMP_INSTANCE : "Component instance updated successfully.",
			ERROR_GET_COMP_INSTANCE_TAGS : "Error in getting component instance tags!",
			ERROR_UPDATE_COMP_INSTANCE : "Error in updating component instance!",
			COMP_INSTANCE_NAME_INVALID_ERROR : "Component Instance Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			COMP_INSTANCE_NAME_MIN_LENGTH_ERROR : "Component Instance Name should at least contain 2 characters",
			COMP_INSTANCE_NAME_MAX_LENGTH_ERROR : "Component Instance Name should not exceed 45 characters",
			CONFIRM_KPI_GROUP_VALUE_DELETE : "Are you sure you want to delete a KPI group value?",
			ERROR_KPI_GROUP_VALUE_REQUIRED : "Value required for KPI group",
			ERROR_KPI_PRODUCER_COMP_INST_MAP : "Error in getting KPI/Producer Mapping for component instance!",
			ERROR_DIFFERENT_VERSIONS_MULTI_EDIT : "Multiple edit for component instances of different components & versions is not allowed.",
			ERROR_ADD_COMP_INSTANCES : "Error in adding multiple component instances!",
			ERROR_GET_COMP_INSTANCES_ATTRIBUTES : "Error in getting component instance attributes!",
			SUCCESS_ADD_COMP_INSTANCES : "Component instance(s) added successfully",
			SUCCESS_UPDATE_COMP_INSTANCES : "Component instance(s) updated successfully",
			APPLICATION_NAME_SELECTION_REQUIED : "Please select valid application from the list.",
			COMPINST_LISTS_NOT_CONFIGURED : "Component Instances not configured",
			CONFIRM_PASSWORD_RESET : "Do you want to reset the password?",
			CONFIRM_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP : "Component Instance data will be cleared due to change in selection.Are you sure you want to proceed with the change?",
			CONFIRM_HOST_ATTRIBUTE_CLEAR_FOR_SELECTED_COMP : "Host Instance data will be cleared due to change in selection.Are you sure you want to proceed with the change?",
			CONFIRM_HOST_CLEAR_FOR_SELECTED_COMP : "Host data will be cleared due to change in selection.Are you sure you want to proceed with the change?",
			CONFIRM_DELETE_INSTANCE : "Are you sure you want to delete?",
			CONFIRM_DELETE_SERVER_DETAILS : "Are you sure you want to delete server details?",
			CONFIRM_RESET_COMP_INST : "Are you sure you want to reset component instance details?",
			CONFIRM_SAVE_COMP_INST_DETAILS : "You have unsaved Component Instance details. Do you want to save?",
			CONFIRM_DELETE_HOST_DETAILS : "Are you sure you want to delete host details?",
			CONFIRM_COMP_TYPE_CHANGE : "Changing component type will clear all the data of current type. Do you want to continue?",
			CONFIRM_COMP_CHANGE : "Changing component name will clear all the data of current component. Do you want to continue?",
			CONFIRM_COMP_VERSION_CHANGE : "Changing component version will clear all the data of current version. Do you want to continue?"
		},

		//KPI messages
		kpiConfig:{
			KPI_DATATYPE_REQUIRED : "KPI datatype is required",
			KPI_UNITS_REQUIRED : "KPI unit is required",
			KPI_CLUSTEROPERN_REQUIRED : "KPI cluster operation is required",
			ERROR_GET_CLUSTEROPERN_LISTS : "Error in getting Cluster operations list!",
			ERROR_GET_KPITYPE_LISTS : "Error in getting KPI type list!",
			ERROR_GET_KPI_GROUP_LISTS : "Error in getting KPI group list!",
			KPI_MULTIPLE_NAME_LENGTH_ERROR : "KPI Name should be between 2 and 45 characters in length and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space.",
			SUCCESS_MULTIPLE_ADD_KPIS : "KPI(s) added successfully",
			ERROR_ADD_MULTIPLE_KPIS : "Error in adding KPI(s)!",
			ERROR_ADD_KPI : "Error in adding KPI!",
			ERROR_EDIT_KPI : "Error in editing KPI!",
			SUCCESS_ADD_KPI : "KPI added successfully.",
			SUCCESS_UPDATE_KPI : "KPI updated successfully.",
			SUCCESS_MULTIPLE_UPDATE_KPIS : "KPIs updated successfully.",
			ERROR_GET_KPI_TAGS : "Error in getting KPI tags!",
			ERROR_EDIT_MULTIPLE_KPIS : "Error in editing multiple KPI(s)",
			KPI_LISTS_NOT_CONFIGURED : "KPIs not configured",
			KPI_DESC_INFO : "Please provide description for KPI as it will be helpful to know the detail information of KPI",
			ERROR_MSG_SAME_TYPE : "Please select same type, for KPIs edit operation!...",
			ERROR_MSG_SAME_CATEGORY : "Please select same category, for KPIs edit operation!...",
			ERROR_MSG_SAME_DATATYPE_FORTYPE : "Please select same datatype, for KPIs edit operation!...",
			CONFIRM_KPI_TYPE_CLEAR : "Changing KPI type will clear KPI group selection. Do you want to continue?",
			CONFIRM_DELETE_KPI : "Are you sure you want to delete?"
		},

		//Producer Configuration
		producerConfig:{
			ERROR_GET_PRODUCERS : "Error in getting producers!",
			SUCCESS_ADD_PRODUCER : "Producer added successfully.",
			SUCCESS_UPDATE_PRODUCER : "Producer updated successfully.",
			ERROR_GET_PRODUCER_TAGS : "Error in getting producer tags!",
			ERROR_ADD_PRODUCER : "Error in adding producer!",
			ERROR_UPDATE_PRODUCER : "Error in updating producer!",
			PRODUCER_NAME_REQUIRED : "Producer Name is required",
			PRODUCER_NAME_INVALID_ERROR : "Producer Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			PRODUCER_NAME_MIN_LENGTH_ERROR : "Producer Name should at least contain 2 characters",
			PRODUCER_NAME_MAX_LENGTH_ERROR : "Producer Name should not exceed 45 characters",
			PRODUCER_MAP_TO_MIXED_KPI_TYPE_ERROR : "You cannot map same producer to different KPI types.",
			PRODUCER_MAP_TO_GROUPED_UNGROUPED_ERROR : "You cannot map same producer to both grouped & ungrouped KPIs.",
			CONFIRM_MAPPING_DELETE	 : "Are you sure you want to delete Component/KPI mapping?",
			CONFIRM_PARAMETER_DELETE : "Are you sure you want to delete parameter?",
			SCRIPT_NAME_REQUIRED : "Script File Name is required",
			SCRIPT_SIGNATURE_REQUIRED : "Script Signature is required",
			SQL_QUERY_REQUIRED : "SQL Query is required",
			CONFIRM_CLEAR_PARAMS : "Current parameter details will be removed. Do you want to continue?",
			ERROR_GET_PRODUCER_PARAMS : "Error in getting producer parameters!",
			SELECT_PARAM_TYPE : "Select Input Type",
			SELECT_PARAM_NAME_MSG : "Please enter Parameter Name",
			ENTER_PARAM_VALUE_MSG : "Please enter Parameter Value",
			TARGET_OBJECT_NAME_REQUIRED : "Target Object Name is required",
			JMX_URL_REQUIRED : "JMX URL is required",
			PARAMETER_TYPE_REQUIRED_ERROR : "Please select Parameter Type",
			SELECT_CONTENT_TYPE : "Select Content Type",
			HTTPD_URL_REQUIRED : "Please enter HTTP Status URL",
			HTTP_JSON_URL_REQUIRED : "Please enter HTTP-JSON Status URL",
			CONTENT_TYPE_REQUIRED : "Please select Content Type",
			MODULE_NAME_REQUIRED : "Please enter Module Name",
			CONFIRM_DELETE	 : "Are you sure you want to delete?",
			JDBC_DRIVER_REQUIRED : "JDBC Driver name is required",
			JDBC_URL_REQUIRED : "JDBC URL is required",
			JDBC_QUERY_RESULT_REQUIRED : "Query Result is required",
			JMX_ATTR_TYPE_REQUIRED : "Attribute Type is required",
			JPPF_SERVER_TYPE_REQUIRED : "Server Type is required",
			SELECT_QUERY_RESULT : "Select Query Result",
			SELECT_ATTRIBUTE_TYPE : "Select Attribute Type",
			SELECT_SERVER_TYPE : "Select Server Type",
			PRODUCER_LISTS_NOT_CONFIGURED : "Producers not configured",
			PRODUCER_ADD_KPI_DISCARD_CONFIRM : "The following KPIs will be discarded as it is unavailable in current selection:\n",
			PRODUCER_EDIT_KPI_DISCARD_CONFIRM : "This action will stop data collection for following KPIs from current producer as it is unavailable in current selection:\n",
			DENY_STANDARD_PRODUCER_EDIT : "Standard producers cannot be edited",
			SELECT_COMP_WITH_VERSION_MSG : "Please add Component/KPI Association",
			SELECT_KPI_FOR_PRODUCER : "Please select KPI(s)"
		},

		//Agent Configuration
		agentConfig:{
			ERROR_ADD_AGENT : "Error in adding agent!",
			ERROR_UPDATE_AGENT : "Error in updating agent!",
			ERROR_GET_AGENT_CONFIG_PROPERTIES : "Error in getting agent download details agent!",
			ERROR_GET_AGENTS : "Error in getting agents!",
			ERROR_GET_HOSTS : "Error in getting hosts!",
			ERROR_GET_AGENT_TYPES : "Error in getting agent types!",
			ERROR_GET_AGENT_MODES : "Error in getting agent modes!",
			ERROR_GET_COM_PROTOCOLS : "Error in getting data communication protocols list!",
			ERROR_GET_GRPC_SETTINGS : "Error in getting GRPC settings list!",
			SELECT_AGENT_MODE : "Select Mode",
			SELECT_AGENT_TYPE : "Select Type",
			SELECT_HOST : "Select Host",
			ENTER_SELECT_HOST : "Enter/Select Host",
			SELECT_COM_PROTOCOL : "Select Protocol",
			CONFIRM_CLEAR_COMP_INSTS : "Current search or selection of component instance(s) will be cleared. Do you want to continue?",
			CONFIRM_AGENT_TYPE_CHANGE : "Changing agent type will clear all the data of current type. Do you want to continue?",
			MULTIPLE_COMP_INSTANCES_SELECTION_ERROR : "Multiple component instance selection not allowed for agent type: ",
			AGENT_NAME_REQUIRED : "Agent Name is required",
			AGENT_NAME_MIN_LENGTH_ERROR : "Agent Name should at least contain 2 characters",
			AGENT_NAME_MAX_LENGTH_ERROR : "Agent Name should not exceed 45 characters",
			AGENT_NAME_INVALID_ERROR : "Agent Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			SELECT_AGENT_TYPE_MSG : "Please select Agent Type",
			ENTER_SELECT_HOST_MSG : "Please enter/select Host",
			SELECT_AGENT_MODE_MSG : "Please select Agent Mode",
			SELECT_COM_PROTOCOL_MSG : "Please select Data Communication Protocol",
			COM_PROTOCOL_HOST_REQUIRED : "Data Communication Host is required",
			INVALID_COM_PROTOCOL_HOST : "Invalid Data Communication Protocol Host",
			COM_PROTOCOL_PORT_REQUIRED : "Data Communication Port is required",
			COM_PROTOCOL_URL_REQUIRED : "Data Communication URL is required",
			COM_PROTOCOL_PORT_INVALID : "Data Communication Port should be in the range (0 to 65535)",
			ERROR_GET_AGENT_JSON_CONFIG : "Error in downloading agent JSON data",
			SUCCESS_ADD_AGENT : "Agent added successfully with the following Unique ID:\n\n",
			SUCCESS_ADD_AGENT_OTHERS : "Agent added successfully",
			SUCCESS_UPDATE_AGENT_OTHERS : "Agent updated successfully",
			SUCCESS_UPDATE_AGENT : "Agent with the following Unique ID updated successfully:\n\n",
			AGENT_LISTS_NOT_CONFIGURED : "Agents not configured",
			ERROR_GET_VIRTUAL_HOSTS : "Error in getting virtual hosts",
			HTTP_PROXY_HOST_REQUIRED : "HTTP proxy host is required",
			HTTP_PROXY_PORT_REQUIRED : "HTTP proxy port is required",
			HTTP_PROXY_PORT_INVALID : "HTTP proxy port should be in the range (0 to 65535)",
			INTERFACE_REQUIRED : "Interface value is required",
			PHYSICAL_ADDRESS_REQUIRED : "Physical Address is required",
			INVALID_PHYSICAL_ADDRESS : "Invalid Physical Address",
			APPLICATION_SELECTION_REQUIRED : "Please select application(s)",
			PORT_REQUIRED : "Port value is required",
			PORT_INVALID : "Port should be in the range (0 to 65535)",
			INVALID_PORT_VALUE : "Invalid port value",
			FROM_PORT_RANGE_REQUIRED : "Starting port value is required",
			FROM_PORT_RANGE_INVALID : "Starting port value is invalid",
			TO_PORT_RANGE_REQUIRED : "Ending port value is required",
			TO_PORT_RANGE_INVALID : "Ending port value is invalid",
			TO_PORT_INVALID : "Ending Port value should not be greater than 65535",
			PSA_PROTOCOL_REQUIRED : "Please select protocol",
			KEY_FILE_PATH_REQUIRED : "Key file path for HTTPS protocol is required",
			KEY_FILE_PASSWORD_REQUIRED : "Key file password for HTTPS protocol is required",
			INVALID_PORT_RANGE : "Starting port value should be lesser than ending port value",
			INVALID_END_PORT_RANGE : "Ending port value should be greater than starting port value",
			ENTER_SELECT_VIRTUAL_HOST : "Enter/Select Virtual Host",
			ENTER_VIRTUAL_HOST : "Enter Virtual Host",
			INVALID_VIRTUAL_HOST : "Invalid Virtual Host",
			RESPONSE_BODY_OFFSET_REQUIRED : "Response body offset required",
			RESPONSE_BODY_SIZE_REQUIRED : "Response body size required",
			ERROR_GET_OPERATION_MODES : "Error in getting operation modes",
			SELECT_PROTOCOL : "Select Protocol",
			SELECT_GRPC_SETTING_MSG : "Please select GRPC Setting",
			SELECT_PROTOCOL_MSG : "Please select protocol for HTTP proxy",
			ERROR_GET_PSA_SERVER_DETAILS : "Error in getting server details!",
			ERROR_GET_RESPONSE_DETAILS : "Error in getting response details",
			CONFIRM_DELETE_INTERFACE : "Are you sure you want to delete an interface?",
			CONFIRM_DELETE_FILTER : "Are you sure you want to delete filter(s)?",
			SERVER_DETAILS_FILTER_REQUIRED : "Server details should contain atleast one filter",
			LOCAL_REMOTE_CHECKBOX_LABEL : "Please select data capture method to create component agent:",
			COMPONENTAGENTCAPTURED_REMOTE_LABEL: "Please select Component Agent in case to use existing ones",
			COMPONENTAGENTCAPTURED_LOCAL_LABEL: "Please click  on OK to create a Component Agent for each configured host.",
			ERROR_GET_COMPONENT_AGENTS: "Error in getting Component Agents",
			ERROR_COMPONENT_AGENT_FAILURE: "An error occured while creating the agents. Please try again.",
			LOCAL: "Non-AppsOne SetUp",
			REMOTE: "AppsOne SetUp",
			ERROR_GET_UNMAPPED_COMPINSTANCES: "Error in getting unmapped component instances",
			ERROR_DUPLICATE_INTERFACE_DETAILS: "Duplicate interface details not allowed",
			ERROR_DUPLICATE_FILTER_DETAILS: "Duplicate filter details not allowed",
			FILTER_OFFSET_VALUE_REQUIRED: "Response body offset value is required",
			INVALID_FILTER_OFFSET_VALUE: "Response body offset value is invalid",
			FILTER_SIZE_VALUE_REQUIRED: "Response body size is required",
			INVALID_FILTER_SIZE_VALUE: "Response body size is invalid",
			CONFIRM_SAVE_AGENT_DETAILS : "You have unsaved Agent details. Do you want to save?",
			SELECT_GRPC_SETTING: "Select Setting"
		},

		//Transaction configuration
		transactionConfig:{
			ERROR_GET_TRANSACTIONS : "Error in getting transactions",
			ERROR_GET_TRANSACTION_TYPES : "Error in getting transaction types",
			ERROR_GET_TRANSACTION_METHODS : "Error in getting transaction methods",
			ERROR_GET_TRANSACTION_RESPONSE_TYPES : "Error in getting transaction response types",
			TXN_NAME_REQUIRED : "Transaction Name is required",
			TXN_NAME_MIN_LENGTH_ERROR : "Transaction Name should at least contain 2 characters",
			TXN_NAME_MAX_LENGTH_ERROR : "Transaction Name should not exceed 45 characters",
			TXN_NAME_INVALID_ERROR : "Transaction Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			TXN_ADDED_SUCCESSFULL : "Transactions added successfully",
			TXN_METHOD_REQUIRED : "Method is required",
			MSG_SELECT_APPLICATION : "Please select application",
			ERROR_ADD_TRANSACTION : "Error in adding transaction",
			ERROR_UPDATE_TRANSACTION : "Error in updating transaction",
			ERROR_GET_TXN_TAGS : "Error in getting cluster tags!",
			DELETE_CONFIRM_QUERYPARAMS : "Are you sure you want to delete query parameters?",
			DELETE_CONFIRM_HTTP_PATTERN : "Are you sure you want to delete the pattern ?",
			DELETE_CONFIRM_THRESHOLD : "Are you sure you want to delete added thresholds ?",
			ERROR_GET_TXN_JSON_CONFIG : "Error in downloading transaction data",
			TXN_LISTS_NOT_CONFIGURED : "Transactions not configured",
			HTTP_PATTERN_UPDATE_ERROR :  "Please update the HTTP pattern selected to save",
			SELECT_TXN_TYPE_TYPE_MSG: "Please select Transaction Type",
			SUCCESS_MULTIPLE_ADD_TXNS : "Transaction(s) added successfully",
			ERROR_ADD_MULTIPLE_TXNS : "Error in adding Transaction(s)!",
			CONFIRM_DELETE_TXN : "Are you sure you want to delete?",
			TXN_MULTIPLE_NAME_LENGTH_ERROR : "Transaction Name should be between 2 and 45 characters in length and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space.",

			SUCCESS_UPDATE_TXN : "Transaction updated successfully.",
			THRESOLD_ADDLIMIT_ERROR  : "Addition of more thresold is not allowed",
			REQUEST_ADD_PATTERN  : "Please add Pattern for selected type",
			START_PATTERN_REQUIRED : "Start pattern is required",
			END_PATTERN_REQUIRED  : "End pattern is required as length is defined",
			ERROR_SAME_THRESHOLD_TYPE : "Threshold Response type should not be same",
			ERROR_FOR_RESPONSE_VAL :  "Threshold Response value should be less than 999999",
			ERROR_FOR_EMPTY_RESPONSE_VAL :  "Threshold value cannot be empty",
			CONST_HTTP : "HTTP",
			CONST_TCP : "TCP",
			CONST_UPDATE : "Update",
			CONST_ADD : "Add",
			CONST_RESET : "Reset",

			ERROR_SAME_TCP_PATTERN : "Transaction Pattern Error : Same TCP pattern is not allowed",
			ERROR_SAME_HTTP_PATTERN : "Transaction Pattern Error : Same HTTP pattern is not allowed",

			ERROR_SAME_BVE_NAME : "Extractor name already exists",
			ERROR_EXTRACTORNAME : "Please provide extractor name",
			ERROR_EXYTRACTOR_PATTERN : "Please provide extractor pattern",
			ERROR_EXTRACTOR_NAME : "Please provide extractor name",
			ERROR_EXTRACTOR_VALUE : "Please provide extractor value",

			CONFIRM_TXN_TYPE_CHANGE : "All the data of current type will be cleared due to change in transaction type. Are you sure you want to proceed with the change?",
			CONFIRM_SAVE_TXN_DETAILS : "You have unsaved Transaction details. Do you want to save?"
		},

		//User Role Configuration
		userRoleConfig:{
			ERROR_ADD_USER_ROLE : "Error in adding user role!",
			SUCCESS_ADD_USER_ROLE : "User Role added successfully.",
			SUCCESS_UPDATE_USER_ROLE : "User Role updated successfully.",
			ERROR_UPDATE_USER_ROLE : "Error in updating user role!",
			USER_ROLE_NAME_REQUIRED : "User Role Name is required",
			USER_ROLE_NAME_INVALID_ERROR : "User Role Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			USER_ROLE_NAME_MIN_LENGTH_ERROR : "User Role Name should at least contain 2 characters",
			USER_ROLE_NAME_MAX_LENGTH_ERROR : "User Role Name should not exceed 45 characters",
			ERROR_GET_SCREEN_LIST : "Error in getting screen list",
			ERROR_GET_USER_ROLE_PERMISSION : "Error in getting user role permissions",
			ERROR_GET_USER_ADDITIONAL_PERMISSION : "Error in getting additional permissions for user",
			ERROR_DELETE_USER_ROLES : "Error in deleting user roles!",
			ERROR_GET_USER_ROLES : "Error in getting user roles!",
			ERROR_SELECT_ROLE_PERMISSION : "Please select atleast one permission",
			DENY_STANDARD_USER_ROLE_EDIT: "Standard user roles cannot be edited",
			MULTIPLE_LOGIN_OPTION_DISABLED_MSG: "Cannot enable this option since the option in global settings is disabled",
			FAILED_USER_ROLE_DELETE: "The following role(s) could not be deleted since the users are assigned with: \n",
			SUCCESS_USER_ROLE_DELETE: "User role(s) deleted successfully.",
			DENY_STANDARD_USER_ROLE_DELETE: "Standard user roles cannot be deleted",
		},

		//User Profile Configuration
		userProfileConfig:{
			ERROR_ADD_USER_PROFILE : "Error in adding user profile!",
			SUCCESS_ADD_USER_PROFILE : "User Profile added successfully.",
			SUCCESS_UPDATE_USER_PROFILE : "User Profile updated successfully.",
			SUCCESS_UPDATE_USER_PASSWORD_RESET : "Password has been reset successfully.",
			ERROR_UPDATE_USER_PROFILE : "Error in updating user profile!",
			ERROR_RESET_USER_PASSWORD : "Error in resetting user password!",
			USER_PROFILE_FIRST_NAME_REQUIRED : "First Name is required",
			USER_PROFILE_FIRST_NAME_INVALID_ERROR : "First Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			USER_PROFILE_LAST_NAME_INVALID_ERROR : "Last Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			USER_PROFILE_FIRST_NAME_MIN_LENGTH_ERROR : "First Name should at least contain 2 characters",
			USER_PROFILE_LAST_NAME_MIN_LENGTH_ERROR : "Last Name should at least contain 2 characters",
			USER_PROFILE_FIRST_NAME_MAX_LENGTH_ERROR : "First Name should not exceed 20 characters",
			USER_PROFILE_LAST_NAME_MAX_LENGTH_ERROR : "Last Name should not exceed 20 characters",
			USER_NAME_INVALID_ERROR : "User Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or '.'",
			USER_NAME_MIN_LENGTH_ERROR : "User Name should at least contain 5 characters",
			USER_NAME_MIN_TWO_CHAR_LENGTH_ERROR : "User Name should at least contain 2 characters",
			USER_NAME_MAX_LENGTH_ERROR : "User Name should not exceed 10 characters",
			ERROR_GET_USER_PROFILES : "Error in getting user profiles!",
			USER_PROFILE_PASSWORD_REQUIRED : "Password is required",
			USER_PROFILE_PASSWORD_MIN_LENGTH_ERROR : "Password should at least contain 8 characters",
			USER_PROFILE_PASSWORD_MAX_LENGTH_ERROR : "Password should not exceed 15 characters",
			USER_PROFILE_CONFIRM_PASSWORD_REQUIRED : "Please retype password",
			USER_PROFILE_PASSWORDS_MISMATCH : "Passwords should match with each other",
			USER_PROFILE_ROLE_REQUIRED : "Please select user role",
			USER_NAME_REQUIRED : "User Name is required",
			USER_PROFILE_INVALID_PASSWORD: "Password should contain atleast one of each – special character, numeral, lower case and upper case alphabets",
			USER_PROFILE_OLD_PASSWORD_REQUIRED: "Old password is required",
			USER_PROFILE_OLD_NEW_PASSWORD_SAME_ERROR: "Old & new password cannot be same",
			USER_PROFILE_NEW_PASSWORD_REQUIRED: "New password is required",
			MULTIPLE_LOGIN_OPTION_DISABLED_MSG: "Cannot enable this option since the option for the selected role is disabled",
			LOGGED_IN_USER_STATUS_DISABLED_MSG: "Cannot change status of your own profile",
			CONFIRM_USER_PASSWORD_RESET: "Are you sure you want to reset password?"
		},

		//Individual Profile Configuration
		indivUserProfileConfig:{
			ERROR_GET_INDIVIDUAL_USER_PROFILE : "Error in getting user profile details!"
		},

		//Global Settings
		globalSettingsConfig:{
			ERROR_GET_GLOBAL_SETTINGS: "Error in getting global settings!",
			ERROR_UPDATE_GLOBAL_SETTINGS: "Error in updating global settings!",
			SUCCESS_UPDATE_GLOBAL_SETTINGS: "Global settings updated successfully.",
			PWD_EXPIRY_DAYS_REQUIRED: "Password expiry day(s) required",
			PWD_EXPIRY_INTIMATION_DAYS_REQUIRED: "Password expiry intimation day(s) required",
			PWD_EXPIRY_DAYS_MIN_ERROR: "Password expiry day(s) cannot be zero",
			PWD_EXPIRY_INTIMATION_DAYS_MIN_ERROR: "Password expiry intimation day(s) cannot be zero",
			PWD_EXPIRY_DAYS_ERROR: "Password expiry intimation days should be less than password expiry days"
		},

		//Notification Content Profile
		notificationContentProfile:{
			SELECT_ALERT_PROFILE_TYPE: "Select Profile Type",
			SELECT_ALERT_PROFILE_TYPE_MSG: "Please select Profile Type",
			ERROR_GET_NOTIFICATION_CONTENT_PROFILES: "Error in getting notification content profiles!",
			ERROR_GET_ALERT_PROFILE_TYPES: "Error in getting alert profile types!",
			NOTIFICATION_CONTENTS_NOT_CONFIGURED : "Notification Content Profiles not configured",
			NOTIFICATION_CONTENT_NAME_REQUIRED : "Profile Name is required",
			NOTIFICATION_CONTENT_NAME_INVALID_ERROR : "Profile Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			NOTIFICATION_CONTENT_NAME_MIN_LENGTH_ERROR : "Profile Name should at least contain 2 characters",
			NOTIFICATION_CONTENT_NAME_MAX_LENGTH_ERROR : "Profile Name should not exceed 45 characters",
			EMAIL_SUBJECT_REQUIRED : "Email Subject is required",
			EMAIL_MESSAGE_REQUIRED : "Email Message is required",
			SMS_MESSAGE_REQUIRED : "SMS Message is required",
			ERROR_ADD_NOTIFICATION_CONTENT : "Error in adding notification content profile!",
			SUCCESS_ADD_NOTIFICATION_CONTENT : "Notification content profile added successfully.",
			ERROR_UPDATE_NOTIFICATION_CONTENT : "Error in updating notification content profile!",
			SUCCESS_UPDATE_NOTIFICATION_CONTENT : "Notification content profile updated successfully.",
			ERROR_GET_NOTIFICATION_CONTENT_TAGS : "Error in getting notification content profile tags!",
			EMAIL_SUBJECT_MAX_LENGTH_ERROR : "Email Subject content should not exceed 255 characters",
			EMAIL_MESSAGE_MAX_LENGTH_ERROR : "Email Message should not exceed 65535 characters",
			SMS_MESSAGE_MAX_LENGTH_ERROR : "SMS Message should not exceed 512 characters"
		},

		//Time Profile
		timeProfile:{
			TIME_PROFILES_NOT_CONFIGURED : "Time Profiles not configured",
			TIME_PROFILE_NAME_REQUIRED : "Profile Name is required",
			TIME_PROFILE_NAME_INVALID_ERROR : "Profile Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			TIME_PROFILE_NAME_MIN_LENGTH_ERROR : "Profile Name should at least contain 2 characters",
			TIME_PROFILE_NAME_MAX_LENGTH_ERROR : "Profile Name should not exceed 45 characters",
			ERROR_GET_TIME_PROFILES : "Error in getting time profiles!",
			CONFIRM_TIME_TYPE_CHANGE : "Changing time type will reset current values. Do you want to continue?",
			FROM_TO_VALUE_REQUIRED : "Hour/Minute range is mandatory for the week day: ",
			FROM_HOUR_VALUE_REQUIRED : "From Hour is required",
			FROM_MINUTE_VALUE_REQUIRED : "From Minute is required",
			TO_HOUR_VALUE_REQUIRED : "To Hour is required",
			TO_MINUTE_VALUE_REQUIRED : "To Minute is required",
			INVALID_FROM_TO_TIME_RANGE : "Start Hour/Minute should be less than or equal to End Hour/Minute for the week day",
			INVALID_TO_FROM_TIME_RANGE : "End Hour/Minute should be greater than or equal to Start Hour/Minute for the week day",
			TIME_CONFLICT_ERROR : "The time selected conflicts with other time slot in same day",
			ERROR_ADD_TIME_PROFILE : "Error in adding time profile!",
			SUCCESS_ADD_TIME_PROFILE : "Time profile added successfully.",
			ERROR_UPDATE_TIME_PROFILE : "Error in updating notification content profile!",
			SUCCESS_UPDATE_TIME_PROFILE : "Time profile updated successfully.",
			ERROR_GET_TIME_PROFILE_TAGS : "Error in getting time profile tags!",
			ATLEAST_ONE_FROM_TO_VALUE_REQUIRED : "Atleast one Hour/Minute range should be configured",
			CONFIRM_TIME_SLOT_DELETE: "Are you sure you want to delete current time slot?"
		},

		//Alert Profile
		alertProfile:{
			ALERT_PROFILES_NOT_CONFIGURED : "Alert Profiles not configured",
			ALERT_PROFILE_NAME_REQUIRED : "Alert Profile Name is required",
			ALERT_PROFILE_NAME_INVALID_ERROR : "Alert Profile Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ALERT_PROFILE_NAME_MIN_LENGTH_ERROR : "Alert Profile Name should at least contain 2 characters",
			ALERT_PROFILE_NAME_MAX_LENGTH_ERROR : "Alert Profile Name should not exceed 45 characters",
			ERROR_GET_ALERT_PROFILES : "Error in getting alert profiles!",
			CONFIRM_COVERAGE_WINDOW_DELETE : "Are you sure you want to delete coverage window?",
			SELECT_TIME_PROFILE: "Select Profile",
			SELECT_SEVERITY_PROFILE: "Select Profile",
			SELECT_ESCALATION_PROFILE: "Select Profile",
			SELECT_NOTIFICATION_PROFILE: "Select Profile",
			ERROR_ADD_ALERT_PROFILE : "Error in adding alert profile!",
			SUCCESS_ADD_ALERT_PROFILE : "Alert profile added successfully.",
			ERROR_UPDATE_ALERT_PROFILE : "Error in updating alert profile!",
			SUCCESS_UPDATE_ALERT_PROFILE : "Alert profile updated successfully.",
			ERROR_GET_ALERT_PROFILE_TAGS : "Error in getting alert profile tags!",
			SELECT_THRESHOLD_OPERATION : "Select Operator",
			ERROR_GET_THRESHOLD_OPERATIONS : "Error in getting threshold operations!",
			ERROR_GET_TXN_THRESHOLD_TYPES : "Error in getting transaction threshold types!",
			ERROR_GET_TXN_STATUS : "Error in getting transaction status!",
			ERROR_GET_TXN_RESPONSE_TYPES : "Error in getting response types!",
			ERROR_GET_COVERAGE_WINDOW_DETAILS: "Error in getting coverage window details!",
			CONFIRM_ALERT_PROFILE_TYPE_CHANGE : "Changing profile type will clear all the data of current type. Do you want to continue?",
			CONFIRM_ALERT_PROFILE_APPLICATION_CHANGE : "Changing application will clear all the threshold details. Do you want to continue?",
			CONFIRM_ALERT_ON_TYPE_CHANGE : "Changing threshold type will clear all the data of current type. Do you want to continue?",
			CONFIRM_DELETE_STATUS : "Are you sure you want to delete selected transaction status details?",
			ALERT_PROFILES_NOT_CONFIGURED : "Alert Profiles not configured",
			DENY_LAST_COVERAGE_WINDOW: "Cannot delete coverage window, atleast one is required",
			DENY_LAST_TXN_STATUS_DELETE: "Cannot delete status details, atleast one is required"
		},

		//Placeholders
		placeholders:{
			ERROR_GET_PLACEHOLDER_DETAILS : "Error in getting placeholders"
		},

		commonAlertProfile:{
			SELECT_ALERT_PROFILE_TYPE: "Select Profile Type",
			ERROR_GET_ALERT_PROFILE_TYPES: "Error in getting alert profile types!",
			PROFILE_CONTENT_NAME_REQUIRED : "Profile Name is required",
			PROFILE_CONTENT_NAME_INVALID_ERROR : "Profile Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			PROFILE_CONTENT_NAME_MIN_LENGTH_ERROR : "Profile Name should at least contain 2 characters",
			PROFILE_CONTENT_NAME_MAX_LENGTH_ERROR : "Profile Name should not exceed 45 characters",
		},

		//Severity Profile
		severityProfile:{
			ERROR_GET_SEVERITY_PROFILES: "Error in getting severity profiles!",
			ERROR_ADD_SEVERITY:"Error in adding severity profile",
			ERROR_UPDATE_SEVERITY:"Error in updating severity profile",
			SUCCESS_ADD_SEVERITY:"Severity profile added successfully.",
			SUCCESS_UPDATE_SEVERITY:"Severity profile updated successfully.",
			HIGH_PERSISTENT_VALUE : "Please provide persistence value for high severity type",
			MEDIUM_PERSISTENT_VALUE : "Please provide persistence value for medium severity type",
			LOW_PERSISTENT_VALUE : "Please provide persistence value for low severity type",
			ERROR_MAX_PERSISTENCE_HIGH_VALUE : "High severity persistence value cannot be greater than value 1440",
			ERROR_MAX_PERSISTENCE_MEDIUM_VALUE : "Medium severity persistence value cannot be greater than value 1440",
			ERROR_MAX_PERSISTENCE_LOW_VALUE : "Low severity severity persistence value cannot be greater than value 1440",
			ERROR_MIN_PERSISTENCE_HIGH_VALUE : "High severity persistence value cannot be less than value 1",
			ERROR_MIN_PERSISTENCE_MEDIUM_VALUE : "Medium severity persistence value cannot be less than value 1",
			ERROR_MIN_PERSISTENCE_LOW_VALUE : "Low severity persistence value cannot be less than value 1",
			SEVERITY_LISTS_NOT_CONFIGURED : "Severity profiles not configured",
			SEVERITY_NAME_REQUIRED : "Severity Name is required",
			SEVERITY_NAME_INVALID_ERROR : "Severity Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			SEVERITY_NAME_MIN_LENGTH_ERROR : "Severity Name should at least contain 2 characters",
			SEVERITY_NAME_MAX_LENGTH_ERROR : "Severity Name should not exceed 45 characters",
			SEVERITY_MULTIPLE_NAME_LENGTH_ERROR : "Severity Name should be between 2 and 45 characters in length and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space.",
			SEVERITY_DESC_INFO : "Please provide description for Severity as it will be helpful to know the detail information of KPI",	
		},

		//Escalation Profile
		escalationProfile:{
			ERROR_GET_ESCALATION_PROFILES: "Error in getting escalation profiles!",
			ERROR_ADD_ESCALATION:"Error in adding escalation profile",
			ERROR_UPDATE_ESCALATION:"Error in updating escalation profile",
			SUCCESS_ADD_ESCALATION:"Escalation profile added successfully.",
			SUCCESS_UPDATE_ESCALATION:"Escalation profile updated successfully.",
			ESCALATION_LISTS_NOT_CONFIGURED : "Escalation profiles not configured",
			ESCALATION_LISTS_NOT_CONFIGURED : "Escalation profiles not configured",
			ESCALATION_NAME_REQUIRED : "Escalation Name is required",
			ESCALATION_NAME_INVALID_ERROR : "Escalation Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			ESCALATION_NAME_MIN_LENGTH_ERROR : "Escalation Name should at least contain 2 characters",
			ESCALATION_NAME_MAX_LENGTH_ERROR : "Escalation Name should not exceed 45 characters",
			ESCALATION_MULTIPLE_NAME_LENGTH_ERROR : "Escalation Name should be between 2 and 45 characters in length and contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space.",
			ESCALATION_DESC_INFO : "Please provide description for Severity as it will be helpful to know the detail information of KPI",
			CONFIRM_DELETE_ESCALATIONS : "Are you sure you want to delete escalation detail(s)?",
			ERROR_EMAIL_REG_EXP : "Email to address should be valid email format with single or multiple email addresses separated with commas eg: <EMAIL>",
			ERROR_CC_REG_EXP : "CC address should be valid email format with single or multiple email addresses separated with commas eg: <EMAIL>",
			ERROR_BCC_REG_EXP : "BCC address should be valid email format with single or multiple email addresses separated with commas eg: <EMAIL>",
			ERROR_PHONE_REG_EXP : "Phone number should be valid phone number format with single or multiple numbers separated with commas eg: 9481255022,4344456554",
			LEVELNAME_REQUIRED_ERROR:"Please provide Level name ",
			EMAILTO_REQUIRED_ERROR:"Please provide Email to addresss ",
			PHONENO_REQUIRED_ERROR:"Please provide Phone number ",
			ERROR_MIN_HIGH_PERSISTENCE:"High persistence value should be greater than 0",
			ERROR_MAX_HIGH_PERSISTENCE:"High persistence value should be less than 100 ",

			ERROR_MIN_MEDIUM_PERSISTENCE:"Medium persistence value should be greater than 0",
			ERROR_MAX_MEDIUM_PERSISTENCE:"Medium persistence value should be less than 100 ",

			ERROR_MIN_LOW_PERSISTENCE:"Low persistence value should be greater than 0",
			ERROR_MAX_LOW_PERSISTENCE:"Low persistence value should be less than 100 ",

			ERROR_HIGHSUPPRESSION_VALUE_EMPTY:"Please enter numerical suppression value",
			ERROR_HIGHSUPPRESSION_VALUE:"High suppression value should be greater than 0",
			ERROR_ANYSUPPRESSION_VALUE_EMPTY_CHECK:"Please enter numerical suppression value for atleast one type of severity",
			//ERROR_ANYSUPPRESSION_VALUE:"Please provide suppression value greater than 0 for atleast one type of severity",

			VALIDATE_ONE_ESCALATION_LEVEL:"Escalation level 0 cannot be deleted",

			ERROR_IN_EMAIL_LENGTH:"Email to field length should be less than 512",
			ERROR_IN_MOBILENO_LENGTH:"Mobile number field length should be less than 512",

			CONFIRM_ALERT_TYPE_CHANGE : "Escalation level details list will be cleared due to change in type selection.Are you sure you want to proceed with the change?",
			ERROR_IN_LEVELS_FOR_HIGH_SUPPRESSION_VALUES:"High suppression value for higher levels cannot be empty or 0 when next level values are defined",
			ERROR_IN_LEVELS_FOR_MEDIUM_SUPPRESSION_VALUES:"Medium suppression value for higher levels cannot be empty or 0 when next level values are defined",
			ERROR_IN_LEVELS_FOR_LOW_SUPPRESSION_VALUES:"Low suppression value for higher levels cannot be empty or 0 when next level values are defined",
		},

		emailSMSSettings:{
			REQUIRED_SMTP_IP_ADDRESS:"SMTP IP Address cannot be empty",
			REQUIRED_SMTP_PORT:"SMTP port cannot be empty",
			REQUIRED_FROM_EMAILID:"From recepient email-id cannot be empty",
			INVALID_SMTP_IP_ADDRESS:"Invalid IP address format",
			INVALID_SMTP_PORT: "SMTP port should be in the range (0 to 65535)",
			INVALID_FROM_RECEPIENT_EMAILID: "Email-id format should be valid email format eg: <EMAIL> ",
			ERROR_GET_SECURITY_TYPES:"Error in getting smtp security types",
			ERROR_GET_MAIL_SERVER_SETTINGS:"Error in getting email server settings",
			ERROR_GET_SMS_SERVER_SETTINGS:"Error in getting sms server settings",
			ERROR_GET_PROTOCOLS:"Error in getting protocols",
			ERROR_GET_REQUEST_METHODS:"Error in getting request methods",
			ERROR_GET_PARAM_TYPES:"Error in getting parameter types",
			ERROR_GET_SMS_PLACE_HOLDERS:"Error in getting placeholders",
			ERROR_ADD_MAIL_SERVER_SETTINGS:"Error in adding mail server setting",
			ERROR_UPDATE_MAIL_SERVER_SETTINGS:"Error in updating mail server setting",
			ERROR_ADD_SMS_SERVER_SETTINGS:"Error in adding sms server setting",
			ERROR_UPDATE_SMS_SERVER_SETTINGS:"Error in updating sms server setting",
			SUCCESS_ADD_MAIL_SERVER_SETTINGS:"Mail server settings added successfully.",
			SUCCESS_ADD_SMS_SERVER_SETTINGS:"SMS server added successfully.",
			SUCCESS_UPDATE_MAIL_SERVER_SETTINGS:"Mail server settings updated successfully.",
			SUCCESS_UPDATE_SMS_SERVER_SETTINGS:"SMS server settings updated successfully.",
			REQUIRED_RELATIVEURL:"Relative URL cannot be empty",
			ERROR_COUNTRY_CODE:"Country code should be in range (1 to 99999999)",
			DELETE_CONFIRM_USERPARAMS:"Are you sure you want to delete user defined parameter?",
			REQUIRED_USERNAME:"Please enter username",
			REQUIRED_PASSWORD:"Please enter password",
			ERROR_USERNAME_SSL:"Username should not exceed 45 characters in length",
			ERROR_PASSWORD_SSL:"Password should not exceed 60 characters in length",
			CONFIRM_PROTOCOL_CHANGE : "User parameter list will be cleared due to change in protocol selection.Are you sure you want to proceed with the change?",
			ERROR_IN_PARAM_NAME_LENGTH:"Parameter name field length should not exceed 127 characters",
			ERROR_IN_PARAM_VALUE_LENGTH:"Parameter value field length should not exceed 127 characters",
		},

		//Global Search
		globalSearch:{
			ENTER_SEARCH_TXT: "Please enter search keyword",
			ERROR_GET_SEARCH_RESULTS: "Error in getting search results!"
		},

		//GRPC Settings
		grpcSettings:{
			GRPC_SETTINGS_NOT_CONFIGURED : "GRPC Settings not configured",
			GRPC_SETTINGS_NAME_MIN_LENGTH_ERROR : "GRPC Settings Name should at least contain 2 characters",
			GRPC_SETTINGS_NAME_MAX_LENGTH_ERROR : "GRPC Settings Name should not exceed 45 characters",
			ERROR_GET_GRPC_SETTINGS: "Error in getting GRPC Settings!",
			ERROR_GET_DATA_COMM_HOSTS: "Error in Data Communication Hosts!",
			SELECT_GRPC_HOST: "Select Host",
			ENTER_SELECT_GRPC_HOST: "Enter/Select Host",
			ERROR_ADD_GRPC_SETTINGS : "Error in adding GRPC Settings!",
			SUCCESS_ADD_GRPC_SETTINGS : "GRPC Settings added successfully.",
			ERROR_UPDATE_GRPC_SETTINGS : "Error in updating GRPC Settings!",
			SUCCESS_UPDATE_GRPC_SETTINGS : "GRPC Settings updated successfully.",
			ERROR_GET_GRPC_SETTINGS_TAGS : "Error in getting GRPC Settings tags!",
			GRPC_SETTINGS_NAME_REQUIRED : "GRPC Settings Name is required",
			GRPC_SETTINGS_NAME_INVALID_ERROR : "GRPC Settings Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			GRPC_SETTINGS_NAME_MIN_LENGTH_ERROR : "GRPC Settings Name should at least contain 2 characters",
			GRPC_SETTINGS_NAME_MAX_LENGTH_ERROR : "GRPC Settings Name should not exceed 45 characters",
			ENTER_SELECT_DATA_COMM_HOST: "Please enter/select Data Communication Host",
			ENTER_DATA_COMM_PORT: "Data Communication Port is required",
			COM_PROTOCOL_PORT_INVALID : "Data Communication Port should be in the range (0 to 65535)"
		},

		//KPI Group
		kpiGroupConfig:{
			KPI_GROUP_NAME_REQUIRED : "KPI Group Name is required",
			KPI_GROUP_NAME_INVALID_ERROR : "KPI Group Name should contain alphanumeric characters (a-z, A-Z, 0-9) without consecutive '-', '_' or space",
			KPI_GROUP_NAME_MIN_LENGTH_ERROR : "KPI Group Name should at least contain 2 characters",			TIME_PROFILE_NAME_MAX_LENGTH_ERROR : "Profile Name should not exceed 45 characters",
			KPI_GROUP_NAME_MAX_LENGTH_ERROR : "KPI Group Name should not exceed 45 characters",
			ERROR_GET_KPI_GROUPS : "Error in getting KPI Groups!",
			KPI_GROUPS_NOT_CONFIGURED : "KPI Groups not configured",
			ERROR_ADD_KPI_GROUP : "Error in adding KPI group!",
			SUCCESS_ADD_KPI_GROUP : "KPI group added successfully.",
			ERROR_UPDATE_KPI_GROUP : "Error in updating KPI group!",
			SUCCESS_UPDATE_KPI_GROUP : "KPI group updated successfully.",
			ERROR_GET_KPI_GROUP_TAGS : "Error in getting KPI group tags!",
			SELECT_KPI_GROUP_TYPE_MSG: "Please select KPI Group Type",
			INVALID_REGEX_PATTERN: "Invalid Regex pattern"
		}
	};
