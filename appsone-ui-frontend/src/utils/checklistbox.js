/*
  Name: checklistbox
  Author: <PERSON><PERSON><PERSON><PERSON>d<PERSON>
*/

(function($){
    
    jQuery.fn.checklistbox = function(listDetails){
    	var container = $(this);
    	

    	(function initCheckListBox() {
                var checkListHtml = "<div class='multiselect'>";
                var checklistStyleClass = "";

                for(obj in listDetails.data){
                    checklistStyleClass = ""
                    /*if(listDetails.data[obj].hasOwnProperty("isActive") && (listDetails.data[obj].isActive == 0 || listDetails.data[obj].isActive == false)){
                        checklistStyleClass = " class='inactiveOptionClass' ";
                    }
*/
                checkListHtml += "<label" + checklistStyleClass + " title='"+listDetails.data[obj].name+"'><input type='checkbox' class='checkList'" + "value='" + listDetails.data[obj].id +"' name='" + listDetails.data[obj].name + "'/>" + (listDetails.data[obj]["imageSrc"] ? "<img src='"+listDetails.data[obj]["imageSrc"]+"' title='" + (listDetails.data[obj].tooltipText || listDetails.data[obj].name) +"' style='padding-right: 3px;'>" : "") + listDetails.data[obj].name + "</label>"
            }
            checkListHtml += "</div>";
            container.empty().append(checkListHtml);

        })();
        
        jQuery.fn.getSelectedValues = function() {
            var values = [];
            $(this).find('input:checked').each(function() {
                values.push(this.value);
            });

            return values;
        };

        jQuery.fn.getSelectedItems = function() {
            var items = [];

            $(this).find('input:checked').each(function() {
                items.push({"value":this.value,"label":this.name});
            });

            return items;
        };

        jQuery.fn.getAllValues = function() {
            var values = [];
            $(this).find('input:checkbox').each(function() {
                values.push(this.value);
            });

            return values;
        };

        jQuery.fn.getIdsNames = function() {
            var items = [];

            $(this).find('input:checkbox').each(function() {
                items.push({"id":this.value,"name":this.name});
            });

            return items;
        };
    };
})(jQuery);
