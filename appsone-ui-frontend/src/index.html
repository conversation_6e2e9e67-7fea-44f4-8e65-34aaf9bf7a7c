<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, 
    user-scalable=no">
    <link rel="shortcut icon" href="images/favicon.ico" />
    <title>Appsone</title>
    <!-- build:css -->      
    <link href="bower_modules/c3/c3.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/bootstrap/dist/css/bootstrap.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/bootstrap-switch/dist/css/bootstrap3/bootstrap-switch.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/bootstrap-tokenfield/docs-assets/css/docs.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/bootstrap-tokenfield/docs-assets/css/pygments-manni.css" rel="stylesheet" type="text/css">

    <link href="bower_modules/bootstrap-tokenfield/dist/css/tokenfield-typeahead.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/bootstrap-tokenfield/dist/css/bootstrap-tokenfield.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/jquery-ui/themes/base/jquery-ui.min.css" rel="stylesheet" type="text/css">
    
    <link href="bower_modules/chosen/chosen.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/highchecktree/css/highchecktree.css" rel="stylesheet" type="text/css">

    <link href="bower_modules/gridstack/dist/gridstack.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/gridstack/dist/gridstack-extra.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/tooltipster/dist/css/tooltipster.bundle.min.css" rel="stylesheet" type="text/css">
    <link href="bower_modules/eonasdan-bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css" rel="stylesheet" type="text/css">
    <link href="css/icon.css" rel="stylesheet" type="text/css">
    <link href="css/styles.css" rel="stylesheet" type="text/css">
    <link href="css/checklistbox.css" rel="stylesheet" type="text/css">
    <link href="css/appnomic.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="bower_modules/components-font-awesome/css/font-awesome.min.css">
    <link rel="stylesheet" href="bower_modules/select2/dist/css/select2.min.css">
    <!-- endbuild -->
    <!-- <link rel="stylesheet" type="text/css" media="print" href="bootstrap.min.css"> -->
    
  </head>
  <body>
    <div id="wrapper" class="scrollbox" style="height: 100%;">
      <!-- <nav-bar params="route: route"></nav-bar> -->
      <top-bar></top-bar>
      <side-bar></side-bar>

      <!--Loading animation  -->     
      <div style="display:none;" class="loadingContainer">
        <span class="loading style-2"></span><h3>Loading...</h3>
      </div>   
      <!--Loading animation end -->
      
      <!-- <side-bar-collapsible></side-bar-collapsible> -->

      <div id="page" class="container" data-bind="component: { name: route().page, params: route }"></div>
    </div>

    <div id="loginModal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" style="z-index: 9999999">
      <div class="modal-dialog modal-sm">

        <div class="modal-content">
           <div class="modal-header" style="display:none;">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
          </div>
          <div class="modal-body">
            <div class="row">
              <!-- <div class="logo_cont col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div>
                  <span>AppsOne Logo</span>
                  
                </div>        
              </div> -->
              <div class="ctrl_cont col-lg-12 col-md-12 col-sm-12 col-xs-12">
                <div class="header_cont">
                  <span class="glyphicon glyphicon-user"></span>
                  <span>Member Login</span>
                </div>
                <div class="input_cont">
                  <input type="text" id="textUsername" placeholder="User Name" autofocus/>
                  <input type="password" id="textPassword" placeholder="Password"/>
                </div>
                <div class="link_cont">
                  <div><a href="#login" id="linkForgotPassword" style="text-align:right;" data-bind="click: window.onForgotPasswordClick">Forgot Password?</a></div>
                </div>        
                <div class="btn_cont">
                  <button type="button" class="btn btn-default" id="btnLogin" onclick="validateLogin(textUsername, textPassword, errorMessage)">Login</button>
                </div>
                <div class="small" style="color:rgb(174, 93, 93);margin-top:2%;">
                  <label id="errorMessage"></label>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div id="idleWarningModal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog">
        <div class="modal-content">
          <div class="modal-header" style="visibility: hidden;">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
          </div>
          <div class="modal-body">
             <p>You are about to be logged out of session due to idle state!</p>
              <p>Click on 'Continue' to retain the session.</p>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-default" onclick="startIdleHandler()" data-dismiss="modal">Continue...</button>
          </div>
        </div>
      
      </div>
    </div>


  <div id="messageDialogBox" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false" style="z-index: 99999999;">
      <div class="modal-dialog">
        <div class="modal-content" style="width: 450px; margin: 0 auto;">
          <div class="modal-header" style="background-color: lightblue; ">
            <button type="button" class="close" data-dismiss="modal" style="opacity: 1">&times;</button>
          </div>
          <div class="modal-body">

            <span id="msgSign" style="vertical-align: top; font-size: 25px; position: absolute; top: 14px;"></span>
            <p id="messageCont" style="vertical-align: top; padding-left: 30px; -webkit-box-decoration-break: clone;"></p>
          </div>

          <div class="modal-footer" style="padding-top: 5px; padding-bottom: 5px; border-top: 0px;">
            <button id="okBtn" type="button" class="btn btn-default" data-dismiss="modal">OK</button>
            <button id="yesBtn" type="button" class="btn btn-default" style="display: none" data-dismiss="modal" onclick="onConfirmYes()">Yes</button>
            <button id="noBtn" onclick="onConfirmNo()" type="button" class="btn btn-default" style="display: none" data-dismiss="modal">No</button>
          </div>
        </div>
      </div>
    </div>


<!--   <div id="forgotPasswordModal" class="modal fade" role="dialog" data-backdrop="static" data-keyboard="false">
      <div class="modal-dialog modal-sm">

        <div class="modal-content">
           <div class="modal-header" style="display:none;">
            <button type="button" class="close" data-dismiss="modal">&times;</button>
          </div>
          <div class="modal-body">
            <div class="row">
              <span>Member Login</span>
            </div>
          </div>
        </div>
      </div>
    </div> -->

  <!-- build:js -->
  <script src="app/require.config.js"></script>
  <script data-main="app/startup" src="bower_modules/requirejs/require.js"></script>
  <!-- endbuild -->
  </body>
</html>