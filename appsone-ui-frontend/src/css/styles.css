CSS Reset start*/
/*h1, h2, h3, h4, h5, h6,*/
html, body, div, span, applet, object, iframe,
p, blockquote, pre, a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed, 
figure, figcaption, footer, header, hgroup, 
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font-size: 100%;
  font: inherit;
  vertical-align: baseline;
}
/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure, 
footer, header, hgroup, menu, nav, section {
  display: block;
}

ol, ul {
  list-style: none;
}
blockquote, q {
  quotes: none;
}
blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}
table {
  border-collapse: collapse;
  border-spacing: 0;
  table-layout: fixed;  
}

.table>thead>tr>th {
    vertical-align: middle;
}

.table>tbody>tr>td{
  vertical-align: middle;
}

.table-responsive {
    overflow-x: visible;
}

.inner-div-container {
    height: 180px;
    overflow-y: scroll;
    overflow-x:hidden;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.inner-div-container-fixed {
    height: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.inner-div-data-content{
    position:absolute;
}

.a1-inner-table{
    border: 1px solid #ddd;
}

.a1-inner-table-thead{
    background-color: lightgrey;
}

@font-face {
  font-family: "arial";
  /*src: url(fonts/arial/arial.ttf);*/
  /*src: url(fonts/arial/arial.ttf);*/
}

html, body{
  /*font-family: "arial" !important;*/ 
  /*font-family: "Helvetica Neue",Helvetica,Arial,sans-serif;*/
  font-family: "arial" !important;
}
a {
    color: #428BCA;
}
a:hover{
    cursor: pointer;
}
label{
  margin-bottom: 0;
}
/*CSS Reset end*/

body{
  background-color: #EEEEEE;
}
.container{
  padding-left: 10px;
  padding-right: 10px;
  padding-top: 70px;
  margin-left: 0;
  margin-right: 0;
  width: 100%;
}

.tokenfield {
    height: auto;
    min-height: 34px;
    padding-bottom: 0;    
    width: auto;
}

.uncommontokenfield {
  color: #8B4513;
}

/*for disabled element*/
.disabled{
    cursor: not-allowed;
    filter: alpha(opacity=65);
    -webkit-box-shadow: none;
    box-shadow: none;
    opacity: .20;
    pointer-events: none;
}

/*for grid remove icon */
.gridRemove{
  border: none;
  background-color: transparent;
  box-shadow: none;
}
/*mandatory field starts */
.mandatoryField {
  color:red;
}
/*mandatory field starts */
.errorMessageField {
  color:red;
  /*margin-left: 10px;*/
}

.aoneErrorMessage{
  border-color: red;
  border-style: solid;
  border-width: 2px;
}
/*Text overflow ommiter(...)*/
.textOverflowOmmiter{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.tableHeaderOverflowOmmiter{
    overflow: hidden;
    text-overflow: ellipsis;
}


.indexCol{
  text-align: center;
}
.chosen-container a{
  background-color: transparent;
  border: none;
  background: transparent !important;
  border: 1px solid transparent !important;
  box-shadow: none !important;
}
.chosen-container{
    height: 34px;
    width: 100% !important;
    padding: 2px 6px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/***********************************************************************************************
Loading Style Start
************************************************************************************************/

.loadingContainer{
  position: fixed;
  z-index: 1000;
  top: 50%;
  left: 50%;
  opacity:0.7;
  text-align: center;  
}
.loading {   
  display: inline-block;
  border-width: 30px;
  border-radius: 50%;
  opacity: 0.7;
  /*margin: 15%;*/
  -webkit-animation: spin 1s linear infinite;
     -moz-animation: spin 1s linear infinite;
       -o-animation: spin 1s linear infinite;
          animation: spin 1s linear infinite;
  }

.style-2 {
  border-style: double;
  border-color: #FFF transparent;
  background-color: #333;
  }

@-webkit-keyframes spin {
  100% { -webkit-transform: rotate(359deg); }
  }

@-moz-keyframes spin {
  100% { -moz-transform: rotate(359deg); }
  }

@-o-keyframes spin {
  100% { -moz-transform: rotate(359deg); }
  }

@keyframes spin {
  100% {  transform: rotate(359deg); }
 }

/*Loading animation end*/

/***********************************************************************************************
POD Loading Style Start
************************************************************************************************/
.podLoadingCont{
  margin: 10% 0;
}
#cupcake{
flex-direction:row;
-webkit-flex-direction:row;
-ms-flex-direction:row;
-mos-flex-direction:row;
-o-flex-direction:row;
justify-content:center;
-webkit-justify-content:center;
-ms-justify-content:center;
height:100%;
width:100%;
/* background-color:#FFD454 */;    
}

.letter{
    font-size:30px;
    color:#222;
    font-family:tahoma;
}

.box{
    display: box;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}
.apps{
  color:#0080ff;
}

.one{
  color:#00b300;
}
.cupcakeCircle, .cupcakeInner, .cupcakeCore{
     border-radius:50%;
    -webkit-border-radius:50%;
    -moz-border-radius:50%;
    -ms-border-radius:50%;
}

.cupcake, .letter, .cupcakeCircle, .cupcakeInner, .cupcakeCore{
    flex:none;
    -webkit-flex:none;
    -moz-flex:none;
    -ms-flex:none;
    -o-flex:none;
}

.letter, .cupcakeCircle{
    align-self:center;
    -webkit-align-self:center;
    -moz-align-self:center;
    -o-align-self:center;
    -ms-align-self:center;
}
.cupcakeCircle{
    align-items:center;
    -ms-align-items:center;
    justify-content:center;
    -ms-justify-content:center;
    height:50px;
    width:50px;
    background-color:#0080ff;
}

.cupcakeInner{
    align-self:center;
    -ms-align-self:center;
    justify-content:center;
    -ms-justify-content:center;
    height:50%;
    width:50%;
    background-color:#00b300;
    -webkit-animation-name:cupcakeAnimate;
    -webkit-animation-duration:500ms;
    -webkit-animation-direction:alternate;
    -webkit-animation-timing-function:ease-in-out;
    -webkit-animation-iteration-count:infinite;
    
}
.cupcakeCore{
    align-self:center;
    -ms-align-self:center;
    height:25%;
    width:25%;
    background-color:#fff;
    -webkit-animation-name:coreAnimate;
    -webkit-animation-duration:1s;
    -webkit-animation-direction:alternate;
    -webkit-animation-timing-function:ease-in-out;
    -webkit-animation-iteration-count:infinite;
}

@-webkit-keyframes cupcakeAnimate{
        to{ height:90%; width:90%; }
}
@keyframes cupcakeAnimate{
    to{ height:90%; width:90%; }
}

@-webkit-keyframes coreAnimate{
    to{ height:90%; width:90%; }
}
@keyframes coreAnimate{
    to{ height:90%; width:90%; }
}


/*POD Loading animation end*/

/****************************************************************************************************************************
Pod panel Start
******************************************************************************************************************************/
#podCont .podListItems{
  border-top: 1px solid transparent;
}
.pod{
  /*margin-bottom: 30px !important;*/
  height: 100%;
  padding: 10px;

}
.pod .panel-heading{
  cursor: pointer;
  padding: 0px 0px;
  background-color: transparent;
}
.pod .panel-heading .panel-title div .clspan{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: inline-block;
    width: 60%;
    font-size: 0.9em;
}
.pod .panel-heading .panel-title div .symbols{
  float: right;
  width: 40%;
  display:inline-block;
  text-align: right;
  padding: 0 !important;
}
.pod .panel-heading .panel-title div .symbols li{  
  display: inline-block;
  font-size: 0.8em;
}
.pod .panel-heading .panel-title div .symbols li .dropdown-menu li, .pod .panel-heading .panel-title div .symbols li .dropdown-menu li .dropdown-menu li{ 
  display: inline-block;  
  margin: 2px;
}
.panel-heading-sel-item{
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  padding-left: 5px;
  font-weight: normal;
}
.panel
  margin-bottom: 0;
}
.pod-open{
  overflow-y: hidden !important;
  /*opacity: 0.5;*/
}
.panel-fullscreen {
    display: block !important;
    position: fixed !important;
    z-index: 9999 !important;
    width: 98% !important;
    height: 98% !important;
    top: 1% !important;
    right: 1% !important;
    left: 1% !important;
    bottom: 1% !important;
    overflow-x: hidden !important; 
    overflow-y: hidden !important;
}
/*Grid stack start*/
/*Default Demo*/

.grid-stack-item-content {
    display: block;
    margin: 20px 0 10px;
    overflow-x: hidden !important;
    overflow-y: hidden !important;
    left: 5px !important;
    right: 5px !important;
}

header a, header a:hover { color: #fff; }

.podbody {
   height: 100% !important;
   width: 100% !important;
   padding: 0px;
   overflow-x: auto !important;
   overflow-y: auto !important;
   /*overflow: auto !important;*/
}
.podbody>.panel-body {
   height: 100%;
   width: 100%;
   padding: 0px;
}

.inner-panel-body{
  padding-left: 0px;
  padding-right: 0px;
  margin-left: -1px;
  margin-right: -1px;
}

.DBPod:active {
    border: 1px dashed #333;
}
.panelbody{
  /*height: 100%;
  width: 100%;
  overflow-x: hidden !important;
  overflow-y: hidden !important;*/

}

.panel-label-expand-collapse {
  font-family: arial;
  margin-left: 5px;
}

div.table-responsive  table th{
  background-color: #4D6E75;
  color: #ffffff;
  margin-top: 10px;
}

div.table-responsive  table td,
div.table-inner-panel table td{
  padding-left: 2px;
  padding-right: 2px;
}

.unselect-all-check{
  vertical-align: middle !important;
  text-align: center;
}

.podListContainer{
  margin-bottom: 20px;
}
/*Grid stack end*/

/*POD Modal start*/

.podModalBody{
    height: 94%;
    overflow-x: hidden !important;
    overflow-y: auto !important;
}

/*POD Modal end*/

/*Pod Panel end*/


/****************************************************************************************************************************
Login Styles Start
******************************************************************************************************************************/

/* Large desktop */
@media (min-width: 1250px) {
  .login_container{ 
    top: 5em;
    transform: translateY(5em);
    -ms-transform: translateY(5em);
    -moz-transform: translateY(5em);
    -webkit-transform: translateY(5em);
    -o-transform: translateY(5em);
  }
}
 
/* Portrait tablet to landscape and desktop */
@media (min-width: 768px) and (max-width: 979px) {
  .login_container{ 
    top: 3em;
    transform: translateY(3em);
    -ms-transform: translateY(3em);
    -moz-transform: translateY(3em);
    -webkit-transform: translateY(3em);
    -o-transform: translateY(3em);
  }
}
 
/* Landscape phone to portrait tablet */
@media (max-width: 767px) {
  .login_container{ 
    top: 2em;
    transform: translateY(2em);
    -ms-transform: translateY(2em);
    -moz-transform: translateY(2em);
    -webkit-transform: translateY(2em);
    -o-transform: translateY(2em);
  }
}
 
/* Landscape phones and down */
@media (max-width: 480px) {
  .login_container{ 
    top: 1em;
    transform: translateY(1em);
    -ms-transform: translateY(1em);
    -moz-transform: translateY(1em);
    -webkit-transform: translateY(1em);
    -o-transform: translateY(1em);
  }
}

.login_sub_container{
  /*padding: 2% 3%;*/
  margin-left: -2%;
  background-color: #FFFFFF;
  border-radius: 10px 10px 10px 10px;
  -moz-border-radius: 10px 10px 10px 10px;
  -webkit-border-radius: 10px 10px 10px 10px;
  border: 1px solid #000000;
}
.ctrl_cont div{
  width: 100%;
}
.logo_cont div{
  /*font-size: 2em;
  border: 1px solid #333;*/
  text-align: center;
  padding: 2%;
}
.header_cont{
  position: relative;
  margin-top: 4%;
}
/*.header_cont span:nth-child(1){
  font-size: 2em;
  color: #C5C5C5;
}*/
.header_cont span:nth-child(1){
  font-size: 1.5em;
  margin-left: 3%;
}
.input_cont input{
  margin-top: 4%;
  width: 100%;
  padding-left: 5%;
  height: 2em;
}
.loginInput {
    display: block;
    overflow: hidden;

}
.loginMandatoryField {
    float: right;
}

.link_cont{
  margin-top: 4%;
}
.link_cont div a{
  float: left;
  width: 50%;
  margin-bottom: 5%;
}
.btn_cont{
  text-align: right;
}
.btn_cont button{
  border-radius: 5px;
  width: 35%;
}
.reset-password-cont{
  margin-top: 15px;
  text-align: right;
}
.actionControl {
    width: 35px;
    text-align: center !important;
}
.glyphicon {
    top: 0px;
    cursor: pointer;
}
.glyphicon-ok {
    color: #009c00;
}
.glyphicon-remove {
    color: #E40101;
}
.glyphicon-edit {
    color: #0000DC;
}
.glyphicon-filter:before,
.glyphicon-refresh:before,
.glyphicon-repeat:before {
    color: #008000;
    cursor: pointer;
}

.glyphicon-refresh-white:before {
    color: #FFFFFF;
     cursor: pointer;
}

.panel .block-glyphicon {
    color: #FFFFFF;
    text-decoration: none;
}
.a1-glyphicon-action-btn {
    font-size: 12px;
    color: #FFFFFF;
}
.glyphicon-plus {
  font-size: 12px;
}
.btn-small {
    display: inline-block;
    /* padding: 6px 12px; */
    margin-bottom: 3px;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.42857143;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px;
}

.input-group-btnadd, .input-group-btndelete, .input-group-btndollar {
    padding-left: 5px;
    padding-right: 5px;
    background: transparent;
    border: 0px;
    padding-right: 1px;
}
.input-grp-label {
    background: transparent;
    border: 0px;
    font-size: 13px;
    text-align: right;
    padding-right: 2px;
}
/*Login Styles End*/

/****************************************************************************************************************************
Tiles Page Styles Start
******************************************************************************************************************************/
.tile_container{
  /*padding: 2% 10%;*/
}
.tile_items{
    height: 10em;
    margin: 1em;
    border: 1px solid #333;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
}

/****************************************************************************************************************************
Top Bar Styles Start
******************************************************************************************************************************/
.dashboard ul li span:nth-child(1){
  margin-right: 10px;
}
.navbar-brand {
  padding: 0px;
}
.navbar-brand>img {
  height: 100%;
  padding: 8px;
  width: auto;
}

.navbar-inverse {
    /*background-color: #EDEDED;
    border-color: #EDEDED;*/
}
/* adjust body when menu is open */
body.slide-active {
    overflow-x: hidden
}
/*first child of #page-content so it doesn't shift around*/
.no-margin-top {
    margin-top: 0px!important
}
/*wrap the entire page content but not nav inside this div if not a fixed top, don't add any top padding */
#page-content {
    position: relative;
    padding-top: 70px;
    left: 0;
}
#page-content.slide-active {
    padding-top: 0
}


/* put toggle bars on the left :: not using button */
#slide-nav .navbar-toggle {
    cursor: pointer;
    position: relative;
    line-height: 0;
    float: left;
    margin: 0;
    width: 30px;
    height: 40px;
    padding: 10px 0 0 0;
    border: 0;
    background: transparent;
}
/* icon bar prettyup - optional */
#slide-nav .navbar-toggle > .icon-bar {
    width: 100%;
    display: block;
    height: 3px;
    margin: 5px 0 0 0;
}
#slide-nav .navbar-toggle.slide-active .icon-bar {
    background: orange
}
.navbar-header {
    position: relative;
    background-color: #ffffff;
}
/* un fix the navbar when active so that all the menu items are accessible */
.navbar.navbar-fixed-top.slide-active {
    position: relative
}
/* screw writing importants and shit, just stick it in max width since these classes are not shared between sizes */
@media (max-width:767px) { 
  #slide-nav .container {
      margin: 0!important;
      padding: 0!important;
      height:100%;
  }
  #slide-nav .navbar-header {
      margin: 0 auto;
      padding: 0 15px;
  }
  #slide-nav .navbar.slide-active {
      position: absolute;
      width: 80%;
      top: -1px;
      z-index: 1000;
  }
  #slide-nav #slidemenu {
      background: #f7f7f7;
      left: -100%;
      width: 50%;
      min-width: 0;
      position: absolute;
      padding-left: 0;
      z-index: 2;
      top: -8px;
      margin: 0;
  }
  #slide-nav #slidemenu .navbar-nav {
      min-width: 0;
      width: 100%;
      margin: 0;
  }
  #slide-nav #slidemenu .navbar-nav .dropdown-menu li a {
      min-width: 0;
      width: 80%;
      white-space: normal;
  }
  #slide-nav {
      border-top: 0
  }
  #slide-nav.navbar-inverse #slidemenu {
      background: #333
  }
  /* this is behind the navigation but the navigation is not inside it so that the navigation is accessible and scrolls*/
  #navbar-height-col {
      position: fixed;
      top: 0;
      height: 100%;
        bottom:0;
      width: 80%;
      left: -80%;
      background: #f7f7f7;
  }
  #navbar-height-col.inverse {
      background: #333;
      z-index: 1;
      border: 0;
  }
  #slide-nav .navbar-form {
      width: 100%;
      margin: 8px 0;
      text-align: center;
      overflow: hidden;
      /*fast clearfixer*/
  }
  #slide-nav .navbar-form .form-control {
      text-align: center
  }
  #slide-nav .navbar-form .btn {
      width: 100%
  }
}
@media (min-width:768px) { 
  #page-content {
      left: 0!important
  }
  .navbar.navbar-fixed-top.slide-active {
      position: fixed
  }
  .navbar-header {
      left: 0!important
  }
}

/*Top Bar Styles End*/




/****************************************************************************************************************************
Side Bar Styles Start
******************************************************************************************************************************/

.sideBar_container
{
    /*width:800px;
    overflow:hidden;*/
    /*display:inline-block;*/
    padding-left: 15px;
    padding-right: 15px;
    margin-left: auto;
    margin-right: auto;
    display: block;
}
.side-bar
{
    background:rgba(34, 34, 34, 0.68);
    position:fixed;
    height:auto;
    width:200px;
    top: 50px;
    /*left: 50px;*/
    color:#fff;
    transition: margin-left 0.5s;
    z-index: 999999999;
}

.side-bar ul
{
    list-style:none;
    padding:0px;
    cursor: pointer;
}

.side-bar ul li.menu-head
{
    font-family: 'Lato', sans-serif;
    padding:20px;
    font-size: 1.5em;
}


.side-bar ul li.menu-head a
{
    color:#fff;
    text-decoration:none;
    height:50px;
}


.side-bar ul .menu-head  a
{
    color:#fff;
    text-decoration:none;
    height:50px;
}

.side-bar ul .menu li  a 
{
    color:#fff;
    text-decoration:none;
    display:inline-table;
    width:100%;
    padding-left:20px;
    padding-right:20px;
    padding-top:10px;
    padding-bottom:10px;
}

.side-bar ul .menu li  a:hover
{
    border-left:3px solid #ECECEA;    
    padding-left:17px;
}

.side-bar ul .menu li  a.active
{
    padding-left:17px;
    background:#222;
    border-left:3px solid #ECECEA;
}

.side-bar ul .menu li  a.active:before {
   content:"";
   position: absolute;
   width: 0;
   height: 0;
   border-top: 20px solid transparent;
   border-bottom: 20px solid transparent;
   border-left: 7px solid #222;
   margin-top: -10px;
   margin-left: 180px;
}

.content
{
    padding-left: 200px;
    transition: padding-left 0.5s;
}

.active > .side-bar
{
    margin-left:-150px;   
    transition: margin-left 0.5s;
}

.active > .content
{
    padding-left:50px;
    transition: padding-left 0.5s;
}


/*Side Bar Styles End*/


/****************************************************************************************************************************
Side Bar 1 Styles Start
******************************************************************************************************************************/
body {
    position: relative;
    overflow-x: hidden;
}
body,
html { height: 100%;}
.nav .open > a, 
.nav .open > a:hover, 
.nav .open > a:focus {background-color: transparent;}

/*-------------------------------*/
/*           Wrappers            */
/*-------------------------------*/

#wrapper {
    padding-left: 0;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
}

#wrapper.toggled {
    padding-left: 220px;
}

.sidebar-wrapper {
    z-index: 1000;
    left: 220px;
    width: 0;
    height: 100%;
    margin-left: -220px;
    overflow-y: auto;
    overflow-x: hidden;
    background: #FFFFFF;
    -webkit-transition: all 0.5s ease;
    -moz-transition: all 0.5s ease;
    -o-transition: all 0.5s ease;
    transition: all 0.5s ease;
    position: absolute;
    padding-top: 70px;
    -webkit-box-shadow: 10px 0 5px -2px #888;
    box-shadow: 4px 0 6px -2px #888;
}

.sidebar-wrapper::-webkit-scrollbar {
  display: none;
}

#wrapper.toggled .sidebar-wrapper {
    width: 220px !important;
}

.toggledMenuList {
  display: inline-block !important;
}

#page-content-wrapper {
    width: 100%;
    padding-top: 70px;
}

#wrapper.toggled #page-content-wrapper {
    position: absolute;
    margin-right: -220px;
}

/*-------------------------------*/
/*     Sidebar nav styles        */
/*-------------------------------*/

.sidebar-nav {
    position: absolute;
    top: 0;
    width: 220px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.sidebar-nav li {
    position: relative; 
    line-height: 20px;
    display: inline-block;
    width: 100%;
}

.sidebar-nav li:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
    height: 100%;
    width: 3px;
    background-color: #1c1c1c;
    -webkit-transition: width .2s ease-in;
      -moz-transition:  width .2s ease-in;
       -ms-transition:  width .2s ease-in;
            transition: width .2s ease-in;

}
.sidebar-nav li:first-child a {
    color: #fff;
    background-color: #1a1a1a;
}
.sidebar-nav li:nth-child(2):before {
    background-color: #ec1b5a;   
}
.sidebar-nav li:nth-child(3):before {
    background-color: #79aefe;   
}
.sidebar-nav li:nth-child(4):before {
    background-color: #314190;   
}
.sidebar-nav li:nth-child(5):before {
    background-color: #279636;   
}
.sidebar-nav li:nth-child(6):before {
    background-color: #7d5d81;   
}
.sidebar-nav li:nth-child(7):before {
    background-color: #ead24c;   
}
.sidebar-nav li:nth-child(8):before {
    background-color: #2d2366;   
}
.sidebar-nav li:nth-child(9):before {
    background-color: #35acdf;   
}
.sidebar-nav li:hover:before,
.sidebar-nav li.open:hover:before {
    width: 100%;
    -webkit-transition: width .2s ease-in;
      -moz-transition:  width .2s ease-in;
       -ms-transition:  width .2s ease-in;
            transition: width .2s ease-in;

}

.sidebar-nav li a {
    display: block;
    color: #ddd;
    text-decoration: none;
    padding: 10px 15px 10px 30px;    
}

.sidebar-nav li a:hover,
.sidebar-nav li a:active,
.sidebar-nav li a:focus,
.sidebar-nav li.open a:hover,
.sidebar-nav li.open a:active,
.sidebar-nav li.open a:focus{
    color: #fff;
    text-decoration: none;
    background-color: transparent;
}

.sidebar-nav > .sidebar-brand {
    height: 65px;
    font-size: 20px;
    line-height: 44px;
}
.sidebar-nav .dropdown-menu {
    position: relative;
    width: 100%;
    padding: 0;
    margin: 0;
    border-radius: 0;
    border: none;
    background-color: #222;
    box-shadow: none;
}

/*-------------------------------*/
/*       Hamburger-Cross         */
/*-------------------------------*/

.hamburger {
  position: fixed;
  top: 20px;  
  z-index: 999;
  display: block;
  width: 32px;
  height: 32px;
  margin-left: 15px;
  background: transparent;
  border: none;
}
.hamburger:hover,
.hamburger:focus,
.hamburger:active {
  outline: none;
}
.hamburger.is-closed:before {
  content: '';
  display: block;
  width: 100px;
  font-size: 14px;
  color: #fff;
  line-height: 32px;
  text-align: center;
  opacity: 0;
  -webkit-transform: translate3d(0,0,0);
  -webkit-transition: all .35s ease-in-out;
}
.hamburger.is-closed:hover:before {
  opacity: 1;
  display: block;
  -webkit-transform: translate3d(-100px,0,0);
  -webkit-transition: all .35s ease-in-out;
}

.hamburger.is-closed .hamb-top,
.hamburger.is-closed .hamb-middle,
.hamburger.is-closed .hamb-bottom,
.hamburger.is-open .hamb-top,
.hamburger.is-open .hamb-middle,
.hamburger.is-open .hamb-bottom {
  position: absolute;
  left: 0;
  height: 4px;
  width: 100%;
}
.hamburger.is-closed .hamb-top,
.hamburger.is-closed .hamb-middle,
.hamburger.is-closed .hamb-bottom {
  background-color: #1a1a1a;
}
.hamburger.is-closed .hamb-top { 
  top: 5px; 
  -webkit-transition: all .35s ease-in-out;
}
.hamburger.is-closed .hamb-middle {
  top: 50%;
  margin-top: -2px;
}
.hamburger.is-closed .hamb-bottom {
  bottom: 5px;  
  -webkit-transition: all .35s ease-in-out;
}

.hamburger.is-closed:hover .hamb-top {
  top: 0;
  -webkit-transition: all .35s ease-in-out;
}
.hamburger.is-closed:hover .hamb-bottom {
  bottom: 0;
  -webkit-transition: all .35s ease-in-out;
}
.hamburger.is-open .hamb-top,
.hamburger.is-open .hamb-middle,
.hamburger.is-open .hamb-bottom {
  background-color: #1a1a1a;
}
.hamburger.is-open .hamb-top,
.hamburger.is-open .hamb-bottom {
  top: 50%;
  margin-top: -2px;  
}
.hamburger.is-open .hamb-top { 
  -webkit-transform: rotate(45deg);
  -webkit-transition: -webkit-transform .2s cubic-bezier(.73,1,.28,.08);
}
.hamburger.is-open .hamb-middle { display: none; }
.hamburger.is-open .hamb-bottom {
  -webkit-transform: rotate(-45deg);
  -webkit-transition: -webkit-transform .2s cubic-bezier(.73,1,.28,.08);
}
.hamburger.is-open:before {
  content: '';
  display: block;
  width: 100px;
  font-size: 14px;
  color: #fff;
  line-height: 32px;
  text-align: center;
  opacity: 0;
  -webkit-transform: translate3d(0,0,0);
  -webkit-transition: all .35s ease-in-out;
}
.hamburger.is-open:hover:before {
  opacity: 1;
  display: block;
  -webkit-transform: translate3d(-100px,0,0);
  -webkit-transition: all .35s ease-in-out;
}

/*-------------------------------*/
/*            Overlay            */
/*-------------------------------*/

.overlay {
    position: fixed;
    display: none;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(250,250,250,.8);
    z-index: 1;
}
/*Side Bar 1 Styles End*/


/****************************************************************************************************************************
Side Bar 2 Styles Start
******************************************************************************************************************************/
.mini-submenu{
  /*display:none;*/  
  display: inline-block;
  background-color: rgba(0, 0, 0, 0);  
  border: 1px solid rgba(0, 0, 0, 0.9);
  border-radius: 4px;
  padding: 9px;  
  /*position: relative;*/
  width: 42px;
}

.mini-submenu:hover{
  cursor: pointer;
}

.mini-submenu .icon-bar {
  border-radius: 1px;
  display: block;
  height: 2px;
  width: 22px;
  margin-top: 3px;
  background-color: #000;
}

/*.list-group {
  display:none;
  position: relative;
}*/

#slide-submenu{
  background: rgba(0, 0, 0, 0.45);
  display: inline-block;
  padding: 0 8px;
  border-radius: 4px;
  cursor: pointer;
}
/*Side Bar 2 Styles Start*/



/****************************************************************************************************************************
Home Page Styles Start
******************************************************************************************************************************/
.home_container{
  margin-left: -40px;
  /*margin-top: -20px;
  padding: 20px 0;*/
}

/*Home Page Styles End


/****************************************************************************************************************************
Pagination Styles Start
******************************************************************************************************************************/
.paginationLabel {
  font-size:0.8em;
  font-family:Arial;
}

.paginationFooterLabel {
  font-size:0.8em;
  font-family:Arial;
  font-weight: normal;
  font-style: italic;
}

.pageNumber{ 
    font-weight: bold;
    width: 3em;
    text-align: center;
 }

.paginationArrowDisabled{
    color: grey;
    cursor: not-allowed !important;
 }

.confButtonDisabled{
    color: grey;
    cursor: not-allowed !important;
 }

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    margin: 0; 
}

/*Pagination Styles End*/


/****************************************************************************************************************************
table Styles Start
******************************************************************************************************************************/
th {text-align:center}

.table-hover>tbody>tr:hover>td, .table-hover>tbody>tr:hover>th {
  background-color: #b9b9b9;
}
/*table Styles End*/

/****************************************************************************************************************************
form Styles Start
******************************************************************************************************************************/
.readonly{
  background-color: lightgrey;
  color: #000000;
}

.form-control{
  color: #000000;
}

.form-control[readonly] {
   background-color: lightgrey;
}

.form-control[disabled] {
   background-color: lightgrey;
   cursor: default;
}

.form-horizontal .checkbox, .form-horizontal .radio{
    min-height: 18px;
}

.form-horizontal .control-label{
  padding-top: 0px;
}

.checklist-disabled{
  background-color: lightgrey;
}

.checklist-div-table{
  table-layout: fixed;
  width: 100%;
}

.configPanel {
    background-color: #4D6E75 !important;
    color: #ffffff !important;
    padding: 2px 15px;
}

.inner-panel {
    margin-left: 10px;
    margin-right: 10px;
}

.inner-panel-form {
    margin-left: -20px !important;
}

.panel-expand-img{
   content: url(../images/expanded.png);
   cursor:pointer;
}

.panel-collapse-img{
   content: url(../images/collapsed.png);
   cursor:pointer;
}

.form-group-seperator {
    background:grey;
    border:0;
    height:1px;
    margin-top: 10px;
    margin-bottom: 10px;
}

.small-form-control {
     padding: 3px 6px;
     height: auto;
     border-radius: 4px !important;
}

.chosen-disabled {
   background-color: lightgrey;
   opacity: 1 !important;
}

.chosen-single {
  color: #000000 !important;
}

.chosen-container-single .chosen-single span {
  white-space: pre;
}

.inactiveOptionClass {
  color: lightslategray !important;
  font-style: italic;
}

.multi-value-row {
  display: inline;
  margin: 0 10px;
}

.form-sub-label {
  font-size: 12px;
}

fieldset.config-border {
    padding: 0.35em 1em .75em;
    border: 1px solid silver;
    margin-bottom: 10px;
}

legend.config-border {
    width: auto;
    font-size: inherit;
    border-bottom: 0;
    margin-bottom: 5px;
}

.config-option-label {
  font-weight: normal;
  padding-right: 10px;
}

.control-option-label{
  width: 120px;
}

/*form Styles End*/

/********************************************************************************************
Sub menu styles start
*********************************************************************************************/
.dropdown-submenu {
    position: relative;
}

.dropdown-submenu>.dropdown-menu {
    top: 0;
    left: 100%;
    margin-top: -6px;
    margin-left: -1px;
    -webkit-border-radius: 0 6px 6px 6px;
    -moz-border-radius: 0 6px 6px;
    border-radius: 0 6px 6px 6px;
}

.dropdown-submenu:hover>.dropdown-menu {
    display: block;
}

.dropdown-submenu>a:after {
    display: block;
    content: " ";
    float: right;
    width: 0;
    height: 0;
    border-color: transparent;
    border-style: solid;
    border-width: 5px 0 5px 5px;
    border-left-color: #ccc;
    margin-top: 5px;
    margin-right: -10px;
}

.dropdown-submenu:hover>a:after {
    border-left-color: #fff;
}

.dropdown-submenu.pull-left {
    float: none;
}

.dropdown-submenu.pull-left>.dropdown-menu {
    left: -100%;
    margin-left: 10px;
    -webkit-border-radius: 6px 0 6px 6px;
    -moz-border-radius: 6px 0 6px 6px;
    border-radius: 6px 0 6px 6px;
}

/*Sub menu styles end*/


/********************************************************************************************
Checkbox Tree styles start
*********************************************************************************************/
.checktree li label{
    font-weight: normal;
}

.checktree li .collapsed {
  background: url(../images/checktree_icon.png) no-repeat;
  background-position: 0px -22px;
}

.checktree li .expanded {
  background: url(../images/checktree_icon.png) no-repeat;
  background-position: -14px -22px;
}

.checktree li .checkbox {
  width: 18px;
  height: 18px;
  cursor: pointer;
  float: left;
  background: url(../images/checktree_icon.png) no-repeat;
  background-position: 1px 0;
  margin-right: 7px;
}

.checktree li .checked {
  background-position: -15px 0;
}

.checktree li .half_checked {
  background-position: -31px 0;
}

.checktree li li .arrow {
  margin-left: 24px;
}

.checktree li li li .arrow {
  margin-left: 48px;
}

.checktree li li li li .arrow {
  margin-left: 72px;
}

.checktree li li li li li .arrow {
  margin-left: 96px;
}

.checktree-container {
    height: 150px;
    overflow-y: scroll;
    overflow-x:hidden;
    border: 1px solid #ccc;
    border-radius: 4px;
}

/*Checkbox Tree styles end*/


/********************************************************************************************
Modal Window styles start
*********************************************************************************************/
.modal-header {
    padding: 5px 15px;
}

.modal-invoker-btn {
    color: green;
    margin-top: 3px;
}

.config-modal-height{
    min-height: 20em;
}
/*Modal Window styles end*/



/********************************************************************************************
Topology styles start
*********************************************************************************************/


.topologyCont{
  height: 100%;
  /*padding: 0 10%;*/
  display: flex;
  align-items:center;
  justify-content:center;
}
.legendCont{
  text-align: left;
  position: absolute;
}
.legendCont .label{
    display: block;
    margin: 10px 0;
    line-height: 1em;
}
.jqSimpleConnect{
  z-index: 1;
}
.topologyChildEntity{
  z-index: 1000;
  background-color: #FFF;
}

.entryCont{
  text-align: right;
}
.entryCont, .donutChartCont, .exitCont{
  float: left;
  width: auto;
  z-index: 1000;
}
.donutChartCont{
  margin: 0 5%;
  z-index: 1000;
}
.exitChildCont{
  margin: 0 0 0 10%;
  z-index: 1000;
}
.exitChildCont .topologyChildEntity{
  height: 4em;
  position: relative;
  margin:0;
  /*border: 1px solid #333;*/
}
.dataLabel{
  font-size: 0.8em;
}
.topologyChildEntityData{
  display: inline-block;
  vertical-align: middle;
}
.topologyChildEntity .violationColor, .entryCont .violationColor{
    color: #A5130E;
}
.popover-content{
  height: auto;
  max-height: 200px;
  overflow-y:auto;
}
.violatedCell{
    color: #d9534f;
    
}
/*Topology end*/


/********************************************************************************************
tablesorter styles start
*********************************************************************************************/

table.tablesorter thead tr .header {
  background-image: url(../images/bg.gif);
  background-repeat: no-repeat;
  background-position: center right;
  cursor: pointer;
}
table.tablesorter thead tr .headerSortUp {
  background-image: url(../images/asc.gif);
}
table.tablesorter thead tr .headerSortDown {
  background-image: url(../images/desc.gif);
}

table.tablesorter thead tr .header {
   padding-right: 20px;
}
/*tablesorter styles end*/


tbody.rowSpaceBottom > tr > td
{
  padding-bottom: 0.5em;
}

.hour-box, .minute-box {
  width: 50px;
  text-align: center;
}

.stepper .stepper-arrow.down {
    background-position: center bottom;
    bottom: 0;
}
.stepper .stepper-arrow {
    background: #eee url(../images/jquery.fs.stepper-arrows.png) no-repeat;
    border: 1px solid #ccc;
    cursor: pointer;
    display: block;
    height: 50%;
    position: absolute;
    right: 0;
    text-indent: -99999px;
    width: 20px;
    z-index: 50;
}

.field-note-msg {
    font-size: 13px;
    color: red;
}

.profile-tooltip-content > label,
.profile-tooltip-content > p {
    font-weight: normal;
}

.profile-tooltip-hr{
    margin-top: 1px;
    margin-bottom: 1px;
}

.text-two-line-ellipsis {
    word-wrap: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    line-height: 16px;     /* fallback */
    max-height: 32px;      /* fallback */
    -webkit-line-clamp: 2; /* number of lines to show */
    -webkit-box-orient: vertical;
}

tr.a1-border-bottom td {
    border-bottom:1pt solid black;
}

.glyphicon-question-style {
    color: #0011a0;
}

.modalPlaceholder {
    cursor: pointer;
}

.ui-autocomplete { z-index: 5000; }

.error-highlighter {
    border-color: red !important;
}

.error-span {
    position: fixed;
    top: 50px;
    background-color: #a00101;
    color: white;
    width: 100%;
    z-index: 9999;
    height: 33px;
    padding: 10px 10px 30px 10px;
}

.navbar-zindex {
  z-index: 99999 !important;
}

/*input:focus, select:focus, textarea:focus {
    border-color: transparent;
}*/

/********************************************************************************************
tablesorter styles start
*********************************************************************************************/
/*.tablesorter th,
.tablesorter td {
  border: #cdcdcd 1px solid;
  border-width: 0 1px 1px 0;
}*/

.tablesorter .header,
.tablesorter .tablesorter-header {
  background-image: url(../images/bg.gif);
  background-repeat: no-repeat;
  background-position: center right;
  padding: 4px 18px 4px 4px;
  white-space: normal;
  cursor: pointer;
  outline: none;
}

.tablesorter .headerSortUp,
.tablesorter .tablesorter-headerSortUp,
.tablesorter .tablesorter-headerAsc {
  background-image: url(../images/asc.gif);
}

.tablesorter .headerSortDown,
.tablesorter .tablesorter-headerSortDown,
.tablesorter .tablesorter-headerDesc {
  background-image: url(../images/desc.gif);
}

.tablesorter thead .sorter-false {
  background-image: none;
  cursor: default;
  padding: 4px;
}

.tablesorter-header-inner {
  overflow: hidden;
  text-overflow: ellipsis;
}

/*table.tablesorter thead tr .header {
  background-image: url(../images/bg.padding: );
  background-repeat: no-repeat;
  background-position: center right;
  cursor: pointer;
}
table.tablesorter thead tr .headerSortUp {
  background-image: url(../images/asc.png);
}
table.tablesorter thead tr .headerSortDown {
  background-image: url(../images/desc.png);
}*/

table.tablesorter thead tr .header {
   padding-right: 20px;
}
/*tablesorter styles end*/

.search-choice-close {
  visibility: hidden;
}

.glyphicon-style{
  position: inherit !important;
}

.table{
  margin-bottom: 0px;
}

.config-pagination-footer{
  padding-top: 22px;
  padding-bottom: 22px;
  margin-top: 2px;
  background-color: #FFFFFF;
  border-left: 1px solid rgb(221, 221, 221);
  border-right: 1px solid rgb(221, 221, 221);
  border-bottom: 1px solid rgb(221, 221, 221);
}

.wrapper {
    overflow-x: hidden;
    overflow-y: auto;
    width: 100%;
}

.readonly-element{
  background-color: transparent !important;
  border-color: transparent !important;
  box-shadow: 0px 0px !important;
}

.hide-downdown-arrow{
  display: none !important;
}

@media screen and (min-width: 500px) {
    .modal-lg {
        width: 90%;
    }
}

.table-control-label {
  width: 20%;
  display: inline-block;
}

.table-input {
  width: 75%;
  display: inline-block;
}

.table-div {
  width: 100%;
}

.chosen-min-dynamic-width {
  min-width: 200px;
  width: auto !important;
}

.chosen-dynamic-width {
  width: auto !important;
}

.timeUnitSelector {
  width: 60% !important;
}

.wrapper-scroll-table {
  overflow-x: auto;
  overflow-y: scroll;
  width: 100%;
}

.glyphicon-filter{
  padding: 0 0 0 5px;
  border: none;
  background: none;
}

.glyphicon-repeat {
    padding: 0 5px 0 0;
    border: none;
    background: none;
    -webkit-transform: matrix(-1, 0, 0, 1, 0, 0);
    -moz-transform: matrix(-1, 0, 0, 1, 0, 0);
    -o-transform: matrix(-1, 0, 0, 1, 0, 0);
    transform: matrix(-1, 0, 0, 1, 0, 0);
}

.chosen-container-listview {
  width: auto !important;
}

.columnsListContainer {
  position: absolute;
  display: none;
  background-color: white;
  padding-top: 2px;
  /*top: 25px;*/
  margin-top: -10px;
  right: 15px;
  border: 1px solid #888888;
  box-shadow: 3px 3px 1px #888888;
  z-index: 99999999;
}

.columnsList {
  text-align: left;
  background-color: white;
  color: black;
  padding: 5px 10px;
}

.columnsListSpan {
  padding-left: 10px;
  font-weight: bold;
  color: lightslategrey;
}

.listViewBtn {
  color: #4d4d4d;
  background-color: #f9fbfd;
  border-color: #BCBCBC;
  box-shadow: 0px 2px 1px #BCBCBC !important;
  border-radius: 2px;
  font-size: 13px;
  font-weight: bold;
  width: 100px;
  vertical-align: top;
}

.listViewBtnTxt {
    color: #4d4d4d !important;
    padding-right: 6px;
    font-weight: bold !important;
}

/*.btn-primary:hover {
  border-color: #BCBCBC;
  background-color: #89abc7 !important;
}
*/
.filterapplied {
  height: 38px;
  outline: none !important;
  border-bottom-style: none !important;
  box-shadow: none !important;
}

/*.btn-primary, .btn-primary:visited {
  color: #4d4d4d;
  border-color: #BCBCBC;
  background-color: #f9fbfd !important;

}
*/

.btn-primary, .btn-primary:visited {
  background-color: #347FDF;
  font-size: 13px;
  font-weight: bold;
  color: #FFFFFF;
}

.filterFieldWidth {
  width: 220px !important;
}

.a1-action-btn {
  color: #4d4d4d;
  background-color: #f9fbfd;
  border-color: #BCBCBC;
  box-shadow: 0px 2px 1px #BCBCBC !important;
  border-radius: 2px;
  font-size: 13px;
  font-weight: bold;
}

.a1-filter-box {
  padding: 7px;
  margin-bottom: 10px;
  border-color: #BCBCBC;
  border-style: solid;
  border-width: 1px;
  box-shadow: 0px 2px 1px #BCBCBC !important;
  background-color: #f9fbfd;
  margin-top: -7px;
  display: none;
}

.div-token {
  color: white;
  background-color: #666666 !important;
  opacity: 1 !important;
}

.tokenfield-container {
    background-color: #D7D8D8;
    padding-left: 3px;
    border: none;
}

.filter-table-td {
  background-color: #D7D8D8;
}

.a1-action-link {
  background-color: transparent;
  color: #337DDC;
  font-size: 12px;
  font-weight: bold;
}

.a1-options-display {
  width: 30px;
  color: #337DDC;
  text-align: center;
  cursor: pointer;
}

.a1-span-link {
  cursor: pointer;
  text-decoration: underline;
  color: #337DDC;
  font-size: 13px;
}

.a1-list-grid {
  color: #505050;
  /*border: 1px solid #ccc;*/
}

.a1-list-grid-header {
  background-color: #F5F6F6;
  margin-top: 10px;
  border: 1px solid #ccc;
  color: #505050;
}

.a1-page-title {
  color: #525fae;
}

.a1-list-grid-header tr th {
  text-align: left;
}

.bootstrap-datetimepicker-widget {
  z-index: 999999999999;
}

/*.ui-dialog{
    overflow: initial !important;
}*/

.navbar-inverse {
  background-color: #4080F5;
  border-color: #3c78d0;
}

.dropdown-toggle {
  color: #ffffff !important;
}

.menu-option {
  width: 35px;
  padding-top: 2px;
}

.menu-option-link {
  color: #000000;
}

.menu-option-div {
  margin-top: 15px;
}

.list-group.panel > .list-group-item {
  border-bottom-right-radius: 4px;
  border-bottom-left-radius: 4px;
  padding: 10px 0px !important;
}

.addOptionsContainer {
  position: absolute;
  display: none;
  background-color: white;
  padding-top: 2px;
  /*top: 25px;*/
  margin-top: -10px;
  right: 15px;
  border: 1px solid #888888;
  box-shadow: 3px 3px 1px #888888;
  z-index: 99999999;
}

.optionListButton {
  width: auto;
  border-left: none;
  background-color: #2968B3;
  height: 32px;
  color: #FFFFFF;
}

.addOptionsChoice {
  padding: 8px 16px;
  color: #66747B;
  cursor: pointer;
}

.optionListButton:hover {
  color: #FFFFFF;
}

/*.addOptionsChoice:hover {
    background-color: #B8B8B8;
    color: white;
}*/


.optionListButton>span{
    color: #FFFFFF;

}

.btn-primary > span {
  color: #FFFFFF !important;
}

.columnsSelectAll {
  padding-left: 15px;
  display: block;
  margin-bottom: 1px;
  font-weight: normal;
}

.horizRuleColumnsList {
  width: 100%;
  margin-top: 0px;
  margin-bottom: 0px;
  border-color: #ccc;
}

.modalInvokerDisabled{
    color: grey;
    cursor: not-allowed !important;
 }

 .divActionPanel > .btn-primary {
    font-size: 14px !important; 
    font-weight: 400;
 }

.select2-selection__arrow {
  padding-top: 3px;
}

#gridDataBody {
  border: 2px solid rgb(221, 221, 221);
  border-bottom: none;
}

.pageNumDropDown {
  border-bottom: 1px solid #aaa !important;
}

.a1LabelFooter {
  color: #606F75;
  margin-left: 15px;
}

.paginationPrev {
  color: #606F75;
  width: auto !important;
  margin-left: 22px;
  font-size: 22px !important;
  padding-right: 10px;
  cursor: pointer;
}

.paginationNext {
  color: #606F75;
  width: auto !important;
  font-size: 22px !important;
  cursor: pointer;
  margin-right: 22px;
}