// require.js looks for the following global when initializing
var require = {
    baseUrl: ".",
    paths: {
        "validator":            "utils/validation-helper",
        "ui-constants":         "utils/ui-constants",
        "ui-common":            "utils/ui-common",
        "checklistbox":         "utils/checklistbox",
        "c3":                   "bower_modules/c3/c3.min",
        "d3":                   "bower_modules/d3/d3.min",
        "bootstrap":            "bower_modules/bootstrap/dist/js/bootstrap.min",
        "bootstrap-switch":     "bower_modules/bootstrap-switch/dist/js/bootstrap-switch.min",
        "bootstrap-carousel":   "bower_modules/bootstrap/js/carousel",
        "crossroads":           "bower_modules/crossroads/dist/crossroads.min",
        "hasher":               "bower_modules/hasher/dist/js/hasher.min",
        "jquery":               "bower_modules/jquery/dist/jquery",
        "knockout":             "bower_modules/knockout/dist/knockout",
        "knockout-projections": "bower_modules/knockout-projections/dist/knockout-projections",
        "signals":              "bower_modules/js-signals/dist/signals.min",
        "text":                 "bower_modules/requirejs-text/text",
        "fusionCharts":         "bower_modules/fusioncharts/fusioncharts",
        "fusionthemes":         'bower_modules/fusioncharts/themes/fusioncharts.theme.fint',
        // "fusioncharts-widgets": "bower_modules/fusioncharts/fusioncharts.widgets",
        //"fusioncharts-charts":  "bower_modules/fusioncharts/fusioncharts.charts",
        // "fusioncharts-powercharts":"bower_modules/fusioncharts/fusioncharts.powercharts",
        "cryptojs-core":        "bower_modules/cryptojslib/components/core-min",
        "cryptojs-base64":      "bower_modules/cryptojslib/components/enc-base64-min",
        "cryptojs-md5":         "bower_modules/cryptojslib/components/md5-min",
        "evpkdf":               "bower_modules/cryptojslib/components/evpkdf-min",
        "cipher-core":          "bower_modules/cryptojslib/components/cipher-core-min",
        "cryptojs-aes":         "bower_modules/cryptojslib/components/aes-min",
        "bootstrap-tokenfield": "bower_modules/bootstrap-tokenfield/dist/bootstrap-tokenfield.min",
        "bootstrap-tagsinput":  "bower_modules/bootstrap-tagsinput/dist/bootstrap-tagsinput.min",
        "affix":                "bower_modules/bootstrap-tokenfield/docs-assets/js/affix",        
        "scrollspy":            "bower_modules/bootstrap-tokenfield/docs-assets/js/scrollspy",
        "typeahead":            "bower_modules/bootstrap-tokenfield/docs-assets/js/typeahead.bundle",
        "jquery-ui":            "bower_modules/jquery-ui/ui/minified/jquery-ui.min",
        "jquery-ui-sortable":   "bower_modules/jquery-ui/ui/jquery.ui.sortable",
        "jquery-chosen":        "bower_modules/chosen/chosen.jquery",
        "highchecktree":        "bower_modules/highchecktree/js/highchecktree",
        "bootstrap-wizard":     "bower_modules/twitter-bootstrap-wizard/jquery.bootstrap.wizard.min",
        "jquery-ui":            "bower_modules/jquery-ui/jquery-ui",
        "jquery-chosen":        "bower_modules/chosen/chosen.jquery",
        "gridstack":            "bower_modules/gridstack/dist/gridstack.min",
        "lodash":               "bower_modules/lodash/dist/lodash.min",
        "jquery-ui/core":       "bower_modules/jquery-ui/ui/minified/core.min",
        "jquery-ui/widget":     "bower_modules/jquery-ui/ui/minified/widget.min",
        "jquery-ui/mouse":      "bower_modules/jquery-ui/ui/minified/mouse.min",
        "jquery-ui/draggable":  "bower_modules/jquery-ui/ui/minified/draggable.min",
        "jquery-ui/resizable":  "bower_modules/jquery-ui/ui/minified/resizable.min",
        "jquery-ui-touch-punch": "bower_modules/jquery-ui-touch-punch/jquery.ui.touch-punch.min",
        "btpopover":            "bower_modules/bootstrap/js/popover",
        "bttooltip":            "bower_modules/bootstrap/js/tooltip",
        "jqSimpleConnect":      "bower_modules/jqSimpleConnect/source/jqSimpleConnect",
        "knockout-es5":         "bower_modules/knockout-es5/dist/knockout-es5.min",
        "bootstrap-toggle":     "bower_modules/bootstrap-toggle/js/bootstrap-toggle.min",
        "application-model":    "components/dashboard-view/menu-models/application-model",
        "enterprise-model":     "components/dashboard-view/menu-models/enterprise-model",
        "jvm-model":            "components/dashboard-view/menu-models/jvm-model",
        "method-model":         "components/dashboard-view/menu-models/method-model",
        "page-name-model":      "components/dashboard-view/menu-models/page-name-model",
        "scenario-model":       "components/dashboard-view/menu-models/scenario-model",
        "time-model":           "components/dashboard-view/menu-models/time-model",
        //"moment":               "bower_modules/moment/moment",
        "jQuery-plugins":       "bower_modules/jQuery-plugins/numeric/jquery.numeric.min",
        "fsstepper":            "bower_modules/fsstepper/jquery.fs.stepper.min",
        "tooltipster":          "bower_modules/tooltipster/dist/js/tooltipster.bundle.min",
        "scrollable-tooltip":   "bower_modules/scrollable-tooltip/tooltipster-scrollableTip.min",
        "bootstrap-datepicker": "bower_modules/eonasdan-bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min",
        "moment":               "bower_modules/moment/min/moment.min",
        //"tablesorter":          "bower_modules/tablesorter/jquery.tablesorter.min"
        "tablesorter":          "bower_modules/jquery.tablesorter/js/jquery.tablesorter",
        "floatThead":           "bower_modules/floatThead/dist/jquery.floatThead.min",
        "select2":              "bower_modules/select2/dist/js/select2.min"
        /*"datetime-local":       "bower_modules/moment/locale/en-gb",
        "moment":               "bower_modules/moment/min/moment.min",
        "datetime-picker":      "bower_modules/datetimepicker" */

    },
    shim: {        
        "bootstrap": { deps: ["jquery"] },
        "bootstrap-carousel": { deps: ["jquery"] },
        "jquery-ui": {deps: ["jquery"], exports: 'jquery-ui'},
        "jquery-ui/resizable": {deps: ["jquery-ui/core", "jquery-ui/widget", "jquery-ui/mouse"]},
        "jquery-ui-touch-punch": {deps: ["jquery", "jquery-ui"]},
        "fusionCharts": { deps: ["jquery"]},
        "fusionthemes": { deps: ['fusionCharts'] },
        //"fusioncharts-widgets" : { deps: ["fusioncharts"]},
        //"fusioncharts-charts" : { deps: ["fusionCharts"]},
        //"fusioncharts-powercharts" : { deps: ["fusioncharts"]},
        "bootstrap-switch": { deps: ["jquery","bootstrap"] },
        "bootstrap-tagsinput": { deps: ["jquery","jquery-ui","bootstrap"]},
        "bootstrap-tokenfield": { deps: ["jquery","jquery-ui","bootstrap"]},
        "bootstrap-datepicker": { deps: ["jquery", "moment"]},
        "scrollspy": {deps: ["jquery","jquery-ui","bootstrap"]},
        "affix": {deps: ["jquery","jquery-ui","bootstrap"]},
        "typeahead": {deps: ["jquery","jquery-ui","bootstrap"]},
        "jquery-chosen": {deps: ["jquery","jquery-ui"]},
        "cryptojs-aes": {deps: ["cryptojs-base64","cryptojs-md5","evpkdf","cipher-core"]},
        "highchecktree": {deps: ["jquery"]},
        "bootstrap-wizard": {deps: ["jquery","bootstrap"]},
        "gridstack":    {deps: ["lodash","jquery", "jquery-ui","jquery-ui/core","jquery-ui/widget","jquery-ui/mouse","jquery-ui/draggable","jquery-ui/resizable","jquery-ui-touch-punch"]},
        "btpopover": { deps: ["jquery","bttooltip"], exports: '$.fn.popover' },
        "jqSimpleConnect": { deps: ["jquery"], exports:'jqSimpleConnect' },
        "ui-constants": {exports: 'uiConstants'},
        "jQuery-plugins": {deps: ["jquery"]}
    }
};
