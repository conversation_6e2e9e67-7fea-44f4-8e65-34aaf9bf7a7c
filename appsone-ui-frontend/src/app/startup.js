define(['jquery', 'knockout', './router', 'bootstrap'], function($, ko, router) {


  // ... or for template-only components, you can just point to a .html file directly:
  ko.components.register('about-page', {
    template: { require: 'text!components/about-page/about.html' }
  });

  // Components can be packaged as AMD modules, such as the following:

  ko.components.register('login-page', { require: 'components/login-page/login-page' });

  ko.components.register('tiles-page', { require: 'components/tiles-page/tiles-page' });

  ko.components.register('side-bar', { require: 'components/side-bar/side-bar' });

  ko.components.register('top-bar', { require: 'components/top-bar/top-bar' });

  ko.components.register('home-page', { require: 'components/home-page/home-page' });

  ko.components.register('pod-cont-view', { require: 'components/pod-cont-view/pod-cont-view' });

  ko.components.register('topology-view', { require: 'components/topology-view/topology-view' });

  ko.components.register('real-time-chart', { require: 'components/real-time-chart/real-time-chart' });

  ko.components.register('multi-chart', { require: 'components/multi-chart/multi-chart' });

  ko.components.register('doghunt-chart-3-d', { require: 'components/doghunt-chart-3-d/doghunt-chart-3-d' });

  ko.components.register('lines-chart', { require: 'components/lines-chart/lines-chart' });

  ko.components.register('health-view', { require: 'components/health-view/health-view' });

  ko.components.register('dashboard-aggregated-txns', { require: 'components/dashboard-aggregated-txns/dashboard-aggregated-txns' });

  ko.components.register('list-grid-view', { require: 'components/list-grid-view/list-grid-view' });

  ko.components.register('grid-pagination', { require: 'components/grid-pagination/grid-pagination' });

  ko.components.register('application-add-edit', { require: 'components/application-add-edit/application-add-edit' });

  ko.components.register('add-multiple', { require: 'components/add-multiple/add-multiple' });

  ko.components.register('app-type-list-view', { require: 'components/app-type-list-view/app-type-list-view' });

  ko.components.register('app-type-add-edit', { require: 'components/app-type-add-edit/app-type-add-edit' });

  ko.components.register('comp-type-list-view', { require: 'components/comp-type-list-view/comp-type-list-view' });

  ko.components.register('comp-type-add-edit', { require: 'components/comp-type-add-edit/comp-type-add-edit' });

  ko.components.register('config-wizard', { require: 'components/config-wizard/config-wizard' });

  ko.components.register('application-config-wizard', { require: 'components/application-config-wizard/application-config-wizard' });

  ko.components.register('default-dashboard-home', { require: 'components/default-dashboard-home/default-dashboard-home' });

  ko.components.register('custom-dashboard-home', { require: 'components/custom-dashboard-home/custom-dashboard-home' });

  ko.components.register('component-list-view', { require: 'components/component-list-view/component-list-view' });

  ko.components.register('component-add-edit', { require: 'components/component-add-edit/component-add-edit' });

  ko.components.register('horizontal-bar-chart', { require: 'components/horizontal-bar-chart/horizontal-bar-chart' });

  ko.components.register('dashboard-grid-view', { require: 'components/dashboard-grid-view/dashboard-grid-view' });

  ko.components.register('kpi-producer-mapping', { require: 'components/kpi-producer-mapping/kpi-producer-mapping' });

  ko.components.register('cluster-add-edit', { require: 'components/cluster-add-edit/cluster-add-edit' });

  ko.components.register('cluster-list-view', { require: 'components/cluster-list-view/cluster-list-view' });

  ko.components.register('component-instance-list-view', { require: 'components/component-instance-list-view/component-instance-list-view' });

  ko.components.register('component-instance-add-edit', { require: 'components/component-instance-add-edit/component-instance-add-edit' });

  ko.components.register('kpi-list-view', { require: 'components/kpi-list-view/kpi-list-view' });

  ko.components.register('kpi-add-edit', { require: 'components/kpi-add-edit/kpi-add-edit' });

  ko.components.register('add-multiple-kpi', { require: 'components/add-multiple-kpi/add-multiple-kpi' });

  ko.components.register('add-multiple-component-instance', { require: 'components/add-multiple-component-instance/add-multiple-component-instance' });

  ko.components.register('dashboard-multi-txn-performance', { require: 'components/dashboard-multi-txn-performance/dashboard-multi-txn-performance' });

  ko.components.register('producer-list-view', { require: 'components/producer-list-view/producer-list-view' });

  ko.components.register('producer-add-edit', { require: 'components/producer-add-edit/producer-add-edit' });

  ko.components.register('agent-list-view', { require: 'components/agent-list-view/agent-list-view' });

  ko.components.register('component-instance-config-wizard', { require: 'components/component-instance-config-wizard/component-instance-config-wizard' });

  ko.components.register('add-multiple-component-instance-general', { require: 'components/add-multiple-component-instance-general/add-multiple-component-instance-general' });

  ko.components.register('dashboard-kpi-performance', { require: 'components/dashboard-kpi-performance/dashboard-kpi-performance' });

  ko.components.register('transaction-list-view', { require: 'components/transaction-list-view/transaction-list-view' });

  ko.components.register('agent-add-edit', { require: 'components/agent-add-edit/agent-add-edit' });

  ko.components.register('transaction-add-edit', { require: 'components/transaction-add-edit/transaction-add-edit' });

  ko.components.register('add-multiple-transactions', { require: 'components/add-multiple-transactions/add-multiple-transactions' });

  ko.components.register('change-password', { require: 'components/change-password/change-password' });

  ko.components.register('user-role-add-edit', { require: 'components/user-role-add-edit/user-role-add-edit' });

  ko.components.register('user-role-list-view', { require: 'components/user-role-list-view/user-role-list-view' });

  ko.components.register('user-profile-list-view', { require: 'components/user-profile-list-view/user-profile-list-view' });

  ko.components.register('user-profile-add-edit', { require: 'components/user-profile-add-edit/user-profile-add-edit' });

  ko.components.register('view-individual-profile', { require: 'components/view-individual-profile/view-individual-profile' });

  ko.components.register('forgot-password', { require: 'components/forgot-password/forgot-password' });

  ko.components.register('message-dialog-box', { require: 'components/message-dialog-box/message-dialog-box' });

  ko.components.register('agent-wizard-modal', { require: 'components/agent-wizard-modal/agent-wizard-modal' });

  ko.components.register('dashboard-view', { require: 'components/dashboard-view/dashboard-view' });

  ko.components.register('dashboard-alerts-summary', { require: 'components/dashboard-alerts-summary/dashboard-alerts-summary' });

  ko.components.register('dashboard-txn-performance', { require: 'components/dashboard-txn-performance/dashboard-txn-performance' });

  ko.components.register('dashboard-pod-expandedview', { require: 'components/dashboard-pod-expandedview/dashboard-pod-expandedview' });

  ko.components.register('single-axis-line-chart', { require: 'components/single-axis-line-chart/single-axis-line-chart' });

  ko.components.register('sparkline-chart', { require: 'components/sparkline-chart/sparkline-chart' });

  ko.components.register('dashboard-transaction-health', { require: 'components/dashboard-transaction-health/dashboard-transaction-health' });

  ko.components.register('kpi-producer-cluster-mapping', { require: 'components/kpi-producer-cluster-mapping/kpi-producer-cluster-mapping' });

  ko.components.register('global-settings', { require: 'components/global-settings/global-settings' });
  
  ko.components.register('single-axis-area-spline-chart', { require: 'components/single-axis-area-spline-chart/single-axis-area-spline-chart' });

  ko.components.register('notification-content-profile-list-view', { require: 'components/notification-content-profile-list-view/notification-content-profile-list-view' });
  
  ko.components.register('notification-content-profile-add-edit', { require: 'components/notification-content-profile-add-edit/notification-content-profile-add-edit' });

  ko.components.register('severity-profile-add-edit', { require: 'components/severity-profile-add-edit/severity-profile-add-edit' });
  
  ko.components.register('severity-profile-list-view', { require: 'components/severity-profile-list-view/severity-profile-list-view' });
  
  ko.components.register('placeholder-options', { require: 'components/placeholder-options/placeholder-options' });

  ko.components.register('dashboard-bve-performance', { require: 'components/dashboard-bve-performance/dashboard-bve-performance' });

  ko.components.register('escalation-profile-list-view', { require: 'components/escalation-profile-list-view/escalation-profile-list-view' });
  
  ko.components.register('escalation-profile-add-edit', { require: 'components/escalation-profile-add-edit/escalation-profile-add-edit' });

  
  ko.components.register('time-profile-list-view', { require: 'components/time-profile-list-view/time-profile-list-view' });
  
  ko.components.register('time-profile-add-edit', { require: 'components/time-profile-add-edit/time-profile-add-edit' });
  
  ko.components.register('alert-profile-main-list-view', { require: 'components/alert-profile-main-list-view/alert-profile-main-list-view' });
  
  ko.components.register('alert-profile-main-add-edit', { require: 'components/alert-profile-main-add-edit/alert-profile-main-add-edit' });
  
  ko.components.register('global-search', { require: 'components/global-search/global-search' });

  ko.components.register('email-sms-settings', { require: 'components/email-sms-settings/email-sms-settings' });
  
  ko.components.register('grpc-settings-list-view', { require: 'components/grpc-settings-list-view/grpc-settings-list-view' });
  
  ko.components.register('grpc-settings-add-edit', { require: 'components/grpc-settings-add-edit/grpc-settings-add-edit' });
  
  ko.components.register('kpi-group-list-view', { require: 'components/kpi-group-list-view/kpi-group-list-view' });
  
  ko.components.register('kpi-group-add-edit', { require: 'components/kpi-group-add-edit/kpi-group-add-edit' });
  
  ko.components.register('side-bar-collapsible', { require: 'components/side-bar-collapsible/side-bar-collapsible' });
  
  // [Scaffolded component registrations will be inserted here. To retain this feature, don't remove this comment.]

  // Start the application
  //ko.applyBindings({ route: router.currentRoute,refreshInterval:null,routeEnterpriseName:null });
  ko.applyBindings({ route: router.currentRoute, nameValidation: 0, tagValidation: 1});
});
