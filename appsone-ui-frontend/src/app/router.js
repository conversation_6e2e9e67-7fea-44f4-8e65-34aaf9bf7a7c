define(["jquery", "knockout", "crossroads", "hasher", 'ui-constants'], function($, ko, crossroads, hasher, uiConstants) {

    // This module configures crossroads.js, a routing library. If you prefer, you
    // can use any other routing library (or none at all) as Knockout is designed to
    // compose cleanly with external libraries.
    //
    // You *don't* have to follow the pattern established here (each route entry
    // specifies a 'page', which is a Knockout component) - there's nothing built into
    // Knockout that requires or even knows about this technique. It's just one of
    // many possible ways of setting up client-side routes.

    return new Router({
        routes: [
            { url: 'about',         params: { page: 'about-page' } },
            { url: '',              params: { page: 'login-page' } },
            { url: 'side',          params: { page: 'side-bar' } },
            { url: 'top',           params: { page: 'top-bar' } },            
            { url: 'home',          params: { page: 'home-page' } },
            { url: 'cdashboard',    params: { page: 'custom-dashboard-home' } },
            { url: 'tiles',         params: { page: 'tiles-page' } },            
            //{ url: 'podview',       params: { page: 'pod-cont-view' } },            
            //{ url: 'topology',      params: { page: 'topology-view' } },
            //{ url: 'realchart',     params: { page: 'real-time-chart' } },
            //{ url: 'multichart',    params: { page: 'multi-chart' } },
            //{ url: 'doghuntchart',       params: { page: 'doghunt-chart-3-d' } },
            //{ url: 'linechart',    params: { page: 'lines-chart' } },
            //{ url: 'healthview',    params: { page: 'health-view' } },
            { url: 'appConfig', params: { page: 'app-config'} },
            { url: 'listGridView', params: { page: 'list-grid-view'} },
            { url: 'addMultiple', params: { page: 'add-multiple'} },
            { url: 'appTypeListView', params: { page: 'app-type-list-view'} },
            { url: 'compTypeListView', params: { page: 'comp-type-list-view'} },
            { url: 'appTypeAddEdit', params: { page: 'app-type-add-edit'} },
            { url: 'compTypeAddEdit', params: { page: 'comp-type-add-edit'} },
            { url: 'configWizard', params: { page: 'config-wizard'} },
            { url: 'applicationConfigWizard', params: { page: 'application-config-wizard'} },
            { url: 'componentListView', params: { page: 'component-list-view'} },
            { url: 'clusterListView', params: { page: 'cluster-list-view'} },
            { url: 'componentInstanceListView', params: { page: 'component-instance-list-view'} },
            { url: 'kpiListView', params: { page: 'kpi-list-view'} },
            { url: 'addMultipleKpi', params: { page: 'add-multiple-kpi'} },
            //{ url: 'dashboardMultiTxnPerformance', params: { page: 'dashboard-multi-txn-performance'} },
            { url: 'producerListView', params: { page: 'producer-list-view'} },
            { url: 'producerAddEdit', params: { page: 'producer-add-edit'} },
            { url: 'agentListView', params: { page: 'agent-list-view'} },
            //{ url: 'dashboardKpiPerformance', params: {page: 'dashboard-kpi-performance'} },
            { url: 'agentAddEdit', params: { page: 'agent-add-edit'} },
            { url: 'transactionListView', params: { page: 'transaction-list-view'} },
            { url: 'changePassword', params: { page: 'change-password'} },
            { url: 'userRoleListView', params: { page: 'user-role-list-view'} },
            { url: 'userRoleAddEdit', params: { page: 'user-role-add-edit'} },
            { url: 'userProfileListView', params: { page: 'user-profile-list-view'} },
            { url: 'userProfileAddEdit', params: { page: 'user-profile-add-edit'} },
            { url: 'viewIndividualProfile', params: { page: 'view-individual-profile'} },
            { url: 'globalSettings', params: { page: 'global-settings'} },
            { url: 'notificationContentListView', params: { page: 'notification-content-profile-list-view'} },
            { url: 'notificationContentAddEdit', params: { page: 'notification-content-profile-add-edit'} },
            { url: 'alertProfileMainListView', params: { page: 'alert-profile-main-list-view'} },
            { url: 'timeProfileListView', params: { page: 'time-profile-list-view'} },
            { url: 'severityProfileListView', params: { page: 'severity-profile-list-view'} },
            { url: 'escalationProfileListView', params: { page: 'escalation-profile-list-view'} },
            { url: 'globalSearch', params: { page: 'global-search'} },
            { url: 'emailSmsSettings', params: { page: 'email-sms-settings'} },
            { url: 'grpcSettingsListView', params: { page: 'grpc-settings-list-view'} },
            { url: 'kpiGroupListView', params: { page: 'kpi-group-list-view'} },
            { url: 'kpiGroupAddEdit', params: { page: 'kpi-group-add-edit'} },

            //{ url: 'details/{enterpriseName}/{appName}/{id}',     params: { page: 'details-page' } }
        ]
    });

    function Router(config) {
        var currentRoute = this.currentRoute = ko.observable({});

        ko.utils.arrayForEach(config.routes, function(route) {
            crossroads.addRoute(route.url, function(requestParams) {
                currentRoute(ko.utils.extend(requestParams, route.params));
            });
        });

        activateCrossroads();
    }

    function activateCrossroads() {
        function parseHash(newHash, oldHash) {
            crossroads.parse(newHash);
            window.history.forward();


            if(newHash == ""){
                if($("#loginModal").css('display') != 'none'){
                    $("#loginModal").modal("hide");
                    removeLoadingContainer();
                }
            }

            if($("#messageDialogBox").css('display') != 'none'){
                $('#messageDialogBox').modal('hide');
            }

            if(localStorage.apptoken){
                var authTokenPayload = JSON.parse(atob(localStorage.apptoken.split(".")[1]));
                uiConstants.common.FORCE_PASSWORD_CHANGE = authTokenPayload.forcePasswordChange;
                if(uiConstants.common.FORCE_PASSWORD_CHANGE && newHash !== "changePassword" && newHash !== ""){
                    hasher.setHash("#changePassword");
                }
            }
        }
        crossroads.normalizeFn = crossroads.NORM_AS_OBJECT;
        hasher.initialized.add(parseHash);
        hasher.changed.add(parseHash);
        hasher.init();
    }
});
