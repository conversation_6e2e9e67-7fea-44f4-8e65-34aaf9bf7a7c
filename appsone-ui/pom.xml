<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.appnomic</groupId>
        <artifactId>appsone</artifactId>
        <version>5.8.0</version>
    </parent>

    <groupId>com.appnomic.appsone</groupId>
    <artifactId>appsone-ui</artifactId>
    <version>5.8.1</version>
    <packaging>jar</packaging>
    <name>appsone-ui</name>

    <dependencies>

        <dependency>
            <groupId>com.appnomic.appsone</groupId>
            <artifactId>common</artifactId>
            <version>5.8.1</version>
        </dependency>

        <dependency>
            <groupId>com.appnomic.appsone</groupId>
            <artifactId>data-service</artifactId>
            <version>5.8.1</version>
        </dependency>

        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.sparkjava</groupId>
            <artifactId>spark-core</artifactId>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jayway.restassured</groupId>
            <artifactId>rest-assured</artifactId>
            <version>2.1.0</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/web</directory>
                <targetPath>web</targetPath>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifest>
                            <addClasspath>true</addClasspath>
                            <classpathPrefix>lib/</classpathPrefix>
                            <mainClass>com.appnomic.appsone.ui.BootStrapUIService</mainClass>
                        </manifest>
                        <manifestEntries>
                            <Class-Path>./config/</Class-Path>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>

            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-script-folder</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/scripts</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/scripts</directory>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>copy-config-folder</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/config</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/config</directory>
                                    <excludes>
                                        <exclude>**/*.jks</exclude>
                                    </excludes>
                                    <filtering>true</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>

                    <execution>
                        <id>copy-config-folder-keystorefiles</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/config</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>src/main/config</directory>
                                    <includes>
                                        <include>**/*.jks</include>
                                    </includes>
                                    <filtering>false</filtering>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>

                </executions>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <overWriteReleases>false</overWriteReleases>
                            <overWriteSnapshots>false</overWriteSnapshots>
                            <overWriteIfNewer>true</overWriteIfNewer>
                            <includeScope>runtime</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>rpm-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>attach-rpm</id>
                        <phase>package</phase>
                        <goals>
                            <goal>attached-rpm</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <license>commercial software</license>
                    <distribution>${project.groupId}</distribution>
                    <group>${project.groupId}</group>
                    <autoRequires>false</autoRequires>
                    <mappings>

                        <mapping>
                            <directory>${rpm.install.directory}</directory>
                            <filemode>755</filemode>
                            <username>${unix.user.name}</username>
                            <groupname>${unix.user.name}</groupname>
                            <artifact />
                        </mapping>

                        <mapping>
                            <directory>${rpm.install.directory}/lib</directory>
                            <filemode>755</filemode>
                            <username>${unix.user.name}</username>
                            <groupname>${unix.user.name}</groupname>
                            <sources>
                                <source>
                                    <location>${project.build.directory}/lib</location>
                                </source>
                            </sources>
                        </mapping>

                        <mapping>
                            <directory>/etc/init.d/</directory>
                            <directoryIncluded>false</directoryIncluded>
                            <filemode>755</filemode>
                            <username>root</username>
                            <groupname>root</groupname>
                            <sources>
                                <source>
                                    <location>${project.build.directory}/scripts/appsone-ui</location>
                                </source>
                            </sources>
                        </mapping>

                        <mapping>
                            <directory>${rpm.install.directory}/log</directory>
                            <filemode>755</filemode>
                            <username>${unix.user.name}</username>
                            <groupname>${unix.user.name}</groupname>
                        </mapping>

                        <mapping>
                            <directory>${rpm.install.directory}/config</directory>
                            <configuration>true</configuration>
                            <filemode>755</filemode>
                            <username>${unix.user.name}</username>
                            <groupname>${unix.user.name}</groupname>
                            <sources>
                                <source>
                                    <location>${project.build.directory}/config</location>
                                </source>
                            </sources>
                        </mapping>

                    </mappings>
                    <preinstallScriptlet>
                        <scriptFile>${project.build.directory}/scripts/preinstall.sh</scriptFile>
                        <fileEncoding>utf-8</fileEncoding>
                    </preinstallScriptlet>
                    <postinstallScriptlet>

                        <fileEncoding>utf-8</fileEncoding>
                    </postinstallScriptlet>
                    <preremoveScriptlet>

                    </preremoveScriptlet>
                    <postremoveScriptlet>

                    </postremoveScriptlet>
                    <requires>
                    </requires>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>