#################### appsone-ui configurations ####################
# https port appsone-ui.sh listens on; default appsone-ui.sh port is 9191
#https.port=
#
# Mysql Database Details, uncomment and set each properties if different
# from default. Default Values; mysql.server.ip=127.0.0.1 mysql.server.port=3306
# mysql.server.username=root mysql.server.password=
#
mysql.server.ip=127.0.0.1
mysql.server.port=3306
#mysql.server.username=
#mysql.server.password=
mysql.server.schema=appsone
#
#
# JWT token String Constants
# Ensure that token expiry time is more than token idle time
# Token expiry and token idle time is in minutes
#
jwt.token.expiry=172820
jwt.token.idleTime=172820
#
#
#
# Notification mail server Ip. Default values ; email.http.server.ip=127.0.0.1 http.server.port=8888
# email.http.server.ip=
# http.server.port=

#Active directory configuration
#IP address for active directory
#active.directory.ip=

#Active directory port, default port is 389
#active.directory.port=

#Active directory domain name
#active.directory.domain=

#Default user role
#active.directory.user.role=

#Default account for user
#active.directory.account=

#Active directory object name
#active.directory.objectname=

#Active directory basic search for example "CN=Users,DC=apptestmail,DC=com"
#active.directory.base.search=

#Specify the attributes to return
#Active directory email attribute name
#active.directory.user.detail.email=

#Active directory full name attribute name
#active.directory.user.detail.name=

#Active directory user name attribute name
#active.directory.user.detail.account=