/*
 * Copyright (c) 2015-2016 Appnomic Systems Private Limited, Bangalore
 *
 * All rights reserved
 *
 * The source code in this file is licensed and does not fall under any
 * type of open source license.  No form of reproduction is allowed without
 * prior written consent from Appnomic Systems.
 */

package com.appnomic.appsone.ui;

import com.appnomic.appsone.common.util.ConfProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import static spark.Spark.*;

/**
 * Created by <PERSON><PERSON> on 7/5/15.
 */
public class BootStrapUIService
{
    private static final Logger log = LoggerFactory.getLogger(BootStrapUIService.class);

    public static void main(String[] args)
    {
        initializeSpark();
    }

    private static void initializeSpark()
    {
        log.info("starting appsone-ui....");
        int httpPort = ConfProperties.getInt(Constants.UISERVICE_HTTPS_PORT_PROPERTY_NAME, Constants.UISERVICE_DEFAULT_HTTPS_PORT);
        try
        {
            String keystoreFileName = ConfProperties.getString(Constants.UISERVICE_HTTPS_KEYSTORE_FILE_NAME_PROPERTY_NAME,Constants.UISERVICE_DEFAULT_HTTPS_KEYSTORE_FILE_NAME);
            String keystoreFilePath = BootStrapUIService.class.getClassLoader().getResource(keystoreFileName).getFile();
            String keystorePassword =  ConfProperties.getString(Constants.UISERVICE_HTTPS_KEYSTORE_PASSWORD_PROPERTY_NAME,Constants.UISERVICE_DEFAULT_HTTPS_KEYSTORE_PASSWORD);
            secure(keystoreFilePath,keystorePassword, null, null);
        }
        catch (Exception e)
        {
            log.warn("could not find keystore file in conf folder, Hence starting appsone-ui in non-secure mode");
        }
        port(httpPort);
        staticFileLocation("/web");
        Routes.init();
        attachShutDownHook();
    }

    private static void attachShutDownHook()
    {
        Runtime.getRuntime().addShutdownHook(new Thread()
        {
            @Override
            public void run()
            {
                log.info("shutting down appsone-ui....");
                // add shutdown/cleanup activities here
                log.info("appsone-ui shutdown completed....");
            }
        });
    }

}