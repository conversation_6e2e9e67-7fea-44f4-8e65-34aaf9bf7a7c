/*
 * Copyright (c) 2015-2016 Appnomic Systems Private Limited, Bangalore
 *
 * All rights reserved
 *
 * The source code in this file is licensed and does not fall under any
 * type of open source license.  No form of reproduction is allowed without
 * prior written consent from Appnomic Systems.
 */

/*
 * Created by <PERSON><PERSON> on 26/5/15.
 */
package com.appnomic.appsone.ui;

import com.appnomic.appsone.common.CommonConstants;
import com.appnomic.appsone.common.exception.AppsOneException;
import com.appnomic.appsone.common.exception.InvalidTokenException;
import com.appnomic.appsone.common.util.Commons;
import com.appnomic.appsone.common.util.ResourceBundleUtils;
import com.appnomic.appsone.common.util.ResponseObject;
import com.appnomic.appsone.dataservice.cassandra.*;
import com.appnomic.appsone.dataservice.constants.Constants;
import com.appnomic.appsone.dataservice.constants.ResponseConstants;
import com.appnomic.appsone.dataservice.dao.MstSubTypeCrud;
import com.appnomic.appsone.dataservice.dao.MstTypeCrud;
import com.appnomic.appsone.dataservice.domain.AddComponentObject;
import com.appnomic.appsone.dataservice.domain.ClusterObject;
import com.appnomic.appsone.dataservice.domain.ErrorObject;
import com.appnomic.appsone.dataservice.domain.MstSubTypeObject;
import com.appnomic.appsone.dataservice.service.*;
import com.appnomic.appsone.dataservice.service.TransactionService;
import com.appnomic.appsone.dataservice.utils.*;
import com.google.common.reflect.TypeToken;
import org.boon.json.JsonFactory;
import org.boon.json.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Filter;
import spark.Request;
import spark.Response;
import spark.Spark;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.*;

import static spark.Spark.*;

public class Routes {
    private static final Logger log = LoggerFactory.getLogger(Routes.class);
    private static final HashMap<String, String> corsHeaders = new HashMap<String, String>();

    static {
        corsHeaders.put("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS");
        corsHeaders.put("Access-Control-Allow-Origin", "*");
        corsHeaders.put("Access-Control-Allow-Headers", "Content-Type,Authorization,X-Requested-With,Content-Length,Accept,Origin,");
        corsHeaders.put("Access-Control-Allow-Credentials", "true");
        corsHeaders.put("Content-Type", "text/css");
    }

    public final static void applyCorsFilters() {
        Filter filter = new Filter() {
            @Override
            public void handle(Request request, Response response) throws Exception {
                corsHeaders.forEach((key, value) -> {
                    response.header(key, value);
                });
            }
        };
        Spark.after(filter);
    }

    public static void init() {
        Routes.applyCorsFilters();
        before("/v1.0/ui/*", (request, response) -> {
            log.info("Called API {} at time {}", request.pathInfo(), System.currentTimeMillis());

            // Request parameter configuration
            log.debug("AuthToken : " + request.headers(Constants.AUTHORIZATION_HEADER) + " for request uri : " + request
                    .pathInfo() + " request method : " + request.requestMethod());
            if (!request.pathInfo().equals(Constants.LOGIN_API_PATH)
                    && !request.pathInfo().equals(Constants.FORGOT_PASSWORD_API_PATH)
                    && !request.requestMethod().equals("OPTIONS")) {
                JwtUtil.validateJWT(request.headers(Constants.AUTHORIZATION_HEADER));

                //Validate request parameter fields like name and description.
                //TODO Currently this implementation may not work for some screens
                //TODO because of customize parameter object names. So keeping it on hold for now.
                //TODO change all request to same then this implementation will work.
                //ValidationHelper.validateRequestParam(request);
            }
            // Response parameter configuration
            response.type("application/json");
        });


        options("/v1.0/ui/*", (req, res) -> {
            log.info("Called API {} at time {}", req.pathInfo(), System.currentTimeMillis());
            return "";
        });

        //Get calls
        get("/v1.0/ui/applications/applicationTypeId/:applicationTypeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applications/applicationTypeId/:applicationTypeId");
            return ApplicationService.getAllApplicationsUsingApplicationTypeId(request,
                    Integer.parseInt(request.params(":applicationTypeId")));
        }, new JsonTransformer());

        get("/v1.0/ui/applications", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applications");
            return ApplicationService.getAllApplicationDetails(request);
        }, new JsonTransformer());

        get("/v1.0/ui/application/getAllMasterTypes", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/getAllMasterTypes");
            return MstTypeCrud.getAllMstTypes(request);
        }, new JsonTransformer());

        get("/v1.0/ui/application/getAllMasterType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/getAllMasterTypes");
            return MstTypeCrud.getMasterTypeId(CommonConstants.APPLICATION_TYPE, request);
        }, new JsonTransformer());


        get("/v1.0/ui/masterTypes/application", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/masterTypes/application");
            return MstSubTypeService.getAllApplicationTypes(request);
        }, new JsonTransformer());

        get("/v1.0/ui/maintenanceProfiles", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/maintenanceProfiles");
            return MaintenanceProfileService.getAllMaintenanceProfiles(request);
        }, new JsonTransformer());

        get("/v1.0/ui/timeZones", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/timeZones");
            return TimeZoneService.getTimeZones(request);
        }, new JsonTransformer());

        get("/v1.0/ui/applicationType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applicationType");
            return MstSubTypeService.getAllApplicationTypesWithFilter(request);
        }, new JsonTransformer());

        get("/v1.0/ui/componentType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentType");
            return MstComponentTypeService.getAllComponentTypes(request);
        }, new JsonTransformer());

        get("/v1.0/ui/user/menuDetails", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/user/menuDetails");
            /*ObjectMapper objectMapper = JsonFactory.create();*/
            /*Object returnObject = objectMapper.fromJson(Constants.menuString);*/
            return MstMenuService.getMenu(request);
        }), new JsonTransformer());

        get("/v1.0/ui/user/notificationType", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/user/notificationType");
            /*ObjectMapper objectMapper = JsonFactory.create();*/
            /*Object returnObject = objectMapper.fromJson(Constants.menuString);*/
            Integer accountId = RequestUtil.getAccountIdFromRequest(request);
            return MstSubTypeService.getMstSubTypeForMasterTypeName(Constants.USER_REGISTRATION_NOTIFICATION_TYPE,
                    1,accountId);
        }), new JsonTransformer());

        /*get("/v1.0/ui/user/menuPermission", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/user/menuPermission");
            return MstMenuService.getMenuPermission(request);
        }), new JsonTransformer());*/

        get("/v1.0/ui/component", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/component");
            return ComponentService.getComponent(request);
        }), new JsonTransformer());

        get("/v1.0/ui/component/:applicationType", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/component/:applicationType");
            return MstComponentService.getComponentForApplicationType(request, Integer.parseInt(request.params
                    (":applicationType")));
        }), new JsonTransformer());

        get("/v1.0/ui/componentTemplate", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentTemplate");
            return ComponentTemplateService.getAllComponentTemplate();
        }), new JsonTransformer());

        get("/v1.0/ui/componentConfig", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/component");
            return MstComponentVersionService.getComponentForFilter(request);
            //return MstComponentVersionService.getComponentForFilter(request);
        }), new JsonTransformer());

        get("/v1.0/ui/attribute/:componentId", ((request, response) -> {
            Integer componentId = Integer.parseInt(request.params(":componentId"));
            log.trace(Constants.API_ROUTE_CALLED + "for componentId", "/v1.0/attributes/",
                    componentId);
            return MstComponentAttributeService.getComponentAttribute(request, componentId);
        }), new JsonTransformer());

        get("/v1.0/ui/attribute", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/attributes");
            return MstCommonAttributeService.getAllCommonAttributes(request);
        }), new JsonTransformer());

        get("/v1.0/ui/tags/component", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/tags/component");
            return MstComponentService.getAllTagsForComponent();
        }), new JsonTransformer());

        get("/v1.0/ui/clusterOperation", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/clusterOperation");
            return KPIDetailsService.getClusterOperationList(request);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiUnits", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiUnits");
            return KPIDetailsService.getAllKpiUnits(request);
        }), new JsonTransformer());

        get("/v1.0/ui/attributeType", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/attributeType");
            return MstCommonAttributeService.getAttributeTypes(request);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiGroups", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiGroups");
            return MstKpiGroupService.getKpiGroups(request);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiDataTypes", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiDataTypes");
            return KPIDetailsService.getKpiDataTypes(request);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiDataTypesWithUnits", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiDataTypes");
            return KPIDetailsService.getKpiDataTypesWithUnits(request);
        }), new JsonTransformer());

        get("/v1.0/ui/applicationsName", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applicationsName");
            return ApplicationService.getAllApplicationName(request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentInstance/:componentId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/componentInstance");
            return MstComponentInstanceService.getComponentInstance(request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentAttribute/:componentVersionId", ((request, response) -> {
            Integer componentVersionId = Integer.parseInt(request.params(":componentVersionId"));
            log.trace(Constants.API_ROUTE_CALLED + "for componentVersionId :{}", "v1.0/ui/componentAttributes",
                    componentVersionId);
            return MstComponentAttributeService.getComponentAttributeForComponentVersionId(request, componentVersionId);
        }), new JsonTransformer());

        get("/v1.0/ui/componentAttribute", ((request, response) -> {
            return MstComponentAttributeService.getComponentAttributeForComponentVersionIds(request);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiDetails", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiDetails");
            boolean isUiRoutes = true;
            return KPIDetailsService.getKpiDetails(request, isUiRoutes);
        }), new JsonTransformer());

        get("/v1.0/ui/kpis", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/kpiName");
            return KPIDetailsService.getProducerKpi(request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentKpiProducerMapping/:componentVersionId", ((request, response) -> {
            Integer componentVersionId = Integer.parseInt(request.params(":componentVersionId"));
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiProducerMapping");
            return MstProducerKpiMappingService.getAllMstProducerMappingForComponentVersionId(request,
                    componentVersionId);
        }), new JsonTransformer());

        get("/v1.0/ui/instance/add/producerKpiMapping/:componentVersionId", ((request, response) -> {
            Integer componentVersionId = Integer.parseInt(request.params(":componentVersionId"));
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiProducerMapping");
            return MstProducerKpiMappingService.getAllProducerKpiMappingForInstanceAdd(request,
                    componentVersionId);
        }), new JsonTransformer());

        get("/v1.0/ui/componentKpiProducerMappingForGroup/:componentVersionId", ((request, response) -> {
            Integer componentVersionId = Integer.parseInt(request.params(":componentVersionId"));
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiProducerMapping");
            Integer accountId = RequestUtil.getAccountIdFromRequest(request);
            Integer status = StringUtils.getIntegerFromString(request.queryParams("status"),1);
            return MstProducerKpiMappingService.getMstProducerMappingForGroupOnlyByComponentVersionId
                    (componentVersionId,accountId,status);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiProducerMapping/:componentVersionId", ((request, response) -> {
            Integer componentVersionId = Integer.parseInt(request.params(":componentVersionId"));
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiProducerMapping");
            return MstProducerKpiMappingService.getAllMstProducerKpiMappings(request, componentVersionId);
        }), new JsonTransformer());

        get("/v1.0/ui/kpiProducerMappingForInstances", ((request, response) -> {
            String[] compInstanceIds = request.queryParamsValues("id");
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiProducerMapping");
            boolean isClusterKpis = false;
            return MstProducerKpiMappingService.getAllMstProducerKpiMappingsForCompInstanceId(request, compInstanceIds,isClusterKpis);
        }), new JsonTransformer());

        get("/v1.0/ui/threshold/kpiListForInstance", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiListForInstance");
            return MstProducerKpiMappingService.getAllMstProducerKpiMappingsForCompInstanceId(request);
        }), new JsonTransformer());

        get("/v1.0/ui/threshold/clusterListView", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiListForInstance/:compInstanceId");
            return MstProducerKpiMappingService.getAllListOfCluster(request);
        }), new JsonTransformer());

        get("/v1.0/ui/threshold/compInstancesListView/:clusterId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/threshold/compInstancesListView/:clusterId");
            return ClusterService.getAllCompInstanceUsingClusterId(request,
                    Integer.parseInt(request.params(":clusterId")));
        }), new JsonTransformer());

        get("/v1.0/ui/producerVersionKpiMapping/:producerId", ((request, response) -> {
            Integer producerId = Integer.parseInt(request.params(":producerId"));
            log.trace(Constants.API_ROUTE_CALLED + "for producerId : {}", "v1.0/ui/producerVersionKpiMapping", producerId);
            return MstProducerKpiMappingService.getProducerVersionKpiMapping(request, producerId);
        }), new JsonTransformer());

        get("/v1.0/ui/producerName", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/producerName");
            return MstProducerService.getAllProducersName(request);
        }), new JsonTransformer());

        get("/v1.0/ui/jmxAttributeTypes",((request, response) ->{
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/jmxAttributeTypes");
            return MstProducerService.getAttributeDataTypeList(request);
        }), new JsonTransformer());

        get("/v1.0/ui/jppfServerTypes",((request, response) ->{
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/jppfServerTypes");
            return MstProducerService.getJPPFServerTypeList(request);
        }), new JsonTransformer());

        get("/v1.0/ui/jdbcQueryResults", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/jdbcQueryResults");
            return MstProducerService.getJdbcQueryResults(request);
        }), new JsonTransformer());

        get("/v1.0/ui/producerType", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/producerType");
            return MstProducerService.getProducerType(request);
        }), new JsonTransformer());

        get("/v1.0/ui/cluster", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/cluster");
            boolean isUiRoutes = true;
            return CompInstanceService.getAllClusters(request, isUiRoutes);
        }), new JsonTransformer());

        get("/v1.0/ui/componentName", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/componentName");
            return MstComponentService.getAllComponentName(request);
        }), new JsonTransformer());

        get("/v1.0/ui/clusterName/:componentId", ((request, response) -> {
            Integer componentId = Integer.parseInt(request.params(":componentId"));
            log.trace(Constants.API_ROUTE_CALLED + " for componentId :{} ", "v1.0/ui/clusterName",
                    componentId);
            return ClusterService.getClusterForComponentId(request, componentId);
        }), new JsonTransformer());

        get("/v1.0/ui/parameterType/:type", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/parameterType");
            String type = request.params("type");
            return MstTypeService.getParameterType(type, request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentInstance", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/componentInstance");
            return CompInstanceService.getAllComponentInstance(request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentInstanceForApplication", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/componentInstance");
            return CompInstanceService.getComponentInstanceForComponentAndApplication(request);
        }), new JsonTransformer());

        get("/v1.0/ui/componentInstances", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/componentInstance");
            return CompInstanceService.getComponentInstances(request);
        }), new JsonTransformer());

        get("/v1.0/ui/clusterOperation/:componentId", ((request, response) -> {
            Integer componentId = Integer.parseInt(request.params(":componentId"));
            log.trace(Constants.API_ROUTE_CALLED + "for componentId : {} ",
                    "v1.0/ui/clusterOperation", componentId);
            return  ClusterService.getClusterOperations(request,componentId);
            //return CompInstanceKpiDetailService.getCompInstanceKpiDetails(request, componentId);
        }), new JsonTransformer());

        get("/v1.0/ui/KpiDetails/:componentId", (((request, response) -> {
            Integer componentId = Integer.parseInt(request.params(":componentId"));
            log.trace(Constants.API_ROUTE_CALLED + "for compInstanceId : {} ", "v1.0/ui/kpi/:componentId",
                    componentId);
            return MstComponentVersionKpiMappingService.getKpiForComponentId(request, componentId);
        })), new JsonTransformer());

        get("/v1.0/ui/kpis_KpiGroups", (((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/kpis_KpiGroups");
            return KPIDetailsService.getAllKpiGroupDetail(request);
        })), new JsonTransformer());

        get("/v1.0/ui/producerMapping/kpi/:kpiId", (((request, response) -> {
            Integer kpiId = Integer.parseInt(request.params(":kpiId"));
            log.trace(Constants.API_ROUTE_CALLED + "for kpiId : {} ", "v1.0/ui/producerMapping/kpi",
                    kpiId);
            return MstProducerKpiMappingService.getProducerKpiMappingForKpiId(request, kpiId);
        })), new JsonTransformer());

        get("/v1.0/ui/producerMapping", (((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/producerMapping");
            return MstProducerKpiMappingService.getProducerMappingsForKpi(request);
        })), new JsonTransformer());

        get("/v1.0/ui/producerMapping/kpiGroup/:kpiGroupId", (((request, response) -> {
            Integer kpiGroupId = Integer.parseInt(request.params(":kpiGroupId"));
            log.trace(Constants.API_ROUTE_CALLED + "for kpiGroupId : {} ", "v1.0/ui/producerMapping/kpiGroup",
                    kpiGroupId);
            return MstProducerKpiMappingService.getProducerKpiMappingForKpiGroupId(request, kpiGroupId);
        })), new JsonTransformer());

        get("v1.0/ui/producers", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "producerList()");
            return MstProducerService.getProducerListView(request);
        }), new JsonTransformer());

        get("/v1.0/ui/producerParameter/:producerId", ((request, response) -> {
            Integer producerId = Integer.parseInt(request.params(":producerId"));
            log.trace(Constants.API_ROUTE_CALLED + "for producerId : {}", "producerParameter()", producerId);
            Integer accountId = RequestUtil.getAccountIdFromRequest(request);
            boolean isUiRoute = true;
            return MstProducerService.getProducerParameter(producerId, accountId, isUiRoute);
        }), new JsonTransformer());

        get("/v1.0/ui/agentType", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agentTypes");
            return AgentService.getAgentTypes(request);
        }), new JsonTransformer());

        get("/v1.0/ui/communicationProtocol", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/communicationProtocol");
            return AgentService.getCommunicationProtocols(request);
        }), new JsonTransformer());

        get("/v1.0/ui/agentMode", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agentMode");
            return AgentService.getAgentModes();
        }), new JsonTransformer());

        get("/v1.0/ui/transactionTypes", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transactionTypes");
            return TransactionService.getTransactionType(request);
        }), new JsonTransformer());

        get("/v1.0/ui/transactionAuditData", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transactionTypes");
            return TransactionService.getTxnAuditData(request);
        }), new JsonTransformer());

        get("/v1.0/ui/transactionMethods", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transactionMethods");
            return TransactionService.getTransactionMethod(request);
        }), new JsonTransformer());

        get("/v1.0/ui/transactionResponseTypes", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transactionResponseTypes");
            return TransactionService.getTransactionResponseType(request);
        }), new JsonTransformer());

        get("/v1.0/ui/transactions/:applicationId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transactions");
            return TransactionService.getTransactionsListView(request);
        }), new JsonTransformer());

        get("/v1.0/ui/agent", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agent");
            return AgentService.getAgentWithFilter(request);
        }, new JsonTransformer());

        get("/v1.0/ui/agents/identifier/:identifier", ((request, response) -> {
            final String identifier = request.params(":identifier");
            log.trace(Constants.API_ROUTE_CALLED, " /v1.0/ui/agents/identifier/{}", identifier);
            // for non ui user account id is not required so added field isUiRoutes to differentiate
            boolean isUiRoutes = false;
            return AgentService.getAgentByIdentifier(request, identifier, true);
        }), new JsonTransformer());

        get("/v1.0/ui/host", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/host");
            return CompInstanceService.getHostDetails(request);
        }), new JsonTransformer());

        get("/v1.0/ui/agent/host", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/host");
            return AgentService.getAgentHosts(request);
        }), new JsonTransformer());

        get("/v1.0/ui/mappedHostList", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/host");
            return CompInstanceService.getMappedHostDetails(request);
        }), new JsonTransformer());

        get("/v1.0/ui/tag", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/tag");
            return TagMappingService.getTagForName(request);
        }, new JsonTransformer());

        get("/v1.0/ui/cluster/:componentId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/cluster/:componentId");
            return CompInstanceService.getClusterForComponentId(request, Integer.valueOf(request.params
                    ("componentId")));
        }, new JsonTransformer());

        get("/v1.0/ui/clusters/application", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/cluster/application");
            return CompInstanceService.getClusterForApplicationIds(request);
        }, new JsonTransformer());

        get("/v1.0/ui/wizard/componentInstances/:applicationId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " for applicationId : {}", "/v1.0/ui/wizard/componentInstances");
            Integer applicationId = StringUtils.getIntegerFromString(request.params("applicationId"));
            return CompInstanceService.getWizardDetalsForApplication(request, applicationId);
        }), new JsonTransformer());

        get("/v1.0/ui/wizard/applicationMappedInstanceIds/:applicationId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " for applicationId : {}", "/v1.0/ui/wizard/componentInstances");
            return CompInstanceService.getApplicationMappedInstanceIds(request);
        }), new JsonTransformer());

        get("/v1.0/ui/operationModes/CA", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " operationModes/CA");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.COMPONENT_AGENT_OPERATION_MODES);
        }), new JsonTransformer());

        get("/v1.0/ui/operationModes/PSA", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " operationModes/PSA");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.PS_AGENT_OPERATION_MODES);
        }), new JsonTransformer());

        get("/v1.0/ui/responseOptions", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + "responseOptions");
            return MstSubTypeService.getResponseOptions(request, Constants.RESPONSE_OPTIONS);
        }), new JsonTransformer());

        get("/v1.0/ui/thresholdOperations", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + "responseOptions");
            return MstSubTypeService.getResponseOptions(request, Constants.THRESHOLD_OPERATIONS);
        }), new JsonTransformer());

        get("/v1.0/ui/ServerDetailProtocols", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " ServerDetailProtocols");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.SERVER_DETAILS_PROTOCAL);
        }), new JsonTransformer());

        get("/v1.0/ui/proxyProtocols", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " proxyProtocols");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.HTTP_PROXY_PROTOCOLS);
        }), new JsonTransformer());

        get("/v1.0/ui/serverDetails/:agentId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " serverDetails");
            return AgentService.getAgentServerDetails(request);
        }), new JsonTransformer());


        get("/v1.0/ui/wizard/agents", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/wizard/agents");
            return AgentService.getAllAgents(request);
        }), new JsonTransformer());

        get("/v1.0/ui/config/screens", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/config/menue");
            return MstMenuService.getConfigScreens(request);
        }), new JsonTransformer());

        get("/v1.0/ui/role/permissions/:roleId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/role/permissions");
            return MstRoleService.getRolePermissions(request);
        }), new JsonTransformer());


        get("/v1.0/ui/user/permissions/:userId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/user/permissions");
            return ProfileDetailService.getUserPermissions(request);
        }), new JsonTransformer());

        get("/v1.0/ui/userRoles", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userRoles");
            return MstRoleService.getAllRoles(request);
        }), new JsonTransformer());

        get("/v1.0/ui/globalSettings", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/globalSettings");
            return AccountService.getAccountSettings(request);
        }), new JsonTransformer());

        get("/v1.0/ui/userProfiles", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userProfiles");
            return ProfileDetailService.getUserProfileDetails(request);
        }), new JsonTransformer());

        //get logged in user profile
        get("/v1.0/ui/loggedinUserProfile", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userProfiles");
            return ProfileDetailService.getLoggedInUserProfile(request);
        }), new JsonTransformer());

        get("/v1.0/ui/clusterKpiMapping/:clusterId",((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED,"/v1.0/ui/clusterKpiMapping/");
            return ClusterService.getClusterKpisAndGroups(request);
        }),new JsonTransformer());

        // Dashboard DataServices
        get("/v1.0/ui/application/:applicationId/dashboard", ((request, response) ->{
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/:applicationId/dashboard");
            return UiDashBoardService.getDashboardForApplicationId(request);
        }), new JsonTransformer());

        get("/v1.0/ui/application/globalDashboard", ((request, response) ->{
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/globalDashboard");
            return UiDashBoardService.getGlobalDashBoard(request);
        }), new JsonTransformer());

        get("/v1.0/ui/application/:application/dashboardData", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/:application/dashboardData");
            return RequestHandler.requestParser(request);
        }), new JsonTransformer());

        get("/v1.0/ui/dashboard/application/:applicationId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/dashboard/application/");
            return RequestHandler.requestParser(request);
        }), new JsonTransformer());

        get("/v1.0/ui/dashboard/application/:applicationId/topologyView", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/dashboard/application/:applicationId/topologyView");
            return ApplicationCompInstanceMappingService.getTopologyData(request);
        }), new JsonTransformer());

        get("/v1.0/ui/alertProfileTypes", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " alertProfileTypes");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.COMPONENT_AGENT_KPI_TYPES);
        }), new JsonTransformer());

        get("/v1.0/ui/severityProfile/:severityProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/severityProfile/:severityProfileId");
            return SeverityProfileService.getSeverityProfileListView(request, Integer.parseInt
                    (request.params(":severityProfileId")));
        }, new JsonTransformer());

        get("/v1.0/ui/severityProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/severityProfile");
            return SeverityProfileService.getSeverityProfileListView(request, 0);
        }, new JsonTransformer());

        get("/v1.0/ui/severityProfile/mstSubType/:mstSubTypeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/severityProfile/mstSubTypeId/:mstSubTypeId");
            return SeverityProfileService.getAllSeverityProfileUsingMstSubType(request, Integer.parseInt
                    (request.params(":mstSubTypeId")));
        }, new JsonTransformer());

        get("/v1.0/ui/escalationProfile/:escalationProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/escalationProfile/:escalationProfileId");
            return EscalationService.getEscalationProfile(request, Integer.parseInt
                    (request.params(":escalationProfileId")));
        }, new JsonTransformer());

        get("/v1.0/ui/escalationProfiles", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/escalationProfile");
            return EscalationService.getEscalationProfileListView(request);
        }, new JsonTransformer());

        get("/v1.0/ui/escalationProfiles/mstSubType/:mstSubTypeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/escalationProfiles/mstSubType/:mstSubTypeId");
            return EscalationService.getAllEscalationDetailUsingMstSubType(request, Integer.parseInt
                    (request.params(":mstSubTypeId")));
        }, new JsonTransformer());

        get("/v1.0/ui/notificationProfiles", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/notificationProfiles");
            return NotificationProfileService.getAllNotificationProfiles(request);
        }, new JsonTransformer());

        get("/v1.0/ui/notificationProfiles/mstSubType/:mstSubTypeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/notificationProfiles/mstSubTypeId/:mstSubTypeId");
            return NotificationProfileService.getAllNotificationProfilesUsingMstSubType(request, Integer.parseInt
                    (request.params(":mstSubTypeId")));
        }, new JsonTransformer());

        get("/v1.0/ui/coverageWindowProfiles", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/coverageWindowProfiles");
            return CoverageWindowProfileService.getCoverageWindowProfiles(request);
        }, new JsonTransformer());

        get("/v1.0/ui/coverageWindowProfiles/mstSubType/:mstSubTypeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/coverageWindowProfiles/mstSubType/:mstSubTypeId");
            return CoverageWindowProfileService.getAllCoverageWindowProfileUsingMstSubType(request, Integer.parseInt
                    (request.params(":mstSubTypeId")));
        }, new JsonTransformer());

        get("/v1.0/ui/placeholders/:typeId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/placeHolders");
            return MstSubTypeService.getPlaceHolders(request,Constants.COMMON_PLACEHOLDERS);
        }, new JsonTransformer());

        get("/v1.0/ui/notification/type", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui//v1.0/ui/notification/type");
            return NotificationProfileService.getNotificationTypes(request);
        }, new JsonTransformer());

        get("/v1.0/ui/dayoptions", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/dayoptions");
            return CoverageWindowProfileService.getDayOptions(request);
        }, new JsonTransformer());

        get("/v1.0/ui/days", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/days");
            return CoverageWindowProfileService.getDays(request);
        }, new JsonTransformer());

        get("/v1.0/ui/alertProfiles", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfiles");
            return AlertProfileService.getAlertProfileListView(request);
        }, new JsonTransformer());

        get("/v1.0/ui/alertProfile/coverageWindows/:alertProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/coverageWindows/:alertProfileId");
            return AlertProfileService.getCoverageWindowsListView(request, Integer.parseInt
                    (request.params(":alertProfileId")));
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/smtp", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/smtp");
            return SMTPDetailService.getSmtpDetails(request);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/sms", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/smtp");
            return SMSDetailService.getSmsDetail(request);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/security/smtp", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/security/smtp");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.SMTP_SECURITY_OPTIONS);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/sms/parameterTypes", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms/parameterTypes");
            String type = StringUtils.isNotNullNotEmpty(request.queryParams("type")) ? request.queryParams("type")
                    +Constants.SMS_GATEWAY_PARAMETER_TYPES:"";
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, type);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/sms/placeholders", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms/placeholders");
            return MstSubTypeService.getPlaceHolders(request, Constants.SMS_GATEWAY_PLACEHOLDERS);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/sms/protocols", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms/protocols");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.SMS_GATEWAY_PROTOCOLS);
        }, new JsonTransformer());

        get("/v1.0/ui/gateway/sms/methods", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms/methods");
            return MstSubTypeService.getMstSUbTypeObjectForMstType(request, Constants.SMS_GATEWAY_METHODS);
        }, new JsonTransformer());

        get("/v1.0/ui/search", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/search");
            return SearcherService.search(request);
        }, new JsonTransformer());

        get("/v1.0/ui/transactionThreshold/status", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + "/v1.0/ui/transactionThreshold/status");
            ResponseObject responseObject = MstSubTypeService.getResponseOptions(request, Constants.TRANSACTION_THRESHOLD_STATUS);
            List<MstSubTypeObject> mstSubTypeObjectList = (List<MstSubTypeObject>) responseObject.getResult();
            for (MstSubTypeObject mstSubTypeObject : mstSubTypeObjectList){
                //Remove 'Good' transaction status from list for transaction threshold
                if (mstSubTypeObject.getName().equalsIgnoreCase(Constants.TRANSACTION_THRESHOLD_GOOD_STATUS))
                    mstSubTypeObjectList.remove(mstSubTypeObject);
            }
            responseObject.setResult(mstSubTypeObjectList);
            return responseObject;
        }), new JsonTransformer());

        get("/v1.0/ui/transactionThreshold/type", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + "/v1.0/ui/transactionThreshold/type");
            return MstSubTypeService.getResponseOptions(request, Constants.TRANSACTION_THRESHOLD_TYPE);
        }), new JsonTransformer());

        get("/v1.0/ui/alertProfile/transactionThresholdListView", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThresholdListView");
            return TransactionThresholdDetailService.txnThresholdListView(request);
        }, new JsonTransformer());

        get("/v1.0/ui/alertProfile/transactionThreshold/coverageWindows/:alertProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThreshold/coverageWindows/:alertProfileId");
            return TransactionThresholdDetailService.getCoverageWindowsListViewForTxnThreshold(request, Integer.parseInt
                    (request.params(":alertProfileId")));
        }, new JsonTransformer());

        get("/v1.0/ui/alertProfile/transactionThreshold/transactionList/:applicationId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThreshold/transactionList/:applicationId");
            return TransactionService.getAllTransactionsUsingAppId(request, Integer.parseInt
                    (request.params(":applicationId")));
        }, new JsonTransformer());

        get("/v1.0/ui/alertProfile/transactionThreshold/applicationList", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThreshold/applicationList");
            return ApplicationService.getAllApplicationsUsingStatus(request);
        }, new JsonTransformer());

        get("/v1.0/ui/grpcSettings/:grpcSettingsId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/grpcSettings/:grpcSettingsId");
            return GRPCSettingsService.getGRPCSettings(request, Integer.parseInt
                    (request.params(":grpcSettingsId")));
        }, new JsonTransformer());

        get("/v1.0/ui/grpcSettingsListView", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/grpcSettingsListView");
            return GRPCSettingsService.getAllGRPCSettings(request);
        }, new JsonTransformer());

        get("/v1.0/ui/grpcSettingsHostList", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/grpcSettingsHostList");
            return GRPCSettingsService.getAllGRPCSettingsList(request);
        }), new JsonTransformer());

        get("/v1.0/ui/holidayProfileListView", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/holidayProfileListView");
            return HolidayProfileService.getHolidayProfileListView(request);
        }, new JsonTransformer());

        get("/v1.0/ui/holidayProfile/:holidayProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/holidayProfile/:holidayProfileId");
            return HolidayProfileService.getHolidayProfileForId(request, Integer.parseInt
                    (request.params(":holidayProfileId")));
        }, new JsonTransformer());

        get("/v1.0/ui/alertThreshold/holidayProfile/mstListView", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertThreshold/holidayProfile/mstListView");
            return HolidayProfileService.getHolidayProfileMstListView(request);
        }, new JsonTransformer());

        //Post calls
        //To authenticate username/password
        post(Constants.LOGIN_API_PATH, (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, Constants.LOGIN_API_PATH);
            return UserDetailService.loginUser(request);
        }, new JsonTransformer());

        //to logout

        post("/v1.0/ui/applicationType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applicationType");
            return MstSubTypeService.addApplicationType(request);
        }, new JsonTransformer());

        post("/v1.0/ui/severityProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/severityProfile");
            return SeverityProfileService.addSeverityProfile(request);
        }, new JsonTransformer());

        post("/v1.0/ui/escalationProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/escalationProfile");
            return EscalationService.addEscalation(request);
        }, new JsonTransformer());

        post("/v1.0/ui/componentType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentType");
            return MstComponentTypeService.addMstComponentType(request);
        }, new JsonTransformer());

        post("/v1.0/ui/applications", (request, response) -> {
            log.debug(Constants.API_ROUTE_CALLED, "/v1.0/ui/applications : {}", request.body());
            return ApplicationService.addApplications(request);
        }, new JsonTransformer());

        post("/v1.0/ui/application/masterType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/application/masterType");
            return MstSubTypeCrud.addApplicationType(request);
        });

        post("/v1.0/ui/component", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/component");
            AddComponentObject addComponentObject = JsonUtil.deserializeJsonFromRequest(request, TypeToken.of
                    (AddComponentObject.class));
            return MstComponentService.addComponent(addComponentObject, request);
        }), new JsonTransformer());

        post("/v1.0/ui/kpiDetail", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiDetail");
            return KPIDetailsService.addMstKpiDetails(request, true);
        }, new JsonTransformer());

        post("/v1.0/ui/cluster", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/cluster");
            ObjectMapper objectMapper = JsonFactory.create();
            ClusterObject addCluster = objectMapper.fromJson(request.bodyAsBytes(), ClusterObject.class);
            return MstComponentInstanceService.addCluster(request, addCluster);
        }), new JsonTransformer());

        post("/v1.0/ui/componentInstance", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentInstance");
            return CompInstanceService.addComponentInstance(request);
        }), new JsonTransformer());

        post("/v1.0/ui/wizard/componentInstances", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " for applicationId : {}", "/v1.0/ui/wizard/componentInstances");
            return CompInstanceService.addComponentInstanceForHost(request);
        }), new JsonTransformer());

        post("/v1.0/ui/producer", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/producer");
            return MstProducerService.addProducer(request);
        }), new JsonTransformer());

        post("/v1.0/ui/transaction", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transaction");
            return TransactionService.addTransaction(request);
        }), new JsonTransformer());

        post("/v1.0/ui/agent/component", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agent/component");
            return AgentService.addComponentAgent(request);
        }, new JsonTransformer());

        post("/v1.0/ui/agent/JIA", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agent/JIA");
            return AgentService.addJIAAgent(request);
        }, new JsonTransformer());

        post("/v1.0/ui/agent/PSA", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agent/PSA");
            return AgentService.addPSAgent(request);
        }, new JsonTransformer());

        post("/v1.0/ui/wizard/agent", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/wizard/agent");
            return AgentService.addWizardAgents(request);
        }, new JsonTransformer());

        post("/v1.0/ui/userRole", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userRole");
            return MstRoleService.addUserRole(request);
        }, new JsonTransformer());

        put("/v1.0/ui/globalSettings", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/globalSettings");
            return AccountService.saveAccountSettings(request);
        }, new JsonTransformer());

        post("/v1.0/ui/userProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userRole");
            return ProfileDetailService.addUserProfile(request);
        }, new JsonTransformer());

        post("/v1.0/ui/notificationProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/notificationProfile");
            return NotificationProfileService.saveNotificationProfile(request);
        }, new JsonTransformer());

        post("/v1.0/ui/coverageWindow", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/coverageWindow");
            return CoverageWindowProfileService.addCoverageWindow(request);
        }, new JsonTransformer());

        post("/v1.0/ui/alertProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile");
            return AlertProfileService.addAlertProfile(request);
        }, new JsonTransformer());

        post("/v1.0/ui/gateway/smtp", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/smtp");
            return SMTPDetailService.saveSmtpDetails(request);
        }, new JsonTransformer());

        post("/v1.0/ui/gateway/sms", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms");
            return SMSDetailService.saveSmsDetails(request);
        }, new JsonTransformer());

        post("/v1.0/ui/alertProfile/transactionThreshold", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThreshold");
            return TransactionThresholdDetailService.addAlertProfileForTxnThreshold(request);
        }, new JsonTransformer());


        post("/v1.0/ui/kpiGroup", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiGroup");
            return MstKpiGroupService.addKpiGroup(request);
        },new JsonTransformer());

        post("/v1.0/ui/grpcSettings", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/grpcSettings");
            return GRPCSettingsService.addGRPCSettings(request);

        }, new JsonTransformer());

        post("/v1.0/ui/holidayProfile", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/holidayProfile");
            return HolidayProfileService.addHolidayProfile(request);

        }, new JsonTransformer());

        //put calls
        put("/v1.0/ui/logout", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/logout");
            return UserDetailService.logoutUser(request);
        }, new JsonTransformer());

        put("/v1.0/ui/password/reset", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/resetPassword");
            return PasswordService.resetPassword(request);
        }, new JsonTransformer());

        put(Constants.FORGOT_PASSWORD_API_PATH, (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/password/forgot/reset");
            return PasswordService.resetForgotPassword(request);
        }, new JsonTransformer());

        put("/v1.0/ui/componentInstance", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentInstance");
            return CompInstanceService.updateComponentInstance(request);
        }), new JsonTransformer());

        put("/v1.0/ui/component/:componentId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/component/:componentId");
            return MstComponentService.updateComponent(request, Integer.parseInt
                    (request.params(":componentId")));
        }), new JsonTransformer());

        put("/v1.0/ui/applications", (request, response) -> {
            log.debug(Constants.API_ROUTE_CALLED, "/v1.0/ui/applications : {}", request.body());
            return ApplicationService.updateApplications(request);
        }, new JsonTransformer());

        put("/v1.0/ui/applicationType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/applicationType");
            return MstSubTypeService.updateApplicationType(request);
        }, new JsonTransformer());

        put("/v1.0/ui/componentType", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentType");
            return MstComponentTypeService.updateMstComponentType(request);
        }, new JsonTransformer());

        put("/v1.0/ui/user/tokenLastUsedTime", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/user/tokenLastUsedTime");
            return UserDetailService.updateTokenLastUsedTimeForUser(request);
        }), new JsonTransformer());

        put("/v1.0/ui/kpiDetail", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiDetail");
            return KPIDetailsService.updateMstKpiDetails(request, true);
        }, new JsonTransformer());

        put("/v1.0/ui/cluster/:clusterId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/cluster");
            ObjectMapper objectMapper = JsonFactory.create();
            ClusterObject addClusterObject = objectMapper.fromJson(request.bodyAsBytes(), ClusterObject.class);
            return MstComponentInstanceService.updateCluster(request, addClusterObject, request.params("clusterId"));
        }), new JsonTransformer());

        put("/v1.0/ui/producer/:producerId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/ui/producer");
            Integer producerId = StringUtils.getIntegerFromString(request.params("producerId"));
            return MstProducerService.editProducer(request, producerId);
        }), new JsonTransformer());

        put("/v1.0/ui/componentKpiMapping", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/componentKpiMapping");
            return MstComponentVersionKpiMappingService.updateComponentKpiMapping(request);
        }), new JsonTransformer());

        put("/v1.0/ui/clusterKpiMapping/:clusterId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/clusterKpiMapping");
            return MstComponentVersionKpiMappingService.updateClusterKpiMapping(request);
        }), new JsonTransformer());

        put("/v1.0/ui/transaction/:txnId", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/transaction");
            return TransactionService.editTransaction(request);
        }), new JsonTransformer());

        put("/v1.0/ui/agent/component/:agentId", (request, response) -> {
            Integer agentId = Integer.parseInt(request.params(":agentId"));
            log.trace("API route called : {} for agentId : {}", "/v1.0/ui/agent/component", agentId);
            return AgentService.updateComponentAgent(request, agentId);
        }, new JsonTransformer());

        put("/v1.0/ui/agent/JIA/:agentId", (request, response) -> {
            Integer agentId = Integer.parseInt(request.params(":agentId"));
            log.trace("API route called : {} for agentId : {}", "/v1.0/ui/JIA/component", agentId);
            return AgentService.updateJIAAgent(request, agentId);
        }, new JsonTransformer());

        put("/v1.0/ui/agent/PSA/:agentId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/agent/PSA");
            Integer agentId = Integer.parseInt(request.params(":agentId"));
            return AgentService.updatePSAgent(request, agentId);
        }, new JsonTransformer());

        put("/v1.0/ui/userRole/:roleId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userRole");
            return MstRoleService.updateUserRole(request);
        }, new JsonTransformer());

        put("/v1.0/ui/wizard/componentInstances", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED + " for applicationId : {}", "/v1.0/ui/wizard/componentInstances");
            return CompInstanceService.updateComponentInstanceForHost(request);
        }), new JsonTransformer());

        put("/v1.0/ui/userProfile/:userId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/userRole");
            return ProfileDetailService.updateUserProfile(request);
        }, new JsonTransformer());

        put("/v1.0/ui/severityProfile/:severityProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/severityProfile/:severityProfileId");
            return SeverityProfileService.updateSeverityProfile(request, Integer.parseInt
                    (request.params(":severityProfileId")));
        }, new JsonTransformer());

        put("/v1.0/ui/escalationProfile/:escalationProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/escalationProfile/:escalationProfileId");
            return EscalationService.updateEscalationProfile(request, Integer.parseInt
                    (request.params(":escalationProfileId")));
        }, new JsonTransformer());

        put("/v1.0/ui/notificationProfile/:notificationProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/notificationProfile");
            return NotificationProfileService.updateNotificationProfile(request);
        }, new JsonTransformer());

        put("/v1.0/ui/coverageWindow/:coverageWindowProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/coverageWindow");
            return CoverageWindowProfileService.updateCoverageWindow(request);
        }, new JsonTransformer());

        put("/v1.0/ui/alertProfile/:alertProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/:alertProfileId");
            return AlertProfileService.updateAlertProfile(request, Integer.parseInt
                    (request.params(":alertProfileId")));
        }, new JsonTransformer());

        put("/v1.0/ui/gateway/smtp", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/smtp");
            return SMTPDetailService.updateSmtpDetails(request);
        }, new JsonTransformer());

        put("/v1.0/ui/gateway/sms", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/gateway/sms");
            return SMSDetailService.updateSmsDetails(request);
        }, new JsonTransformer());

        put("/v1.0/ui/alertProfile/transactionThreshold/:alertProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/alertProfile/transactionThreshold/:alertProfileId");
            return TransactionThresholdDetailService.updateAlertProfileForTxnThreshold(request, Integer.parseInt
                    (request.params(":alertProfileId")));
        }, new JsonTransformer());


        put("/v1.0/ui/kpiGroup/:kpiGroupId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/kpiGroup");
            return MstKpiGroupService.updateKpiGroup(request);
        }, new JsonTransformer());

        put("/v1.0/ui/grpcSettings/:grpcSettingsId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/grpcSettings/:grpcSettingsId");
            return GRPCSettingsService.updateGRPCSettings(request, Integer.parseInt
                    (request.params(":grpcSettingsId")));
        }, new JsonTransformer());

        put("/v1.0/ui/holidayProfile/:holidayProfileId", (request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/holidayProfile/:holidayProfileId");
            return HolidayProfileService.updateHolidayProfile(request, Integer.parseInt
                    (request.params(":holidayProfileId")));
        }, new JsonTransformer());

        //delete Apis.

        delete("/v1.0/ui/roles", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/roles");
            return MstRoleService.deleteRoles(request);
        }), new JsonTransformer());


        //Non UI routes

        get("/v1.0/agents/identifier/:identifier", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/agents/identifier/{}", request.params(":identifier"));
            // for non ui user account id is not required so added field isUiRoutes to differentiate
            boolean isUiRoutes = false;
            String identifier = request.params(":identifier");
            return AgentService.getAgentForIdentifier(request,response,identifier,isUiRoutes);
        }), new JsonTransformer());

        get("/v1.0/agents", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/agents/{}", request.params(":identifier"));
            ResponseObject responseObject = AgentService.getAgentByType(request, request.queryParams("type"));
            //if the agent configuration is not changed, send response Not-Modified
            String configCheckSum = Commons.getHashCode(responseObject.getResult().toString());
            if (configCheckSum.equals(request.headers("ConfigCheckSum"))) {
                responseObject.setResult(new ArrayList<>());
                response.status(304);
            }
            response.header("ConfigCheckSum", configCheckSum); //send config checksum in response header.
            return responseObject;
        }), new JsonTransformer());


        get("/v1.0/account/:accountId/transactions", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/account/{}/transactions", request.params(":accountId"));
            String txnType = "ALL";
            if (request.queryParams("txnType") != null && (request.queryParams("txnType").equalsIgnoreCase("TCP") ||
                    request.queryParams("txnType")
                            .equalsIgnoreCase("HTTP"))) {
                txnType = request.queryParams("txnType").toUpperCase();
            }
            ResponseObject responseObject = TransactionService.getTransactions(request.params(":accountId"), request.headers("ConfigCheckSum"), txnType);

            String configCheckSum = Commons.getHashCode(responseObject.getResult().toString());

            //if the agent configuration is not changed, send response Not-Modified
            //base 64 Encoded check sum update in agent checksum cache.
//            byte[] stringBytes = configCheckSum.getBytes(Charset.defaultCharset());
//            configCheckSum = Base64.getEncoder().encodeToString(stringBytes);
            if (configCheckSum.equals(request.headers(Constants.CONFIG_CHECK_SUM))) {
                response.header(Constants.CONFIG_CHECK_SUM, configCheckSum);
                responseObject.setResult(new ArrayList<>());
                response.status(304);
            }

            response.header(Constants.CONFIG_CHECK_SUM, configCheckSum);
            return responseObject;
        }), new JsonTransformer());

        //get cluster list
        get("/v1.0/clusters", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "v1.0/clusters");
            boolean isUiRoutes = false;
            return CompInstanceService.getAllClusters(request, isUiRoutes);
        }), new JsonTransformer());

        //get kpi-clusterOperations
        get("/v1.0/kpiDetails", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/kpiDetails");
            boolean isUiRoutes = false;
            return KPIDetailsService.getKpiDetails(request, isUiRoutes);
        }), new JsonTransformer());

        get("/v1.0/ui/dashboard/account/:accountId/podInstances", ((request, response) -> {
            log.trace(Constants.API_ROUTE_CALLED, "/v1.0/ui/dashboard/account/:accountId/podInstances", request
                    .params(":accountId"));
//            ResponseObject responseObject = DashboardService.getPodInstancesByAccount(request.params(":accountId"));
            return null;
        }), new JsonTransformer());

        exception(InvalidTokenException.class, ((exception, request, response) -> {

            log.error("Invalid Token Exception : ", exception);

            Map<String, Object> responseMap = new HashMap<>();
            responseMap.put("result", "InvalidToken");

            ObjectMapper objectMapper = JsonFactory.create();
            String jsonResponse = objectMapper.toJson(responseMap);
            response.status(401);
            response.type("application/json");
            response.header("Access-Control-Allow-Origin", "*");
            response.header("Access-Control-Allow-Headers", "accept, authorization");
            response.body(jsonResponse);
        }));

        exception(AppsOneException.class, (exception, request, response) -> {
            log.error("Business Exception : {}", exception.getMessage(), exception);
            ErrorObject errorObject = new ErrorObject(ResponseConstants.FAILURE, ((AppsOneException) exception).getErrorCode(),
                    ((AppsOneException) exception).getErrorMessage());
            ObjectMapper objectMapper = JsonFactory.create();
            String jsonResponse = objectMapper.toJson(errorObject);
            if (errorObject.getErrorCode().equals("E2008")) {
                response.status(404);
            } else if (errorObject.getErrorCode().equals("E2001")) {
                response.status(500);
            }
            response.type("application/json");
            response.header("Access-Control-Allow-Origin", "*");
            response.header("Access-Control-Allow-Headers", "accept, authorization");
            response.body(jsonResponse);
        });

        exception(Exception.class, (exception, request, response) -> {
            log.error("Exception during REST Call : ", exception);
            ErrorObject errorObject = new ErrorObject(exception.getMessage(), ResponseConstants.FAILURE);
            ObjectMapper objectMapper = JsonFactory.create();
            String jsonResponse = objectMapper.toJson(errorObject);
            response.status(500);
            response.type("application/json");
            response.header("Access-Control-Allow-Origin", "*");
            response.header("Access-Control-Allow-Headers", "accept, authorization");
            response.body(jsonResponse);
        });



        // Http Filter
        after("/v1.0/*", (request, response) -> {
            response.type("application/json");

            // adding callback for JSON-P request
            String callback = request.queryParams("callback");
            if (callback == null) {
                response.type("application/json");
            } else {
                String body = callback + "(" + response.body() + ")";
                response.body(body);
            }
            log.info("Returning API {}", request.pathInfo());
        });
    }


}
