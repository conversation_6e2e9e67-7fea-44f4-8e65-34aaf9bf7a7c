/*
 * Copyright (c) 2015-2016 Appnomic Systems Private Limited, Bangalore
 *
 * All rights reserved
 *
 * The source code in this file is licensed and does not fall under any
 * type of open source license.  No form of reproduction is allowed without
 * prior written consent from Appnomic Systems.
 */
package com.appnomic.appsone.ui;

public final class Constants
{
    // UI https related configurations
    public static final String UISERVICE_DEFAULT_HTTPS_PORT = "9191";
    public static final String UISERVICE_HTTPS_PORT_PROPERTY_NAME = "https.port";

    public static final String UISERVICE_DEFAULT_HTTPS_KEYSTORE_PASSWORD = "serverpw";
    public static final String UISERVICE_HTTPS_KEYSTORE_PASSWORD_PROPERTY_NAME = "keystore.password";

    public static final String UISERVICE_DEFAULT_HTTPS_KEYSTORE_FILE_NAME = "keystore.jks";
    public static final String UISERVICE_HTTPS_KEYSTORE_FILE_NAME_PROPERTY_NAME = "keystore.file.name";

    public static final String MYSQL_SERVER_DEFAULT_IP = "127.0.0.1";
    public static final String MYSQL_SERVER_IP_PROPERTY_NAME = "mysql.server.ip";

    private Constants()
    {
    }

}