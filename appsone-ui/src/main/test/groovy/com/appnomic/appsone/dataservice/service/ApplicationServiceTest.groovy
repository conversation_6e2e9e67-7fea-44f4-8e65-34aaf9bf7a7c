
package com.appnomic.appsone.dataservice.service

import com.appnomic.appsone.ui.BootStrapUIService
import com.jayway.restassured.response.Response
import groovy.test.GroovyAssert
import org.junit.AfterClass
import org.junit.BeforeClass
import org.junit.Test
import static com.jayway.restassured.RestAssured.expect;
import com.jayway.restassured.RestAssured;
import static org.hamcrest.Matchers.equalTo;
import com.appnomic.appsone.dataservice.TestEnvironment

import static org.hamcrest.Matchers.hasItems

/**
 * Created by chaitra on 28/4/16.
 */


class ApplicationServiceTest{

    @BeforeClass
    def static void init()
    {
        TestEnvironment.initialiseH2DB()

        BootStrapUIService.main();

        RestAssured.baseURI="http://localhost:9191"

    }

    //@Test
    def void authenticateUser()
    {
        String bodyStr="{" +
                "    \"userName\" : \"admin\"," +
                "    \"password\" : \"KICekGmsle4brMVtBhVgZA==\"," +
                "    \"initializationVector\" : \"73b6569f3e4b3f95\"" +
                "}"
        Response response = RestAssured.given().contentType("application/json").body(bodyStr).when().post("v1.0/user/authenticate").then().statusCode(200);
        String authToken = response.body().jsonPath().getInt("authToken");//response.getHeader("authToken");
        Boolean result = new Boolean(response.body().jsonPath().getInt("result"));

        //.then().assertThat().body("result", true) String authToken =
    }

    //@Test
    def void retreivingMasterTypes()
    {
        RestAssured.get("/v1.0/application/getAllMasterTypes").then().assertThat().body("responseStatus", equalTo("SUCCESS"));
        RestAssured.get("/v1.0/application/getAllMasterTypes").then().assertThat().body("result.masterSubTypeName", hasItems("KPI Type","Application Type"));
        //expect().body("Result", equalTo(“Hello world!”)).when().get("/v1.0/application/getAllMasterTypes");
    }

    @AfterClass
    def static void destroy()
    {
        System.setProperty "ISTestRun", "false"
    }


}