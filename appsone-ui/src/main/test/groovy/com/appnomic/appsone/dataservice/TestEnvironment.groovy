

package com.appnomic.appsone.dataservice

import groovy.sql.Sql

class TestEnvironment
{
    private static initialised = false

    static def initialiseH2DB()
    {
        if (!initialised)
        {
            initialised = true
            System.setProperty "ISTestRun", "true"
            Sql.newInstance("jdbc:h2:mem:appsoneDB;DB_CLOSE_DELAY=-1;MODE=MySQL;INIT=RUNSCRIPT FROM './src/main/test/resources/create.sql'\\;RUNSCRIPT FROM './src/main/test/resources/populate.sql'", "root", "", "org.h2.Driver")

           /* if(System.getProperty("ISTestRun").equals("true")){
                return Persistence.createEntityManagerFactory("AppsOneH2Dao");
            }*/
        }
    }
}