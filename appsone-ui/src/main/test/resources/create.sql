
CREATE SCHEMA AppsOneH2Dao;

CREATE TABLE IF NOT EXISTS `AppsOneH2Dao`.`profile_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `email_address` VARCHAR(256) NULL,
  `phone_number` INT UNSIGNED NULL,
  `country_code` VARCHAR(32) NULL,
  `name` VARCHAR(64) NULL,
  `last_login_time` TIMESTAMP NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `dbts` INT NOT NULL,
  PRIMARY KEY (`id`));

CREATE TABLE IF NOT EXISTS `AppsOneH2Dao`.`user_details` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `login_id` VARCHAR(256) NOT NULL,
  `password` VARCHAR(256) NOT NULL,
  `status` TINYINT NOT NULL DEFAULT 1,
  `last_failure_attempts` TINYINT NULL,
  `salt` TEXT NULL,
  `token` VARCHAR(256) NULL,
  `token_created_time` D<PERSON><PERSON><PERSON>E NULL,
  `profile_details_id` INT NOT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `dbts` INT NOT NULL,
  `account_id` INT NOT NULL,
  `token_last_used_time` DATETIME NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_user_details_profile_details1_idx` (`profile_details_id` ASC),
  INDEX `user_details_account1_idx` (`account_id` ASC),
  CONSTRAINT `fk_user_details_profile_details1`
    FOREIGN KEY (`profile_details_id`)
    REFERENCES `AppsOneH2Dao`.`profile_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);

CREATE TABLE IF NOT EXISTS `AppsOneH2Dao`.`mst_type` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `type` VARCHAR(45) NOT NULL,
  `description` VARCHAR(128) NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NOT NULL,
  `dbts` INT NOT NULL,
  `user_details_id` INT NOT NULL,
  `account_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_mst_type_user_details1_idx` (`user_details_id` ASC),
  INDEX `mst_type_account1_idx` (`account_id` ASC),
  CONSTRAINT `fk_mst_type_user_details1`
    FOREIGN KEY (`user_details_id`)
    REFERENCES `AppsOneH2Dao`.`user_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION);


 CREATE TABLE `AppsOneH2Dao`.`mst_sub_type` (
   `id` int(11) NOT NULL AUTO_INCREMENT,
   `name` varchar(45) NOT NULL,
   `mst_type_id` int(11) NOT NULL,
   `created_time` datetime NOT NULL,
   `updated_time` datetime NOT NULL,
   `dbts` int(11) NOT NULL,
   `user_details_id` int(11) NOT NULL,
   `account_id` int(11) NOT NULL,
   `description` varchar(256) DEFAULT NULL,
   `status` int(4) DEFAULT NULL,
   PRIMARY KEY (`id`),
   KEY `fk_mst_sub_type_mst_type_idx` (`mst_type_id`),
   KEY `fk_mst_sub_type_user_details1_idx` (`user_details_id`),
   KEY `mst_sub_type_account1_idx` (`account_id`),
   CONSTRAINT `fk_mst_sub_type_mst_type` FOREIGN KEY (`mst_type_id`) REFERENCES `AppsOneH2Dao`.`mst_type` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION,
   CONSTRAINT `fk_mst_sub_type_user_details1` FOREIGN KEY (`user_details_id`) REFERENCES `AppsOneH2Dao`.`user_details` (`id`) ON DELETE NO ACTION ON UPDATE NO ACTION
 );

CREATE TABLE IF NOT EXISTS `AppsOneH2Dao`.`application` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `name` VARCHAR(45) NULL,
  `description` VARCHAR(45) NULL,
  `account_id` INT NULL,
  `mst_sub_type_id` INT NOT NULL,
  `is_maintenance` TINYINT(1) NULL DEFAULT 0,
  `status` TINYINT NOT NULL,
  `maintenance_profile_id` INT NULL,
  `created_time` DATETIME NOT NULL,
  `updated_time` DATETIME NULL,
  `dbts` INT NOT NULL,
  `user_details_id` INT NOT NULL,
  PRIMARY KEY (`id`),
  INDEX `fk_application_mst_sub_type1_idx` (`mst_sub_type_id` ASC),
  INDEX `fk_application_user_details1_idx` (`user_details_id` ASC),
  INDEX `application_account1_idx` (`account_id` ASC),
  CONSTRAINT `fk_application_mst_sub_type1`
    FOREIGN KEY (`mst_sub_type_id`)
    REFERENCES `AppsOneH2Dao`.`mst_sub_type` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION,
  CONSTRAINT `fk_application_user_details1`
    FOREIGN KEY (`user_details_id`)
    REFERENCES `AppsOneH2Dao`.`user_details` (`id`)
    ON DELETE NO ACTION
    ON UPDATE NO ACTION
);