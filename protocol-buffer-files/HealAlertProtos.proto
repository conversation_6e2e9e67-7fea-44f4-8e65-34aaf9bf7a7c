syntax = "proto3";
package protbuf;

option java_package = "com.appnomic.appsone.common.protbuf";
option java_outer_classname = "HealAlertProtos";

message HealAlertEvent {
    string anomalyId = 1;
    string alertTime = 2;
    string alertStatus = 3;
    int64 identifiedTime = 4;
    string entityType = 5;
    string entityId = 6;
    string currentSeverityId = 7;
    string categoryId = 8;
    string kpiAttribute = 9;
    int32 kpiId = 10;
    string kpiIdentifier = 11;
    repeated string serviceId = 12;
    AlertMetadata metadata = 13;
    string operationType = 14;
    string thresholdType = 15;
    string persistence = 16;
    string suppression = 17;
    string alertType = 18;
    AlertThresholdInfo thresholds = 19;
    string anomalyScore = 20;
    string value = 21;
    string timestamp = 22;
}

message AlertMetadata {
    string isMaintenanceExcluded = 1;
    string violationLevel = 2;
    string isInformatic = 3;
    string kpiType = 4;
    string attributeName = 5;
}

message AlertThresholdInfo {
    double upper = 1;
    double lower = 2;
}
