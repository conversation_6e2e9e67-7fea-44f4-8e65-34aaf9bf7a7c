syntax = "proto3";
package protbuf;

option java_package = "com.appnomic.appsone.common.protbuf";
option java_outer_classname = "HealAnomalyProtos";

message HealAnomalyEvent {
    string anomalyId = 1;
    int64 anomalyStartTime = 2;
    int64 anomalyEndTime = 3;
    int64 anomalyTime = 4;
    string anomalyStatus = 5;
    int64 identifiedTime = 6;
    string entityType = 7;
    string entityId = 8;
    string lastSeverityId = 9;
    string startSeverityId = 10;
    string categoryId = 11;
    string kpiAttribute = 12;
    int32 kpiId = 13;
    string kpiIdentifier = 14;
    repeated string serviceId = 15;
    AnomalyMetadata metadata = 16;
    string operationType = 17;
    repeated string signalIds = 18;
    string thresholdType = 19;
    ThresholdInfo lastThresholdsMeet = 20;
    string anomalyScore = 21;
    string value = 22;
    string closingReason = 23;
    string timestamp = 24;
}

message AnomalyMetadata {
    string isMaintenanceExcluded = 1;
    string violationLevel = 2;
    string serviceIdentifier = 3;
    string isInformatic = 4;
    string kpiType = 5;
    string attributeName = 6;
    int32 remaindersCount = 7;
    int32 closeWindowResetCount = 8;
    int32 databreakResetCount = 9;
    int32 lowPersistenceMeetCount = 10;
    int32 lowSuppressionMeetCount = 11;
    int32 mediumPersistenceMeetCount = 12;
    int32 mediumSuppressionMeetCount = 13;
    int32 highPersistenceMeetCount = 14;
    int32 highSuppressionMeetCount = 15;
}

message ThresholdInfo {
    double upper = 1;
    double lower = 2;
}
