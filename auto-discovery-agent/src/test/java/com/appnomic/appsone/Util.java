package com.appnomic.appsone;

import com.appnomic.appsone.common.beans.discovery.Attribute;
import com.appnomic.appsone.common.beans.discovery.AttributeAccess;
import com.appnomic.appsone.common.beans.discovery.NetworkInterface;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.util.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.common.util.StringUtils;
import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.util.Constants;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.File;
import java.net.InetAddress;
import java.util.ArrayList;
import java.util.List;

public class Util {

    private static final Logger logger = LoggerFactory.getLogger(BasicParser.class);

    public static List<NetworkInterface> getNetworkInterfacesList() {
        List<NetworkInterface> networkInterfaceList = new ArrayList<>();

        NetworkInterface networkInterface = new NetworkInterface();
        networkInterface.setNetworkInterfaceIdentifier("C63C5ED1C12C94447A65E611D3D6DCC0");
        networkInterface.setInterfaceName("ens160");
        networkInterface.setInterfaceType("Ethernet");
        networkInterface.setInterfaceIP("**************");
        networkInterface.setHardwareAddress("00:50:56:92:C7:73");
        networkInterface.setInterfaceDescription("Intel(R) Wireless-AC 9560 160MHz");
        networkInterface.setInterfaceMask("*************");

        NetworkInterface virtualNetworkInterface = new NetworkInterface();
        virtualNetworkInterface.setNetworkInterfaceIdentifier("8AF1F11B18E1997E6595A83CC7DD4A9D");
        virtualNetworkInterface.setInterfaceName("eth24");
        virtualNetworkInterface.setInterfaceType("Ethernet");
        virtualNetworkInterface.setInterfaceIP("************");
        virtualNetworkInterface.setHardwareAddress("00:09:0F:AA:00:01");
        virtualNetworkInterface.setInterfaceDescription("Fortinet SSL VPN Virtual Ethernet Adapter");
        virtualNetworkInterface.setInterfaceMask("***************");

        networkInterfaceList.add(networkInterface);
        networkInterfaceList.add(virtualNetworkInterface);
        return networkInterfaceList;
    }

    public static Attribute getHostAttribute() {
        List<AttributeAccess> hostAttributeAccessList = new ArrayList<>();

        AttributeAccess hostAttributeAccess = new AttributeAccess();
        hostAttributeAccess.setMethod(DiscoveryMethod.INHERIT.getValue());
        hostAttributeAccess.setValue("HOST");
        hostAttributeAccess.setPriority(1);
        hostAttributeAccessList.add(hostAttributeAccess);

        Attribute hostAttribute = new Attribute();
        hostAttribute.setAttributeName("HostAddress");
        hostAttribute.setIsMandatory(1);
        hostAttribute.setAccess(hostAttributeAccessList);

        return hostAttribute;
    }

    public static Attribute getLimoAttribute(List<AttributeAccess> limoAttributeAccess) {

        Attribute limoAttribute = new Attribute();
        limoAttribute.setAttributeName(Constants.FINACLE_LIMO_SERVER_ATTRIBUTE_NAME);
        limoAttribute.setIsMandatory(0);
        limoAttribute.setAccess(limoAttributeAccess);

        return limoAttribute;
    }

    public static Attribute getLisrvrAttribute(List<AttributeAccess> lisrvrAttributeAccess) {

        Attribute lisrvrAttribute = new Attribute();
        lisrvrAttribute.setAttributeName(Constants.FINACLE_LI_SERVER_ATTRIBUTE_NAME);
        lisrvrAttribute.setIsMandatory(0);
        lisrvrAttribute.setAccess(lisrvrAttributeAccess);

        return lisrvrAttribute;
    }

    public static Attribute getMariaAttribute(List<AttributeAccess> mariaAttributeAccess) {

        Attribute mariaAttribute = new Attribute();
        mariaAttribute.setAttributeName(Constants.FINACLE_MARIA_SERVER_ATTRIBUTE_NAME);
        mariaAttribute.setIsMandatory(0);
        mariaAttribute.setAccess(mariaAttributeAccess);

        return mariaAttribute;
    }

    public static Attribute getBcMonAttribute(List<AttributeAccess> bcMonAttributeAccess) {

        Attribute mariaAttribute = new Attribute();
        mariaAttribute.setAttributeName(Constants.FINACLE_BCMON_SERVER_ATTRIBUTE_NAME);
        mariaAttribute.setIsMandatory(0);
        mariaAttribute.setAccess(bcMonAttributeAccess);

        return mariaAttribute;
    }

    public static Attribute getBcEchoAttribute(List<AttributeAccess> bcEchoAttributeAccess) {

        Attribute mariaAttribute = new Attribute();
        mariaAttribute.setAttributeName(Constants.FINACLE_BCECHO_SERVER_ATTRIBUTE_NAME);
        mariaAttribute.setIsMandatory(0);
        mariaAttribute.setAccess(bcEchoAttributeAccess);

        return mariaAttribute;
    }

    public static Attribute getMonitorPortAttribute(List<AttributeAccess> monitorPortAttributeAccessList) {

        Attribute monitorPortAttribute = new Attribute();
        monitorPortAttribute.setAttributeName(Constants.MONITOR_PORT);
        monitorPortAttribute.setIsMandatory(1);
        monitorPortAttribute.setAccess(monitorPortAttributeAccessList);

        return monitorPortAttribute;
    }

    public static Attribute getIMBQueueManagerAttribute(List<AttributeAccess> queueManagerAttributeAccessList) {

        Attribute queueManagerAttribute = new Attribute();
        queueManagerAttribute.setAttributeName(Constants.IBM_MQ_QUEUE_MANAGER_ATTRIBUTE_NAME);
        queueManagerAttribute.setIsMandatory(1);
        queueManagerAttribute.setAccess(queueManagerAttributeAccessList);

        return queueManagerAttribute;
    }

    public static AttributeAccess getListenAttributeAccess() {
        AttributeAccess AJPPortAttributeAccess = new AttributeAccess();
        AJPPortAttributeAccess.setMethod(DiscoveryMethod.APACHE_PARSER.getValue());
        AJPPortAttributeAccess.setValue(Constants.APACHE_LISTEN_ATTRIBUTE_VALUE);
        AJPPortAttributeAccess.setPriority(1);
        return AJPPortAttributeAccess;
    }

    public static AttributeAccess getMySqlAttributeAccess() {
        AttributeAccess MySqlAttributeAccess = new AttributeAccess();
        MySqlAttributeAccess.setMethod(DiscoveryMethod.INI_PARSER.getValue());
        MySqlAttributeAccess.setValue(Constants.PORT_ATTRIBUTE_VALUE);
        MySqlAttributeAccess.setPriority(1);
        return MySqlAttributeAccess;
    }

    public static AttributeAccess getNodeJsAttributeAccess() {
        AttributeAccess MySqlAttributeAccess = new AttributeAccess();
        MySqlAttributeAccess.setMethod(DiscoveryMethod.FILE_PARSER.getValue());
        MySqlAttributeAccess.setValue(Constants.NODEJS_PORT_ATTRIBUTE_VALUE);
        MySqlAttributeAccess.setPriority(1);
        return MySqlAttributeAccess;
    }

    public static AttributeAccess getLimoAttributeAccess() {
        AttributeAccess limoAttributeAccess = new AttributeAccess();
        limoAttributeAccess.setMethod(Constants.FINACLE_PARSER_NAME);
        limoAttributeAccess.setValue("_");
        limoAttributeAccess.setPriority(1);
        return limoAttributeAccess;
    }

    public static AttributeAccess getLisrvrAttributeAccess() {
        AttributeAccess lisrvrAttributeAccess = new AttributeAccess();
        lisrvrAttributeAccess.setMethod(Constants.FINACLE_PARSER_NAME);
        lisrvrAttributeAccess.setValue(Constants.FINACLE_LISRVR_ATTRIBUTE_VALUE);
        lisrvrAttributeAccess.setPriority(1);
        return lisrvrAttributeAccess;
    }

    public static AttributeAccess getMariaAttributeAccess() {
        AttributeAccess mariaAttributeAccess = new AttributeAccess();
        mariaAttributeAccess.setMethod(Constants.FINACLE_PARSER_NAME);
        mariaAttributeAccess.setValue(Constants.FINACLE_MARIA_ATTRIBUTE_VALUE);
        mariaAttributeAccess.setPriority(1);
        return mariaAttributeAccess;
    }

    //getBcMonAttributeAccess
    public static AttributeAccess getBcMonAttributeAccess() {
        AttributeAccess mariaAttributeAccess = new AttributeAccess();
        mariaAttributeAccess.setMethod(Constants.FINACLE_PARSER_NAME);
        mariaAttributeAccess.setValue(Constants.FINACLE_BCMON_ATTRIBUTE_VALUE);
        mariaAttributeAccess.setPriority(1);
        return mariaAttributeAccess;
    }

    public static AttributeAccess getBcEchoAttributeAccess() {
        AttributeAccess mariaAttributeAccess = new AttributeAccess();
        mariaAttributeAccess.setMethod(Constants.FINACLE_PARSER_NAME);
        mariaAttributeAccess.setValue(Constants.FINACLE_BCECHO_ATTRIBUTE_VALUE);
        mariaAttributeAccess.setPriority(1);
        return mariaAttributeAccess;
    }

    public static AttributeAccess getOracleDbAttributeAccess() {
        AttributeAccess MySqlAttributeAccess = new AttributeAccess();
        MySqlAttributeAccess.setMethod(DiscoveryMethod.ORA_PARSER.getValue());
        MySqlAttributeAccess.setValue(Constants.PORT_ATTRIBUTE_VALUE);
        MySqlAttributeAccess.setPriority(1);
        return MySqlAttributeAccess;
    }

    public static List<Process> getProcessListFromFile(String fileName)
    {
        List<Process> processList = new ArrayList<>();
        try (BufferedReader br = new BufferedReader(PathFinder.loadConfigFile(fileName))) {
            String line = br.readLine();
            while (line != null) {
                String [] proDetail = line.trim().split(Constants.PROCESS_SPLIT_PATTERN);
                if(proDetail[0] == null && proDetail[1] == null && proDetail[2] == null) {
                    continue;
                }
                String processName = line.split(" " + proDetail[1].trim() + " ")[1].trim();
                Process process = new Process();
                process.setPid(proDetail[0].trim());
                process.setProcessName("dummy" + File.separator + "test");
                process.setProcessArgs(processName);
                process.setProcessCurrentWorkingDirectory(File.separator);
                process.setProcessIdentifier(NodeIdGenerator.generateNodeIdForProcess(process, InetAddress.getLocalHost().getHostName()));
                processList.add(process);
                line = br.readLine();
            }
        }
        catch (Exception e)
        {
            logger.error("Error while creating process list from text file : {}",e.getMessage(), e);
        }
        return processList;
    }

    public static String getRunningProcessFromFile(String fileName) {

        String runningProcess;

        StringBuilder stringBuilder = new StringBuilder();

        try (BufferedReader br = new BufferedReader(PathFinder.loadConfigFile(fileName))) {
            String line = br.readLine();
            while (line != null) {
                stringBuilder.append(line).append("\n");
                line = br.readLine();
            }
        } catch (Exception e) {
            logger.error("Error while creating process list from text file : {}", e);
        }

        runningProcess = stringBuilder.toString();

        return runningProcess;
    }
}










