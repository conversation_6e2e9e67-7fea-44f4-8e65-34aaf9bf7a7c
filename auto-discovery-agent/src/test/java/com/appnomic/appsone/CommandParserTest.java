package com.appnomic.appsone;

import com.appnomic.appsone.common.beans.discovery.DiscoveryAgentArgs;
import com.appnomic.appsone.common.enums.DiscoveryMode;
import com.appnomic.appsone.parser.CommandParser;
import com.appnomic.appsone.util.Utils;
import org.junit.Test;

import java.io.File;

public class CommandParserTest {

    @Test
    public void isOfflineMode() {
        String[] args = {"-m", "offline", "-p", "."};
        DiscoveryAgentArgs discoveryAgentArgs;
        try {
            CommandParser commandParser = new CommandParser(args);
            discoveryAgentArgs = commandParser.parse();
            assert discoveryAgentArgs.getMode().equals(DiscoveryMode.OFFLINE);
        } catch (Exception e) {
            assert false;
        }
    }

    @Test
    public void isOnlineMode() {
        String[] args = {"-m", "online", "-p", ".", "-u", "http://localhost:8996"};
        DiscoveryAgentArgs discoveryAgentArgs;
        try {
            CommandParser commandParser = new CommandParser(args);
            discoveryAgentArgs = commandParser.parse();
            assert discoveryAgentArgs.getMode().equals(DiscoveryMode.ONLINE);
        } catch (Exception e) {
            assert false;
        }
    }

    @Test
    public void isDualMode() {
        String[] args = {"-m", "dual", "-p", ".", "-u", "http://localhost:8996"};
        try {
            CommandParser commandParser = new CommandParser(args);
            DiscoveryAgentArgs discoveryAgentArgs = commandParser.parse();
            assert discoveryAgentArgs.getMode().equals(DiscoveryMode.DUAL);
        } catch (Exception e) {
            assert false;
        }
    }

    @Test
    public void invalidMode() {
        String[] args = {"-m", "invalidMode", "-p", ".",};
        try {
            CommandParser commandParser = new CommandParser(args);
            commandParser.parse();
        } catch (Exception e) {
            assert e.getMessage().equals("No enum constant com.appnomic.appsone.common.enums.DiscoveryMode.INVALIDMODE");
        }
    }

    @Test
    public void offlinePathMissing() {
        String[] args = {"-m", "offline"};
        try {
            CommandParser commandParser = new CommandParser(args);
            commandParser.parse();
        } catch (Exception e) {
            assert e.getMessage().equals("Invalid output directory: null");
        }
    }

    @Test
    public void onlineModeCcUrlMissing() {
        String[] args = {"-m", "online", "-p", ".",};
        try {
            CommandParser commandParser = new CommandParser(args);
            commandParser.parse();
        } catch (Exception e) {
            assert e.getMessage().equals("Invalid Control Centre Endpoint: null");
        }
    }

    @Test
    public void dualModeCcUrlMissing() {
        String[] args = {"-m", "dual", "-p", ".",};
        try {
            CommandParser commandParser = new CommandParser(args);
            commandParser.parse();
        } catch (Exception e) {
            assert e.getMessage().equals("Invalid Control Centre Endpoint: null");
        }
    }

    @Test
    public void dualModePathMissing() {
        String[] args = {"-m", "dual", "-u", "http://localhost:8996"};
        try {
            CommandParser commandParser = new CommandParser(args);
            commandParser.parse();
        } catch (Exception e) {
            assert e.getMessage().equals("Invalid output directory: null");
        }
    }

    @Test
    public void writeFileToDiskTest() {

        try {
            String filename = Utils.writeFileToDisk(".", "testFile", SampleData.DISCOVERY_JSON);
            File newlyCreatedFile = new File(filename);
            assert newlyCreatedFile.exists();
            newlyCreatedFile.delete();
        } catch (Exception e) {
            assert false;
        }
    }

    @Test
    public void RunPreReqCheck() {
        String[] args = {"-m", "offline", "-p", ".", "-prc", "true"};
        DiscoveryAgentArgs discoveryAgentArgs;
        try {
            CommandParser commandParser = new CommandParser(args);
            discoveryAgentArgs = commandParser.parse();
            assert discoveryAgentArgs.isPreReqCheck();
        } catch (Exception e) {
            assert false;
        }
    }

    @Test
    public void DoNotRunPreReqCheck() {
        String[] args = {"-m", "offline", "-p", ".", "-prc", "false"};
        DiscoveryAgentArgs discoveryAgentArgs;
        try {
            CommandParser commandParser = new CommandParser(args);
            discoveryAgentArgs = commandParser.parse();
            assert !discoveryAgentArgs.isPreReqCheck();
        } catch (Exception e) {
            assert false;
        }
    }

}
