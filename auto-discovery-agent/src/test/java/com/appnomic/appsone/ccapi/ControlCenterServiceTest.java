package com.appnomic.appsone.ccapi;

import com.appnomic.appsone.SampleData;
import org.junit.Test;

public class ControlCenterServiceTest {

    @Test
    public void pushDiscoveryDataCCUnreachable() {
        try {
            ControlCenterService.pushDiscoveryDataJson("https://keycloak.appnomic:8996/heal-controlcenter", SampleData.DISCOVERY_JSON);
            assert true;
        } catch (Throwable e) {
            assert false;
        }
    }

/*
    @Test
    public void getComponentsCCUnreachable() {
        assert ControlCenterService.getComponents("https://keycloak.appnomic:8996/appsone-controlcenter").size() == 0;
    }
*/

    @Test
    public void pullConfigurationEntities() {
        ControlCenterService.getConfigurationEntities("https://keycloak.appnomic:8996/appsone-controlcenter");
    }
}
