package com.appnomic.appsone.ccapi;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.junit.Test;

import java.util.List;

public class YamlFileLoaderTest {

    @Test
    public void componentsFromBackUpFile() {
        List<Component> components = YamlFileLoader.getComponents();
        assert components.size() > 0;
    }

    @Test
    public void atLeastOneAttributeFoundForEachComponent() {
        List<Component> components = YamlFileLoader.getComponents();
        for (Component component : components) {
            assert component.getAttributes().size() > 0;
        }
    }

    @Test
    public void serverListFromBackUpFile() {
        List<Server> serverList = YamlFileLoader.getServerList();
        assert serverList.size() == 4;
    }

    @Test
    public void entityListFromBackUpFile() {
        List<Entity> entityList = YamlFileLoader.getConfigurationEntities();
        assert entityList.size() > 0;
    }

    @Test
    public void loadVersionInfoYamlFile() {
        List<ComponentVersion> versionInfoList = YamlFileLoader.getComponentsVersion();
        assert versionInfoList.size() > 1;
    }

    @Test
    public void loadScriptInfoYamlFile() {
        List<ComponentScript> serverList = YamlFileLoader.getComponentScripts();
        assert serverList.size() > 1;
    }

    @Test
    public void validateDefaultPortsFromYamlFile() {
        List<Component> componentList = YamlFileLoader.getComponents();
        for (Component component : componentList) {
            int defaultPort = YamlFileLoader.getDefaultMonitorPortForComponent(component.getComponentId());
            if (defaultPort != 0) {
                assert defaultPort > 50;
            }
        }
    }
}
