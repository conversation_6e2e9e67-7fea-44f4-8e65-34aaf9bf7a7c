package com.appnomic.appsone.netstat;

import com.appnomic.appsone.SampleData;
import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.common.beans.discovery.NetStatOutput;
import com.appnomic.appsone.netstat.aix.AIXNetstat;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import org.junit.Test;

import java.util.Arrays;

public class AIXNetstatTest {

    @Test
    public void getConnections() {
        Host host = new Host();
        host.setOperatingSystem(Constants.AIX_PLATFORM);

        Netstat netstat = NetstatFactory.getInstance().getNetstat(host);
        assert netstat instanceof AIXNetstat;

        NetStatOutput netStatOutput = new NetStatOutput();
        netStatOutput.setRegexPattern(AIXNetstat.getAix_netstat_pattern());
        netStatOutput.setBashCommand(AIXNetstat.getAix_netstat_command_ESTABLISHED());
        netStatOutput.setRawNetStatLines(Arrays.asList(SampleData.AIX_OS_NETSTAT_ESTABLISHED.split("\n")));

        Utils.getNetstatResults(netStatOutput, "/bin/bash", false);

        host = netstat.getConnections(host, netStatOutput);

        assert host.getConnections().size() == 13;

    }

    @Test
    public void getEndpoints() {
        Host host = new Host();
        host.setOperatingSystem(Constants.AIX_PLATFORM);

        Netstat netstat = NetstatFactory.getInstance().getNetstat(host);
        assert netstat instanceof AIXNetstat;

        NetStatOutput netStatOutput = new NetStatOutput();
        netStatOutput.setRegexPattern(AIXNetstat.getAix_netstat_pattern());
        netStatOutput.setBashCommand(AIXNetstat.getAix_netstat_command_LISTEN());
        netStatOutput.setRawNetStatLines(Arrays.asList(SampleData.AIX_OS_NETSTAT_LISTEN.split("\n")));


        Utils.getNetstatResults(netStatOutput, "/bin/bash", false);

        host = netstat.getEndpoints(host, netStatOutput);

        assert host.getEndpoints().size() == 13;
    }
}
