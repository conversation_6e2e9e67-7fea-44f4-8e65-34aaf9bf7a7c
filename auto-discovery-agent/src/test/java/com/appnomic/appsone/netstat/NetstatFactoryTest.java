package com.appnomic.appsone.netstat;

import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.netstat.aix.AIXNetstat;
import com.appnomic.appsone.netstat.sigar.SigarNetstat;
import com.appnomic.appsone.netstat.sun.SunOSNetstat;
import com.appnomic.appsone.util.Constants;
import org.junit.Test;

public class NetstatFactoryTest {

    @Test
    public void isAixInstance() {
        Host host = new Host();
        host.setOperatingSystem(Constants.AIX_PLATFORM);

        Netstat netstatAix = NetstatFactory.getInstance().getNetstat(host);

        assert netstatAix instanceof AIXNetstat;
    }

    @Test
    public void isSunOSInstance() {
        Host host = new Host();
        host.setOperatingSystem(Constants.SUN_OS_PLATFORM);

        Netstat netstatSunOS = NetstatFactory.getInstance().getNetstat(host);

        assert netstatSunOS instanceof SunOSNetstat;
    }

    @Test
    public void isSigarInstance() {
        Host host = new Host();
        host.setOperatingSystem("Windows 10");

        Netstat netstatSigar = NetstatFactory.getInstance().getNetstat(host);

        assert netstatSigar instanceof SigarNetstat;
    }

}
