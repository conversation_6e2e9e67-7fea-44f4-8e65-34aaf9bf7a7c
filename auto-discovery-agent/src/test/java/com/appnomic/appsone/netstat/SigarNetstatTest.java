package com.appnomic.appsone.netstat;

import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.netstat.sigar.SigarNetstat;
import org.junit.Test;
import oshi.SystemInfo;

public class SigarNetstatTest {

    @Test
    public void getConnections() {
        SystemInfo systemInfo = new SystemInfo();
        String osFamily = systemInfo.getOperatingSystem().getFamily();
        String osVersion = systemInfo.getOperatingSystem().getVersion().getVersion();
        Host host = new Host();
        host.setOperatingSystem(osFamily);
        host.setOperatingSystemVersion(osVersion);

        if (host.getOperatingSystem().contains("Windows")) {

            Netstat netstat = NetstatFactory.getInstance().getNetstat(host);
            assert netstat instanceof SigarNetstat;

            host = netstat.getConnections(host, null);

            assert host.getConnections().size() > 0;
        }
    }

    @Test
    public void getEndpoints() {
        SystemInfo systemInfo = new SystemInfo();
        String osFamily = systemInfo.getOperatingSystem().getFamily();
        String osVersion = systemInfo.getOperatingSystem().getVersion().getVersion();
        Host host = new Host();
        host.setOperatingSystem(osFamily);
        host.setOperatingSystemVersion(osVersion);

        if (host.getOperatingSystem().contains("Windows")) {

            Netstat netstat = NetstatFactory.getInstance().getNetstat(host);
            assert netstat instanceof SigarNetstat;

            host = netstat.getEndpoints(host, null);

            assert host.getEndpoints().size() > 0;
        }
    }
}
