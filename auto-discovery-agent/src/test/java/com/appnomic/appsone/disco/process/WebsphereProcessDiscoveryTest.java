package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class WebsphereProcessDiscoveryTest {

    @Test
    public void validateWebsphereListenPortXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenPortXPathAttributeAccess = getListenPortXPathAttributeAccess();

        monitorPortAttributeAccessList.add(listenPortXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getWebsphereComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process1 = getWebsphereProcess1();
        Process process2 = getWebsphereProcess2();
        Process process3 = getWebsphereProcess3();
        Process process4 = getWebsphereProcess4();
        Process process5 = getWebsphereProcess5();
        Process process6 = getWebsphereProcess6();

        processList.add(process1);
        processList.add(process2);
        processList.add(process3);
        processList.add(process4);
        processList.add(process5);
        processList.add(process6);


        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306811"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("10088");

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306822"))
                proc = p;
        }
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("15144");


        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306833"))
                proc = p;
        }
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("14144");

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306844"))
                proc = p;
        }
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("13144");

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306855"))
                proc = p;
        }
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("8878");

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("306866"))
                proc = p;
        }
        assert proc.getComponentId() == 15;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("12144");

    }

    private AttributeAccess getListenPortXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("XPATH");
        HTTPPortAttributeAccess.setValue("serverindex:ServerIndex/serverEntries[@serverName=\"???\"]/specialEndpoints[@endPointName=\"SOAP_CONNECTOR_ADDRESS\"]/endPoint/@port");
        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getWebsphereComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("WebSphere");
        component.setComponentId(15);
        component.setComponentTypeId(3);
        component.setDiscoveryPattern("(?<containsWebsphere>[Ww][eE][bB][sS][pP][hH][eE][rR][eE])");
        component.setRelativePathList(Collections.singletonList("serverindex.xml"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getWebsphereProcess1() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E111");
        process.setPid("306811");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A ");
        // /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }

    private Process getWebsphereProcess2() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E122");
        process.setPid("306822");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8E ");
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }

    private Process getWebsphereProcess3() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E133");
        process.setPid("306833");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8D ");
        // /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }

    private Process getWebsphereProcess4() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E144");
        process.setPid("306844");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8C ");
        // /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }

    private Process getWebsphereProcess5() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E155");
        process.setPid("306855");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A nodeagent ");
        // /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }

    private Process getWebsphereProcess6() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A1071493295E166");
        process.setPid("306866");
        process.setProcessName("WebSphere" + File.separator + "bin");
        process.setProcessArgs("-Dwas.status.socket=49206 com.ibm.ws.runtime.WsServer /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8B ");
        // /websphere/profile1/FE/PROFILE/UBIFINPROF7/config UBIFINCell CLUSTNODE8A CLUSTMEMB8A
        // /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        process.setProcessCurrentWorkingDirectory("websphere" + File.separator + "bin");

        return process;
    }
}