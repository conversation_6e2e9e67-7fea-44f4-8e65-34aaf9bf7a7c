package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.junit.Test;
import org.junit.experimental.runners.Enclosed;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.io.File;
import java.util.*;

@RunWith(Enclosed.class)
public class FinacleProcessDiscoveryTest {

    private static Component getFinacleComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("FinacleServices");
        component.setComponentId(11);
        component.setComponentTypeId(3);
        component.setDiscoveryPattern("(?<containsFinacle>[Ll][Ii][Mm][Oo][ ][Ss][Tt][Aa][Rr][Tt])");
        component.setRelativePathList(Collections.singletonList("conf/sample.conf"));
        component.setAttributes(attributeList);

        return component;
    }

    public static class NonParamaterisedFinacleProcessDiscoveryTest {

        @Test
        public void validateFinacleListenPort()
        {
            Attribute hostAttribute = Util.getHostAttribute();


            List<AttributeAccess> limoAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> lisrvrAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> mariaAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> bcMonAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> bcEchoAttributeAccessList = new ArrayList<>();

            AttributeAccess limoAttributeAccess = Util.getLimoAttributeAccess();
            limoAttributeAccessList.add(limoAttributeAccess);
            AttributeAccess lisrvrAttributeAccess = Util.getLisrvrAttributeAccess();
            lisrvrAttributeAccessList.add(lisrvrAttributeAccess);
            AttributeAccess mariaAttributeAccess = Util.getMariaAttributeAccess();
            mariaAttributeAccessList.add(mariaAttributeAccess);
            AttributeAccess bcMonAttributeAccess = Util.getBcMonAttributeAccess();
            bcMonAttributeAccessList.add(bcMonAttributeAccess);
            AttributeAccess bcEchoAttributeAccess = Util.getBcEchoAttributeAccess();
            bcEchoAttributeAccessList.add(bcEchoAttributeAccess);


            Attribute limoAttribute = Util.getLimoAttribute(Collections.singletonList(limoAttributeAccess));
            Attribute lisrvrAttribute = Util.getLisrvrAttribute(Collections.singletonList(lisrvrAttributeAccess));
            Attribute mariaAttribute = Util.getMariaAttribute(Collections.singletonList(mariaAttributeAccess));
            Attribute bcMonAttribute = Util.getBcMonAttribute(Collections.singletonList(bcMonAttributeAccess));
            Attribute bcEchoAttribute = Util.getBcEchoAttribute(Collections.singletonList(bcEchoAttributeAccess));

            List<Attribute> attributeList = new ArrayList<>();
            attributeList.add(hostAttribute);
            attributeList.add(limoAttribute);
            attributeList.add(lisrvrAttribute);
            attributeList.add(mariaAttribute);
            attributeList.add(bcMonAttribute);
            attributeList.add(bcEchoAttribute);

            Component component = getFinacleComponent(attributeList);

            List<Component> componentList = new ArrayList<>();
            componentList.add(component);

            List<Process> processList = new ArrayList<>();

            Process process1 = getFinacleProcessLinux1();
            Process process2 = getFinacleProcessLinux2();
            Process process3 = getFinacleProcessLinux3();
            Process process5 = getFinacleProcessLinux5();
            Process process6 = getFinacleProcessLinux6();

            processList.add(process1);
            processList.add(process2);
            processList.add(process3);
            processList.add(process5);
            processList.add(process6);

            List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

            String hostAddress = Utils.getHostAddress(interfaceList);

            YamlFileLoader.setAutoDiscoveryComponents(componentList);

            Host host = new Host();
            host.setOperatingSystem("");
            host.setNetworkInterfaces(interfaceList);

            ComponentDiscovery componentDiscovery = new ComponentDiscovery();

            List<Process> discoveredProcessList = componentDiscovery.run(processList, host, runningProcesses());

            Set<Integer> componentId = new HashSet<>();
            for(Process process : discoveredProcessList)
            {
                if(process.getPid().equalsIgnoreCase("16449882")) {
                    componentId.add(process.getComponentId());
                }
            }
            assert componentId.size() == 1;
            assert componentId.contains(11);
            assert discoveredProcessList.get(0).getAttributes().containsKey("HostAddress") && discoveredProcessList.get(0).getAttributes().get("HostAddress").equals(hostAddress);
        }

   /*
    Line 30: 16449882        1   0   Oct 07 0:00 ./limo start coresession_UBANG02_PROD_F10_NIG_APP2
	Line 47: 19792192        1   0 16:00:01 0:00 ./limo start eabgst_UBANG02_NG_PROD_F10_NIG_APP2
	Line 101: 15008368        1   0   Oct 07 0:00 ./limo start referral_UBANG02_PROD_F10_NIG_APP2
	Line 166: 15008580        1   0   Oct 07 0:00 ./limo start finlistval_UBANG02_PROD_F10_NIG_APP2
	--> Line 200: 22217508        1   0   Oct 10 0:00 ./limo start uniser_UBANG02_NG_PROD_F10_NIG_APP2
     */

        private Process getFinacleProcessLinux1() {
            Process process = new Process();
            process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08111");
            process.setPid("16449882");
            process.setProcessName("./limo start coresession_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessArgs("./limo start coresession_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessCurrentWorkingDirectory(File.separator);

            return process;
        }
        private Process getFinacleProcessLinux2() {
            Process process = new Process();
            process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08222");
            process.setPid("19792192");
            process.setProcessName("./limo start eabgst_UBANG02_NG_PROD_F10_NIG_APP2");
            process.setProcessArgs("./limo start eabgst_UBANG02_NG_PROD_F10_NIG_APP2");
            process.setProcessCurrentWorkingDirectory(File.separator);

            return process;
        }
        private Process getFinacleProcessLinux3() {
            Process process = new Process();
            process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08333");
            process.setPid("15008368");
            process.setProcessName("./limo start referral_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessArgs("./limo start referral_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessCurrentWorkingDirectory(File.separator);

            return process;
        }
        private Process getFinacleProcessLinux5() {
            Process process = new Process();
            process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08555");
            process.setPid("15008580");
            process.setProcessName("./limo start finlistval_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessArgs("./limo start finlistval_UBANG02_PROD_F10_NIG_APP2");
            process.setProcessCurrentWorkingDirectory(File.separator);

            return process;
        }
        private Process getFinacleProcessLinux6() {
            Process process = new Process();
            process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08666");
            process.setPid("22217508");
            process.setProcessName("./limo start uniser_UBANG02_NG_PROD_F10_NIG_APP2");
            process.setProcessArgs("./limo start uniser_UBANG02_NG_PROD_F10_NIG_APP2");
            process.setProcessCurrentWorkingDirectory(File.separator);

            return process;
        }

        private String runningProcesses()
        {
            return "      PID     PPID   CMD\n" +
                    "       1        0   /etc/init\n" +
                    " 5833138  4391578   /usr/sbin/syslogd\n" +
                    " 5898660  4391578   /usr/sbin/inetd\n" +
                    " 6095332  4391578   /usr/sbin/sshd\n" +
                    " 6291766        1   /usr/sbin/uprintfd\n" +
                    " 6357248        1   /commvault/Base/cvfwd\n" +
                    "12517870 11862842   ../bin/config_server\n" +
                    "12583236 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12910868 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "13107686  4391578   /opt/rsct/bin/IBM.ServiceRMd\n" +
                    "13435236  6095332   sshd: appassure [priv]\n" +
                    "13828514 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14221704 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "14352776 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14680562 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14746012 11862842   ../bin/config_server\n" +
                    "14811554 11862842   ../bin/config_server\n" +
                    "14942684 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15139086 25755962   -ksh\n" +
                    "15204618 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15270366 26477358   sleep 30\n" +
                    "15335836 16974866   sshd: 6928@pts/1\n" +
                    "15597954 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/cdci-maria\n" +
                    "15860036 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16056584 14877852   -ksh\n" +
                    "16253358 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16384324 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/maria-fin-list\n" +
                    "16449882        1   ./limo start coresession_UBANG02_PROD_F10_NIG_APP2\n" +
                    "16515392 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "16580952 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "16646490 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "16712028 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "16777572 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "16843120 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-purgealert\n" +
                    "16908664 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-alertsrvr\n" +
                    "17498412 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17564106 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17629680 16188116   /usr/java71_64/bin/java -DPEAS_HOME=/finacle/UBAAPP/Fin10216/APP/PEASENG/PEASEngine/EngineServer/peashome -DFINAC\n" +
                    "17695004 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17826106 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "18219314 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18547174 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19333560 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "19529998 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19792192        1   ./limo start eabgst_UBANG02_NG_PROD_F10_NIG_APP2\n" +
                    "19988970 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20120018 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20316462 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20513144 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20578752 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20775188 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21365116 16319222   topas\n" +
                    "21430598 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22086050 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22216968 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "22348056 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22741482 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22937868 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23134652 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23200156 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23527818 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23658794 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23789938 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24248654 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24904118 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25035106 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25166324 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25755962 13435236   sshd: appassure@pts/6\n" +
                    "25887188 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "26083646  7341148   /usr/lpp/mmfs/bin/mmksh /usr/lpp/mmfs/bin/mmccrmonitor 15 0 no\n" +
                    "27787714 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "11469464 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "11666138 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12190286 11862842   ../bin/config_server\n" +
                    "12386958 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "12649028 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12780224 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12845754 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "13369978 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14025222 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14156328 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14811776 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14942780 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/maria-fin-list\n" +
                    "15008368        1   ./limo start referral_UBANG02_PROD_F10_NIG_APP2\n" +
                    "15073888 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15270626 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15467086 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-purgealert\n" +
                    "15532634 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/maria-alertsrvr\n" +
                    "15598206 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-purgealert\n" +
                    "15663744 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-refdaemon\n" +
                    "15794740 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16188116 13894810   /bin/sh /finacle/UBAAPP/Fin10216/APP/PEASENG/PEASEngine/EngineServer/EngineServerStartup.sh ../../../common/lib/C\n" +
                    "16253544 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "16319222 15335836   -ksh\n" +
                    "16646876 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16712444  5833518   sshd: finapp@pts/3\n" +
                    "17105412 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17236502 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17498800 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "17695250 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "17826390 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18612826 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19202626 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19333640 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19530242 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19726902 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20054684 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20447944 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20513412 16450490   topas\n" +
                    "21037706 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21168686 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21561926 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21824198 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22348426 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23397020 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23528100 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "23986810 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "24117786 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "25101038 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27001480 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "27263584 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "28050094 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "28181212 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "11731900 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "11797408 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "11862842        1   ./limo ../data/config_service.cfg\n" +
                    "11928458  4391578   /opt/rsct/bin/IBM.ConfigRMd\n" +
                    "11994048 11862842   ../bin/config_server\n" +
                    "12059626 11862842   ../bin/lic_server\n" +
                    "12452862 15008580  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "12649362 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12714998 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12911530 21824280   topas\n" +
                    "13108126 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "13828888 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14091078 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14222100 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14353260 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14418836 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14877518 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "15008580        1   ./limo start finlistval_UBANG02_PROD_F10_NIG_APP2\n" +
                    "15205236 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "15336262 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/maria-fin-list\n" +
                    "15401808 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15467358 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15532894 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15598432 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15663970 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15729510 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15795048 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "15860592 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-refdaemon\n" +
                    "15926134 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-refdaemon\n" +
                    "15991674 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-alertsrvr\n" +
                    "16057236 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16122816 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/lisrvr-alertsrvr\n" +
                    "16253886        1   /usr/java71_64/bin/java -Xms256m -Xmx1024m -Djava.awt.headless=true -DPRIMARY_IP=PROD_F10_NIG_APP2 -DALTERNATE_IP\n" +
                    "16319408 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16450490 16712444   -ksh\n" +
                    "17499016  6095332   sshd: 8236 [priv]\n" +
                    "17564450 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18088956 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18351034 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18481950 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19071970 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19202894 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20251630 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20448014 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20644762 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20775750 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20906800 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21300194 22217508  /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "21824280 24576834   -ksh\n" +
                    "22021072 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22152014 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22217508        1   ./limo start uniser_UBANG02_NG_PROD_F10_NIG_APP2\n" +
                    "22545312 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "22872854 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23200542 22807746   -ksh\n" +
                    "23790408 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24380324 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "24576834 17499016   sshd: 8236@pts/4\n" +
                    "24707840 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24970022 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25297888 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25625542 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "25690944        1   /usr/bin/topas_nmon  -f -t -d -A -O -L -M -N -P -V -T -^ -m /usr/local/scripts/nmon/NMON_DATA -s 60 -c 1440 -yout\n" +
                    "25822206        1   /finusers/appassure/finacleassure/Agents_App2/Supervisor/Supervisor /finusers/appassure/finacleassure/Agents_App2\n" +
                    "26018702 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "26280880 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "26477358        1   /bin/sh /finusers/appassure/finacleassure/Agents_App2/Supervisor/supervisor_monitor.sh\n" +
                    "26739604 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27132790 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27329372 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "27460516 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27722594 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27853660 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "27919270 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "10093818 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "10159320  4391578   /opt/rsct/bin/IBM.MgmtDomainRMd\n" +
                    "10290328        1   /usr/bin/topasrec  -L -s 300 -R 1 -r 6 -o /etc/perf/daily/ -ypersistent=1 -O type=bin -ystart_time=17:31:05,Oct07\n" +
                    "10421380 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "10552500  4391578   /opt/rsct/bin/rmcd -a IBM.LPCommands -r -S 1500\n" +
                    "10617890 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "10683538 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "11011124 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "11273330 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "11600988 19792192   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/eabgst_NG/bin/lisrvr-eab-gst-update\n" +
                    "11994358 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12322028 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12452924 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "12911670 15139086   ps -ef\n" +
                    "13304884 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "13370410 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/maria-fin-list\n" +
                    "13632596 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "13698128 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/lisrvr-core-session\n" +
                    "13763690 16449882   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/coresession/bin/maria-core-session\n" +
                    "13829234        1   /usr/java71_64/bin/rmiregistry -J-Djava.rmi.server.useCodebaseOnly=false 10061\n" +
                    "13894810        1   /usr/java71_64/bin/java -DPEAS_KILL=Y -DFINACLE_INSTALL_ID=UBANG02 -cp ../../common/lib/SOAR_CLNT_LIB/arappmonito\n" +
                    "14025852 15008368   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/referral/bin/maria-alertsrvr\n" +
                    "14091312 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14222520 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "14550230 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "14943456 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15008892 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "15336610 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15402036 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15598604 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "15729910 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16122980 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16909512 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "16974866  6095332   sshd: 6928 [priv]\n" +
                    "17105966 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17499146 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17564678 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17892352 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "17958000  6095332   sshd: finapp [priv]\n" +
                    "18154734 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "18351270 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18482352 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "18678820 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "19727434 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "20186336 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20382880 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20644960 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "20907172 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "21234688 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22086762 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "22217880 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "22807746 17958000   sshd: finapp@pts/2\n" +
                    "22873336 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "23266378 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "23463114 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "23790832 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24052748 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver\n" +
                    "24249348 15008580   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/finlistval/bin/lisrvr-fin-listval\n" +
                    "24249347 15008580   /fincbc/FINBACKEND/CBC/services/cbcothers/bin/lisrvr-bc-echo\n" +
                    "24249346 15008580  /fincbc/FINBACKEND/CBC/services/cbcfcol/bin/lisrvr-bc-mon\n" +
                    "24577054        1   /finusers/appassure/finacleassure/Agents_App2/JAVA_AIX/jre/bin/java -XX:+UseConcMarkSweepGC -XX:-HeapDumpOnOutOfM\n" +
                    "26281028 22217508   /finacle/UBAAPP/Fin10216/APP/Finacle/FC/app/services/uniser_NG/bin/lisrvr-uniserver";

        }
    }
    @RunWith(Parameterized.class)
    public static class ParameterisedFinacleProcessDiscoveryTest {
        private String fileName;

        private String processId;

        public ParameterisedFinacleProcessDiscoveryTest(String fileName, String processId) {
            this.fileName = fileName;
            this.processId = processId;
        }

        @Test
        public void validateFinacleComponent() {

            List<String> processIdsFile = new ArrayList<>();
            List<Component> componentList = new ArrayList<>();
            Host host = new Host();
            List<Attribute> attributeList = new ArrayList<>();

            List<String> processIdList = Arrays.asList(processId.split(","));
            Attribute hostAttribute = Util.getHostAttribute();


            List<AttributeAccess> limoAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> lisrvrAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> mariaAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> bcMonAttributeAccessList = new ArrayList<>();
            List<AttributeAccess> bcEchoAttributeAccessList = new ArrayList<>();

            AttributeAccess limoAttributeAccess = Util.getLimoAttributeAccess();
            limoAttributeAccessList.add(limoAttributeAccess);
            AttributeAccess lisrvrAttributeAccess = Util.getLisrvrAttributeAccess();
            lisrvrAttributeAccessList.add(lisrvrAttributeAccess);
            AttributeAccess mariaAttributeAccess = Util.getMariaAttributeAccess();
            mariaAttributeAccessList.add(mariaAttributeAccess);
            AttributeAccess bcMonAttributeAccess = Util.getBcMonAttributeAccess();
            bcMonAttributeAccessList.add(bcMonAttributeAccess);
            AttributeAccess bcEchoAttributeAccess = Util.getBcEchoAttributeAccess();
            bcEchoAttributeAccessList.add(bcEchoAttributeAccess);


            Attribute limoAttribute = Util.getLimoAttribute(Collections.singletonList(limoAttributeAccess));
            Attribute lisrvrAttribute = Util.getLisrvrAttribute(Collections.singletonList(lisrvrAttributeAccess));
            Attribute mariaAttribute = Util.getMariaAttribute(Collections.singletonList(mariaAttributeAccess));
            Attribute bcMonAttribute = Util.getBcMonAttribute(Collections.singletonList(bcMonAttributeAccess));
            Attribute bcEchoAttribute = Util.getBcEchoAttribute(Collections.singletonList(bcEchoAttributeAccess));

            attributeList.add(hostAttribute);
            attributeList.add(limoAttribute);
            attributeList.add(lisrvrAttribute);
            attributeList.add(mariaAttribute);
            attributeList.add(bcMonAttribute);
            attributeList.add(bcEchoAttribute);

            Component component = getFinacleComponent(attributeList);
            componentList.add(component);

            List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();
            YamlFileLoader.setAutoDiscoveryComponents(componentList);
            host.setOperatingSystem("");
            host.setNetworkInterfaces(interfaceList);

            List<Process> processList = Util.getProcessListFromFile(fileName);
            for(Process p : processList)
            {
                processIdsFile.add(p.getPid());
            }

            String runningProcess = Util.getRunningProcessFromFile(fileName);
            ComponentDiscovery componentDiscovery = new ComponentDiscovery();

            List<Process> discoveredProcessList = componentDiscovery.run(processList, host, runningProcess);

            for (Process p : discoveredProcessList) {
                if (processIdsFile.contains(p.getPid()) && processIdList.contains(p.getPid())) {
                    assert p.getComponentId() == 11;
                }
                if (processIdsFile.contains(p.getPid()) && !processIdList.contains(p.getPid())) {
                    assert p.getComponentId() != 11;
                }
            }
        }

        @Parameterized.Parameters
        public static Collection fileDetails() {
            return Arrays.asList(new Object[][]{
                    {"src/test/resources/clientDataFiles/JKB/c24CBC196.txt", "57618,2784,47062,57054,2718,51712,57904,57441,57500,57139,57736,2659,57559,1657,57677,57963,57205"},
                    {"src/test/resources/clientDataFiles/JKB/c24CBC197.txt", "39950,30303,40252,30658,40169,40337,39893,40089,30398,30457,40034,30585,40242,30516,30199,30313,30279"},
                    {"src/test/resources/clientDataFiles/JKB/c24CBC198.txt.txt", "56074,46347,56143,56084,56023,46574,55936,56295,56212,46430,55988,56236,46268,46327,46515,46371,48123"},
                    {"src/test/resources/clientDataFiles/JKB/c24Uniser193.txt", "44251,55514,5957,40037,30509,6473,57064,47795,40631,1782,57381,56276,56483,56802,55917,46030,1511,56120,16670,51254,52065,56599,57158,20407,46307,56976,38636,40558,56885,40112,40536,56371,16548,40225,26221,51245"},
                    {"src/test/resources/clientDataFiles/JKB/c24Uniser194.txt", "8038,64531,8111,4210,4864,7801,4732,39306,4354,4791,4413,4555,40042,6088,51072,9494,50090,4486,11094,55285,42018,6587,4673"},
                    {"src/test/resources/clientDataFiles/JKB/c24Uniser195.txt", "53247,5300,3415,5785,6557,8301,3720,4073,3602,32434,27101,10541,5718,3779,52846,9464,34211,5659,34351,50598,7202,34861,34486,63623,3484,42676,3661"}
            });
        }
    }
}