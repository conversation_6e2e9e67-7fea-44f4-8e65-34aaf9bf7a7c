package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.*;

public class IBMMqProcessDiscoveryTest {

    @Test
    public void validateIbmMqLinux() {

        List<Component> componentList = getIbmComponentDetails();

        List<Process> processList = new ArrayList<>();
        Process process1 = getIBMmqProcess1();
        Process process2 = getIBMmqProcess2();

        processList.add(process1);
        processList.add(process2);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();
        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = getComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("3209"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 26;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("QueueManager") && proc.getAttributes().get("QueueManager").equals("QM1");

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("2434"))
                proc = p;
        }
        assert proc.getComponentId() == 0;
    }

    @Test
    public void validateIBMMQComponentWindows() {

        List<Component> componentList = getIbmComponentDetails();

        List<Process> processList = new ArrayList<>();
        Process process = getIBMmqProcessWindows();
        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();
        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        Set<String> qm = new HashSet<>();
        qm.add("QM1");
        qm.add("MQ3");

        ComponentDiscovery componentDiscovery = new ComponentDiscovery(qm);
        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for (Process p : discoveredProcessList) {
            if (p.getPid().equalsIgnoreCase("19580"))
                proc = p;
        }
        assert proc != null;
        if (Utils.isWindows()) {
            assert proc.getComponentId() == 26;
            assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
            assert proc.getAttributes().containsKey("QueueManager") && proc.getAttributes().get("QueueManager").equals("MQ3");
        }
    }

    private ComponentDiscovery getComponentDiscovery(){

        Set<String> qm = new HashSet<>();
        qm.add("QM1");
        qm.add("QM3");
        ComponentDiscovery componentDiscovery = new ComponentDiscovery(qm);
        return componentDiscovery;
    }
    private List<Component> getIbmComponentDetails(){
        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> queueManagerAttributeAccessList = new ArrayList<>();

        AttributeAccess queueManagerAttributeAccess = getQueueManagerAttributeAccess();

        queueManagerAttributeAccessList.add(queueManagerAttributeAccess);

        Attribute queueManagerAttribute = Util.getIMBQueueManagerAttribute(queueManagerAttributeAccessList);


        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(queueManagerAttribute);

        Component component = getIBMmqComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        return componentList;
    }

    private AttributeAccess getQueueManagerAttributeAccess() {
        AttributeAccess queueManagerAttributeAccess = new AttributeAccess();
        queueManagerAttributeAccess.setMethod("REG_EX");
        queueManagerAttributeAccess.setValue("((-m) (?<queuemanager>[0-9a-zA-Z.]+))");
        queueManagerAttributeAccess.setDefaultValue(0);
        queueManagerAttributeAccess.setPriority(1);
        return queueManagerAttributeAccess;
    }

    private Component getIBMmqComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("IBM MQ");
        component.setComponentId(26);
        component.setComponentTypeId(7);
        //    /java.*.com.ibm.mq.MQXRService.RunMQXRService.*
        component.setDiscoveryPattern("(?<ContainsIBM>[Ii][Bb][Mm].*[Mm][Qq].*-[Mm])");
        component.setRelativePathList(Collections.singletonList("sampleConfig.xml"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getIBMmqProcess1() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A10714933209111");
        process.setPid("3209");
        process.setProcessName("/opt/mqm/amqp/bin");
        process.setProcessArgs("/opt/mqm/amqp/bin/../../java/jre64/jre/bin/java -Dcom.ibm.mq.mqxr.service.type=amqp -Dcom.ibm.msg.client.config.location=file:////var/mqm/qmgrs/QM1//./amqp/amqp_trace.config -Xoptionsfile=/var/mqm/qmgrs/QM1//./amqp/amqp_java.properties -Dcom.ibm.msg.client.commonservices.wmq.logdir=/var/mqm/qmgrs/QM1//./errors -Dcom.ibm.msg.client.commonservices.wmq.tracedir=/var/mqm//./trace -Dcom.ibm.msg.client.commonservices.wmq.ffdcdir=/var/mqm//./errors com.ibm.mq.MQXRService.RunMQXRService -t /opt/mqm/amqp/bin/../config -m QM1 -d /var/mqm/qmgrs/QM1//. -g /var/mqm//.\n");
        process.setProcessCurrentWorkingDirectory("mqm" + File.separator + "bin");

        return process;
    }

    private Process getIBMmqProcess2() {
        Process process = new Process();
        process.setProcessIdentifier("147A4A8C5123E60F481A10714932434222");
        process.setPid("2434");
        process.setProcessName("/opt/mqm/amqp/bin");
        process.setProcessArgs("/opt/mqm/amqp/bin/../../java/jre64/jre/bin/java -Dcom.ibm.mq.mqxr.service.type=amqp -Dcom.ibm.msg.client.config.location=file:////var/mqm/qmgrs/SATURN!QUEUE!MANAGER//./amqp/amqp_trace.config -Xoptionsfile=/var/mqm/qmgrs/SATURN!QUEUE!MANAGER//./amqp/amqp_java.properties -Dcom.ibm.msg.client.commonservices.wmq.logdir=/var/mqm/qmgrs/SATURN!QUEUE!MANAGER//./errors -Dcom.ibm.msg.client.commonservices.wmq.tracedir=/var/mqm//./trace -Dcom.ibm.msg.client.commonservices.wmq.ffdcdir=/var/mqm//./errors com.ibm.mq.MQXRService.RunMQXRService -t /opt/mqm/amqp/bin/../config -m QM1 -d /var/mqm/qmgrs/SATURN!QUEUE!MANAGER//. -g /var/mqm//.\n");
        process.setProcessCurrentWorkingDirectory("mqm" + File.separator + "bin");

        return process;
    }

        private Process getIBMmqProcessWindows() {
            Process process = new Process();
            process.setProcessIdentifier("436F4CDEBDFF61F02BC971B31F09E95B");
            process.setPid("19580");
            process.setProcessName("C:\\\\Program Files\\\\IBM\\\\MQ\\\\bin64\\\\amqzxma0.exe");
            process.setProcessArgs("C:\\\\Program Files\\\\IBM\\\\MQ\\\\bin64\\\\amqzfuma.exe -m MQ3 ");
            process.setProcessCurrentWorkingDirectory("C:\\\\ProgramData\\\\IBM\\\\MQ");
            return process;
    }

    /*@Test
    public void test(){

        String commandOutput = "QMNAME(SATURN.QUEUE.MANAGER)                              STATUS(Running)\n" +
                "QMNAME(QM1)                                               STATUS(Running)\n" +
                "QMNAME(QM2)                                               STATUS(Ended normally)";

        List<String> output = new ArrayList<>();
        String reg = "QMNAME\\([0-9a-zA-Z.]*\\)";
        Matcher patternMatcher = Pattern.compile("(?=(" + reg + "))").matcher(commandOutput);
        while(patternMatcher.find()) {
            String qManager = patternMatcher.group(1).split("QMNAME")[1];
            output.add(qManager.substring(1, qManager.length()-1));
        }
        System.out.println(output);
    }*/
}
