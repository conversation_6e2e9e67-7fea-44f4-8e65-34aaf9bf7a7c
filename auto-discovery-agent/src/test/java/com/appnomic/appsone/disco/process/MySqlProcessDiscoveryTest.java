package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class MySqlProcessDiscoveryTest {

    @Test
    public void validatePort() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess mysqlPortAttributeAccess = Util.getMySqlAttributeAccess();
        monitorPortAttributeAccessList.add(mysqlPortAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getMySQLComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getMySqlProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Linux");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host , StringUtil.EMPTY_STRING);

        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("1860"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 18;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("3306");
    }

    private Component getMySQLComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("MySQL");
        component.setComponentId(18);
        component.setComponentTypeId(4);
        component.setDiscoveryPattern("(?<containsMySql>[Mm][Yy][Ss][Qq][Ll]).*");
        component.setRelativePathList(Collections.singletonList("my.ini"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getMySqlProcess() {

        Process process = new Process();
        process.setProcessIdentifier("3D0A4585F755619E9D7D3CAB93ED7E13");
        process.setPid("1860");
        process.setProcessName("mysql\\my.ini");
        process.setProcessArgs("--defaults-file=my.ini");
        process.setProcessCurrentWorkingDirectory("mysql");

        return process;
    }
}
