package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class JbossProcessDiscoveryTest {

    @Test
    public void validateJbossHTTPSXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess HTTPXPathAttributeAccess = getHTTPXPathAttributeAccess();

        monitorPortAttributeAccessList.add(HTTPXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getJbossComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getJbossProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);

        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("10676"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 14;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("8443");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("jboss\\standalone\\configuration\\standalone.xml");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("jboss/standalone/configuration/standalone.xml");
        }
    }

    private AttributeAccess getHTTPXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("XPATH");
        HTTPPortAttributeAccess.setValue("jbo:server/jbo:socket-binding-group/jbo:socket-binding[@name=\"https\"]/@port");
        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getJbossComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("JBoss");
        component.setComponentId(14);
        component.setComponentTypeId(3);
        component.setDiscoveryPattern("(?<process>java).*(?<containsJboss>[jJ][bB][oO][sS][sS])");
        component.setRelativePathList(Collections.singletonList("standalone\\configuration\\standalone.xml"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getJbossProcess() {

        Process process = new Process();
        process.setProcessIdentifier("93D272FF2B44CD1CDA6CD4F94F3AB99D");
        process.setPid("10676");
        process.setProcessName("jboss" + File.separator + "bin" + File.separator + "java.exe");
        process.setProcessArgs("-Dprogram.name=standalone.bat -Djboss.home.dir=" + File.separator + "jboss");
        process.setProcessCurrentWorkingDirectory("jboss" + File.separator + "bin");
        process.setComponentVersion("7.1.0");
        return process;
    }
}
