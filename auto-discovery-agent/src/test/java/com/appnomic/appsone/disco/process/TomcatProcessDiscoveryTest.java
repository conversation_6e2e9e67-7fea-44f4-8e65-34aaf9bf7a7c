package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class TomcatProcessDiscoveryTest {

    @Test
    public void validateTomcatAJPXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess AJPXPathAttributeAccess = getAJPXPathAttributeAccess();

        AttributeAccess HTTPXPathAttributeAccess = getHTTPXPathAttributeAccess();

        monitorPortAttributeAccessList.add(AJPXPathAttributeAccess);
        monitorPortAttributeAccessList.add(HTTPXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(2);
        monitorPortAttribute.getAccess().get(1).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getTomcatComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getTomcatProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);
        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();
        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("6936"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 13;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("8080");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("tomcat\\conf\\server.xml");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("tomcat/conf/server.xml");
        }

    }

    @Test
    public void validateTomcatHTTPXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess AJPXPathAttributeAccess = getAJPXPathAttributeAccess();

        AttributeAccess HTTPXPathAttributeAccess = getHTTPXPathAttributeAccess();

        monitorPortAttributeAccessList.add(AJPXPathAttributeAccess);
        monitorPortAttributeAccessList.add(HTTPXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);
        monitorPortAttribute.getAccess().get(1).setPriority(2);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getTomcatComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getTomcatProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);

        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("6936"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 13;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("8080");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("tomcat\\conf\\server.xml");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("tomcat/conf/server.xml");
        }
    }

    private AttributeAccess getAJPXPathAttributeAccess() {
        AttributeAccess AJPPortAttributeAccess = new AttributeAccess();
        AJPPortAttributeAccess.setMethod("XPATH");
        AJPPortAttributeAccess.setValue("/Server/Service/Connector[@protocol='AJP/1.3']/@port");
        AJPPortAttributeAccess.setPriority(1);
        return AJPPortAttributeAccess;
    }

    private AttributeAccess getHTTPXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("XPATH");
        HTTPPortAttributeAccess.setValue("/Server/Service/Connector[@protocol='HTTP/1.1']/@port");
        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getTomcatComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("Apache Tomcat");
        component.setComponentId(13);
        component.setComponentTypeId(3);
        component.setDiscoveryPattern("(?<process>([tT][oO][mM][cC][aA][tT]))");
        component.setRelativePathList(Collections.singletonList("conf\\server.xml"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getTomcatProcess() {

        Process process = new Process();
        process.setProcessIdentifier("7B114802D73FF714203B2A9CEC01E15A");
        process.setPid("6936");
        process.setProcessName("tomcat" + File.separator + "bin" + File.separator + "Tomcat8.exe");
        process.setProcessArgs("-Djava.util.logging.manager=org.apache.juli.ClassLoaderLogManager");
        process.setProcessCurrentWorkingDirectory("tomcat" + File.separator + "bin");
        process.setComponentVersion("8.5.71");
        return process;
    }
}
