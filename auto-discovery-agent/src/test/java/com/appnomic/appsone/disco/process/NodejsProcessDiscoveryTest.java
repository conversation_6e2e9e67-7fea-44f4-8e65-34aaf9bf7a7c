package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NodejsProcessDiscoveryTest {
    @Test
    public void validatePort() {

        final Logger log = LoggerFactory.getLogger(YamlFileLoader.class);

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess nodeJsPortAttributeAccess = Util.getNodeJsAttributeAccess();
        monitorPortAttributeAccessList.add(nodeJsPortAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getNodeJsComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getNodeJSProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();
        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);

        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("1861"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 65;

        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("3002");
    }

    private Component getNodeJsComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("NODEJS");
        component.setComponentId(65);
        component.setComponentTypeId(2);
        component.setDiscoveryPattern("(?<containsNode>[.][Nn][Vv][Mm].*[Nn][Oo][Dd][Ee].*)");
        component.setRelativePathList(Collections.singletonList("server.js"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getNodeJSProcess() {

        Process process = new Process();
        process.setProcessIdentifier("3D0A4585F755619E9D7D3CAB93ED7E11");
        process.setPid("1861");
        process.setProcessName("/root/.nvm/versions/node/v12.22.9/bin/node");
        process.setProcessArgs("node server.js");
        process.setProcessCurrentWorkingDirectory("nodejs");///opt/autodiscovery/data-volumes-01-starting-setup

        return process;
    }
}
