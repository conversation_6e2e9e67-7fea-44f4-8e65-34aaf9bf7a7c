package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class DifferentHttpdProcessDiscoveryTest {

    @Test
    public void validateHttpdComponents() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenAttributeAccess = Util.getListenAttributeAccess();
        monitorPortAttributeAccessList.add(listenAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        List<Component> componentList = getHttpdComponents(attributeList);


        List<Process> processList = getHttpdProcess();

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();
        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process apacheProc = null;
        Process ohsProc = null;

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("893211")) {
                apacheProc = p;
            }
            if(p.getPid().equalsIgnoreCase("28456")) {
                ohsProc = p;
            }
        }

        assert apacheProc  != null;
        assert apacheProc.getComponentId() == 7;
        assert apacheProc.getAttributes().containsKey("HostAddress") && apacheProc.getAttributes().get("HostAddress").equals(hostAddress);
        assert apacheProc.getAttributes().containsKey("MonitorPort") && apacheProc.getAttributes().get("MonitorPort").equals("9444");

        assert ohsProc  != null;
        assert ohsProc.getComponentId() == 10;
        assert ohsProc.getAttributes().containsKey("HostAddress") && ohsProc.getAttributes().get("HostAddress").equals(hostAddress);
        assert ohsProc.getAttributes().containsKey("MonitorPort") && ohsProc.getAttributes().get("MonitorPort").equals("7771");

    }

    private List<Component> getHttpdComponents(List<Attribute> attributeList) {

        List<Component> componentList = new ArrayList<>();

        Component apacheComponent = new Component();

        apacheComponent.setComponentName("Apache httpd - Apache");
        apacheComponent.setComponentId(7);
        apacheComponent.setComponentTypeId(2);
        apacheComponent.setDiscoveryPattern("(?<process>([h][t][t][p][d]))");
        apacheComponent.setRelativePathList(Collections.singletonList("conf/httpd.conf"));
        apacheComponent.setAttributes(attributeList);
        componentList.add(apacheComponent);

        Component ohsComponent = new Component();

        ohsComponent.setComponentName("OHS - Oracle HTTP Server");
        ohsComponent.setComponentId(10);
        ohsComponent.setComponentTypeId(2);
        ohsComponent.setDiscoveryPattern("(?<containsOHS>([Oo][Hh][Ss]).*.[Hh][Tt][Tt][Pp][Dd])");
        ohsComponent.setRelativePathList(Collections.singletonList("httpd.conf"));
        ohsComponent.setAttributes(attributeList);
        componentList.add(ohsComponent);

        return componentList;
    }

    private List<Process> getHttpdProcess() {
        List<Process> processList = new ArrayList<>();

        Process apacheProcess = new Process();

        apacheProcess.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08222");
        apacheProcess.setPid("893211");
        apacheProcess.setProcessName("httpd_Server1" + File.separator + "sbin" + File.separator + "httpd");
        apacheProcess.setProcessArgs("httpd_Server1" + File.separator + "sbin" + File.separator + "httpd -DFOREGROUND ");
        apacheProcess.setProcessCurrentWorkingDirectory(File.separator);
        processList.add(apacheProcess);

        Process ohsProcess = new Process();

        ohsProcess.setProcessIdentifier("A2E98FCA7910B1536F0AF2CE7DC06038");
        ohsProcess.setPid("28456");
        ohsProcess.setProcessName("/home/<USER>/Oracle/Middleware/Oracle_Home/ohs/bin/httpd");
        ohsProcess.setProcessArgs("/home/<USER>/Oracle/Middleware/Oracle_Home/wlserver/../ohs/bin/httpd -DOHS_MPM_EVENT -d /home/<USER>/Oracle/Middleware/Oracle_Home/user_projects/domains/base_domain/config/fmwconfig/components/OHS/instances/ohs1 -k start -f /ohs1/httpd.conf ");
        ohsProcess.setProcessCurrentWorkingDirectory("ohs1");
        processList.add(ohsProcess);

        return processList;
    }

}
