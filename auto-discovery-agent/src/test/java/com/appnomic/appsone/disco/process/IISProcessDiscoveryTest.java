package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class IISProcessDiscoveryTest {

    @Test
    public void validategetDefaultPortXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenPortXPathAttributeAccess = getDefaultPortXPathAttributeAccess();

        monitorPortAttributeAccessList.add(listenPortXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getIISComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getIISProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);
        YamlFileLoader.init();
        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("28960"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 8;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("80");
        assert !proc.getComponentVersion().equals("8.5") || proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
    }

    private AttributeAccess getDefaultPortXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("XPATH");
        HTTPPortAttributeAccess.setValue("/configuration/system.applicationHost/sites/site[@name=\"Default Web Site\"]/bindings/binding[@protocol='http']/@bindingInformation");

        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getIISComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("IIS");
        component.setComponentId(8);
        component.setComponentTypeId(2);
        component.setDiscoveryPattern("(?<containsIIS>[Ii][Ii][sS]).*");
        component.setRelativePathList(Collections.singletonList("inetsrv\\config\\applicationHost.config"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getIISProcess() {

        Process process = new Process();
        process.setProcessIdentifier("93D272FF2B44CD1CDA6CD4F94F3AB99C");
        process.setPid("28960");
        process.setProcessName("iis");
        process.setProcessArgs("-Dprogram.name=InetMgr.exe -Diis.home.dir=" + File.separator + "iis");
        process.setProcessCurrentWorkingDirectory("inetsrv" + File.separator + "bin");
        process.setComponentVersion("8.5");

        return process;
    }
}
