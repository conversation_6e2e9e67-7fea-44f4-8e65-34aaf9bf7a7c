package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class NgInxProcessDiscoveryTest {

    @Test
    public void validateDefaultPort() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenPortXPathAttributeAccess = getDefaultPortXPathAttributeAccess();

        monitorPortAttributeAccessList.add(listenPortXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getNgINxComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getNgINxProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("2149958"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 47;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("80");
    }

    private AttributeAccess getDefaultPortXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("NGINX_PARSER");
        HTTPPortAttributeAccess.setValue("listen");

        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getNgINxComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("NGINX");
        component.setComponentId(47);
        component.setComponentTypeId(2);
        component.setDiscoveryPattern("(?<process>[Nn][Gg][Ii][Nn][Xx])");
        component.setRelativePathList(Collections.singletonList("conf.d" + File.separator + "default.conf"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getNgINxProcess() {

        Process process = new Process();
        process.setProcessIdentifier("4B131E60247A6839E2B475F24EF43751");
        process.setPid("2149958");
        process.setProcessName("/usr/sbin/nginx");
        process.setProcessArgs("nginx: master process /usr/sbin/nginx -c /etc/nginx/nginx.conf nginx");
        process.setProcessCurrentWorkingDirectory("nginx");

        return process;
    }
}
