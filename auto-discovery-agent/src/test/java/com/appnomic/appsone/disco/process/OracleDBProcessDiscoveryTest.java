package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class OracleDBProcessDiscoveryTest {
    @Test
    public void validatePort() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess oraclePortAttributeAccess = Util.getOracleDbAttributeAccess();
        monitorPortAttributeAccessList.add(oraclePortAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getOracleDbComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getOracleDbProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("2347"))
                proc = p;
        }
        assert proc  != null;

        assert proc.getComponentId() == 19;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("1521");
        assert proc.getComponentVersion().equals("********.0");
    }

    private Component getOracleDbComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("Oracle");
        component.setComponentId(19);
        component.setComponentTypeId(4);
        component.setDiscoveryPattern("(?<containsOracleDb>[Oo][Rr][Aa][Uu][Ss][Ee][Rr]).*");
        component.setRelativePathList(Collections.singletonList("NETWORK/ADMIN/listener.ora"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getOracleDbProcess() {

        Process process = new Process();
        process.setProcessIdentifier("7E0W8685F755619E9X7D5CAB89EL9f54");
        process.setPid("2347");
        process.setProcessName("dbhome" + File.separator + "BIN" + File.separator + "orauser-TNSLSNR.exe");
        process.setProcessArgs("orauser");
        process.setProcessCurrentWorkingDirectory("dbhome" + File.separator + "BIN");
        return process;
    }
}