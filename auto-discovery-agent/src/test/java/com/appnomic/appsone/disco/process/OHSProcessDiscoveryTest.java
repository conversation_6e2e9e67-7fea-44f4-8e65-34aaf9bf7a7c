package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.boon.core.Sys;
import org.junit.Test;
import org.junit.experimental.runners.Enclosed;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;

import java.util.*;

@RunWith(Enclosed.class)
public class OHSProcessDiscoveryTest {

    public static class NonParamaterisedIHSProcessDiscoveryTest {

        @Test
        public void validateListenPort() {

            Host host = OHSProcessDiscoveryTest.hostAttribute();

            String hostAddress = Utils.getHostAddress(host.getNetworkInterfaces());

            List<Process> ohsProcessList = new ArrayList<>();

            Process ohsProcess = getOHSProcess();

            ohsProcessList.add(ohsProcess);

            ComponentDiscovery componentDiscovery = new ComponentDiscovery();

            List<Process> discoveredProcessList = componentDiscovery.run(ohsProcessList, host, StringUtil.EMPTY_STRING);
            Process proc = null;
            for(Process p: discoveredProcessList) {
                if(p.getPid().equalsIgnoreCase("28456")) {
                    proc = p;
                }
            }
            assert proc != null;
            assert proc.getComponentId() == 10;
            assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
            assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("7771");
        }

        private Process getOHSProcess() {

            Process process = new Process();
            process.setProcessIdentifier("A2E98FCA7910B1536F0AF2CE7DC06038");
            process.setPid("28456");
            process.setProcessName("/home/<USER>/Oracle/Middleware/Oracle_Home/ohs/bin/httpd");
            process.setProcessArgs("/home/<USER>/Oracle/Middleware/Oracle_Home/wlserver/../ohs/bin/httpd -DOHS_MPM_EVENT -d /home/<USER>/Oracle/Middleware/Oracle_Home/user_projects/domains/base_domain/config/fmwconfig/components/OHS/instances/ohs1 -k start -f /ohs1/httpd.conf ");
            process.setProcessCurrentWorkingDirectory("ohs1");

            return process;
        }
    }

    @RunWith(Parameterized.class)
    public static class ParameterisedOHSProcessDiscoveryTest {
        private final String fileName;

        private final String processId;

        public ParameterisedOHSProcessDiscoveryTest(String fileName, String processId) {
            this.fileName = fileName;
            this.processId = processId;
        }

        @Test
        public void validateOHSComponent() {

            Host host = OHSProcessDiscoveryTest.hostAttribute();

            List<String> processIdsFile = new ArrayList<>();

            List<String> processIdList = Arrays.asList(processId.split(","));

            List<Process> processList = Util.getProcessListFromFile(fileName);

            for (Process p : processList) {
                processIdsFile.add(p.getPid());
            }
            ComponentDiscovery componentDiscovery = new ComponentDiscovery();

            List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
            for (Process p : discoveredProcessList) {
                if (processIdsFile.contains(p.getPid()) && processIdList.contains(p.getPid())) {
                    assert p.getComponentId() == 10;
                }
                if (processIdsFile.contains(p.getPid()) && !processIdList.contains(p.getPid())) {
                    assert p.getComponentId() != 10;
                }
            }
        }

        @Parameterized.Parameters
        public static Collection fileDetails() {
            return Arrays.asList(new Object[][]{
                    {"src/test/resources/clientDataFilesOHS/JKB/coreweb1.txt", "2646,3837,5066,2413"},
                    {"src/test/resources/clientDataFilesOHS/JKB/coreweb2.txt", "50835,13920,16195,5060"},
                    {"src/test/resources/clientDataFilesOHS/JKB/coreweb3.txt", "36548,20768,20850,60124"}
            });
        }
    }

    private static Component getOHSComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("OHS - Oracle HTTP Server");
        component.setComponentId(10);
        component.setComponentTypeId(2);
        component.setDiscoveryPattern("(?<containsOHS>([Oo][Hh][Ss]).*.[Hh][Tt][Tt][Pp][Dd])");
        component.setRelativePathList(Collections.singletonList("httpd.conf"));
        component.setAttributes(attributeList);

        return component;
    }

    private static Host hostAttribute() {
        List<Component> componentList = new ArrayList<>();
        Host host = new Host();
        List<Attribute> attributeList = new ArrayList<>();
        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();
        AttributeAccess listenAttributeAccess = Util.getListenAttributeAccess();
        monitorPortAttributeAccessList.add(listenAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getOHSComponent(attributeList);
        componentList.add(component);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();
        YamlFileLoader.setAutoDiscoveryComponents(componentList);
        host.setOperatingSystem("");
        host.setNetworkInterfaces(interfaceList);

        return host;
    }
}