package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import org.junit.Test;

public class ProcessDiscoveryTest {

    @Test
    public void testHashCollision() {
        Process processHost1 = new Process();
        processHost1.setProcessName("/usr/sbin/httpd");
        processHost1.setProcessArgs("/usr/sbin/httpd -DFOREGROUND");
        processHost1.setProcessIdentifier("3264");

        String hash1 = NodeIdGenerator.generateNodeIdForProcess(processHost1, "WIN-ZAQ36O8RB0K");


        Process processHost2 = new Process();
        processHost2.setProcessName("/usr/sbin/httpd");
        processHost2.setProcessArgs("/usr/sbin/httpd -DFOREGROUND");
        processHost2.setProcessIdentifier("12816");

        String hash2 = NodeIdGenerator.generateNodeIdForProcess(processHost2, "HQQARAVITLT02");

        assert !hash1.equals(hash2);
    }
}
