package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class WeblogicProcessDiscoveryTest {

    @Test
    public void validateWeblogicListenPortXPath() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenPortXPathAttributeAccess = getListenPortXPathAttributeAccess();

        monitorPortAttributeAccessList.add(listenPortXPathAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        monitorPortAttribute.getAccess().get(0).setPriority(1);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getWeblogicComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process = getWeblogicProcess();

        processList.add(process);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);

        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("13870"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 16;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("7001");
        assert proc.getComponentVersion().equals("********.0");
    }

    private AttributeAccess getListenPortXPathAttributeAccess() {
        AttributeAccess HTTPPortAttributeAccess = new AttributeAccess();
        HTTPPortAttributeAccess.setMethod("XPATH");
        HTTPPortAttributeAccess.setValue("dom:domain/dom:server/dom:listen-port");
        HTTPPortAttributeAccess.setPriority(1);
        return HTTPPortAttributeAccess;
    }

    private Component getWeblogicComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("Weblogic");
        component.setComponentId(16);
        component.setComponentTypeId(3);
        component.setDiscoveryPattern("(?<containsWeblogic>[Ww][eE][bB][lL][oO][gG][iI][cC]).*");
        component.setRelativePathList(Collections.singletonList("config/config.xml"));
        component.setAttributes(attributeList);

        return component;
    }

    private Process getWeblogicProcess() {

        Process process = new Process();
        process.setProcessIdentifier("93D272FF2B44CD1CDA6CD4F94F3AB99B");
        process.setPid("13870");
        process.setProcessName("weblogic" + File.separator + "bin" + File.separator + "java.exe");
        process.setProcessArgs("-Dprogram.name=startWebLogic.bat -Dweblogic.home.dir=" + File.separator + "weblogic");
        process.setProcessCurrentWorkingDirectory("weblogic" + File.separator + "bin");

        return process;
    }
}