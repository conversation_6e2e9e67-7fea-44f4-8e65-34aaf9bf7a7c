package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.Util;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.junit.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class HttpdProcessDiscoveryTest {

    @Test
    public void validateHTTPDListenPortWindows() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenAttributeAccess = Util.getListenAttributeAccess();
        monitorPortAttributeAccessList.add(listenAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getHttpdComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process1 = getHttpdProcessWindows();
        Process process2 = getHttpdProcessWindows1();

        processList.add(process1);
        processList.add(process2);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);
        YamlFileLoader.init();

        Host host = new Host();
        host.setOperatingSystem("Windows");
        Arguments.setOS("Windows");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host , StringUtil.EMPTY_STRING);
        Process proc = null;
        for (Process p : discoveredProcessList) {
            if (p.getPid().equalsIgnoreCase("11808"))
                proc = p;
        }
        assert proc  != null;

        assert proc.getComponentId() == 7;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("80");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd\\conf\\httpd.conf");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd/conf/httpd.conf");
        }


        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("11809"))
                proc = p;
        }
        assert proc.getComponentId() == 7;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("80");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_windows\\conf\\httpd.conf");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_windows/conf/httpd.conf");
        }
    }

    @Test
    public void validateHTTPDListenPortLinux() {

        Attribute hostAttribute = Util.getHostAttribute();

        List<AttributeAccess> monitorPortAttributeAccessList = new ArrayList<>();

        AttributeAccess listenAttributeAccess = Util.getListenAttributeAccess();
        monitorPortAttributeAccessList.add(listenAttributeAccess);

        Attribute monitorPortAttribute = Util.getMonitorPortAttribute(monitorPortAttributeAccessList);

        List<Attribute> attributeList = new ArrayList<>();
        attributeList.add(hostAttribute);
        attributeList.add(monitorPortAttribute);

        Component component = getHttpdComponent(attributeList);

        List<Component> componentList = new ArrayList<>();
        componentList.add(component);

        List<Process> processList = new ArrayList<>();

        Process process1 = getHttpdProcessLinux1();
        Process process2 = getHttpdProcessLinux2();
        Process process3 = getHttpdProcessLinux3();
        Process process4 = getHttpdProcessLinux4();

        processList.add(process1);
        processList.add(process2);
        processList.add(process3);
        processList.add(process4);

        List<NetworkInterface> interfaceList = Util.getNetworkInterfacesList();

        String hostAddress = Utils.getHostAddress(interfaceList);

        YamlFileLoader.setAutoDiscoveryComponents(componentList);

        Host host = new Host();
        host.setOperatingSystem("Linux");
        Arguments.setOS("Linux");
        host.setNetworkInterfaces(interfaceList);

        ComponentDiscovery componentDiscovery = new ComponentDiscovery();

        List<Process> discoveredProcessList = componentDiscovery.run(processList, host, StringUtil.EMPTY_STRING);
        Process proc = null;
        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("893211"))
                proc = p;
        }
        assert proc  != null;
        assert proc.getComponentId() == 7;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("9444");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_Server1\\conf\\httpd.conf");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_Server1/conf/httpd.conf");
        }

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("893222"))
                proc = p;
        }
        assert proc.getComponentId() == 7;
        assert proc.getAttributes().containsKey("HostAddress") && proc.getAttributes().get("HostAddress").equals(hostAddress);
        assert proc.getAttributes().containsKey("MonitorPort") && proc.getAttributes().get("MonitorPort").equals("9446");
        assert proc.getKpis().get(Constants.KPI_FILE_WATCH) != null;
        if(Utils.isWindows()) {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_Server2\\conf\\" +
                    "httpd.conf");
        }
        else {
            assert proc.getKpis().get(Constants.KPI_FILE_WATCH).get(0).equals("httpd_Server2/conf/" +
                    "httpd.conf");
        }

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("893333"))
                proc = p;
        }
        assert proc.getComponentId() == 0;

        for(Process p : discoveredProcessList)
        {
            if(p.getPid().equalsIgnoreCase("893444"))
                proc = p;
        }
        assert proc.getComponentId() == 0;

    }

    private Component getHttpdComponent(List<Attribute> attributeList) {

        Component component = new Component();
        component.setComponentName("Apache httpd - Apache");
        component.setComponentId(7);
        component.setComponentTypeId(2);
        component.setDiscoveryPattern("(?<process>([h][t][t][p][d]))");
        component.setRelativePathList(Collections.singletonList("conf\\httpd.conf"));
        component.setAttributes(attributeList);

        return component;
    }


    private Process getHttpdProcessWindows() {

        Process process = new Process();
        process.setProcessIdentifier("3D0A4585F755619E9D7D3CAB93ED7E11");
        process.setPid("11808");
        process.setProcessName("httpd" + File.separator + "bin" + File.separator + "httpd.exe");
        process.setProcessArgs("httpd" + File.separator + "bin" + File.separator + "httpd.exe");
        process.setProcessCurrentWorkingDirectory("C:");
        process.setComponentVersion("2.4.6");

        return process;
    }

    private Process getHttpdProcessWindows1() {

        Process process = new Process();
        process.setProcessIdentifier("3D0A4585F755619E9D7D3CAB93ED7E11");
        process.setPid("11809");
        process.setProcessName("httpd_windows" + File.separator + "bin" + File.separator + "httpd.exe");
        process.setProcessArgs("httpd_windows" + File.separator + "bin" + File.separator + "httpd.exe");
        process.setProcessCurrentWorkingDirectory("C:");
        process.setComponentVersion("2.4.6");

        return process;
    }

    private Process getHttpdProcessLinux1() {
        Process process = new Process();
        process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08222");
        process.setPid("893211");
        process.setProcessName("httpd_Server1" + File.separator + "sbin" + File.separator + "httpd");
        process.setProcessArgs("httpd_Server1" + File.separator + "sbin" + File.separator + "httpd -DFOREGROUND ");

        process.setProcessCurrentWorkingDirectory(File.separator);
        process.setComponentVersion("2.4.6");

        return process;
    }

    private Process getHttpdProcessLinux2() {
        Process process = new Process();
        process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08111");
        process.setPid("893222");
        process.setProcessName("httpd_Server2" + File.separator + "sbin" + File.separator + "httpd");
        process.setProcessArgs("httpd_Server2" + File.separator + "sbin" + File.separator + "httpd -DFOREGROUND ");

        process.setProcessCurrentWorkingDirectory(File.separator);
        process.setComponentVersion("2.4.6");

        return process;
    }

    private Process getHttpdProcessLinux3() {
        Process process = new Process();
        process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08333");
        process.setPid("893333");
        process.setProcessName("httpd_Server2" + File.separator + "sbin" + File.separator + "httpd");
        process.setProcessArgs("httpd_Server2" + File.separator + "sbin" + File.separator + "httpd -DFOREGROUND ");

        process.setProcessCurrentWorkingDirectory(File.separator);


        return process;
    }

    private Process getHttpdProcessLinux4() {
        Process process = new Process();
        process.setProcessIdentifier("058FE21E5D75317C9B30BA5D70E3FF08444");
        process.setPid("893444");
        process.setProcessName("/usr/sbin/pfcdaemon");
        process.setProcessArgs("/usr/sbin/pfcdaemon");

        process.setProcessCurrentWorkingDirectory(File.separator);

        return process;
    }
}
