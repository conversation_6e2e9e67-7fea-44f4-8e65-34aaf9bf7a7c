package com.appnomic.appsone;

import com.appnomic.appsone.prereq.PrerequisiteComponentScript;
import org.junit.Test;

public class PrerequisiteComponentScriptTest {

    @Test
    public void apacheHTTPDScriptLinux() {

        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(7, "LINUX", true);
        assert effectiveScript.trim().equals("Apache-HTTPD_2.4_Prerequiste_Validate.sh");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(7, "LINUX", false);
        assert effectiveScript.trim().equals("Apache-HTTPD_2.4_Prerequiste_Enable.sh");

    }

    @Test
    public void apacheHTTPDScriptWindows() {

        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(7, "WINDOWS", true);
        assert effectiveScript.equals("Apache-HTTPD_2.4_Prerequiste_Validate.bat");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(7, "WINDOWS", false);
        assert effectiveScript.trim().equals("Apache-HTTPD_2.4_Prerequiste_Enable.bat");

    }

    @Test
    public void apacheWEBLOGICScriptLinux() {

        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(16, "LINUX", true);
        assert effectiveScript.equals("WebLogic_12C_Prerequiste_Validate.sh");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(16, "LINUX", false);
        assert effectiveScript.equals("WebLogic_12C_Prerequiste_Enable.sh");

    }

    @Test
    public void apacheWEBLOGICScriptWindows() {
        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(16, "WINDOWS", true);
        assert effectiveScript.equals("WebLogic_12C_Prerequiste_Validate.bat");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(16, "WINDOWS", false);
        assert effectiveScript.trim().equals("WebLogic_12C_Prerequiste_Enable.bat");

    }

    @Test
    public void apacheOracleScriptLinux() {

        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(19, "LINUX", true);
        assert effectiveScript.equals("Oracle_Prerequiste_Validate.sh");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(19, "LINUX", false);
        assert effectiveScript.equals("Oracle_Prerequiste_Enable.sh");

    }

    @Test
    public void apacheOracleScriptWindows() {
        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        String effectiveScript = prerequisiteComponentScript.getApplicableScriptName(19, "WINDOWS", true);
        assert effectiveScript.equals("Oracle_Prerequiste_Validate.bat");

        effectiveScript = prerequisiteComponentScript.getApplicableScriptName(19, "WINDOWS", false);
        assert effectiveScript.trim().equals("Oracle_Prerequiste_Enable.bat");

    }
}