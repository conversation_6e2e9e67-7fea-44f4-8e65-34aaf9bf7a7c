import com.appnomic.appsone.util.Arguments;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.IOUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class NetstatExecutor {

    private static final Logger logger = LoggerFactory.getLogger(NetstatExecutor.class);

    public static void main(String[] args) {
        try {
            Process p = new ProcessBuilder(Arguments.getShellPath(), "-c", "netstat -an | grep LISTEN").start();
            String stdout = IOUtils.toString(p.getInputStream());
            String[] lines = stdout.split("\n");
            String pattern = "tcp(|4|6)\\s+[0-9]\\s+[0-9]\\s+(?<address>[0-9\\.\\:\\*]+)\\s+[\\*\\.]+\\s+LISTEN\\s+";
            Pattern p1 = Pattern.compile(pattern);
            for (String line : lines) {
                Matcher m = p1.matcher(line);
                if (m.find()) {
                    String ipPort = m.group("address");
                    int index = ipPort.lastIndexOf(".");
                    if (index != -1) {
                        String ip = ipPort.substring(0, index);
                        String port = ipPort.substring(index + 1);
                        System.out.println("Got " + ip + " & " + port);
                    }
                }
            }
        } catch (Exception ex) {
            logger.error("Exception during NetstatExecutor {} : {}", ex.getMessage(), ex);
        }
    }
}
