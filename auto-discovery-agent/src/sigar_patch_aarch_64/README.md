##Background

Hyperic Sigar is an open source library which uses OS specific binaries ( .so, .dll, .ln etc) to gather system related information and provides out of the box bindings for multiple languages including java JNI bindings

For scenarios where we don’t have the native binaries available for a specific platform, we can modify the source and generate the platform binaries accordingly.

This article documents the steps to generate binaries for aarch-64 Linux which is officially not supported.


##Steps

1. Clone the official repository from Github on aarch-64 host

2. cd into sigar directory

3. Make modifications to source files specific to aarch-64

4. Run /autoclean.sh to delete any old build

5. Set the needed major, minor version in configure.cc for native code

6. Set the needed major, minor version in version.properties for JNI bindings

7. Run ./autogen.sh

8. Run ./configure

9. Run make

10. Verify if native libraries got generated in sigar/src/.libs

11. cd into sigar/bindings/java/

12. Run ant

13. Verify if sigar.jar and libsigar-aarch64-linux.so got generated at sigar/bindings/java/sigar-bin/lib

14. Verify if generated .so file has all symbols exported for JNI bindings
nm libsigar_aarch64.so | grep getNative


**Use the .so and and sigar.jar from sigar/bindings/java/sigar-bin/lib**



##Source modification for aarch-64


###Add an exclude property for sigar_version_autoconf.c in jni-build.xml

> sigar/bindings/java/hyperic_jni/jni-build.xml


>  <fileset dir="${jni.source.dir}">
>     <patternset refid="jni.source.files"/>
>     <exclude name="src/sigar_version_autoconf.c"/>
>  </fileset>


###Remove all xml tags which adds command line flag -m64 to gcc in jni-build.xml

> sigar/bindings/java/hyperic_jni/jni-build.xml


> value="-m64" if="jni.arch64"


###Comment out the following arch check logic from ArchNameTask.java

> sigar/bindings/java/hyperic_jni/src/org/hyperic/jni/ArchNameTask.java


> if (ArchLoader.IS_LINUX) {
>    if (!osArch.equals("ia64") && !osArch.equals("aarch64")) {
>         getProject().setProperty("jni.gccm", "-m64");
>    }
>  }