---
configurationEntities:
  - componentName: "Apache httpd - Apache"
    componentType: "Web Server"
    version: "2.4.6"
    entities:
      - entityName: "httpd.conf"
        relativePath: "conf/httpd.conf"
        kpiType: "FileWatch"
      - entityName: "httpd.conf-test"
        relativePath: "conf/httpd.conf-test"
        kpiType: "KeyValue"
  - componentName: "IIS"
    componentType: "Application Server"
    version: "8.5"
    entities:
      - entityName: "applicationHost.config"
        relativePath: "inetsrv/config/applicationHost.config"
        kpiType: "FileWatch"
      - entityName: "redirection.config"
        relativePath: "inetsrv/config/redirection.config"
        kpiType: "KeyValue"
  - componentName: "Apache Tomcat"
    componentType: "Application Server"
    version: "6.0.x"
    entities:
      - entityName: "server.xml"
        relativePath: "conf/server.xml"
        kpiType: "FileWatch"
      - entityName: "web.xml"
        relativePath: "conf/web.xml"
        kpiType: "FileWatch"
      - entityName: "setenv.sh"
        relativePath: "bin/setenv.sh"
        kpiType: "KeyValue"
      - entityName: "catalina.sh"
        relativePath: "bin/catalina.sh"
        kpiType: "KeyValue"
  - componentName: "Apache Tomcat"
    componentType: "Application Server"
    version: "7.0.x"
    entities:
      - entityName: "server.xml"
        relativePath: "conf/server.xml"
        kpiType: "FileWatch"
      - entityName: "web.xml"
        relativePath: "conf/web.xml"
        kpiType: "FileWatch"
      - entityName: "setenv.sh"
        relativePath: "bin/setenv.sh"
        kpiType: "KeyValue"
      - entityName: "catalina.sh"
        relativePath: "bin/catalina.sh"
        kpiType: "KeyValue"
  - componentName: "Apache Tomcat"
    componentType: "Application Server"
    version: "8.0.x"
    entities:
      - entityName: "server.xml"
        relativePath: "conf/server.xml"
        kpiType: "FileWatch"
      - entityName: "web.xml"
        relativePath: "conf/web.xml"
        kpiType: "FileWatch"
      - entityName: "setenv.sh"
        relativePath: "bin/setenv.sh"
        kpiType: "KeyValue"
      - entityName: "catalina.sh"
        relativePath: "bin/catalina.sh"
        kpiType: "KeyValue"
  - componentName: "Apache Tomcat"
    componentType: "Application Server"
    version: "8.5.x"
    entities:
      - entityName: "server.xml"
        relativePath: "conf/server.xml"
        kpiType: "FileWatch"
      - entityName: "web.xml"
        relativePath: "conf/web.xml"
        kpiType: "FileWatch"
      - entityName: "setenv.sh"
        relativePath: "bin/setenv.sh"
        kpiType: "KeyValue"
      - entityName: "catalina.sh"
        relativePath: "bin/catalina.sh"
        kpiType: "KeyValue"
  - componentName: "Apache Tomcat"
    componentType: "Application Server"
    version: "9.0.x"
    entities:
      - entityName: "server.xml"
        relativePath: "conf/server.xml"
        kpiType: "FileWatch"
      - entityName: "web.xml"
        relativePath: "conf/web.xml"
        kpiType: "FileWatch"
      - entityName: "setenv.sh"
        relativePath: "bin/setenv.sh"
        kpiType: "KeyValue"
      - entityName: "catalina.sh"
        relativePath: "bin/catalina.sh"
        kpiType: "KeyValue"
  - componentName: "JBoss"
    componentType: "Application Server"
    version: "7.1.0"
    entities:
      - entityName: "standalone.xml"
        relativePath: "standalone/configuration/standalone.xml"
        kpiType: "FileWatch"
      - entityName: "application-users.properties"
        relativePath: "standalone/configuration/application-users.properties"
        kpiType: "KeyValue"
  - componentName: "MSSQL"
    componentType: "Database Server"
    version: "2016"
    entities:
      - entityName: "License_msodbcsql_ENU.txt"
        relativePath: "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/130/License Terms/License_msodbcsql_ENU.txt"
        kpiType: "FileWatch"
      - entityName: "sqlodbc_keyfile.dll"
        relativePath: "C:/Program Files/Microsoft SQL Server/Client SDK/ODBC/130/KeyFile/1033/sqlodbc_keyfile.dll"
        kpiType: "KeyValue"