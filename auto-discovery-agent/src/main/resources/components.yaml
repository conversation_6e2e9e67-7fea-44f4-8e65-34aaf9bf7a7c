---
components:
  - componentId: 7
    componentTypeId: 2
    componentName: "Apache httpd - Apache"
    relativePathList:
      - "conf/httpd.conf"
      - "apache2/ports.conf"
    discoveryPattern: "(?<process>([h][t][t][p][d]))"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "APACHE_PARSER"
            value: "Listen"
            defaultValue: 80
            priority: 1
  - componentId: 8
    componentTypeId: 2
    componentName: "IIS"
    relativePathList:
      - "/inetsrv/config/applicationHost.config"
    discoveryPattern: "(?<containsIIS>[Ii][Ii][sS]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "XPATH"
            value: "/configuration/system.applicationHost/sites/site[@name=\"Default Web Site\"]/bindings/binding[@protocol='http']/@bindingInformation"
            defaultValue: 80
            priority: 1
  - componentId: 13
    componentTypeId: 3
    componentName: "Apache Tomcat"
    relativePathList:
      - "conf/server.xml"
    discoveryPattern: "(?<process>([tT][oO][mM][cC][aA][tT]))"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "JMXPort"
        isMandatory: 1
        access:
          - method: "REG_EX"
            value: "((-Dcom.sun.management.jmxremote.port=)(?<jmxport>[0-9]+))"
            defaultValue: 9875
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "XPATH"
            value: "/Server/Service/Connector[@protocol!='AJP/1.3']/@port"
            defaultValue: 8009
            priority: 2
          - method: "XPATH"
            value: "/Server/Service/Connector[@protocol!='HTTP/1.1']/@port"
            defaultValue: 8080
            priority: 1
      - attributeName: "TomcatDistribution"
        isMandatory: 0
        access:
          - method: "REG_EX"
            value: "apache-tomcat-(?<tomcatDistro>[0-9.]+)"
            priority: 1
  - componentId: 14
    componentTypeId: 3
    componentName: "JBoss"
    relativePathList:
      - "standalone/configuration/standalone.xml"
    discoveryPattern: "(?<containsJboss>[jJ][bB][oO][sS][sS])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "XPATH"
            value: "jbo:server/jbo:socket-binding-group/jbo:socket-binding[@name=\"https\"]/@port"
            defaultValue: 8080
            priority: 1
  - componentId: 15
    componentTypeId: 3
    componentName: "WebSphere"
    relativePathList:
      - "serverindex.xml"
    discoveryPattern: "(?<containsWebsphere>[Ww][eE][bB][sS][pP][hH][eE][rR][eE].*.[Ii][Bb][Mm].[Ww][Ss].[Rr][Uu][Nn][Tt][Ii][Mm][Ee].[Ww][Ss][Ss][Ee][Rr][Vv][Ee][Rr])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "XPATH"
            value: "serverindex:ServerIndex/serverEntries[@serverName=\"???\"]/specialEndpoints[@endPointName=\"SOAP_CONNECTOR_ADDRESS\"]/endPoint/@port"
            defaultValue: 9080
            priority: 3
          - method: "XPATH"
            value: "serverindex:ServerIndex/serverEntries[@serverName=\"???\"]/specialEndpoints[@endPointName=\"WC_defaulthost_secure\"]/endPoint/@port"
            defaultValue: 9443
            priority: 2
          - method: "XPATH"
            value: "serverindex:ServerIndex/serverEntries[@serverName=\"???\"]/specialEndpoints[@endPointName=\"WC_defaulthost\"]/endPoint/@port"
            defaultValue: 9080
            priority: 1
      - attributeName: "JMXPort"
        isMandatory: 1
        access:
          - method: "REG_EX"
            value: "((-Dcom.sun.management.jmxremote.port=)(?<jmxport>[0-9]+))"
            defaultValue: 9100
            priority: 1
  - componentId: 16
    componentTypeId: 3
    componentName: "Weblogic"
    relativePathList:
      - "config/config.xml"
    discoveryPattern: "(?<containsWeblogic>[Ww][eE][bB][lL][oO][gG][iI][cC]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "JMXPort"
        isMandatory: 1
        access:
          - method: "REG_EX"
            value: "((-Dcom.sun.management.jmxremote.port=)(?<jmxport>[0-9]+))"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "XPATH"
            value: "dom:domain/dom:server/dom:listen-port"
            defaultValue: 7001
            priority: 1
  - componentId: 18
    componentTypeId: 4
    componentName: "MySQL"
    relativePathList:
      - "my.ini"
    discoveryPattern: "(?<containsMySql>[Mm][Yy][Ss][Qq][Ll]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "INI_PARSER"
            value: "port"
            defaultValue: 3306
            priority: 1
  - componentId: 19
    componentTypeId: 4
    componentName: "Oracle"
    relativePathList:
      - "NETWORK/ADMIN/listener.ora"
    discoveryPattern: "(?<containsOracleDb>[Dd][Bb][Hh][Oo][Mm][Ee]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "ORA_PARSER"
            value: "port"
            priority: 1
  - componentId: 21
    componentTypeId: 4
    componentName: "MSSQL"
    relativePathList:
      - "conf/mssql"
    discoveryPattern: "(?<containsMSSQLDb>[Ss][Qq][Ll][Ss][Ee][Rr][Vv][Rr][.][Ee][Xx][Ee]|[Mm][Ss][Ss][Qq][Ll]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "BINARY_EXECUTE"
            value: "FOR /F \"tokens=3 delims= \" %G IN ('reg.exe query HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\MSSQLServer\\MSSQLServer\\SuperSocketNetLib\\Tcp /v TcpPort')  DO @echo %G"
            defaultValue: 1433
            priority: 1
  - componentId: 29
    componentTypeId: 4
    componentName: "Zookeeper"
    relativePathList:
      - "conf/zoo.cfg"
    discoveryPattern: "(?<containsZookeeper>[Zz][Oo][Oo][Kk][Ee][Ee][Pp][Ee][Rr]|[Qq][Uu][Oo][Rr][Mm]).*"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "INI_PARSER"
            value: "clientPort"
            defaultValue: 2181
            priority: 1
  - componentId: 40
    componentTypeId: 11
    componentName: "KafkaServer"
    relativePathList:
      - "config/server.properties"
    discoveryPattern: "(?<process>[Kk][Aa][Ff][Kk][Aa])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "PROPERTY_PARSER"
            value: "port"
            defaultValue: 9092
            priority: 1
  - componentId: 47
    componentTypeId: 2
    componentName: "NGINX"
    relativePathList:
      - "conf.d/default.conf"
      - "conf/nginx.conf"
    discoveryPattern: "(?<process>[Nn][Gg][Ii][Nn][Xx])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "NGINX_PARSER"
            value: "listen"
            defaultValue: 80
            priority: 1
  - componentId: 10
    componentTypeId: 2
    componentName: "OHS - Oracle Http Server"
    relativePathList:
      - "httpd.conf"
    discoveryPattern: "(?<containsOHS>([Oo][Hh][Ss]).*.[Hh][Tt][Tt][Pp][Dd])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "APACHE_PARSER"
            value: "Listen"
            defaultValue: 7777
            priority: 1
      - attributeName: "INSTALLATION_PATH"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "InstallationPath"
            priority: 1
      - attributeName: "Protocol"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "openssl s_client -connect"
            priority: 1
  - componentId: 65
    componentTypeId: 2
    componentName: "NODEJS"
    relativePathList:
      - "conf/server.js"
      - "server.js"
    discoveryPattern: "(?<containsNode>[.][Nn][Vv][Mm].*[Nn][Oo][Dd][Ee].*)"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "MonitorPort"
        isMandatory: 1
        access:
          - method: "FILE_PARSER"
            value: ".listen("
            priority: 1
  - componentId: 11
    componentTypeId: 3
    componentName: "FinacleServices"
    discoveryPattern: "(?<containsFinacle>[Ll][Ii][Mm][Oo][ ][Ss][Tt][Aa][Rr][Tt])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "LimoServer"
        isMandatory: 0
        access:
          - method: "FINACLE_PARSER"
            value: "limo start"
            priority: 1
      - attributeName: "LiServer"
        isMandatory: 0
        access:
          - method: "FINACLE_PARSER"
            value: "lisrvr"
            priority: 1
      - attributeName: "MariaServer"
        isMandatory: 0
        access:
          - method: "FINACLE_PARSER"
            value: "maria"
            priority: 1
      - attributeName: "LimoServerBCMon"
        isMandatory: 0
        access:
          - method: "FINACLE_PARSER"
            value: "bc-mon"
            priority: 1
      - attributeName: "LimoServerBCEcho"
        isMandatory: 0
        access:
          - method: "FINACLE_PARSER"
            value: "bc-echo"
            priority: 1
  - componentId: 26
    componentTypeId: 7
    componentName: "IBM MQ"
    relativePathList:
      - "sampleConfig.xml"
    discoveryPattern: "(?<ContainsIBM>[Ii][Bb][Mm].*[Mm][Qq].*-[Mm])"
    attributes:
      - attributeName: "HostAddress"
        isMandatory: 1
        access:
          - method: "INHERIT"
            value: "HOST"
            priority: 1
      - attributeName: "QueueManager"
        isMandatory: 1
        access:
          - method: "REG_EX"
            value: "((-m)[ ]{0,}(?<queuemanager>[0-9a-zA-Z.]+))"
            priority: 1