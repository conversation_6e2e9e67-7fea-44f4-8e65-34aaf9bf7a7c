---
componentsVersion:
  - componentId: 7
    componentName: "Apache httpd - Apache"
    versionCommands:
      - path: "bin/httpd.exe"
        command: "-v"
        method: "BINARY_EXECUTE"
        operatingSystem: "Windows"
      - path: "bin/apachectl"
        command: " -v |head -1 | cut -d ':' -f2 | cut -d '/' -f2 | cut -d ' ' -f1"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 8
    componentName: "IIS"
    versionCommands:
      - path: ""
        command: "reg.exe query HKLM\\SOFTWARE\\Microsoft\\InetStp /v VersionString"
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
    versionMap:
  - componentId: 13
    componentName: "Apache Tomcat"
    versionCommands:
      - path: "bin/version.sh"
        command: " "
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
      - path: "bin/version.sh"
        command: "-v  | grep -E 'version' | cut -d '/' -f 2"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 14
    componentName: "JBoss"
    versionCommands:
      - path: "bin/standalone.sh"
        command: "-v"
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
      - path: "bin/standalone.sh"
        command: "-v  | grep -E 'JBoss EAP' | cut -d 'G' -f 1 | cut -d ' ' -f 3"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
      - path: "bin/run.sh"
        command: "--version"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 15
    componentName: "WebSphere"
    versionCommands:
      - path: "WebSphere/AppServer/bin/versionInfo.sh"
        command: " "
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 16
    componentName: "Weblogic"
    versionCommands:
      - path: "inventory/registry.xml"
        command: "dom:registry/dom:distributions/dom:distribution/@version"
        method: "XPATH"
        operatingSystem: "Windows"
      - path: "inventory/registry.xml"
        command: "dom:registry/dom:distributions/dom:distribution/@version"
        method: "XPATH"
        operatingSystem: "Linux"
    versionMap:
  - componentId: 18
    componentName: "MySQL"
    versionCommands:
      - path: "bin/mysqld.exe"
        command: "-V  | cut -d ' ' -f 4"
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
      - path: ""
        command: "mysqld -V  | cut -d ' ' -f 4"
        method: "BINARY_EXECUTE"
        operatingSystem: "Linux"
    versionMap:
  - componentId: 19
    componentName: "Oracle"
    versionCommands:
      - path: "inventory/ContentsXML/comps.xml"
        command: "/PRD_LIST/TL_LIST/COMP/@VER"
        method: "XPATH"
        operatingSystem: "Windows"
      - path: "inventory/ContentsXML/comps.xml"
        command: "/PRD_LIST/TL_LIST/COMP/@VER"
        method: "XPATH"
        operatingSystem: "Linux"
    versionMap:
  - componentId: 21
    componentName: "MSSQL"
    versionCommands:
      - path: ""
        command: "FOR /F \"tokens=3 delims= \" %G IN ('reg.exe query HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\MSSQLServer\\MSSQLServer\\CurrentVersion /v CurrentVersion')  DO @echo %G"
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
    versionMap:
      "15.": "2019"
      "14.": "2017"
      "13.": "2016"
      "12.": "2014"
      "11.": "2012"
      "10.5": "2008 R2"
      "10.": "2008"
      "9.": "2005"
      "8.": "2000"
  - componentId: 47
    componentName: "NGINX"
    versionCommands:
      - path: "nginx.exe"
        command: "-v |& grep 'version' | cut -d '/' -f 2"
        method: "BINARY_EXECUTE"
        operatingSystem: "Windows"
      - path: "bin/nginx.exe"
        command: "-v |& grep 'version' | cut -d '/' -f 2"
        method: "BINARY_EXECUTE"
        operatingSystem: "Windows"
      - path: ""
        command: "nginx -v  |& grep -E 'version' | cut -d '/' -f 2"
        method: "BINARY_EXECUTE"
        operatingSystem: "Linux"
    versionMap:
  - componentId: 40
    componentName: "KafkaServer"
    versionCommands:
      - path: "bin/windows/kafka-topics.bat"
        command: "--version"
        method: "BINARY_EXECUTE"
        operatingSystem: "WINDOWS"
      - path: "bin/kafka-topics.sh"
        command: "--version"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
      - path: ""
        command: "find ./libs/ -name \\*kafka_\\* | head -1 | grep -o '\\kafka[^\\n]*' | cut -d '-' -f2 | cut -d 'j' -f1"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 65
    componentName: "NODEJS"
    versionCommands:
      - path: ""
        command: "node -v | cut -d'v' -f2"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 10
    componentName: "OHS - Oracle HTTP Server"
    versionCommands:
      - path: "bin/version.txt"
        command: " | head -2 | cut -d '_' -f2 | cut -d ' ' -f2 | tail -1"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap:
  - componentId: 26
    componentName: "IBM MQ"
    versionCommands:
      - path: ""
        command: "dspmqver -f 2"
        method: "BINARY_EXECUTE"
        operatingSystem: "Windows"
      - path: ""
        command: "dspmqver | head -2 |tail -1 | cut -d ':' -f2"
        method: "BINARY_EXECUTE"
        operatingSystem: "LINUX"
    versionMap: