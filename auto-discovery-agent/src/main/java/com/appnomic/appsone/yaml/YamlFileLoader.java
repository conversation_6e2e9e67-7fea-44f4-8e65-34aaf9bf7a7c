package com.appnomic.appsone.yaml;

import com.appnomic.appsone.ccapi.ControlCenterService;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import io.netty.util.internal.StringUtil;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class YamlFileLoader {

    private static final Logger log = LoggerFactory.getLogger(YamlFileLoader.class);

    private static ComponentsYamlFile componentsYamlFile = new ComponentsYamlFile();

    private static VersionInfoYamlFile versionInfoYamlFile = new VersionInfoYamlFile();

    private static EntitiesYamlFile entitiesYamlFile = new EntitiesYamlFile();

    private static ServerListYamlFile serverYamlFile = new ServerListYamlFile();

    private static ScriptInfoYamlFile scriptInfoYamlFile = new ScriptInfoYamlFile();

    private static BlackListedProcesses blackListedProcesses = new BlackListedProcesses();

    private static List<Component> autoDiscoveryComponents = null;

    private static List<Entity> configurationEntities = null;

    static {
        loadComponentsYamlFile();
        loadVersionInfoYamlFile();
        loadServerListYamlFile();
        loadScriptInfoYamlFile();
        loadEntitiesYamlFile();
        loadProcessBlackList();
    }

    public static List<Component> getComponents() {
        if (componentsYamlFile.getComponents() == null) {
            loadComponentsYamlFile();
        }
        return componentsYamlFile.getComponents();
    }

    public static void refreshComponentsFromYamlFile() {
        loadComponentsYamlFile();
        autoDiscoveryComponents = componentsYamlFile.getComponents();
        YamlFileLoader.fixComponentConfigFileNames();
    }

    public static List<Component> getAutoDiscoveryComponents() {
        return autoDiscoveryComponents;
    }

    public static void setAutoDiscoveryComponents(List<Component> componentList) {
        autoDiscoveryComponents = componentList;
    }

    public static List<ComponentVersion> getComponentsVersion() {
        if (versionInfoYamlFile.getComponentsVersion() == null) {
            loadVersionInfoYamlFile();
        }
        return versionInfoYamlFile.getComponentsVersion();
    }

    public static List<Entity> getConfigurationEntities() {
        if (entitiesYamlFile.getConfigurationEntities() == null) {
            loadEntitiesYamlFile();
        }
        return entitiesYamlFile.getConfigurationEntities();
    }

    public static Entity getConfigurationEntity(String componentName, String componentVersion) {
        List<Entity> entityList = configurationEntities;
        Entity matchingEntity = null;
        if (componentVersion == null || componentVersion.isEmpty()) {
            return null;
        }
        for (Entity entity : entityList) {
            Matcher versionMatcher = Pattern.compile(entity.getVersion()).matcher(componentVersion);
            if (entity.getComponentName().equals(componentName) &&
                    versionMatcher.find()) {
                matchingEntity = entity;
            }
        }
        return matchingEntity;
    }

    public static Component getComponentById(int componentId) {
        List<Component> autoDiscoveryComponents = getComponents();
        Component matchingComponent = null;
        for (Component component : autoDiscoveryComponents) {
            if (component.getComponentId() == componentId) {
                matchingComponent = component;
            }
        }
        return matchingComponent;
    }

    public static List<Server> getServerList() {
        if (serverYamlFile.getServerList() == null) {
            loadServerListYamlFile();
        }
        return serverYamlFile.getServerList();
    }

    public static List<ComponentScript> getComponentScripts() {
        if (scriptInfoYamlFile.getComponentScripts() == null) {
            loadScriptInfoYamlFile();
        }
        return scriptInfoYamlFile.getComponentScripts();
    }

    public static List<String> getProcessBlackList() {
        if (blackListedProcesses.getProcessBlackList() == null) {
            loadProcessBlackList();
        }
        return blackListedProcesses.getProcessBlackList();
    }

    private static void loadServerListYamlFile() {
        String fileName = Constants.PORT_LIST_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.PORT_LIST_FILE_NAME;
        serverYamlFile = (ServerListYamlFile) loadYamlFile(ServerListYamlFile.class, fileName);
        log.trace("Successfully loaded serverYamlFile : {}", serverYamlFile.toString());
    }

    private static void loadVersionInfoYamlFile() {
        String fileName = Constants.COMPONENTS_VERSION_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.COMPONENTS_VERSION_FILE_NAME;
        versionInfoYamlFile = (VersionInfoYamlFile) loadYamlFile(VersionInfoYamlFile.class, fileName);
        log.trace("Successfully loaded versionInfoYamlFile : {}", versionInfoYamlFile.toString());
    }

    private static void loadScriptInfoYamlFile() {
        String fileName = Constants.COMPONENTS_SCRIPT_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.COMPONENTS_SCRIPT_FILE_NAME;
        scriptInfoYamlFile = (ScriptInfoYamlFile) loadYamlFile(ScriptInfoYamlFile.class, fileName);
        log.trace("Successfully loaded scriptInfoYamlFile : {}", scriptInfoYamlFile.toString());
    }

    private static void loadComponentsYamlFile() {
        String fileName = Constants.COMPONENTS_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.COMPONENTS_FILE_NAME;
        componentsYamlFile = (ComponentsYamlFile) loadYamlFile(ComponentsYamlFile.class, fileName);
        log.trace("Successfully loaded ComponentYamlFile : {}", componentsYamlFile.toString());
    }

    private static void loadEntitiesYamlFile() {
        String fileName = Constants.CONFIGURATION_ENTITIES_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.CONFIGURATION_ENTITIES_FILE_NAME;
        entitiesYamlFile = (EntitiesYamlFile) loadYamlFile(EntitiesYamlFile.class, fileName);
        log.trace("Successfully loaded EntitiesYamlFile : {}", entitiesYamlFile.toString());
    }

    private static void loadProcessBlackList() {
        String fileName = Constants.PROCESS_BLACK_LIST_FILE_NAME;
        if (!Arguments.getYamlFilePath().equals(StringUtil.EMPTY_STRING))
            fileName = Arguments.getYamlFilePath() + File.separator + Constants.PROCESS_BLACK_LIST_FILE_NAME;
        blackListedProcesses = (BlackListedProcesses) loadYamlFile(BlackListedProcesses.class, fileName);
        log.trace("Successfully loaded processBlackListYamlFile");
    }

    public static void fixComponentConfigFileNames() {
        List<Component> correctedComponents = new ArrayList<>();
        for (Component component : autoDiscoveryComponents) {
            List<String> correctedPaths = new ArrayList<>();
            if (!(component.getComponentId() == 11)) {
                for (String relativePath : component.getRelativePathList()) {
                    log.info("Relative path for the component {} found in components yaml file is {}", component.getComponentName(), relativePath);
                    if (Utils.isWindows()) {
                        relativePath = relativePath.replace("/", "\\");
                    } else {
                        relativePath = relativePath.replace("\\", "/");
                    }

                    if (!Utils.isWindows() && relativePath.contains(".ini")) {
                        relativePath = relativePath.replace(".ini", ".cnf");
                        correctedPaths.add(relativePath);
                        continue;
                    }
                    if (!Utils.isWindows() && relativePath.contains(".ora")) {
                        relativePath = relativePath.toLowerCase();
                        correctedPaths.add(relativePath);
                        continue;
                    }
                    log.info("Corrected relative path for the component {} found in components yaml file is {}", component.getComponentName(), relativePath);
                    correctedPaths.add(relativePath);
                }
            }
            component.setRelativePathList(correctedPaths);
            correctedComponents.add(component);
        }
        autoDiscoveryComponents = correctedComponents;
        YamlFileLoader.fixVersionPath();
        log.trace("Successfully fixed Component config filename");
    }

    public static void fixConfigEntitiesFileNames() {
        for (Entity entity : configurationEntities) {
            if (entity.getVersion().endsWith(Constants.VERSION_POSTFIX)) {
                entity.setVersion(entity.getVersion().replace(Constants.VERSION_POSTFIX, Constants.MULTIPLE_CHAR_REGEX));
            } else {
                entity.setVersion(entity.getVersion() + Constants.MULTIPLE_CHAR_REGEX);
            }
            for (EntityDetail entityDetail : entity.getEntities()) {

                if (Utils.isWindows()) {
                    entityDetail.setRelativePath(entityDetail.getRelativePath().replace("/", "\\"));
                } else {
                    entityDetail.setRelativePath(entityDetail.getRelativePath().replace("\\", "/"));
                }
                if (!Utils.isWindows() && entityDetail.getRelativePath().contains(".ini")) {
                    entityDetail.setRelativePath(entityDetail.getRelativePath().replace(".ini", ".cnf"));
                    continue;
                }
                if (!Utils.isWindows() && entityDetail.getRelativePath().contains(".ora")) {
                    entityDetail.setRelativePath(entityDetail.getRelativePath().toLowerCase());
                }
            }
        }
        log.trace("Successfully fixed config entity filename");
    }

    public static void fixVersionPath() {
        for (ComponentVersion versionInfo : versionInfoYamlFile.getComponentsVersion()) {
            if (!Utils.isWindows() && versionInfo.getComponentId() == 18 &&
                    versionInfo.getVersionCommands().get(0).getPath().contains(Constants.MYSQLD)) {
                String correctedPath = versionInfo.getVersionCommands().get(0).getPath().replace("bin/", "");
                versionInfo.getVersionCommands().get(0).setPath(correctedPath);
            }
        }
    }

    public static int getDefaultMonitorPortForComponent(int componentId) {
        Component defaultPortComponent = getComponentById(componentId);

        List<Attribute> attributes = new ArrayList<>();

        if (defaultPortComponent != null) {
            attributes = defaultPortComponent.getAttributes();
        }

        int defaultPort = 0;

        for (Attribute attribute : attributes) {

            if (!StringUtils.isEmpty(attribute.getAttributeName()) &&
                    attribute.getIsMandatory() == 1 &&
                    attribute.getAttributeName().equals("MonitorPort")) {

                List<AttributeAccess> attributeAccessList = attribute.getAccess();

                for (AttributeAccess attributeAccess : attributeAccessList) {

                    if (attributeAccess.getPriority() == 1 &&
                            attributeAccess.getDefaultValue() != 0) {

                        defaultPort = attributeAccess.getDefaultValue();
                    }
                }
            }
        }

        return defaultPort;
    }

    public static void init() {
        if (autoDiscoveryComponents == null) {
            if (Arguments.getCcUrl() != null) {
                autoDiscoveryComponents = ControlCenterService.getComponents(Arguments.getCcUrl());
            }
            if (autoDiscoveryComponents == null || autoDiscoveryComponents.size() == 0) {
                log.info("Using backup file {} to retrieve components entities for auto discovery", Constants.COMPONENTS_FILE_NAME);
                autoDiscoveryComponents = getComponents();
            }
            //log.info("init :  components in size :{}, autoDiscoveryComponents :{}", autoDiscoveryComponents.size(), autoDiscoveryComponents.toString());
            YamlFileLoader.fixComponentConfigFileNames();
        }
        if (configurationEntities == null) {
            if (Arguments.getCcUrl() != null) {
                configurationEntities = ControlCenterService.getConfigurationEntities(Arguments.getCcUrl());
            }
            if (configurationEntities == null || configurationEntities.size() == 0) {
                log.info("Using backup file {} to retrieve configuration entities for auto discovery", Constants.CONFIGURATION_ENTITIES_FILE_NAME);
                configurationEntities = getConfigurationEntities();
            }
        }
        YamlFileLoader.fixConfigEntitiesFileNames();
    }

    private static Object loadYamlFile(Class clazz, String fileName) {

        log.info("Inside loadYamlFile with fileName : {}", fileName);
        InputStream fileInputStream = Utils.loadFile(fileName);

        Object loadedObject = null;

        log.debug("Parsing yaml file: {} ", fileName);

        try {
            loadedObject = Utils.getYamlMapper().readValue(fileInputStream, clazz);
        } catch (IOException e) {
            log.info("Error loading yaml file at: {} ", fileName);
        }

        try {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
        } catch (IOException e) {
            log.error("Exception occurred in closing input stream- {} : {} ", e.getMessage(), e);
        }
        return loadedObject;
    }
}