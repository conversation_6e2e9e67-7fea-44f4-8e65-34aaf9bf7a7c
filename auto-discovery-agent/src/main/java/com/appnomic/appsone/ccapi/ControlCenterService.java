package com.appnomic.appsone.ccapi;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.appnomic.appsone.common.beans.discovery.Entity;
import com.appnomic.appsone.common.beans.discovery.GenericResponse;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class ControlCenterService {

    final static Logger log = LoggerFactory.getLogger(ControlCenterService.class);
    private static final ObjectMapper jsonMapper = Utils.getJsonMapper();
    private static boolean isAvailable = false;
    private static List<Component> autoDiscoveryComponents = new ArrayList<>();
    private static List<Entity> configurationEntities = new ArrayList<>();

    public static List<Component> getComponents(String ccUrl) {
        callApi(ccUrl, Constants.ENDPOINT_URL_AUTO_DISCOVERY_COMPONENTS, null);
        return autoDiscoveryComponents;
    }

    public static List<Entity> getConfigurationEntities(String ccUrl) {
        callApi(ccUrl, Constants.ENDPOINT_URL_AUTO_DISCOVERY_CONFIGURATION_ENTITIES, null);
        return configurationEntities;
    }

    public static void pushDiscoveryDataJson(String ccUrl, String discoveryDataJson) {
        callApi(ccUrl, Constants.ENDPOINT_URL_PUSH_DATA, discoveryDataJson);
    }

    private static void callApi(String ccUrl, String apiName, String body) {

        int retryAttempts = Constants.MAX_RETRY_ATTEMPTS;

        while (!(retryAttempts <= 1)) {

            log.debug("Connecting to Control Center. Remaining retry attempts: {} )", retryAttempts - 1);

            try {
                if (!isAvailable) {
                    checkConnectivity(ccUrl);
                }

                if (isAvailable) {
                    if (apiName.equals(Constants.ENDPOINT_URL_PUSH_DATA)) {
                        pushData(ccUrl, body);
                        break;
                    }

                    if (apiName.equals(Constants.ENDPOINT_URL_AUTO_DISCOVERY_COMPONENTS)) {
                        pullAutoDiscoveryComponents(ccUrl);
                        break;
                    }

                    if (apiName.equals(Constants.ENDPOINT_URL_AUTO_DISCOVERY_CONFIGURATION_ENTITIES)) {
                        pullConfigurationEntities(ccUrl);
                        break;
                    }
                }

            } catch (IOException e) {

                log.error("Control Center is not reachable!- {} : {} ", e.getMessage(), e);
                isAvailable = false;

                try {
                    log.debug("Connecting again in 5 Seconds...");
                    Thread.sleep(5 * 1000);
                } catch (InterruptedException ie) {
                    log.error("Auto Discovery Agent Thread interrupted while waiting for reconnection- {} : {} ", ie.getMessage(), ie);
                }
            }

            retryAttempts = retryAttempts - 1;
        }
    }

    private static void pushData(String ccUrl, String discoveryDataJson) throws IOException {

        String finalUrl = ccUrl + Constants.ENDPOINT_URL_PREFIX + Constants.ENDPOINT_URL_PUSH_DATA;
        log.info("Pushing discovery data to CC URL - {}", finalUrl);

        RequestBody body = RequestBody.create(MediaType.parse("application/json"), discoveryDataJson);
        try {
            Response response = sendHttpRequest(finalUrl, "POST", body);
            if (response.code() == Constants.SUCCESS) {
                GenericResponse<String> data = jsonMapper.readValue(Objects.requireNonNull(response.body()).string(), new TypeReference<GenericResponse<String>>() {
                });
                log.info("Push discovery data CC API Successful! - {} ", data);
            } else {
                log.error("Push discovery data CC API Failed! - {} ", Objects.requireNonNull(response.body()).string());
            }
        } catch (IOException e) {
            log.error("Unable to push discovery data to Control Center- {} : {} ", e.getMessage(), e);
            throw new IOException(e.getMessage());
        }
    }

    private static void pullAutoDiscoveryComponents(String ccUrl) throws IOException {

        String finalUrl = ccUrl + Constants.ENDPOINT_URL_PREFIX + Constants.ENDPOINT_URL_AUTO_DISCOVERY_COMPONENTS;
        log.info("Retrieving auto discovery components from CC URL - {}", finalUrl);

        try {
            Response response = sendHttpRequest(finalUrl, "GET", null);
            if (response.code() == Constants.SUCCESS) {

                String responseBody = Objects.requireNonNull(response.body()).string();
                log.info("auto discovery CC API response - {} ", responseBody);

                GenericResponse<List<Component>> data = jsonMapper.readValue(responseBody, new TypeReference<GenericResponse<List<Component>>>() {
                });

                autoDiscoveryComponents = data.getData();

                if (data.getData() != null && data.getData().size() != 0) {
                    log.info("Successfully retrieved {} auto discovery components from Control Center", data.getData().size());
                }
            } else {
                log.error("Unable to retrieve auto discovery components from Control Center. API response - {} ", Objects.requireNonNull(response.body()).string());
            }
        } catch (IOException e) {
            log.error("Error retrieving auto discovery components from Control Center - {} : {} ", e.getMessage(), e);
            throw new IOException(e.getMessage());
        } catch (Exception exception) {
            log.error("Error retrieving auto discovery components from Control Center - {} : {} ", exception.getMessage(), exception);
        }
    }

    private static void pullConfigurationEntities(String ccUrl) throws IOException {

        String finalUrl = ccUrl + Constants.ENDPOINT_URL_PREFIX + Constants.ENDPOINT_URL_AUTO_DISCOVERY_CONFIGURATION_ENTITIES;
        log.info("Retrieving auto discovery - Configuration Entities from CC URL - {}", finalUrl);

        try {
            Response response = sendHttpRequest(finalUrl, "GET", null);
            if (response.code() == Constants.SUCCESS) {

                String responseBody = Objects.requireNonNull(response.body()).string();
                log.info("auto discovery CC API response - {} ", responseBody);

                GenericResponse<List<Entity>> data = jsonMapper.readValue(responseBody, new TypeReference<GenericResponse<List<Entity>>>() {
                });

                configurationEntities = data.getData();

                if (data.getData() != null && data.getData().size() != 0) {
                    log.info("Successfully retrieved {} auto discovery configuration entities from Control Center", data.getData().size());
                }
            } else {
                log.error("Unable to retrieve auto discovery configuration entities from Control Center. API response - {} ", Objects.requireNonNull(response.body()).string());
            }
        } catch (IOException e) {
            log.error("Error retrieving auto discovery configuration entities from Control Center - {} : {} ", e.getMessage(), e);
            throw new IOException(e.getMessage());
        } catch (Exception exception) {
            log.error("Error retrieving auto discovery configuration entities from Control Center - {} : {} ", exception.getMessage(), exception);
        }
    }

    private static void checkConnectivity(String ccUrl) throws IOException {
        String finalUrl = ccUrl + Constants.ENDPOINT_URL_PREFIX + Constants.ENDPOINT_URL_KEYCLOAK_SETTINGS;
        log.info("Checking connectivity to Control Center - {}", finalUrl);
        try {
            Response response = sendHttpRequest(finalUrl, "GET", null);

            if (response.code() == Constants.SUCCESS) {
                isAvailable = true;
                log.info("Successfully connected to Control Center: {} ", Objects.requireNonNull(response.body()).string());
            } else {
                isAvailable = false;
                log.error("Unable to connect to Control Center: {} ", Objects.requireNonNull(response.body()).string());
            }
        } catch (IOException e) {
            isAvailable = false;
            log.error("Error connecting to Control Center- {} : {} ", e.getMessage(), e);
            throw new IOException(e.getMessage());
        }
    }

    private static Response sendHttpRequest(String url, String method, RequestBody body) throws IOException {
        Request request = new Request.Builder()
                .url(url)
                .method(method, body).build();
        Response response;

        try {
            OkHttpClient.Builder builder = new OkHttpClient.Builder();

            builder.connectTimeout(Constants.CONNECT_TIMEOUT_DEFAULT, TimeUnit.MINUTES);
            builder.readTimeout(Constants.READ_TIMEOUT_DEFAULT, TimeUnit.MINUTES);
            builder.writeTimeout(Constants.WRITE_TIMEOUT_DEFAULT, TimeUnit.MINUTES);

            OkHttpClient client = builder.build();
            response = client.newCall(request).execute();

        } catch (IOException e) {
            log.error("Error connecting to control center- {} : {} ", e.getMessage(), e);
            throw new IOException(e.getMessage());
        }
        return response;
    }
}
