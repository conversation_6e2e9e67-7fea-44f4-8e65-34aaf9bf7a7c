package com.appnomic.appsone.ignorable;

import com.appnomic.appsone.common.beans.discovery.Prerequisite;
import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.fasterxml.jackson.annotation.JsonIgnore;

public interface Process {

    @JsonIgnore
    abstract String getAddToSystemErrorMessage();

    @JsonIgnore
    abstract String getComponentInstanceSDMIdentifier();

    @JsonIgnore
    abstract String getHostIdentifier();

    @JsonIgnore
    abstract long getLastUpdatedTime();

    @JsonIgnore
    abstract DiscoveryStatus getDiscoveryStatus();

    @JsonIgnore
    abstract int getIsBlacklisted();

    @JsonIgnore
    abstract int getIsIgnored();

    @JsonIgnore
    abstract String getIgnoredBy();

    @JsonIgnore
    abstract Prerequisite getPreRequisitesFound();

}
