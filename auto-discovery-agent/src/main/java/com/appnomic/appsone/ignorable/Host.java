package com.appnomic.appsone.ignorable;

import com.appnomic.appsone.common.beans.discovery.Prerequisite;
import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.List;

public interface Host {

    @JsonIgnore
    abstract long getLastUpdatedTime();

    @JsonIgnore
    abstract int getEnvironment();

    @JsonIgnore
    abstract String getAddToSystemErrorMessage();

    @JsonIgnore
    abstract String getHostInstanceSDMIdentifier();

    @JsonIgnore
    abstract List<String> getAvailableCommandsInPath();

    @JsonIgnore
    abstract int getIsIgnored();

    @JsonIgnore
    abstract String getIgnoredBy();

    @JsonIgnore
    abstract Prerequisite getPreRequisitesFound();

    @JsonIgnore
    abstract String getScriptPath();
}
