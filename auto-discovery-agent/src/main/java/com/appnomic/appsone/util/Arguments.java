package com.appnomic.appsone.util;

import io.netty.util.internal.StringUtil;

public class Arguments {
    private static String yamlFilePath = StringUtil.EMPTY_STRING;
    private static String shellPath = StringUtil.EMPTY_STRING;

    private static boolean readHttpdConfigSubFiles = false;

    private static String ccUrl = null;
    private static String finacleVersion = "10";

    private static String OS = "linux";

    public static String getYamlFilePath() {
        return yamlFilePath;
    }

    public static void setYamlFilePath(String yamlFilePath) {
        Arguments.yamlFilePath = yamlFilePath;
    }

    public static String getShellPath() {
        return shellPath;
    }

    public static void setShellPath(String shellPath) {
        Arguments.shellPath = shellPath;
    }

    public static boolean getReadHttpdConfigSubFiles() { return  readHttpdConfigSubFiles; }

    public static void setReadHttpdConfigSubFiles(boolean readHttpdConfigSubFiles) { Arguments.readHttpdConfigSubFiles = readHttpdConfigSubFiles; }

    public static String getFinacleVersion() {
        return finacleVersion;
    }

    public static String getOS() {
        return OS;
    }

    public static void setOS(String OS) {
        Arguments.OS = OS;
    }

    public static void setFinacleVersion(String finacleVersion) {
        Arguments.finacleVersion = finacleVersion;
    }

    public static String getCcUrl() {
        return ccUrl;
    }

    public static void setCcUrl(String ccUrl) {
        Arguments.ccUrl = ccUrl;
    }
}
