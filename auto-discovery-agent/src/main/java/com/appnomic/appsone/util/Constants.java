package com.appnomic.appsone.util;


public class Constants {
    public static final String DATE_TIME_PATTERN = "yyyy-MM-dd-HH-mm-ss";

    public static final String OS_NAME_SYS_KEY = "os.name";

    public static final String OS_VERSION_SYS_KEY = "os.version";
    public static final String IP_V4_ANY_PATTERN = "*";

    public static final String NEXT_LINE = "\n";

    public static final String MULTIPLE_SPACE_PATTERN = "[ \t]+";

    public static final String IP_V6_SUBNET = "::ffff:";

    public static final String VIRTUAL = "virtual";
    public static final String VIRTUAL_NET_MASK = "***************";

    public static final String AIX_PLATFORM = "AIX";
    public static final String SUN_OS_PLATFORM = "SunOS";
    public static final String SIGAR = "Sigar";
    public static final String KPI_FILE_WATCH = "FILE_WATCH";
    public static final String KPI_KEY_VALUE = "KEY_VALUE";
    public static final String FILE_WATCH = "FileWatch";
    public static final String KEY_VALUE = "KeyValue";

    public static final String WINDOWS = "Windows";
    public static final String HOST_ADDRESS = "HostAddress";
    public static final String MONITOR_PORT = "MonitorPort";

    public static final String INSTALLATION_PATH = "INSTALLATION_PATH";

    public static  final String PROTOCOL = "Protocol";

    public static final String BIN = "bin";
    public static final String SBIN = "sbin";
    public static final String IIS = "inetsrv";
    public static final String MYSQLD = "mysqld";
    public static final String DATA_DIR = "Data";
    public static final String JBOSS_HOME = "jboss.home.dir";
    public static final String ORACLE_DB_HOME = "dbhome";
    public static final String LIB = "lib";
    public static final String SQL_CONFIG_FILE_LOCATION = "--defaults-file";
    public static final String JBOSS_PORT = "jboss.https.port";
    public static final String CENT_OS = "CentOS";

    public static final String RHEL_OS = "Red Hat Enterprise Linux";
    public static final String RPM_QUERY = "rpm -ql ";

    public static final String DEFAULT_SHELL_PATH = "/bin/bash";
    public static final String UBUNTU_OS = "Ubuntu";

    public static final String DPKG_QUERY = "dpkg -L $package | grep $entity";
    public static final String LOCATE_QUERY = "locate $entity";

    public static final String MYSQL_DEFAULT_CONFIG_LOC_LINUX_1 = "/etc/my.cnf";
    public static final String MYSQL_DEFAULT_CONFIG_LOC_LINUX_2 = "/etc/mysql/my.cnf";
    public static final String MYSQL_DEFAULT_CONFIG_LOC_LINUX_3 = "/usr/etc/my.cnf";
    public static final String MYSQL_DEFAULT_CONFIG_LOC_LINUX_4 = "~/.my.cnf";

    public static final String ENDPOINT_URL_PUSH_DATA = "/push-discovery-data";
    public static final String ENDPOINT_URL_KEYCLOAK_SETTINGS = "/keycloak-settings";
    public static final String ENDPOINT_URL_AUTO_DISCOVERY_COMPONENTS = "/auto-discovery-components";
    public static final String ENDPOINT_URL_AUTO_DISCOVERY_CONFIGURATION_ENTITIES = "/auto-discovery-configuration-entities";
    public static final String ENDPOINT_URL_PREFIX = "/v2.0/api";
    public static final int CONNECT_TIMEOUT_DEFAULT = 2;
    public static final int READ_TIMEOUT_DEFAULT = 2;
    public static final int WRITE_TIMEOUT_DEFAULT = 2;
    public static final int MAX_RETRY_ATTEMPTS = 2;
    public static final int SUCCESS = 200;

    public static final String COMPONENTS_FILE_NAME = "components.yaml";
    public static final String COMPONENTS_VERSION_FILE_NAME = "versions.yaml";
    public static final String CONFIGURATION_ENTITIES_FILE_NAME = "configuration-entities.yaml";
    public static final String PORT_LIST_FILE_NAME = "ports.yaml";
    public static final String COMPONENTS_SCRIPT_FILE_NAME = "scripts.yaml";

    public static final String PROCESS_BLACK_LIST_FILE_NAME = "process-black-list.yaml";

    public static final String SCRIPT_PREFIX_SOLARIS = "Solaris_";
    public static final String SCRIPT_PREFIX_AIX = "Aix_";
    public static final String SCRIPT_PREFIX_WINDOWS = "Windows_";
    public static final String SCRIPT_PREFIX_LINUX = "Linux_";

    public static final String SCRIPT_SUFFIX_VALIDATE = "_Prerequiste_Validate";
    public static final String SCRIPT_SUFFIX_ENABLE = "_Prerequiste_Enable";

    public static final String DEFAULT_SSH_CONFIG_LINUX = "/etc/ssh/sshd_config";

    public static final String DEFAULT_SSH_HOME_LINUX = "/etc/ssh";

    public static final String DEFAULT_SSH_PORT = "22";

    public static final String DEFAULT_MYSQL_PORT = "3306";

    public static final String DEFAULT_KAFKA_PORT = "9092";

    public static final String DEFAULT_SSH_CONFIG_WINDOWS = "C:\\Windows\\System32\\OpenSSH\\sshd_config_default";

    public static final String DEFAULT_SSH_HOME_WINDOWS = "C:\\Windows\\System32\\OpenSSH";


    /*
     *
     * Constant related to Apache Configuration Parser
     * */

    public final static String newLine = System.getProperty("line.separator");

    //Selects spaces before or after a comma
    public final static String replaceCommaSpacesRegex = "(\\s*,\\s*)";
    //Selects any space that is not between a quotation
    public final static String replaceSpacesInValuesRegex = "\\s+(?=([^\"']*[\"'][^\"']*[\"'])*[^\"']*$)";
    // Constants to search for apache directives
    public final static String defineDirective = "Define";

    public final static String FINACLE_LI_SERVER_ATTRIBUTE_NAME="LiServer";

    public final static String FINACLE_MARIA_SERVER_ATTRIBUTE_NAME="MariaServer";
    public final static String FINACLE_LIMO_SERVER_ATTRIBUTE_NAME="LimoServer";

    public final static String FINACLE_BCMON_SERVER_ATTRIBUTE_NAME="LimoServerBCMon";

    public final static String FINACLE_BCECHO_SERVER_ATTRIBUTE_NAME="LimoServerBCEcho";
    public final static String FINACLE_BCMON_SERVER_SEARCH_KEYWORD="bc-mon";

    public final static String FINACLE_BCECHO_SERVER_SEARCH_KEYWORD="bc-echo";

    public final static String FINACLE_ECHO_SERVER_SEARCH_KEYWORD="echo";

    public final static String FINACLE_COMPONENT_NAME = "FinacleServices";

    public final static String FINACLE_PARSER_NAME = "FINACLE_PARSER";

    public final static String FINACLE_LISRVR_ATTRIBUTE_VALUE = "lisrvr";

    public final static String FINACLE_MARIA_ATTRIBUTE_VALUE = "maria";

    public final static String FINACLE_BCMON_ATTRIBUTE_VALUE = "bc-mon";

    public final static String FINACLE_BCECHO_ATTRIBUTE_VALUE = "bc-echo";
    public final static String WEBSPHERE_COMPONENT_NAME = "WebSphere";

    public final static String IBM_MQ_COMPONENT_NAME = "IBM MQ";
    public final static String IBM_MQ_ATTRIBUTE_REGEX = "((-m)[ ]{0,}(?<queuemanager>[0-9a-zA-Z.]+))";
    public final static String IBM_MQ_QUEUE_MANAGER_COMMAND = "dspmq -a";
    public final static String IBM_MQ_QUEUE_MANAGER_REGEX= "(?=(QMNAME\\([0-9a-zA-Z.]*\\)))";

    public final static String IBM_MQ_QUEUE_MANAGER_SPLIT_PATTERN = "QMNAME";
    public final static String IBM_MQ_QUEUE_MANAGER_ATTRIBUTE_NAME = "QueueManager";
    public final static String APACHE_TOMCAT_COMPONENT_NAME = "Apache Tomcat";
    public final static String AUTODISCOVERY_USER_COMMAND = "ps -ef | grep auto-discovery-agent";

    public final static String LIST_ALL_PROCESS_COMMAND = "ps -eo pid,ppid,args";

    public final static String TOMCAT_ARGUMENT = "catalina.home";

    public final static String IBM_ARGUMENT = "ibm";

    public final static String HTTPD_TYPE_COMMAND = "-v |head -1 | cut -d ':' -f2 | cut -d '/' -f1";

    public final static int APACHE_HTTPD_COMPONENT_ID = 7;

    public final static int IHS_COMPONENT_ID = 9;

    public final static int IHS_COMPONENT_TYPE_ID = 2;

    public final static int JBOSS_COMPONENT_ID = 14;

    public final static int NGINX_COMPONENT_ID = 47;

    public final static int MYSQL_COMPONENT_ID = 18;

    public final static int MSSQL_COMPONENT_ID = 21;

    public final static int IIS_COMPONENT_ID = 8;

    public final static String DEFAULT_ATTRIBUTE_VALUE= "na";

    public final static String APACHE_LISTEN_ATTRIBUTE_VALUE = "Listen";

    public final static String PORT_ATTRIBUTE_VALUE = "port";

    public final static String NODEJS_PORT_ATTRIBUTE_VALUE = ".listen(";
    public static final String PROCESS_SPLIT_PATTERN = "\\s+";

    public final static int IBM_COMPONENT_ID = 26;

    public final static String WINDOWS_OS = "Windows";

    public final static String ARGUMENT_F_VALUE_PATTERN = "-f .[a-zA-Z\\/\\_0-9\\.]+";

    public final static String HTTPD_COMMON_COMPONENT_NAME = "http";

    public final static int WEBSPHERE_COMPONENT_ID = 15;

    public final static String SPACE_DELIMITER = " ";

    public final static String EQUAL_DELIMITER = "=";

    public final static String WEBSPHERE_VERSION_LOOKUP = "WebSphere Application Server";

    public final static String VERSION_KEYWORD = "version";

    public final static String FILE_ARGUMENT = "-f";

    public final static String HTTPD_CONFIG_FILE_COMMAND = " -V | grep SERVER_CONFIG_FILE | cut -d '=' -f 2";

    public final static  String HTTPD_VERSION_COMMAND_TYPE = "apachectl";

    public final static String VERSION_POSTFIX = ".x";
    public final static String MULTIPLE_CHAR_REGEX = ".*";

    public final static String OHS_ARGUMENT = "ohs";

    public final static String HTTPS_PROTOCOL_CHECK = "Server Certificate";

    public final static String HTTP_PROTOCOL = "HTTP";

    public final static String HTTPS_PROTOCOL = "HTTPS";

    public final static String CAT_COMMAND = "cat ";

    public final static String TEXT_FILE = ".txt";

    public final static String SPACE = " ";


    public final static String COMMENT_PREFIX_ARGUMENT = "#";
}
