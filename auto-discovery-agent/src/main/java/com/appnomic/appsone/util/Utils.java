package com.appnomic.appsone.util;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.fasterxml.jackson.core.util.DefaultIndenter;
import com.fasterxml.jackson.core.util.DefaultPrettyPrinter;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.hyperic.sigar.NetFlags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.XMLConstants;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.parsers.ParserConfigurationException;
import java.io.*;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class Utils {

    private static final Logger logger = LoggerFactory.getLogger(Utils.class);

    public static ObjectMapper getJsonMapper() {
        ObjectMapper objectMapper = new ObjectMapper();

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }

    public static ObjectMapper getYamlMapper() {

        ObjectMapper objectMapper = new ObjectMapper(new YAMLFactory());

        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        return objectMapper;
    }

    public static String getTimeInGMT(long time) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat(Constants.DATE_TIME_PATTERN);
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT"));
        return simpleDateFormat.format(time);
    }

    public static String jsonify(Object data) throws Exception {

        DefaultPrettyPrinter defaultPrettyPrinter = new DefaultPrettyPrinter();
        defaultPrettyPrinter.indentArraysWith(DefaultIndenter.SYSTEM_LINEFEED_INSTANCE);
        defaultPrettyPrinter.indentObjectsWith(DefaultIndenter.SYSTEM_LINEFEED_INSTANCE);

        Map<Class<?>, Class<?>> ignorable = new HashMap<>();
        ignorable.put(Connection.class, com.appnomic.appsone.ignorable.Connection.class);
        ignorable.put(Endpoint.class, com.appnomic.appsone.ignorable.Endpoint.class);
        ignorable.put(Host.class, com.appnomic.appsone.ignorable.Host.class);
        ignorable.put(MountPoint.class, com.appnomic.appsone.ignorable.MountPoint.class);
        ignorable.put(NetworkInterface.class, com.appnomic.appsone.ignorable.NetworkInterface.class);
        ignorable.put(Process.class, com.appnomic.appsone.ignorable.Process.class);
        ignorable.put(Component.class, com.appnomic.appsone.ignorable.Component.class);
        ignorable.put(Agents.class, com.appnomic.appsone.ignorable.Agents.class);

        return getJsonMapper()
                .setMixIns(ignorable)
                .writer()
                .with(defaultPrettyPrinter)
                .writeValueAsString(data);
    }

    public static String removeNodeFromJson(String discoveryDataJson, String nodeName) {
        JsonNode discoDataJson = null;
        try {
            discoDataJson = Utils.getJsonMapper().readTree(discoveryDataJson);
            JsonNode rootNode = discoDataJson.findParent(nodeName);
            ((ObjectNode) rootNode).remove(nodeName);
            discoveryDataJson = Utils.jsonify(discoDataJson);
        } catch (IOException e) {
            logger.error("Exception removing node {} from json- {} : {} ", nodeName, e.getMessage(), e);
        } catch (Exception e) {
            logger.error("Exception converting to json- {} : {} ", e.getMessage(), e);
        }
        return discoveryDataJson;
    }

    public static void getNetstatResults(NetStatOutput netStatOutput, String shellCmdPath, boolean procExc) {
        try {
            java.lang.Process process;
            String[] netstatLines;
            List<DiscoveryError> discoveryErrors = new ArrayList<>();
            if (procExc) {
                process = new ProcessBuilder(shellCmdPath, "-c", netStatOutput.getBashCommand()).start();
                String stdout = IOUtils.toString(process.getInputStream(), StandardCharsets.UTF_8);
                netstatLines = stdout.split("\n");
                netStatOutput.setRawNetStatLines(Arrays.asList(netstatLines));
                if (netstatLines.length == 0) {
                    String stderr = IOUtils.toString(process.getErrorStream(), StandardCharsets.UTF_8);
                    logger.error("Nothing discovered via netstat : {}", stderr);
                    discoveryErrors.add(DiscoveryError.fromError("Nothing discovered via netstat : " + stderr));
                }
            }
            Pattern netstatPattern = Pattern.compile(netStatOutput.getRegexPattern());
            List<Matcher> matchedLines = new ArrayList<>();
            for (String netstatLine : netStatOutput.getRawNetStatLines()) {
                Matcher matcher = netstatPattern.matcher(netstatLine);
                if (matcher.find()) {
                    matchedLines.add(matcher);
                } else {
                    logger.error("Line {} did not match pattern", netstatLine);
                    discoveryErrors.add(DiscoveryError.fromError("Line did not match pattern: " + netstatLine));
                }
            }
            netStatOutput.setDiscoveryErrors(discoveryErrors);
            netStatOutput.setMatchedLines(matchedLines);
        } catch (IOException e) {
            logger.error("IOException for Netstat Discovery {} : {}", e.getMessage(), e);
        }
    }

    public static String writeFileToDisk(String directory, String nodeId, String discoveryDataJson) throws Exception {
        String timestamp = Utils.getTimeInGMT(System.currentTimeMillis());
        StringBuffer filename = new StringBuffer();
        filename.append(directory).append(File.separator);
        if (!StringUtils.isEmpty(nodeId)) {
            filename.append(nodeId);
        }
        filename.append(timestamp).append(".json");
        try {
            File file = new File(filename.toString());
            Writer w = new OutputStreamWriter(new FileOutputStream(file), StandardCharsets.UTF_8);
            PrintWriter pw = new PrintWriter(w);
            pw.println(discoveryDataJson);
            pw.close();
            logger.info("Auto discovery json file successfully written to disk file {}", filename);
        } catch (Exception e) {
            logger.error("Error creating json file {} : {}" + e.getMessage(), e);
            throw new Exception("Error creating json file {}: " + filename);
        }
        return filename.toString();
    }

    public static String getHostAddress(List<NetworkInterface> networkInterfaces) {
        String hostAddress = StringUtils.EMPTY;
        for (NetworkInterface networkInterface : networkInterfaces) {
            if (!NetFlags.isAnyAddress(networkInterface.getInterfaceIP()) &&
                    !NetFlags.isLoopback(networkInterface.getInterfaceIP()) &&
                    !isVirtualInterface(networkInterface)) {
                hostAddress = networkInterface.getInterfaceIP();
                break;
            }
        }
        return hostAddress;
    }

    public static boolean isVirtualInterface(NetworkInterface networkInterface) {
        boolean virtualMask = false;
        boolean virtualDescription = false;
        if (!StringUtils.isEmpty(networkInterface.getInterfaceMask())) {
            if (Constants.VIRTUAL_NET_MASK.equals(networkInterface.getInterfaceMask())) {
                virtualMask = true;
            }
        }
        if (!StringUtils.isEmpty(networkInterface.getInterfaceDescription())) {
            if (networkInterface.getInterfaceDescription().toLowerCase().contains(Constants.VIRTUAL)) {
                virtualDescription = true;
            }
        }
        return virtualMask | virtualDescription;
    }

    public static String getScriptPrefix(String operatingSystem) {
        String prefix = StringUtils.EMPTY;
        if (operatingSystem.toLowerCase().contains(Constants.SUN_OS_PLATFORM.toLowerCase())) {
            prefix = Constants.SCRIPT_PREFIX_SOLARIS;
        } else if (operatingSystem.toLowerCase().contains(Constants.AIX_PLATFORM.toLowerCase())) {
            prefix = Constants.SCRIPT_PREFIX_AIX;
        } else if (operatingSystem.toLowerCase().contains(Constants.WINDOWS.toLowerCase())) {
            prefix = Constants.SCRIPT_PREFIX_WINDOWS;
        } else {
            prefix = Constants.SCRIPT_PREFIX_LINUX;
        }
        return prefix;
    }

    public static String getScriptSuffix(boolean isValidateScript) {
        String suffix = StringUtils.EMPTY;
        suffix = Constants.SCRIPT_SUFFIX_ENABLE;
        if (isValidateScript) {
            suffix = Constants.SCRIPT_SUFFIX_VALIDATE;
        }
        return suffix;
    }

    public static int executeCommand(String command, boolean deadlockFlag) {
        String commandOutput = StringUtils.EMPTY;
        int ret = -1;
        if (!StringUtils.isEmpty(command)) {
            try {
                java.lang.Process process;
                if (Utils.isWindows()) {
                    process = new ProcessBuilder().command("cmd.exe", "/c", command).start();
                    if (deadlockFlag) {
                        commandOutput = preventPossibleDeadlock(process);
                    }
                } else {
                    process = new ProcessBuilder(Arguments.getShellPath(), "-c", command).start();
                }
                if (process.getInputStream() != null) {
                    commandOutput = IOUtils.toString(process.getInputStream(), StandardCharsets.UTF_8).trim();
                    logger.info("Command: {} Output: {} ", command, commandOutput);
                }
                if (StringUtils.isEmpty(commandOutput)) {
                    String stderr = IOUtils.toString(process.getErrorStream(), StandardCharsets.UTF_8);
                    logger.error("Error: {} running command: {} ", stderr, command);
                }
                ret = process.exitValue();
            } catch (IOException e) {
                logger.error(" {} while running command : {}",command , e);
            }
        } else {
            logger.error("Unable to execute command: {} - should not be empty!", command);
        }
        return ret;
    }

    public static String getCommandOutput(String command, boolean deadlockFlag, boolean redirectErrors) {
        String commandOutput = StringUtils.EMPTY;
        if (!StringUtils.isEmpty(command)) {
            try {
                java.lang.Process process;
                ProcessBuilder processBuilder;
                if (Utils.isWindows()) {
                    logger.info("Trying to run the windows command  : {} ",command);
                    processBuilder = new ProcessBuilder().command("cmd.exe", "/c", command);
                    if (redirectErrors) {
                        processBuilder = processBuilder.redirectErrorStream(true);
                        logger.info("redirecting error stream: {} ", processBuilder.redirectErrorStream());
                    }
                    process = processBuilder.start();
                    if (deadlockFlag) {
                        commandOutput = preventPossibleDeadlock(process);
                    }
                } else {
                    logger.info("Trying to run the shellPath : {} , command : {} ",Arguments.getShellPath(), command);
                    processBuilder = new ProcessBuilder(Arguments.getShellPath(), "-c", command);
                    if (redirectErrors) {
                        processBuilder = processBuilder.redirectErrorStream(true);
                        logger.info("redirecting error stream: {} ", processBuilder.redirectErrorStream());
                    }
                    process = processBuilder.start();
                }
                if (process.getInputStream() != null) {
                    commandOutput = IOUtils.toString(process.getInputStream(), StandardCharsets.UTF_8).trim();
                    logger.info("Command: {} Output: {} ", command, commandOutput);
                }
                if (StringUtils.isEmpty(commandOutput)) {
                    String stderr = IOUtils.toString(process.getErrorStream(), StandardCharsets.UTF_8);
                    logger.error("Error: {} running command: {} ", stderr, command);
                }
            } catch (IOException e) {
                logger.error(" {} while running command : {}", command, e);
            }
        } else {
            logger.error("Unable to execute command: {} - should not be empty!", command);
        }
        return commandOutput;
    }

    private static String preventPossibleDeadlock(java.lang.Process externalSubProcess) throws IOException {

        final BufferedReader inputStreamReader = new BufferedReader(new InputStreamReader(externalSubProcess.getInputStream(), StandardCharsets.UTF_8));
        final BufferedWriter outputStreamReader = new BufferedWriter(new OutputStreamWriter(externalSubProcess.getOutputStream(), StandardCharsets.UTF_8));

        String line = StringUtils.EMPTY;

        try {
            while ((line = inputStreamReader.readLine()) != null) {
                logger.info(" {} ", line);
                if (line.contains(".Final") && line.contains("WildFly") || line.contains("SVNTag")) {
                    logger.info("version line :  {} ", line);
                    try {
                        logger.info("Waiting for 5 seconds for sub process to exit:  ");
                        Thread.sleep(5 * 1000);
                    } catch (InterruptedException e) {
                        logger.error("Thread interrupted while waiting: {} - {} ", e.getMessage(), e);
                    }
                    logger.info("Sub Process Timeout Out");
                    externalSubProcess.destroy();
                    break;
                }
            }
        } catch (IOException e) {
            logger.error("Error reading from sub process input stream: {} - {} ", externalSubProcess.toString(), e);
        }
        inputStreamReader.close();
        outputStreamReader.close();
        try {
            externalSubProcess.waitFor();
        } catch (InterruptedException e) {
            logger.error("Process waitFor failed: {} ", e);
        }
        return line;
    }

    public static Server extractServerDetails(String argName, String argVal) {
        Server server = new Server();
        if (!StringUtils.isEmpty(argVal)) {
            if (argVal.contains(":")) {
                String splitArg[] = argVal.split(":");
                server.setServerName(argName);
                server.setAddress(splitArg[0].trim());
                server.setPortNumber(Integer.parseInt(splitArg[1].trim()));
                return server;
            } else {
                logger.error("Invalid Server Details {} - {} ", argName, argVal);
            }
        }
        return null;
    }

    public static List<String> extractLocations(String argVal) {
        List<String> locations = new ArrayList<>();
        if (!StringUtils.isEmpty(argVal)) {
            if (argVal.contains(";")) {
                String splitArg[] = argVal.split(";");
                for (int i = 0; i < splitArg.length; i++) {
                    locations.add(splitArg[i].trim());
                }
            } else {
                locations.add(argVal.trim());
            }
            return locations;
        }
        return null;
    }

    public static InputStream loadFile(String fileName) {
        InputStream fileInputStream = null;
        try {
            File file = new File(fileName);
            if (file.exists()) {
                fileInputStream = new FileInputStream(fileName);
            } else {
                fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(fileName);
            }
        } catch (IOException e) {
            logger.error("Error loading file at: {} ", fileName, e);
        }
        return fileInputStream;
    }

    public static List<Server> checkPortAvailability(List<Server> serverList) {
        List<Server> serversAvailable = new ArrayList<>();

        for (Server server : serverList) {
            if (NetFlags.isLoopback(server.getAddress())) {
                server = checkPortAvailabilityLocally(server);
            } else {
                server = checkPortConnectivityToRemote(server);
            }
            serversAvailable.add(server);
        }
        return serversAvailable;
    }

    private static Server checkPortAvailabilityLocally(Server server) {
        int portNumber = server.getPortNumber();
        ServerSocket socket;
        try {
            socket = new ServerSocket();
            socket.setReuseAddress(false);
            socket.bind(new InetSocketAddress(InetAddress.getByName("localhost"), portNumber), 1);
            logger.info("Local Port: {}  is free!", portNumber);
            server.setAvailable(true);
        } catch (IOException e) {
            logger.error("Local Port: {} is NOT free- {} : {} ", portNumber, e.getMessage(), e);
            server.setAvailable(false);
        }
        return server;
    }

    private static Server checkPortConnectivityToRemote(Server server) {

        int portNumber = server.getPortNumber();
        String remoteServer = server.getAddress();
        Socket socket;
        try {
            socket = new Socket(remoteServer, portNumber);
            boolean connected = socket.isConnected();
            if (connected) {
                logger.info("Successfully connected to remote port: {} on server {} ", portNumber, remoteServer);
                server.setAvailable(true);
            } else {
                logger.info("Not able to connect to remote port: {} on server {} - {} :{} ", portNumber, remoteServer);
                server.setAvailable(false);
            }
        } catch (Exception e) {
            logger.error("Unable to connect to remote port: {} on server {} - {} :{} ", portNumber, remoteServer, e.getMessage(), e);
            server.setAvailable(false);
        }
        return server;
    }

    public static DocumentBuilder getSecureDocumentBuilder() {

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        try {
            factory.setNamespaceAware(true);
            factory.setFeature(XMLConstants.FEATURE_SECURE_PROCESSING, true);
            factory.setAttribute(XMLConstants.ACCESS_EXTERNAL_DTD, "");
            return factory.newDocumentBuilder();
        } catch (ParserConfigurationException e) {
            logger.error("Exception building instance of document builder {} : {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * Replaces all tabs and multiple spaces with a single space
     *
     * @param line the line to sanitize
     * @return the sanitized line
     */
    public static String sanitizeLineSpaces(String line) {
        return line.replaceAll("\\s+", " ").trim();
    }

    /**
     * @return true if the OS is Windows, false otherwise
     */
    public static boolean isWindows() {
        return (System.getProperty("os.name").toLowerCase().contains("win"));
    }
}


