package com.appnomic.appsone.util;

import org.hyperic.sigar.Sigar;
import org.hyperic.sigar.SigarLoader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.util.Collection;
import java.util.Objects;

public class SigarProvisioner {

    /**
     * The Constant EOF.
     */
    static final int EOF = -1;
    /**
     * The Constant SIZE.
     */
    static final int SIZE = 64 * 1024;
    /**
     * Provisioner logger.
     */
    private static final Logger logger = LoggerFactory.getLogger(SigarProvisioner.class);

    /**
     * Verify if sigar native library is loaded and operational.
     *
     * @return true, if is native library loaded.
     */
    public static synchronized boolean isNativeLoaded() {
        try {
            return isSigarAlreadyLoaded();
        } catch (final Throwable e) {
            try {
                final Sigar sigar = new Sigar();
                new Sigar();
                sigar.getPid();
                sigar.close();
                return true;
            } catch (final Throwable ex) {
                return false;
            }
        }
    }

    /**
     * Extract and load native sigar library in the provided folder.
     * <p>
     * Library extraction folder.
     *
     * @throws Exception The provisioning failure exception.
     */
    public static synchronized void provision()
            throws Exception {

        if (isNativeLoaded()) {
            logger.info("Sigar library is already provisioned.");
            return;
        }


        final SigarLoader sigarLoader = new SigarLoader(Sigar.class);

        /* Library name for given architecture. */
        final String libraryName = sigarLoader.getLibraryName();

        /* Absolute path to the extracted library the on file system. */
        final File targetPath = new File(System.getProperty("user.dir") + File.separator + libraryName).getAbsoluteFile();

        /* Extract library form the jar to the local file system. */
        final InputStream sourceStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(libraryName);
        final OutputStream targetStream = new FileOutputStream(targetPath);
        transfer(Objects.requireNonNull(sourceStream), targetStream);
        sourceStream.close();
        targetStream.close();

        /* Load library via absolute path. */
        final String libraryPath = targetPath.getAbsolutePath();
        System.load(libraryPath);

        /* Tell sigar loader that the library is already loaded. */
        System.setProperty("org.hyperic.sigar.path", "-");
        sigarLoader.load();

        logger.info("Sigar library provisioned: " + libraryPath);

    }

    /**
     * Perform stream copy.
     *
     * @param input  The input stream.
     * @param output The output stream.
     * @throws Exception The stream copy failure exception.
     */
    public static void transfer(final InputStream input,
                                final OutputStream output) throws Exception {
        final byte[] data = new byte[SIZE];
        while (true) {
            final int count = input.read(data, 0, SIZE);
            if (count == EOF) {
                break;
            }
            output.write(data, 0, count);
        }
    }

    /**
     * Check silently if sigar was loaded in order to avoid the SigarException: no libsigar-*.
     *
     * @return true, if Sigar native library is present in the ClassLoader otherwise false.
     */
    private static boolean isSigarAlreadyLoaded() throws Exception {
        final String libraryName = new SigarLoader(Sigar.class).getLibraryName();
        final Field loadedLibraryNames = ClassLoader.class.getDeclaredField("loadedLibraryNames");
        loadedLibraryNames.setAccessible(true);
        final Collection<String> libraries = (Collection<String>) loadedLibraryNames.get(SigarProvisioner.class.getClassLoader());

        for (String library : libraries) {
            if (library.contains(libraryName)) return true;
        }
        return false;
    }

}
