package com.appnomic.appsone.netstat.sigar;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.ConnectionType;
import com.appnomic.appsone.common.enums.Direction;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.disco.SigarInstance;
import com.appnomic.appsone.netstat.Netstat;
import com.appnomic.appsone.util.Constants;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.hyperic.sigar.NetConnection;
import org.hyperic.sigar.NetFlags;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class SigarNetstat implements Netstat {
    private static final Logger log = LoggerFactory.getLogger(SigarNetstat.class);

    private final SigarInstance sigarInstance = SigarInstance.get();

    public Host discover(Host host) {
        host = getEndpoints(host, null);
        host = getConnections(host, null);
        return host;
    }

    public Host discover(Host host, DiscoveryAgentArgs discoveryAgentArgs) {
        host = getEndpoints(host, null);
        host = getConnections(host, null);
        return host;
    }

    public Host getConnections(Host host, NetStatOutput netStatOutput) {
        try {

            NetConnection[] connections = sigarInstance.sigar.getNetConnectionList(NetFlags.CONN_TCP | NetFlags.TCP_ESTABLISHED | NetFlags.TCP_TIME_WAIT | NetFlags.TCP_CLOSE_WAIT);
            log.info("Detected {} established connections", connections.length);
            List<Connection> conxs = new ArrayList<>();

            for (NetConnection netConnection : connections) {
                Connection conn = new Connection();
                if (netConnection.getLocalAddress().contains(Constants.IP_V6_SUBNET)) {
                    conn.setLocalIP(netConnection.getLocalAddress().replace(Constants.IP_V6_SUBNET, ""));
                } else {
                    conn.setLocalIP(netConnection.getLocalAddress());
                }
                if (netConnection.getRemoteAddress().contains(Constants.IP_V6_SUBNET)) {
                    conn.setRemoteIP(netConnection.getRemoteAddress().replace(Constants.IP_V6_SUBNET, ""));
                } else {
                    conn.setRemoteIP(netConnection.getRemoteAddress());
                }
                conn.setLocalPort(netConnection.getLocalPort());
                conn.setRemotePort(netConnection.getRemotePort());
                conn.setConnectionIdentifier(NodeIdGenerator.generateNodeIdForConnection(conn));
                conn.setHostIdentifier(host.getHostIdentifier());
                conn.setConnectionType(ConnectionType.CONN_TCP);
                conn.setDirection(Direction.OUTGOING_CONNECTION);
                log.debug("Established Connection: {} ", conn);
                conxs.add(conn);
            }
            host.setConnections(conxs);
            log.info("Discovered Connections: {}", conxs);

        } catch (Exception exception) {
            log.error("Exception for Connections Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Connections Discovery", 1, exception));
        }
        return host;
    }

    public Host getEndpoints(Host host, NetStatOutput netStatOutput) {
        try {

            NetConnection[] connections = sigarInstance.sigar.getNetConnectionList(NetFlags.TCP_LISTEN | NetFlags.CONN_TCP | NetFlags.CONN_UDP);
            log.info("Detected {} listening endpoints", connections.length);
            List<Endpoint> endpoints = new ArrayList<>();

            for (NetConnection conn : connections) {
                Endpoint endpoint;
                if (conn.getLocalAddress().equals(NetFlags.ANY_ADDR_V6)) {
                    endpoint = new Endpoint(NetFlags.ANY_ADDR, conn.getLocalPort());
                } else {
                    endpoint = new Endpoint(conn.getLocalAddress(), conn.getLocalPort());
                }
                endpoint.setEndpointIdentifier(NodeIdGenerator.generateNodeIdForEndpoint(endpoint));
                endpoint.setHostIdentifier(host.getHostIdentifier());
                endpoints.add(endpoint);
                log.debug("Endpoint: {}", endpoint);
            }

            log.info("Added {} endpoints", Sets.newHashSet(endpoints).size());
            host.setEndpoints(Lists.newArrayList(Sets.newHashSet(endpoints)));

        } catch (Exception exception) {
            log.error("Exception for Endpoints Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Endpoints Discovery", 1, exception));
        }
        return host;
    }
}