package com.appnomic.appsone.netstat;

import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.netstat.aix.AIXNetstat;
import com.appnomic.appsone.netstat.sigar.SigarNetstat;
import com.appnomic.appsone.netstat.sun.SunOSNetstat;
import com.appnomic.appsone.util.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.Map;

public class NetstatFactory {

    private static final Logger log = LoggerFactory.getLogger(NetstatFactory.class);

    private static NetstatFactory _this;
    private final Map<String, Netstat> netstats;

    private NetstatFactory() {
        this.netstats = new HashMap<>();
        this.netstats.put(Constants.AIX_PLATFORM, new AIXNetstat());
        this.netstats.put(Constants.SUN_OS_PLATFORM, new SunOSNetstat());
        this.netstats.put(Constants.SIGAR, new SigarNetstat());
    }

    public static NetstatFactory getInstance() {
        if (_this == null) {
            _this = new NetstatFactory();
        }
        return _this;
    }

    public Netstat getNetstat(Host host) {
        String operatingSystem = host.getOperatingSystem();
        log.info("Operating System: {}", operatingSystem);
        if (operatingSystem != null && !operatingSystem.contains(Constants.AIX_PLATFORM) && !operatingSystem.contains(Constants.SUN_OS_PLATFORM)) {
            return this.netstats.get(Constants.SIGAR);
        } else {
            return this.netstats.get(operatingSystem);
        }
    }
}

