package com.appnomic.appsone.netstat;

import com.appnomic.appsone.common.beans.discovery.DiscoveryAgentArgs;
import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.common.beans.discovery.NetStatOutput;

public interface Netstat {
    Host discover(Host host);

    Host discover(Host host, DiscoveryAgentArgs discoveryAgentArgs);

    Host getConnections(Host host, NetStatOutput netStatOutput);

    Host getEndpoints(Host host, NetStatOutput netStatOutput);
}
