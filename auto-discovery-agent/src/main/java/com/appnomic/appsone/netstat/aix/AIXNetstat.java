package com.appnomic.appsone.netstat.aix;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.ConnectionType;
import com.appnomic.appsone.common.enums.Direction;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.netstat.Netstat;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Utils;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;

public class AIXNetstat implements Netstat {
    private static final Logger log = LoggerFactory.getLogger(AIXNetstat.class);

    private static final String aix_netstat_pattern = "tcp(|4|6)\\s+[0-9]\\s+[0-9]\\s+(?<localaddress>[0-9\\.\\:\\*]+)\\s+(?<remoteaddress>[0-9\\.\\:\\*]+)+\\s+[A-Z]+";
    private static final String aix_netstat_command_LISTEN = "netstat -an | grep LISTEN";
    private static final String aix_netstat_command_ESTABLISHED = "netstat -an | grep -e ESTABLISHED -e WAIT";

    public static String getAix_netstat_pattern() {
        return aix_netstat_pattern;
    }

    public static String getAix_netstat_command_LISTEN() {
        return aix_netstat_command_LISTEN;
    }

    public static String getAix_netstat_command_ESTABLISHED() {
        return aix_netstat_command_ESTABLISHED;
    }

    public Host discover(Host host )
    {
        return host;
    }
    public Host discover(Host host , DiscoveryAgentArgs discoveryAgentArgs) {
        NetStatOutput netStatOutput = new NetStatOutput();
        netStatOutput.setRegexPattern(aix_netstat_pattern);
        netStatOutput.setBashCommand(aix_netstat_command_ESTABLISHED);
        Utils.getNetstatResults(netStatOutput, Arguments.getShellPath(), true);

        host = getConnections(host, netStatOutput);

        netStatOutput.setBashCommand(aix_netstat_command_LISTEN);
        Utils.getNetstatResults(netStatOutput, Arguments.getShellPath(), true);

        host = getEndpoints(host, netStatOutput);
        return host;
    }

    public Host getConnections(Host host, NetStatOutput netStatOutput) {

        List<Connection> connectionsList = new ArrayList<>();
        for (Matcher matcher : netStatOutput.getMatchedLines()) {

            String ipPortLocal = matcher.group("localaddress");
            int index = ipPortLocal.lastIndexOf(".");
            if (index != -1) {
                String ip = ipPortLocal.substring(0, index);
                String port = ipPortLocal.substring(index + 1);

                String ipPortRemote = matcher.group("remoteaddress");
                index = ipPortRemote.lastIndexOf(".");
                if (index != -1) {
                    String ipRemote = ipPortRemote.substring(0, index);
                    String portRemote = ipPortRemote.substring(index + 1);

                    Connection connection = new Connection();
                    connection.setLocalIP(ip);
                    connection.setLocalPort(Integer.parseInt(port));
                    connection.setRemoteIP(ipRemote);
                    connection.setRemotePort(Integer.parseInt(portRemote));
                    connection.setConnectionType(ConnectionType.CONN_TCP);
                    connection.setDirection(Direction.OUTGOING_CONNECTION);
                    connection.setConnectionIdentifier(NodeIdGenerator.generateNodeIdForConnection(connection));
                    connection.setHostIdentifier(host.getHostIdentifier());
                    connectionsList.add(connection);
                    log.debug("Adding connection: {}", connection);
                }
            }
        }
        host.setConnections(connectionsList);
        log.info("Added {} connections", connectionsList.size());
        for (DiscoveryError error : netStatOutput.getDiscoveryErrors()) {
            host.addDiscoveryError(error);
        }
        return host;
    }

    public Host getEndpoints(Host host, NetStatOutput netStatOutput) {
        List<Endpoint> endpointList = new ArrayList<>();
        for (Matcher matcher : netStatOutput.getMatchedLines()) {
            String ipPort = matcher.group("localaddress");
            int index = ipPort.lastIndexOf(".");
            if (index != -1) {
                String ip = ipPort.substring(0, index);
                String port = ipPort.substring(index + 1);
                Endpoint endpoint = new Endpoint(ip, Integer.parseInt(port));
                endpoint.setEndpointIdentifier(NodeIdGenerator.generateNodeIdForEndpoint(endpoint));
                endpoint.setHostIdentifier(host.getHostIdentifier());
                endpointList.add(endpoint);
                log.debug("Adding Endpoint: {}", endpoint);
            }
        }
        log.info("Added {} endpoints", Sets.newHashSet(endpointList).size());
        host.setEndpoints(Lists.newArrayList(Sets.newHashSet(endpointList)));
        for (DiscoveryError error : netStatOutput.getDiscoveryErrors()) {
            host.addDiscoveryError(error);
        }
        return host;
    }
}