package com.appnomic.appsone;

import com.appnomic.appsone.ccapi.ControlCenterService;
import com.appnomic.appsone.common.beans.discovery.DiscoveryAgentArgs;
import com.appnomic.appsone.common.beans.discovery.DiscoveryError;
import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.Prerequisite;
import com.appnomic.appsone.common.enums.DiscoveryMode;
import com.appnomic.appsone.disco.process.HealAutoDiscovery;
import com.appnomic.appsone.disco.version.VersionDiscovery;
import com.appnomic.appsone.parser.CommandParser;
import com.appnomic.appsone.prereq.PrerequisiteDiscovery;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class Main {

    private static final Logger log = LoggerFactory.getLogger(Main.class);

    public static void main(String[] args) {

        log.info("Agent Name: {} ", Main.class.getPackage().getImplementationTitle());
        log.info("Agent Version: {} ", Main.class.getPackage().getImplementationVersion());

        CommandParser commandParser = new CommandParser(args);
        DiscoveryAgentArgs discoveryAgentArgs = new DiscoveryAgentArgs();

        Host discoveryData = new Host();
        Prerequisite prerequisite;
        String discoveryDataJson = StringUtil.EMPTY_STRING;

        try {
            discoveryAgentArgs = commandParser.parse();
            if (discoveryAgentArgs.getShellCmdPath() == null && !Utils.isWindows()) {
                Arguments.setShellPath(Constants.DEFAULT_SHELL_PATH);
            } else if (discoveryAgentArgs.getShellCmdPath() != null) {
                Arguments.setShellPath(discoveryAgentArgs.getShellCmdPath());
            }
            log.info("The shell path is : {}", Arguments.getShellPath());

            if (discoveryAgentArgs.getReadHttpdConfigSubFiles() != null) {
                Arguments.setReadHttpdConfigSubFiles(Boolean.parseBoolean(discoveryAgentArgs.getReadHttpdConfigSubFiles()));
            }

            if (discoveryAgentArgs.getYamlFilePath() != null) {
                Arguments.setYamlFilePath(discoveryAgentArgs.getYamlFilePath());
            }

            if (discoveryAgentArgs.getFinacleVersion() != null) {
                Arguments.setFinacleVersion(VersionDiscovery.getFinacleVersion(discoveryAgentArgs.getFinacleVersion()));
            }

            if (!Utils.isWindows()) {
                log.info("The user who is currently running the Autodiscovery agent : cmd : {}", Constants.AUTODISCOVERY_USER_COMMAND);
                Utils.getCommandOutput(Constants.AUTODISCOVERY_USER_COMMAND, false, false);
            }

            log.info("The yaml file path is : {}", Arguments.getYamlFilePath());
            PrerequisiteDiscovery prerequisiteDiscovery = new PrerequisiteDiscovery();
            Arguments.setCcUrl(discoveryAgentArgs.getCcUrl());
            YamlFileLoader.init();

            discoveryData = HealAutoDiscovery.getInstance().runDiscovery(discoveryAgentArgs);

            if (discoveryAgentArgs.isPreReqCheck()) {
                prerequisite = prerequisiteDiscovery.run(discoveryAgentArgs);
                if (prerequisite != null) {
                    log.info("Adding Pre-requisite stats to discovery json");
                    discoveryData.setPreRequisitesFound(prerequisite);
                }
            }
            discoveryData.setAgents(discoveryAgentArgs.getAgents());
            log.debug("DiscoveryData: {} ", discoveryData);
            if (discoveryData.getDiscoveryErrors() == null) {
                List<DiscoveryError> discoveryErrors = new ArrayList<>();
                discoveryData.setDiscoveryErrors(discoveryErrors);
            }

            discoveryDataJson = Utils.jsonify(discoveryData);


        } catch (Exception e) {
            log.error("Exception occurred when running agent- {} : {} ", e.getMessage(), e);
        }

        if (discoveryAgentArgs.getMode().equals(DiscoveryMode.OFFLINE) || discoveryAgentArgs.getMode().equals(DiscoveryMode.DUAL)) {
            try {
                Utils.writeFileToDisk(discoveryAgentArgs.getResultsDir(), discoveryData.getHostIdentifier(), discoveryDataJson);
            } catch (Exception e) {
                log.error("Exception occurred when writing file to disk- {} : {} ", e.getMessage(), e);
            }
        }

        if (discoveryAgentArgs.getMode().equals(DiscoveryMode.ONLINE) || discoveryAgentArgs.getMode().equals(DiscoveryMode.DUAL)) {
            try {
                ControlCenterService.pushDiscoveryDataJson(discoveryAgentArgs.getCcUrl(),
                        Utils.jsonify(getFilteredDiscoveryData(discoveryData)));
            } catch (Exception e) {
                log.error("Exception while pushing discovered data to CC : ", e);
            }
        }
    }

    public static Host getFilteredDiscoveryData(Host discoveryData) {
        log.info("Validating json before CC call");
        List<Process> filteredProcesses = new ArrayList<>();
        for (Process p : discoveryData.getRunningProcesses()) {
            if (p.getComponentId() == 0) {
                continue;
            }
            if (p.getComponentVersion() == null || p.getComponentVersion().equals(StringUtil.EMPTY_STRING) ||
                    p.getAttributes() == null || p.getAttributes().isEmpty()) {
                log.info("The below process will not be created in CC as the version, attribute is null or empty");
                try {
                    String process = Utils.jsonify(p);
                    log.info("process : {}", process);
                } catch (Exception e) {
                    log.error("Exception while creating json of a process : {}", p, e);
                }
                continue;
            }
            if (p.getProcessCurrentWorkingDirectory() == null || p.getProcessCurrentWorkingDirectory().isEmpty()) {
                p.setProcessCurrentWorkingDirectory(File.separator);
            }
            filteredProcesses.add(p);
        }

        discoveryData.setRunningProcesses(filteredProcesses);
        return discoveryData;
    }
}