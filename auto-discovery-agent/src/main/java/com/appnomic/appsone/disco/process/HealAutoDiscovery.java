package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.common.enums.DiscoveryStatus;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.disco.SigarInstance;
import com.appnomic.appsone.netstat.Netstat;
import com.appnomic.appsone.netstat.NetstatFactory;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.parser.ParserFactory;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import org.apache.commons.lang3.StringUtils;
import org.hyperic.sigar.FileSystem;
import org.hyperic.sigar.NetFlags;
import org.hyperic.sigar.NetInterfaceConfig;
import org.hyperic.sigar.SigarException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import oshi.SystemInfo;

import java.io.File;
import java.net.InetAddress;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

public class HealAutoDiscovery {

    private static final Logger log = LoggerFactory.getLogger(HealAutoDiscovery.class);
    private static HealAutoDiscovery INSTANCE = null;
    private final SigarInstance sigarInstance = SigarInstance.get();

    protected HealAutoDiscovery() {
    }

    public static HealAutoDiscovery getInstance() {
        if (INSTANCE == null) {
            INSTANCE = new HealAutoDiscovery();
        }
        return INSTANCE;
    }

    private void discoverBasicSystemInformation(Host host) {
        try {
            String osVersion = null;
            String osFamily = null;
            try {
                SystemInfo systemInfo = new SystemInfo();
                osFamily = systemInfo.getOperatingSystem().getFamily();
                osVersion = systemInfo.getOperatingSystem().getVersion().getVersion();
                if (osFamily.toLowerCase().contains(Constants.WINDOWS_OS.toLowerCase())) {
                    osVersion = System.getProperty(Constants.OS_NAME_SYS_KEY).split(Constants.WINDOWS_OS)[1].trim();
                }
                log.info("osVersion: {} , osFamily: {} ", osVersion, osFamily);
            } catch (UnsupportedOperationException e) {
                log.error("Exception loading system information  using oshi library. Trying to get the  Host deatails from System.getProperty.");
            }
            if (osVersion == null || StringUtils.isEmpty(osVersion) ||
                    osFamily == null || StringUtils.isEmpty(osFamily)) {
                osFamily = System.getProperty(Constants.OS_NAME_SYS_KEY);
                osVersion = System.getProperty(Constants.OS_VERSION_SYS_KEY);
                if (osFamily.toLowerCase().contains(Constants.WINDOWS_OS.toLowerCase())) {
                    osVersion = osFamily.split(Constants.WINDOWS_OS)[1].trim();
                }
            }
            host.setLastDiscoveryRunTime(Calendar.getInstance().getTimeInMillis());
            host.setDiscoveryStatus(DiscoveryStatus.DISCOVERED_NOT_ADDED_TO_SYSTEM);
            host.setOperatingSystem(osFamily);
            Arguments.setOS(osFamily);
            host.setOperatingSystemVersion(osVersion);
            host.setPlatform(System.getProperty("os.arch"));
            host.setCurrentUser(System.getProperty("user.name"));

            String[] ifaces = sigarInstance.sigar.getNetInterfaceList();
            Map<String, String> interfaceMacIds = new HashMap<>();
            StringBuilder interfaces = new StringBuilder();
            for (String iface : ifaces) {
                NetInterfaceConfig cfg = sigarInstance.sigar.getNetInterfaceConfig(iface);
                if (NetFlags.isLoopback(cfg.getAddress()) || NetFlags.NULL_HWADDR.equals(cfg.getHwaddr())) {
                    continue;
                }
                interfaceMacIds.put(cfg.getName(), cfg.getHwaddr());
            }

            for (Map.Entry<String, String> entry : interfaceMacIds.entrySet()) {
                interfaces.append(entry.getValue()).append("-");
            }
            log.debug("Interfaces with MAC address detected: {} | Interfaces Key: {}", interfaceMacIds, interfaces);

            String hostname = InetAddress.getLocalHost().getHostName();
            host.setHostname(hostname);
            interfaces.append(hostname);
            String nodeIdentifier = NodeIdGenerator.generateNodeId(interfaces);

            log.debug("hostname: {} , identifier: {} ", hostname, nodeIdentifier);
            host.setHostIdentifier(nodeIdentifier);

        } catch (UnknownHostException | SigarException e) {
            log.error("Exception for Basic Discovery {} : {}", e.getMessage(), e);
            host.addDiscoveryError(DiscoveryError.fromException("Basic Info Discovery", 1, e));
        }
    }

    private void discoverNetworkInterfaces(Host host) {
        try {
            String[] networkInterfaces = sigarInstance.sigar.getNetInterfaceList();
            List<NetworkInterface> discoveredNetworkInterfaceList = new ArrayList<>();
            for (String nwInterface : networkInterfaces) {
                NetInterfaceConfig netInterfaceConfig = sigarInstance.sigar.getNetInterfaceConfig(nwInterface);
                if (NetFlags.isLoopback(netInterfaceConfig.getAddress()) || NetFlags.isAnyAddress(netInterfaceConfig.getAddress())) {
                    continue;
                }
                NetworkInterface discoveredInterface = new NetworkInterface();
                discoveredInterface.setInterfaceName(netInterfaceConfig.getName());
                discoveredInterface.setInterfaceIP(netInterfaceConfig.getAddress());
                discoveredInterface.setInterfaceType(netInterfaceConfig.getType());
                discoveredInterface.setHardwareAddress(netInterfaceConfig.getHwaddr());
                discoveredInterface.setInterfaceMask(netInterfaceConfig.getNetmask());
                discoveredInterface.setInterfaceDescription(netInterfaceConfig.getDescription());
                discoveredInterface.setHostIdentifier(host.getHostIdentifier());
                discoveredInterface.setNetworkInterfaceIdentifier(NodeIdGenerator.generateNodeIdForNetworkInterface(discoveredInterface));
                discoveredInterface.setInterfaceDescription(discoveredInterface.getInterfaceDescription());
                discoveredInterface.setInterfaceMask(discoveredInterface.getInterfaceMask());

                discoveredNetworkInterfaceList.add(discoveredInterface);
            }
            host.setNetworkInterfaces(discoveredNetworkInterfaceList);
            log.info("Discovered Network Interfaces: {}", discoveredNetworkInterfaceList);

        } catch (Exception exception) {
            log.error("Exception for Network Interface Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Network Interface Discovery", 1, exception));
        }
    }

    private void discoverMountPoints(Host host) {
        try {
            FileSystem[] fileSystems = sigarInstance.sigar.getFileSystemList();
            List<MountPoint> mountPoints = new ArrayList<>();
            for (FileSystem fs : fileSystems) {
                MountPoint mountPoint = new MountPoint();
                mountPoint.setDevName(fs.getDevName());
                mountPoint.setDirName(fs.getDirName());
                mountPoint.setTypeName(fs.getTypeName());
                mountPoint.setMountPointIdentifier(NodeIdGenerator.generateNodeIdForMountPoint(mountPoint));
                mountPoint.setHostIdentifier(host.getHostIdentifier());
                mountPoints.add(mountPoint);
            }
            host.setMountPoints(mountPoints);
            log.info("Discovered Mountpoints: {}", mountPoints);
        } catch (Exception exception) {
            log.error("Exception for Mount Points Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Mount Points Discovery", 1, exception));
        }
    }

    public Host runDiscovery(DiscoveryAgentArgs discoveryAgentArgs) {
        log.info("Triggering Discovery");

        ProcessDiscovery processDiscovery = new ProcessDiscovery();

        Host hostFound = new Host();

        hostFound.setValidationScriptExecRequested(discoveryAgentArgs.isScriptValidate());
        hostFound.setEnableScriptExecRequested(discoveryAgentArgs.isScriptEnable());
        hostFound.setScriptPath(discoveryAgentArgs.getScriptPath());

        discoverBasicSystemInformation(hostFound);
        Netstat netstat = NetstatFactory.getInstance().getNetstat(hostFound);

        discoverMountPoints(hostFound);
        discoverNetworkInterfaces(hostFound);
        hostFound = netstat.discover(hostFound, discoveryAgentArgs);
        hostFound = processDiscovery.discover(hostFound);
        discoverExecutablesOnPath(hostFound);
        discoverHostAttributes(hostFound);
        discoverUniqueErrors(hostFound);

        sigarInstance.unload();

        return hostFound;
    }

    private void discoverUniqueErrors(Host host) {
        log.info("Discovering Unique errors");
        List<DiscoveryError> allErrorsFound = host.getDiscoveryErrors();
        Map<DiscoveryError, Integer> uniqueErrorsWithCount = new HashMap<>();
        try {
            if (allErrorsFound != null && allErrorsFound.size() > 0) {
                Set<DiscoveryError> uniqueErrors = new HashSet<>(allErrorsFound);

                for (DiscoveryError unique : uniqueErrors) {
                    for (DiscoveryError error : allErrorsFound) {
                        if (error.equals(unique)) {
                            if (uniqueErrorsWithCount.containsKey(error)) {
                                Integer count = uniqueErrorsWithCount.get(error);
                                count = count + 1;
                                for (DiscoveryError entry : uniqueErrorsWithCount.keySet()) {
                                    if (entry.equals(error)) {
                                        entry.getVariables().addAll(error.getVariables());
                                        uniqueErrorsWithCount.put(entry, count);
                                    }
                                }
                            } else {
                                uniqueErrorsWithCount.put(error, 1);
                            }
                        }
                    }
                }
            }
            if (host.getDiscoveryErrors() != null && host.getDiscoveryErrors().size() > 0) {
                host.getDiscoveryErrors().clear();
            }
            log.info("Unique errors encountered: {} ", uniqueErrorsWithCount);

            for (Map.Entry<DiscoveryError, Integer> entry : uniqueErrorsWithCount.entrySet()) {
                DiscoveryError error = entry.getKey();
                error.setErrorCount(uniqueErrorsWithCount.get(error));
                host.addDiscoveryError(error);
            }
        } catch (Exception e) {
            log.error("Exception discovering unique errors with count {} : {}", e.getMessage(), e);
        }
    }

    private void discoverExecutablesOnPath(Host host) {
        List<String> executablesOnPath = new ArrayList<>();

        String osName = host.getOperatingSystem();

        String path = System.getenv("PATH");
        log.debug("PATH Environment variable: {} ", path);

        String[] directoriesFoundOnPath;

        if (osName.contains("Windows")) {
            directoriesFoundOnPath = path.split(";");
        } else {
            directoriesFoundOnPath = path.split(":");
        }

        for (String directory : directoriesFoundOnPath) {
            File directoryPath = new File(directory);
            String[] contents = directoryPath.list();
            if (contents != null && contents.length > 0) {
                log.debug("Folder: {} ", directory);
                for (String file : contents) {
                    Path pathDirectory = Paths.get(directory + "/" + file);
                    if (!Files.isDirectory(pathDirectory)) {
                        boolean isFileExecutable = Files.isExecutable(pathDirectory);
                        if (isFileExecutable) {
                            if (osName.contains("Windows")) {
                                if (file.endsWith(".exe") || file.endsWith(".bat")) {
                                    executablesOnPath.add(directory + "/" + file);
                                }
                            } else {
                                executablesOnPath.add(directory + "/" + file);
                            }
                        }
                    }
                }
            }
        }
        log.debug("Executables found on path:  {} ", executablesOnPath);
        host.setAvailableCommandsInPath(executablesOnPath);
    }

    private void discoverHostAttributes(Host host) {
        try {
            Map<String, String> attributesFound = new HashMap<>();

            String hostAddress = Utils.getHostAddress(host.getNetworkInterfaces());

            attributesFound.put("HostAddress", hostAddress);

            ParseTarget parseTarget = new ParseTarget();

            if (Utils.isWindows()) {
                parseTarget.setConfigFileName(Constants.DEFAULT_SSH_CONFIG_WINDOWS);
                parseTarget.setServerRoot(Constants.DEFAULT_SSH_HOME_WINDOWS);
            } else {
                parseTarget.setConfigFileName(Constants.DEFAULT_SSH_CONFIG_LINUX);
                parseTarget.setServerRoot(Constants.DEFAULT_SSH_HOME_LINUX);
            }

            try {
                parseTarget.setAttributeName("Port");
                String sshPort = ParserFactory.getInstance()
                        .getParser(DiscoveryMethod.APACHE_PARSER.getValue())
                        .parse(parseTarget);
                if (StringUtils.isEmpty(sshPort)) {
                    sshPort = Constants.DEFAULT_SSH_PORT;
                    log.info("SSH port could not be located in config file, reporting default port {} ", Constants.DEFAULT_SSH_PORT);
                }
                if (!Utils.isWindows()) {
                    attributesFound.put("SshPort", sshPort);
                    attributesFound.put("HostUsername", host.getCurrentUser());
                } else {
                    attributesFound.put("Username", host.getCurrentUser());
                }
            } catch (Exception e) {
                log.error("Exception for Host Attribute Discovery {} : {}", e.getMessage(), e);
                host.addDiscoveryError(DiscoveryError.fromException("Host Attribute Discovery", 1, e));
            }

            log.info("Discovered Host Attributes: {}", attributesFound);
            host.setHostAttributes(attributesFound);

        } catch (Exception exception) {
            log.error("Exception for Network Interface Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Network Interface Discovery", 1, exception));
        }
    }
}
