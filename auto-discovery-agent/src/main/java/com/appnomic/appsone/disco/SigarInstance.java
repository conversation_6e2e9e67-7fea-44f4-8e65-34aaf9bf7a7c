package com.appnomic.appsone.disco;

import com.appnomic.appsone.util.SigarProvisioner;
import org.hyperic.sigar.Sigar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class SigarInstance {

    private static final Logger log = LoggerFactory.getLogger(Sigar.class);
    private static SigarInstance INSTANCE = null;
    public Sigar sigar;

    private SigarInstance() {

    }

    public static synchronized SigarInstance get() {
        if (INSTANCE == null) {
            INSTANCE = new SigarInstance();
            INSTANCE.load();
        }
        return INSTANCE;
    }

    private void load() {
        try {
            log.debug("Loading Sigar library");
            SigarProvisioner.provision();
            sigar = new Sigar();
            log.info("Sigar loaded successfully, Process pid: {} ", sigar.getPid());
        } catch (Exception e) {
            log.error("Failed to load Sigar Library- {} : {} ", e.getMessage(), e);
        }
    }

    public void unload() {
        try {
            log.debug("UnLoading Sigar library");
            sigar.close();
            log.info("<PERSON>gar closed successfully");
        } catch (Exception e) {
            log.error("Failed to close Sigar Library- {} : {} ", e.getMessage(), e);
            throw e;
        }
    }
}
