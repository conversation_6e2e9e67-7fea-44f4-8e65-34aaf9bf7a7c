package com.appnomic.appsone.disco.entity;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.appnomic.appsone.common.beans.discovery.Entity;
import com.appnomic.appsone.common.beans.discovery.EntityDetail;
import com.appnomic.appsone.parser.ConfigFileDetails;
import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EntityDiscovery {

    private static final Logger log = LoggerFactory.getLogger(EntityDiscovery.class);

    public Map<String, List<String>> run(ConfigFileDetails configFileDetails, String componentVersion) {
        log.info("Starting the Entity Discovery with  configFileDetails : {} and ComponentVersion : {}", configFileDetails, componentVersion);
        Map<String, List<String>> kpis = new HashMap<>();
        List<String> fileWatchList = new ArrayList<>();
        List<String> keyValueList = new ArrayList<>();
        Component component = YamlFileLoader.getComponentById(configFileDetails.getComponentId());
        YamlFileLoader.init();
        Entity entityConfiguration = YamlFileLoader.getConfigurationEntity(component.getComponentName(), componentVersion);

        if(entityConfiguration == null)
        {
            log.info("The configuration entity for provided component name {} and version {} is not supported. Hence, skipping the entity discovery.",configFileDetails.getComponentName(), componentVersion);
            return kpis;
        }

        List<EntityDetail> entities = entityConfiguration.getEntities();
        for (EntityDetail entityDetail : entities) {
            log.info("Discovering the Entity : {}, Current Version : {} , Component : {}", entityDetail, componentVersion, configFileDetails.getComponentId());
            if (configFileDetails.getConfigFilePath().contains(entityDetail.getEntityName())) {
                log.info("Adding discovered component config file {} to file watch.", configFileDetails.getConfigFilePath());
                fileWatchList.add(configFileDetails.getConfigFilePath().trim());
            } else {
                String location = PathFinder.searchInsideInstallationDirectory(configFileDetails.getComponentRoot(), entityDetail.getEntityName(), entityDetail.getRelativePath());

                if (StringUtils.isEmpty(location) && configFileDetails.isConfigFilePresent()) {
                    String configFilePath = configFileDetails.getConfigFilePath();
                    configFilePath = configFilePath.substring(0, configFilePath.lastIndexOf(File.separator));

                    if (!StringUtils.isEmpty(configFilePath) && configFilePath.contains(File.separator)) {
                        location = PathFinder.searchInsideInstallationDirectory(configFilePath, entityDetail.getEntityName(), entityDetail.getRelativePath()).trim();
                    }
                }
                if(!StringUtils.isEmpty(location)) {
                    location = location.trim();
                    if (entityDetail.getKpiType().equalsIgnoreCase(Constants.FILE_WATCH)) {
                        log.info("Adding discovered component config file {} to file watch kpi list", location);
                        fileWatchList.add(location);
                    }
                    if (entityDetail.getKpiType().equalsIgnoreCase(Constants.KEY_VALUE)) {
                        log.info("Adding discovered component config file {} to config watch kpi list as keyValue.", location);
                        keyValueList.add(location);
                    }
                }
            }
        }
        if(!fileWatchList.isEmpty()) {
            kpis.put(Constants.KPI_FILE_WATCH, fileWatchList);
        }
        if(!keyValueList.isEmpty()) {
            kpis.put(Constants.KPI_KEY_VALUE, keyValueList);
        }

        return kpis;
    }
}