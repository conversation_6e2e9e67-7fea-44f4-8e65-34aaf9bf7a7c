package com.appnomic.appsone.disco.process;

import com.appnomic.appsone.common.beans.discovery.DiscoveryError;
import com.appnomic.appsone.common.beans.discovery.Host;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.disco.SigarInstance;
import com.appnomic.appsone.disco.component.ComponentDiscovery;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import io.netty.util.internal.StringUtil;
import org.hyperic.sigar.ProcExe;
import org.hyperic.sigar.SigarException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ProcessDiscovery {

    private static final Logger log = LoggerFactory.getLogger(ProcessDiscovery.class);

    private final SigarInstance sigarInstance = SigarInstance.get();

    public Host discover(Host host) {

        // Key is PID, Value is Process argument
        Map<String, String> processArguments = new HashMap<>();
        String processes = StringUtil.EMPTY_STRING;
        if (!Utils.isWindows()) {
            log.info("The running processes from  cmd : {}", Constants.LIST_ALL_PROCESS_COMMAND);
            processes = Utils.getCommandOutput(Constants.LIST_ALL_PROCESS_COMMAND, false, false);
            processArguments = getProcessArg(processes);
        }
        List<Process> runningProcesses = new ArrayList<>();

        try {

            long[] procIds = sigarInstance.sigar.getProcList();

            Integer noOfErrors = 0;

            for (long pid : procIds) {

                try {
                    ProcExe procExe = sigarInstance.sigar.getProcExe(pid);
                    String procCwd = StringUtil.EMPTY_STRING;
                    String processArgs = StringUtil.EMPTY_STRING;
                    try {
                        procCwd = procExe.getCwd();
                        String[] args = sigarInstance.sigar.getProcArgs(pid);
                        StringBuilder stringBuilder = new StringBuilder();
                        if (args != null) {
                            for (String arg : args) {
                                stringBuilder.append(arg).append(" ");
                            }
                            processArgs = stringBuilder.toString();
                        }
                    } catch (Exception e) {
                        noOfErrors++;
                        host.addDiscoveryError(DiscoveryError.fromException(String.valueOf(pid), 1, e));
                        log.warn("Exception while getting process details from sigar library  for pid :{} : {}. Trying to get Process Details from command.", pid, e);
                    }

                    if (processArgs.isEmpty() && !processArguments.isEmpty() && !Utils.isWindows()) {
                        try {
                            processArgs = processArguments.get(Long.toString(pid));
                            log.info("Getting process Argument from command for pid :{} , args : {}", pid, processArgs);
                        } catch (Exception e) {
                            noOfErrors++;
                            host.addDiscoveryError(DiscoveryError.fromException(String.valueOf(pid), 1, e));
                            log.error("Exception while getting process argument from command :{} ", Constants.LIST_ALL_PROCESS_COMMAND, e);
                        }
                    }

                    Process process = new Process();
                    process.setProcessName(procExe.getName());
                    process.setProcessArgs(processArgs);
                    process.setProcessCurrentWorkingDirectory(procCwd);
                    process.setPid(Long.toString(pid));
                    process.setProcessIdentifier(NodeIdGenerator.generateNodeIdForProcess(process, host.getHostname()));
                    process.setHostIdentifier(host.getHostIdentifier());
                    runningProcesses.add(process);
                } catch (SigarException exception) {
                    noOfErrors++;
                    log.error("Exception for Processes Discovery with Pid {} & {} : {}", pid, exception.getMessage(), exception);
                    host.addDiscoveryError(DiscoveryError.fromException(String.valueOf(pid), 1, exception));
                }
            }

            ComponentDiscovery componentDiscovery = new ComponentDiscovery();
            runningProcesses = componentDiscovery.run(runningProcesses, host, processes);
            log.info("Total discovered {} running processes with {} errors", runningProcesses.size(), noOfErrors);

        } catch (Exception exception) {
            log.error("Exception for Processes Discovery {} : {}", exception.getMessage(), exception);
            host.addDiscoveryError(DiscoveryError.fromException("Processes Discovery", 1, exception));
        }
        host.setRunningProcesses(runningProcesses);
        return host;
    }

    public Map<String, String> getProcessArg(String processesFromCmd) {
        Map<String, String> processArgs = new HashMap<>();
        try {
            String[] processes = processesFromCmd.split(Constants.NEXT_LINE);
            for (String process : processes) {
                String[] proDetail = process.trim().split(Constants.MULTIPLE_SPACE_PATTERN);
                if (proDetail.length < 3) {
                    log.warn("Process command is not in expected format. {}-{}", proDetail.length, process);
                    continue;
                }
                if (proDetail[0] == null || proDetail[1] == null || proDetail[2] == null) {
                    continue;
                }
                String processName = process.split(Constants.SPACE_DELIMITER + proDetail[1].trim() + Constants.SPACE_DELIMITER)[1].trim();
                processArgs.put(proDetail[0].trim(), processName);

            }
            log.info("The process Arguments : {}", processArgs);
        } catch (Exception e) {
            log.error("Exception while parsing for process arguments : {}", processesFromCmd, e);
        }
        return processArgs;
    }
}
