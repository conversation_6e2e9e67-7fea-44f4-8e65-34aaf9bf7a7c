package com.appnomic.appsone.disco.version;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.DiscoveryItems;
import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.parser.ConfigFileDetails;
import com.appnomic.appsone.parser.ParserFactory;
import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.*;

public class VersionDiscovery {

    private static final Logger logger = LoggerFactory.getLogger(VersionDiscovery.class);

    private static final Map<Integer, String> componentVersionDetected = new HashMap<>();

    private static final Map<Integer, ComponentVersion> componentsVersion = new HashMap<>();

    public VersionDiscovery(List<Component> autoDiscoveryComponents) {
        if (autoDiscoveryComponents != null) {
            for (Component component : autoDiscoveryComponents) {
                componentVersionDetected.put(component.getComponentId(), StringUtils.EMPTY);
            }
        }
        List<ComponentVersion> versionList = YamlFileLoader.getComponentsVersion();
        if (versionList != null) {
            for (ComponentVersion vi : versionList) {
                componentsVersion.put(vi.getComponentId(), vi);
            }
        }
    }

    public static String getFinacleVersion(String finacleVersion) {
        logger.info("The finacle version is : {}", finacleVersion);
        return finacleVersion;
    }

    public Process run(Process targetProcess, Component component, String operatingSystem) {

        logger.info("Inside VersionDiscovery for PROCESS : {} and COMPONENT :{}", targetProcess.getProcessName(), component.getComponentName());
        String detectedVersion = StringUtils.EMPTY;
        if(component.getComponentId() != Constants.APACHE_HTTPD_COMPONENT_ID) {
            detectedVersion = componentVersionDetected.get(component.getComponentId());
        }
        String commandOutput;

        String command = StringUtils.EMPTY;

        ConfigFileDetails configFileDetails;

        if(!StringUtils.isEmpty(detectedVersion)) {
            targetProcess.setComponentVersion(detectedVersion);
            logger.info("The version detected for PROCESS : {} is {}", targetProcess.getProcessName(), detectedVersion);
            return targetProcess;
        }

        ComponentVersion versionInfo = componentsVersion.get(component.getComponentId());
        if(versionInfo == null) {
            return targetProcess;
        }

        for (VersionCommandDetail versionCommand : versionInfo.getVersionCommands()) {
            if(operatingSystem.equalsIgnoreCase(Constants.WINDOWS) && !versionCommand.getOperatingSystem().equalsIgnoreCase(Constants.WINDOWS)) {
                continue;
            }
            if(!operatingSystem.equalsIgnoreCase(Constants.WINDOWS) && versionCommand.getOperatingSystem().equalsIgnoreCase(Constants.WINDOWS)) {
                continue;
            }
            String versionCommandRelativePath = PathFinder.getApplicablePath(versionCommand.getPath());
            component.setRelativePathList(Collections.singletonList(versionCommandRelativePath));
            configFileDetails = new ConfigFileDetails(targetProcess, component, operatingSystem, DiscoveryItems.VERSION);
            logger.info("configFileDetails created for versionDiscovery for Process :{} : {}", targetProcess.getPid(), targetProcess.getProcessName());
            if (configFileDetails.isConfigFilePresent()) {

                if (DiscoveryMethod.BINARY_EXECUTE.getValue().equals(versionCommand.getMethod())) {
                    command = getApplicableCommand(component, configFileDetails, versionCommand);

                    if (Utils.isWindows() && component.getComponentId() == Constants.JBOSS_COMPONENT_ID) {
                        commandOutput = Utils.getCommandOutput(command, true, false);
                    } else {
                        commandOutput = Utils.getCommandOutput(command, false, false);
                    }
                    if (component.getComponentId() == Constants.NGINX_COMPONENT_ID) {
                        commandOutput = Utils.getCommandOutput(command, false, true);
                    }

                    logger.info("Version command output : {} ", commandOutput);

                    detectedVersion = extractVersionFromCommandOutput(commandOutput , component);
                }
                if (DiscoveryMethod.XPATH.getValue().equals(versionCommand.getMethod())) {
                    AttributeAccess attributeAccess = new AttributeAccess();
                    attributeAccess.setMethod(versionCommand.getMethod());
                    attributeAccess.setValue(versionCommand.getCommand());
                    attributeAccess.setPriority(1);
                    detectedVersion = ParserFactory.getInstance()
                            .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                            .xpathParse(attributeAccess, configFileDetails);
                }

                if (!StringUtils.isEmpty(detectedVersion)) {
                    String lookup = lookupVersionMapForComponent(versionInfo.getVersionMap(), detectedVersion);
                    if (!StringUtils.isEmpty(lookup)) {
                        detectedVersion = lookup;
                    }
                    markVersionAsDetected(component.getComponentId(), detectedVersion);
                    targetProcess.setComponentVersion(detectedVersion);
                    logger.info("The version detected for PROCESS : {} is {}", targetProcess.getProcessName(), detectedVersion);
                    targetProcess = checkHttpdComponentType(component, targetProcess, command);
                    break;
                } else {
                    logger.error("Component Version could not be detected");
                }
            }
        }

        return targetProcess;
    }

    public Process checkHttpdComponentType(Component component, Process targetProcess, String command)
    {
        if(component.getComponentId() != Constants.APACHE_HTTPD_COMPONENT_ID || command.isEmpty()) {
            return targetProcess;
        }
        logger.info("Trying to check if the PROCESS : {} is IHS or APACHE", targetProcess.getProcessName());
        String[] commandUtils = command.split("-v");
        command = commandUtils[0] + Constants.HTTPD_TYPE_COMMAND;
        String commandOutput = Utils.getCommandOutput(command, false, false);
        if(commandOutput != null && (commandOutput.toLowerCase().contains(Constants.IBM_ARGUMENT)))
        {
            targetProcess.setComponentId(Constants.IHS_COMPONENT_ID);
            targetProcess.setComponentTypeId(Constants.IHS_COMPONENT_TYPE_ID);
        }
        return targetProcess;
    }

    private String lookupVersionMapForComponent(Map<String, String> versionMap, String detectedVersion) {
        if (versionMap != null) {
            for (Map.Entry<String, String> entry : versionMap.entrySet()) {
                if (detectedVersion.startsWith(entry.getKey())) {
                    logger.info("version lookup: {} for the detectedVersion: {} ", entry.getValue(), detectedVersion);
                    return entry.getValue();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    private String getApplicableCommand(Component component, ConfigFileDetails configFileDetails, VersionCommandDetail versionBinary) {
        StringBuilder command = new StringBuilder();
        if (Utils.isWindows()) {
            if (!StringUtils.isEmpty(versionBinary.getPath())) {
                command.append("cd ")
                        .append("\"")
                        .append(configFileDetails.getComponentRoot())
                        .append("\"")
                        .append(" && ")
                        .append("\"")
                        .append(configFileDetails.getConfigFilePath())
                        .append("\"");
            }

            command.append(" ")
                    .append(versionBinary.getCommand());
            if (component.getComponentId() == Constants.IIS_COMPONENT_ID) {
                command.append(" | FindStr  /I version");
            }
            else if (component.getComponentId() != Constants.JBOSS_COMPONENT_ID && component.getComponentId() != Constants.MYSQL_COMPONENT_ID && component.getComponentId() != Constants.MSSQL_COMPONENT_ID && component.getComponentId() != Constants.IBM_COMPONENT_ID) {
                command.append(" | FindStr  version");
            }
        } else {
            if ((!new File(configFileDetails.getConfigFilePath()).isDirectory())) {
                if(configFileDetails.getConfigFilePath().contains(Constants.TEXT_FILE)) {
                    command.append(Constants.CAT_COMMAND).append(configFileDetails.getConfigFilePath());
                }
                else {
                    command.append(configFileDetails.getConfigFilePath()).append(Constants.SPACE);
                }
            }
            command.append(versionBinary.getCommand());
        }
        logger.info("Version command to be executed: {} ", command);

        return command.toString();
    }

    private void markVersionAsDetected(int componentId, String detectedVersion) {
        componentVersionDetected.put(componentId, detectedVersion);
    }

    private String extractVersionFromCommandOutput(String commandOutput, Component component) {
        String extractedVersion = StringUtils.EMPTY;
        if (!StringUtils.isEmpty(commandOutput)) {
            if (Utils.isWindows()) {
                if(component.getComponentName().toLowerCase().contains(Constants.IBM_ARGUMENT) && commandOutput.contains(":")) {
                    commandOutput = commandOutput.split(":")[1].trim();
                }
                if (commandOutput.contains(":") && commandOutput.contains("/")) {
                    commandOutput = commandOutput.split(":")[1].split("/")[1].trim();
                    commandOutput = commandOutput.split(Constants.SPACE_DELIMITER)[0].trim();
                }

                if (commandOutput.contains(".Final") && (commandOutput.contains("WildFly") || commandOutput.contains("SVNTag"))) {
                    String versionPart = commandOutput.split(".Final")[0];
                    String[] fragments = versionPart.split(Constants.SPACE_DELIMITER);
                    commandOutput = fragments[fragments.length - 1];
                }
                if (commandOutput.contains("REG_SZ")) {
                    String[] fragments = commandOutput.split(Constants.SPACE_DELIMITER);
                    commandOutput = fragments[fragments.length - 1];
                }
                if (commandOutput.contains("sql")) {
                    String[] fragments = commandOutput.split("Ver")[1].split(Constants.SPACE_DELIMITER);
                    commandOutput = fragments[1];
                }
                extractedVersion = commandOutput;
            } else if(component.getComponentId() == Constants.WEBSPHERE_COMPONENT_ID) {
                extractedVersion = getWebSphereVersion(commandOutput);
            } else {
                commandOutput = commandOutput.split(Constants.SPACE_DELIMITER)[0].trim();
                if (commandOutput.endsWith(".")) {
                    commandOutput = commandOutput.substring(0, commandOutput.length() - 1).trim();
                }
                extractedVersion = commandOutput;
            }
        }
        logger.info("Extracted Version: {} ", extractedVersion);

        return extractedVersion;
    }

    private String getWebSphereVersion(String commandOutput)
    {
        String version = StringUtils.EMPTY;
        try {
            String webSphereVersion = commandOutput.split(Constants.WEBSPHERE_VERSION_LOOKUP)[1].trim();
            String[] versionLines = webSphereVersion.split(Constants.NEXT_LINE);
            for (String line : versionLines) {
                if (line.toLowerCase().contains(Constants.VERSION_KEYWORD)) {
                    version = line.split(Constants.MULTIPLE_SPACE_PATTERN)[1].trim();
                    break;
                }
            }
        }catch (Exception e)
        {
            logger.error("Exception while parsing the WebSphere version : ", e);
        }
        return version;
    }
}