package com.appnomic.appsone.disco.component;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.DiscoveryItems;
import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.disco.entity.EntityDiscovery;
import com.appnomic.appsone.disco.version.VersionDiscovery;
import com.appnomic.appsone.parser.*;
import com.appnomic.appsone.prereq.PrerequisiteComponentScript;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.RunningProcess;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ComponentDiscovery {

    private static final Logger logger = LoggerFactory.getLogger(ComponentDiscovery.class);

    // List of known processes after pattern match
    private static final Map<Process, Component> processMarkedForDiscovery = new HashMap<>();

    // key = process pid , If the unique process discovered or not ?
    private static final Map<String, Boolean> processDiscoveredAsComponent = new HashMap<>();

    private static final Set<String> ibmMqQueueManagers = new HashSet<>();
    private static boolean discoveredIbmQmOnce = false;

    public ComponentDiscovery() {

    }
    public ComponentDiscovery(Set<String> ibmQMs) {
        ibmMqQueueManagers.addAll(ibmQMs);
    }

    public static String searchMysqlDefaultLocationsLinux(Component component) {
        logger.info("componentId = {} Searching default locations for config file", component.getComponentId());
        String attributeFound = StringUtils.EMPTY;
        for (String defaultLocation : PathFinder.getMysqlDefaultLocationsLinux()) {
            logger.info("MySql default {} location", defaultLocation);
            if (PathFinder.locateConfigFile(defaultLocation)) {
                ParseTarget parseTarget = new ParseTarget();
                parseTarget.setConfigFileName(defaultLocation);
                parseTarget.setAttributeName(component.getAttributes().get(1).getAttributeName());
                attributeFound = ParserFactory.getInstance()
                        .getParser(DiscoveryMethod.INI_PARSER.getValue())
                        .parse(parseTarget);
            }
        }
        if (StringUtils.isEmpty(attributeFound)) {
            logger.info("Reporting mysql default port {}, port not configured explicitly, ", Constants.DEFAULT_MYSQL_PORT);
            attributeFound = Constants.DEFAULT_MYSQL_PORT;
        }
        return attributeFound;
    }

    private Map<String, String> discoverAttributes(ConfigFileDetails configFileDetails, Component component, String hostAddress) {
        Map<String, String> attributesFound = new HashMap<>();
        logger.info("Attributes configured for the component:  {} : {}", component.getComponentName(), component.getAttributes());
        for (Attribute attribute : component.getAttributes()) {
            if (attribute.getIsMandatory() == 1) {
                List<AttributeAccess> allAccessForAttribute = attribute.getAccess();
                Collections.sort(allAccessForAttribute, new Comparator<AttributeAccess>() {
                    @Override
                    public int compare(AttributeAccess access1, AttributeAccess access2) {
                        return access2.getPriority() - access1.getPriority();
                    }
                });
                for (AttributeAccess access : allAccessForAttribute) {
                    logger.info("COMPONENT: {}, Trying to find Attribute : {} with AttributeAccess : {}", component.getComponentName(), attribute.getAttributeName(), access.toString());
                    if (!StringUtils.isEmpty(access.getMethod()) &&
                            !StringUtils.isEmpty(access.getValue()) &&
                            !access.getMethod().equals(DiscoveryMethod.INHERIT.getValue())) {
                        String attributeFound;

                        ParseTarget parseTarget = new ParseTarget();
                        parseTarget.setConfigFileName(configFileDetails.getConfigFilePath());
                        parseTarget.setServerRoot(configFileDetails.getComponentRoot());
                        parseTarget.setAttributeName(access.getValue());

                        if (access.getMethod().equals(DiscoveryMethod.XPATH.getValue())) {

                            attributeFound = ParserFactory.getInstance()
                                    .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                                    .xpathParse(access, configFileDetails);
                            if (!StringUtils.isEmpty(attributeFound)) {
                                logger.info("The attribute found  :{} : {} from : {} using method : {}", parseTarget.getAttributeName(), attributeFound, configFileDetails.getConfigFilePath(), access.getMethod());
                                attributesFound.put(attribute.getAttributeName(), attributeFound);
                                break;
                            }
                        }
                        if (access.getMethod().equals(DiscoveryMethod.FILE_PARSER.getValue())) {
                            attributeFound = ParserFactory.getInstance()
                                    .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                                    .fileParser(access, configFileDetails, parseTarget);
                            if (!StringUtils.isEmpty(attributeFound)) {
                                logger.info("The attribute found  :{} : {} from : {} using method : {}", parseTarget.getAttributeName(), attributeFound, configFileDetails.getConfigFilePath(), access.getMethod());
                                attributesFound.put(attribute.getAttributeName(), attributeFound);
                                break;
                            }
                        }

                        if (access.getMethod().equals(DiscoveryMethod.REG_EX.getValue())) {
                            attributeFound = ParserFactory.getInstance()
                                    .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                                    .regExParse(access, configFileDetails, component);
                            if (!StringUtils.isEmpty(attributeFound)) {
                                logger.info("The attribute found  :{} : {} from : {} using method : {}", parseTarget.getAttributeName(), attributeFound, configFileDetails.getConfigFilePath(), access.getMethod());
                                attributesFound.put(attribute.getAttributeName(), attributeFound);
                                break;
                            }
                        }

                        if (access.getMethod().equals(DiscoveryMethod.APACHE_PARSER.getValue()) ||
                                access.getMethod().equals(DiscoveryMethod.INI_PARSER.getValue()) ||
                                access.getMethod().equals(DiscoveryMethod.ORA_PARSER.getValue()) ||
                                access.getMethod().equals(DiscoveryMethod.NGINX_PARSER.getValue()) ||
                                access.getMethod().equals(DiscoveryMethod.PROPERTY_PARSER.getValue())) {

                            attributeFound = ParserFactory.getInstance()
                                    .getParser(access.getMethod())
                                    .parse(parseTarget);

                            if (!StringUtils.isEmpty(attributeFound)) {
                                logger.info("The attribute found  :{} : {} from : {} using method : {}", parseTarget.getAttributeName(), attributeFound, configFileDetails.getConfigFilePath(), access.getMethod());
                                attributesFound.put(attribute.getAttributeName(), attributeFound);
                                break;
                            }
                        }
                        if (access.getMethod().equals(DiscoveryMethod.BINARY_EXECUTE.getValue())) {
                            WindowsRegistryParser windowsRegistryParser = new WindowsRegistryParser();
                            attributeFound = windowsRegistryParser.parse(access.getValue());

                            if (!StringUtils.isEmpty(attributeFound)) {
                                logger.info("The attribute found  :{} : {} from : {} using method : {}", parseTarget.getAttributeName(), attributeFound, configFileDetails.getConfigFilePath(), access.getMethod());
                                attributesFound.put(attribute.getAttributeName(), attributeFound);
                                break;
                            }
                        }
                    }
                    if (component.getComponentName().toLowerCase().contains(Constants.HTTPD_COMMON_COMPONENT_NAME) && attribute.getAttributeName().equalsIgnoreCase(Constants.PROTOCOL)) {
                        String monitorPort = attributesFound.get(Constants.MONITOR_PORT);
                        String protocolAttribute = StringUtils.EMPTY;
                        if (monitorPort != null && !monitorPort.isEmpty()) {
                            protocolAttribute = discoverProtocolAttribute(hostAddress, monitorPort, component, access.getValue());
                        }
                        if (!protocolAttribute.isEmpty()) {
                            attributesFound.put(Constants.PROTOCOL, protocolAttribute);
                        }
                    }
                }
            }
        }
        return attributesFound;
    }

    private List<Process> handleComponentsNotDiscovered(List<Process> processList, String hostAddress, String operatingSystem, VersionDiscovery versionDiscovery) {
        EntityDiscovery entityDiscovery = new EntityDiscovery();
        Set<Process> uniqueProcesses = new HashSet<>(processList);
        logger.info("Known Process  but not yet discovered as Component . So using default values for attributes: {}", processMarkedForDiscovery.keySet());
        for (Map.Entry<Process, Component> entry : processMarkedForDiscovery.entrySet()) {
            Component component = entry.getValue();
            Process process = entry.getKey();
            Map<String, String> attributesFound = new HashMap<>();
            String attribute;
            if (process != null && !processDiscoveredAsComponent.get(process.getPid())) {

                attribute = String.valueOf(YamlFileLoader.getDefaultMonitorPortForComponent(component.getComponentId()));

                if (component.getComponentId() == 18) {
                    attribute = searchMysqlDefaultLocationsLinux(component);
                }

                if (attribute != null) {

                    List<Server> localHost = new ArrayList<>();

                    Server server = new Server();
                    server.setServerName("localhost");
                    server.setAddress("localhost");
                    server.setPortNumber(Integer.parseInt(attribute));

                    localHost.add(server);

                    localHost = Utils.checkPortAvailability(localHost);

                    if (!localHost.get(0).isAvailable()) {

                        logger.info("reporting default monitor port: {} for component: {} ", attribute, component.getComponentName());
                        attributesFound.put(Constants.HOST_ADDRESS, hostAddress);
                        attributesFound.put(Constants.MONITOR_PORT, attribute);
                        process.setAttributes(attributesFound);
                        process.setComponentId(component.getComponentId());
                        process.setComponentTypeId(component.getComponentTypeId());
                        process = versionDiscovery.run(process, component, operatingSystem);
                        if (process.getComponentVersion() != null && !process.getComponentVersion().isEmpty()) {
                            ConfigFileDetails configFileDetails = new ConfigFileDetails(process, component, operatingSystem, DiscoveryItems.COMPONENT);
                            Map<String, List<String>> kpis = entityDiscovery.run(configFileDetails, process.getComponentVersion());
                            process.setKpis(kpis);
                        }
                        uniqueProcesses.add(process);
                    }
                }
            }
        }
        processList.clear();
        processList.addAll(uniqueProcesses);
        return processList;
    }

    public List<Process> run(List<Process> processList, Host host, String runningProcess) {
        List<Process> result = new ArrayList<>();
        List<Component> autoDiscoveryComponentsUnModified = YamlFileLoader.getAutoDiscoveryComponents();
        VersionDiscovery versionDiscovery = new VersionDiscovery(autoDiscoveryComponentsUnModified);
        PrerequisiteComponentScript prerequisiteComponentScript = new PrerequisiteComponentScript();
        EntityDiscovery entityDiscovery = new EntityDiscovery();

        List<List<Process>> segregatedProcesses = findUniqueprocessesforDiscovery(processList);
        List<Process> uniqueProcesses = segregatedProcesses.get(0);
        List<Process> nonUniqueProcesses = segregatedProcesses.get(1);

        String hostAddress = Utils.getHostAddress(host.getNetworkInterfaces());
        String operatingSystem = host.getOperatingSystem();
        for (Process process : uniqueProcesses) {
            logger.info("Starting the component discovery for process : {}", process);
            YamlFileLoader.refreshComponentsFromYamlFile();
            List<Component> autoDiscoveryComponents = YamlFileLoader.getAutoDiscoveryComponents();
            for (Component component : autoDiscoveryComponents) {
                BasicParser basicParser = new BasicParser();
                ConfigFileDetails configFileDetails = basicParser.matchProcess(process, component, operatingSystem);
                if (configFileDetails == null) {
                    logger.info("Inside ComponentDiscovery  for the PROCESS: {} and COMPONENT : {} , Its  CONFIG_FILE_DETAILS is null and will be ignored.", process.getProcessName(), component.getComponentName());
                    continue;
                }
                if(component.getComponentName().equalsIgnoreCase(Constants.IBM_MQ_COMPONENT_NAME) && !continueIbmMQDiscovery(component, configFileDetails)){
                    continue;
                }
                logger.info("Process marked for Discovery .  PROCESS: {} and COMPONENT : {} , Its  CONFIG_FILE_DETAILS is {}", process.getProcessName(), component.getComponentName(), configFileDetails);
                processMarkedForDiscovery.put(process, component);
                processDiscoveredAsComponent.put(process.getPid(), false);
                if (component.getComponentName().equalsIgnoreCase(Constants.APACHE_TOMCAT_COMPONENT_NAME) && !configFileDetails.isConfigFilePresent()) {
                    process = setWorkingDirectory(component, process);
                    configFileDetails = basicParser.matchProcess(process, component, operatingSystem);
                }
                process.setComponentId(component.getComponentId());
                process.setComponentTypeId(component.getComponentTypeId());
                if (host.isEnableScriptExecRequested()) {
                    process.setEnableScriptExecStatus(prerequisiteComponentScript.run(configFileDetails, host.getScriptPath(), operatingSystem, false));
                }
                if (host.isValidationScriptExecRequested()) {
                    process.setValidationScriptExecStatus(prerequisiteComponentScript.run(configFileDetails, host.getScriptPath(), operatingSystem, true));
                }
                if (component.getComponentName().equalsIgnoreCase(Constants.FINACLE_COMPONENT_NAME)) {
                    process = discoverFinacleAttributes(process, component, runningProcess, hostAddress);
                } else {
                    Map<String, String> attributesFound = discover(process, component, hostAddress, configFileDetails);
                    if (attributesFound != null && !attributesFound.isEmpty()) {
                        process.setAttributes(attributesFound);
                    }
                    process = versionDiscovery.run(process, component, operatingSystem);
                    Map<String, List<String>> kpis = entityDiscovery.run(configFileDetails, process.getComponentVersion());
                    process.setKpis(kpis);
                }
            }
        }
        uniqueProcesses = handleComponentsNotDiscovered(uniqueProcesses, hostAddress, operatingSystem, versionDiscovery);

        result.addAll(uniqueProcesses);
        result.addAll(nonUniqueProcesses);
        printSummaryLogs(autoDiscoveryComponentsUnModified, result);
        return result;
    }


    private void setIbmMqQueueManagers(){
        discoveredIbmQmOnce = true;
        try {
            String commandOutput = Utils.getCommandOutput(Constants.IBM_MQ_QUEUE_MANAGER_COMMAND, false, false);
            Matcher patternMatcher = Pattern.compile(Constants.IBM_MQ_QUEUE_MANAGER_REGEX).matcher(commandOutput);
            while (patternMatcher.find()) {
                String qManager = patternMatcher.group(1).split(Constants.IBM_MQ_QUEUE_MANAGER_SPLIT_PATTERN)[1];
                ibmMqQueueManagers.add(qManager.substring(1, qManager.length() - 1));
            }
            if (ibmMqQueueManagers.isEmpty()) {
                logger.info("There are no active IBM MQ Queue Managers");
            }else {
                logger.info("Number of active QManagers of IBM MQ : {} and QManagers : {}", ibmMqQueueManagers.size(), ibmMqQueueManagers);
            }
        }catch (Exception e){
            logger.error("Exception in setIbmMqQueueManagers : ", e);
        }
    }

    private boolean continueIbmMQDiscovery(Component component, ConfigFileDetails configFileDetails){
        if(!discoveredIbmQmOnce) {
            setIbmMqQueueManagers();
        }
        if(ibmMqQueueManagers.isEmpty()){
            return false;
        }
        AttributeAccess access = new AttributeAccess();
        access.setMethod(DiscoveryMethod.REG_EX.getValue());
        access.setValue(Constants.IBM_MQ_ATTRIBUTE_REGEX);
        access.setPriority(1);
        String currentQM = ParserFactory.getInstance()
                .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                .regExParse(access, configFileDetails, component);
        if(StringUtils.isEmpty(currentQM)){
            return false;
        }
        if(ibmMqQueueManagers.contains(currentQM)){
            ibmMqQueueManagers.remove(currentQM);
            return true;
        }
        return false;
    }

    public Process discoverFinacleAttributes(Process process, Component component, String runningProcesses, String hostAddress) {
        logger.info("Discovering Finacle Component Attributes");
        try {
            List<RunningProcess> runningProcessesFromCmdLine = fetchRunningprocess(runningProcesses);
            if (runningProcessesFromCmdLine.isEmpty()) {
                logger.warn("No running processes found for Finacle component");
                return process;
            }
            Map<String, String> attributesFound = new HashMap<>();
            attributesFound.put(Constants.HOST_ADDRESS, hostAddress);

            List<RunningProcess> childProcesses = new ArrayList<>();
            for (RunningProcess runningProcess : runningProcessesFromCmdLine) {
                if (runningProcess.getPpid().equals(process.getPid())) {
                    childProcesses.add(runningProcess);
                }
            }

            if (!childProcesses.isEmpty()) {
                try {
                    String[] finacleWorkingDirectory = childProcesses.get(0).getCmd().split("/bin");
                    if (finacleWorkingDirectory.length >= 1 && finacleWorkingDirectory[0] != null) {
                        process.setProcessCurrentWorkingDirectory(finacleWorkingDirectory[0]);
                        logger.debug("Finacle working directory set to: {}", finacleWorkingDirectory[0]);
                    }
                } catch (Exception e) {
                    logger.error("Finacle working directory is empty");
                }
            }
            for (Attribute attribute : component.getAttributes()) {
                logger.trace("Finding attribute {} for {} component", attribute.getAttributeName(), component.getComponentName());
                if (attributesFound.containsKey(attribute.getAttributeName())) {
                    continue;
                }
                if (attribute.getAttributeName().equalsIgnoreCase(Constants.FINACLE_LIMO_SERVER_ATTRIBUTE_NAME)) {
                    String[] limoServer = process.getProcessArgs().toLowerCase().split(attribute.getAccess().get(0).getValue());
                    attributesFound.put(attribute.getAttributeName(), limoServer[1].trim());
                    logger.debug("Finacle LIMO server found: {}", limoServer[1].trim());
                    continue;
                }
                for (AttributeAccess access : attribute.getAccess()) {
                    for (RunningProcess childProcess : childProcesses) {
                        if (attribute.getAttributeName().equalsIgnoreCase(Constants.FINACLE_LI_SERVER_ATTRIBUTE_NAME) && childProcess.getCmd().contains(access.getValue()) && !childProcess.getCmd().contains(Constants.FINACLE_BCMON_SERVER_SEARCH_KEYWORD) && !childProcess.getCmd().contains(Constants.FINACLE_BCECHO_SERVER_SEARCH_KEYWORD)) {
                            attributesFound.put(attribute.getAttributeName(), childProcess.getCmd());
                            logger.debug("Finacle LI server found: {}", childProcess.getCmd());
                            break;
                        } else if (attribute.getAttributeName().equalsIgnoreCase(Constants.FINACLE_BCMON_SERVER_ATTRIBUTE_NAME) && childProcess.getCmd().contains(access.getValue()) && !childProcess.getCmd().contains(Constants.FINACLE_ECHO_SERVER_SEARCH_KEYWORD)) {
                            attributesFound.put(attribute.getAttributeName(), childProcess.getCmd());
                            logger.debug("Finacle BCMON server found: {}", childProcess.getCmd());
                            break;
                        } else if (childProcess.getCmd().contains(access.getValue())) {
                            attributesFound.put(attribute.getAttributeName(), childProcess.getCmd());
                            logger.debug("Finacle component attribute found: {}", childProcess.getCmd());
                            break;
                        }
                    }
                }
                if (attributesFound.containsKey(attribute.getAttributeName())) {
                    continue;
                }
                attributesFound.put(attribute.getAttributeName(), Constants.DEFAULT_ATTRIBUTE_VALUE);
                logger.debug("Finacle component attribute set to default value: {}", Constants.DEFAULT_ATTRIBUTE_VALUE);
            }

            logger.info("The RUNNING PROCESS :{} is successfully discovered as COMPONENT : {} ", process.getPid(), component.getComponentName());
            processDiscoveredAsComponent.put(process.getPid(), true);
            process.setAttributes(attributesFound);
            process.setComponentVersion(Arguments.getFinacleVersion());
            return process;
        } catch (Exception e) {
            logger.error("Error occurred while discovering Finacle component attributes: {}", e.getMessage(), e);
            return process;
        }
    }

    public List<RunningProcess> fetchRunningprocess(String runningProcessFromCmd) {
        logger.info("Fetching running process for Finacle Component");
        if (runningProcessFromCmd.isEmpty()) {
            logger.debug("Running process command is empty for finacle component");
            return Collections.emptyList();
        }
        List<RunningProcess> runningProcesses = new ArrayList<>();
        try {
            String[] processes = runningProcessFromCmd.split("\n");
            logger.trace("Finacle Processes found from command : {}", processes);
            for (String process : processes) {
                String[] proDetail = process.trim().split("[ \t]+");
                //PID   PPID  ARG
                // 0     1    2
                if (proDetail.length == 3 && proDetail[0] != null && proDetail[1] != null && proDetail[2] != null) {
                    RunningProcess runningProcess = new RunningProcess();
                    runningProcess.setPid(proDetail[0].trim());//PID
                    runningProcess.setPpid(proDetail[1].trim());//PPID
                    runningProcess.setCmd(proDetail[2].trim());//ARG
                    runningProcesses.add(runningProcess);
                }
            }
        } catch (Exception e) {
            logger.error("Error occurred while fetching running process: {}", e.getMessage());
            return Collections.emptyList();
        }
        logger.info("Running process found for finacle component : {}", runningProcesses);
        return runningProcesses;
    }


    private List<List<Process>> findUniqueprocessesforDiscovery(List<Process> processList) {
        List<String> processBlackList = YamlFileLoader.getProcessBlackList();
        List<List<Process>> result = new ArrayList<>();
        List<Process> uniqueProcesses = new ArrayList<>();
        Set<String> uniqueProcessArgs = new HashSet<>();
        List<Process> nonUniqueProcesses = new ArrayList<>();
        boolean ignore;
        for (Process process : processList) {
            ignore = false;
            if (!uniqueProcessArgs.contains(process.getProcessArgs())) {
                for (String blackListedProcess : processBlackList) {
                    if (!process.getProcessName().toLowerCase().contains("dummy") && process.getProcessName().endsWith(blackListedProcess)) {
                        nonUniqueProcesses.add(process);
                        ignore = true;
                        break;
                    }
                }
                if (!ignore) {
                    uniqueProcessArgs.add(process.getProcessArgs());
                    uniqueProcesses.add(process);
                }
            } else {
                nonUniqueProcesses.add(process);
            }
        }
        result.add(uniqueProcesses);
        result.add(nonUniqueProcesses);
        return result;
    }

    private void printSummaryLogs(List<Component> autoDiscoveryComponents, List<Process> processList) {
        try {
            Map<Integer, String> componentMap = new HashMap<>();
            for (Component component : autoDiscoveryComponents) {
                componentMap.put(component.getComponentId(), component.getComponentName());
            }
            Map<String, Integer> componentsDiscovered = new HashMap<>();
            Map<String, Integer> knownComponents = new HashMap<>();
            for (Process process : processList) {
                String componentName = componentMap.get(process.getComponentId());
                int disCompInsCount = 1;
                if (process.getAttributes() != null && !process.getAttributes().isEmpty()) {
                    if (componentsDiscovered.containsKey(componentName)) {
                        disCompInsCount = componentsDiscovered.get(componentName) + 1;
                    }
                    componentsDiscovered.put(componentName, disCompInsCount);
                }
                int knownCompInsCount = 1;
                if (process.getComponentId() != 0) {
                    if (knownComponents.containsKey(componentName)) {
                        knownCompInsCount = knownComponents.get(componentName) + 1;
                    }
                    knownComponents.put(componentName, knownCompInsCount);
                }
            }
            logger.info("The DiscoveredComponents : {}", componentsDiscovered);
            logger.info("The KnownComponents : {}", knownComponents);
        } catch (Exception e) {
            logger.error("Exception while printing summary : {}", e.getMessage());
        }
    }

    private Process setWorkingDirectory(Component component, Process process) {
        if (component.getComponentName().equalsIgnoreCase(Constants.APACHE_TOMCAT_COMPONENT_NAME)) {
            logger.info("Setting the current working directory for Tomcat process : {}", process.getPid());
            process = tomcatWorkingDirectory(process);
        }

        return process;
    }

    private Process tomcatWorkingDirectory(Process process) {
        if (!process.getProcessArgs().isEmpty()) {
            return process;
        }
        String[] processArgs = process.getProcessArgs().split(" ");
        for (String argument : processArgs) {
            if (argument.toLowerCase().contains(Constants.TOMCAT_ARGUMENT)) {
                String[] arg = argument.split("=");
                if (arg.length >= 2 && !arg[1].isEmpty()) {
                    logger.info("Changing the working directory of the TOMCAT process from : {} to {}", process.getProcessCurrentWorkingDirectory(), arg[1]);
                    process.setProcessCurrentWorkingDirectory(arg[1]);
                    break;
                }
            }
        }
        return process;
    }


    private Map<String, String> discover(Process process, Component component, String hostAddress, ConfigFileDetails configFileDetails) {
        Map<String, String> attributesFound = new HashMap<>();
        if (!component.getComponentName().equalsIgnoreCase(Constants.IBM_MQ_COMPONENT_NAME) && (configFileDetails == null || !configFileDetails.isConfigFilePresent())) {
            return attributesFound;
        }
        logger.info("Calling Attribute discovery for process :{}", process.getProcessName());
        attributesFound = discoverAttributes(configFileDetails, component, hostAddress);
        if (!attributesFound.isEmpty()) {
            attributesFound.put(Constants.HOST_ADDRESS, hostAddress);
            logger.info("The RUNNING PROCESS :{} is successfully discovered as COMPONENT : {} ", process.getPid(), component.getComponentName());
            processDiscoveredAsComponent.put(process.getPid(), true);
            if (configFileDetails != null) {
                if (configFileDetails.isConfigFilePresent() || component.getComponentId() == Constants.MSSQL_COMPONENT_ID) {
                    logger.info("Calling Attribute discovery for process :{}", process.getProcessName());
                    attributesFound = discoverAttributes(configFileDetails, component, hostAddress);
                    if (!attributesFound.isEmpty()) {
                        attributesFound.put(Constants.HOST_ADDRESS, hostAddress);
                        if (component.getComponentName().toLowerCase().contains(Constants.HTTPD_COMMON_COMPONENT_NAME)) {
                            attributesFound.put(Constants.INSTALLATION_PATH, process.getProcessCurrentWorkingDirectory());
                        }
                        logger.info("The RUNNING PROCESS :{} is successfully discovered as COMPONENT : {} ", process.getPid(), component.getComponentName());
                        processDiscoveredAsComponent.put(process.getPid(), true);
                    }
                }
            }
        }
        return attributesFound;
    }

    private String discoverProtocolAttribute(String hostAddress, String monitorPort, Component component, String accessValue) {
        {
            String protocolAttributeFound = StringUtils.EMPTY;
            try {

                String commandoutput = Utils.getCommandOutput(accessValue + Constants.SPACE + hostAddress + ":" + monitorPort, false, false);
                if (!commandoutput.isEmpty()) {
                    protocolAttributeFound = commandoutput.contains(Constants.HTTPS_PROTOCOL_CHECK) ? Constants.HTTPS_PROTOCOL : Constants.HTTP_PROTOCOL;
                }
            } catch (Exception e) {
                logger.error("Error Finding protocol for :{} {}", component.getComponentName(), e);
            }
            return protocolAttributeFound;
        }
    }
}
