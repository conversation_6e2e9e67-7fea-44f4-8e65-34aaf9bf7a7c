package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.parser.apache.ApacheFileParser;
import com.appnomic.appsone.parser.ini.IniFileParser;
import com.appnomic.appsone.parser.nginx.NginxParser;
import com.appnomic.appsone.parser.ora.OraFileParser;
import com.appnomic.appsone.parser.property.PropertyParser;

import java.util.HashMap;
import java.util.Map;

public class ParserFactory {
    private static ParserFactory _this;
    private final Map<String, Parser> parsers;

    private ParserFactory() {
        this.parsers = new HashMap<>();
        this.parsers.put(DiscoveryMethod.INI_PARSER.getValue(), new IniFileParser());
        this.parsers.put(DiscoveryMethod.ORA_PARSER.getValue(), new OraFileParser());
        this.parsers.put(DiscoveryMethod.APACHE_PARSER.getValue(), new ApacheFileParser());
        this.parsers.put(DiscoveryMethod.BASIC_PARSER.getValue(), new BasicParser());
        this.parsers.put(DiscoveryMethod.NGINX_PARSER.getValue(), new NginxParser());
        this.parsers.put(DiscoveryMethod.PROPERTY_PARSER.getValue(), new PropertyParser());
    }

    public static ParserFactory getInstance() {
        if (_this == null) {
            _this = new ParserFactory();
        }
        return _this;
    }

    public Parser getParser(String parserName) {
        return this.parsers.get(parserName);
    }
}
