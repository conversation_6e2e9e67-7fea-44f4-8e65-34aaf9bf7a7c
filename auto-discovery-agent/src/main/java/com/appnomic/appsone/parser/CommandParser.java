package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.beans.discovery.Agents;
import com.appnomic.appsone.common.beans.discovery.DiscoveryAgentArgs;
import com.appnomic.appsone.common.beans.discovery.Server;
import com.appnomic.appsone.common.beans.discovery.SupportedArgs;
import com.appnomic.appsone.common.enums.DiscoveryMode;
import com.appnomic.appsone.common.util.StringUtils;
import com.appnomic.appsone.util.Utils;
import org.apache.commons.cli.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class CommandParser {

    private final Logger log = LoggerFactory.getLogger(CommandParser.class);

    private final CommandLineParser parser = new DefaultParser();
    private final Options options = new Options();
    private final String[] commandLineArgs;

    public CommandParser(String[] args) {

        for (SupportedArgs supportedArg : SupportedArgs.values()) {
            Option option = new Option(supportedArg.getOpt(), supportedArg.getLongOpt(), supportedArg.isArgPresent(), supportedArg.getDescription());
            option.setRequired(supportedArg.isRequired());
            options.addOption(option);
        }
        commandLineArgs = args.clone();
    }

    public DiscoveryAgentArgs parse() throws Exception {

        CommandLine cmd = parser.parse(options, commandLineArgs);

        String modeFoundInArgs = cmd.getOptionValue(SupportedArgs.MODE.getLongOpt());
        String resultsDirFoundInArgs = cmd.getOptionValue(SupportedArgs.PATH.getLongOpt());
        String ccEndpointFoundInArgs = cmd.getOptionValue(SupportedArgs.CC_URL.getLongOpt());

        String componentAgentUid = cmd.getOptionValue(SupportedArgs.COMPONENT_AGENT_UID.getLongOpt());
        String logForwarderUid = cmd.getOptionValue(SupportedArgs.LOG_FORWARDER_UID.getLongOpt());
        String psAgentUid = cmd.getOptionValue(SupportedArgs.PS_AGENT_UID.getLongOpt());
        String jimAgentUid = cmd.getOptionValue(SupportedArgs.JIM_AGENT_UID.getLongOpt());
        String forensicAgentUid = cmd.getOptionValue(SupportedArgs.FORENSIC_AGENT_UID.getLongOpt());
        String supervisorUid = cmd.getOptionValue(SupportedArgs.SUPERVISOR_UID.getLongOpt());

        String prcScriptPath = cmd.getOptionValue(SupportedArgs.PRC_SCRIPT_PATH.getLongOpt());
        String prcScriptEnable = cmd.getOptionValue(SupportedArgs.PRC_SCRIPT_ENABLE.getLongOpt());
        String prcScriptValidate = cmd.getOptionValue(SupportedArgs.PRC_SCRIPT_VALIDATE.getLongOpt());

        String shellCmdPath = cmd.getOptionValue(SupportedArgs.SHELL_COMMAND_PATH.getLongOpt());

        String yamlFilePath = cmd.getOptionValue(SupportedArgs.YAML_FILE_PATH.getLongOpt());

        String readHttpdConfigSubFiles = cmd.getOptionValue(SupportedArgs.READ_HTTPD_CONFIG_SUBFILES.getLongOpt());

        String finacleVersion = cmd.getOptionValue(SupportedArgs.FINACLE_VERSION.getLongOpt());

        DiscoveryMode mode = DiscoveryMode.getMode(modeFoundInArgs);

        DiscoveryAgentArgs discoveryAgentArgs = new DiscoveryAgentArgs();
        Agents agents = new Agents();
        agents.setComponentAgentUid(componentAgentUid);
        agents.setLogForwarderUid(logForwarderUid);
        agents.setPsAgentUid(psAgentUid);
        agents.setJimAgentUid(jimAgentUid);
        agents.setForensicAgentUid(forensicAgentUid);
        agents.setSupervisorUid(supervisorUid);

        discoveryAgentArgs.setAgents(agents);
        discoveryAgentArgs.setScriptPath(prcScriptPath);
        discoveryAgentArgs.setScriptEnable(Boolean.parseBoolean(prcScriptEnable));
        discoveryAgentArgs.setScriptValidate(Boolean.parseBoolean(prcScriptValidate));
        discoveryAgentArgs.setShellCmdPath(shellCmdPath);
        discoveryAgentArgs.setYamlFilePath(yamlFilePath);
        discoveryAgentArgs.setReadHttpdConfigSubFiles(readHttpdConfigSubFiles);
        discoveryAgentArgs.setFinacleVersion(finacleVersion);

        boolean prcFlagInCommand = cmd.hasOption(SupportedArgs.PRE_REQ_CHECK.getLongOpt());

        if (prcFlagInCommand == true) {

            String prcVal = cmd.getOptionValue(SupportedArgs.PRE_REQ_CHECK.getLongOpt());
            if (Boolean.parseBoolean(prcVal)) {
                discoveryAgentArgs.setPreReqCheck(true);

                boolean osLocations = cmd.hasOption(SupportedArgs.LOCATIONS.getLongOpt());

                if (osLocations) {
                    String osLocationsVal = cmd.getOptionValue(SupportedArgs.LOCATIONS.getLongOpt());
                    List<String> locationsInCommand = Utils.extractLocations(osLocationsVal);
                    if (locationsInCommand != null && !locationsInCommand.isEmpty()) {
                        discoveryAgentArgs.setLocations(locationsInCommand);
                    }
                }

                List<Server> serversFoundInCommand = new ArrayList<>();

                boolean grpc = cmd.hasOption(SupportedArgs.GRPC.getLongOpt());
                if (grpc) {
                    String grpcVal = cmd.getOptionValue(SupportedArgs.GRPC.getLongOpt());
                    Server server = Utils.extractServerDetails(SupportedArgs.GRPC.getLongOpt(), grpcVal);
                    if (server != null) {
                        serversFoundInCommand.add(server);
                    }
                }
                boolean haProxy = cmd.hasOption(SupportedArgs.HA_PROXY.getLongOpt());
                if (haProxy) {
                    String haProxyVal = cmd.getOptionValue(SupportedArgs.HA_PROXY.getLongOpt());
                    Server server = Utils.extractServerDetails(SupportedArgs.HA_PROXY.getLongOpt(), haProxyVal);
                    if (server != null) {
                        serversFoundInCommand.add(server);
                    }
                }
                boolean keyCloak = cmd.hasOption(SupportedArgs.KEY_CLOAK.getLongOpt());
                if (keyCloak) {
                    String keyCloakVal = cmd.getOptionValue(SupportedArgs.KEY_CLOAK.getLongOpt());
                    Server server = Utils.extractServerDetails(SupportedArgs.KEY_CLOAK.getLongOpt(), keyCloakVal);
                    if (server != null) {
                        serversFoundInCommand.add(server);
                    }
                }
                boolean jaegar = cmd.hasOption(SupportedArgs.JAEGAR.getLongOpt());
                if (jaegar) {
                    String jaegarVal = cmd.getOptionValue(SupportedArgs.JAEGAR.getLongOpt());
                    Server server = Utils.extractServerDetails(SupportedArgs.JAEGAR.getLongOpt(), jaegarVal);
                    if (server != null) {
                        serversFoundInCommand.add(server);
                    }
                }
                discoveryAgentArgs.setServers(serversFoundInCommand);
            } else {
                if (!StringUtils.isEmpty(prcVal) && !prcVal.equals("false")) {
                    log.error("Invalid option for -prc {}", prcVal);
                }
                discoveryAgentArgs.setPreReqCheck(false);
            }
        }

        if (DiscoveryMode.ONLINE.equals(mode)) {
            discoveryAgentArgs.setMode(DiscoveryMode.ONLINE);
            if (StringUtils.isEmpty(ccEndpointFoundInArgs)) {
                log.error("Invalid Control Centre Endpoint: {}", ccEndpointFoundInArgs);
                throw new Exception("Invalid Control Centre Endpoint: " + ccEndpointFoundInArgs);
            }
            log.info("Control Centre Endpoint: {}", ccEndpointFoundInArgs);
            discoveryAgentArgs.setCcUrl(ccEndpointFoundInArgs);
        } else if (DiscoveryMode.OFFLINE.equals(mode)) {
            discoveryAgentArgs.setMode(DiscoveryMode.OFFLINE);
            if (StringUtils.isEmpty(resultsDirFoundInArgs)) {
                log.error("Invalid output directory: {}", resultsDirFoundInArgs);
                throw new Exception("Invalid output directory: " + resultsDirFoundInArgs);
            }
            discoveryAgentArgs.setResultsDir(resultsDirFoundInArgs);
            log.info("Output files to be generated in folder: {}", resultsDirFoundInArgs);

        } else if (DiscoveryMode.DUAL.equals(mode)) {
            discoveryAgentArgs.setMode(DiscoveryMode.DUAL);
            if (StringUtils.isEmpty(ccEndpointFoundInArgs)) {
                log.error("Invalid Control Centre Endpoint: {}", ccEndpointFoundInArgs);
                throw new Exception("Invalid Control Centre Endpoint: " + ccEndpointFoundInArgs);
            }
            log.info("Control Centre Endpoint: {}", ccEndpointFoundInArgs);
            discoveryAgentArgs.setCcUrl(ccEndpointFoundInArgs);
            if (StringUtils.isEmpty(resultsDirFoundInArgs)) {
                log.error("Invalid output directory: {}", resultsDirFoundInArgs);
                throw new Exception("Invalid output directory: " + resultsDirFoundInArgs);
            }
            discoveryAgentArgs.setResultsDir(resultsDirFoundInArgs);
            log.info("Output files to be generated in folder: {}", resultsDirFoundInArgs);

        } else {
            log.error("Invalid Mode for discovery agent: {} ", mode);
            throw new Exception("Invalid Mode: " + mode);
        }

        return discoveryAgentArgs;
    }

}
