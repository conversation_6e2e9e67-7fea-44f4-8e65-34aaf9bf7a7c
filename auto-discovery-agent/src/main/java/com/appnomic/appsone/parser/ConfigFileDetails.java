package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.beans.discovery.*;
import com.appnomic.appsone.common.enums.DiscoveryItems;
import com.appnomic.appsone.common.enums.DiscoveryMethod;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import io.netty.util.internal.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfigFileDetails {

    private static final Logger logger = LoggerFactory.getLogger(ConfigFileDetails.class);
    private String configFilePath;
    private String componentRoot;
    private String processArgs;
    private boolean configFilePresent;
    private int componentId;

    private String componentName;

    public ConfigFileDetails(Process process, Component component, String operatingSystem, DiscoveryItems discoveryItems) {
        String componentRoot = StringUtils.EMPTY;
        logger.info("Initializing ConfigFileDetails for component: " + component.getComponentName());
        if(component.getComponentName().equalsIgnoreCase(Constants.FINACLE_COMPONENT_NAME)) {
            configFilePath = null;
        }
        else {
            componentRoot = getComponentRoot(process, component);
            if (Utils.isWindows() && PathFinder.isCwdNotUsable(process)) {
                process.setProcessCurrentWorkingDirectory(componentRoot);
            }
            if (discoveryItems.getValue().equalsIgnoreCase(DiscoveryItems.ATTRIBUTES.getValue())) {
                configFilePath = constructConfigFilePath(process, component, operatingSystem, componentRoot, discoveryItems);
            } else {
                configFilePath = getConfigFileLocation(componentRoot, component, operatingSystem);
            }
        }
        this.componentRoot = componentRoot;
        this.setComponentId(component.getComponentId());
        this.setConfigFilePath(configFilePath);
        this.setComponentRoot(componentRoot);
        this.setProcessArgs(process.getProcessArgs());
        this.setConfigFilePresent(!StringUtils.isEmpty(configFilePath));
    }

    public ConfigFileDetails(String processArgs, String componentName) {
        this.processArgs = processArgs;
        this.componentName = componentName;
    }

    private String constructConfigFilePath(Process process, Component component, String operatingSystem, String componentRoot,DiscoveryItems discoveryItems) {
        String configFilePath;
            if (component.getComponentName().equalsIgnoreCase(Constants.WEBSPHERE_COMPONENT_NAME)) {
                configFilePath = constructWebSphereConfigPath(process, component, operatingSystem, componentRoot);
            }
            else if(component.getComponentName().toLowerCase().contains(Constants.HTTPD_COMMON_COMPONENT_NAME)
                    && discoveryItems.getValue().equalsIgnoreCase(DiscoveryItems.ATTRIBUTES.getValue())) {
                configFilePath = constructHttpdConfigPath(process, component, operatingSystem, componentRoot);
            }
            else {
                configFilePath = getConfigFileLocation(componentRoot, component, operatingSystem);
            }
        return  configFilePath;
    }

    private String constructHttpdConfigPath(Process process, Component component, String operatingSystem, String componentRoot) {
        String configFilePath;
        boolean fileFound;

        if (!Utils.isWindows()) {
            try{
                logger.info("Trying to get configFile path using command");
                configFilePath = getHttpdConfigPath(component, process, operatingSystem);
                if (!configFilePath.isEmpty()) {
                    fileFound = PathFinder.locateConfigFile(configFilePath);
                    if (fileFound) {
                        logger.info("The config file location for httpd component : {}  , pid : {}", configFilePath, process.getPid());
                        return configFilePath;
                    }
                }
            }catch (Exception e) {
                logger.error("Exception while finding the config file path using command for HTTPD component :", e);
            }
        }

        if(process.getProcessArgs().contains(Constants.FILE_ARGUMENT)) {
            try{
                logger.info("Trying to get config File path using argument");
                AttributeAccess access = new AttributeAccess();
                access.setMethod(DiscoveryMethod.REG_EX.getValue());
                access.setValue(Constants.ARGUMENT_F_VALUE_PATTERN);

                ConfigFileDetails configFileDetails = new ConfigFileDetails(process.getProcessArgs(), component.getComponentName());

                configFilePath = ParserFactory.getInstance()
                        .getParser(DiscoveryMethod.BASIC_PARSER.getValue())
                        .regExParse(access, configFileDetails,component);
                if(!configFilePath.isEmpty()) {
                    fileFound = PathFinder.locateConfigFile(configFilePath);
                    if (fileFound) {
                        logger.info("The config file location for httpd component : {}  , pid : {}", configFilePath, process.getPid());
                        return configFilePath;
                    }
                }
            }catch (Exception e) {
                logger.error("Exception while finding the config file path using argument for HTTPD component :", e);
            }
        }
        YamlFileLoader.refreshComponentsFromYamlFile();
        component = YamlFileLoader.getComponentById(component.getComponentId());
        configFilePath = getConfigFileLocation(componentRoot, component, operatingSystem);
        return configFilePath;
    }

    private String getHttpdConfigPath(Component component, Process process, String operatingSystem)
    {
        String configFilePath = StringUtils.EMPTY;
        ComponentVersion versionInfo = null;
        String commandOutput;
        String command;

        List<ComponentVersion> versionList = YamlFileLoader.getComponentsVersion();
        if (versionList != null) {
            for (ComponentVersion vi : versionList) {
                if(vi.getComponentId() == component.getComponentId())
                {
                    versionInfo = vi;
                    break;
                }
            }
        }
        if (versionInfo != null) {
            for (VersionCommandDetail versionCommand : versionInfo.getVersionCommands()) {
                if (!versionCommand.getPath().contains(Constants.HTTPD_VERSION_COMMAND_TYPE)) {
                    continue;
                }
                String versionCommandRelativePath = PathFinder.getApplicablePath(versionCommand.getPath());
                component.setRelativePathList(Collections.singletonList(versionCommandRelativePath));
                ConfigFileDetails configFileDetails = new ConfigFileDetails(process, component, operatingSystem, DiscoveryItems.VERSION);
                logger.info("configFileDetails created for command to get the config file path for Process :{} : {}", process.getPid(), process.getProcessName());
                if (configFileDetails.isConfigFilePresent()) {
                    command = configFileDetails.getConfigFilePath() + Constants.HTTPD_CONFIG_FILE_COMMAND;
                    logger.info("Pid = {} : The command to be executed to get the httpd config file  : {} ", process.getPid(), command);
                    commandOutput = Utils.getCommandOutput(command, false, false);
                    logger.info("Pid = {} : The config file location for httpd component : {}  ", process.getPid(), commandOutput);
                    configFilePath = commandOutput.trim();
                    break;
                }
            }
        }
        return configFilePath;
    }
    private String constructWebSphereConfigPath(Process process, Component component, String operatingSystem, String componentRoot)
    {
        String configFilePath = StringUtil.EMPTY_STRING;
        //com.ibm.ws.runtime.WsServer /websphere/wasadm/FE/PROFILE/UATF10PROFILE/config UATF10Cell UATF10Node UATF10Server
        if(process.getProcessArgs().contains("com.ibm.ws.runtime.WsServer"))
        {
            String [] processArguments = process.getProcessArgs().split("com.ibm.ws.runtime.WsServer");
            if(processArguments.length >=1 && !processArguments[1].isEmpty()) {
                String[] filePathKeywords = processArguments[1].trim().split(" ");
                if(filePathKeywords.length>=3 && !filePathKeywords[0].isEmpty()
                        && !filePathKeywords[1].isEmpty() && !filePathKeywords[2].isEmpty()) {
                    if(operatingSystem.equalsIgnoreCase("Windows")  || operatingSystem.equalsIgnoreCase(""))
                    {
                        String [] path = filePathKeywords[0].split("websphere");
                        if(path.length >=2 && !path[1].isEmpty() )
                            configFilePath = "websphere"+ path[1] + "/cells/" + filePathKeywords[1] + "/nodes/" + filePathKeywords[2].concat("/").concat(component.getRelativePathList().get(0));
                    }
                    else
                        configFilePath = filePathKeywords[0] + "/cells/" + filePathKeywords[1] + "/nodes/" + filePathKeywords[2].concat("/").concat(component.getRelativePathList().get(0));
                }
                if(filePathKeywords[3] != null) {
                    for (Attribute attribute : component.getAttributes()) {
                        if (attribute.getAttributeName().equalsIgnoreCase("MonitorPort")) {
                            {
                                String attributeValue;
                                for (AttributeAccess access : attribute.getAccess()) {
                                    attributeValue = access.getValue();
                                    if (attributeValue.contains("???")) {
                                        attributeValue = attributeValue.replace("???", filePathKeywords[3]);
                                        access.setValue(attributeValue);
                                    }
                                }
                            }
                        }
                    }
                }

            }
        }
        if(!configFilePath.equalsIgnoreCase(StringUtil.EMPTY_STRING)) {
            logger.info(" The WEBSPHERE configFilePath created from process arguments {} ", configFilePath);
            boolean fileFound = PathFinder.locateConfigFile(configFilePath);
            if (!fileFound) {
                logger.info("WEBSPHERE configFile not found: {} . So calling getConfigFileLocation using find cmd.", configFilePath);
                configFilePath = getConfigFileLocation(componentRoot, component, operatingSystem);
            }
            else
                logger.info("The WEBSPHERE configFile for Port is found : {} ", configFilePath);
        }
        return configFilePath;
    }

    private String getComponentRoot(Process process, Component component) {
        String componentRoot;
        if (PathFinder.isCwdNotUsable(process)) {
            componentRoot = PathFinder.getComponentRootForNonCwdProcess(process, component);
        } else {
            if (PathFinder.isCwdNotUsableDirectly(process)) {
                componentRoot = PathFinder.interpolateCwd(process);
            } else {
                componentRoot = process.getProcessCurrentWorkingDirectory();
            }
        }
        logger.info("Component Root Folder: {} ", componentRoot);
        return componentRoot;
    }

    private String getConfigFileLocation(String componentRoot, Component component, String operatingSystem) {
        boolean found = false;
        String configFileLocation = StringUtils.EMPTY;
        for (String relativePath : component.getRelativePathList()) {
            logger.info("using relativePath : {} ", relativePath);
            configFileLocation = componentRoot.concat(File.separator).concat(relativePath);
            logger.info(" {} ", configFileLocation);
            boolean fileFound = PathFinder.locateConfigFile(configFileLocation);
            if (fileFound) {
                logger.info("Config file found: {} ", configFileLocation);
                found = true;
                break;
            } else {
                logger.info("Config file NOT found using relative path : {} - {} ", operatingSystem, configFileLocation);
                if (!Utils.isWindows()) {
                    String rpmPath = PathFinder.searchRPMPackagesForPath(component, operatingSystem);
                    if (!StringUtils.isEmpty(rpmPath)) {
                        found = PathFinder.locateConfigFile(rpmPath);
                        configFileLocation = rpmPath;
                        logger.info("Path found: {} in RPM package located: {} ", rpmPath, found);
                        break;
                    }
                }
                if (!found || StringUtils.isEmpty(configFileLocation.trim())) {
                    logger.info("Searching configuration file in installation directory: {} ", componentRoot);
                    try {
                        String splitBy = File.separatorChar == '\\' ? "\\\\" : File.separator;
                        String[] entityArray = relativePath.split(splitBy);
                        String entityName = entityArray[entityArray.length - 1];
                        logger.info("entityName: {} ", entityName);
                        String installationPath = PathFinder.searchInsideInstallationDirectory(componentRoot, entityName, relativePath);
                        if (!StringUtils.isEmpty(installationPath)) {
                            found = PathFinder.locateConfigFile(installationPath);
                            configFileLocation = installationPath;
                            logger.info("Configuration file found: {} inside installation directory: {} ", installationPath, found);
                            break;
                        } else {
                            String locatedFile = PathFinder.runLocateCommand(entityName, operatingSystem);
                            if (!StringUtils.isEmpty(locatedFile)) {
                                found = PathFinder.locateConfigFile(locatedFile);
                                configFileLocation = locatedFile;
                                logger.info("Configuration file found using locate command at : {} ", locatedFile);
                                break;
                            }
                        }
                    } catch (Exception e) {
                        logger.error("Error while searching installation directory: {} - {}", e.getMessage(), e);
                    }
                }
            }
        }
        return found ? configFileLocation : StringUtils.EMPTY;
    }
}