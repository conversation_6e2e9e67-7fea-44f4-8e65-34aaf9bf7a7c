package com.appnomic.appsone.parser.apache.parser;

import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.parser.apache.directives.Define;
import com.appnomic.appsone.parser.apache.modules.Module;
import com.appnomic.appsone.parser.apache.modules.SharedModule;
import com.appnomic.appsone.parser.apache.modules.StaticModule;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;

import java.io.BufferedReader;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Stack;
import java.util.regex.Pattern;

/**
 * This class is used to provide generic parser functionality for the Apache configuration.
 */
public class Parser {

    protected String rootConfFile;
    protected String serverRoot;
    protected StaticModule[] staticModules;
    protected SharedModule[] sharedModules;

    /**
     * @param rootConfFile  the Apache root configuration file.
     * @param serverRoot    the Apache server root
     * @param staticModules
     * @param sharedModules
     * @throws Exception if the rootConfFile or serverRoot do not exist
     */
    public Parser(String rootConfFile, String serverRoot, StaticModule[] staticModules, SharedModule[] sharedModules) throws Exception {
        if (!PathFinder.locateConfigFile(rootConfFile)) {
            throw new Exception("The root configuration file does not exist");
        }

        if (!PathFinder.locateConfigFile(serverRoot)) {
            throw new Exception("The server root does not exist");
        }

        this.rootConfFile = rootConfFile;
        this.serverRoot = serverRoot;
        this.staticModules = staticModules.clone();
        this.sharedModules = sharedModules.clone();
    }

    /**
     * Utility to check if a line matches an Apache comment.
     *
     * @param line the line to check for a comment.
     * @return a boolean indicating if the line is a comment.
     */
    public static boolean isCommentMatch(String line) {
        Pattern commentPattern = Pattern.compile("^\\s*#");
        return commentPattern.matcher(line).find();
    }

    /**
     * Utility to check if a line matches a directive type.
     *
     * @param line          the line to check for the directive type.
     * @param directiveType the type of the directive to match against. This is not case sensitive.
     * @return a boolean indicating if the line mathes the directiveType
     */
    public static boolean isDirectiveMatch(String line, String directiveType) {
        Pattern directivePattern = Pattern.compile("^\\s*\\b" + directiveType + "\\b\\s+", Pattern.CASE_INSENSITIVE);
        boolean matchesDirectivePattern = directivePattern.matcher(line).find();
        return matchesDirectivePattern || line.contains("=");
    }

    /**
     * Utility used to check if a line matches a VirtualHost <br/>
     * <br/>
     * Example :<br/>
     * &lt;VirtualHost *:80&gt;
     *
     * @param line the line to match against
     * @return a boolean indicating if the line matches a VirtualHost
     */
    public static boolean isVHostMatch(String line) {
        Pattern virtualHostPattern = Pattern.compile("<\\s*\\bVirtualHost\\b.*>", Pattern.CASE_INSENSITIVE);
        return virtualHostPattern.matcher(line).find();
    }

    /**
     * Utility used to check if a line matches a VirtualHost Close declaration<br/>
     * <br/>
     * Example :<br/>
     * &lt;/VirtualHost&gt;
     *
     * @param line the line to match against
     * @return a boolean indicating if the line matches a VirtualHost Close declaration.
     */
    public static boolean isVHostCloseMatch(String line) {
        Pattern virtualHostClosePattern = Pattern.compile("</.*\\bVirtualHost\\b.*>", Pattern.CASE_INSENSITIVE);
        return virtualHostClosePattern.matcher(line).find();
    }

    /**
     * Utility used to check if a line matches an IfModule Open Negation <br/>
     * <br/>
     * Example :<br/>
     * &lt;IfModule !mpm_netware_module&gt;
     *
     * @param line the line to match against
     * @return a boolean indicating if the line matches an IfModule Open Negation
     */
    public static boolean isIfModuleOpenNegateMatch(String line) {
        Pattern ifModuleOpenNegatePattern = Pattern.compile("<\\s*\\bifmodule\\b\\s+!.*>", Pattern.CASE_INSENSITIVE);
        return ifModuleOpenNegatePattern.matcher(line).find();
    }

    /**
     * Utility used to check if a line matches an IfModule Open Declaration<br/>
     * <br/>
     * Example :<br/>
     * &lt;IfModule status_module&gt;
     *
     * @param line the line to match against
     * @return a boolean indicating if the line matches an IfModule Open Declaration
     */
    public static boolean isIfModuleOpenMatch(String line) {
        Pattern ifModuleOpenPattern = Pattern.compile("<\\s*\\bifmodule\\b.*>", Pattern.CASE_INSENSITIVE);
        return ifModuleOpenPattern.matcher(line).find();
    }

    /**
     * Utility used to check if a line matches an IfModule Close declaration<br/>
     * <br/>
     * Example :<br/>
     * &lt;/ifmodule&gt;
     *
     * @param line the line to match against
     * @return a boolean indicating if the line matches an IfModule Close declaration.
     */
    public static boolean isIfModuleCloseMatch(String line) {
        Pattern ifModuleClosePattern = Pattern.compile("</\\s*\\bifmodule\\b\\s*>", Pattern.CASE_INSENSITIVE);
        return ifModuleClosePattern.matcher(line).find();
    }

    /**
     * Utility used to check if a line matches an Include directive
     *
     * @param line the line to match against the Include directive.
     * @return a boolean indicating if the line matches an Include directive.
     */
    public static boolean isIncludeMatch(String line) {
        Pattern includePattern = Pattern.compile("^\\s*\\b(Include|IncludeOptional)\\b", Pattern.CASE_INSENSITIVE);
        return includePattern.matcher(line).find();
    }

    /**
     * Checks if a line has a valid ifmodule that does not belong to a loaded apache module<br/>
     * <p>
     * <br/>
     * Example:<br/>
     * &lt;IfModule !mpm_winnt_module&gt; or &lt;IfModule !mod_ssl.c&gt;
     *
     * @param line    the line to match against
     * @param modules list of modules to compare against
     * @return true if the line matches a negate module
     */
    public static boolean isInNegateModules(String line, Module[] modules) {
        for (Module module : modules) {
            if (module.getName().replaceAll("_module", "")
                    .equals(line.replaceAll("(?i)<\\s*\\bifmodule\\b\\s*!mod_", "").replaceAll("\\.c\\s*>", "").replaceAll("(?i)<\\s*\\bifmodule\\b\\s*!", "").replaceAll("_module\\s*>", ""))) {
                return true;
            }
        }

        return false;
    }

    /**
     * Checks if a line has a valid ifmodule that belongs to a loaded apache module<br/>
     * <p>
     * <br/>
     * Example:<br/>
     * &lt;IfModule mpm_winnt_module&gt; or &lt;IfModule mod_ssl.c&gt;
     *
     * @param line    the line to match against
     * @param modules list of modules to compare against
     * @return true if the line matches module
     */
    public static boolean isInModules(String line, Module[] modules) {
        for (Module module : modules) {
            if (module.getName().replaceAll("_module", "")
                    .equals(line.replaceAll("(?i)<\\s*\\bifmodule\\b\\s*mod_", "").replaceAll("\\.c\\s*>", "").replaceAll("(?i)<\\s*\\bifmodule\\b\\s*", "").replaceAll("_module\\s*>", ""))) {
                return false;
            }
        }

        return true;
    }

    protected String getFileFromInclude(String line) {
        return line.replaceAll("(?i)\\b(Include|IncludeOptional)\\b\\s+", "").replaceAll("\"", "");
    }

    private String processConfigurationLine(String line, Define[] defines) {

        String processedLine = line.replaceAll("\\s+\\\\\\s*" + Constants.newLine, " ");

        processedLine = Define.replaceDefinesInString(defines, Utils.sanitizeLineSpaces(processedLine));

        return processedLine;
    }

    private ConfigurationLine[] getConfigurationLines(String confFile, boolean loadDefines) throws Exception {

        Define[] defines;
        if (loadDefines) {
            defines = Define.getAllDefine(new DirectiveParser(rootConfFile, serverRoot, staticModules, sharedModules));
        } else {
            defines = new Define[0];
        }

        ArrayList<ConfigurationLine> configurationLines = new ArrayList<>();

        getConfigurationLines(defines, confFile, configurationLines);

        return configurationLines.toArray(new ConfigurationLine[0]);
    }

    private void getConfigurationLines(Define[] defines, String confFile, ArrayList<ConfigurationLine> configurationLines) throws Exception {

        try (BufferedReader br = new BufferedReader(PathFinder.loadConfigFile(confFile))) {

            String strLine, cmpLine;
            StringBuilder concatLine = new StringBuilder();
            Stack<String> ifModuleStack = new Stack<>();

            int lineNumInFile = 0, currentConcatLineNum = -1;
            while ((strLine = br.readLine()) != null) {

                lineNumInFile++;

                currentConcatLineNum = (currentConcatLineNum == -1 ? lineNumInFile : currentConcatLineNum);
                concatLine.append(strLine);

                // Multiline configuration line
                if (strLine.trim().endsWith("\\")) {
                    concatLine.append(Constants.newLine);
                    continue;
                }

                cmpLine = processConfigurationLine(concatLine.toString(), defines);

                boolean isComment = isCommentMatch(cmpLine);
                configurationLines.add(new ConfigurationLine(concatLine.toString(), cmpLine, confFile, isComment, currentConcatLineNum, lineNumInFile));

                concatLine = new StringBuilder();
                currentConcatLineNum = -1;

                if (!isComment) {

                    if (isIfModuleOpenNegateMatch(cmpLine)) {
                        if (ifModuleStack.isEmpty()) {
                            if (isInNegateModules(cmpLine, staticModules) || isInNegateModules(cmpLine, sharedModules)) {
                                ifModuleStack.push(cmpLine);
                            }
                        } else {
                            // we have found a nested iFModule iterate the counter
                            ifModuleStack.push(cmpLine);
                        }
                    } else if (isIfModuleOpenMatch(cmpLine)) {
                        // Check if were already in a module that isn't loaded
                        if (ifModuleStack.isEmpty()) {
                            if (isInModules(cmpLine, staticModules) && isInModules(cmpLine, sharedModules)) {
                                ifModuleStack.push(cmpLine);
                            }
                        } else {
                            // we have found a nested iFModule iterate the counter
                            ifModuleStack.push(cmpLine);
                        }
                    }

                    if (!ifModuleStack.isEmpty()) {
                        if (isIfModuleCloseMatch(cmpLine)) {
                            ifModuleStack.pop();
                        }

                    } else if (isIncludeMatch(cmpLine) && Arguments.getReadHttpdConfigSubFiles()) {

                        String file = getFileFromInclude(cmpLine);

                        // if the filename starts with it is an absolute path,
                        // otherwise its a relative path
                        ApacheFile check;
                        if (file.startsWith("/") || (file.contains(":"))) {
                            check = new ApacheFile(file);
                        } else {
                            check = new ApacheFile(serverRoot, file);
                        }

                        // check if its a directory, if it is we must include all
                        // files in the directory
                        if (check.isDirectory()) {
                            String[] children = check.list();

                            if (children != null) {
                                Arrays.sort(children);

                                ApacheFile refFile;
                                for (String child : children) {
                                    refFile = new ApacheFile(check.getAbsolutePath(), child);
                                    if (!refFile.isDirectory()) {
                                        getConfigurationLines(defines, refFile.getAbsolutePath(), configurationLines);
                                    }
                                }
                            }
                        } else {
                            // check if its wild card here
                            if (file.contains("*")) {
                                ApacheFile parent = new ApacheFile(check.getParentFile());
                                String[] children = parent.list();

                                if (children != null) {
                                    Arrays.sort(children);

                                    ApacheFile refFile;
                                    for (String child : children) {
                                        refFile = new ApacheFile(parent.getAbsolutePath(), child);
                                        if (!refFile.isDirectory() && refFile.getName().matches(check.getName().replaceAll("\\.", "\\.").replaceAll("\\*", ".*"))) {
                                            getConfigurationLines(defines, refFile.getAbsolutePath(), configurationLines);
                                        }
                                    }
                                }
                            } else {
                                getConfigurationLines(defines, check.getAbsolutePath(), configurationLines);
                            }
                        }
                    }
                }

            }
        }

    }

    protected ParsableLine[] getParsableLines(ConfigurationLine[] configurationLines, boolean includeVHosts) {

        ArrayList<ParsableLine> lines = new ArrayList<>();
        Stack<String> ifModuleStack = new Stack<>();
        Stack<String> virtualHostStack = new Stack<>();

        String cmpLine;
        boolean isComment;
        for (ConfigurationLine configurationLine : configurationLines) {
            cmpLine = configurationLine.getProcessedLine();
            isComment = configurationLine.isComment();

            /*
              Parse IfModule statements to see if we should add the directives

              Two types of IfModules <IfModule mpm_prefork_module> <IfModule mod_ssl.c>

             */
            if (!isComment) {

                if (isIfModuleOpenNegateMatch(cmpLine)) {
                    if (ifModuleStack.isEmpty()) {
                        if (isInNegateModules(cmpLine, staticModules) || isInNegateModules(cmpLine, sharedModules)) {
                            ifModuleStack.push(cmpLine);
                        }
                    } else {
                        // we have found a nested iFModule iterate the counter
                        ifModuleStack.push(cmpLine);
                    }
                } else if (isIfModuleOpenMatch(cmpLine)) {
                    // Check if were already in a module that isn't loaded
                    if (ifModuleStack.isEmpty()) {
                        if (isInModules(cmpLine, staticModules) && isInModules(cmpLine, sharedModules)) {
                            ifModuleStack.push(cmpLine);
                        }
                    } else {
                        // we have found a nested iFModule iterate the counter
                        ifModuleStack.push(cmpLine);
                    }
                }

                /*
                  Parse VirtualHost statements to see if we should add the directives

                  Example VirtualHost <VirtualHost *:80>

                 */
                if (!includeVHosts && isVHostMatch(cmpLine)) {
                    virtualHostStack.push(cmpLine);
                }
            }

            if (!ifModuleStack.isEmpty()) {
                if (!isComment && isIfModuleCloseMatch(cmpLine)) {
                    ifModuleStack.pop();
                }

                lines.add(new ParsableLine(configurationLine, false));
            } else if (!virtualHostStack.isEmpty()) {
                if (!isComment && isVHostCloseMatch(cmpLine)) {
                    virtualHostStack.pop();
                }

                lines.add(new ParsableLine(configurationLine, false));
            } else {
                lines.add(new ParsableLine(configurationLine, true));
            }
        }

        return lines.toArray(new ParsableLine[0]);
    }

    protected ParsableLine[] getConfigurationParsableLines(boolean loadDefines, boolean includeVHosts) throws Exception {
        return getParsableLines(getConfigurationLines(rootConfFile, loadDefines), includeVHosts);
    }

}
