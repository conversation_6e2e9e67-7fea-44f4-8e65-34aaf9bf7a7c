package com.appnomic.appsone.parser;

import javax.xml.namespace.NamespaceContext;
import java.util.Iterator;

public class XmlNameSpace implements NamespaceContext {
    String documentNameSpace;
    String namespacePrefix;

    public XmlNameSpace(String documentNameSpace, String namespacePrefix) {
        this.documentNameSpace = documentNameSpace;
        this.namespacePrefix = namespacePrefix;
    }

    public String getNamespaceURI(String prefix) {
        return documentNameSpace;
    }


    public String getPrefix(String namespaceURI) {
        return namespacePrefix;
    }


    public Iterator getPrefixes(String namespaceURI) {
        return null;
    }
}
