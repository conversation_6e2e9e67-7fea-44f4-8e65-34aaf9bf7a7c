package com.appnomic.appsone.parser.property;

import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.util.Constants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public class PropertyParser extends BasicParser {

    private static final Logger logger = LoggerFactory.getLogger(PropertyParser.class);

    private static Properties prop = new Properties();

    public String parse(ParseTarget parseTarget) {
        String AttributeName = parseTarget.getAttributeName();
        logger.info("Discovering attribute via PropertyParser - : {}", AttributeName);

        InputStream fileInputStream = null;

        logger.debug("Loading properties file: {} ", parseTarget.getConfigFileName());

        try {
            File file = new File(parseTarget.getConfigFileName());
            if (file.exists()) {
                fileInputStream = new FileInputStream(parseTarget.getConfigFileName());
            } else {
                fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(parseTarget.getConfigFileName());
            }
            prop.load(fileInputStream);
        } catch (IOException e) {
            logger.error("Error loading .properties file: {} ", parseTarget.getConfigFileName());
        }

        try {
            if (fileInputStream != null) {
                fileInputStream.close();
            }
        } catch (IOException e) {
            logger.error("Exception occurred in closing input stream- {} : {} ", e.getMessage(), e);
        }

        if (!prop.isEmpty()) {
            if (prop.containsKey(parseTarget.getAttributeName())) {
                logger.info("Attribute: {} found in .properties file: {} ", parseTarget.getAttributeName(), prop.getProperty(parseTarget.getAttributeName()));
                return prop.getProperty(parseTarget.getAttributeName());
            } else {
                logger.info("Port not configured in .properties file returning default kafka server port: {} ", Constants.DEFAULT_KAFKA_PORT);
            }
        }
        return Constants.DEFAULT_KAFKA_PORT;
    }
}
