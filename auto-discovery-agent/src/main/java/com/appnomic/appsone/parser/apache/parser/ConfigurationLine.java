package com.appnomic.appsone.parser.apache.parser;

/**
 * This class is used to model a configuration line from the Apache Configuration <br/>
 * <br/>
 * A configuration line consists of the following:<br/>
 * line - The unmodified line(s) from the Apache Configuration. A configuration line may span multiple lines as long as the end of the line ends with a backslash<br/>
 * processed line - The processed configuration line. The following is done to process a configuration line:<br/>
 * <br/>
 * 1. Trim leading and trailing whitespace from the line.<br/>
 * 2. Remove multiple spaces and tabs and replace them with one space.<br/>
 * 3. If the configuration line spans multiple lines make it a single line.<br/>
 * <br/>
 * file - The file that contains the line<br/>
 * isComment - indicates whether the configuration line is a comment<br/>
 * lineOfStart - The line number inside of the file where the configuration line starts<br/>
 * lineOfEnd - The line number insode of the file where the configuration line ends.<br/>
 */

public class ConfigurationLine {

    private final String line;
    private final String processedLine;
    private final String file;
    private final int lineOfStart;
    private final int lineOfEnd;
    private final boolean isComment;

    public ConfigurationLine(String line, String processedLine, String file, boolean isComment, int lineOfStart, int lineOfEnd) {
        this.line = line;
        this.processedLine = processedLine;
        this.file = file;
        this.isComment = isComment;
        this.lineOfStart = lineOfStart;
        this.lineOfEnd = lineOfEnd;
    }

    /**
     * @return the processedLine from the configuration.
     */
    public String getProcessedLine() {
        return processedLine;
    }

    /**
     * @return the file that contains the configuration line.
     */
    public String getFile() {
        return file;
    }

    /**
     * @return the isComment
     */
    public boolean isComment() {
        return isComment;
    }

    /**
     * @return
     */
    public int getLineOfStart() {
        return lineOfStart;
    }
}
