package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.beans.discovery.AttributeAccess;
import com.appnomic.appsone.common.beans.discovery.Component;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.common.enums.DiscoveryItems;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.xml.sax.SAXException;

import javax.xml.namespace.NamespaceContext;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.xpath.*;
import java.io.*;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class BasicParser implements Parser {

    private static final Logger logger = LoggerFactory.getLogger(BasicParser.class);

    public ConfigFileDetails matchProcess(Process process, Component component, String operatingSystem) {
        logger.info("Trying to match PROCESS pid :{},  name  - {}, with COMPONENT - {} .", process.getPid(), process.getProcessName(), component.getComponentName() + "->" + component.getDiscoveryPattern());
        String concatenatedProcString = process.getProcessName().concat(process.getProcessArgs());
        boolean matchingProcessFound = matchesWithComponent(concatenatedProcString, component);
        if (matchingProcessFound) {
            logger.info("Known component found: Name- {} , Id- {} ", component.getComponentName(), component.getComponentId());
            logger.info("Process Name: {} ", process.getProcessName());
            logger.info("Process Args: {} ", process.getProcessArgs());
            logger.info("Process Working Directory: {} ", process.getProcessCurrentWorkingDirectory());
            return new ConfigFileDetails(process, component, operatingSystem, DiscoveryItems.ATTRIBUTES);
        }
        return null;
    }

    public boolean matchesWithComponent(String processFragment, Component component) {
        boolean discoveryPatternMatch = false;
        try {
            Matcher discoveryPatternMatcher = Pattern.compile(component.getDiscoveryPattern()).matcher(processFragment);
            discoveryPatternMatch = discoveryPatternMatcher.find();
            if (discoveryPatternMatch && component.getComponentId() == Constants.APACHE_HTTPD_COMPONENT_ID && processFragment.toLowerCase().contains(Constants.OHS_ARGUMENT)) {
                discoveryPatternMatch = false;
            }
        } catch (Exception exception) {
            logger.error("Exception matching process details: {} with Component's Regex(discoveryPattern): {} - {}", processFragment, component.getDiscoveryPattern(), exception);
        }
        return discoveryPatternMatch;
    }

    public String xpathParse(AttributeAccess attributeAccess, ConfigFileDetails configInfo) {
        logger.info("Discovering attribute via XPATH - : {}", attributeAccess);
        String attributeFound = StringUtils.EMPTY;
        Document doc = null;
        XPath xpath = null;
        try {
            DocumentBuilder builder = Utils.getSecureDocumentBuilder();

            if (builder != null) {

                InputStream fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configInfo.getConfigFilePath());
                if (fileInputStream != null) {
                    doc = builder.parse(fileInputStream);
                    fileInputStream.close();
                } else {
                    doc = builder.parse(configInfo.getConfigFilePath());
                }
                doc.setXmlStandalone(true);
                doc.normalizeDocument();
                XPathFactory xpathFactory = XPathFactory.newInstance();
                xpath = xpathFactory.newXPath();

                NamespaceContext namespaceContext = getDocumentNameSpace(configInfo);
                if (namespaceContext instanceof XmlNameSpace &&
                        !StringUtils.isEmpty(namespaceContext.getNamespaceURI(""))) {
                    xpath.setNamespaceContext(namespaceContext);
                }
            }
        } catch (IOException | SAXException e) {
            logger.error("Exception during detailed discovery {} : {}", e.getMessage(), e);
        }
        try {
            String targetXPath = attributeAccess.getValue();
            XPathExpression expr = Objects.requireNonNull(xpath).compile(targetXPath);
            NodeList nodes = (NodeList) expr.evaluate(doc, XPathConstants.NODESET);
            if (nodes != null) {
                logger.info("{} - nodes found for XPath: {} ", nodes.getLength(), targetXPath);
                for (int i = 0; i < nodes.getLength(); i++) {
                    attributeFound = nodes.item(i).getNodeValue();
                    if (nodes.item(i).getNodeType() == 1) {
                        attributeFound = nodes.item(i).getTextContent();
                    }
                    logger.info("Node : {} ", attributeFound);
                    if (!StringUtils.isEmpty(attributeFound) && attributeFound.contains(":")) {
                        attributeFound = attributeFound.split(":")[1].replace("}", "").trim();
                    }
                }
            }
        } catch (XPathExpressionException e) {
            logger.error("Exception during detailed discovery via XPATH {} : {}", e.getMessage(), e);
        }
        logger.info("Attributes Found via XPath: {} ", attributeFound);
        return attributeFound;
    }

    public String fileParser(AttributeAccess attributeAccess, ConfigFileDetails configInfo, ParseTarget parseTarget) {
        String attributeFound = StringUtils.EMPTY;
        try {
            try (BufferedReader br = new BufferedReader(PathFinder.loadConfigFile(parseTarget.getConfigFileName()))) {
                String line = br.readLine();

                while (line != null) {
                    if (line.contains(attributeAccess.getValue())) {
                        int diffStart = StringUtils.indexOf(line, attributeAccess.getValue());
                        int portStartIndex = diffStart + attributeAccess.getValue().length();
                        int portEndIndex = StringUtils.indexOf(line, ")");
                        attributeFound = line.substring(portStartIndex, portEndIndex);
                        br.close();
                        break;
                    }
                    line = br.readLine();
                }
            }
        } catch (Exception e) {
            logger.error("Exception while parsing a file for attribute : {} ", attributeAccess.toString(), e);
        }
        return attributeFound;
    }

    public String regExParse(AttributeAccess attributeAccess, ConfigFileDetails configInfo, Component component) {
        logger.info("Discovering attribute via REGEX - : {}", attributeAccess);
        String attributeFound = StringUtils.EMPTY;
        try {
            String processArgs = configInfo.getProcessArgs();
            Matcher patternMatcher = Pattern.compile(attributeAccess.getValue()).matcher(processArgs);
            String splitPattern = Constants.EQUAL_DELIMITER;
            if (patternMatcher.find()) {
                logger.info("Attributes Found via REGEX: {} ", attributeFound);
                if (component.getComponentName().equalsIgnoreCase(Constants.IBM_MQ_COMPONENT_NAME) || configInfo.getComponentName().toLowerCase().contains(Constants.HTTPD_COMMON_COMPONENT_NAME)) {
                    splitPattern = Constants.SPACE_DELIMITER;
                }
                attributeFound = patternMatcher.group().split(splitPattern)[1];
            }
        } catch (Exception e) {
            logger.error("Exception during detailed discovery via REGEX {} : {}", e.getMessage(), e);
        }
        return attributeFound;
    }


    private NamespaceContext getDocumentNameSpace(ConfigFileDetails configInfo) {
        Document doc;
        NamespaceContext namespaceContext = null;
        String documentNamespace;
        try {
            DocumentBuilder builder = Utils.getSecureDocumentBuilder();
            if (builder != null) {
                InputStream fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configInfo.getConfigFilePath());
                if (fileInputStream != null) {
                    doc = builder.parse(fileInputStream);
                    fileInputStream.close();
                } else {
                    doc = builder.parse(configInfo.getConfigFilePath());
                }
                doc.setXmlStandalone(true);
                doc.normalizeDocument();

                documentNamespace = doc.getDocumentElement().getAttribute("xmlns");
                if (StringUtils.isEmpty(documentNamespace)) {
                    documentNamespace = doc.getDocumentElement().getAttribute("xmlns:serverindex");
                }
                logger.info("Namespace {} for document {} : ", documentNamespace, configInfo.getConfigFilePath());

                if (!StringUtils.isEmpty(documentNamespace) && documentNamespace.contains("weblogic")) {
                    namespaceContext = new XmlNameSpace(documentNamespace, "dom");
                } else {
                    namespaceContext = new XmlNameSpace(documentNamespace, "");
                }
            }
            return namespaceContext;
        } catch (IOException | SAXException e) {
            logger.error("Exception parsing xml document {} : {}", e.getMessage(), e);
        }
        return null;
    }

    public String parse(ParseTarget parseTarget) {
        return null;
    }
}