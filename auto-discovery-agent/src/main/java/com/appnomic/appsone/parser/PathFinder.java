package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.beans.discovery.Component;
import com.appnomic.appsone.common.beans.discovery.Process;
import com.appnomic.appsone.util.Arguments;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

public class PathFinder {

    private static final Logger logger = LoggerFactory.getLogger(PathFinder.class);

    private static final List<String> mysqlDefaultLocationsLinux = new ArrayList<>();
    private static final String linuxCommand = "find $rootDir -name  \"$entityName\"";
    private static final String windowsCommand = "where -r \"$rootDir\" \"$entityName\"";

    static {
        mysqlDefaultLocationsLinux.add(Constants.MYSQL_DEFAULT_CONFIG_LOC_LINUX_1);
        mysqlDefaultLocationsLinux.add(Constants.MYSQL_DEFAULT_CONFIG_LOC_LINUX_2);
        mysqlDefaultLocationsLinux.add(Constants.MYSQL_DEFAULT_CONFIG_LOC_LINUX_3);
        mysqlDefaultLocationsLinux.add(Constants.MYSQL_DEFAULT_CONFIG_LOC_LINUX_4);
    }

    public static List<String> getMysqlDefaultLocationsLinux() {
        return mysqlDefaultLocationsLinux;
    }

    public static String getApplicablePath(String versionBinaryRelativePath) {
        if (Utils.isWindows()) {
            if (versionBinaryRelativePath.contains(".sh")) {
                versionBinaryRelativePath = versionBinaryRelativePath.replace(".sh", ".bat");
            }

            versionBinaryRelativePath = versionBinaryRelativePath.replace("/", "\\");
        } else {
            if (versionBinaryRelativePath.contains(".bat") || versionBinaryRelativePath.contains(".exe")) {
                versionBinaryRelativePath = versionBinaryRelativePath.replace(".bat", ".sh");
                versionBinaryRelativePath = versionBinaryRelativePath.replace(".exe", "");
            }

            versionBinaryRelativePath = versionBinaryRelativePath.replace("\\", "/");
        }
        return versionBinaryRelativePath;
    }

    public static boolean isCwdNotUsable(Process process) {
        String cwd = process.getProcessCurrentWorkingDirectory();
        if (Arguments.getOS().contains(Constants.WINDOWS_OS)) {
            return !process.getProcessName().contains(cwd);
        }
        return StringUtils.isEmpty(cwd) ||
                cwd.endsWith(File.separator) ||
                cwd.endsWith(File.separator + Constants.DATA_DIR) ||
                cwd.contains(Constants.ORACLE_DB_HOME) ||
                cwd.contains(Constants.LIB);
    }

    public static boolean isCwdNotUsableDirectly(Process process) {
        String cwd = process.getProcessCurrentWorkingDirectory();
        return cwd.endsWith(File.separator + Constants.BIN) ||
                cwd.endsWith(File.separator + Constants.IIS);
    }

    public static String interpolateCwd(Process process) {
        String cwd = process.getProcessCurrentWorkingDirectory();
        String interpolatedPath;
        interpolatedPath = cwd.replace(File.separator + Constants.BIN, "");
        interpolatedPath = interpolatedPath.replace(File.separator + Constants.IIS, "");
        interpolatedPath = interpolatedPath.trim();
        return interpolatedPath;
    }

    public static String interpolateProcessName(String processName, String path) {
        if (path.endsWith(File.separator)) {
            path = path.substring(0, path.length() - 1);
        }
        if (processName.contains(Constants.ORACLE_DB_HOME)) {
            path = processName.substring(0, processName.indexOf(Constants.ORACLE_DB_HOME));
            if (!Utils.isWindows()) {
                path = path + PathFinder.getCorrectOracleDbHome(processName);
            } else {
                path = path + Constants.ORACLE_DB_HOME;
            }
        }
        if (processName.contains(Constants.SBIN)) {
            if (processName.contains(Constants.MYSQLD)) {
                path = processName.substring(0, processName.indexOf(Constants.MYSQLD));
            } else {
                path = processName.substring(0, processName.indexOf(Constants.SBIN));
            }
        }

        if (path.endsWith(File.separator)) {
            path = path.substring(0, path.length() - 1);
        }
        return path;
    }

    public static boolean locateConfigFile(String configFilePath) {
        logger.info("Trying to locate path : {} ", configFilePath);
        boolean configFileFound = false;
        configFilePath = configFilePath.trim();
        try {
            InputStream fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configFilePath);
            if (fileInputStream != null) {
                configFileFound = true;
                fileInputStream.close();
            } else {
                File filename = new File(configFilePath);
                configFileFound = filename.getAbsoluteFile().exists();
            }
        } catch (IOException e) {
            logger.info("Error locating config file at: {} ", configFilePath);
        }
        return configFileFound;
    }

    public static InputStreamReader loadConfigFile(String configFilePath) {
        InputStreamReader configFile = null;
        try {
            InputStream fileInputStream = Thread.currentThread().getContextClassLoader().getResourceAsStream(configFilePath);
            if (fileInputStream != null) {
                configFile = new InputStreamReader(fileInputStream, StandardCharsets.UTF_8);
            } else {
                File file = new File(configFilePath);
                if (file.exists()) {
                    configFile = new InputStreamReader(Files.newInputStream(Paths.get(configFilePath)), StandardCharsets.UTF_8);
                }
            }
        } catch (IOException e) {
            logger.info("Error loading config file at: {} ", configFilePath);
        }
        return configFile;
    }

    public static String getCorrectOracleDbHome(String path) {
        StringBuilder correctHomeDir = new StringBuilder();
        correctHomeDir.append(Constants.ORACLE_DB_HOME);

        if (path.contains(Constants.ORACLE_DB_HOME)) {
            char[] pathChars = path.toCharArray();
            int index = path.indexOf(Constants.ORACLE_DB_HOME) + Constants.ORACLE_DB_HOME.length();
            for (; index < path.toCharArray().length; index++) {
                if (String.valueOf(pathChars[index]).equals("/")) {
                    break;
                }
                correctHomeDir.append(pathChars[index]);
            }
        }
        return correctHomeDir.toString();
    }

    public static String searchRPMPackagesForPath(Component component, String operatingSystem) {
        StringBuilder command = new StringBuilder();
        String commandOutput = StringUtils.EMPTY;
        if (operatingSystem.toLowerCase().contains(Constants.CENT_OS.toLowerCase()) ||
                operatingSystem.toLowerCase().contains(Constants.RHEL_OS.toLowerCase())) {
            for (String relativePath : component.getRelativePathList()) {

                String splitBy = File.separatorChar == '\\' ? "\\\\" : File.separator;
                String[] entityArray = relativePath.split(splitBy);
                String entityName = entityArray[entityArray.length - 1];
                command.append(Constants.RPM_QUERY)
                        .append(component.getComponentName().toLowerCase())
                        .append(" | grep ").append(relativePath).append("$");
                logger.info("Querying RPM package : {} ", command);
                commandOutput = Utils.getCommandOutput(command.toString(), false, false);

                if (StringUtils.isEmpty(commandOutput)) {
                    command = new StringBuilder();
                    command.append(Constants.RPM_QUERY)
                            .append(component.getComponentName().toLowerCase())
                            .append(" | grep ").append(entityName).append("$");
                    logger.info("Querying RPM package : {} ", command);
                    commandOutput = Utils.getCommandOutput(command.toString(), false, false);
                }
                if (!StringUtils.isEmpty(commandOutput)) {
                    commandOutput = commandOutput.split("\n")[0];
                    break;
                }
            }
        }
        return commandOutput;
    }

    public static String runLocateCommand(String entityName, String operatingSystem) {
        StringBuilder command = new StringBuilder();
        String commandOutput = StringUtils.EMPTY;
        if (operatingSystem.toLowerCase().contains(Constants.UBUNTU_OS.toLowerCase())) {
            command.append(Constants.LOCATE_QUERY.replace("$entity", entityName));
            logger.info("Ubuntu locate command: {} ", command);
            commandOutput = Utils.getCommandOutput(command.toString(), false, false);
        }
        return commandOutput;
    }

    public static String getComponentRootForNonCwdProcess(Process process, Component component) {
        logger.info("NonCwdProcess: Process Working directory NOT reported: {} : {}", process.getPid(), process.getProcessName());

        String possibleRoot = getComponentRootFromProcessName(process.getProcessName(), component);

        if (StringUtils.isEmpty(possibleRoot)) {
            possibleRoot = getComponentRootFromProcessArgs(process.getProcessArgs(), component);
        }

        if (StringUtils.isEmpty(possibleRoot)) {
            possibleRoot = process.getProcessName().substring(0, process.getProcessName().lastIndexOf(File.separator));
        }

        if (StringUtils.isEmpty(possibleRoot)) {
            possibleRoot = process.getProcessArgs().substring(0, process.getProcessArgs().lastIndexOf(File.separator));
        }

        return possibleRoot;
    }

    private static String getComponentRootFromProcessName(String processName, Component component) {
        String possibleRoot = StringUtils.EMPTY;
        BasicParser basicParser = new BasicParser();
        if (basicParser.matchesWithComponent(processName, component)) {
            if (processName.toLowerCase().contains(Constants.SBIN)) {
                possibleRoot = PathFinder.interpolateProcessName(processName, possibleRoot);
            } else {
                if (processName.toLowerCase().contains(Constants.BIN) && (processName.contains(Constants.BIN))) {
                    possibleRoot = processName.substring(0, processName.indexOf(Constants.BIN));
                }
            }
            possibleRoot = PathFinder.interpolateProcessName(processName, possibleRoot);

            logger.info("Possible Component Root Folder: {} ", possibleRoot);
        }
        return possibleRoot;
    }

    private static String getComponentRootFromProcessArgs(String processArgs, Component component) {
        String possibleRoot = StringUtils.EMPTY;
        BasicParser basicParser = new BasicParser();
        if (basicParser.matchesWithComponent(processArgs, component)) {
            String[] processFrags = processArgs.split(" ");
            for (String frag : processFrags) {
                if (!StringUtils.isEmpty(frag)) {
                    if (frag.contains(Constants.BIN)) {
                        if (basicParser.matchesWithComponent(frag, component)) {
                            possibleRoot = frag.substring(0, frag.indexOf(Constants.BIN));
                            if (possibleRoot.endsWith(File.separator)) {
                                possibleRoot = possibleRoot.substring(0, possibleRoot.length() - 1);
                            }
                        }
                    }
                    if (frag.contains(Constants.JBOSS_HOME) || frag.contains(Constants.SQL_CONFIG_FILE_LOCATION)) {
                        possibleRoot = frag.split("=")[1];
                    }
                }
            }
        }
        logger.info("Possible Component Root Folder: {} ", possibleRoot);

        return possibleRoot;
    }

    public static String searchInsideInstallationDirectory(String installationDirectory, String entityName, String relativePath) {

        String commandOutput;

        String command = linuxCommand.replace("$rootDir", installationDirectory)
                .replace("$entityName", entityName);
        if (Utils.isWindows()) {
            command = windowsCommand.replace("$rootDir", installationDirectory.trim())
                    .replace("$entityName", entityName);
        }
        logger.info("Locating configuration entity using command: {} ", command);

        commandOutput = Utils.getCommandOutput(PathFinder.getApplicablePath(command), false, false);

        String candidate;

        if (!StringUtils.isEmpty(commandOutput)) {
            candidate = bestSearchCandidate(commandOutput, relativePath);
        } else {
            candidate = searchRecursively(installationDirectory, entityName, relativePath);
        }

        return candidate;

    }

    public static String searchRecursively(String installationDirectory, String entityName, String relativePath) {

        String subDirectory;
        String candidate = StringUtils.EMPTY;

        logger.info("Searching installationDirectory recursively... {} ", installationDirectory);

        int index = installationDirectory.lastIndexOf(File.separatorChar);

        if (index != -1) {

            subDirectory = installationDirectory.substring(0, index);

            while (!StringUtils.isEmpty(subDirectory) && index != -1 && StringUtils.isEmpty(candidate)) {

                subDirectory = subDirectory.substring(0, index);

                logger.info("Searching subDirectory... {} ", subDirectory);

                String command = linuxCommand.replace("$rootDir", subDirectory)
                        .replace("$entityName", entityName);
                if (Utils.isWindows()) {
                    command = windowsCommand.replace("$rootDir", subDirectory)
                            .replace("$entityName", entityName);
                }
                String commandOutput = Utils.getCommandOutput(PathFinder.getApplicablePath(command), false, false);

                if (!StringUtils.isEmpty(commandOutput)) {
                    candidate = bestSearchCandidate(commandOutput, relativePath);
                    if (!StringUtils.isEmpty(candidate)) {
                        break;
                    }
                }

                index = subDirectory.lastIndexOf(File.separatorChar);
            }
        }
        return candidate;
    }

    public static String bestSearchCandidate(String commandOutput, String relativePath) {
        TreeMap<String, Integer> searchCandidates = new TreeMap<>();
        String bestCandidate = commandOutput.split("\n")[0];
        String splitBy = File.separatorChar == '\\' ? "\\\\" : File.separator;
        String[] searchBoosters = relativePath.split(splitBy);

        if (!StringUtils.isEmpty(commandOutput)) {
            logger.info("Search Results:");
            for (String possiblePath : commandOutput.split("\n")) {
                searchCandidates.put(possiblePath, 0);
                logger.info(" {} ", possiblePath);
            }
            for (String possiblePath : commandOutput.split("\n")) {
                int match = 0;
                for (String booster : searchBoosters) {
                    if (possiblePath.contains(booster)) {
                        match = match + 1;
                    }
                }
                searchCandidates.put(possiblePath, match);
            }
            if (!searchCandidates.isEmpty()) {
                logger.info(" {} ", searchCandidates);
                int max = Collections.max(searchCandidates.values());
                for (Map.Entry<String, Integer> entry : searchCandidates.entrySet()) {
                    if (entry.getValue() == max) {
                        bestCandidate = entry.getKey();
                        break;
                    }
                }
            }
        }
        logger.info("Best Candidate: {} ", bestCandidate);

        return bestCandidate;
    }

}
