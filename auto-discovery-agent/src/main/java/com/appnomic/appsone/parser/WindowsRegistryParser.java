package com.appnomic.appsone.parser;

import com.appnomic.appsone.util.Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class WindowsRegistryParser extends BasicParser {

    private static final Logger logger = LoggerFactory.getLogger(WindowsRegistryParser.class);

    public String parse(String command) {

        String commandOutput = Utils.getCommandOutput(command, false, false);

        logger.info("Value found in windows registry : {} ", commandOutput);

        return commandOutput;

    }
}
