package com.appnomic.appsone.parser.ora;

import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.parser.PathFinder;
import edu.gatech.gtri.orafile.Orafile;
import edu.gatech.gtri.orafile.OrafileDict;
import edu.gatech.gtri.orafile.OrafileVal;
import org.antlr.runtime.RecognitionException;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class OraFileParser extends BasicParser {
    private static final Logger logger = LoggerFactory.getLogger(OraFileParser.class);

    public String parse(ParseTarget parseTarget) {
        logger.info("Discovering attribute via ORA File Parser - : {}", parseTarget.getAttributeName());

        InputStreamReader inputStreamReader = PathFinder.loadConfigFile(parseTarget.getConfigFileName());

        String oraFileContent = null;
        try {
            oraFileContent = IOUtils.toString(inputStreamReader);
        } catch (IOException e) {
            logger.error("Exception during attribute discovery in File {} : {}", e.getMessage(), e);
        }
        OrafileDict tns = null;
        try {
            tns = Orafile.parse(oraFileContent);
        } catch (RecognitionException e) {
            logger.error("Exception during attribute discovery in File {} : {}", e.getMessage(), e);
        }
        if (tns != null) {
            if (tns.get("LISTENER") != null && tns.get("LISTENER").get(0).get("DESCRIPTION_LIST") != null) {
                OrafileVal LISTENER = tns.get("LISTENER").get(0).get("DESCRIPTION_LIST").get(0).get("DESCRIPTION").get(0);
                List<Map<String, String>> possibleAttributeList = LISTENER.findParamAttrs("ADDRESS", Arrays.asList("PROTOCOL", "PORT"));
                for (Map<String, String> attributeMap : possibleAttributeList) {
                    for (String key : attributeMap.keySet()) {
                        if (attributeMap.containsValue("TCP")) {
                            return attributeMap.get("PORT");
                        }
                    }
                }
            } else {
                logger.info("No listener found or configured in File {} ", parseTarget.getConfigFileName());
            }
        }
        return StringUtils.EMPTY;
    }
}
