package com.appnomic.appsone.parser.apache.modules;

/**
 * <p>
 * Class used to model an Apache module. A module has a name and type.
 * </p>
 * <p>
 * Types are as follows:<br/>
 * Static - Modules that are compiled into Apache.<br/>
 * Shared - Modules that are loaded into Apache using the "LoadModule" Directive.<br/>
 * Available - Modules that are available to be loaded into Apache.<br/>
 * </p>
 */
public class Module implements Comparable<Module> {
    private final String name;
    private final Type type;

    public Module(String name, Type type) {
        this.name = name;
        this.type = type;
    }

    public String getName() {
        return name;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Module module = (Module) o;

        if (!name.equals(module.name)) return false;
        return type == module.type;
    }

    @Override
    public int hashCode() {
        int result = name.hashCode();
        result = 31 * result + type.hashCode();
        return result;
    }

    public int compareTo(Module mod) {
        return this.name.compareTo(mod.getName());
    }

    public enum Type {
        STATIC, SHARED
    }

}
