package com.appnomic.appsone.parser.apache;

import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.parser.apache.modules.SharedModule;
import com.appnomic.appsone.parser.apache.modules.StaticModule;
import com.appnomic.appsone.parser.apache.parser.Directive;
import com.appnomic.appsone.parser.apache.parser.DirectiveParser;
import com.appnomic.appsone.util.Constants;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ApacheFileParser extends BasicParser {

    private static final Logger logger = LoggerFactory.getLogger(ApacheFileParser.class);

    public String parse(ParseTarget parseTarget) {
        logger.info("Discovering attribute via Apache Conf Parser - : {}", parseTarget.getAttributeName());

        Directive[] directives = new Directive[0];
        StaticModule[] staticModules = new StaticModule[0];
        SharedModule[] sharedModules = new SharedModule[0];
        DirectiveParser parser;

        try {
            parser = new DirectiveParser(parseTarget.getConfigFileName(), parseTarget.getServerRoot(), staticModules, sharedModules);
            directives = parser.getDirective(parseTarget.getAttributeName(), true);

        } catch (Exception e) {
            logger.error("Exception during attribute discovery in File {} : {}", e.getMessage(), e);
        }
        logger.info("The number of Directives got for ParserTarget is : {} : {}", parseTarget.toString(), directives.length);

        for (Directive directive : directives) {
            logger.info(" The directive :{} ", directive.toString());
            String attributeFound = directive.toString();
            if (directive.toString().contains(parseTarget.getAttributeName())) {
                attributeFound = attributeFound.replace(parseTarget.getAttributeName(), "");
                attributeFound = attributeFound.trim();
                if (parseTarget.getAttributeName().equalsIgnoreCase("Listen")) {
                    if (attributeFound.matches("[a-zA-Z]+")) {
                        continue;
                    } else {
                        attributeFound = attributeFound.split(Constants.SPACE_DELIMITER)[0].trim();
                        if (attributeFound.contains(":")) {
                            attributeFound = attributeFound.split(":")[1].trim();
                        }
                        if (!StringUtils.isNumeric(attributeFound)) {
                            continue;
                        }
                    }
                }
                logger.info("Attribute discovered in Configuration File: {} ", attributeFound);
                return attributeFound;
            }
        }
        return StringUtils.EMPTY;
    }
}
