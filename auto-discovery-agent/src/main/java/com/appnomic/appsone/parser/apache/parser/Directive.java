package com.appnomic.appsone.parser.apache.parser;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Class used to model an Apache directive. An Apache directive has a type and value(s).
 * </p>
 * <p>
 * Some example directives are as follows:<br/>
 * "Listen 80" - The type of this directive is "Listen" and the value is "80"<br/>
 * "Options Indexes FollowSymLinks" - The type of this directive is "Options" and the values are "Indexes" and "FollowSymLinks"<br/>
 * </p>
 */
public class Directive {
    private final String type;
    private final List<String> values;

    public Directive(String type) {
        this.type = type;
        this.values = new ArrayList<>();
    }

    public void addValue(String value) {
        values.add(value);
    }

    public String[] getValues() {
        return values.toArray(new String[0]);
    }

    public String toString() {
        StringBuilder rep = new StringBuilder();
        rep.append(this.type);
        for (String value : this.values) {
            rep.append(" ").append(value);
        }
        return rep.toString();
    }
}
