package com.appnomic.appsone.parser;

import com.appnomic.appsone.common.beans.discovery.AttributeAccess;
import com.appnomic.appsone.common.beans.discovery.Component;

public interface Parser {
    String parse(ParseTarget parseTarget);

    String regExParse(AttributeAccess attributeAccess, ConfigFileDetails configInfo, Component component);

    String xpathParse(AttributeAccess attributeAccess, ConfigFileDetails configInfo);

    String fileParser(AttributeAccess attributeAccess, ConfigFileDetails configInfo, ParseTarget parseTarget);
}