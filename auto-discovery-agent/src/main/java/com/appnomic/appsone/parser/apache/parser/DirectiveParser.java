package com.appnomic.appsone.parser.apache.parser;

import com.appnomic.appsone.parser.apache.modules.SharedModule;
import com.appnomic.appsone.parser.apache.modules.StaticModule;
import com.appnomic.appsone.util.Constants;

import java.util.ArrayList;

/**
 * This class is used to parse the Apache configuration and obtain directives.
 */
public class DirectiveParser extends Parser {

    /**
     * @param rootConfFile  the Apache root configuration file.
     * @param serverRoot    the Apache server root
     * @param staticModules
     * @param sharedModules
     * @throws Exception if the rootConfFile or serverRoot do not exist
     */
    public DirectiveParser(String rootConfFile, String serverRoot, StaticModule[] staticModules, SharedModule[] sharedModules) throws Exception {
        super(rootConfFile, serverRoot, staticModules, sharedModules);
    }

    /**
     * <p>
     * Takes in a directive and puts it into parts<br/>
     * <br/>
     * <p>
     * Example: "Listen 80 http" will be split into "Listen" "80" "http"
     *
     * </p>
     *
     * @param line the line with parts to extract
     * @return an array with the directive parts
     */
    public static String[] extractDirectiveToParts(String line) {
        String strLine = line.replaceAll(Constants.replaceCommaSpacesRegex, ",");
        strLine = strLine.replaceAll(Constants.replaceSpacesInValuesRegex, "@@");
        strLine = strLine.replaceAll("=", "@@");

        return strLine.split("@@");
    }

    /**
     * <p>
     * Parses all active configuration files for the directive specified by directiveType.
     * </p>
     *
     * @param directiveType The directive name. This is not case sensitive.
     * @param includeVHosts flag to indicate whether to include directives inside VirtualHosts
     * @return an array with all instances of the directive.
     * @throws Exception
     */
    public Directive[] getDirective(String directiveType, boolean includeVHosts) throws Exception {

        ArrayList<Directive> directives = new ArrayList<>();

        boolean loadDefines = !directiveType.equals(Constants.defineDirective);

        ParsableLine[] lines = getConfigurationParsableLines(loadDefines, includeVHosts);
        String strLine;
        for (ParsableLine line : lines) {
            if (line.isInclude()) {

                strLine = line.getConfigurationLine().getProcessedLine();

                String[] directiveValueList;
                Directive addDirective;

                if (!isCommentMatch(strLine) && isDirectiveMatch(strLine, directiveType)) {

                    addDirective = new Directive(directiveType);

                    directiveValueList = extractDirectiveToParts(strLine);
                    for (int i = 1; i < directiveValueList.length; i++) {
                        addDirective.addValue(directiveValueList[i]);
                    }
                    directives.add(addDirective);
                }
            }
        }

        return directives.toArray(new Directive[0]);
    }

    /**
     * <p>
     * Parses all active configuration files for the directive values specified by directiveType.
     * </p>
     * <p>
     * For example if you search for the directive "Listen" and the apache configuration contains the lines:<br/>
     * Listen 80<br/>
     * Listen 443 https<br/>
     * The function will return an array with values "80" and "443 https".
     * </p>
     *
     * @param directiveType The directive name. This is not case sensitive.
     * @param includeVHosts flag to indicate whether to include directives inside VirtualHosts
     * @return gets all of the values of a directive in an array. If one instance of a directive has multiple values then they will be separated by spaces.
     * @throws Exception
     */
    public String[] getDirectiveValue(String directiveType, boolean includeVHosts) throws Exception {
        ArrayList<String> directiveValues = new ArrayList<>();
        Directive[] directives = getDirective(directiveType, includeVHosts);

        String[] directiveValueList;
        StringBuilder values;

        for (Directive directive : directives) {

            directiveValueList = directive.getValues();

            values = new StringBuilder();
            for (String directiveValue : directiveValueList) {
                values.append(directiveValue).append(" ");
            }

            directiveValues.add(values.toString().trim());
        }

        return directiveValues.toArray(new String[0]);
    }
}
