package com.appnomic.appsone.parser.apache.parser;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;

/**
 * <p>
 * This class provides an extension of the built in java.io.File class. It has been extended to normalize file paths on Windows.
 * </p>
 * <p>
 * For Example the absolute or canonical path on Windows will be converted from C:\\Apache24 to C://Apache24
 * </p>
 */
public class ApacheFile extends java.io.File {

    private static final Logger logger = LoggerFactory.getLogger(ApacheFile.class);

    private static final long serialVersionUID = 1L;

    public ApacheFile(java.io.File file) {
        super(file.getAbsolutePath());
    }

    public ApacheFile(String pathname) {
        super(pathname);
    }

    public ApacheFile(String parent, String child) {
        super(parent, child);
    }

    @Override
    public String getAbsolutePath() {
        try {
            return getCanonicalPath();
        } catch (IOException e) {
            logger.error("Exception during NetstatExecutor {} : {}", e.getMessage(), e);
        }

        return super.getAbsolutePath().replaceAll("\\\\", "/");
    }

    @Override
    public String getCanonicalPath() throws IOException {
        return super.getCanonicalPath().replaceAll("\\\\", "/");
    }

}
