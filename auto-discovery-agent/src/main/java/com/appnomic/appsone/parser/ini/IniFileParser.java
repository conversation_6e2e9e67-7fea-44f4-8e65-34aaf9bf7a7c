package com.appnomic.appsone.parser.ini;

import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.parser.PathFinder;
import com.appnomic.appsone.util.Constants;
import org.apache.commons.lang3.StringUtils;
import org.ini4j.Ini;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStreamReader;

public class IniFileParser extends BasicParser {

    private static final Logger logger = LoggerFactory.getLogger(IniFileParser.class);

    public String parse(ParseTarget parseTarget) {
        logger.info("Discovering attribute via INI file Parser - : {}", parseTarget.getAttributeName());
        Ini ini = new Ini();
        InputStreamReader inputStreamReader = PathFinder.loadConfigFile(parseTarget.getConfigFileName());
        try {
            ini.load(inputStreamReader);
            Ini.Section mysqldSection = ini.get(Constants.MYSQLD);
            return mysqldSection.get(parseTarget.getAttributeName());
        } catch (IOException e) {
            logger.error("Exception during attribute discovery in File {} : {}", e.getMessage(), e);
        }
        return StringUtils.EMPTY;
    }

}
