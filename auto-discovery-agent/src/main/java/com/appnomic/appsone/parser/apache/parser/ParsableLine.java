package com.appnomic.appsone.parser.apache.parser;

/**
 * <p>
 * Class used to give information on whether a line in the configuration is parsable.<br/>
 * If a line is parsable then the include value will be set to true. <br/>
 * </p>
 */
public class ParsableLine {
    private final ConfigurationLine configurationLine;
    private final boolean include;

    public ParsableLine(ConfigurationLine configurationLine, boolean include) {
        this.configurationLine = configurationLine;
        this.include = include;
    }

    public ConfigurationLine getConfigurationLine() {
        return configurationLine;
    }

    /**
     * @return true if the line is used in the configuration, false otherwise
     */
    public boolean isInclude() {
        return include;
    }

}
