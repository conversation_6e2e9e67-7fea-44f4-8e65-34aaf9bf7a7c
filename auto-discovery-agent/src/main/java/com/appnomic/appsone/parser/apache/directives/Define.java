package com.appnomic.appsone.parser.apache.directives;

import com.appnomic.appsone.parser.apache.parser.DirectiveParser;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;

import java.util.ArrayList;
import java.util.regex.Pattern;

public class Define {
    /**
     * The Define directive has the following format: Define Server Root "/Apache24"
     * <p>
     * Defines can be read in the configuration using the following: ServerRoot "${SRVROOT}"
     */

    private String name;
    private String value;

    public Define() {
        this.name = "";
        this.value = "";
    }

    /**
     * @param directiveValue A String with a valid apache Define directive value
     */
    public Define(String directiveValue) {
        directiveValue = Utils.sanitizeLineSpaces(directiveValue);

        name = "";
        value = "";

        String[] parts = directiveValue.replaceAll("\\\\", "/").replaceFirst(" ", "@@").split("@@");
        if (parts.length > 0) {
            name = parts[0];
        }
        if (parts.length > 1) {
            value = parts[1].replaceAll("^\"|\"$", "");
        }
    }

    /**
     * Static function to get all of the configured Defines in apache.
     *
     * @return an array of Define objects
     */
    public static Define[] getAllDefine(DirectiveParser parser) throws Exception {
        return (new Define().getAllConfigured(parser));
    }

    public static String replaceDefinesInString(Define[] defines, String line) {
        String newLine = line;

        String regex;
        for (Define define : defines) {

            if(define.getName().contains("{")) {
                String[] str = define.getName().split("\\{");
                String result = str[0] + "\\{" + str[1];
                String[] str2 = result.split("\\}");
                result = str2[0] + "\\}" + str2[1];
                regex = "\\$\\{ *" + result + " *}";
            }
            else
                regex = "\\$\\{ *" + define.getName() + " *}";

            Pattern pattern = Pattern.compile(regex);
            newLine = pattern.matcher(line).replaceAll(define.getValue());
        }

        return newLine;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public Define[] getAllConfigured(DirectiveParser parser) throws Exception {

        ArrayList<Define> define = new ArrayList<>();

        String[] defines = parser.getDirectiveValue(Constants.defineDirective, true);
        for (String s : defines) {
            define.add(new Define(s));
        }

        return define.toArray(new Define[0]);

    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        Define define = (Define) o;

        if (!name.equals(define.name)) return false;
        return value.equals(define.value);
    }

    @Override
    public int hashCode() {
        int result = name.hashCode();
        result = 31 * result + value.hashCode();
        return result;
    }
}
