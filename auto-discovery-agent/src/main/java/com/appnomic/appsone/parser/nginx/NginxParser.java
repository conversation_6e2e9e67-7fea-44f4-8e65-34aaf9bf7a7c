package com.appnomic.appsone.parser.nginx;

import com.appnomic.appsone.parser.BasicParser;
import com.appnomic.appsone.parser.ParseTarget;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.github.odiszapc.nginxparser.NgxBlock;
import com.github.odiszapc.nginxparser.NgxConfig;
import com.github.odiszapc.nginxparser.NgxEntry;
import com.github.odiszapc.nginxparser.NgxParam;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.util.List;

public class NginxParser extends BasicParser {

    private static final Logger logger = LoggerFactory.getLogger(NginxParser.class);

    public String parse(ParseTarget parseTarget) {
        String AttributeName = parseTarget.getAttributeName();
        StringBuilder line = new StringBuilder();
        String attributeValue= StringUtils.EMPTY;
        logger.info("Discovering attribute via Nginx Conf Parser - : {}", AttributeName);

        InputStream fileInputStream = Utils.loadFile(parseTarget.getConfigFileName());
        try {
            InputStreamReader streamReader = new InputStreamReader(fileInputStream, StandardCharsets.UTF_8);
            BufferedReader reader = new BufferedReader(streamReader);
            String str;
            while ((str = reader.readLine()) != null) {
                if(!str.trim().startsWith(Constants.COMMENT_PREFIX_ARGUMENT)) {
                    line.append(str).append(Constants.newLine);
                }
            }
            reader.close();
        } catch (Exception e) {
                logger.error("Exception while removing the comment lines : {}", e.getMessage(), e);
        }

        try {
            fileInputStream = new ByteArrayInputStream(line.toString().getBytes(StandardCharsets.UTF_8));
            NgxConfig conf = NgxConfig.read(fileInputStream);
            List<NgxEntry> httpServers = conf.findAll(NgxConfig.BLOCK, "http", "server");
            List<NgxEntry> servers = conf.findAll(NgxConfig.PARAM, "server", AttributeName);
            if (!httpServers.isEmpty()) {
                for (NgxEntry entry : httpServers) {
                    attributeValue = ((NgxBlock) entry).findParam(AttributeName).getValue();
                }
            } else {
                for (NgxEntry entry : servers) {
                    attributeValue = ((NgxParam) entry).getValue();
                }
            }
            return attributeValue;
        } catch (IOException e) {
            logger.error("Exception during attribute discovery in File {} : {}", e.getMessage(), e);
        }
        return StringUtils.EMPTY;
    }
}
