package com.appnomic.appsone.prereq;

import com.appnomic.appsone.common.beans.discovery.ComponentScript;
import com.appnomic.appsone.parser.ConfigFileDetails;
import com.appnomic.appsone.util.Constants;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;

public class PrerequisiteComponentScript {

    private static final Logger log = LoggerFactory.getLogger(PrerequisiteComponentScript.class);

    private static final List<ComponentScript> componentScriptList = YamlFileLoader.getComponentScripts();

    public static String getApplicableScriptName(int componentId, String operatingSystem, boolean isValidateScript) {
        StringBuilder sb = new StringBuilder();

        if (isComponentScriptAvailable(componentId)) {

            String scriptName = getComponentScriptName(componentId);

            //sb.append(Utils.getScriptPrefix(operatingSystem));
            sb.append(scriptName);
            sb.append(Utils.getScriptSuffix(isValidateScript));

            if (operatingSystem.toLowerCase().contains(Constants.WINDOWS.toLowerCase())) {
                sb.append(".bat");
            } else {
                sb.append(".sh");
            }
            log.info("Applicable ScriptName for the platform: {}", sb);

        }
        return sb.toString();
    }

    private static String getComponentScriptName(int componentId) {
        for (ComponentScript componentScript : componentScriptList) {
            if (componentScript.getComponentId() == componentId) {
                return componentScript.getFileName();
            }
        }
        return StringUtils.EMPTY;
    }

    private static boolean isComponentScriptAvailable(int componentId) {
        String scriptName = getComponentScriptName(componentId);
        if (!StringUtils.isEmpty(scriptName)) {
            return true;
        }
        log.info("No Prerequisite script Available for componentId: {}", componentId);
        return false;
    }

    public boolean run(ConfigFileDetails configFileDetails, String scriptPath, String operatingSystem, boolean isValidateScript) {
        int ret = -1;

        if (!StringUtils.isEmpty(scriptPath)) {
            String command = getOSCommand(configFileDetails, scriptPath, operatingSystem, isValidateScript);
            ret = Utils.executeCommand(command, false);
        }

        if (ret != 0) {
            return false;
        }

        return true;
    }

    private String getOSCommand(ConfigFileDetails configFileDetails, String scriptPath, String operatingSystem, boolean isValidateScript) {

        StringBuilder commandBuilder = new StringBuilder();

        if (!StringUtils.isEmpty(scriptPath) && configFileDetails.isConfigFilePresent()) {

            commandBuilder.append(scriptPath).append("/");

            String applicableScript = getApplicableScriptName(configFileDetails.getComponentId(), operatingSystem, isValidateScript);

            commandBuilder.append(applicableScript).append(" ");

            commandBuilder.append(configFileDetails.getConfigFilePath());
        }
        log.info("Script command: {}", commandBuilder);

        return commandBuilder.toString();
    }
}