package com.appnomic.appsone.prereq;

import com.appnomic.appsone.common.beans.discovery.DiscoveryAgentArgs;
import com.appnomic.appsone.common.beans.discovery.Prerequisite;
import com.appnomic.appsone.common.beans.discovery.Server;
import com.appnomic.appsone.common.util.NodeIdGenerator;
import com.appnomic.appsone.util.Utils;
import com.appnomic.appsone.yaml.YamlFileLoader;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.security.action.GetPropertyAction;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileSystems;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileOwnerAttributeView;
import java.nio.file.attribute.UserPrincipal;
import java.nio.file.attribute.UserPrincipalLookupService;
import java.nio.file.attribute.UserPrincipalNotFoundException;
import java.security.AccessController;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class PrerequisiteDiscovery {

    private static final Logger log = LoggerFactory.getLogger(PrerequisiteDiscovery.class);

    private final List<Server> defaultServerList = YamlFileLoader.getServerList();

    private final String currentUser = System.getProperty("user.name");

    public Prerequisite run(DiscoveryAgentArgs discoveryAgentArgs) {
        List<Server> serverList = discoveryAgentArgs.getServers();
        log.info("Running Pre-requisite check on Host");
        Prerequisite preRequisite = new Prerequisite();

        String version = Runtime.class.getPackage().getSpecificationVersion();
        log.info("Java Version: {} ", version);

        String javaHomeSystemEnv = System.getenv("JAVA_HOME");
        log.info("JAVA_HOME Environment Variable: {} ", javaHomeSystemEnv);

        if (StringUtils.isEmpty(javaHomeSystemEnv)) {
            log.info("JAVA_HOME is NOT Set");
            String javaHomeDir = System.getProperty("java.home");
            log.info("Java Home Directory: {} ", javaHomeDir);
            preRequisite.setJavaHomeDir(javaHomeDir);
        } else {
            preRequisite.setJavaHomeDir(javaHomeSystemEnv);
        }
        preRequisite.setJavaHomeSystemEnv(StringUtils.isNotEmpty(javaHomeSystemEnv));

        boolean java64Bit = com.sun.jna.Native.POINTER_SIZE == 8;
        log.info("JAVA 64 bit: {} ", java64Bit);

        long freeMemory = Runtime.getRuntime().freeMemory();
        log.info("Free Memory: {} ", freeMemory);

        preRequisite.setUser(currentUser);

        if (serverList.isEmpty()) {
            log.info("Using Default Server List - No valid servers found on command line");
            serverList = defaultServerList;
        }

        long usableSpace = getUsableDiskSpace(currentUser);

        int cpuCores = Runtime.getRuntime().availableProcessors();
        log.info("CPU Cores Available: {} ", cpuCores);

        List<Server> portsAvailable = Utils.checkPortAvailability(serverList);

        Map<String, Boolean> locationPermissions = checkLocationWritePermission(discoveryAgentArgs.getLocations(), currentUser);

        preRequisite.setPrerequisiteIdentifier(NodeIdGenerator.generateNodeIdForPrerequisite(preRequisite));
        preRequisite.setJavaVersion(version);
        preRequisite.setFreeMemory(freeMemory);
        preRequisite.setDiskSpace(usableSpace);
        preRequisite.setCpuCores(cpuCores);
        preRequisite.setPortAvailability(portsAvailable);
        preRequisite.setJava64Bit(java64Bit);
        preRequisite.setWriteAccess(locationPermissions);

        return preRequisite;
    }

    private Map<String, Boolean> checkLocationWritePermission(List<String> locations, String effectiveUser) {
        //File tempFile = null;
        List<File> tempFiles = new ArrayList<>();
        Map<String, Boolean> locationWriteAccess = new HashMap<>();
        log.info("Checking write permissions");

        if (locations != null && !locations.isEmpty()) {
            // create an empty file in the user defined location
            for (String location : locations) {
                try {
                    File tempFile = File.createTempFile(effectiveUser, null, new File(location));
                    tempFiles.add(tempFile);
                } catch (IllegalArgumentException | UnsupportedOperationException | IOException | SecurityException e) {
                    log.error("Failed to create temp file - {} : {} ", e.getMessage(), e);
                    locationWriteAccess.put(location, false);
                }
            }
        } else {
            // create an empty file in default temp locations [/tmp, /var/tmp, %temp%]
            try {
                File tempFile = File.createTempFile(effectiveUser, null, null);
                tempFiles.add(tempFile);
            } catch (IllegalArgumentException | UnsupportedOperationException | IOException | SecurityException e) {
                log.error("Failed to create temp file - {} : {} ", e.getMessage(), e);
                File tmpdir = new File(AccessController.doPrivileged(new GetPropertyAction("java.io.tmpdir")));
                locationWriteAccess.put(tmpdir.getPath(), false);
            }
        }
        for (File tempFile : tempFiles) {
            boolean flag = false;
            if (tempFile != null && tempFile.exists()) {
                log.info("Temp file created: {} - {} ", tempFile.getName(), tempFile.getAbsolutePath());
                UserPrincipal userPrincipal = findUser(effectiveUser);
                if (userPrincipal != null && !StringUtils.isEmpty(userPrincipal.getName())) {
                    flag = changeFileOwnership(userPrincipal, tempFile.getAbsolutePath());
                }
                locationWriteAccess.put(tempFile.getParent(), flag);
                if (tempFile.delete()) {
                    log.info("Temp file deleted!");
                }
            }
        }
        return locationWriteAccess;
    }

    private long getUsableDiskSpace(String effectiveUser) {
        File tempFile = null;
        long usableSpace = 0;
        try {
            tempFile = File.createTempFile(effectiveUser, null, null);
            usableSpace = tempFile.getUsableSpace();
            log.info("Usable Space on Disk: {} GB ", usableSpace / (1024.0 * 1024 * 1024));

        } catch (IllegalArgumentException | UnsupportedOperationException | IOException | SecurityException e) {
            log.error("Failed to create temp file - {} : {} ", e.getMessage(), e);
        } finally {
            if (tempFile != null) {
                if (tempFile.delete()) {
                    log.info("Temp file deleted!");
                }
            }
        }
        return usableSpace;
    }

    private UserPrincipal findUser(String user) {

        UserPrincipalLookupService lookupService = FileSystems.getDefault().getUserPrincipalLookupService();

        UserPrincipal userPrincipal = null;
        try {
            userPrincipal = lookupService.lookupPrincipalByName(user);
        } catch (UserPrincipalNotFoundException e) {
            log.error("User {} not found - {} : {} ", user, e.getMessage(), e);
        } catch (SecurityException e) {
            log.error("User {} not found - {} : {} ", user, e.getMessage(), e);
        } catch (IOException e) {
            log.error("User {} not found - {} : {} ", user, e.getMessage(), e);
        }
        return userPrincipal;
    }

    private boolean changeFileOwnership(UserPrincipal userPrincipal, String absolutePath) {
        boolean ableToChangeOwnership = false;
        Path path = Paths.get(absolutePath);
        try {
            log.info("Changing file ownership to {} ", userPrincipal.getName());
            Files.setOwner(path, userPrincipal);
            FileOwnerAttributeView view = Files.getFileAttributeView(path, FileOwnerAttributeView.class);
            view.setOwner(userPrincipal);
            ableToChangeOwnership = true;
        } catch (UnsupportedOperationException | SecurityException | IOException e) {
            log.error("Unable to change file ownership - {} : {} ", e.getMessage(), e);
        }
        return ableToChangeOwnership;
    }

}
