<assembly>
    <id>bin</id>

    <formats>
        <format>tar.gz</format>
    </formats>

    <baseDirectory>${project.artifactId}</baseDirectory>
    <dependencySets>
        <dependencySet>
            <useProjectArtifact>true</useProjectArtifact>
            <outputDirectory>lib</outputDirectory>
            <unpack>false</unpack>
        </dependencySet>
    </dependencySets>

    <fileSets>

        <fileSet>
            <directory>${project.build.directory}</directory>
            <outputDirectory></outputDirectory>
            <includes>
                <include>*.jar</include>
            </includes>
        </fileSet>

    </fileSets>

    <files>
        <file>
            <source>src/main/resources/logback.xml</source>
            <outputDirectory>config</outputDirectory>
            <destName>logback.xml</destName>
        </file>
        <file>
            <source>src/main/resources/configuration-entities.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>configuration-entities.yaml</destName>
        </file>
        <file>
            <source>src/main/resources/components.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>components.yaml</destName>
        </file>
        <file>
            <source>src/main/resources/versions.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>versions.yaml</destName>
        </file>
        <file>
            <source>src/main/resources/ports.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>ports.yaml</destName>
        </file>
        <file>
            <source>src/main/resources/scripts.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>scripts.yaml</destName>
        </file>
        <file>
            <source>src/main/resources/process-black-list.yaml</source>
            <outputDirectory>config</outputDirectory>
            <destName>process-black-list.yaml</destName>
        </file>
    </files>

</assembly>